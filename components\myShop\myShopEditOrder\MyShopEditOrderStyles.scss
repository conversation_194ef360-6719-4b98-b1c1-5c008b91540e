.edit-order-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: var(--color-background-2);
  position: relative;
  border-radius: 10px;
  height: 100%;
  max-height: 90dvh;

  // &>.title-header {
  //     // font-size: 1.3em;
  //     padding: 5px 0;
  //     margin: 0;
  //     text-align: center;
  //     width: 100%;
  //     border-bottom: thin solid #ccc;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     background: white;
  //     border-radius: 10px 10px 0 0;

  //     & h3 {
  //         margin: 0;
  //     }
  // }

  & .edit-order-content-container,
  .none-order-result {
    padding: 10px;
    overflow: auto;
    flex: 1;
  }

  & .main-stack {
    width: 100%;
    padding: 5px;
    background-color: white;
    margin-top: 10px;
  }

  & .h-stack {
    width: 100%;
    background: white;
    gap: 5px;
  }

  & > .v-stack {
    flex: 1;
    width: 100%;
    background: var(--color-background-2);
    justify-content: flex-start;
    align-items: flex-start;
    overflow: auto;
  }

  & .label {
    font-size: 1em;
    font-weight: 500;
    color: var(--color-text-note);
  }

  & .short-code {
    font-size: 1em;
    font-weight: 600;
    color: var(--primary-color-1);
    user-select: all;
  }

  & .add-product-to-order-stack {
    padding: 0;
    background: transparent;

    & .add-product-to-order-button {
      border: thin solid var(--primary-color-1);
      border-radius: 5px;
      background: white;
      width: 100%;
      padding: 5px;
      color: var(--primary-color-1);
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
    }
  }

  & .order-items-container {
    background-color: var(--color-background-2);

    & > .order-item-container {
      padding: 5px;
      width: 100%;
      align-items: flex-start;
      display: flex;
      flex: 1;
      gap: 5px;
      background-color: white;
      border-bottom: thin solid #ddd;

      & > img {
        object-fit: cover;
        width: 70px;
        height: 70px;
        border-radius: 10px;
      }

      & > .item-order-detail {
        justify-content: space-between;
        flex: 1;
        height: 100%;
        padding-left: 10px;
        overflow: hidden;

        & > .name {
          font-size: 1em;
          font-weight: 400;
        }

        & > .price {
          font-size: 0.9em;
          font-weight: 600;
          color: var(--primary-color-2);

          & > .price-display-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;

            & > .price-display {
              font-weight: 600;
              color: var(--primary-color-2);
            }

            & > .price-input-edit {
              flex: 1;
              color: var(--primary-color-2);
              font-weight: 500;
              text-align: left;
              padding: 4px 8px;
              border: 1px solid var(--primary-color-2);
              border-radius: 4px;
              background-color: white;
            }

            & > .price-edit-btn {
              background: #a8a7a7;
              color: white;
              border: none;
              border-radius: 4px;
              padding: 4px 6px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 28px;
              height: 28px;
              transition: background-color 0.2s;

              &:hover {
                background: var(--primary-color-2);
              }

              &:active {
                transform: scale(0.95);
              }
            }
          }          
          & > em.off {
            font-size: 0.8em;
            font-style: normal;
            color: var(--color-text-note);
            text-decoration: line-through;
            position: relative;
            margin-left: 5px;
            
            & > .discount-percent {
              display: inline-block;
              background-color: #ff4d4f;
              color: white;
              border-radius: 2px;
              padding: 0 4px;
              font-size: 0.9em;
              font-weight: bold;
              margin-left: 5px;
              text-decoration: none;
            }
          }

          & > .discount-percent {
            display: inline-block;
            background-color: #ff4d4f;
            color: white;
            border-radius: 2px;
            padding: 0 4px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 5px;
            text-decoration: none;
          }

          & > .increase-percent {
            display: inline-block;
            background-color: #52c41a;
            color: white;
            border-radius: 2px;
            padding: 0 4px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 5px;
            text-decoration: none;
          }
        }
        & > .note {
          font-size: 0.9em;
          font-style: italic;
          font-weight: 400;
          color: var(--color-text-note);
        }

        & > .price-quantity {
          justify-content: space-between;
          flex: 1;
          align-items: flex-start;
          margin-top: 10px;

          & > .quantity {
            align-items: center;
            gap: 0;
            flex: 1;
            width: 50%;

            & > .quantity-input {
              width: inherit;
              flex: 1;
              padding: 0;
              height: 30px;
              border: thin solid #ccc;
              text-align: center;
              font-weight: 500;
            }

            & > .quantity-button {
              background-color: white;
              border-radius: 5px;
              height: 30px;
              border: thin solid var(--primary-color-1);
              padding: 5px;
              color: var(--primary-color-1);
              display: flex;
              align-items: center;
              justify-content: center;
            }

            & > .quantity-button.minus {
              border-right: none;
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }

            & > .quantity-button.plus {
              border-left: none;
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }
          & .price-input-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
            width: 50%;
            text-align: center;

            & > .price-input {
              color: var(--primary-color-2);
              font-weight: 500;
              text-align: center;
            }
            & > .price {
              color: var(--primary-color-3);
              font-size: 0.8em;
              font-style: italic;
            }
          }
        }
      }
    }
  }

  & .customer-info {
    border-bottom: thin solid #ccc;
    padding: 5px;

    & > img {
      width: 50px;
      height: 50px;
      border-radius: 2em;
      object-fit: cover;
      background-color: var(--color-background-2);
    }

    & .name {
      font-weight: 500;
    }

    & .phone {
      font-weight: 500;
      color: var(--color-text-note);
    }
  }

  & .address-info {
    padding: 5px 10px;

    & > .address-input {
      font-weight: 500;
    }
  }

  & .order-payment-info {
    font-size: 1.2em;
    font-weight: 400;
    justify-content: space-between;
    padding: 5px;

    & > span {
      font-weight: inherit;
    }

    & > span.data {
      font-weight: 500;
    }
    & .price-input {
      text-align: right;
      font-weight: 500;
      width: 150px;
    }
  }

  & .total {
    border-top: thin solid #ccc;

    & > span.label {
      font-weight: 500;
      color: var(--primary-color-1);
    }

    & > span.data {
      font-weight: 500;
      color: var(--primary-color-2);
    }
  }

  & .note-order-input {
    border: none;
    border-bottom: thin solid #ccc;
    padding: 10px;
    width: 100%;
    min-height: 100px;
    outline: none;
    background: var(--color-background-2);
    resize: none;
  }

  & .edit-actions {
    justify-content: space-evenly;
    gap: 15px;
    padding: 5px;
    border-radius: 0 0 10px 10px;

    & button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }
  }
}

.edit-order-modal .modal-content {
  border-radius: 10px;
  height: calc(100dvh - var(--bs-modal-margin) * 2);
}

.add-product-to-order-modal {
  z-index: 1000;
  height: 90dvh !important;
  height: 88vh;
  width: 90%;
  border-radius: 10px;
  margin: auto;

  & .add-product-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    flex-direction: column;
    height: 100%;
  }

  & .search-bar {
    width: 100%;
    background: #f5f6fa;
    padding: 5px 10px;
    border-radius: 5px;
    gap: 5px;
  }

  & .search-input-container {
    width: 100%;
    background-color: #f5f6fa;
    border-radius: 0 !important;
    border-color: transparent;
    outline: none;
    height: 100%;
    font-size: var(--font-size);
  }

  & .none-search-result {
    margin: 10px 0;
    justify-content: center;
    border-radius: 50%;
    width: 250px;
    height: 250px;
    object-fit: contain;
    margin: auto;
  }

  & .products {
    overflow: auto;
    flex: 1;
    width: 100%;
  }

  & .product-result-item.disabled {
    color: var(--color-text-note);
  }

  & .product-result-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    font-size: 1.2em;
    padding: 5px;
    cursor: pointer;

    & .my-checkbox {
      width: 1em;
      min-width: 1em;
    }
  }

  & .actions {
    width: 100%;
    gap: 5px;
    padding: 5px;
    justify-content: space-between;
    border-top: thin solid #ccc;
    position: sticky;
    bottom: 0;
    background: white;
    margin-top: auto;

    & > .clear-selected-button {
      border: thin solid var(--color-button-special);
      color: var(--color-button-special);
      border-radius: 2em;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 15px;
      margin-left: auto;
      background-color: white;
      line-height: 1;
    }

    & > .accept-button {
      border: thin solid var(--primary-color-1);
      width: fit-content;
      padding: 5px 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      height: auto;
      font-size: 1em;
    }
  }
}

.add-product-to-order-backdrop {
  z-index: 1000;
}
