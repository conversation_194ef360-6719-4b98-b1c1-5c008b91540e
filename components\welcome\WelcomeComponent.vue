<template>
  <div class="welcome-container">
    <button v-on:click="() => {
      router.push(appRoute.HomeComponent)
    }">
      {{ $t('WelcomeComponent.bat_dau') }}
    </button>
  </div>
</template>

<script lang="ts" setup>
import logo from "~/assets/image_13_3_2024/logo.png";
const { t } = useI18n();
useSeoMeta({
  title: t('AppRouteTitle.WelcomeComponent'),
});
var router = useRouter();
var route = useRoute();
// var props = defineProps({
// 	categoryData: "" as any,
// 	dataCategories: {}
// })

var emit = defineEmits(["close"]);

import { toast } from "vue3-toastify";
import { appConst } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";

</script>

<style lang="scss" src="./WelcomeStyles.scss"></style>
