<template>
	<v-overlay v-model="showDateTimePicker" location="bottom" contained :z-index="1001" key="show_product_select"
		v-on:click:outside="() => {
			close()
		}"
		class="date-time-picker-overlay-container" persistent content-class='date-time-picker-container'
		no-click-animation>
		<HeaderComponent :title="$props.title ? $props.title : $t('AppRouteTitle.DateTimePickerComponent')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close" v-on:click="() => {
					close()
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="date-time-picker-content-container">
			<div class="picker-content date-picker" v-if="showDatePicker">
				<Swiper class="my-carousel picker-buttons-carousel" :modules="[]" :centeredSlides="true"
					:direction="'vertical'" :centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
					:effect="'creative'" :autoplay="false" key="date-picker-carousel" :initialSlide="selectDateIndex"
					@slideChange="(e: any) => {

						selectDate = dateList[e.activeIndex].value;
						selectDateIndex = e.activeIndex;
						if (showTimePicker) {
							timeListConstructor();
							setFirstSelectTime();
							if (timeCarouselButton) {
								timeCarouselButton.slideTo(0);
							}

						}
					}" @init="(e: any) => {
						dateCarouselButton = e;
					}">
					<SwiperSlide class="picker-item" v-for="(itemDate, indexDate) of dateList" v-on:click="async () => {
						dateCarouselButton.slideTo(indexDate)
					}">
						<div class="tab-title">
							<span class='name'>
								{{ itemDate.displayName }}
							</span>
						</div>
					</SwiperSlide>
				</Swiper>
			</div>
			<div class="picker-content time-picker" v-if="showTimePicker">
				<Swiper class="my-carousel picker-buttons-carousel" :modules="[SwiperFreeMode]" :centeredSlides="true"
					:freeMode="{
						enabled: true,
						sticky: true
					}" :direction="'vertical'" :centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
					:effect="'creative'" :autoplay="false" :initialSlide="selectTimeIndex" key="time-picker-carousel"
					@slideChange="(e: any) => {
						selectTime = timeList[e.activeIndex].value;
						selectTimeIndex = e.activeIndex
					}" @init="(e: any) => {
						timeCarouselButton = e;
					}">
					<SwiperSlide class="picker-item" v-for="(itemTime, indexTime) of timeList" v-on:click="async () => {
						timeCarouselButton.slideTo(indexTime)
					}">
						<div class="tab-title">
							<span class='name'>
								{{ itemTime.displayName }}
							</span>
						</div>
					</SwiperSlide>
				</Swiper>
			</div>
		</div>
		<div class="footer-actions">
			<button class="submit-button" v-on:click="() => {
				submit();
			}">
				{{ $t('DateTimePickerComponent.chon') }}
			</button>
		</div>
	</v-overlay>
</template>

<script lang="ts" setup>
import moment from 'moment';
const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
	title: null,
	startDate: null,
	endDate: null,
	startTime: null,
	endTime: null,
	initialDate: null,
	initialTime: null,
	showDatePicker: null,
	showTimePicker: null,
	stepMinute: null,
	firstStepMinute: null,
	firstItemTimeTitle: null,
	firstItemTimeIsNull: null
})

var showDateTimePicker = ref(false);
var dateList = ref([] as any);
var timeList = ref([] as any);
var showDatePicker = ref(props.showDatePicker ? props.showDatePicker : true);
var showTimePicker = ref(props.showTimePicker ? props.showTimePicker : true);

var dateCarouselButton = ref();
var timeCarouselButton = ref();

var selectDateIndex = ref(0);
var selectTimeIndex = ref(0);
var selectDate = ref(null as any);
var selectTime = ref(null as any);
onMounted(async () => {

	if (showDatePicker.value) {
		await dateListConstructor();
		await setFirstSelectDate();
	}
	if (showTimePicker.value) {
		await timeListConstructor();
		await setFirstSelectTime();
	}
	showDateTimePicker.value = true;
})

function dateListConstructor(intervalDate = 1) {
	let startDate = moment(props.startDate);
	let endDate = moment(props.endDate);
	var days = [];
	while (startDate.isBefore(endDate)) {
		days.push({
			value: startDate.format('DD/MM/YYYY'),
			displayName: moment().format('DD/MM/YYYY') == startDate.format('DD/MM/YYYY') ? t('DateTimePickerComponent.hom_nay') : startDate.format('DD/MM/YYYY'),
		});
		startDate.add(intervalDate, 'days');
	}
	dateList.value = days;

}

function timeListConstructor(intervalMinutes = props.stepMinute ? props.stepMinute : 30) {
	var slots = [];
	let startTime;

	let currentDate = moment().format("DD/MM/YYYY");

	if (currentDate == selectDate.value) {
		if (props.firstItemTimeIsNull) {
			slots.push({
				value: null,
				displayName: props.firstItemTimeTitle ?? t('DateTimePickerComponent.bay_gio'),
			})
		}

		startTime = moment().add(intervalMinutes - moment().minute() % intervalMinutes + props.firstStepMinute, 'minutes').seconds(0);
		// if (moment().minutes() > 30) {
		// 	startTime = moment().add(1, 'hours').minutes(0).seconds(0);
		// }
		// else {
		// 	startTime = moment().minutes(30).seconds(0);
		// }
	}
	else {
		startTime = moment(props.startTime);
	}
	let endTime = moment(props.endTime);

	while (startTime.isBefore(endTime)) {
		slots.push({
			value: startTime.format('HH:mm'),
			displayName: `${startTime.format('HH:mm')}`,
		});
		startTime.add(intervalMinutes, 'minutes');
	}

	timeList.value = slots;
}
function setFirstSelectDate() {

	let initDateIndex = dateList.value.findIndex(function (e: any) {
		return e.value == props.initialDate;
	});
	selectDateIndex.value = initDateIndex != -1 ? initDateIndex : 0;
	selectDate.value = dateList.value[selectDateIndex.value]?.value;
}
function setFirstSelectTime() {
	let initTimeIndex = timeList.value.findIndex(function (e: any) {
		return e.value == props.initialTime
	});
	selectTimeIndex.value = initTimeIndex != -1 ? initTimeIndex : 0;
	if (timeList.value && timeList.value.length > 0 && selectTimeIndex.value < timeList.value.length) {
		selectTime.value = timeList.value[selectTimeIndex.value].value;
	} else {
		selectTime.value = null; // or handle the case when timeList is empty
	}
}

function close(value?: any) {
	showDateTimePicker.value = false;
	emit('close', value);
}
function submit() {
	showDateTimePicker.value = false;
	if (selectDate.value && selectTime.value){
		emit('submit', `${selectDate.value} ${selectTime.value}`)
	}
	else {
		emit('submit', null)
	}
}
</script>

<style lang="scss" src="./DateTimePickerStyles.scss"></style>