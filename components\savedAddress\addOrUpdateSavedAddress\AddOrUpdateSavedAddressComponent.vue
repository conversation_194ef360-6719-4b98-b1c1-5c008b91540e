<template>
  <VueFinalModal class="my-modal-container" :click-to-close="false" :overlay-behavior="'persist'"
    :modal-id="'add_or_update_saved_address'"
    content-class="my-modal-content-container form-modal saved-address-modal-container" v-model="showModalInfo"
    v-on:closed="() => {
      showModalInfo = false
    }" contentTransition="vfm-slide-down">
    <div class='v-stack edit-customer-info-container'>
      <SubHeaderV2Component :title="$t('AddOrUpdateSavedAddressComponent.thong_tin')">
        <template v-slot:header_left></template>
      </SubHeaderV2Component>

      <div class="edit-customer-info-content">
        <button class="current-location-leaflet-btn" :title="$t('AddOrUpdateSavedAddressComponent.lay_vi_tri_hien_tai')" v-on:click="() => {
          gotoCurrentLocationLeaflet();
        }">
          <Icon name="hugeicons:location-09" class="my-location-icon" />
          {{ $t('AddOrUpdateSavedAddressComponent.lay_vi_tri_hien_tai') }}
        </button>
        <div class='v-stack'>
          <!-- <span class='label required'>
            {{ $t('AddOrUpdateSavedAddressComponent.ten') }}
          </span> -->
          <!-- <input :title="$t('AddOrUpdateSavedAddressComponent.ten')" name='customer-name' maxLength=255
            autoComplete="on" class='input-order' :placeholder="$t('AddOrUpdateSavedAddressComponent.ten_placeholder')"
            :value="userInfoTemp.name || null" v-on:input="($event: any) => {
              userInfoTemp.name = $event.target.value;
              if (!userInfoTemp.name || !userInfoTemp.name.length) {
                userInfoTemp.nameErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_ten');
              }
              else {
                userInfoTemp.nameErr = '';
              }
            }" v-on:blur="() => {
              if (!userInfoTemp.name || !userInfoTemp.name.length) {
                userInfoTemp.nameErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_ten');
              }
              else {
                userInfoTemp.nameErr = '';
              }
            }" /> -->
          <v-text-field :label="$t('AddOrUpdateSavedAddressComponent.ten')" variant="outlined" name="customer-name"
            maxLength=255 autoComplete="on" class="v-input-order" v-model="userInfoTemp.name" hide-details
            v-on:update:model-value="() => {
              if (!userInfoTemp.name || !userInfoTemp.name.length) {
                userInfoTemp.nameErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_ten');
              }
              else {
                userInfoTemp.nameErr = '';
              }
            }" v-on:update:focused="() => {
                if (!userInfoTemp.name || !userInfoTemp.name.length) {
                  userInfoTemp.nameErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_ten');
                }
                else {
                  userInfoTemp.nameErr = '';
                }
              }">
            <template v-slot:label>
              <span class='label required'>
                {{ $t('AddOrUpdateSavedAddressComponent.ten') }}
              </span>
            </template>
          </v-text-field>
          <span class='error-message'>{{ userInfoTemp.nameErr }}</span>
        </div>
        <div class='v-stack'>
          <!-- <span class='label required'>
            {{ $t('AddOrUpdateSavedAddressComponent.so_dien_thoai') }}
          </span>
          <input :title="$t('AddOrUpdateSavedAddressComponent.so_dien_thoai')" name='customer-phone' maxLength=255
            type="phone" autoComplete="on" class='input-order'
            :placeholder="$t('AddOrUpdateSavedAddressComponent.so_dien_thoai_placeholder')"
            :value="userInfoTemp.phone || null" v-on:input="($event: any) => {
              userInfoTemp.phone = validPhone($event.target.value);
              phoneValidation();
            }" v-on:blur="() => {
              phoneValidation();
            }" /> -->
          <v-text-field :label="$t('AddOrUpdateSavedAddressComponent.so_dien_thoai')" variant="outlined"
            name="customer-phone" type="phone" maxLength=255 autoComplete="on" class="v-input-order"
            v-model="userInfoTemp.phone" hide-details v-on:update:model-value="() => {
              phoneValidation()
            }" v-on:update:focused="() => {
                phoneValidation()
              }">
            <template v-slot:label>
              <span class='label required'>
                {{ $t('AddOrUpdateSavedAddressComponent.so_dien_thoai') }}
              </span>
            </template>
          </v-text-field>
          <span class='error-message'>{{ userInfoTemp.phoneErr }}</span>
        </div>
        <div class='v-stack'>
          <div class="h-stack">
            <span class='label required'>
              {{ $t('AddOrUpdateSavedAddressComponent.dia_chi') }}
            </span>
            <v-menu class="bootstrap-dropdown-container coordinate-input-menu" location="bottom right"
              :close-on-content-click="false" contained>
              <template v-slot:activator="{ props }">
                <button class="coordinate-button" v-bind="props">
                  <Icon name="ic:outline-edit-location"></Icon>
                  <span>{{ $t('AddOrUpdateSavedAddressComponent.nhap_toa_do') }}</span>
                </button>
              </template>
              <div class="coordinate-input-container">
                <input type="text" v-model="coordinatesText"
                  :placeholder="$t('AddOrUpdateSavedAddressComponent.toa_do_x_y')"
                  v-on:keydown.enter="() => { jumpToCoordinate() }">
                <button class="submit-coordinate" v-on:click="() => { jumpToCoordinate() }">
                  <Icon name="gis:location-poi"></Icon>
                </button>
              </div>

            </v-menu>
          </div>


          <div class="map-container">
            <client-only>
              <LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
                leafletMap = e;
                leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

                initleafletMap();
              }" :max-zoom="appConst.leafletMapTileOption.maxZoom" v-on:update:center="async (bounds: any) => {
                  userInfoTemp.latitude = leafletMap.getCenter().lat?.toFixed(6);
                  userInfoTemp.longitude = leafletMap.getCenter().lng?.toFixed(6);
                  if (!firstLoad) await getUserAddress();
                }" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true" :use-global-leaflet="true">
                <LControlZoom position="bottomright"></LControlZoom>
                <span v-if="false" class="current-location-leaflet" :title="$t('Map.vi_tri_cua_ban')" v-on:click="() => {
                  gotoCurrentLocationLeaflet();
                }">
                  <Icon name="line-md:my-location-loop" class="my-location-icon" />
                </span>
                <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip" data-placement="right"
                  :title="$t('Map.nhan_de_chuyen_loai_map')" v-bind:style="{
                    backgroundImage: `url(${buttonMapTileBackgound})`,
                  }" v-on:click="(event: any) => {
                      if (event.isTrusted) {
                        if (
                          leafletMapTileUrl ==
                          appConst.leafletMapTileUrl.roadmap
                        ) {
                          leafletMapTileUrl =
                            appConst.leafletMapTileUrl.hyprid;
                          mapTypeTitle = $t('Map.ve_tinh');
                          mapType = 'hyprid';
                          buttonMapTileBackgound = map_sateline;
                        } else if (
                          leafletMapTileUrl ==
                          appConst.leafletMapTileUrl.hyprid
                        ) {
                          leafletMapTileUrl =
                            appConst.leafletMapTileUrl.streetmap;
                          mapTypeTitle = $t('Map.co_dien');
                          mapType = 'hyprid';
                          buttonMapTileBackgound = map_streetmap;
                        } else if (
                          leafletMapTileUrl ==
                          appConst.leafletMapTileUrl.streetmap
                        ) {
                          leafletMapTileUrl =
                            appConst.leafletMapTileUrl.roadmap;
                          mapTypeTitle = $t('Map.ve_tinh_va_nhan');
                          mapType = 'roadmap';
                          buttonMapTileBackgound = map_sateline;
                        }
                      } else event.preventDefault();
                    }">
                  <span>{{ mapTypeTitle }}</span>
                </div>
                <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                  :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base" name="GoogleMap">
                </LTileLayer>
                <div class="marker-location">
                  <img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon" alt="" />
                </div>
              </LMap>
            </client-only>
          </div>
          <label class="change-address-on-moving" :class="{
            'active': changeAddressOnMoving
          }" for="change_address_on_moving" v-on:click="() => {
              changeAddressOnMoving = !changeAddressOnMoving
            }">
            <v-switch :model-value="changeAddressOnMoving" v-on:update:model-value="(e: any) => {
              changeAddressOnMoving = e;
            }" flat color="#3393cb" hide-details class="my-switches" name="change_address_on_moving">

            </v-switch>
            <span>{{ $t('AddOrUpdateSavedAddressComponent.cap_nhat_dia_chi_khi_di_chuyen_ban_do') }}</span>
          </label>
          <!-- <input :title="$t('AddOrUpdateSavedAddressComponent.dia_chi')" name='customer-address' maxLength=255
            autoComplete="on" class='input-order'
            :placeholder="$t('AddOrUpdateSavedAddressComponent.dia_chi_placeholder')"
            :value="userInfoTemp.address || null" v-on:input="($event: any) => {
              userInfoTemp.address = $event.target.value;
              if (!userInfoTemp.address || !userInfoTemp.address.length) {
                userInfoTemp.addressErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_dia_chi');
              }
              else {
                userInfoTemp.addressErr = ''
              }
            }" v-on:blur="() => {
              if (!userInfoTemp.address || !userInfoTemp.address.length) {
                userInfoTemp.addressErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_dia_chi');
              }
              else {
                userInfoTemp.addressErr = ''
              }
            }" /> -->
          <UTextarea autoresize variant="outline" :maxrows="5" :rows="1"
            :label="$t('AddOrUpdateSavedAddressComponent.dia_chi')" name="customer-address" class='input-address'
            :placeholder="$t('AddOrUpdateSavedAddressComponent.dia_chi_placeholder')" v-model="userInfoTemp.address"
            v-on:update:model-value="() => {
              console.log(userInfoTemp.address)
              if (!userInfoTemp.address || !userInfoTemp.address.length) {
                userInfoTemp.addressErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_dia_chi');
              }
              else {
                userInfoTemp.addressErr = ''
              }
            }" v-on:blur="() => {
                if (!userInfoTemp.address || !userInfoTemp.address.length) {
                  userInfoTemp.addressErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_dia_chi');
                }
                else {
                  userInfoTemp.addressErr = ''
                }
              }" id="customer_address" />
          <span class='error-message'>{{ userInfoTemp.addressErr }}</span>


        </div>

        <!-- <div class='v-stack'>
          <span class='label required'>
            {{ $t('AddOrUpdateSavedAddressComponent.ten_dia_chi') }}
          </span>
          <div class="title-address-options">
            <button class="title-option"
              :class="{ 'active': userInfoTemp.title == t('AddOrUpdateSavedAddressComponent.nha_rieng') }" v-on:click="() => {
                userInfoTemp.title = t('AddOrUpdateSavedAddressComponent.nha_rieng');
              }">
              {{ $t('AddOrUpdateSavedAddressComponent.nha_rieng') }}
            </button>
            <button class="title-option"
              :class="{ 'active': userInfoTemp.title == t('AddOrUpdateSavedAddressComponent.cong_ty') }" v-on:click="() => {
                userInfoTemp.title = t('AddOrUpdateSavedAddressComponent.cong_ty');
              }">
              {{ $t('AddOrUpdateSavedAddressComponent.cong_ty') }}
            </button>
            <button class="title-option"
              :class="{ 'active': userInfoTemp.title != t('AddOrUpdateSavedAddressComponent.nha_rieng') && userInfoTemp.title != t('AddOrUpdateSavedAddressComponent.cong_ty') }"
              v-on:click="() => {
                userInfoTemp.title = '';
              }">
              {{ $t('AddOrUpdateSavedAddressComponent.khac') }}
            </button>
          </div>
          <input
            v-if="userInfoTemp.title != $t('AddOrUpdateSavedAddressComponent.nha_rieng') && userInfoTemp.title != $t('AddOrUpdateSavedAddressComponent.cong_ty')"
            :title="$t('AddOrUpdateSavedAddressComponent.ten_dia_chi')" name='customer-address' maxLength=255
            autoComplete="on" class='input-order'
            :placeholder="$t('AddOrUpdateSavedAddressComponent.ten_dia_chi_placeholder')"
            :value="userInfoTemp.title || null" v-on:input="($event: any) => {
              userInfoTemp.title = $event.target.value;
              if (userInfoTemp.title && userInfoTemp.title.length > 0) {
                userInfoTemp.titleErr = ''
              }
              else {
                userInfoTemp.titleErr = $t('AddOrUpdateSavedAddressComponent.vui_long_nhap_ten_dia_chi');
              }
            }" />
          <span class='error-message'>{{ userInfoTemp.titleErr }}</span>
        </div> -->

        <div class="v-stack image">
          <span class="label">{{ t('AddOrUpdateSavedAddressComponent.anh') }} <em>({{ images?.length ?? 0
          }}/3)</em></span>
          <div class="image-list">
            <div class="select-image" v-if="images.length < 3">
              <label :disabled="images.length >= 3">
                <Icon name="ion:plus-round"></Icon>
                <input type="file" accept='image/*' :multiple="true" v-on:click="(e) => {
                  if (images.length >= 3) {
                    e.preventDefault()
                  }
                }" v-on:change="($event: any) => {
                    fileChangeInput($event)
                  }" ref="imageFilesName" />
              </label>
            </div>
            <div class="selected-image" v-for="(itemImamge, index) in images">
              <img :src="itemImamge?.id ? (domainImage + itemImamge.path) : itemImamge.src" />
              <div class="action-overlay">
                <button class="delete-image" v-on:click="() => {
                  images.splice(index, 1)
                }">
                  <Icon name="bi:trash3-fill"></Icon>
                </button>
              </div>

            </div>
          </div>
        </div>

        <div class='v-stack'>
          <span class='label optional'>
            {{ $t('AddOrUpdateSavedAddressComponent.ghi_chu_cho_dia_chi') }}
            <em class="text-length">({{ userInfoTemp.note?.length ?? 0 }}/{{ appConst.max_text_short }})</em>
          </span>
          <!-- <textarea :title="$t('AddOrUpdateSavedAddressComponent.ghi_chu_cho_dia_chi')" name='address-note' type="text"
            rows="3" autoComplete="on" class='textarea-order' :maxlength="appConst.max_text_long"
            :placeholder="$t('AddOrUpdateSavedAddressComponent.ghi_chu_cho_dia_chi_placeholder')"
            v-model="userInfoTemp.note"></textarea> -->
          <UTextarea autoresize variant="outline" :title="$t('AddOrUpdateSavedAddressComponent.ghi_chu_cho_dia_chi')"
            name='address-note' :maxlength="appConst.max_text_long" type="text" :maxrows="5" :rows="3" autoComplete="on"
            class='input-address' :placeholder="$t('AddOrUpdateSavedAddressComponent.ghi_chu_cho_dia_chi_placeholder')"
            v-model="userInfoTemp.note" />
        </div>
      </div>

      <div class='h-stack edit-customer-info-actions'>
        <button class='reject-button' :disabled="isSaving" v-on:click="() => {
          close();
        }">
          {{ $t('AddOrUpdateSavedAddressComponent.dong') }}
        </button>
        <button class='accept-button' :disabled="isSaving
          || !userInfoTemp.name.length
          || !userInfoTemp.phone.length
          || !userInfoTemp.address.length
          // || !userInfoTemp.title.length
          || userInfoTemp.nameErr?.length > 0
          || userInfoTemp.phoneErr?.length > 0
          || userInfoTemp.addressErr?.length > 0
          // || userInfoTemp.titleErr?.length > 0
          " v-on:click="() => {
            saveUserInfo()
          }">
          <span v-if="!isSaving">{{ $t('AddOrUpdateSavedAddressComponent.luu') }}</span>
          <Icon name="eos-icons:loading" size="20" v-else />

        </button>
      </div>
    </div>

  </VueFinalModal>
</template>

<script lang="ts" setup>
import icon_for_product from "~/assets/image/icon-for-product.png";
import none_save_user from "~/assets/image_08_05_2024/none-save-user.jpg";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import marker_location_icon from "~/assets/image/marker-location.png";
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import exifr from "exifr";
import {
  appConst,
  appDataStartup,
  baseLogoUrl,
  domain,
  domainImage,
  formatCurrency,
  formatNumber,
  validPhone,
  zaloConfig
} from "~/assets/AppConst";
import appRoute from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import non_avatar from "~/assets/image/non-avatar.jpg";
import { PublicService } from "~/services/publicService/publicService";

import type { CartDto } from "~/assets/appDTO";
import { VueFinalModal } from "vue-final-modal";
import { UserAddressService } from "~/services/userAddressService/userAddressService";
import { HttpStatusCode } from "axios";

const router = useRouter();
const route = useRoute();
const nuxtApp = useNuxtApp();

const { t } = useI18n()
const props = defineProps({
  mode: null,
  selectedObject: null
})

const emit = defineEmits([
  'close',
])

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var userAddressService = new UserAddressService();
var imageService = new ImageService();
var publicService = new PublicService();

var selectedIndex = ref(null as any);
var showModalInfo = ref(false);
var firstLoad = ref(true)
var userInfoTemp = ref({
  id: null,
  name: "",
  address: "",
  phone: "",
  nameErr: "",
  phoneErr: "",
  addressErr: "",
  note: null as any,
  latitude: null as any,
  longitude: null as any,
  province_id: null,
  district_id: null,
  ward_id: null,
  // title: t('AddOrUpdateSavedAddressComponent.nha_rieng'),
  // titleErr: "",
  is_default: false,
});

var leafletMap: L.Map;
var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('Map.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var buttonMapTileBackgound = ref(map_sateline);

var webInApp = ref(null as any);
var searchAddressTimeout: any;

var isSaving = ref(false);


var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
// watch(() => [user_latitude.value, user_longitude?.value], () => {
//   userInfoTemp.value.latitude = user_latitude?.value;
//   userInfoTemp.value.longitude = user_longitude?.value;
// });

var changeAddressOnMoving = ref(true);

var coordinatesText = ref("");
var images = ref(props.selectedObject?.images ? JSON.parse(JSON.stringify(props.selectedObject?.images)) : []);
var imageFilesName = ref(null as any);
watch(() => [userInfoTemp.value.latitude, userInfoTemp.value.longitude], () => {
  coordinatesText.value = `${userInfoTemp.value.latitude}, ${userInfoTemp.value.longitude}`
})
onUnmounted(() => {
  nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onBeforeMount(async () => {
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})

onMounted(async () => {
  // let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

  // dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];
  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

  console.log(props.selectedObject);
  userInfoTemp.value = JSON.parse(JSON.stringify(props.selectedObject));
  console.log(userInfoTemp.value);
  showModalInfo.value = true
});

function phoneValidation() {
  let re = appConst.validateValue.phone;
  if (!validPhone(userInfoTemp.value.phone) || !validPhone(userInfoTemp.value.phone).length) {
    userInfoTemp.value.phoneErr = t('AddOrUpdateSavedAddressComponent.vui_long_nhap_sdt')
    return;
  }
  if (!re.test(validPhone(userInfoTemp.value?.phone))) {
    userInfoTemp.value.phoneErr = t('AddOrUpdateSavedAddressComponent.sdt_khong_dung');
    return;
  }
  else {
    userInfoTemp.value.phoneErr = '';
  }
}

async function initleafletMap() {
  await setCurrentLocationLeafletEdit();
  // (leafletMap as any)["gestureHandling"].enable();
  setTimeout(() => {
    firstLoad.value = false;
  }, 2000);
}

async function setCurrentLocationLeafletEdit() {
  if (userInfoTemp.value.latitude && userInfoTemp.value.longitude) {
    leafletMap.setView([parseFloat(userInfoTemp.value.latitude), parseFloat(userInfoTemp.value.longitude)], 17);
    // setLocationLeafletMarker(latitude.value, longitude.value);
  } else {
    // if ("geolocation" in navigator) {
    //   navigator.geolocation.getCurrentPosition(
    //     (position) => {

    //       userInfoTemp.value.latitude = position.coords.latitude.toString();
    //       userInfoTemp.value.longitude = position.coords.longitude.toString();
    //       leafletMap.setView(
    //         [position.coords.latitude, position.coords.longitude],
    //         17
    //       );
    //       // getUserAddress()
    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     (error) => {
    //       toast.warning(t('AddOrUpdateSavedAddressComponent.chua_cung_cap_vi_tri'), {
    //         autoClose: 1000,
    //         hideProgressBar: true,
    //       });
    //       userInfoTemp.value.latitude = appConst.defaultCoordinate.latitude.toString();
    //       userInfoTemp.value.longitude = appConst.defaultCoordinate.longitude.toString();
    //       leafletMap.setView([parseFloat(userInfoTemp.value.latitude), parseFloat(userInfoTemp.value.longitude)], 17);
    //       // getUserAddress()
    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     {
    //       enableHighAccuracy: false, // Use less accurate but faster methods
    //       timeout: 5000, // Set a timeout (in milliseconds)

    //     }
    //   );
    // }

    userInfoTemp.value.latitude = user_latitude.value ?? appConst.defaultCoordinate.latitude;
    userInfoTemp.value.longitude = user_longitude.value ?? appConst.defaultCoordinate.longitude;
    leafletMap.setView(
      [user_latitude.value, user_longitude.value],
      17
    );
  }
}
function getUserAddress() {
  clearTimeout(searchAddressTimeout);
  searchAddressTimeout = setTimeout(() => {
    placeService
      .myGeocoderByLatLngToAddress(parseFloat(userInfoTemp.value.latitude), parseFloat(userInfoTemp.value.longitude))
      .then((res: any) => {
        if (res.body.data && res.body.data.length) {
          if (changeAddressOnMoving.value) {
            userInfoTemp.value.address = res.body.data[0].address ? res.body.data[0].address : "";
            userInfoTemp.value.province_id = res.body.data[0].province_id ? res.body.data[0].province_id : null;
            userInfoTemp.value.district_id = res.body.data[0].district_id ? res.body.data[0].district_id : null;
            userInfoTemp.value.ward_id = res.body.data[0].ward_id ? res.body.data[0].ward_id : null;
          }
        }
      });
  }, 500);
}

async function gotoCurrentLocationLeaflet(event?: Event) {
  if (!event || event.isTrusted == true) {
    // if ("geolocation" in navigator) {
    //   navigator.geolocation.getCurrentPosition(
    //     (position) => {
    //       userInfoTemp.value.latitude = position.coords.latitude.toString();
    //       userInfoTemp.value.longitude = position.coords.longitude.toString();
    //       leafletMap.flyTo(
    //         [position.coords.latitude, position.coords.longitude],
    //         17
    //       );
    //       getUserAddress()
    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     (error) => {
    //       toast.warning(t('AddOrUpdateSavedAddressComponent.chua_cung_cap_vi_tri'), {
    //         autoClose: 1000,
    //         hideProgressBar: true,
    //       });
    //       userInfoTemp.value.latitude = appConst.defaultCoordinate.latitude.toString();
    //       userInfoTemp.value.longitude = appConst.defaultCoordinate.longitude.toString();
    //       leafletMap.flyTo([parseFloat(userInfoTemp.value.latitude), parseFloat(userInfoTemp.value.longitude)], 17);
    //       getUserAddress()
    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     {
    //       enableHighAccuracy: false, // Use less accurate but faster methods
    //       timeout: 5000, // Set a timeout (in milliseconds)

    //     }
    //   );
    // }
    userInfoTemp.value.latitude = user_latitude.value;
    userInfoTemp.value.longitude = user_longitude.value;
    leafletMap.flyTo(
      [userInfoTemp.value.latitude, userInfoTemp.value.longitude],
      17
    );
    getUserAddress()

  }
}

async function saveUserInfo() {
  isSaving.value = true;
  let info = {
    id: userInfoTemp?.value?.id,
    name: userInfoTemp?.value?.name,
    address: userInfoTemp?.value?.address,
    phone: validPhone(userInfoTemp?.value?.phone),
    latitude: userInfoTemp?.value?.latitude,
    longitude: userInfoTemp?.value?.longitude,
    province_id: userInfoTemp?.value?.province_id,
    district_id: userInfoTemp?.value?.district_id,
    ward_id: userInfoTemp?.value?.ward_id,
    // title: userInfoTemp?.value?.title && userInfoTemp?.value?.title?.length ? userInfoTemp?.value?.title : " ",
    title: null,
    is_default: userInfoTemp?.value?.is_default ? userInfoTemp?.value?.is_default : false,
    note: userInfoTemp?.value?.note,
    image_delete: await handleGetListDeleteImage(),
    images: await handleGetListUpdateImage()
  }
  if (userInfoTemp?.value?.id) {
    // dataSavedUsers.value[selectedIndex.value] = JSON.parse(JSON.stringify(info))
    userAddressService.update(info).then(async (res) => {
      var handleSaveNewImage = async () => {
        images.value.forEach(async (img: any, index: any) => {
          let image_comment = {
            path: img.src,
            object_type: appConst.object_type.user_address,
            title: `${userInfoTemp.value?.address.slice(0, 20)}...`,
            description: `${userInfoTemp.value?.address.slice(0, 20)}...`,
            index: index,
            orientation: img.orientation,
            isEdit: false,
            parent_id: res.body.data.id,
            is_profile_picture: index == 0 ? true : false
          }

          if (!img.id) {
            await imageService.insertImage(image_comment)
          }
        })
        return;
      }
      if (res.status == HttpStatusCode.Ok) {
        if (images.value?.length) {
          try {
            await handleSaveNewImage()
          }
          finally {
            isSaving.value = false;
            toast.success(t('AddOrUpdateSavedAddressComponent.cap_nhat_thanh_cong'));
            setTimeout(() => {
              close(true)
            }, 1500);
          }
        }
        else {
          isSaving.value = false;
          toast.success(t('AddOrUpdateSavedAddressComponent.cap_nhat_thanh_cong'));
          close(true)
        }
      }
      else {
        toast.error(t('AddOrUpdateSavedAddressComponent.cap_nhat_that_bai'))
        isSaving.value = false;
      }
    })
  }
  else {
    // dataSavedUsers.value.push(JSON.parse(JSON.stringify(info)));
    userAddressService.create(info).then(async (res) => {
      var addImages = async () => {
        await images.value.forEach(async (imageItem: any, index: number) => {
          let profile_picture_ref = {
            path: imageItem.src,
            object_type: appConst.object_type.user_address,
            title: `${userInfoTemp.value?.address.slice(0, 20)}...`,
            description: `${userInfoTemp.value?.address.slice(0, 20)}...`,
            index: 0,
            orientation: imageItem.orientation,
            isEdit: false,
            parent_id: res.body.data.id
          }
          await imageService.insertImage(profile_picture_ref);

        });
        return
      };

      if (res.status == HttpStatusCode.Ok) {
        try {
          // await addImages()
          await addImages();
        }
        finally {
          isSaving.value = false;
          toast.success(t('AddOrUpdateSavedAddressComponent.cap_nhat_thanh_cong'));
          setTimeout(() => {
            close(true)
          }, 1500);
        }

      }
      else {
        toast.error(t('AddOrUpdateSavedAddressComponent.luu_dia_chi_that_bai'))
        isSaving.value = false;
      }
    })
  }
}

function jumpToCoordinate() {
  let coorArr = coordinatesText.value?.split(",");
  console.log(coorArr);
  if (coorArr.length == 2) {

    if (leafletMap) {
      leafletMap.flyTo([userInfoTemp.value.latitude, userInfoTemp.value.longitude], 17, {
        duration: 1
      })
    }
  }
}

function handleGetListDeleteImage() {
  let listDelete: any[] = [];
  props.selectedObject?.images.forEach((img: any, index: number) => {
    let indexInCurrentList = images.value?.findIndex(function (e: any) {
      return e.id == img.id;
    })
    if (indexInCurrentList == -1) {
      listDelete.push({ id: img.id })
    }
  });
  return listDelete;
}

function handleGetListUpdateImage() {
  let listUpdate: any[] = [];
  images.value.forEach((img: any, index: number) => {
    let indexInCurrentList = props.selectedObject?.images?.findIndex(function (e: any) {
      return e.id == img.id;
    })
    if (indexInCurrentList != -1) {
      let updateImg;
      updateImg = {
        id: img.id,
        title: img.title,
        index: indexInCurrentList,
        is_profile_picture: indexInCurrentList == 0 ? true : false
      }
      listUpdate.push(updateImg)
    }
  });
  return listUpdate;
}
async function fileChangeInput(fileInput: any, childProduct?: any) {
  if (fileInput.target.files.length) {
    if ((fileInput.target.files.length + images.value.length) > 3) {
      toast.error(t('AddOrUpdateSavedAddressComponent.so_luong_toi_da', { amount: 3 }))
    }
    else {
      imageFilesName.value = fileInput.target.files;
      for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
        if (fileInput.target.files[i].size > appConst.image_size.max) {
          let imgErr = t('AddOrUpdateSavedAddressComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
          // toast.error(imgErr);
        }
        else {
          const reader = new FileReader();
          reader.onload = async (e: any) => {
            const image = new Image();
            image.src = e.target.result;

            let orientationExif;
            let imgTemp = {
              src: "",
              orientation: 0,

            };
            if (fileInput.target.files[i].type != 'image/webp') {
              orientationExif = await exifr.orientation(image) || 0;
            }
            else orientationExif = 0;
            imgTemp.src = image.src;
            imgTemp.orientation = orientationExif ? orientationExif : 0;
            images.value.push(imgTemp);
          }
          await reader.readAsDataURL(fileInput.target.files[i]);
        }
      };
    }
  }
}

function close(refresh = false) {
  emit('close', refresh);
}
</script>

<style lang="scss" src="./AddOrUpdateSavedAddressStyles.scss"></style>
