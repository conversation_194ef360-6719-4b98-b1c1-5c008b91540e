.agent-order-detail-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    align-items: center;
    justify-content: flex-start;
    // overflow: auto;
    
    &>.title-header {
        // font-size: 1.3em;
        // padding: 5px 0;
        // margin: 0;
        // text-align: center;
        // width: 100%;
        border-bottom: none;
        background-color: var(--primary-color-1);
        color: white;
        // display: flex;
        // justify-content: center;
        // font-size: 1.6em;
        // align-items: center;

        & button {
            background: inherit;
        }

        // & h3 {
        //     margin: 0;
        // }
    }

    & .main-stack {
        width: 100%;
        padding: 15px;
        background-color: white;
        margin-bottom: 15px;
        color: #313036;

        & > .first-content{
            justify-content: space-between;
            align-items: flex-start;
            gap: 5px;

            & > .title-main-stack{
                gap: 10px;
                display: flex;
                align-items: center;
                font-weight: bold;
                align-self: baseline;

                & > svg{
                    font-size: 20px;
                    color: var(--primary-color-1);
                    align-self: baseline;
                }

                & .customer-phone {
                    color: #828187;
                }
            }
            & > .action.copy{
                text-transform: uppercase;
            }

            & .action {
                gap: 5px; 
            }
            & .call-customer{
                color: #5f5e66;
                font-size: 15px;
                padding: 2px 7px;
                border-radius: 5px ;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 5px;
                border: thin solid #ecebf3;
                font-weight: 600;
                white-space: nowrap;

                & > svg {
                    width: 20px;
                }
                
                & > img {
                    border-radius: 50%;
                    border: thin solid #ecebf3;
                    width: 20px;
                }


            }
            
        }

        & > .primary-content{
            display: flex;
            padding: 0 0 0 30px;
            align-items: flex-start;
            gap: 5px;

            & > .content-detail{
                display: flex;
                flex-direction: column;
                font-weight: 600;
                color: #828187;
                white-space: break-spaces;

                & > span.status-name {
                    display: list-item;
                }

                & > span.status-name::marker{
                    color: var(--primary-color-1);
                    font-size: 18px;
                }
            }

            & .customer-name{
                color: #313036;
                font-weight: bold;
            }

            
        }

        & button.action{
            color: var(--primary-color-1);
            font-weight: bold;
        }

    }

    &>.h-stack {
        width: 100%;
        padding: 10px;
        background: white;
    }

    &>.v-stack {
        flex: 1;
        width: 100%;
        background: var(--color-background-2);
        justify-content: flex-start;
        align-items: flex-start;
        overflow: auto;
    }

    & .not-existing-order {
        flex: 1 1;
        justify-content: flex-start;
        align-items: center;
        margin-top: 10px;
        width: 100%;
        display: flex;
        gap: 10px;
        flex-direction: column;

        &>img {
            width: 80%;
            object-fit: contain;
        }

        &>p {
            font-size: 1.75em;
        }
    }

    & .user-avatar {
        border-radius: 50%;
        width: 50px;
        min-width: 50px;
        height: 50px;
        object-fit: cover;
    }

    & .user-detail {
        flex: 1;
        align-items: flex-start;
        justify-content: flex-start;
        padding-left: 30px;
        gap: 10px;

        & .name {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 1.2em;

            &>span {
                font-weight: 600;
            }
        }

        & .phone {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 1.2em;

            & > .contact-customer{
                padding: 0 5px;
                color: var(--primary-color-1);
            }
        }

    }

    & .address-detail {
        font-size: 1.1em;
        gap: 10px;
        margin-top: 5px;

        &>.address {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }
    }

    & .order-items-container {
        background-color: transparent;
    }

    & .item-order-container {
        padding: 5px;
        align-items: flex-start;
        width: 100%;
        margin-top: 10px;
        gap: 5px;
        flex: 1;
        background-color: white;
        // border-bottom: thin solid #ddd;

        &>.item-order-avatar {
            width: 100px;
            height: 100px;
            position: relative;

            &>img {
                object-fit: cover;
                border-radius: 10px;
                background-color: var(--color-background-2);
                width: 100%;
                height: 100%;
                aspect-ratio: 1;
            }

        }

        &>.item-order-detail {
            gap: 5px;
            height: 100%;
            min-height: 100px;
            flex: 1;
            padding: 5px 0 5px 5px;


            &>.item-order-name {
                font-size: 1.1em;
                font-weight: 600;
            }

            &>.item-order-note {
                font-size: .9em;
                font-style: italic;
                color: var(--color-text-note);
            }

            &>.item-order-quantity-price {
                font-size: 1em;
                display: flex;
                gap: 10px;
                font-weight: 600;
                color: #828187;

                & > .price{
                    color: var(--primary-color-1);
                }

                & em {
                    color: var(--color-text-note);
                    font-style: normal;
                }

                & em.off{
                    text-decoration: line-through;
                    font-style: normal;
                    font-size: .9em;
                }
            }
        }
    }

    & .payment-detail {
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: thin solid #ebeaef;

        &>.label {
            color: #828187;
            font-size: 1em;
            font-weight: 400;
        }

        &>.data {
            font-size: 1em;
            font-weight: 500;
        }
    }

    & .payment-detail.total {
        font-size: 1.1em;
        flex-wrap: wrap;
        border-bottom: none;

        &>.label {
            font-weight: 600;
            color: #313036;
        }

        &>.data {
            color: #313036;
            font-weight: bold;
        }

        & > .note{
            color: #313036;
            font-weight: 600;
            text-align: right;
            width: 100%;
            font-size: .9em;

        }
    }

    & .actions-stack {
        justify-content: space-evenly;
        gap: 10px;
        position: sticky;
        bottom: 0;
        left: 0;

        & > button {
            flex: .5;
            color: #828187;
            border: thin solid #828187;
        }

        & > button.accept-button {
            flex: .5;
            color: white;
            border: thin solid #1a95ff;
            background: #1a95ff;
        }

        & > button.view-another{
            flex: 1;
            color: var(--primary-color-1);
            border-color: var(--primary-color-1);
        }
    }
}

.cancel-order-content {
    font-size: 1.3em;
    text-align: center;
}

.cancel-order-title {
    font-size: 1em;
    font-weight: bold;
}

.cancel-order-message {
    font-size: 1em;
    text-align: center;
}

.cancel-order-message>.order-code {
    color: var(--primary-color-2);
    font-weight: bold;
}

.delivery-type{
    color: var(--primary-color-1);
    font-weight: 600;
    text-align: center;
}

.confirm-cancel-order-modal{
    max-height: 95dvh;
    // height: 95dvh;
    width: 500px;
    // max-height: 90%;
    max-width: 95%;
    // min-height: 40dvh;
    border-radius: 10px;
    padding: 10px;
    background-color: white;
}
// .edit-order-container{
//     height: 90dvh !important;
//     height: 87vh;
//     overflow: auto;
// }
