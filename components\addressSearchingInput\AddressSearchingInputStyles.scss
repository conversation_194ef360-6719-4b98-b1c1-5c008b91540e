.search-address-container{
  display: flex;
  align-items: center;
  border-radius: 5px;
  color: #2e2d30;
  position: relative;
  background: white;
  z-index: 500;
  flex: 1;

  & input {
    background-color: white;
    height: 30px;
    padding: 3px 0 3px 10px;
    border-radius: 5px;
    outline: none;
    color: #2e2d30;
    font-weight: 600;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
  }

  & > .icon-right{
    padding: 0 10px;
    font-size: 20px;
    color: #2e2d30;
    cursor: pointer;
    display: flex;
  }

  & .address-suggest-list-container{
    position: absolute;
    margin-top: 5px;
    top: 100%;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    & .address-suggest-item{
      width: 100%;
      font-size: 15px !important;
    }
  }

  & .none-address-searching{
    font-style: italic;
    text-align: center;
    width: 100%;
  }
}