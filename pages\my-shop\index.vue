<template>
	<!-- <NuxtLoadingIndicator /> -->
	<MyShopComponent />
</template>
<script lang="ts" setup>

import { appConst } from '~/assets/AppConst';
import { AuthService } from '~/services/authService/authService';
import { appRoute } from '~/assets/appRoute';
import { useRouter, useRoute } from 'vue-router';
import { useNuxtApp, onBeforeMount } from '#imports';
const router = useRouter();
const route = useRoute();
const authService = new AuthService();
const nuxtApp = useNuxtApp();

onBeforeMount(async () => {
    (nuxtApp as any).$emit(appConst.event_key.show_footer, false);
    const user = await authService.checkAuth();
    if (!user) {
        nuxtApp.$emit(appConst.event_key.require_login, {
			redirect_url: route.path,
			back_on_close: true
		})
    }
});

function onBeforeMount_(arg0: () => Promise<void>) {
    throw new Error('Function not implemented.');
}
</script>
<!-- <script setup>
import { appConst } from '~/assets/AppConst';

const  slug = useRoute().params
console.log(slug)
const url = appConst.apiURL.deleteProduct + slug;
const { data: product } = await $fetch(url, { key: slug });
console.log(product.value);
</script> -->