<template>
    <VueFinalModal class="my-modal-container" :modal-id="'update_message_modal'" :keep-overlay="true" :overlay-behavior="'persist'"
        :hide-overlay="false" :hide-overlay-on-blur="false" content-class="v-stack form-modal update-message-container"
        :click-to-close="false" :esc-to-close="false" v-on:before-close="() => {
            showUpdateMessageModal = false
        }" v-model="showUpdateMessageModal" contentTransition="vfm-slide-down">
        <SubHeaderV2Component :title="props.title ?? $t('UpdateMessageComponent.tin_nhan')">
            <template v-slot:header_left></template>
        </SubHeaderV2Component>
        <div class="update-message-content-container">
            <VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
                class="update-message-content">
                <div class="form-field-container">
                    <label class="optional" for="name_input">{{
                        $t('LocalizedLanguageName.vi') }}</label>
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t(`UpdateMessageComponent.tieng_viet_placeholder_${props.message_type == 'greeting' ? 'loi_chao' : props.message_type == 'default_delivery_message' ? 'thong_bao_don_giao_mac_dinh' : 'thong_bao'}`)" class="custom-input" :validate-on-input="true"
                            :name="`message_vi`" type="text" id="message_vi_value_input"
                            :as="props.message_type == 'greeting' ? 'input' : 'textarea'"
                            v-bind:model-value="messageData?.vi" v-on:update:model-value="($event: any) => {
                                messageData.vi = $event;
                            }">
                        </Field>
                    </div>
                    <div class="h-stack">
                        <ErrorMessage class="error-message" name="message_vi"></ErrorMessage>
                        <em class="length-content">{{ messageData?.vi?.length ?? 0 }}/{{ props.max_content }}</em>
                    </div>
                </div>

                <div class="form-field-container">
                    <label class="optional" for="name_input">{{
                        $t('LocalizedLanguageName.en') }}</label>
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t(`UpdateMessageComponent.tieng_anh_placeholder_${props.message_type == 'greeting' ? 'loi_chao' : props.message_type == 'default_delivery_message' ? 'thong_bao_don_giao_mac_dinh' : 'thong_bao'}`)" 
                            class="custom-input" :validate-on-input="true"
                            :name="`message_en`" type="text" id="message_en_value_input"
                            :as="props.message_type == 'greeting' ? 'input' : 'textarea'"
                            v-bind:model-value="messageData?.en" v-on:update:model-value="($event: any) => {
                                messageData.en = $event;
                            }">
                        </Field>
                    </div>
                    <div class="h-stack">
                        <ErrorMessage class="error-message" name="message_en"></ErrorMessage>
                        <em class="length-content">{{ messageData?.en?.length ?? 0 }}/{{ props.max_content }}</em>
                    </div>
                    
                </div>

                <div class="form-field-container">
                    <label class="optional" for="name_input">{{
                        $t('LocalizedLanguageName.ko') }}</label>
                    <div class="h-stack input-group-container">
                        <Field 
                            :placeholder="$t(`UpdateMessageComponent.tieng_han_placeholder_${props.message_type == 'greeting' ? 'loi_chao' : props.message_type == 'default_delivery_message' ? 'thong_bao_don_giao_mac_dinh' : 'thong_bao'}`)" 
                            class="custom-input" :validate-on-input="true"
                            :as="props.message_type == 'greeting' ? 'input' : 'textarea'"
                            :name="`message_ko`" type="text" id="message_ko_value_input"
                            v-bind:model-value="messageData?.ko" v-on:update:model-value="($event: any) => {
                                messageData.ko = $event;
                            }">
                        </Field>
                    </div>
                    <div class="h-stack">
                        <ErrorMessage class="error-message" name="message_ko"></ErrorMessage>
                        <em class="length-content">{{ messageData?.ko?.length ?? 0 }}/{{ props.max_content }}</em>
                    </div>
                    
                </div>

                <div class="form-field-container">
                    <label class="optional" for="name_input">{{
                        $t('LocalizedLanguageName.ru') }}</label>
                    <div class="v-stack input-group-container">
                        <Field :placeholder="$t(`UpdateMessageComponent.tieng_nga_placeholder_${props.message_type == 'greeting' ? 'loi_chao' : props.message_type == 'default_delivery_message' ? 'thong_bao_don_giao_mac_dinh' : 'thong_bao'}`)" class="custom-input" :validate-on-input="true"
                            :name="`message_ru`" type="text" id="message_ru_value_input"
                            :as="props.message_type == 'greeting' ? 'input' : 'textarea'"
                            v-bind:model-value="messageData?.ru" v-on:update:model-value="($event: any) => {
                                messageData.ru = $event;
                            }">
                        </Field>
                    </div>

                    <div class="h-stack">
                        <ErrorMessage class="error-message" name="message_ru"></ErrorMessage>
                        <em class="length-content">{{ messageData?.ru?.length ?? 0 }}/{{ props.max_content }}</em>
                    </div>
                    
                </div>

                <button hidden ref="submitFormButton" v-on:click="() => {
                    submit()
                }"></button>
            </VeeForm>

        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                {{ $t('UpdateMessageComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => {
                submitFormButton?.click();
            }">
                <span>{{ $t('UpdateMessageComponent.luu') }}</span>
            </button>
        </div>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, nonAccentVietnamese } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { DeliveryFeeConfig, type delivery_fee_condition, type delivery_fee_config, condition_params, operators, condition_relationship } from '../ShopConfigDTO';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    init_value: null,
    mode: null,
    title: null,
    message_type: null,
    max_content: null
})

var nuxtApp = useNuxtApp();
const { t, locale, locales, availableLocales } = useI18n();

var submitFormButton = ref<HTMLElement | undefined>();
var messageData = ref<any>({
    vi: null,
    en: null,
    ko: null,
    ru: null
})
const formSchema = yup.object({
    message_vi: yup.string().notRequired().max(props.max_content ?? 255, t('UpdateMessageComponent.toi_da_x_ky_tu', {x: props.max_content ?? 255})),
    message_en: yup.string().notRequired().max(props.max_content ?? 255, t('UpdateMessageComponent.toi_da_x_ky_tu', {x: props.max_content ?? 255})),
    message_ko: yup.string().notRequired().max(props.max_content ?? 255, t('UpdateMessageComponent.toi_da_x_ky_tu', {x: props.max_content ?? 255})),
    message_ru: yup.string().notRequired().max(props.max_content ?? 255, t('UpdateMessageComponent.toi_da_x_ky_tu', {x: props.max_content ?? 255})),
});
const { handleSubmit } = useForm({
    validationSchema: formSchema,
});

var showUpdateMessageModal = ref(false)

var showConditionSelectValues = ref(false);
var indexSelectedCondition = ref<any>(null);
onBeforeUnmount(() => {
})
onBeforeMount(() => {

})
onMounted(async () => {
    messageData.value = props.init_value ?? messageData.value
    showUpdateMessageModal.value = true;
})
onUpdated(() => {
})

function close(value?: any) {
    emit('close', value);
}

async function submit() {

    var valid = await formSchema.isValid({
        message_vi: messageData.value?.vi,
        message_en: messageData.value?.en,
        message_ko: messageData.value?.ko,
        message_ru: messageData.value?.ru,
    });
    if (valid) {
        emit('submit', JSON.parse(JSON.stringify(messageData.value)));
    }
    else {
        console.log('form invalid')
    }
}

</script>

<style lang="scss" src="./UpdateMessageStyles.scss"></style>