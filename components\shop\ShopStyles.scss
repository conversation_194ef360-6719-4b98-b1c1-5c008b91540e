/* .loading-skeleton{
        padding: 0 5px;
    } */
@media screen and (max-width: 720px) {
  .product-item-container-grid > img {
    height: 47vw;
  }
}

@media screen and (max-width: 500px) {
  .item-open-time {
    display: flex;
    align-items: center;
    // justify-content: space-between;

    & > .days {
      font-size: 15px !important;
      width: 40% !important;
    }

    & > .times {
      font-size: 17px !important;
      width: 59% !important;
    }
  }
}
.public-container {
  & > .footer-absolute {
    position: absolute;
    bottom: 0;

    & > .go-up {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      font-size: 30px;
      background: var(--primary-color-1);
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      z-index: 100;
      margin: 15px 15px 15px auto;
    }
  }
}

.loading-skeleton {
  gap: 10px;

  .h-stack,
  .v-stack {
    padding: 0 10px;
    gap: 5px;
  }
}

.avt-skeleton {
  margin-right: auto;
}
.title-header.shop-title-header {
  margin: 5px 0;
  top: 10px;
  left: unset;
  z-index: 10;
  background: transparent;
  position: absolute !important;
  width: 100%;
  padding: 10px 15px 0;
  border: none;
  filter: drop-shadow(0px 3px 5px rgb(0, 0, 0, 0.5));
  position: absolute;
  top: 0;

  & > .header-left {
    flex: none;
    margin-right: 10px;
    > button {
      background-color: white;
      width: 30px;
      height: 30px;
      color: #c3c5c7;
    }
  }

  & > .search-input-container {
    height: 40px;
    padding: 5px 15px;
    border-radius: 2em;
    color: #6f6f6f;
    background: white;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;

    & > svg {
      width: 20px;
      height: 20px;
    }

    & > input {
      flex: 1;
      outline: none;
    }

    & > input::placeholder {
      color: #646464;
    }
  }

  & > .header-right {
    flex: none;
    margin-left: 10px;
    font-size: 1em;

    & > .cart-in-search {
      animation: none;
      width: 30px;
      height: 30px;
      color: white;
      position: relative;

      & > em {
        border-radius: 2em;
        color: white;
        background: var(--color-button-error);
        min-width: 15px;
        width: fit-content;
        height: 15px;
        padding: 2px;
        font-size: 0.8em;
        display: flex;
        align-items: center;
        justify-content: center;
        font-style: normal;
        position: absolute;
        bottom: -3px;
        right: -3px;
        line-height: 1;
        font-weight: 500;

        & > span {
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.shop-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  /* min-height: inherit; */
  /* border-radius: 10px; */
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.3em;
  // overflow: auto;
  position: relative;

  & > div {
    width: 100%;
  }

  .none-shop-container {
    padding-top: 75px;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
    flex: 1;
  }

  .none-shop-container > img {
    width: 100%;
    align-items: center;
    border-radius: 50%;
    height: 200px;
    object-fit: contain;
    position: relative;
  }

  .none-shop-container > span {
    font-size: 1.5em;
  }

  .product-select-overlay-container {
    overflow: hidden;

    @keyframes slide-up {
      0% {
        bottom: -50%;
      }
      100% {
        bottom: 0;
      }
    }
    .product-selected-container {
      background: white;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: fit-content;
      border-radius: 20px 20px 0 0;
      animation: slide-up 0.5s ease;

      & > .product-selected-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        text-align: center;
        font-size: 15px;
        gap: 5px;
        position: relative;
        padding: 10px 10px 20px 10px;

        & > .close {
          width: fit-content;
          align-self: flex-end;
          cursor: pointer;
          position: absolute;
        }

        & > img {
          width: 200px;
          height: 200px;
          margin: auto;
          border-radius: 10px;
          aspect-ratio: 1;
          object-fit: contain;
        }

        & > .name {
          color: #626262;
          font-size: 1.1em;
          font-weight: 600;
        }
        & > .price {
          color: #ed1b24;
          font-size: 1.3em;
          font-weight: bold;
          padding: 10px 0;
        }
        & > .origin-price {
          text-align: center;
          color: #626262;
          font-size: 1em;
          text-decoration: line-through;
        }

        & > .actions {
          justify-content: space-between;
          align-items: center;

          & > .quantity-actions {
            display: flex;
            width: 45%;
            justify-content: space-evenly;

            & > .quantity-button {
              color: #626262;
              font-size: 35px;
              display: flex;
            }

            & > span {
              display: flex;
              align-items: center;
              font-size: 1.6em;
              color: #ed1b24;
              font-weight: bold;
            }
          }

          & > .add-to-cart-action {
            border: thin solid #ed1b24;
            color: #ed1b24;
            border-radius: 10px;
            padding: 5px 15px;
            width: 55%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;

            & > .total-price {
              font-size: 1.3em;
            }

            & > .add-action {
              font-size: 0.9em;
            }
          }
        }
      }
    }
  }

  & > .shop-content-container {
    // overflow-y: auto;
    // overflow-x: hidden;
    font-size: 20px;
    position: relative;
    background: white;

    & > .shop-banner {
      height: 250px;
      width: 100%;
      min-height: 250px;
      object-fit: cover;
      overflow: hidden;
      background-color: white;
      position: relative;

      & > .banner-origin-container {
        transform: scale(calc(250 / var(--scale-origin)));
      }
      & img {
        // height: 100%;
        // width: 100%;
        object-fit: cover;
      }
    }

    & > .shop-detail-container {
      display: flex;
      // flex-wrap: wrap;
      padding: 15px;
      gap: 15px;
      & > .shop-logo {
        position: relative;
        border-radius: 25px;
        border: 0;
        align-self: flex-start;
        box-shadow: 0 0 35px rgba(0, 0, 0, 0.25);
      }

      & > .shop-detail-content {
        display: flex;
        flex-direction: column;
        gap: 5px;
        padding: 5px 0 0;
        font-size: 20px;
        line-height: 1.125;
        flex: 1;

        & > .name {
          color: #020202;
          font-weight: bold;
        }

        & > .business-types {
          color: #ef4e74;
          font-weight: 600;
          font-size: 0.8em;
        }

        & > .open-time {
          font-weight: 600;
          font-size: 15px;

          & > em {
            color: var(--primary-color-1);
          }
        }

        & > .address {
          font-size: 0.7em;
          font-weight: 500;
          color: #8c8b8f;
        }

        & > .actions {
          display: flex;
          justify-content: flex-start;
          margin-top: auto;
          position: relative;

          & > button, > a {
            color: #ef4e74;
            font-size: 30px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: auto;
          }

          & > button.share {
            color: #8b8a90;
            font-size: 40px;
          }
        }
      }
    }
    & > .line {
      width: calc(100% - 30px);
      border-bottom: thin solid #b3b3b5;
      margin: 0 auto;
    }
    & > .shop-detail-open-time {
      padding: 15px;

      & > .shop-detail-open-time-content {
        padding: 10px 15px;
        border-radius: 10px;
        background: #f5f6fa;
        display: flex;
        flex-direction: column;

        & > .label {
          font-size: 17px;
          color: black;
          font-weight: 500;
          text-transform: uppercase;
        }

        & > .list-open-time {
          display: flex;
          flex-direction: column;
          gap: 5px;
          font-size: 15px;

          & > .item-open-time {
            display: flex;
            align-items: center;
            // justify-content: space-between;

            & > .days {
              font-size: 17px;
              color: #ef4e74;
              font-weight: 600;
              width: 30%;
            }

            & > .times {
              font-size: 20px;
              color: #ef4e74;
              font-weight: bold;
              margin-right: auto;
            }
          }
        }
      }
    }
    & > .shop-detail-description {
      & > .description {
        font-size: 0.8em;
        color: #020202;
        line-height: 1.125;
        display: flex;
        flex-direction: column;
        transition: all 0.2s ease-in-out;
        max-height: calc(1.125 * 3);
        padding: 0 15px;
        margin: 15px 0;

        & > span {
          display: block;

          & > span {
            padding-bottom: 5px;
            white-space: break-spaces;
          }
        }
      }
    }

    & > .shop-products-container {
      display: flex;
      flex-direction: column;
      padding: 15px;
      font-size: 20px;
      position: relative;

      & > .title-stack {
        display: flex;
        justify-content: space-between;
        align-items: center;

        & > span {
          color: #38373c;
          font-size: 0.9em;
          font-weight: bold;
        }

        & > .button-fitler-sort {
          color: var(--primary-color-1);
          font-size: 0.8em;
          display: flex;
          align-items: center;

          & > svg {
            color: #595959;
            font-size: 40px;
          }
        }
      }
      & > .categories-container.sticky-padding {
        padding-top: 65px;
        transition: all 0.2s ease-in-out;
      }
      & > .categories-container {
        padding: 10px 0;
        font-size: 0.7em;
        position: sticky;
        top: 0;
        background: white;
        z-index: 1;

        & .category-item-slide {
          width: fit-content !important;
          padding: 1px 10px;
          border: thin solid #c4c4c4;
          color: #818181;
          border-radius: 5px;
          cursor: pointer;
        }

        & .category-item-slide.active {
          color: white;
          background-color: #ef4e74;
          border-color: #ef4e74;
        }
      }
      & > .products-container.grid {
        flex-direction: row;
        min-height: unset;
      }
      & > .products-container {
        flex-direction: column;
        flex-wrap: wrap;
        margin-top: 10px;
        position: relative;
        display: flex;
        justify-content: flex-start;
        row-gap: 5px;
        min-height: 100vh;

        & .category-products-container {
          padding: 10px;
          background: white;
          margin-top: 10px;
          border-radius: 10px 10px 0 0;
          display: flex;
          flex-direction: column;
          gap: 10px;
          // box-shadow: 0 0 15px rgb(0, 0, 0, 0.1);
          border-bottom: thin solid #efefef;

          & > .category-title {
            font-size: 17px;
            font-weight: 600;
          }
        }

        & .product-item-container {
          background: white;
          border-radius: 5px;
          width: 100%;
          gap: 5px;
          display: flex;
          margin-bottom: 5px;
          cursor: pointer;
          padding: 5px 5px 0;
          border: thin solid transparent;

          & > img {
            height: 100px;
            width: 100px;
            aspect-ratio: 1;
            object-fit: cover;
            align-self: flex-start;
            border-radius: 10px;
            background-color: var(--color-background-2);
          }

          & > .product-detail {
            align-items: flex-start;
            gap: 5px;
            flex: 1;
            min-height: 115px;
            padding: 5px 5px 15px 5px;
            // border-bottom: thin solid #c4c4c4;

            & > .product-name {
              font-size: 0.75em;
              color: #595959;
              font-weight: bold;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              overflow: hidden;
            }

            & > .h-stack {
              width: 100%;
              margin-top: auto;
              & > .product-price {
                color: #ed1b24;
                font-size: 0.8em;
                font-weight: bold;
                display: flex;
                flex-direction: column;
                line-height: 1.125;

                & > em.off {
                  font-size: 0.9em;
                  font-style: normal;
                  color: var(--color-text-note);
                  text-decoration: line-through;
                }
              }

              & > .add-to-cart-btn,
              > .edit-button {
                background: white;
                border: thin solid #ed1b24;
                color: #ed1b24;
                border-radius: 2em;
                font-size: 15px;
                padding: 5px 15px;
                font-weight: bold;
                margin-left: auto;
                white-space: nowrap;
              }
            }
          }
        }

        & .product-item-container-grid {
          background: white;
          border-radius: 5px;
          padding: 5px 5px 0;
          width: 49%;
          display: flex;
          flex-direction: column;
          margin-bottom: 5px;
          cursor: pointer;
          border: thin solid transparent;
          // width: 100%;
          // gap: 5px;
          // display: flex;
          // margin-bottom: 5px;
          // cursor: pointer;
          // padding: 5px 5px 0;
          // border: thin solid transparent;

          & > img {
            align-self: flex-start;
            width: 100%;
            max-height: 200px;
            aspect-ratio: 1;
            object-fit: cover;
            background: var(--color-background-2);
            border-radius: 10px;
          }

          & > .product-detail {
            align-items: flex-start;
            gap: 5px;
            flex: 1;
            min-height: 115px;
            padding: 5px 5px 15px 5px;
            text-align: center;

            & > .product-name {
              font-size: 0.75em;
              color: #595959;
              font-weight: bold;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              overflow: hidden;
            }

            & > .h-stack {
              width: 100%;
              margin-top: auto;
              flex-wrap: wrap;
              & > .product-price {
                color: #ed1b24;
                font-size: 0.8em;
                font-weight: bold;
                display: flex;
                flex-direction: column;
                line-height: 1.125;

                & > em.off {
                  font-size: 0.9em;
                  font-style: normal;
                  color: var(--color-text-note);
                  text-decoration: line-through;
                }
              }

              & > .sale-off-badge {
                color: white;
                font-weight: bold;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: auto;
                font-size: 0.65em;
                width: 50px;
                height: 40px;
                background-image: url("~/assets/image/sale_off_badge_background.png");
                background-size: 100%;
              }
            }
            & > .add-to-cart-btn,
            > .edit-button {
              background: white;
              border: thin solid #ed1b24;
              color: #ed1b24;
              border-radius: 2em;
              font-size: 15px;
              padding: 5px 15px;
              font-weight: bold;
              margin: 5px auto 0;
              width: 80%;
              white-space: nowrap;
            }
          }
        }

        & .product-item-container:hover,
        .product-item-container:active,
        .product-item-container-grid:hover,
        .product-item-container-grid:active {
          background: linear-gradient(#fff7f8, #fff7f8) padding-box,
            linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;

          & > .product-detail {
            border-color: transparent;
          }
        }

        .empty-list {
          height: 100%;
          max-height: 500px;
          flex: 1;
          justify-content: center;
          align-items: center;
          gap: 10px;
          padding: 15px;
          text-align: center;
        }

        .empty-list > .empty-list-image {
          margin: 10px 0;
          justify-content: center;
          border-radius: 50%;
          width: 200px;
          height: 200px;
          object-fit: contain;
        }

        .empty-list > span {
          font-size: 1em;
          color: var(--color-text-note);
        }
      }
    }
  }

  & > .loading-more {
    text-align: center;
    width: 100%;
    font-size: 50px;
    color: #595959;
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0;
    align-items: flex-end;
  }
}

#last_of_list {
  width: 100%;
}
.zalo-share-button {
  display: none;
}

// .shop-share-dropdown-item{
//   color: var(--primary-color-1) !important
// }

.shop-share-dropdown-content > div {
  background: rgb(0, 0, 0, 80%) !important;
  border-radius: 15px !important;
}
.shop-share-dropdown-item {
  color: white !important;
  text-align: center !important;
  background: transparent !important;
  font-size: 1em;
  position: relative;

  & .custom-share-button {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  & .custom-share-button:hover {
    opacity: calc(var(--v-hover-opacity) * var(--v-theme-overlay-multiplier));
  }
}

.absolute-container {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

  & > .absolute-content {
    position: relative;
    width: 100%;
    height: 100%;
  }
}
