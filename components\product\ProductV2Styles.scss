@media screen and (max-width: 720px) {
  .product-item-container-grid > img {
    height: auto;
  }

  .comment-container .comment-image-item {
    width: calc(25% - 5px) !important;
    min-width: unset !important;
    min-height: unset !important;
  }
}

.loading-skeleton,
.product-v2-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  background-color: #f6f5fb;
  position: relative;
  // font-size: calc(var(--font-size) * 1.3);
  overflow: hidden auto;
  // margin-bottom: 50px;
  margin: 0 auto 50px;
  width: 100%;
  max-width: var(--max-width-content-view-1024) !important;

  & > .sticky-header {
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    background: white;
    z-index: 100;

    & .search-input-container {
      display: flex;
      padding: 5px;
      gap: 2px;
      background: linear-gradient(to right, #00a873, var(--linear-color-1));
      height: 45px;
      position: sticky;
      top: 0;
      z-index: 100;
      transition: all 0.1s cubic-bezier(0.075, 0.82, 0.165, 1);

      &.top-55 {
        top: 55px;
        transition: all 0.3s ease !important;
      }

      & > .search-input-group {
        display: flex;
        background: white;
        border-radius: 5px;
        padding: 0 5px;
        flex: 1;
        height: 35px;

        & > button {
          color: var(--primary-color-1);
          width: 35px;
          height: 35px;
          font-size: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        & > button.category-button {
          font-size: 22px;
        }

        & > input {
          background: white;
          height: 35px;
          outline: none;
          font-size: 13px;
          font-weight: bold;
          flex: 1;
        }
      }

      .cart-button {
        color: white;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        & > svg {
          width: 30px;
        }

        & > .count {
          position: absolute;
          top: 3px;
          right: -2px;
          padding: 0 5px;
          border-radius: 2em;
          background-color: #cc313a;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
        }
      }

      .share-button {
        color: white;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        & > svg {
          width: 25px;
        }
      }

      & > .back-button {
        width: 30px;
        height: 30px;
        color: white;
        font-size: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: center;
      }
    }

    & > .sections-tab-carousel {
      display: flex;
      background: white;
      gap: 5px;
      height: 0;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-in-out;

      & .section-tab-item-slide {
        width: fit-content !important;
        padding: 1px;
        color: #a8a7a7;
        font-size: 15px;
        font-weight: 400;
        cursor: pointer;
        flex: 1;
        justify-content: center;

        & .tab-title {
          display: flex;
          align-items: center;
          justify-content: center;
          flex: 1;
        }
      }

      & .section-tab-item-slide.active {
        color: #5c5c5c;
        border-bottom: 3px solid var(--primary-color-1);
      }

      & .swiper-button-prev,
      .swiper-button-next {
        display: none;
      }
    }
  }

  & > .sticky-header.sticky-55 > .sections-tab-carousel {
    height: fit-content;
    max-height: 100px;
    padding: 3px 0 0;
  }

  & > .product-v2-content-container {
    display: flex;
    flex: 1;
    // flex-direction: column;
    flex-wrap: wrap;
    width: 100%;
    margin: auto;

    // @media screen and (min-width: 701px) {
    //   padding: 10px;
    // }

    & > .product-v2-profile-picture {
      width: 100dvw;
      max-width: 100%;
      height: 100dvw;
      // min-height: 250px;
      max-height: 480px;
      aspect-ratio: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-right: auto;

      @media screen and (min-width: 721px) {
        // --product-profile-width: calc(40% - 5px);
        // --product-profile-max-width: calc(40% - 5px);
      }

      & > img {
        object-fit: contain;
        height: 100%;
        width: inherit;
        max-height: inherit;
        max-width: inherit;
        aspect-ratio: 1;
      }

      & .product-images-carousel {
        width: 100%;
        height: 100%;
        max-height: 480px;
        aspect-ratio: 1;

        & img {
          min-height: unset;
          max-height: unset;
        }

        & > .image-index-showing {
          position: absolute;
          bottom: 15px;
          right: 15px;
          font-size: 13px;
          font-weight: 600;
          color: #545454;
          background: white;
          padding: 1px 10px;
          border-radius: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10;
          line-height: normal;
        }
      }

      & .swiper-button-prev,
      .swiper-button-next {
        color: white;
        font-size: 15px;
        width: 30px;
        height: 50%;
        top: 50%;
        transform: translateY(-50%);
        padding: 0;
        bottom: 0;
        margin-top: 0;
        filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
        text-shadow: none;

        &::after {
          position: absolute;
          top: 75px;
          transform: translateY(-50%);
          width: 30px;
          height: 30px;
          padding: 5px;
          border-radius: 50%;
          border: 2px solid white;
        }
      }
      & .swiper-button-prev {
        left: 20px;
        right: unset;
      }
      & .swiper-button-next {
        right: 20px;
        left: unset;
      }

      .icon-hang-tuyen {
        position: absolute;
        bottom: 15px;
        left: 15px;
      }
    }

    & > .product-v2-flash-sale {
      display: flex;
      background: linear-gradient(
        to right,
        var(--primary-color-1),
        var(--linear-color-1)
      );
      color: white;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      padding: 4px 15px;
      font-size: 13px;

      & > .flash-sale-label {
        display: flex;
        gap: 5px;
        align-items: center;
        justify-content: center;
      }

      & > .flash-sale-time-remaining {
        font-weight: 400;

        & > span {
          font-weight: 300;
          & em {
            font-style: normal;
            font-weight: 600;
          }
        }
      }
    }

    & > .product-v2-primary-info {
      display: flex;
      flex-direction: column;
      // padding: 10px 15px;
      // background: white;
      width: var(--product-primary-width);
      --product-primary-width: 100dvw;
      --product-primary-max-width: 100%;

      // @media screen and (min-width: 721px) {
      //   --product-primary-width: 60%;
      //   --product-primary-max-width: 60%;
      // }

      & > .price-info {
        display: flex;
        color: var(--primary-color-2);
        font-size: 20px;
        font-weight: 900;
        padding: 0 15px;
        background: white;

        & em.off {
          font-weight: 400;
          font-size: 13px;
          color: #545454;
          font-style: normal;
          text-decoration: line-through;
        }

        & > .percent-tag {
          font-size: 12px;
          font-weight: 700;
          color: var(--primary-color-2);
          background: color-mix(
            in srgb,
            var(--primary-color-2) 10%,
            transparent
          );
          padding: 0 5px;
          display: flex;
          align-items: center;
          margin-left: 10px;
          height: fit-content;
          align-self: center;
          border-radius: 3px;
        }
      }

      & > .product-name {
        font-size: 17px;
        font-weight: 700;
        color: #333333;
        padding: 0 15px;
        background: white;
      }

      & > .rating-sold-amount {
        font-size: 15px;
        display: flex;
        align-items: flex-end;
        color: #7e7d83;
        padding: 0 15px;
        background: white;
        & > div:first-child {
          padding-left: 0;
          border-left: none;
        }
        & > div:last-child {
          border-right: none;
          padding-right: 0;
        }
        & > div {
          margin: 10px 0;
          height: 1em;
          display: flex;
          align-items: center;
        }

        & > .rating {
          display: flex;
          font-weight: 400;
          color: #595959;

          & > svg {
            color: #ffc107;
            font-size: 17px;
            align-self: flex-end;
            margin-right: 2px;
          }
        }

        & > .rate-amount {
          font-size: 13px;
          color: #a8a7a7;
          margin-left: 2px;
        }

        & > .sold-amount {
          color: #595959;
          font-size: 13px;
          margin-left: 10px;
        }

        & > .heart-button {
          margin-left: auto;
          width: 30px;
          height: 30px;
          font-size: 27px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #8c8c8c;
          align-self: baseline;
        }
        & > .heart-button.liked {
          color: var(--primary-color-2);
        }
      }

      & > .hang-tuyen-button {
        color: var(--primary-color-1);
        background-color: color-mix(
          in srgb,
          var(--secondary-color-5) 10%,
          transparent
        );
        padding: 5px 10px;
        display: flex;
        align-items: center;
        border-radius: 5px;
        gap: 5px;
        font-size: 15px;
        font-weight: 800;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        animation: none;

        & > span {
          font-weight: 800;
          overflow: hidden;
          text-overflow: ellipsis;

          & > span {
            font-weight: 500;
          }
        }

        & > .left-icon {
          color: var(--primary-color-2);
          font-size: 20px;
        }

        & > .right-icon {
          margin-left: auto;
          font-size: 17px;
          color: color-mix(in srgb, var(--primary-color-1) 50%, transparent);
        }
      }

      & > .product-v2-short-descriptions {
        display: flex;
        flex-direction: column;
        padding: 15px;
        gap: 5px;
        background: white;
        margin-top: 7px;

        & > .descriptions-section-label {
          font-size: 15px;
          font-weight: 700;
          color: #545454;
        }
        & > .short-description-container {
          position: relative;
          // margin-top: 10px;
          color: #202020;
          height: fit-content;
          font-size: 14px;
          line-height: 20px;
          font-weight: 400;
          overflow: hidden;

          & > span {
            display: -webkit-box;
            line-clamp: 3;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            white-space: break-spaces;
          }
        }

        & > .description-container.show-full {
          max-height: 100%;
          height: fit-content;

          & > .show-full-button {
            position: relative;
            height: fit-content;
          }
        }
      }
    }

    & > .product-v2-descriptions {
      display: flex;
      flex-direction: column;
      padding: 15px 0;
      gap: 5px;
      background: white;
      margin-top: 7px;
      width: 100%;

      & > .descriptions-section-label {
        font-size: 15px;
        font-weight: 700;
        padding: 0 15px;
        color: #545454;
      }
      & > .description-container {
        position: relative;
        // margin-top: 10px;
        color: #202020;
        height: fit-content;
        font-size: 14px;
        line-height: 20px;
        max-height: 100px;
        font-weight: 400;
        overflow: hidden;
        padding: 0 15px;
        transition: max-height 0.2s ease-in-out;

        & > span {
          height: fit-content;
          min-height: fit-content;
          padding-bottom: 10px;
          white-space: break-spaces;
        }
      }
      & > .show-full-button {
        font-size: 14px;
        color: #a8a7a7;
        padding: 10px 15px 0;
        border-top: thin solid #ececec;
        justify-content: center;
        display: flex;
      }
      & > .description-container.show-full {
        max-height: 100%;
        height: fit-content;

        & > .show-full-button {
          position: relative;
          height: fit-content;
        }
      }
    }

    & > .product-v2-rating {
      display: flex;
      flex-direction: column;
      padding: 15px 0;
      gap: 5px;
      background: white;
      margin-top: 7px;
      width: 100%;

      & > .stack-title {
        display: flex;
        justify-content: space-between;
        padding: 0 15px;

        & > .label {
          color: var(--primary-color-1);
          font-size: 17px;
          font-weight: 700;
        }

        & > .add-comment {
          font-size: 15px;
          color: #545454;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 5px;

          & > svg {
            color: var(--primary-color-1);
            font-size: 20px;
          }
        }
      }

      & > .rating-overview {
        display: flex;
        align-items: baseline;
        padding: 0 15px;

        & > .comment {
          line-height: 1;
          margin-left: 5px;
          color: #a8a7a7;
          font-size: 13px;
        }

        & .rating {
          display: flex;
          font-weight: 400;
          color: var(--primary-color-2);
          font-size: 15px;
          gap: 5px;
        }
      }

      & > .comment-container {
        display: flex;
        flex-direction: column;

        & > .comment-item-container:first-child {
          border-top: thin solid #ececec;
        }
        & > .comment-item-container {
          display: flex;
          flex-direction: column;
          padding: 10px 15px;
          border-bottom: thin solid #ececec;
          // gap: 5px;
          & > .rate {
            display: flex;
            gap: 10px;
            align-items: baseline;
            font-weight: 600;
            color: black;
            font-size: 20px;
          }

          & > .comment-detail {
            color: #545454;
            font-weight: 400;
            line-height: normal;
            white-space: break-spaces;
          }

          & > .comment-images {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 5px 0;
            

            & > .comment-image-item {
              width: calc(20% - 5px);
              aspect-ratio: 1;
              min-width: 100px;
              min-height: 100px;
              max-width: 200px;
              border-radius: 2px;
              border: thin solid #e7e9ec;
              color: #e7e9ec;
              font-size: 50px;
              display: flex;
              justify-content: center;
              align-items: center;
              line-height: 1;
              position: relative;
              & > img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: inherit;
              }
            }
          }

          & > .comment-time {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #8c8c8c;
            font-weight: 400;
            margin: 7px 0;

            & > button.open-shop-reply {
              display: flex;
              align-items: flex-start;
              justify-content: flex-end;
              gap: 2px;
              & > svg {
                font-size: 20px;
              }
            }
          }

          & > .shop-reply {
            background: #f5f5f5;
            padding: 5px 10px;
            font-size: 15px;
            display: flex;
            flex-direction: column;
            font-weight: 400;

            & > .label {
              font-size: 15px;
              color: #545454;
              font-weight: 400;
            }

            & > .content {
              color: #8c8c8c;
            }
          }

          & > .user-info {
            display: flex;
            gap: 10px;
            align-items: center;
            color: #7e7d83;

            & img {
              width: 40px;
              height: 40px;
              object-fit: cover;
              border-radius: 50%;
            }
            & > .name-rate {
              display: flex;
              flex-direction: column;

              & > .name {
                color: #545454;
                font-size: 15px;
                font-weight: 700;
              }
            }
          }
        }

        &.my-comment{
          background: color-mix(in srgb, var(--primary-color-1) 5%, transparent);
          padding: 10px;
          border-radius: 10px;
          margin: 10px;

          & > .comment-header{
            display: flex;
            align-items: center;
            justify-content: flex-start;

            & > label{
              color: var(--primary-color-2);
              font-weight: 700;
            }

            & > .update-comment{
              font-weight: 600;
              font-style: italic;
              color: var(--primary-color-1);
              margin-left: auto;
            }
          }
          

          & > .comment-item-container{
            border-bottom: none;

            & .update-comment{
              font-style: italic;
              color: var(--primary-color-1);
            }
          }
        }
      }

      & .rate-loadmore {
        margin: 10px 0 0;
        font-style: italic;
        color: #a8a7a7;
        text-align: center;
        font-size: 13px;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;

        & > svg {
          font-size: 20px;
        }
      }
      & .show-all-rating {
        margin: 10px 0 0;
        font-style: italic;
        color: #7e7d83;
        text-align: center;
        width: 100%;
      }
    }

    & > .product-v2-review {
      display: flex;
      flex-direction: column;
      padding: 15px 0;
      gap: 10px;
      background: white;
      margin-top: 7px;
      width: 100%;

      & > .stack-title {
        display: flex;
        padding: 0 15px;
        gap: 10px;

        & > .left-icon {
          color: var(--primary-color-2);
          font-size: 20px;
        }

        & > .label {
          color: var(--primary-color-1);
          font-size: 15px;
          font-weight: 800;
          text-transform: uppercase;
        }
      }
      & .stack-carousel {
        flex: 1;

        & > .swiper-wrapper {
          height: 100%;
          padding: 0px 0 10px;
          box-sizing: border-box;
        }

        & .item-stack-slide {
          display: flex;
          width: fit-content;
          height: 100%;
          cursor: pointer;
          padding: 5px;
          border: thin solid transparent;

          & .item-stack {
            width: 150px;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
            -webkit-user-drag: none;

            & > img {
              flex: 1;
              background: var(--color-background-2);
              width: 150px;
              height: 150px;
              max-height: 150px;
              min-height: 150px;
              aspect-ratio: 1;
              object-fit: cover;
            }

            & > .item-stack-content {
              display: flex;
              flex-direction: column;
              // height: 75px;
              // min-height: 75px;
              padding: 10px 0;
              text-align: left;
              font-size: 1em;
              gap: 1px;
              flex: 1;

              & > .name {
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: bold;
                color: var(--primary-color-1);
                font-size: 13px;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                line-height: 1.1;
                -webkit-box-orient: vertical;
              }

              & .price {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: bold;
                color: var(--primary-color-2);
                font-size: 15px;
                display: flex;
                flex-direction: column;
                line-height: 1.1;
                margin-top: auto;
                font-weight: 800;
                padding-top: 10px;

                & > .off {
                  color: #6c6c6c;
                  text-decoration: line-through;
                  font-style: normal;
                  font-size: 12px;
                  font-weight: 400;
                }
              }

              & .shop-name {
                color: var(--primary-color-2);
                font-weight: 800;
                font-size: 13px;
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                line-height: 1.1;
              }

              & .shop-address {
                color: #6a7070;
                font-size: 12px;
                font-weight: 700;
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                line-height: 1.1;
                margin-top: 5px;
              }
            }

            & > .shop-stack-content {
              display: flex;
              flex-direction: column;
              background: #ecfef4;
              border-radius: 0 0 10px 10px;
              padding: 15px 5px 5px;
              position: relative;
              color: var(--primary-color-1);
              border: thin solid #c1eee4;
              border-top: 0;

              & > .shop-logo {
                box-shadow: 0 0 0 2px white;
                position: absolute;
                bottom: 100%;
                left: 50%;
                z-index: 100;
                transform: translate(-50%, 10px);

                & img {
                  min-height: unset;
                  max-height: unset;
                }
              }

              & > .shop-name {
                line-height: 1.2;
                font-size: 12px;
                color: #6e716f;
                text-align: center;
                height: 31.2px;
                overflow: hidden;
                font-weight: 700;
                display: flex;
                justify-content: center;
                align-items: center;

                & > span {
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
              }
            }

            & > .shop-logo-stack {
              box-shadow: none;
              border-radius: 0;
              z-index: 100;
            }

            & > .distance {
              position: absolute;
              top: 10px;
              left: 10px;
              background: var(--primary-color-1);
              color: white;
              padding: 1px 1px 1px 10px;
              border-radius: 2em;
              z-index: 100;
              font-size: 10px;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1;

              & > em {
                background: white;
                border-radius: 2em;
                width: 20px;
                height: 20px;
                justify-content: center;
                align-items: center;
                display: flex;
                color: var(--primary-color-1);
                margin-left: 5px;
                font-size: 10px;
                font-style: normal;
              }
            }
          }

          & > .top-left-tag {
            z-index: 1;
            position: absolute;
            top: 10px;
            left: 10px;
            width: 50px;
            height: 50px !important;
            min-height: unset;
            object-fit: contain;
          }
          & > .top-left-tag.small {
            width: 50%;
          }
        }

        & .item-stack-slide.product-review-stack {
          width: 150px;
          height: 300px;
          padding: 0;
          position: relative;

          & .item-stack-header {
            background: linear-gradient(
              to bottom,
              rgb(0, 0, 0, 1) 10%,
              transparent 65%
            );
            color: white;
            position: absolute;
            top: 0;
            width: 100%;
            padding: 5px;
            height: 15%;
            font-size: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;

            & > span {
              display: flex;
              align-items: center;
              gap: 2px;

              & > svg {
                font-size: 15px;
              }
            }
          }

          & .item-stack-footer {
            background: linear-gradient(to top, rgb(0, 0, 0, 0.5), transparent);
            color: white;
            font-weight: 700;
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 5px;
            min-height: 15%;
            height: fit-content;
            font-size: 12px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-start;

            & > span {
              text-align: left;
              width: 100%;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              gap: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          & .primary-content {
            width: 100%;
            height: 100%;

            & > img {
              object-fit: cover;
              width: 100%;
              height: 100%;
              max-height: unset;
            }
          }
        }
        & > .swiper-button-prev,
        > .swiper-button-next {
          color: white;
          font-size: 15px;
          width: 30px;
          height: 50%;
          top: 5px;
          padding: 0;
          bottom: 0;
          margin-top: 0;
          filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
          text-shadow: none;

          &::after {
            position: absolute;
            top: 75px;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            padding: 5px;
            border-radius: 50%;
            border: 2px solid white;
          }
        }
        & > .swiper-button-prev {
          left: 20px;
          right: unset;
        }
        & > .swiper-button-next {
          right: 20px;
          left: unset;
        }
      }
      & .stack-carousel.product-review-carousel {
        padding: 0 10px;
        & > .swiper-button-prev,
        > .swiper-button-next {
          top: 50%;
          transform: translateY(-50%);
        }
      }
      & .show-all-rating {
        margin: 10px 0 0;
        font-style: italic;
        color: #7e7d83;
        text-align: center;
        width: 100%;
      }
    }

    & > .product-v2-shop {
      display: flex;
      flex-direction: column;
      padding: 15px 0 0;
      gap: 10px;
      background: white;
      // margin-top: 7px;
      width: 100%;
      margin: 7px auto auto;

      & > .shop-info-content {
        padding: 0 10px;
        display: flex;
        gap: 10px;

        & > .product-v2-shop-logo {
          border-radius: 10px;
        }

        & > .shop-info {
          display: flex;
          flex-direction: column;
          flex: 1;

          & > .name {
            color: var(--primary-color-1);
            font-size: 17px;
            font-weight: 700;
          }

          & > .follows {
            font-size: 13px;
            color: #8c8c8c;
            font-weight: 300;

            & > em {
              font-weight: 700;
              font-style: normal;
            }
          }
          & > .rating-actions {
            display: flex;
            justify-content: space-between;

            & > .rating {
              display: flex;
              font-weight: 400;
              font-size: 15px;
              color: #545454;
              align-items: center;

              & > svg {
                color: #ffc107;
                font-size: 14px;
                margin-right: 2px;
              }
            }

            & > .go-to-shop-button {
              margin-left: auto;
              padding: 3px 10px;
              color: var(--primary-color-1);
              background-color: color-mix(
                in srgb,
                var(--primary-color-1) 9%,
                transparent
              );
              display: flex;
              gap: 5px;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 15px;
              border-radius: 5px;
            }
          }
        }
      }

      & > .quick-chat-to-shop {
        display: flex;
        flex-wrap: wrap;
        background: #f4f4f4;
        padding: 10px;
        gap: 5px;

        & > .quick-chat-item {
          padding: 5px 10px;
          background: white;
          border-radius: 3px;
          color: #545454;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }

    & > .product-v2-related {
      display: flex;
      flex-direction: column;
      padding: 0;
      gap: 10px;
      background: #f4f4f4;
      margin-top: 7px;

      & > .label {
        color: #545454;
        font-weight: 700;
        font-size: 15px;
        text-align: center;
      }

      & > .list-related-products {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 7px;
        padding: 5px;

        @media screen and (max-width: 500px) {
          & > .product-item-container-grid {
            --width-item: calc((100vw - 7px - 5px * 2) / 2) !important;
          }
        }
        @media screen and (min-width: 501px) and (max-width: 1024px) {
          & > .product-item-container-grid {
            --width-item: calc((100dvw - 7px * 3 - 5px * 2) / 3) !important;
          }
        }
        @media screen and (min-width: 1025px) and (max-width: 1320px) {
          & > .product-item-container-grid {
            --width-item: calc((700px - 7px*3 - 5px * 2) / 3) !important;
          }
        }
        @media screen and (min-width: 1321px) {
          & > .product-item-container-grid {
            --width-item: calc((700px - 7px*3 - 5px*2) / 3) !important;
          }
        }

        & > .product-item-container-grid {
          width: var(--width-item);
          min-width: var(--width-item);
          max-width: var(--width-item);
          justify-content: space-evenly;
          display: block;
          border-radius: 7px;
          background: white;
          border: thin solid transparent;

          & > a.product-item {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
            -webkit-user-drag: none;
            border-radius: 7px;
            align-items: center;
            padding: 7px;

            & > img {
              flex: 1;
              background: var(--color-background-2);
              width: calc(var(--width-item) - 12px);
              height: calc(var(--width-item) - 12px);
              min-height: calc(var(--width-item) - 12px);
              aspect-ratio: 1;
              border-radius: 7px;
              object-fit: cover;
            }

            & > .product-item-content {
              display: flex;
              flex-direction: column;
              padding: 5px 0 0;
              text-align: left;
              font-size: 1em;
              gap: 1px;
              flex: 1;
              width: 100%;

              & > .name {
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: 800;
                color: var(--primary-color-1);
                font-size: 15px;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                line-height: 1.1;
                min-height: 15px * 1.1 * 2;
                -webkit-box-orient: vertical;
              }
              & > .sold-like-amount {
                font-size: 13px;
                color: #a8a7a7;
                font-weight: 400;
                line-height: 15px;
                min-height: 15px;
              }

              & > .h-stack {
                flex: 1;
              }

              & .price {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: bold;
                color: var(--primary-color-2);
                font-size: 17px;
                display: flex;
                flex-direction: column;
                line-height: 1.1;
                margin-top: auto;
                font-weight: 800;
                padding-top: 10px;

                & > .off {
                  color: #6c6c6c;
                  text-decoration: line-through;
                  font-style: normal;
                  font-size: 13px;
                  font-weight: 400;
                }
              }

              & .add-to-cart {
                color: var(--primary-color-2);
                margin-left: auto;
                margin-top: auto;
                width: 40px;
                min-width: 40px;
                height: 40px;
                padding: 5px;
                display: flex;
                font-size: 30px;
                align-items: flex-end;
                justify-content: flex-end;
              }
            }
          }
        }
        & > .product-item-container-grid:first {
          margin-left: 0;
        }
        // & > .product-item-container-grid:last-child {
          // margin-right: auto;
        // }
        & .product-item-container-grid:hover,
        .product-item-container-grid:active,
        .product-item-container-grid:focus,
        .product-item-container-grid:target {
          border: thin solid var(--primary-color-1);
          background: #ebfeff;
          box-shadow: 0 7px 10px -7px #868686;
        }
      }

      & > .none-list-related-products {
        font-style: italic;
        font-size: 15px;
        color: #545454;
        text-align: center;
      }
    }

    & > .product-v2-footer {
      position: sticky;
      bottom: 0;
      width: 100%;
      background: white;
      padding: 10px;
      display: flex;
      gap: 10px;
      z-index: 10;
      margin-top: 50px;

      & > .footer-button {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        background: #f4f4f4;
        color: #545454;
        font-weight: 500;
        font-size: 17px;
        padding: 5px;
        z-index: 100;
        border-radius: 7px;

        @media screen and (max-width: 500px) {
          font-size: 13px;
        }

        & > svg {
          font-size: 25px;
          min-width: 25px;
          color: var(--primary-color-2);
        }

        & > span {
          padding: 0 0 0 5px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          & > em {
            font-size: 10px;
            color: var(--primary-color-2);
            white-space: nowrap;
          }
        }
      }

      & > button.footer-button.add-to-cart {
        background: var(--primary-color-2);
        color: white;

        & > svg {
          color: white;
        }
      }
    }
  }

  & .icon-hang-tuyen {
    padding: 0;
    display: flex;
    border-radius: 3px;
    text-transform: uppercase;
    overflow: hidden;
    color: white;
    z-index: 10;
    font-weight: 700;
    font-size: 13px;
    line-height: normal;

    & > .icon-1 {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 2px 10px 0 10px;
      background: var(--primary-color-1);
      position: relative;

      &::after {
        content: "";
        width: 8px;
        height: 8px;
        transform: rotate(45deg) translate(-50%, -50%);
        transform-origin: top left;
        background: var(--primary-color-1);
        position: absolute;
        top: 50%;
        left: 100%;
      }
    }
    & > .icon-2 {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px 10px 0 10px;
      height: 24px;
      background: var(--primary-color-2);
    }
  }
}
