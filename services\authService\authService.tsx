import axios, { HttpStatusCode } from "axios";

import { BaseHTTPService } from "../baseHttpService";
import { appConst, zaloConfig } from "~/assets/AppConst";
import { UserService } from "../userService/userService";
import appRoute from "~/assets/appRoute";
const userService = new UserService();

export class AuthService extends BaseHTTPService {
  async checkAuth(refresh = false) {
    return new Promise<any>(async (resolve) => {
      let currentToken = await localStorage.getItem(
        appConst.storageKey.token
      );
      let userProfileRef = await localStorage.getItem(
        appConst.storageKey.userInfo
      );

      if (userProfileRef && refresh == false) {
        resolve(JSON.parse(userProfileRef as string));
      } else if (currentToken) {
        let info = await this.getUserInfo()
        resolve(info);
      } else {
        resolve(null)
      }
    })

  }

  async checkAuthorize(role_id: any) {
    let profile = await this.checkAuth();
    if (role_id == profile?.role_id) {
      return true;
    }
    return false
  }

  async getUserInfo() {
    return new Promise((resolve) => {
      return userService.profileInfo().then(res => {
        if (res.status == HttpStatusCode.Ok) {
          resolve(res.data);
        }
        resolve(null);
      })
    })
  }

  login(userName: string, password: string, protocol?: any) {
    let body: any;
    if (protocol == "phone") {
      body = {
        phone: userName,
        password: password,
      };
    } else {
      body = {
        email: userName,
        password: password,
      };
    }

    return this.https("POST", appConst.apiURL.login, body);
  }

  signup(props: any) {
    let body: any = {
      otp: props.otp,
      user_name: props.userName,
      password: props.password,
      password_confirmation: props.passwordConfirmation,
      name: props.name,
      province_id: props.provinceId,
      district_id: props.districtId,
      ward_id: props.wardId,
      address: props.address,
      gender: props.gender,
      date_of_birth: props.dateOfBirth,
      description: props.description,
      agent: props.agent,
    };
    if (props.phone) {
      body.phone = props.phone;
    } else {
      body.email = props.email;
    }
    return this.https("POST", appConst.apiURL.registerClient, body);
  }

  logout() {
    return this.https("POST", appConst.apiURL.logout);
  }

  loginWithZalo(userProfile: any) {
    let body = {
      provider_name: "ZALO",
      provider_id: userProfile.id,
      name: userProfile.name,
      picture: userProfile.picture,
    };
    return this.https("POST", appConst.apiURL.loginDriver, body);
  }

  loginWithGoogle(userProfile: any) {
    let body = {
      provider_name: appConst.provider_name.google,
      provider_id: userProfile.id,
      name: userProfile.name,
      email: userProfile.email,
      picture: userProfile.picture,
    };
    return this.https("POST", appConst.apiURL.loginDriver, body);
  }

  loginWithApple(userProfile: any) {
    let body = {
      provider_name: appConst.provider_name.apple,
      provider_id: userProfile.id,
      name: userProfile.name,
      email: userProfile.email,
      picture: userProfile.picture,
      identifyToken: userProfile.identifyToken,
    };
    return this.https("POST", appConst.apiURL.loginDriver, body);
  }

  getZaloAccesstoken(code: string) {
    // let hrefLinkShare = "https://oauth.zaloapp.com/v4/permission?app_id="
    //     + zaloConfig.appIDZaloLogin
    //     + "&redirect_uri="
    //     + zaloConfig.callbackUrlZalo
    //     + "&state=" + zaloConfig.stateZalo;
    let body = {
      code: code,
      app_id: zaloConfig.appIDZaloLogin,
      grant_type: "authorization_code",
    };
    let url = "https://oauth.zaloapp.com/v4/access_token";
    return axios({
      method: "POST",
      url: url,
      data: body ? body : null,
      headers: {
        secret_key: zaloConfig.secretKeyZaloLogin,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    })
      .then((response) => {
        if (response.status == 200) {
          return response.data;
        } else {
          console.error(response);
          return response;
        }
      })
      .catch((error) => {
        if (axios.isCancel(error)) {
        } else if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error(error.response.data);
          console.error(error.response.status);
          console.error(error.response.headers);
        } else if (error.request) {
          // The request was made but no response was received
          // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
          // http.ClientRequest in node.js
          console.error(error.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error("Error", error.message);
        }
        return error;
      });
  }

  getZaloUserInfo(access_token: string) {
    let url =
      "https://graph.zalo.me/v2.0/me?access_token=" +
      access_token +
      "&fields=id,name,picture";
    return axios({
      method: "GET",
      url: url,
    })
      .then((response) => {
        if (response.status == 200) {
          return response.data;
        } else {
          console.error(response);
          return response;
        }
      })
      .catch((error) => {
        if (axios.isCancel(error)) {
        } else if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error(error.response.data);
          console.error(error.response.status);
          console.error(error.response.headers);
        } else if (error.request) {
          // The request was made but no response was received
          // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
          // http.ClientRequest in node.js
          console.error(error.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error("Error", error.message);
        }
        return error;
      });
  }

  saveFCMToken(body: any) {
    return this.https("POST", appConst.apiURL.insertOrUpdateFCMToken, body);
  }

  deleteToken(token: string) {
    let body = {
      token: token,
    };
    return this.https("POST", appConst.apiURL.deleteToken, body);
  }

  deleteAccount() {
    return this.https("POST", appConst.apiURL.deleteAccount, {
      request: "delete_my_account",
    });
  }

  getDefaultRedirectUrl(role: any, shop_count = 0) {
    switch (role) {
      case appConst.role_enum.driver:
        return appRoute.DriverDashboardComponent;
      case appConst.role_enum.agent:
        return appRoute.AgentShopManageComponent;
      case appConst.role_enum.user:
      case appConst.role_enum.admin:
      case appConst.role_enum.owner:
        if (shop_count > 0) {
          return appRoute.MyShopComponent;
        }
        return appRoute.AroundComponent;
      default:
        return appRoute.AroundComponent;
    }
  }
}
