<template>
	<div class="public-container">
		<div class="v-stack profile-container" v-if="isRefreshing">
			<div class="primary-user-info">
				<div class="primary-info-content skeleton">
					<v-skeleton-loader class="user-avatar"></v-skeleton-loader>

					<div class="user-info-buttons">
						<v-skeleton-loader class="button-skeleton profile-info-button"></v-skeleton-loader>
						<v-skeleton-loader class="button-skeleton my-shop-button"></v-skeleton-loader>
					</div>
				</div>
			</div>
		</div>
		<div class='profile-container' v-else-if="!isRefreshing">

			<div class="primary-user-info" v-if="profileData && profileData.id">
				<div class="primary-info-content">
					<img loading="lazy" :src="profileData && profileData.profile_picture
						? ((appConst.provider_img_domain.some(e => profileData?.profile_picture?.includes(e))) ? profileData.profile_picture : domainImage + profileData.profile_picture)
						: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:click="() => {

							router.push({
								path: appRoute.ProfileInfoComponent,
								state: {
									profileId: profileId,
								}
							})
						}" />
					<nuxt-link :to="{ path: appRoute.ProfileInfoComponent, state: { profileId: profileId } }">
						<span class="name">
							{{ profileData ? profileData.name : "NULL" }}
						</span>
					</nuxt-link>
					<nuxt-link :to="{ path: appRoute.ProfileInfoComponent, state: { profileId: profileId } }">
						<span class="user-detail">
							{{
								(profileData && profileData.phone)
									? (profileData.phone)
									: $t('ProfileComponent.chua_co_sdt')
							}}
						</span>
					</nuxt-link>
					<div class="user-info-buttons">
						<button class="profile-info-button" v-on:click="() => {
							router.push({
								path: appRoute.ProfileInfoComponent,
								state: {
									profileId: profileId
								}
							})
						}">
							<Icon name="ph:user-circle-light" size="30px"></Icon>
							{{ $t("ProfileComponent.trang_ca_nhan") }}
							<!-- Trang cá nhân -->
						</button>
						<button class="my-shop-button" v-on:click="() => {
							router.push({
								path: appRoute.MyShopComponent
							})
						}">
							<Icon name="emojione:department-store"></Icon>
							<span>
								{{ $t("ProfileComponent.cua_hang_cua_toi") }}
								<!-- Cửa hàng của tôi -->
							</span>
							<Icon name="material-symbols:chevron-right-rounded" />
						</button>
					</div>
				</div>
			</div>

			<div class="primary-user-info" v-if="!(profileData && profileData.id)">
				<div class="primary-info-content">
					<img loading="lazy" :src="non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" />
					<div class="user-info-buttons">
						<button class="profile-info-button" v-on:click="() => {
							router.push({
								path: appRoute.LoginComponent,
							})
						}">
							<Icon name="material-symbols:login" size="30px"></Icon>
							{{ $t("ProfileComponent.dang_nhap") }}
							<!-- Đăng nhập -->
						</button>
						<button class="my-shop-button" v-on:click="() => {
							router.push({
								path: appRoute.SignupComponent
							})
						}">
							<Icon name="streamline:user-add-plus-solid" size="25px"></Icon>
							{{ $t("ProfileComponent.dang_ky") }}
							<!-- Đăng ký -->
						</button>
					</div>
				</div>
			</div>

			<div class='v-stack other-detail' v-if="profileData && profileData.id && false">
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.chung") }}
						</span>
					</div>
				</nuxt-link>
				<div class='other-detail-options grid'>
					<nuxt-link :to="appRoute.CreateDeliveryComponent" class="other-option">
						<div class="option-icon">
							<Icon name="hugeicons:delivery-truck-02"></Icon>
						</div>
						<span class="option-label">{{ $t("ProfileComponent.giao_hang") }}</span>
					</nuxt-link>
				</div>
			</div>

			<div class='v-stack other-detail my-orders' v-if="profileData && profileData.id">
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.tai_khoan") }}
							<!-- Tài khoản -->
						</span>
						<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<nuxt-link :to="appRoute.ProfileInfoComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.thong_tin_tai_khoan") }}
							<!-- Thông tin tài khoản -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.ChatManageComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.tin_nhan") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.SavedAddressComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.so_dia_chi") }}
							<!-- Sổ địa chỉ -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.MyOrdersComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.don_hang_da_dat") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<!-- <button class="other-option">
					<span>
						Liên kết đăng nhập
					</span>

					<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
				</button> -->

					<div class="other-option" v-if="!profileData.has_password" v-on:click="() => {
						nuxtApp.$emit(appConst.event_key.check_password);
					}">
						<span>
							{{ $t("ProfileComponent.tao_mat_khau") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</div>
					<nuxt-link :to="appRoute.ChangePasswordComponent" v-if="profileData.has_password"
						class="other-option">
						<span>
							{{ $t("ProfileComponent.doi_mat_khau") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.DeleteAccountComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.yeu_cau_xoa_tai_khoan") }}
							<!-- Yêu cầu khóa tài khoản -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
				</div>
			</div>

			<div class='v-stack other-detail' v-if="profileData?.id
				&& ((profileData?.role_id == appConst.role_enum.driver)
					|| (profileData?.role_id == appConst.role_enum.admin)
					|| (profileData?.role_id == appConst.role_enum.admin2)
				)">
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.cong_cu_cho_shipper") }}
						</span>
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<nuxt-link :to="appRoute.DriverDashboardComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.quan_ly") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.DeliveryHistoryComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.don_giao_hang") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
				</div>
			</div>

			<!-- <div class='v-stack other-detail my-orders' v-else>
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.ca_nhan") }}
						</span>
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<NuxtLink :to="appRoute.SavedAddressComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.so_dia_chi") }}
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</NuxtLink>
				</div>
			</div> -->

			<div class='v-stack other-detail my-orders' v-if="profileData?.id
				&& ((profileData?.role_id == appConst.role_enum.agent)
					|| (profileData?.role_id == appConst.role_enum.admin)
					|| (profileData?.role_id == appConst.role_enum.admin2)
				)">
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.dai_ly") }}
							<!-- Đại lý -->
						</span>
						<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<nuxt-link :to="appRoute.ManageShopsComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.quan_ly_cua_hang") }}
							<!-- Quản lý cửa hàng -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.AgentOrderManageComponent" class="other-option" v-if="false">
						<span>
							{{ $t("ProfileComponent.don_hang_ky_gui") }}
							<!-- Đơn hàng ký gửi -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
				</div>
			</div>

			<div class='v-stack other-detail my-orders' v-if="false">
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							Nâng cao
						</span>
						<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<button class="other-option">
						<span>
							SP đã thích
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</button>
					<button class="other-option" v-on:click="() => {
						router.push(appRoute.AboutComponent)
					}">
						<span>
							Đang theo dõi
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</button>
					<button class="other-option" v-on:click="() => {
						router.push(appRoute.AboutComponent)
					}">
						<span>
							Đã xem gần đây
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</button>
				</div>
			</div>

			<div class='v-stack other-detail my-orders'>
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.tro_giup") }}
							<!-- Trợ giúp -->
						</span>
						<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<button class="other-option" v-if="false">
						<span>
							{{ $t("ProfileComponent.lien_he_cskh") }}
							<!-- Liên hệ CSKH -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</button>
					<nuxt-link :to="appRoute.AboutComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.thong_tin_chung") }}
							<!-- Thông tin chung -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
					<nuxt-link :to="appRoute.SupportComponent" class="other-option">
						<span>
							{{ $t("ProfileComponent.ho_tro") }}
							<!-- Thông tin chung -->
						</span>

						<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
					</nuxt-link>
				</div>
			</div>

			<div class='v-stack other-detail my-orders'>
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.cau_hinh") }}
							<!-- Cấu hình -->
						</span>
						<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
					</div>
				</nuxt-link>
				<div class='v-stack other-detail-options'>
					<div class="other-option language-options">
						<span>
							{{ $t("ProfileComponent.ngon_ngu") }}
							<!-- Ngôn ngữ -->
						</span>
						<button v-on:click="() => {
							changeLanguage(lang)
						}" class="language-option" v-for="(lang, index) in langOptions"
							:class="{ 'active': locale == lang.code, 'first': index == 0 }">
							{{ ISO6391.getNativeName(lang.code) }}
						</button>
						<!-- <div class="language-toggle">
						<div class="toggle" :class="{ 'left': locale != 'vi' }"></div>
						<span class="label-toggle" v-for="lang in langOptions" v-on:click="() => {
							changeLanguage(lang)
						}" :class="{ 'active': lang == locale }">{{ lang }}</span>
					</div> -->

					</div>
					<div class="other-option">
						<span>
							{{ $t("ProfileComponent.ban_kinh_tim_kiem") }}
							<!-- Bán kính tìm kiếm -->
						</span>

						<button v-on:click="() => {
							changeRadius(rad);
						}" class="radius-option" v-for="(rad, index) in radiusOptions"
							:class="{ 'active': radius == rad, 'first': index == 0 }">
							{{ rad }} km
						</button>
					</div>
					<div class="other-option notification-options" v-if="!webInApp">
						<span>
							{{ $t("ProfileComponent.nhan_thong_bao") }}:
							<!-- Bán kính tìm kiếm -->
						</span>
						<span class="value" :class="{ 'on': notify_enable, 'off': !notify_enable }">
							{{ notify_enable ? $t("ProfileComponent.dang_bat") : $t("ProfileComponent.dang_tat") }}
						</span>
						<button class="change-value" v-if="false" v-on:click="() => {
							nuxtApp.$emit(appConst.event_key.change_permission)
						}">
							{{ $t("ProfileComponent.thay_doi") }}
						</button>
					</div>
				</div>
			</div>

			<div class='v-stack other-detail my-orders'>
				<nuxt-link>
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.mang_xa_hoi") }}
						</span>
					</div>
				</nuxt-link>

				<div class='h-stack other-detail-options social-detail-options'>
					<nuxt-link class="other-option social-option" target="_blank"
						:to="environment.appConfig.app_facebook" :title="$t('ProfileComponent.fan_page')">
						<Icon name="logos:facebook" class="social-icon"></Icon>
					</nuxt-link>
					<nuxt-link class="other-option social-option" target="_blank"
						:to="environment.appConfig.app_zalo_oa" :title="$t('ProfileComponent.nhan_tin_qua_zalo')">
						<img :src="logo_zalo" loading="lazy" class="social-icon">
					</nuxt-link>
					<nuxt-link class="other-option social-option" target="_blank" :to="environment.appConfig.app_tiktok"
						:title="$t('ProfileComponent.kenh_tiktok')">
						<Icon name="logos:tiktok-icon" class="social-icon"></Icon>
					</nuxt-link>
				</div>

				<nuxt-link v-if="!webInApp">
					<div class='other-detail-button'>
						<span>
							{{ $t("ProfileComponent.tai_ung_dung") }}
						</span>
					</div>

				</nuxt-link>
				<div class='h-stack other-detail-options download-app-detail-options' v-if="!webInApp">
					<nuxt-link class="other-option download-app-option" target="_blank"
						:to="environment.appConfig.app_ch_play_download"
						:title="$t('ProfileComponent.tai_qua_ch_play')">
						<Icon name="logos:google-play-icon"></Icon>
						<div class="v-stack">
							<em class="android">Get it on</em>
							<span>Google Play</span>
						</div>
					</nuxt-link>
					<nuxt-link class="other-option download-app-option" target="_blank"
						:to="environment.appConfig.app_app_store_download"
						:title="$t('ProfileComponent.tai_qua_app_store')">
						<Icon name="mdi:apple"></Icon>
						<div class="v-stack">
							<em>Download on the</em>
							<span>App Store</span>
						</div>
					</nuxt-link>
				</div>
			</div>
			<span class="version" v-on:click="() => {
				reloadNuxtApp()
			}">
				{{ $t("ProfileComponent.phien_ban") }}
				<!-- Phiên bản  -->
				{{ webInApp ? webInApp.appInstalledInfo : nuxtApp.$config.public.appVersion }}
			</span>
			<div class='h-stack profile-footer' v-if="profileData && profileData.id">
				<button class="profile-footer-button" :disabled="logingOut" v-on:click="(e) => {
					logout()
				}">
					<span>
						{{ $t("ProfileComponent.dang_xuat") }}
						<!-- Đăng xuất -->
					</span>
				</button>
				<button class="profile-footer-button" v-if="false">
					<span>
						{{ $t("ProfileComponent.doi_tai_khoan") }}
					</span>
				</button>
			</div>
		</div>
	</div>


	<!-- <template v-show="'menu-public'" is="menu-public">
		<div class='stack menu-public'>
			<nuxt-link :to="appRoute.CartComponent">
				<button class='go-cart-button h-stack'>
					<Icon name="material-symbols:shopping-cart-outline" class='button-go-cart-left-icon' />
					<span>
						Giỏ hàng
					</span>
					<button class='go-cart-button-right-icon' v-if="cartData && cartData.length">
						<span>
							{{ getCartTotalProduct() }}
						</span>
					</button>
				</button>
			</nuxt-link>
		</div>
	</template> -->
</template>

<script lang="ts" setup>

import ISO6391 from "iso-639-1";
import type { CartDto } from "~/assets/appDTO";
import { appConst, domainImage } from "~/assets/AppConst";
import { AuthService } from "~/services/authService/authService";
import { FCMService } from "~/services/fcmService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import { appRoute } from "~/assets/appRoute";
import non_avatar from '~/assets/image/non-avatar.jpg';
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import { HttpStatusCode } from "axios";
import { VueFinalModal } from "vue-final-modal";
import environment from "~/assets/environment/environment";
import { useLogout } from '~/composables/useLogout';
var menuPublic = ref();
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const { locale, locales, t, setLocale } = useI18n();
const switchLocalePath = useSwitchLocalePath()

useSeoMeta({
	title: t('AppRouteTitle.ProfileComponent')
});

const availableLocales = computed(() => {
	return locales.value.filter(i => i.code)
})

var radiusOptions = [5, 10, 15];
var radius = ref(0);

var langOptions = ref([] as any);

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var fcmService = new FCMService();

var isRefreshing = ref(true);
var profileId = ref(route.params ? route.params.profileId : null);
var profileData = ref(null as any);
var isAuth = ref(false);
var cartData = ref([] as CartDto[]);
var webInApp = ref(null as any);
var notify_enable = ref(false);
const { logout, logingOut } = useLogout();

onMounted(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	let radiusRef = localStorage.getItem(appConst.storageKey.radiusFilter);
	radius.value = radiusRef ? JSON.parse(radiusRef) : null;

	langOptions.value = availableLocales.value.map(el => {
		return el
	})

	nuxtApp.$listen(appConst.event_key.user_info_change, () => {
		loadData();
	})
	loadData();
	let cartDataRef = await localStorage.getItem(appConst.storageKey.cart);
	if (cartDataRef) {
		cartData.value = JSON.parse(cartDataRef as string)
	}
	else {
		cartData.value = [];
	}

	checkNotificationPermissionWeb();
})
async function loadData() {
	isRefreshing.value = true;
	let authCheck = await authService.checkAuth();
	if (authCheck) {
		isAuth.value = true;
		profileData.value = authCheck;
		localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(authCheck))
	}
	else {
		isAuth.value = false;
		profileData.value = null;
		localStorage.removeItem(appConst.storageKey.token);
		localStorage.removeItem(appConst.storageKey.userInfo);
		sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
	}
	isRefreshing.value = false;
}
var onRefresh = () => {
	loadData();
}

function getCartTotalProduct() {
	if (!cartData.value.length) return 0;
	if (cartData.value.length <= 10) return cartData.value.length;
	return '10+'
}

function changeRadius(radiusRef = 0) {
	radius.value = radiusRef;
	localStorage.setItem(appConst.storageKey.radiusFilter, JSON.stringify(radiusRef));
}
function changeLanguage(lang = langOptions.value[0]) {
	// locale.value = lang.code;
	setLocale(lang.code)
	if (profileData.value?.id) {
		userService.switchLanguage(lang.code);
	}
	localStorage.setItem(appConst.storageKey.language, lang.code);
	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.setLanguage,
		data: lang.code
	})
}

async function checkNotificationPermissionWeb() {
	if ("Notification" in window) {
		Notification.requestPermission().then((permission) => {
			if (permission === "granted") {
				notify_enable.value = true;
			} else {
				notify_enable.value = false;
			}
		});
	}
}
</script>

<style lang="scss" src="./ProfileStyles.scss"></style>