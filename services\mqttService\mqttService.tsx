// services/mqttService.ts

import mqtt from 'mqtt';
import environment from '~/assets/environment/environment';

export class MqttService {
  private client: mqtt.MqttClient | undefined;

  constructor() {
    if (process.client) {
      this.client = mqtt.connect(environment.mqtt.url, {
        username: environment.mqtt.username,
        password: environment.mqtt.password,
      });

      this.client.on('connect', () => {
        console.log('Connected to MQTT broker');
      });

      this.client.on('error', (err) => {
        console.error('MQTT error:', err);
      });
    }
    else {

    }
  }

  subscribe(topic: string, callback: (message: any) => void) {
    if (process.client) {
      this.client?.subscribe(topic, (err) => {
        if (err) {
          return;
        }

        this.client?.on('message', (receivedTopic, message) => {
          if (receivedTopic === topic) {

            callback(JSON.parse(message.toString()));
          }
        });
      });
    }
  }

  unsubscribe(topic: string) {
    if (process.client) {
      this.client?.unsubscribe(topic)
    }
  }

  publish(topic: string, message: string) {
    if (process.client) {
      this.client?.publish(topic, message, (err) => {
      });
    }

  }
}
