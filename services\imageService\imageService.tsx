
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class ImageService extends BaseHTTPService {
  insertImagesZalo(file: any, title = '') {
    let body = {
      file: file,
      title: title
    }
    return this.https('POST', appConst.apiURL.insertImageZalo, body);
  }

  insertImage(data:any){
      let body = {
        path: data.path,
        object_type: data.object_type,
        image_type: data.image_type ? data.image_type : null,
        title: data.title,
        description: data.desciption,
        index: data.index,
        orientation: data.orientation,
        isEdit: data.isEdit,
        parent_id: data.parent_id,
        style: data.style,
        is_profile_picture: data.is_profile_picture
      }

      return this.https('POST', appConst.apiURL.insertImage, body, null, true);
  }

  deleteImage(imgId:string){
    let body = {
      id: imgId
    }
    return this.https('POST', appConst.apiURL.deleteImage, body, null, true);
  }
}