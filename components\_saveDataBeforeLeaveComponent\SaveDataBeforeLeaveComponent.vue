<template>
    <VueFinalModal 
        class="my-modal-container" :overlay-behavior="'persist'"
        content-class="v-stack save-data-before-leave-container" 
        :click-to-close="true"
		v-model="showSaveDataBeforeLeave" 
        contentTransition="vfm-fade" 
        v-on:closed="() => {
			close()
		}">
		<button class="close-button" v-on:click="()=>{
			close()
		}">
			<Icon name="material-symbols:close-rounded"></Icon>
		</button>
        <h4>{{ t('SaveDataBeforeLeaveComponent.chua_luu_du_lieu') }}</h4>
		<span> {{ t('SaveDataBeforeLeaveComponent.du_lieu_thay_doi_nhung_chua_duoc_luu') }}</span>

		<div class="actions">
			<button class="leave" v-on:click="leave()">{{ t('SaveDataBeforeLeaveComponent.khong_luu') }}</button>
			<button class="save-and-leave" v-on:click="saveAndLeave()">{{ t('SaveDataBeforeLeaveComponent.luu') }}</button>
		</div>
    </VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';

const emit = defineEmits(['leave', 'save_and_leave', 'close']);
const video = ref<HTMLVideoElement | null>(null);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var showSaveDataBeforeLeave = ref(false);
var webInApp = ref(null as any);

onMounted(async () => {
	showSaveDataBeforeLeave.value = true;
});
function close(){
	showSaveDataBeforeLeave.value = false
	emit('close');
}
function leave(){
    showSaveDataBeforeLeave.value = false
    emit('leave')
}

function saveAndLeave(){
    showSaveDataBeforeLeave.value = false
    emit('save_and_leave')
}
</script>

<style lang="scss" src="./SaveDataBeforeLeaveStyles.scss"></style>