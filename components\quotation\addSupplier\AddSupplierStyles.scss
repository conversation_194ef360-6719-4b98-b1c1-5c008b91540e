.add-supplier-container {
  max-height: 95dvh;
  height: auto;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  background-color: white;
  overflow: hidden;

  & > .add-supplier-content-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 15px 0;
    padding: 0 10px;
    overflow: auto;
    
    & .form-field-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
      margin-bottom: 10px;

      & label {
        font-size: 13px;
        font-weight: 700;
        color: #868686;
      }
    }

    & .custom-input,
    .text-area-custom {
      background: white;
      border-radius: 7px;
      margin: 5px 0;
      padding: 10px;
      font-size: 15px;
      font-weight: 600;
      border: 1px solid rgb(231, 233, 236);
      outline: none;
      flex: 1;

      &:disabled {
        background: #f5f6fa;
      }

    }

    & .text-area-custom {
      height: 200px;
      resize: none;
    }
  }

  & > .action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;

    & > button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      box-shadow: none;
      white-space: nowrap;
    }
    & > .cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }
    & > .save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    & > .cancel-button:disabled {
      color: var(--color-text-note);
      border-color: var(--color-text-note);
      opacity: 1;
    }
    // & > .save-button:disabled {
    //   background: #ccc;
    //   color: var(--primary-color-1);
    //   border-color: #ccc;
    //   opacity: 1;
    // }
  }
}
