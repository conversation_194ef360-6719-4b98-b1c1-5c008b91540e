.disconnect-delivery-partner-container {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  padding: 0;
  // min-height: 40dvh;
  overflow: auto;
  border-radius: 10px;
  background-color: white;

  & > img{
    height: 40px;
    object-fit: contain;
  }

  & > span{
    color: var(--primary-color-1);
    font-size: 25px;
    font-weight: 800;
  }

  & > .disconnect-delivery-partner-footer{
    padding: 10px;
    display: flex;
    justify-content: space-evenly;

    & > button{
      border: thin solid;
    }

    & > .reject-button{
      color: var(--primary-color-2);
    }

    & > .accept-button{
      color: white;
      border: thin solid var(--primary-color-1);
    }
  }
}