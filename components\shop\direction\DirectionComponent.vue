<script setup lang="ts">
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import marker_location_icon from "~/assets/image/marker-location.png";

const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();
const { t } = useI18n()

const props = defineProps({
	latitude_destination: null,
	longitude_destination: null,
	logo: null,
})

const emit = defineEmits([
	'close'
])


let vehicle = ref('bike');
let distance = ref("");
let duration = ref("");
let noneRoute = ref(false);

var shopLogo = props.logo ? props.logo : null;
var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var destinationMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('Map.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var buttonMapTileBackgound = ref(map_sateline);

let latitude_start = ref();
let longitude_start = ref();

let latitude_destination = ref(props.latitude_destination ? props.latitude_destination : null);
let longitude_destination = ref(props.longitude_destination ? props.longitude_destination : null);

var webInApp = ref(null as any);

var control: any;

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
watch(() => [user_latitude.value, user_longitude?.value], () => {
  latitude_start.value = user_latitude?.value;
  longitude_start.value = user_longitude?.value;
  setControlRouteMachine();
});
onUnmounted(() => {
  nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onBeforeMount(async () => {
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})

onMounted(async () => {
	console.log("mount");
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	// latitude_destination.value = router.options.history.state.latitude_destination ? JSON.parse(router.options.history.state.selectedItemsCart as string) : JSON.parse(cartDataCurrent as string);
	// 
});
onBeforeMount(async () => {

})

async function initLeafletMap() {

	leafletMap.setView([
		latitude_destination.value,
		longitude_destination.value
	], 17);

	addDestinationMarker();
	getUserLocation();
}
async function getUserLocation() {
	// if ("geolocation" in navigator) {
	// 	navigator.geolocation.getCurrentPosition(
	// 		(position) => {
	// 			latitude_start.value = position.coords.latitude;
	// 			longitude_start.value = position.coords.longitude;
	// 			addStartMarker();
	// 			// fitToDirection();
	// 			setControlRouteMachine();
	// 		},
	// 		(error) => {
	// 			latitude_start.value = appConst.defaultCoordinate.latitude;
	// 			longitude_start.value = appConst.defaultCoordinate.longitude;
	// 			addStartMarker();
	// 			// fitToDirection();
	// 			setControlRouteMachine();
	// 		},
	//         {
	//             enableHighAccuracy: false, // Use less accurate but faster methods
	//             timeout: 5000, // Set a timeout (in milliseconds)

	//         }
	// 	);
	// }
	latitude_start.value = user_latitude.value;
	longitude_start.value = user_longitude.value;
	addStartMarker();
	// fitToDirection();
	setControlRouteMachine();
}

async function setControlRouteMachine() {
	if (control) {
		control.remove();
	}

	control = new nuxtApp.$L.Routing.Control({
		waypointMode: 'connect',
		router: nuxtApp.$L.Routing.osrmv1({
			serviceUrl: appConst.urlOSRMv1,
			requestParameters: {
				overview: 'full',
				annotations: true,
				steps: true,
				alternatives: 2,

			},
			profile: vehicle.value,
			useHints: false
		}),
		plan: new nuxtApp.$L.Routing.Plan([
			nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
			nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
		], {
			createMarker: () => (false),
		}),
		autoRoute: false,
		lineOptions: {
			missingRouteTolerance: 10,
			extendToWaypoints: false,
			addWaypoints: false,
			styles: [{
				color: 'var(--primary-color-1)',
				weight: 5,
			}]
		},
		altLineOptions: {
			missingRouteTolerance: 10,
			extendToWaypoints: false,
			addWaypoints: false,
			styles: [{
				color: '#545454',
				weight: 4,
				opacity: .8,
				dashArray: '10, 10'
			}]
		},
		useZoomParameter: true,
		showAlternatives: true
	})

	// let a = control.getPlan();
	control.on('routesfound', (e: any) => {

		let durationRef = e.routes[0].summary.totalTime * 2;

		distance.value = e.routes[0].summary.totalDistance >= 1000 ? `${(e.routes[0].summary.totalDistance / 1000).toFixed(1)} KM` : `${Math.ceil(e.routes[0].summary.totalDistance)} M`
		duration.value = durationRef > 3600 ? `${Math.ceil(durationRef / 3600)} giờ` : `${Math.ceil(durationRef / 60)} phút`
	});
	control.on('routingerror', () => {
		noneRoute.value = true;
		control.remove()
	})
	control.on('routesfound', () => {
		control?.addTo(leafletMap);
	})
	fitToDirection()

}

function fitToDirection() {

	leafletMap.fitBounds(new nuxtApp.$L.LatLngBounds(
		nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
		nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value),
	), {
		padding: [50, 50],
		// paddingBottomRight: [50, 60]
	})
}

function addStartMarker() {

	localeMarkerLeaflet = nuxtApp.$L.marker([latitude_start.value, longitude_start.value], {
		icon: nuxtApp.$L.divIcon({
			html:
				`<div class='user-location'>
				<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="56" stroke-dashoffset="56" d="M12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4Z"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.5s" values="56;0"/></path><path d="M12 4v0M20 12h0M12 20v0M4 12h0" opacity="0"><set attributeName="opacity" begin="0.9s" to="1"/><animate fill="freeze" attributeName="d" begin="0.9s" dur="0.2s" values="M12 4v0M20 12h0M12 20v0M4 12h0;M12 4v-2M20 12h2M12 20v2M4 12h-2"/><animateTransform attributeName="transform" dur="30s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path></g><circle cx="12" cy="12" r="0" fill="currentColor" fill-opacity="0"><set attributeName="fill-opacity" begin="0.6s" to="1"/><animate fill="freeze" attributeName="r" begin="0.6s" dur="0.2s" values="0;4"/></circle></svg>
			</div>`,
			iconSize: [30, 30],

		}),
	});
	localeMarkerLeaflet.addTo(leafletMap);
}
function addDestinationMarker() {
	destinationMarkerLeaflet = nuxtApp.$L.marker([latitude_destination.value, longitude_destination.value], {
		icon: nuxtApp.$L.divIcon({
			className: "img-icon",
			html: `
                <div class='shop-logo'>
                    ${shopLogo
					? `<div class='logo-origin-container'>
                            <img 
                                src='${shopLogo ? (domainImage + shopLogo.path) : shop_logo}'
                                style='
                                    transform: ${shopLogo?.style?.length ? shopLogo.style : 'scale(1) translate(0)'};
                                    ${shopLogo?.style?.length ? '' : 'height: 100%'}
                                '
                            />
                        </div>`
					: `<img
                            [hidden]=${!!shopLogo}
                            src='${shop_logo}'
                            />`
				} 
                    
                    
                </div>
                
                <div class='after'></div>
                `,
			iconUrl: (shopLogo ? domainImage + shopLogo.path : shop_logo),
			// iconAnchor: [7, 7],
			iconSize: [40, 50],
		}),
	});
	destinationMarkerLeaflet.addTo(leafletMap);
}

function goToDirectionOnGGMap() {
	let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${latitude_start.value},${longitude_start.value}&destination=${latitude_destination.value},${longitude_destination.value}&trabelmode=bicycling`;
	window.open(textToDirections, "_blank")
}
function close(value?: any) {
	emit('close', value);
}
</script>
<template>
	<div class="direction-container">
		<div class="title-header-direction">
			<span>{{ $t('DirectionComponent.dinh_vi_chi_duong') }}</span>
			<button v-on:click="() => { close(); }">
				<Icon name="material-symbols:close-small-outline-rounded"></Icon>
			</button>
			<div class="overlay-bottom"></div>
		</div>
		<div class="map-container">
			<client-only>
				<LMap id="leaflet_map" v-on:ready="(e: any) => {
					leafletMap = e;
					leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
					initLeafletMap();
				}" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }" :world-copy-jump="true"
					:use-global-leaflet="true">
					<LControlZoom position="bottomright"></LControlZoom>
					<span class="current-location-leaflet" :title="$t('Map.vi_tri_cua_ban')" v-on:click="() => {
						// gotoCurrentLocationLeaflet();
						fitToDirection();
					}">
						<Icon name="line-md:my-location-loop" class="my-location-icon" />
					</span>
					<div id="leaflet-map-tile" v-if="false" class="map-type-btn btn-leaflet" data-toggle="tooltip"
						data-placement="right" :title="$t('Map.nhan_de_chuyen_loai_map')" v-bind:style="{
							backgroundImage: `url(` + buttonMapTileBackgound + `)`,
						}" v-on:click="(event: any) => {
							if (event.isTrusted) {
								if (
									leafletMapTileUrl ==
									appConst.leafletMapTileUrl.roadmap
								) {
									leafletMapTileUrl =
										appConst.leafletMapTileUrl.hyprid;
									mapTypeTitle = $t('Map.ve_tinh');
									mapType = 'hyprid';
									buttonMapTileBackgound = map_sateline;
								} else if (
									leafletMapTileUrl ==
									appConst.leafletMapTileUrl.hyprid
								) {
									leafletMapTileUrl =
										appConst.leafletMapTileUrl.streetmap;
									mapTypeTitle = $t('Map.co_dien');
									mapType = 'hyprid';
									buttonMapTileBackgound = map_streetmap;
								} else if (
									leafletMapTileUrl ==
									appConst.leafletMapTileUrl.streetmap
								) {
									leafletMapTileUrl =
										appConst.leafletMapTileUrl.roadmap;
									mapTypeTitle = $t('Map.ve_tinh_phan_hoi');
									mapType = 'roadmap';
									buttonMapTileBackgound = map_sateline;
								}
							} else event.preventDefault();
						}">
						<span>{{ mapTypeTitle }}</span>
					</div>
					<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
						:max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
						name="GoogleMap">
					</LTileLayer>
				</LMap>
			</client-only>
		</div>

		<div class="direction-info-container">
			<div class="direction-info">
				<span class="label">{{ $t('DirectionComponent.vi_tri_hien_tai_cua_ban') }}</span>
				<div class="direction-detail" v-if="!noneRoute">
					<div class="detail-item distance">
						<Icon name="mage:direction-up-right-fill"></Icon>
						<div class="content">
							<span>{{ distance }}</span>
							<span>{{ $t('DirectionComponent.khoang_cach') }}</span>
						</div>
					</div>
					<div class="detail-item distance">
						<Icon name="ic:baseline-access-time-filled"></Icon>
						<div class="content">
							<span>{{ duration }}</span>
							<span>{{ $t('DirectionComponent.thoi_gian') }}</span>
						</div>
					</div>
				</div>
				<div class="direction-detail" v-else>
					{{ $t('DirectionComponent.khong_tim_thay_tuyen_duong_phu_hop') }}
				</div>
				<div class="direction-by-option">
					<!-- <button :title="$t('DirectionComponent.di_bo')" :class="{ 'active': vehicle == 'foot' }" v-on:click="() => {
                    if (vehicle != 'foot') {
                        vehicle = 'foot';
                        setControlRouteMachine();
                    }
                }">
                    <Icon name="fluent:person-walking-16-filled"></Icon>
                </button> -->
					<button :title="$t('DirectionComponent.xe_may')" :class="{ 'active': vehicle == 'bike' }"
						v-on:click="() => {
							if (vehicle != 'bike') {
								vehicle = 'bike';
								setControlRouteMachine();
							}
						}">
						<Icon name="humbleicons:bike"></Icon>
					</button>
					<!-- <button :title="$t('DirectionComponent.o_to')" :class="{ 'active': vehicle == 'car' }" v-on:click="() => {
                    if (vehicle != 'car') {
                        vehicle = 'car';
                        setControlRouteMachine();
                    }
                }">
                    <Icon name="ph:car-fill"></Icon>
                </button> -->
				</div>
				<em class="note">{{ $t('DirectionComponent.chi_mang_tinh_chat_tham_khao') }}</em>
			</div>

			<button class="go-to-direction" :disabled="noneRoute" v-on:click="() => {
				goToDirectionOnGGMap();
			}">
				<div>
					<span>{{ $t('DirectionComponent.xuat_phat') }}</span>
					<Icon name="ic:round-keyboard-double-arrow-right"></Icon>
				</div>
			</button>
		</div>
	</div>

</template>

<style lang="scss" src="./DirectionStyles.scss"></style>
