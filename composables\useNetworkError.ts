import { ref } from 'vue'

// Global state for network error
const showNetworkError = ref(false)

export const useNetworkError = () => {
  const show = () => {
    showNetworkError.value = true
  }

  const hide = () => {
    showNetworkError.value = false
  }

  const toggle = () => {
    showNetworkError.value = !showNetworkError.value
  }

  const reload = () => {
    if (process.client) {
      window.location.reload()
    }
  }

  return {
    showNetworkError: readonly(showNetworkError),
    show,
    hide,
    toggle,
    reload
  }
}
