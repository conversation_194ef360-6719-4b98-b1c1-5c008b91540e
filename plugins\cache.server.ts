export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hook('app:rendered', ctx => {
    const url = ctx.ssrContext?.event.node.req.url;
    
    if (url?.includes("firebase-messaging")) {
      ctx.ssrContext?.event.node.res.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
      ctx.ssrContext?.event.node.res.setHeader("Pragma", "no-cache");
      ctx.ssrContext?.event.node.res.setHeader("Expires", "0");
    } else {
      ctx.ssrContext?.event.node.res.setHeader("Cache-Control", `max-age=${60 * 60 * 24}`);
    }
  })
})