<template>

  <div class='notifications-container' id="list_noti_container" ref="notification_container"
    v-if="props.notificationData?.length" v-on:scroll="() => {
      listNotiScroll();
    }">
    <button class="mark-read-all" v-if="unread_count" v-on:click="(e: Event) => {
      markReadAll()
      e.stopPropagation();
    }">
      {{ $t('NotificationsComponent.danh_dau_doc_tat_ca') }}
    </button>
    <div class="noti-item-content-container" v-for="(itemNoti, index) in props.notificationData">
      <ItemNotificationComponent :noti_data="JSON.parse(JSON.stringify(itemNoti))" :index_noti="index" v-on:mark_read="() => {
        markRead(itemNoti)
      }"></ItemNotificationComponent>
    </div>
    <div id="last_of_list_noti"></div>
    <div class="scroll-top-button">
      <button class="go-up" v-show="scrollPositionListNoti > 0" v-on:click="() => {
        scrollToTop()
      }">
        <Icon name="ic:round-arrow-upward"></Icon>
      </button>
    </div>
    <button class="loadmore-button" v-if="notificationData.length < props.count && !props.loadingMore" v-on:click="async () => {
      emit('load_more_noti')
    }"> {{ $t('NotificationsComponent.tai_them_thong_bao') }} </button>
    <span class="view-all" v-if="notificationData.length >= props.count && !props.loadingMore">{{
      $t('NotificationsComponent.da_tai_het_thong_bao') }}
    </span>
    <span class="loading-more" v-if="props.loadingMore">{{ $t('NotificationsComponent.dang_tai') }}</span>
  </div>
  <div class="none-noti" v-else>
    {{ $t('NotificationsComponent.chua_co_thong_bao') }}
  </div>

</template>

<style lang="scss" src="./NotificationsStyles.scss"></style>

<script setup lang="ts">
import { HttpStatusCode } from 'axios';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { UserService } from '~/services/userService/userService';

const props = defineProps({
  notificationData: null,
  unread_count: null,
  count: null,
  loadingMore: null,
  goUp: null
})
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const emit = defineEmits(['noti_click', 'unread_change', 'load_more_noti'])

var userService = new UserService();
var authService = new AuthService();

var unread_count = ref(props.unread_count);

var user_info = ref<any>();
var timeoutLoadmore: any;
var scrollPositionListNoti = useState('scroll_position_list_noti', () => { return 0 });
var notification_container = ref<HTMLElement | undefined>();
onBeforeMount(() => { })

onMounted(async () => {
  user_info.value = await authService.checkAuth();
  notification_container.value?.scrollTo({
    top: scrollPositionListNoti.value
  })
})

watch(()=>[props.goUp], ()=>{
  scrollToTop()
})
onUpdated(() => {
  
})

function markRead(itemNoti: any) {
  if (itemNoti.read_at == null) {
    userService.detailNotification(itemNoti.id).then((res: any) => {
      emit('unread_change');
      emit('noti_click');
      // if (res.status == HttpStatusCode.Ok) {
      // itemNoti.read_at = res.body?.data?.read_at;
      // unread_count.value = res.body?.data?.unread
      //   emit('unread_change')
      // }
    })
  }
  else {
    emit('noti_click');
  }
}

function markReadAll() {
  if (props.notificationData.length > 0 && unread_count.value > 0)
    userService.detailNotification(`${user_info.value.id}?request_type=on`).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        emit('unread_change', {
          item: 'all',
          unread_count: res.body?.data?.unread,
          count: res.body?.data?.count
        })
      }
    })
}

function listNotiScroll() {

  let containerEl = document.getElementById('list_noti_container') as HTMLElement;

  scrollPositionListNoti.value = containerEl.scrollTop ?? 0;

  // let elLast = document.getElementById('last_of_list_noti')?.getBoundingClientRect().bottom;
  // if (elLast && (elLast < (containerEl.getBoundingClientRect().bottom + 100))) {
  //   clearTimeout(timeoutLoadmore);
  //   timeoutLoadmore = setTimeout(() => {
  //     emit('load_more_noti')
  //   }, 500);

  // }
}

function scrollToTop() {
  notification_container.value?.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}
</script>