.support-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: white;

& .support-content {
  padding: 0 10px 50px;
  flex: 1;

  & > div{
      margin-top: 15px;
  }
}
& .content-title {
  text-transform: uppercase;
  font-size: 1.4em;
  border-bottom: thin solid black;
  display: flex;
  align-items: center;

  & span {
    padding: 10px 0;
    border-bottom: 3px solid var(--primary-color-1);
  }
}

& .content {
  font-size: 1em;
  gap: 10px;
  padding: 20px 0 0;
  color: var(--color-text-black);
  white-space: break-spaces;
  & .v-expansion-panel:not(:first-child)::after{
    opacity: 0 !important;
    border: none !important;
  }
  & .v-expansion-panel__shadow{
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  }

  & .q-panel-title{
    background: #f5f6fa;
    box-shadow: none;
    font-weight: 600;
    animation: none;
    font-size: 17px;
    padding: 10px;
    border: none;
  }

  & .name {
      font-weight: 600;
      width: fit-content;
      color: var(--primary-color-1);
  }

  & em{
    font-size: 17px;
    font-weight: 600;
  }

  & a {
    color: rgb(0 126 203);
    text-decoration: underline;
  }

  & > .faq-list{
    justify-content: flex-start;
    gap: 10px;
    margin-bottom: 20px;
  }
}
}
