<template>
    <div class="public-container" id="dashboard_publish_container" v-on:scroll="(e) => {
        listProductScroll();
        showHideHeader(e)
    }">
        <div class="home-v2-container">
            <!-- <HeaderV2Component :header_level_2_id="'search_input_container'"
                :last_element_id="'last_of_list_product'">
            </HeaderV2Component> -->
            <div class="search-input-container" id="search_input_container">
                <!-- <button class="category-button" v-if="!searchFocus" v-on:click="() => {
                    showCategorySelect = true
                }">
                    <Icon name="iconamoon:menu-burger-horizontal-bold"></Icon>
                </button> -->
                <button v-if="searchFocus" class="back-close-search-button" v-on:click="() => {
                    searchFocus = false
                }">
                    <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                </button>
                <div class="search-input-group">
                    <!-- <button class="category-button" v-if="!searchFocus" v-on:click="() => {
                        showCategorySelect = true
                    }">
                        <Icon name="iconamoon:menu-burger-horizontal-bold"></Icon>
                    </button> -->
                    <button v-if="searchFocus" class="back-close-search-button" v-on:click="() => {
                        searchFocus = false
                    }">
                        <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                    </button>
                    <input type="search" name="search-text" :placeholder="t('HomeV2Component.tim_kiem_placeholder')"
                        :maxlength="appConst.max_text_short"
                        class="search-input-content" v-on:click="() => {
                            searchFocus = true;
                        }" :value="filterData?.search" v-on:input="(e: any) => {
                            filterData = {
                                ...filterData,
                                search: e.target.value
                            };
                            getSearchProductResult();
                        }" autoComplete="off" v-on:keypress.enter="() => {
                            pushToAround(filterData?.search)
                        }" id="search_input" />
                    <button class="clear-button" v-show="searchFocus || filterData?.search?.length" v-on:click="() => {
                        filterData = {
                            ...filterData,
                            search: ''
                        }
                        searchProductSuggest = [];
                        blurSearch()
                    }">
                        <Icon name="iconamoon:sign-times" size="25" />
                    </button>
                    <button class="search-button" v-on:click="() => { focusSearch() }">
                        <Icon name="solar:rounded-magnifer-outline"></Icon>
                    </button>
                </div>
                <!-- <nuxt-link :to="appRoute.CartComponent" class="cart-button">
                    <Icon name=solar:cart-3-bold></Icon>
                    <div class="count" v-if="cartData?.length > 0">{{ cartData.length < 10 ? cartData.length : "9+"
                            }}</div>
                </nuxt-link> -->
            </div>

            <div class="filter-result-container">
                <div class="search-result" id="search-result"
                    :class="{ 'show': searchFocus && (productRecent.length || shopRecent.length || searchRecent.length || searchTextSuggest.length) }"
                    v-on:scroll="(e: any) => {
                        listSuggestScroll(e);
                    }">
                    <div class="history-search" v-if="searchRecent?.length">
                        <span>{{ $t('HomeComponent.lich_su_tim_kiem') }}
                            <button v-on:click="() => {
                                showClearHistorySearchModal = true
                                // clearSearchHistory()
                            }">
                                <Icon name="iconamoon:close-circle-1-light"></Icon>
                            </button>
                        </span>
                        <div class="list-history-search">
                            <button v-for="(itemSearch, indexTab) in searchRecent" v-on:click="() => {
                                pushToAround(itemSearch)
                            }">
                                {{ itemSearch }}
                            </button>
                        </div>
                    </div>
                    <div class="suggest-search" v-if="searchTextSuggest && searchTextSuggest.length">
                        <span>{{ $t('HomeComponent.tim_kiem_them') }}
                            <button v-if="false" v-on:click="() => {
                                getSearchProductResult();
                            }">
                                <Icon name="solar:refresh-linear"></Icon>
                            </button>
                        </span>

                        <div class="list-suggest-search">
                            <button v-for="(itemSearch, indexTab) in searchTextSuggest" v-on:click="() => {
                                pushToAround(itemSearch)
                            }">
                                {{ itemSearch }}
                            </button>
                        </div>
                    </div>
                    <div class="categories-and-list-suggest">
                        <div class="list-search-suggest-container">
                            <div class="list-search-suggest" v-if="shopRecent.length">
                                <span>{{ $t('AroundComponent.cua_hang_da_xem') }}</span>
                                <div class="search-placeholder" v-if="(!shopRecent || !shopRecent.length)">
                                    <img loading="lazy" :src="none_result" :placeholder="none_result" />
                                </div>
                                <nuxt-link
                                    :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)"
                                    v-if="shopRecent && shopRecent.length" v-for="itemRecent of shopRecent"
                                    class="recent-item-container" :title="showTranslateProductName(itemRecent)">
                                    <AvatarComponent class="shop-recent-logo" :imgTitle="itemRecent.name"
                                        :imgStyle="itemRecent.logo?.style" :imgSrc="itemRecent.logo
                                            ? (domainImage + itemRecent.logo.path)
                                            : itemRecent.banner
                                                ? (domainImage + itemRecent.banner.path)
                                                : ''
                                            " :width="100" :height="100" />

                                    <!-- <div class="recent-item-content">
                                            <span class="name">{{ showTranslateProductName(itemRecent) }}</span>
                                            <span class="sold-like-amount">
                                                {{ $t('ShopComponent.luot_ban', {
                                                    count: itemRecent.sold_count ??
                                                        0
                                                }) }}
                                                &nbsp;|&nbsp;
                                                {{ $t('ShopComponent.luot_thich', {
                                                    count: itemRecent.likes ??
                                                        0
                                                }) }}
                                            </span>
                                            <div class="price-actions">
                                                <div class="price-container">
                                                    <em class="origin-price" :class="{
                                                        'hide': !(itemRecent.price_off != null && parseFloat(itemRecent.price_off) < parseFloat(itemRecent.price))
                                                    }">
                                                        {{
                                                            (parseFloat(itemRecent.price) == 0 ||
                                                                parseFloat(itemRecent.price)
                                                                == null)
                                                                ? $t('AroundComponent.gia_lien_he')
                                                                : formatCurrency(parseFloat(itemRecent.price),
                                                                    itemRecent.shop ?
                                                                        itemRecent.shop.currency : selectedShop.currency)
                                                        }}
                                                    </em>
                                                    <span class="price">
                                                        {{
                                                            (itemRecent.price_off != null && itemRecent.price_off <
                                                                itemRecent.price) ?
                                                                formatCurrency(parseFloat(itemRecent.price_off),
                                                                    itemRecent.shop ? itemRecent.shop.currency :
                                                                        itemRecent.currency) : (parseFloat(itemRecent.price) == 0 ||
                                                                            itemRecent.price == null) ? $t('AroundComponent.gia_lien_he')
                                                                    : formatCurrency(parseFloat(itemRecent.price),
                                                                        itemRecent.shop ? itemRecent.shop.currency :
                                                                            selectedShop.currency) }} </span>

                                                </div>
                                                <button class="add-to-cart" v-on:click="async () => {
                                                    selectedProduct = JSON.parse(JSON.stringify(itemRecent));
                                                    selectedProduct.shop = JSON.parse(JSON.stringify(selectedShop));
                                                    showSelectedProduct = true;
                                                }" v-on:click.stop="(e) => { e.preventDefault() }">
                                                    <Icon name="solar:cart-plus-linear"></Icon>
                                                </button>
                                            </div>
                                        </div> -->
                                    <div class="recent-item-content">
                                        <div class="shop-selected-name">
                                            <nuxt-link :title="itemRecent.name"
                                                :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)">{{
                                                    itemRecent.name }}</nuxt-link>
                                        </div>
                                        <nuxt-link :title="itemRecent.address"
                                            :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)"
                                            class="shop-selected-address">{{ itemRecent.address }}</nuxt-link>

                                        <div class="shop-selected-business-rating">
                                            <span class="business-name" v-if="itemRecent.business_types?.id">
                                                {{ itemRecent.business_types?.name }}
                                            </span>
                                            <div class="rating" v-if="itemRecent.ratings">
                                                <Icon name="solar:star-bold"></Icon> {{
                                                    itemRecent.ratings ?? 0
                                                }}<span>/{{
                                                    appConst.defaultMaxRate }}</span>
                                            </div>
                                        </div>
                                    </div>


                                </nuxt-link>

                                <div class="loading-more" v-if="searchProductLoadingMore == true">
                                    {{ $t('AroundComponent.loading') }}
                                </div>
                                <div id="last_of_list_suggest"></div>
                            </div>

                            <div class="list-search-suggest" v-if="productRecent.length">
                                <span>{{ $t('AroundComponent.san_pham_da_xem') }}</span>
                                <div class="search-placeholder" v-if="(!productRecent || !productRecent.length)">
                                    <img loading="lazy" :src="none_result" :placeholder="none_result" />
                                </div>
                                <nuxt-link
                                    :to="appRoute.ProductComponent + '/' + (itemRecent.slug?.length ? itemRecent.slug : itemRecent.id)"
                                    v-if="productRecent && productRecent.length" v-for="itemRecent of productRecent"
                                    class="recent-item-container" :title="showTranslateProductName(itemRecent)">
                                    <img loading="lazy"
                                        :src="itemRecent?.profile_picture?.length ? (domainImage + itemRecent?.profile_picture) : icon_for_product"
                                        :placeholder="icon_for_product" :alt="showTranslateProductName(itemRecent)" />

                                    <div class="recent-item-content">
                                        <span class="name">{{ showTranslateProductName(itemRecent) }}</span>
                                        <span class="sold-like-amount">
                                            <span v-if="itemRecent.sold_count">
                                                {{ $t('AroundComponent.luot_ban', {
                                                    count: itemRecent.sold_count ??
                                                        0
                                                }) }}
                                            </span>
                                            <span v-if="itemRecent.sold_count && itemRecent.likes">
                                                &nbsp;|&nbsp;
                                            </span>
                                            <span v-if="itemRecent.likes">
                                                {{ $t('AroundComponent.luot_thich', {
                                                    count: itemRecent.likes ??
                                                        0
                                                }) }}
                                            </span>

                                        </span>
                                        <div class="price-actions">
                                            <div class="price-container">
                                                <em class="origin-price" :class="{
                                                    'hide': !(itemRecent.price_off != null && parseFloat(itemRecent.price_off) < parseFloat(itemRecent.price))
                                                }">
                                                    {{
                                                        (parseFloat(itemRecent.price) == 0 ||
                                                            parseFloat(itemRecent.price)
                                                            == null)
                                                            ? $t('AroundComponent.gia_lien_he')
                                                            : formatCurrency(parseFloat(itemRecent.price),
                                                                itemRecent.shop ?
                                                                    itemRecent.shop.currency : null)
                                                    }}
                                                </em>
                                                <span class="price">
                                                    {{
                                                        (itemRecent.price_off != null && itemRecent.price_off <
                                                            itemRecent.price) ?
                                                            formatCurrency(parseFloat(itemRecent.price_off), itemRecent.shop
                                                                ? itemRecent.shop.currency : itemRecent.currency) :
                                                            (parseFloat(itemRecent.price) == 0 || itemRecent.price == null) ?
                                                                $t('AroundComponent.gia_lien_he') :
                                                                formatCurrency(parseFloat(itemRecent.price), itemRecent.shop ?
                                                                    itemRecent.shop.currency : null) }} </span>

                                            </div>
                                        </div>
                                    </div>
                                </nuxt-link>

                                <div class="loading-more" v-if="searchProductLoadingMore == true">
                                    {{ $t('AroundComponent.loading') }}
                                </div>
                                <div id="last_of_list_suggest"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard"
                    :class="{ 'show-search': searchFocus && (productRecent.length || shopRecent.length || searchRecent.length || searchTextSuggest.length) }"
                    id="dashboard">

                    <AdvertismentSectionComponent v-if="false"></AdvertismentSectionComponent>
                    <MyInteractionShopSectionComponent v-if="profileInfo?.id"></MyInteractionShopSectionComponent>
                    <BusinessTypeSectionComponent :filter_data="filterData" :enable_load="enableLoadSections">
                    </BusinessTypeSectionComponent>
                    <SaleOffSectionComponent :filter_data="filterData" :enable_load="enableLoadSections" v-if="false">
                    </SaleOffSectionComponent>
                    <SuggestListSectionComponent :filter_data="filterData" :enable_load="enableLoadSections"
                        v-if="false"></SuggestListSectionComponent>
                    <HotDealSectionComponent :filter_data="filterData" :enable_load="enableLoadSections" v-if="false">
                    </HotDealSectionComponent>
                    <ShopNearbySectionComponent :filter_data="filterData" :enable_load="enableLoadSections">
                    </ShopNearbySectionComponent>
                    <ProductNearbySectionComponent :filter_data="filterData" :enable_load="enableLoadSections">
                    </ProductNearbySectionComponent>
                    <ProductNewSectionComponent :filter_data="filterData" :load_more="loadMoreProduct"
                        :enable_load="enableLoadSections"></ProductNewSectionComponent>

                    <div class="scroll-top-button">
                        <button class="go-up" v-show="enableScrollTopBtn" v-on:click="() => {
                            scrollToTop()
                        }">
                            <Icon name="ic:round-arrow-upward"></Icon>
                        </button>
                    </div>
                </div>

            </div>


        </div>
        <CustomSelectComponent v-if="showCategorySelect" :_key="'select_category_filter'" :list_item="dataBusinessType"
            :field_value="'id'" :field_title="'name'" :multiple="false"
            :title="$t('HomeComponent.loai_hinh_kinh_doanh')" :class="'my-custom-select'" :searchable="false"
            :model_value="category_id" v-on:close="() => {
                showCategorySelect = false
            }" v-on:model:update="(e: any) => {
                setCategoryFilter(e);
            }">
            <template v-slot:placeholder>
                <div class="h-stack">
                    <span>{{ $t('RegisterShopComponent.thiet_lap') }}</span>
                    <Icon name="mdi:chevron-down"></Icon>
                </div>
            </template>
            <template v-slot:title_icon_left>
                <Icon name="solar:hamburger-menu-linear"></Icon>
            </template>
        </CustomSelectComponent>

        <ClearSearchHistoryComponent v-if="showClearHistorySearchModal" v-on:close="() => {
            showClearHistorySearchModal = false
        }" v-on:submit="() => {
            clearSearchHistory()
        }"></ClearSearchHistoryComponent>
    </div>

</template>

<style lang="scss" src="./HomeV2Styles.scss"></style>
<script setup lang="ts">
import { getToken } from "firebase/messaging";
import { toast } from "vue3-toastify";
import VueCountdown from '@chenfengyuan/vue-countdown';
import {
    appConst,
    appDataStartup,
    baseLogoUrl,
    domainImage,
    formatCurrency,
    formatNumber,
    showTranslateProductDescription,
    showTranslateProductName
} from "~/assets/AppConst";

import { appRoute } from "~/assets/appRoute";

import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import logo from "~/assets/image_13_3_2024/logo.png"

import icon_for_product from "~/assets/image/icon-for-product.png";
import shop_banner from "~/assets/image/remagan-banner-19_1.png";
import logo_v2 from "~/assets/imageV2/remagan-logo-v2.png";
import flash_sale from "~/assets/imageV2/flash-sale.svg";
import icon_for_shop from "~/assets/image_08_05_2024/shop-logo.png";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import sp_dp from "~/assets/imageV2/SPDP.svg";
import hang_tuyen_label from "~/assets/imageV2/hang_tuyen_label.png";

import none_result from "~/assets/image/none-result-2.webp";

import { VueFinalModal } from "vue-final-modal";
import _, { filter } from "lodash";

import { PublicService } from "~/services/publicService/publicService";

import { filter_sort, type CartDto } from "~/assets/appDTO";

import { HttpStatusCode } from "axios";
import moment from "moment";
import VideoHLSPlayerComponent from "../_videoHLSPlayer/VideoHLSPlayerComponent.vue";
import { AuthService } from "~/services/authService/authService";

const listAdv = [
    "https://tuongviethoasen.vn/application/media/kien_thuc_nau_an/banner.jpg",
    "https://phuongnamvina.com/img_data/images/top-y-tuong-kinh-doanh-do-an-vat-online-hot-nhat.jpg",
    "https://inan2h.vn/wp-content/uploads/2022/12/in-banner-quang-cao-do-an-12.jpg"
];
var productService = new ProductService();
var publicService = new PublicService();
var placeService = new PlaceService();
var authService = new AuthService();
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const { t } = useI18n();

useHead({
    title: t('AppRouteTitle.HomeComponent'),
    meta: [{
        name: "title",
        content: t('AppRouteTitle.HomeComponent'),
    }],
});
let metaDescription = `Rẻ mà gần (remagan.com) là ứng dụng mua sắm tiện lợi,
giúp bạn dễ dàng tìm kiếm và mua hàng với giá rẻ và gần nhất. Chúng tôi không chỉ hỗ trợ người mua,
mà còn là cầu nối cho các doanh nghiệp nhỏ và vừa tiếp cận khách hàng thông qua một phương tiện đơn giản và hiệu quả. Với mục tiêu tối ưu hóa giá cả,
Rẻ mà gần (remagan.com) ưu tiên mang đến cho người mua những ưu đãi vô cùng hấp dẫn với chiết khấu siêu cạnh tranh,
vượt trội so với các ứng dụng khác. Khám phá sự thuận tiện và giá trị vô song cùng Rẻ mà gần (remagan.com) ngay hôm nay ! #Remagan re ma gan #MuaSắm #ƯuĐãi #TiệnLợi #re_ma_gan #re #ma #gan`;

useSeoMeta({
    title: t('AppRouteTitle.HomeComponent'),
    description: metaDescription,
    ogTitle: t('AppRouteTitle.HomeComponent'),
    ogDescription: metaDescription,
    ogImage: baseLogoUrl,
    ogImageHeight: 400,
    ogImageWidth: 720,
    ogUrl: "https://remagan.com",
    ogType: "website",
});
var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

var dataCategory = ref(appDataStartup.listCategory) as any;
var dataBusinessType = ref<any>(appDataStartup.listBusinessType);

// var address = ref("");
var profileInfo = ref<any>(null);
var refreshing = ref(true);
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref(false);
var searchSuggestAll = ref(false);
var searchFocus = ref(route.query?.searchFocus == 'true' ? true : false);
var searchHistory = ref([] as any[]);
var searchTextSuggest = ref([] as any[]);

var filterData = useState('dashboard_filter_data', () => {
    return {
        search: "",
        search_text: "",
        latitude_user: user_latitude.value,
        longitude_user: user_longitude.value,
        latitude_s: "",
        latitude_b: "",
        longitude_s: "",
        longitude_b: "",
        limit: 20,
        offset: 0,
        category_ids: [] as any,
        sortBy: filter_sort.gan_nhat,
        filter_type: 1,
        is_suggest: false,
        is_sale_off: false,
        business_type_id: null,
        child_num: 5,
    }
});

var enableLoadSections = ref(false);
var searchProductSuggest = ref();
var searchProductSuggestCount = ref();
var cartData = ref();

var searchProductTimeout: any;
var loadMore: any;

var loadMoreProduct = ref(0);

var onDraging = ref(false);
var productRecent = ref([] as any[]);
var shopRecent = ref([] as any[]);
var searchRecent = ref([] as any[]);
var webInApp = ref(null as any);

var currentSection = ref('' as any);

var showCategorySelect = ref(false);
var category_id = ref<any>(null);
var showClearHistorySearchModal = ref(false);
var windowInnerWidth = ref();

var dashboardScrollPosition = useState('dashboard_scroll_position', () => { return 0 });
var dashboardWatcherId: any;

var enableScrollTopBtn = ref(false);

var isFirstLoad = ref(true);
watch(()=>[
    user_latitude.value,
    user_longitude.value
], (newValue, oldValue) => {
    console.log(oldValue, newValue)
    filterData.value = {
        ...filterData.value,
        latitude_user: user_latitude?.value,
        longitude_user: user_longitude?.value
    }
    if(isFirstLoad.value){
        refreshHome()
    }
    setTimeout(() => {
        isFirstLoad.value = false;
    }, (500));
})

onUnmounted(async () => {
    let storageState = {
        filterData: filterData.value,
        searchProductSuggest: searchProductSuggest.value,
        searchProductSuggestCount: searchProductSuggestCount.value,
        searchTextSuggest: searchTextSuggest.value
    }

    sessionStorage.setItem(appConst.storageKey.stateRestore.HomeComponent, JSON.stringify(storageState));
});

onBeforeUnmount(() => {
    // if ("geolocation" in navigator) {
    //     navigator.geolocation.clearWatch(dashboardWatcherId);
    // }
    nuxtApp.$unsubscribe(appConst.event_key.app_data_loaded);
    nuxtApp.$unsubscribe(appConst.event_key.refresh_home);
    nuxtApp.$unsubscribe(appConst.event_key.user_moving);
})

onBeforeMount(() => {
    nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
        dataCategory.value = appDataStartup.listCategory;
        dataBusinessType.value = appDataStartup.listBusinessType;
    });
    nuxtApp.$listen(appConst.event_key.refresh_home, () => {
        refreshHome()
    });
    nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
        user_latitude.value = coor.latitude;
        user_longitude.value = coor.longitude;
    })
});

onMounted(async () => {

    currentSection.value = route.query?.section ? route.query?.section : null;

    console.log("mount");
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    profileInfo.value = await authService.checkAuth();
    nuxtApp.$emit(appConst.event_key.send_request_to_app, {
        action: appConst.webToAppAction.requestNotification
    });
    productRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : [];
    shopRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) : [];
    searchRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) : [];
    let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.HomeComponent) as string);

    if (store) {
        searchProductSuggest.value = store.searchProductSuggest;
        searchProductSuggestCount.value = store.searchProductSuggestCount;
        searchTextSuggest.value = store.searchTextSuggest
    }
    document.title = t('AppRouteTitle.HomeComponent');
    let cartData$ = JSON.parse(localStorage.getItem(appConst.storageKey.cart) as string);
    cartData.value = cartData$;
    // await getCurrentLocationOfUser()
    initDashboard();

    document.getElementById("dashboard_publish_container")?.scrollBy({ top: dashboardScrollPosition.value });

    document.getElementById(currentSection.value)?.scrollIntoView({
        block: 'center',
        behavior: 'smooth',
        inline: 'start',
    })
    window.addEventListener('resize', () => {
        windowInnerWidth.value = window.innerWidth;
        nuxtApp.$emit(appConst.event_key.scrolling, null)
    })
    windowInnerWidth.value = window.innerWidth;

});

async function refreshHome() {
    enableLoadSections.value = false;
    dashboardScrollPosition.value = 0;
    await clearNuxtState(['dashboard_shop_nearby', 'dashboard_video', 'dashboard_hot_deal', 'dashboard_product_nearby', 'dashboard_product_new']);
    enableLoadSections.value = true;

    // setTimeout(() => {
    //     document.getElementById("dashboard_publish_container")?.scrollTo({ top: 0, behavior: 'smooth' });
    // }, 400);

}

function initDashboard() {
    enableLoadSections.value = true;
    refreshing.value = false;
}

// function getCurrentLocationOfUser() {
//     return new Promise((resolve) => {
//         if ("geolocation" in navigator) {
//             dashboardWatcherId = navigator.geolocation.watchPosition(
//                 async (position) => {
//                     filterData.value = {
//                         ...filterData.value,
//                         latitude_user: position.coords.latitude,
//                         longitude_user: position.coords.longitude
//                     }

//                     // getUserAddress();
//                     resolve(true);
//                 },
//                 async (error) => {
//                     filterData.value = {
//                         ...filterData.value,
//                         latitude_user: appConst.defaultCoordinate.latitude,
//                         longitude_user: appConst.defaultCoordinate.longitude
//                     }

//                     // getUserAddress();
//                     resolve(false);
//                 }
//             ), {
//                 enableHighAccuracy: false,
//                 maximumAge: 100,
//                 timeout: 1000
//             };
//         }
//         else {
//             filterData.value = {
//                 ...filterData.value,
//                 latitude_user: appConst.defaultCoordinate.latitude,
//                 longitude_user: appConst.defaultCoordinate.longitude
//             }
//             resolve(false);
//         }
//     })

// }

function getSearchProductResult() {
    searchProductLoading.value = true;
    clearTimeout(searchProductTimeout);

    // if (filterData.value.search && filterData.value.search.length) {
    searchProductTimeout = setTimeout(() => {
        publicService.searchSuggest(filterData.value.search, undefined, undefined, filterData.value.latitude_user, filterData.value.longitude_user).then((res) => {
            if (filterData.value.search && filterData.value.search.length) {
                let indexHistory = searchHistory.value.indexOf(filterData.value.search);

                if (indexHistory == -1) {
                    searchHistory.value = [filterData.value.search,
                    ...searchHistory.value,
                    ].slice(0, 10);
                }
            }

            if (res.status == HttpStatusCode.Ok) {
                searchProductSuggest.value = res.body.data.products;
                searchProductSuggestCount.value = res.body.data.count;
                searchTextSuggest.value = res.body.data.strings;
                searchProductLoading.value = false;

                document.getElementById("search-result")?.scrollTo({
                    top: 0
                });
                searchSuggestAll.value = false;
            }

        }).catch(() => {
            searchProductLoading.value = false;
        });
    }

        , 1000);
    // } else {
    // 	searchProductLoading.value = false;
    // }
}

function loadMoreProductSearching() {
    if (searchProductSuggest.value.length < searchProductSuggestCount.value && !searchSuggestAll.value) {
        searchProductLoadingMore.value = true;
        clearTimeout(searchProductTimeout);

        searchProductTimeout = setTimeout(() => {

            publicService.searchSuggest(filterData.value.search,
                searchProductSuggest.value.length,
                25,
                filterData.value.latitude_user, filterData.value.longitude_user).then((res) => {
                    if (res.status == HttpStatusCode.Ok) {
                        if (res.body.data && res.body.data.result.length > 0) {
                            searchProductSuggest.value = [...searchProductSuggest.value,
                            ...res.body.data.products,
                            ];
                        }

                        else {
                            searchSuggestAll.value = true;
                        }

                        searchProductLoadingMore.value = false;
                    }

                }).catch(() => {
                    searchProductLoadingMore.value = false;
                });
        }

            , 1000);
    }
}


// function getUserAddress() {
//     placeService.myGeocoderByLatLngToAddress(filterData.value.latitude_user, filterData.value.longitude_user).then((res: any) => {
//         if (res.body.data && res.body.data.length) {
//             address.value = res.body.data[0].address ? res.body.data[0].address : "";
//         }
//     });
// }

function listSuggestScroll(event: any) {
    let el = document.getElementById("last_of_list_suggest")?.getBoundingClientRect().bottom;

    if (el && el <= window.innerHeight + 10) {
        if (searchProductSuggest.value?.length < searchProductSuggestCount.value) {
            loadMoreProductSearching();
        }
    }
}

function checkCategoryFilter(id: any) {
    if (filterData.value.category_ids && filterData.value.category_ids.indexOf(id) != -1) return true;
    return false;
}

function setCategoryFilter(id: any) {

    // let arr: any = filterData.value.category_ids || [];
    // let index = arr.indexOf(id);
    // if (index == -1) {
    //     arr.push(id);
    // }

    // else {
    //     arr.splice(index, 1);
    // }

    filterData.value = {
        ...filterData.value,
        // category_ids: [id],
        business_type_id: id
    };

    router.push({

        path: appRoute.AroundComponent,
        query: {
            filter: JSON.stringify(filterData.value)
        }
    })
}

function focusSearch() {
    document.getElementById('search_input')?.focus();
    searchFocus.value = true;
}

function blurSearch() {
    document.getElementById('search_input')?.blur();
    searchFocus.value = false;
}

function pushToAround(search_text: string) {
    nuxtApp.$emit('refresh_around');

    setTimeout(() => {
        filterData.value = {
            ...filterData.value,
            search: search_text,
            search_text: search_text
        }

            ;

        // getSearchProductResult();
        router.push({

            path: appRoute.AroundComponent,
            query: {
                filter: JSON.stringify(filterData.value)
            }
        })
    }
        , 300);
}

function scrollToTop() {
    document.getElementById('dashboard_publish_container')?.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
}


function listProductScroll() {
    let containerEl = document.getElementById('dashboard') as HTMLElement;
    if (containerEl?.getBoundingClientRect()?.top < 0) {
        enableScrollTopBtn.value = true;
    }
    else enableScrollTopBtn.value = false;

    let el = document.getElementById('last_of_list_product')?.getBoundingClientRect().bottom;
    if (el && (el < (window.innerHeight + 500))) {
        loadMoreProduct.value = loadMoreProduct.value + 1;
    }

    let ep = document
        .getElementById("dashboard_publish_container")
        ?.scrollTop;
    dashboardScrollPosition.value = ep as number;
}

function showHideHeader(event: any) {
    nuxtApp.$emit(appConst.event_key.scrolling, event);
}

function clearSearchHistory() {
    localStorage.removeItem(appConst.storageKey.searchRecent);
    searchRecent.value = [];
    showClearHistorySearchModal.value = false
}
</script>
