<template>
    <v-overlay v-model="showEditViewTemplate" :z-index="100" :absolute="false" :close-on-back="false"
        key="show_edit_view_template" class="edit-view-template-overlay-container"
        content-class='edit-view-template-modal-container' no-click-animation v-on:click:outside="() => {
            showEditViewTemplate = false;
        }">
        <SubHeaderV2Component>
            <template v-slot:header_left></template>
            <template v-slot:header_middle>
                <h3>{{ props.title ?? $t('UpdateViewTemplateComponent.chon_giao_dien') }}</h3>
            </template>
            <template v-slot:header_right>
                <v-btn class="edit-color-temp-btn" variant="text" v-on:click="() => {
                    showEditArea = !showEditArea
                }">
                    <Icon :name="showEditArea ? 'lucide:arrow-right-to-line' : 'iconamoon:edit-bold'"></Icon>
                </v-btn>
            </template>
        </SubHeaderV2Component>
        <div class="template-overview">
            <ShopTempDefaultOverviewComponent v-if="_select_temp == 'mac_dinh' || !_select_temp"
                :shop_data="JSON.parse(JSON.stringify(props.shop_data))">
            </ShopTempDefaultOverviewComponent>

            <ShopTemp1OverviewComponent v-if="_select_temp == 'nha_hang'"
                :shop_data="JSON.parse(JSON.stringify(props.shop_data))" :css_data="{
                    color_1: _color_1,
                    color_2: _color_2,
                    color_3: _color_3,
                    color_4: _color_4,
                    color_5: _color_5,
                    font: _select_font,
                    font_resize: _font_resize
                }" :banner="_select_banner && !useDefaultBanner
                    ? _select_banner.src
                    : props?.init_value?.banner && !useDefaultBanner
                        ? `${domainImage + props?.init_value?.banner}`
                        : null" :background="_select_background && !useDefaultBackground
                            ? _select_background.src
                            : props?.init_value?.background && !useDefaultBackground
                                ? `${domainImage + props?.init_value?.background}`
                                : null"></ShopTemp1OverviewComponent>

            <v-overlay v-model="showEditArea" :z-index="100" :absolute="false" :close-on-back="true" contained
                class="setting-area-overlay" key="show_setting_area_view_template" content-class="v-stack setting-area"
                no-click-animation v-on:click:outside="() => {
                    showEditArea = false;
                }">

                <div class="select-template">
                    <v-menu class="bootstrap-dropdown-container" :close-on-content-click="true" location="bottom left"
                        contained>
                        <template v-slot:activator="{ props }">
                            <v-btn variant="tonal" class="template-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                e.preventDefault();
                                e.stopPropagation();
                            }">

                                {{ $t(`ShopTemplateComponent.mau_hien_thi`) }}:
                                <em>
                                    {{ $t(`ShopTemplateComponent.${_select_temp}`) }}
                                </em>
                            </v-btn>
                        </template>
                        <v-list class="template-list">
                            <v-list-item key="add_neww" class="address-suggest-item" v-for="(itemTemp) in listTemplate"
                                v-on:click="() => {
                                    _select_temp = itemTemp.id;
                                }">
                                <span> {{ $t(`ShopTemplateComponent.${itemTemp.id}`) }}</span>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </div>

                <div class="select-font-template" v-if="_select_temp && _select_temp != 'mac_dinh'">
                    <v-menu class="bootstrap-dropdown-container" :close-on-content-click="true" location="bottom left"
                        contained>
                        <template v-slot:activator="{ props }">
                            <v-btn variant="tonal" class="font-select-btn" :style="{
                                'font-family': _select_font,
                                fontSize: _select_font ? '20px' : '15px'
                            }" v-bind="props" v-on:click="(e: Event) => {
                                e.preventDefault();
                                e.stopPropagation();
                            }">

                                {{ _select_font ? _select_font : $t(`UpdateViewTemplateComponent.phong_chu`) }}

                            </v-btn>
                        </template>
                        <v-list class="font-template-list">
                            <v-list-item key="font_items" class="font" v-for="(itemFont) in listFont" v-on:click="() => {
                                _select_font = itemFont;
                            }">
                                <span :style="{
                                    'font-family': itemFont
                                }">{{ itemFont }}</span>

                            </v-list-item>
                        </v-list>
                    </v-menu>

                    <div class="font-resize">
                        <v-btn class="decrease" :disabled="_font_resize <= -10" v-on:click="()=>{
                            if(_font_resize > -10){
                                _font_resize = _font_resize - 1
                            }
                        }">
                            <Icon name="line-md:minus"></Icon>
                        </v-btn>
                        <span>{{ _font_resize }}</span>
                        <v-btn class="increase" :disabled="_font_resize >= 10" v-on:click="()=>{
                            if(_font_resize < 10){
                                _font_resize = _font_resize + 1;
                            }
                        }">
                            <Icon name="line-md:plus"></Icon>
                        </v-btn>
                    </div>
                </div>

                <div class="select-banner-background-template" v-if="_select_temp && _select_temp != 'mac_dinh'">
                    <div class="select-banner">
                        <label class="label">{{ $t('ShopTemplateComponent.anh_bia') }}</label>
                        <div class="select-image-input">
                            <div class="actions">
                                <v-btn variant="flat" class="change-image" v-on:click="bannerFilesName.click()">
                                    <Icon name="stash:image-arrow-up"></Icon>

                                    <input type="file" accept='image/*' :multiple="false" v-on:change="($event: any) => {
                                        fileChangeInputBanner($event)
                                    }" ref="bannerFilesName" />
                                </v-btn>
                                <v-btn v-if="_select_banner" variant="flat" class="delete-image" v-on:click="() => {
                                    _select_banner = null;
                                }">
                                    <Icon name="la:trash-alt-solid"></Icon>
                                </v-btn>
                            </div>
                            <img :src="_select_banner
                                ? _select_banner.src
                                : props?.init_value?.banner
                                    ? `${domainImage + props?.init_value?.banner}`
                                    : props.shop_data?.banner?.path
                                        ? `${domainImage + props.shop_data?.banner.path}`
                                        : `${shop_banner_temp}`" alt="">

                        </div>
                        <label class="use-default">
                            <v-switch v-model="useDefaultBanner" :disabled="saving" flat color="var(--primary-color-1)"
                                hide-details class="my-switches">
                            </v-switch>
                            <em>{{ $t('UpdateViewTemplateComponent.dung_banner_chinh') }}</em>
                        </label>
                    </div>
                    <div class="select-banner">
                        <label class="label">{{ $t('ShopTemplateComponent.anh_nen') }}</label>
                        <div class="select-image-input">
                            <div class="actions">
                                <v-btn variant="flat" class="change-image" v-on:click="backgroundFilesName.click()">
                                    <Icon name="stash:image-arrow-up"></Icon>

                                    <input type="file" accept='image/*' :multiple="false" v-on:change="($event: any) => {
                                        fileChangeInputBackground($event)
                                    }" ref="backgroundFilesName" />
                                </v-btn>
                                <v-btn variant="flat" v-if="_select_background" class="delete-image" v-on:click="() => {
                                    _select_background = null;
                                }">
                                    <Icon name="la:trash-alt-solid"></Icon>
                                </v-btn>
                            </div>
                            <img :src="_select_background
                                ? _select_background.src
                                : props?.init_value?.background
                                    ? `${domainImage + props?.init_value?.background}`
                                    : `${shop_bg}`" alt="">

                        </div>

                        <label class="use-default">
                            <v-switch v-model="useDefaultBackground" :disabled="saving" flat
                                color="var(--primary-color-1)" hide-details class="my-switches">
                            </v-switch>
                            <em>{{ $t('UpdateViewTemplateComponent.dung_anh_mac_dinh') }}</em>
                        </label>
                    </div>
                </div>

                <div class="select-color-template" v-if="_select_temp && _select_temp != 'mac_dinh'">
                    <v-menu class="bootstrap-dropdown-container" :close-on-content-click="true" location="bottom left"
                        contained>
                        <template v-slot:activator="{ props }">
                            <v-btn variant="outlined" class="color-template-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                e.preventDefault();
                                e.stopPropagation();
                            }">

                                {{ $t(`UpdateViewTemplateComponent.bang_mau_goi_y`) }}

                            </v-btn>
                        </template>
                        <v-list class="color-template-list">
                            <v-list-item key="add_neww" class="color-suggest-item" v-for="(itemTemp) in listColorTemp"
                                v-on:click="() => {
                                    _color_1 = itemTemp._color_1;
                                    _color_2 = itemTemp._color_2;
                                    _color_3 = itemTemp._color_3;
                                    _color_4 = itemTemp._color_4;
                                    _color_5 = itemTemp._color_5;
                                }">
                                <div class="color-name">{{ $t(`UpdateViewTemplateComponent.${itemTemp.name}`) }}</div>
                                <div class="color-preview" :style="{
                                    background: itemTemp._color_1
                                }"></div>
                                <div class="color-preview" :style="{
                                    background: itemTemp._color_2
                                }"></div>
                                <div class="color-preview" :style="{
                                    background: itemTemp._color_3
                                }"></div>
                                <div class="color-preview" :style="{
                                    background: itemTemp._color_4
                                }"></div>
                                <div class="color-preview" :style="{
                                    background: itemTemp._color_5
                                }"></div>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </div>

                <div class="select-colors" v-if="_select_temp && _select_temp != 'mac_dinh'">
                    <div class="h-stack">
                        <label>{{ $t('UpdateViewTemplateComponent.nhan_cho_mau_1') }}:</label>
                        <v-menu class="bootstrap-dropdown-container" location="bottom left"
                            :close-on-content-click="false" contained v-on:click="(e: Event) => {
                                e.stopPropagation();
                            }">
                            <template v-slot:activator="{ props }">
                                <button class="color-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }">
                                    <div class="color-view" :style="{
                                        background: _color_1
                                    }"></div>
                                    <span>{{ _color_1 }}</span>

                                </button>
                            </template>
                            <v-color-picker v-model="_color_1" :modes="['hexa']"></v-color-picker>
                        </v-menu>
                    </div>
                    <div class="h-stack">
                        <label>{{ $t('UpdateViewTemplateComponent.nhan_cho_mau_2') }}:</label>
                        <v-menu class="bootstrap-dropdown-container" location="bottom left"
                            :close-on-content-click="false" contained v-on:click="(e: Event) => {
                                e.stopPropagation();
                            }">
                            <template v-slot:activator="{ props }">
                                <button class="color-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }">
                                    <div class="color-view" :style="{
                                        background: _color_2
                                    }"></div>
                                    <span>{{ _color_2 }}</span>


                                </button>
                            </template>
                            <v-color-picker v-model="_color_2" :modes="['hexa']"></v-color-picker>
                        </v-menu>
                    </div>
                    <div class="h-stack">
                        <label>{{ $t('UpdateViewTemplateComponent.nhan_cho_mau_3') }}:</label>
                        <v-menu class="bootstrap-dropdown-container" location="bottom left"
                            :close-on-content-click="false" contained v-on:click="(e: Event) => {
                                e.stopPropagation();
                            }">
                            <template v-slot:activator="{ props }">
                                <button class="color-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }">
                                    <div class="color-view" :style="{
                                        background: _color_3
                                    }"></div>
                                    <span>{{ _color_3 }}</span>

                                </button>
                            </template>
                            <v-color-picker v-model="_color_3" :modes="['hexa']"></v-color-picker>
                        </v-menu>
                    </div>
                    <div class="h-stack">
                        <label>{{ $t('UpdateViewTemplateComponent.nhan_cho_mau_4') }}:</label>
                        <v-menu class="bootstrap-dropdown-container" location="bottom left"
                            :close-on-content-click="false" contained v-on:click="(e: Event) => {
                                e.stopPropagation();
                            }">
                            <template v-slot:activator="{ props }">
                                <button class="color-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }">
                                    <div class="color-view" :style="{
                                        background: _color_4
                                    }"></div>
                                    <span>{{ _color_4 }}</span>

                                </button>
                            </template>
                            <v-color-picker v-model="_color_4" :modes="['hexa']"></v-color-picker>
                        </v-menu>
                    </div>
                    <div class="h-stack">
                        <label>{{ $t('UpdateViewTemplateComponent.nhan_cho_mau_5') }}:</label>
                        <v-menu class="bootstrap-dropdown-container" :close-on-content-click="false"
                            location="bottom left" contained v-on:click="(e: Event) => {
                                e.stopPropagation();
                            }">
                            <template v-slot:activator="{ props }">
                                <button class="color-select-btn" v-bind="props" v-on:click="(e: Event) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }">
                                    <div class="color-view" :style="{
                                        background: _color_5
                                    }"></div>
                                    <span>{{ _color_5 }}</span>

                                </button>
                            </template>
                            <v-color-picker v-model="_color_5" :modes="['hexa']"></v-color-picker>
                        </v-menu>
                    </div>
                </div>

                <div class="update-view-template-footer">
                    <v-btn class="footer-btn close" variant="outlined" color="var(--primary-color-2)" :disabled="saving"
                        v-on:click="() => {
                            emit('close')
                        }">
                        {{ $t('UpdateViewTemplateComponent.huy') }}
                    </v-btn>
                    <v-btn class="footer-btn submit" variant="tonal" color="var(--primary-color-1)"
                        v-on:click="handleSubmit" :loading="saving" :disabled="saving">
                        {{ $t('UpdateViewTemplateComponent.chon') }}
                    </v-btn>
                </div>
            </v-overlay>
        </div>
    </v-overlay>

</template>
<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import exifr from "exifr";

import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, formatOpeningHours, nonAccentVietnamese } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { DeliveryFeeConfig, type delivery_fee_condition, type delivery_fee_config, condition_params, operators, condition_relationship } from '../ShopConfigDTO';
import shop_banner_temp from "~/assets/imageV2/logo-landscape-1024.jpg";
import { HttpStatusCode } from "axios";
import { ImageService } from "~/services/imageService/imageService";
import shop_bg from "~/assets/imageV2/shop-bg.jpg";
import { fa } from "vuetify/locale";

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    init_value: null,
    mode: null,
    title: null,
    shop_data: null
})

var nuxtApp = useNuxtApp();
const { t, locale, locales } = useI18n();

var imageService = new ImageService();

var showEditViewTemplate = ref(false);

var _color_1 = ref(props.init_value?.colors?._color_1 ?? 'var(--primary-color-1)');
var _color_2 = ref(props.init_value?.colors?._color_2 ?? 'var(--primary-color-2)');
var _color_3 = ref(props.init_value?.colors?._color_3 ?? '#ffffff');
var _color_4 = ref(props.init_value?.colors?._color_4 ?? 'var(--secondary-color-3)');
var _color_5 = ref(props.init_value?.colors?._color_5 ?? '#d19019');
var _select_temp = ref(props.init_value?.template_id);
var _select_font = ref(props.init_value?.font ?? 'Nunito');
var _font_resize = ref(props.init_value?.font_resize ? parseFloat(props.init_value?.font_resize) : 0);

var bannerFilesName = ref(null as any);
var _select_banner = ref<any>();
var useDefaultBanner = ref(props.init_value?.use_shop_banner ?? false);

var backgroundFilesName = ref(null as any);
var _select_background = ref<any>();
var useDefaultBackground = ref(props.init_value?.use_default_backgrond ?? false);

var showEditArea = ref(false);

var saving = ref(false);
const listTemplate = [
    {
        id: 'mac_dinh',
        name: "Mặc định",
    },
    {
        id: 'nha_hang',
        name: "Nhà hàng"
    }
]

const listFont = [
    "Nunito",
    "Mulish",
    "Noto Sans",
    "Noto Serif",
    "Roboto",
    "Roboto Mono",
    "Montserrat",
    "Lobster",
    "Prata",
    "Pacifico",
    "Tinos",
    "Comfortaa",
    "Shantell Sans",
    "Pangolin",
    "Bellota"
]

const listColorTemp = [
    {
        name: "mac_dinh",
        _color_1: '#1a2f33',
        _color_2: '#A0522D',
        _color_3: '#F4A460',
        _color_4: '#FFFFFF',
        _color_5: '#F39C12',
    },
    {
        name: "he_thong",
        _color_1: '#00a873',
        _color_2: '#cb333b',
        _color_3: '#ffffff',
        _color_4: '#ebfeff',
        _color_5: '#d19019',
    },
    {
        name: "thien_nhien",
        _color_1: '#2A9D8F',
        _color_2: '#264653',
        _color_3: '#ECFFFF',
        _color_4: '#E6FBFF',
        _color_5: '#E76F51',

    },
    {
        name: "hien_dai",
        _color_1: '#0F172A',
        _color_2: '#2550AF',
        _color_3: '#ffffff',
        _color_4: '#D0FCFF',
        _color_5: '#439B1A',

    },
    {
        name: "nang_dong",
        _color_1: '#0B132B',
        _color_2: '#3D1E6D',
        _color_3: '#FFD06B',
        _color_4: '#F0FFBC',
        _color_5: '#0083CC',
    },
]
onBeforeUnmount(() => {
})
onBeforeMount(() => {

})
onMounted(async () => {
    console.log(props.init_value);
    if (props.init_value?.colors) {
        _color_1.value = props.init_value?.colors?._color_1 ?? listColorTemp[0]._color_1;
        _color_2.value = props.init_value?.colors?._color_2 ?? listColorTemp[0]._color_2;
        _color_3.value = props.init_value?.colors?._color_3 ?? listColorTemp[0]._color_3;
        _color_4.value = props.init_value?.colors?._color_4 ?? listColorTemp[0]._color_4;
        _color_5.value = props.init_value?.colors?._color_5 ?? listColorTemp[0]._color_5;
    }
    else {
        _color_1.value = listColorTemp[0]._color_1;
        _color_2.value = listColorTemp[0]._color_2;
        _color_3.value = listColorTemp[0]._color_3;
        _color_4.value = listColorTemp[0]._color_4;
        _color_5.value = listColorTemp[0]._color_5;
    }
    showEditViewTemplate.value = true
})
onUpdated(() => {
})

function close(value?: any) {
    emit('close', value);
}

async function fileChangeInputBanner(fileInput: any) {
    if (fileInput.target.files.length) {
        bannerFilesName.value = fileInput.target.files;

        if (fileInput.target.files[0].size > appConst.image_size.max) {
            let imgErr = t('UpdateViewTemplateComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
            toast.error(imgErr);
        }
        else {
            useDefaultBanner.value = false;
            const reader = new FileReader();
            reader.onload = async (e: any) => {
                const image = new Image();
                image.src = e.target.result;

                let orientationExif;
                let imgTemp = {
                    src: "",
                    orientation: 0,

                };
                if (fileInput.target.files[0].type != 'image/webp') {
                    orientationExif = await exifr.orientation(image) || 0;
                }
                else orientationExif = 0;
                imgTemp.src = image.src;
                imgTemp.orientation = orientationExif ? orientationExif : 0;

                _select_banner.value = imgTemp;
            }
            await reader.readAsDataURL(fileInput.target.files[0]);
        }
    }
}

async function fileChangeInputBackground(fileInput: any) {
    if (fileInput.target.files.length) {
        bannerFilesName.value = fileInput.target.files;

        if (fileInput.target.files[0].size > appConst.image_size.max) {
            let imgErr = t('UpdateViewTemplateComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
            toast.error(imgErr);
        }
        else {
            useDefaultBackground.value = false;
            const reader = new FileReader();
            reader.onload = async (e: any) => {
                const image = new Image();
                image.src = e.target.result;

                let orientationExif;
                let imgTemp = {
                    src: "",
                    orientation: 0,

                };
                if (fileInput.target.files[0].type != 'image/webp') {
                    orientationExif = await exifr.orientation(image) || 0;
                }
                else orientationExif = 0;
                imgTemp.src = image.src;
                imgTemp.orientation = orientationExif ? orientationExif : 0;

                _select_background.value = imgTemp;
            }
            await reader.readAsDataURL(fileInput.target.files[0]);
        }
    }
}
function saveBanner() {
    return new Promise(async (resolve) => {
        let banner$ = {
            path: _select_banner.value?.src,
            object_type: appConst.object_type.shop,
            image_type: 'temp_banner',
            title: props.shop_data?.name + " banner temp 1",
            description: props.shop_data?.name + " banner temp 1",
            index: 0,
            orientation: _select_banner.value?.orientation,
            isEdit: false,
            parent_id: props.shop_data?.id,
            style: null
        }
        await imageService.insertImage(banner$).then((res: any) => {
            if (res.status == HttpStatusCode.Ok) {
                console.log(res);
                resolve(res?.body?.data?.images?.path)
                // toast.success(t('UpdateViewTemplateComponent.cap_nhat_cua_hang_thanh_cong'));
            }
            else {
                toast.error(t('UpdateViewTemplateComponent.luu_anh_bia_that_bai'));
            }
            // isEditingShop.value = false;
        });
    })
}

function saveBackground() {
    return new Promise(async (resolve) => {
        let banner$ = {
            path: _select_background.value?.src,
            object_type: appConst.object_type.shop,
            image_type: 'temp_background',
            title: props.shop_data?.name + " background temp 1",
            description: props.shop_data?.name + " background temp 1",
            index: 0,
            orientation: _select_background.value?.orientation,
            isEdit: false,
            parent_id: props.shop_data?.id,
            style: null
        }
        await imageService.insertImage(banner$).then((res: any) => {
            if (res.status == HttpStatusCode.Ok) {
                resolve(res?.body?.data?.images?.path)
                // toast.success(t('UpdateViewTemplateComponent.cap_nhat_cua_hang_thanh_cong'));
            }
            else {
                resolve(null)
                toast.error(t('UpdateViewTemplateComponent.luu_anh_nen_that_bai'));
            }
            // isEditingShop.value = false;
        });
    })
}

const handleSubmit = async () => {
    saving.value = true;
    let _banner;
    let _background;
    if (_select_banner.value) {
        _banner = await saveBanner()
    }
    if (_select_background.value) {
        _background = await saveBackground()
    }
    emit('submit', {
        template_id: _select_temp,
        colors: {
            _color_1: _color_1,
            _color_2: _color_2,
            _color_3: _color_3,
            _color_4: _color_4,
            _color_5: _color_5,
        },
        font: _select_font,
        banner: _banner,
        use_shop_banner: useDefaultBanner.value,
        background: _background,
        use_default_backgrond: useDefaultBackground.value,
        font_resize: _font_resize.value ?? 0
    })

    saving.value = false;
}
</script>

<style lang="scss" src="./UpdateViewTemplateStyles.scss"></style>