<template>
	<div class="public-container">
		<div class='manage-delivery-partner-container'>
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					close();
				}">
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.DeleteAccountComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.ManageDeliveryPartnerComponent')">
				<template v-slot:header_middle>
					<div class="delivery-partner-manage-header">
                        <h3>{{ $t('AppRouteTitle.ManageDeliveryPartnerComponent') }}</h3>
                        <span>{{ shopData?.name }}</span>
                    </div>
				</template>
				<template v-slot:header_right>
					<button class="empty-button"></button>
				</template>
			</SubHeaderV2Component>
			<div class="manage-delivery-partner-content" v-if="shopData">
				<div class="delivery-partner-item" v-for="itemPartner in listPartners" :class="{
					'connected': itemPartner.is_enabled && itemPartner.is_connected,
					'disconnected': !itemPartner.is_enabled && itemPartner.is_connected,
					'not-connected': !itemPartner.is_connected,
					'is-unsupported': !itemPartner.is_active
				}">
					<div class="name-connection-info">
						<img class="parter-logo" :src="itemPartner.information?.logo"
							v-if="itemPartner.information?.logo" :alt="`logo ${itemPartner.name}`">
						<span class="partner-name" v-else>
							{{ itemPartner.name }}
						</span>
						<div class="connection-tag">
							<span class="unsupported" v-if="!itemPartner.is_active">{{
								$t('ManageDeliveryPartnerComponent.chua_ho_tro') }}</span>
							<span class="connected" v-else-if="itemPartner.is_enabled && itemPartner.is_connected">{{
								$t('ManageDeliveryPartnerComponent.da_ket_noi') }}</span>
							<span class="disconnected"
								v-else-if="!itemPartner.is_enabled && itemPartner.is_connected">{{
									$t('ManageDeliveryPartnerComponent.ngung_ket_noi') }}</span>
							<span class="not-connected" v-else-if="!itemPartner.is_connected">{{
								$t('ManageDeliveryPartnerComponent.chua_ket_noi') }}</span>
						</div>
						<span class="default-flag" v-if="itemPartner.is_default">
							{{ $t('ManageDeliveryPartnerComponent.mac_dinh') }}
						</span>
						<v-menu class="bootstrap-dropdown-container settings-button" location="bottom right"
							v-if="itemPartner.is_connected && itemPartner.is_enabled">
							<template v-slot:activator="{ props }">
								<button class="setting-button" dark v-bind="props"
									v-if="itemPartner.is_active && itemPartner.is_connected" v-on:click="() => {
										// selectedDeliveryPartner = itemPartner;
										// showShopDeliveryPartnerInfoModal = true;
									}">
									<Icon name="material-symbols-light:settings"></Icon>
								</button>
							</template>
							<v-list>
								<v-list-item :key="'set_default'"
									v-if="itemPartner.is_connected && itemPartner.is_enabled && !itemPartner.is_default" v-on:click="() => {
										setDefault(itemPartner)
									}">
									<v-list-item-title>{{ $t('ManageDeliveryPartnerComponent.dat_mac_dinh')
										}}</v-list-item-title>
								</v-list-item>
								<v-list-item :key="'disconnect'" class="disconnect-button"
									v-if="itemPartner.is_connected && itemPartner.is_enabled" v-on:click="() => {
										selectedDeliveryPartner = itemPartner;
										showDisconnectToPartnerModal = true;
									}">
									<v-list-item-title>{{ $t('ManageDeliveryPartnerComponent.ngung_ket_noi')
										}}</v-list-item-title>
								</v-list-item>
							</v-list>
						</v-menu>

					</div>
					<div class="secondary-info">
						<span>{{ itemPartner.information?.description ?? itemPartner?.note }}</span>
					</div>

					<div class="actions" v-if="itemPartner.is_active">
						<button class="show-info" v-if="itemPartner.is_connected" v-on:click="() => {
							selectedDeliveryPartner = itemPartner;
							showShopDeliveryPartnerInfoModal = true;
						}">{{
							$t('ManageDeliveryPartnerComponent.xem_chi_tiet')
						}}</button>
						<button class="connect-button" v-if="itemPartner.is_connected && !itemPartner.is_enabled"
							v-on:click="() => {
								selectedDeliveryPartner = itemPartner;
								reconnect = true;
								showShopDeliveryPartnerInfoModal = true;
							}">
							{{ $t('ManageDeliveryPartnerComponent.ket_noi_lai') }}
						</button>
						<button class="connect-button" v-if="!itemPartner.is_connected" v-on:click="() => {
							selectedDeliveryPartner = itemPartner;
							showShopDeliveryPartnerInfoModal = true;
						}">
							{{ $t('ManageDeliveryPartnerComponent.ket_noi') }}
						</button>
					</div>
				</div>
			</div>
			<NoneMyShopComponent v-else :show_header="false" :title="$t('AppRouteTitle.ManageDeliveryPartnerComponent')"></NoneMyShopComponent>
		</div>

		<ShopDeliveryPartnerInfoComponent v-if="showShopDeliveryPartnerInfoModal" 
			:shop_id="shopData.id"
			:shop_data="JSON.parse(JSON.stringify(shopData))"
			:mode="props.mode" :is_reconnect="reconnect"
			:delivery_partner_info="selectedDeliveryPartner ? JSON.parse(JSON.stringify(selectedDeliveryPartner)) : null"
			v-on:close="(updated) => {
				selectedDeliveryPartner = null;
				showShopDeliveryPartnerInfoModal = false;
				reconnect = false;
				if (updated) {
					listPartnerOfShop();
					toast.success(t('ManageDeliveryPartnerComponent.cap_nhat_thanh_cong'));
				}
			}"></ShopDeliveryPartnerInfoComponent>
		<DisconnectDeliveryPartnerComponent v-if="showDisconnectToPartnerModal" :shop_id="shopData.id"
			:mode="props.mode"
			:delivery_partner_info="selectedDeliveryPartner ? JSON.parse(JSON.stringify(selectedDeliveryPartner)) : null"
			v-on:close="(updated) => {
				selectedDeliveryPartner = null;
				showDisconnectToPartnerModal = false;
				reconnect = false;
				if (updated) {
					listPartnerOfShop();
					toast.success(t('ManageDeliveryPartnerComponent.cap_nhat_thanh_cong'));
				}
			}"></DisconnectDeliveryPartnerComponent>
	</div>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { AuthService } from "~/services/authService/authService";
import { toast } from "vue3-toastify";

import delete_account from "~/assets/image/delete_account.jpg";
import { HttpStatusCode } from "axios";
import { DeliveryPartnerService } from "~/services/deliveryPartnerService/deliveryPartnerService";
import { ShopService } from "~/services/shopService/shopService";
import { AgentService } from "~/services/agentService/agentService";

const { t } = useI18n();
const nuxtApp = useNuxtApp();
const props = defineProps({
	mode: null,
})
useSeoMeta({
	title: t('AppRouteTitle.ManageDeliveryPartnerComponent')
});
var router = useRouter();
var route = useRoute();

var authService = new AuthService();
var shopService = new ShopService();
var agentService = new AgentService();
var deliverPartnerService = new DeliveryPartnerService();

var shop_id = ref<any>(route.params?.id ?? null);
var shopData = ref<any>(null);
var listPartners = ref<any>([]);

var showShopDeliveryPartnerInfoModal = ref(false);
var showDisconnectToPartnerModal = ref(false);
var selectedDeliveryPartner = ref<any>(null);
var reconnect = ref(false);

onMounted(() => {
	getShopInfo().then(() => {
		listPartnerOfShop();
	});

})

function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
function getShopInfo() {
	return new Promise((resolve) => {
		if (props.mode != 'agent') {
			getMyShop().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
		else {
			getShopDetail().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
	})
}

function getMyShop() {
	return new Promise((resolve) => {
		shopService.myShop().then(async res => {
			if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
				shopData.value = res?.body?.data;
			}
			else {
				shopData.value = null;
			}
			resolve(shopData.value);
		}).catch(() => {

			resolve(null);
		})
	})

}
function getShopDetail() {
	return new Promise((resolve) => {
		agentService.agentShopDetail(shop_id.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				shopData.value = res.body.data;
				useSeoMeta({
					title: `${t('AppRouteTitle.ManageDeliveryPartnerComponent')} - ${shopData.value?.name}`
				})
				resolve(shopData.value);
			}
			else if (res.status == HttpStatusCode.Unauthorized) {
				router.push({
					path: appRoute.LoginComponent,
					query: {
						redirect: JSON.stringify(route.fullPath)
					}
				})
				resolve(null);
			}
			else {
				toast.error(t('ManageDeliveryPartnerComponent.khong_quyen_quan_ly'))
				// setTimeout(() => {
				// 	router.back();	
				// }, 2000);
				resolve(null);
			}
		}).catch(e => {
			toast.error(t('ManageDeliveryPartnerComponent.co_loi_xay_ra'))
			resolve(null);
			// setTimeout(() => {
			// 		router.back();	
			// 	}, 2000);
		})
	})
}

function listPartnerOfShop() {
	return new Promise((resolve) => {
		deliverPartnerService.shopListPartner(shopData.value.id).then((res) => {
			if (res.status == 200) {
				listPartners.value = res.body?.partners
			}
			resolve(listPartners.value)
		})
	})
}

function setDefault(itemPartner: any) {
	let data$ = {
		shop_id: shopData.value.id,
		partner: itemPartner?.name.toLowerCase(),
		is_enabled: itemPartner.is_connected,
		delivery_partner_id: itemPartner?.id,
		connect_data: itemPartner?.value,
		is_default: true
	}
	deliverPartnerService.updateDeliveryPartnerInfo(data$).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			listPartnerOfShop();
			toast.success(t('ManageDeliveryPartnerComponent.cap_nhat_thanh_cong'));
		}
		else {
			toast.error(res.body?.message ?? t('ManageDeliveryPartnerComponent.co_loi_xay_ra'));
		}
	}).catch((err) => {
		toast.error(t('ManageDeliveryPartnerComponent.co_loi_xay_ra'));
	})
}

</script>

<style lang="scss" src="./ManageDeliveryPartnerStyles.scss"></style>