<template>
	<div class="public-container">
		<div class="edit-product-container">
			<SubHeaderV2Component :title="$t('AppRouteTitle.EditProductComponent')">
			</SubHeaderV2Component>
			<div class="v-stack edit-product-detail" v-if="productData?.id">
				<div class="v-stack product-content-preview" v-if="!isEditing">
					<div class="product-profile-picture" v-if="!selectedChildProduct">
						<Swiper class="my-carousel stack-carousel product-images-carousel"
							v-if="productData?.images?.length" :modules="[SwiperAutoplay, SwiperNavigation]"
							:slides-per-view="1" :effect="'creative'" :navigation="true" :spaceBetween="10"
							:autoplay="false" key="suggest-carousel">
							<SwiperSlide class="item-stack-slide" v-for="(item, indexImg) of productData.images"
								:key="'suggest_' + item.id">
								<img loading="lazy" class="item-stack" v-on:click="() => {
									listObjectViewer = productData.images;
									indexActive = indexImg;
									showImageViewerModal = true;
								}" v-on:error="(event: any) => {
									event.target.src = icon_for_broken_image;
								}" :src="item?.path ? (domainImage + item?.path) : icon_for_product" :placeholder="icon_for_product"
									:alt="showTranslateProductName(productData)"
									:title="showTranslateProductName(productData)" />
							</SwiperSlide>
						</Swiper>
						<img v-else v-on:click="() => {
							let itemImg = {
								path: productData.profile_picture,
								title: showTranslateProductName(productData),
							}
							listObjectViewer = [itemImg];
							indexActive = 0;
							showImageViewerModal = true;
						}" v-on:error="(event: any) => {
							event.target.src = icon_for_broken_image;
						}" :src="productData?.profile_picture?.length ? (domainImage + productData.profile_picture) : icon_for_product"
							:placeholder="icon_for_product"
							:alt="productData ? showTranslateProductName(productData) : $t('EditProductComponent.khong_co_avatar')" />
					</div>
					<div v-else>
						<Swiper class="my-carousel stack-carousel product-images-carousel"
							v-if="selectedChildProduct.images?.length" :modules="[SwiperAutoplay, SwiperNavigation]"
							:slides-per-view="1" :effect="'creative'" :navigation="true" :spaceBetween="10"
							:autoplay="false" key="suggest-carousel">
							<SwiperSlide class="item-stack-slide" v-for="item of selectedChildProduct.images"
								:key="'suggest_' + item.id">
								<img loading="lazy" class="item-stack" v-on:error="(event: any) => {
									event.target.src = icon_for_broken_image;
								}" :src="item?.path ? (domainImage + item?.path) : icon_for_product" :placeholder="icon_for_product"
									:alt="showTranslateProductName(productData)"
									:title="showTranslateProductName(productData)" />
							</SwiperSlide>
						</Swiper>
						<img v-else v-on:error="(event: any) => {
							event.target.src = icon_for_broken_image;
						}" :src="selectedChildProduct?.profile_picture && selectedChildProduct?.profile_picture?.length ? domainImage + selectedChildProduct?.profile_picture : icon_for_product"
							:placeholder="icon_for_product"
							:alt="productData ? showTranslateProductName(productData) : $t('EditProductComponent.khong_co_avatar')" />
					</div>
					<div class="product-detail-stack primary-detail">
						<span class="product-name">
							{{ showTranslateProductName(productData) }}
						</span>
						<div class="rating-sold-amount">
							<div class="rating" v-if="productData.ratings">
								<Icon name="ic:round-star-rate"></Icon> {{ productData.ratings || 0 }}<span>/5</span>
							</div>

							<div class="rate-amount" v-if="productData.ratings">
								{{ productData.comment_count?.length || 0 }} {{ $t('ProductComponent.danh_gia') }}
							</div>

							<div class="sold-amount" v-if="productData.sold_counter > 10">
								{{ $t('EditProductComponent.da_ban', { count: productData.sold_counter || 0 }) }}
							</div>
						</div>

						<div class="product-price-container">
							<div class="price-content">
								<span class='product-price'>
									{{
										(productData.price_off != null && productData.price_off < productData.price) ?
											formatCurrency(parseFloat(productData.price_off), productData.shop?.currency) :
											(parseFloat(productData.price) == 0 || productData.price == null) ?
												$t('EditProductComponent.gia_lien_he') :
												formatCurrency(parseFloat(productData.price), productData.shop?.currency) }} <em
										class="off"
										v-if="(productData.price_off != null && productData.price_off < productData.price)">
										{{
											(parseFloat(productData.price) == 0 || productData.price == null)
												? $t('EditProductComponent.gia_lien_he')
												: formatCurrency(parseFloat(productData.price), productData.shop?.currency)
										}}</em>
								</span>
							</div>
							<div class="sale-off-badge"
								v-if="productData?.price_off != null && productData?.price_off < productData.price">
								{{ getPercentSaleOff(productData) }}%
							</div>
						</div>
						<div class="product-stock">
							<span class='label'>
								{{ $t('EditProductComponent.ton_kho') }}:
							</span>
							<span class="content">{{ productData.stock != null ? formatNumber(productData.stock) :
								$t('EditProductComponent.khong_quan_ly_ton_kho') }}</span>
						</div>
					</div>
					<div class="stack-space"
						v-if="productData && productData.categories && productData.categories.length">
					</div>
					<div class="product-detail-stack categories-container"
						v-if="productData && productData.categories && productData.categories.length">
						<span class="label">
							{{ $t('EditProductComponent.thong_tin_phan_loai') }}
						</span>
						<div class="stack-content">
							<div class="stack-content-item category">
								<span class="label">{{ $t('EditProductComponent.danh_muc') }}</span>
								<div class="content">
									<span v-for="(categoryItem, index) of productData.categories"
										:key="'category_' + categoryItem.id">
										{{ showTranslateProductName(categoryItem) + (index <
											productData.categories.length - 1 ? ", " : " ") }} </span>
								</div>
							</div>
							<div class="stack-content-item" v-if="productData.brand_id">
								<span class="label">{{ $t('EditProductComponent.thuong_hieu') }}</span>
								<div class="content">
									<span>
										{{ productData.brand?.name }} </span>
								</div>
							</div>
						</div>
					</div>
					<div class="stack-space" v-if="showTranslateProductDescription(productData)?.length"></div>
					<div class="product-detail-stack description"
						v-if="showTranslateProductDescription(productData)?.length">
						<span class="label">{{ $t('EditProductComponent.mo_ta') }}</span>
						<div class="description-container">
							<span id="product_note_content">
								{{ showTranslateProductDescription(productData) }}
							</span>
						</div>
					</div>

					<div class='v-stack childen-product-info-container'
						v-if="productData.children_products && productData.children_products.length > 0">
						<button v-if="!isEditing" v-for="(itemProduct, index) in productData.children_products"
							:key="'child_' + itemProduct.id + '_' + index" class='child-product-item-info'>
							<img class='avatar' :src="(itemProduct && itemProduct.profile_picture &&
								itemProduct.profile_picture.length) ? (domainImage + itemProduct.profile_picture) :
								icon_for_product" />

							<div class='v-stack child-product-detail'>
								<span class='name'>
									{{ showTranslateProductName(itemProduct) }}
								</span>
								<span class='price'>
									{{ formatCurrency((itemProduct.price_off != null && itemProduct.price_off <
										itemProduct.price) ? parseFloat(itemProduct.price_off) : itemProduct.price,
										productData.shop.currency) }} <em class="off"
										v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
										{{ formatCurrency(itemProduct.price || 0, productData.shop.currency) }}</em>
								</span>
								<div class="product-stock">
									<span class='label'>
										{{ $t('EditProductComponent.ton_kho') }}:
									</span>
									<span class="content">{{ itemProduct.stock != null ? formatNumber(itemProduct.stock)
										:
										$t('EditProductComponent.khong_quan_ly_ton_kho') }}</span>
								</div>
							</div>
						</button>
					</div>
				</div>

				<div class='v-stack edit-product-content-container' v-if="isEditing">

					<div class="product-content-edit avatar">

						<div class="content-header">
							<span class='label'>
								{{ $t('EditProductComponent.anh_san_pham') }} <em>({{ listImagesEdit.length }}/{{
									appConst.productImageMaxAmount }})</em>
							</span>
						</div>

						<VueDraggableNext :multi-drag="false" handle=".handle-drag" :animation="300" ghost-class="ghost"
							opacity="1" drag-class="dragging" class="product-image-list" :list="listImagesEdit">
							<div class="selected-image" v-for="(itemImg, index) in listImagesEdit">
								<img :src="itemImg.path" class="handle-drag" />
								<div class="action-overlay">
									<button class="delete-image" v-on:click="() => {
										listImagesEdit.splice(index, 1)
									}">
										<Icon name="bi:trash3-fill"></Icon>
									</button>
								</div>
							</div>
							<div class="select-image" v-if="listImagesEdit.length < appConst.productImageMaxAmount">
								<label>
									<Icon name="ion:plus-round"></Icon>
									<input type="file" accept='image/*' :multiple="true"
										:maxLengh="appConst.productImageMaxAmount - listImagesEdit.length" v-on:change="($event: any) => {
											fileChangeInput($event)
										}" ref="imageFilesName" />
								</label>
							</div>

						</VueDraggableNext>
					</div>


					<div class="language-options">
						<button class="lang-button" v-for="item in shopData.language"
							:class="{ 'active': selectLanguage == item }" v-on:click="() => {
								selectLanguage = item;
							}">
							{{ ISO6391.getNativeName(item) }}
						</button>
					</div>


					<!-- Edit Name -->
					<div class="v-stack product-content-edit" v-if="productData.type == appConst.product_type.private">
						<div class="content-header">
							<span class='label required'>
								{{ $t('EditProductComponent.ten_san_pham') }} {{ selectLanguage ?
									`(${ISO6391.getName(selectLanguage)})` : `` }}
							</span>
						</div>

						<input v-for="item in shopData.language" v-show="item == selectLanguage"
							:maxlength="appConst.max_text_short" :title="$t('EditProductComponent.ten_san_pham')"
							name='product-name'
							:disabled="isSaving || productData.type != appConst.product_type.private"
							class='input-custom'
							:placeholder="`${$t('EditProductComponent.ten_san_pham')} - ${ISO6391.getName(item)}`"
							:value="getNameTranslate(selectLanguage) || ''" v-on:input="async ($event: any) => {
								await nameProductChange($event.target.value, selectLanguage);
								validateName()
							}" />

						<input v-show="!shopData.language?.length || !selectLanguage"
							:title="$t('EditProductComponent.ten_san_pham')" name='product-name'
							:maxlength="appConst.max_text_short"
							:disabled="isSaving || productData.type != appConst.product_type.private"
							class='input-custom' :placeholder="$t('EditProductComponent.ten_san_pham')"
							:value="productData.name || ''" v-on:input="($event: any) => {
								productData = {
									...productData,
									name: $event.currentTarget.value
								}
								validateName();
							}" v-on:blur="() => {
								validateName();
							}" />
						<span class='error-message'>{{ nameErr }}</span>
					</div>

					<div class="v-stack product-content-edit" v-else>
						<div class="content-header">
							<span class='label'>
								{{ $t('EditProductComponent.ten_san_pham') }}
							</span>
						</div>
						<input :title="$t('EditProductComponent.ten_san_pham')" name='product-name' disabled
							class='input-custom' :placeholder="$t('EditProductComponent.ten_san_pham')"
							:maxlength="appConst.max_text_short" :value="productData.name || ''" v-on:input="($event: any) => {
								productData = {
									...productData,
									name: $event.currentTarget.value
								}
								validateName();
							}" v-on:blur="() => {
								validateName();
							}" />
					</div>

					<!-- Edit Notes -->
					<div class="v-stack product-content-edit">
						<div class="content-header">
							<span class='label'>
								{{ $t('EditProductComponent.mo_ta_san_pham') }} {{ selectLanguage ?
									`(${ISO6391.getName(selectLanguage)})` : `` }}
							</span>
						</div>

						<textarea v-for="item in shopData.language" v-show="item == selectLanguage"
							:title="$t('EditProductComponent.mo_ta_san_pham')" name='product-notes'
							:maxlength="appConst.max_text_long" class='text-area-custom' :disabled="isSaving"
							:placeholder="`${$t('EditProductComponent.mo_ta_san_pham')} (${selectLanguage ? ISO6391.getName(selectLanguage) : ''})`"
							v-on:input="($event: any) => {
								noteProductChange($event.target.value, selectLanguage)
							}" :value="getNoteTranslate(selectLanguage) || ''"></textarea>

						<textarea v-show="!shopData?.language?.length || !selectLanguage"
							:maxlength="appConst.max_text_long" :title="$t('EditProductComponent.mo_ta_san_pham')"
							name='product-notes' class='text-area-custom'
							:placeholder="$t('EditProductComponent.mo_ta_san_pham')"
							v-model="productData.notes"></textarea>
					</div>


					<!-- Edit Category -->
					<div class="v-stack product-content-edit">
						<div class="content-header">
							<span class='label'>
								{{ $t('EditProductComponent.danh_muc') }}
							</span>
							<button class="add-category" v-on:click="() => {
								showCreateCategoryModal = true;
							}">
								<Icon name="material-symbols:add-circle-outline-rounded"></Icon> {{
									$t('EditProductComponent.tao_danh_muc') }}
							</button>
						</div>

						<client-only>
							<v-autocomplete :custom-filter="(item: any, queryText: any, itemObj: any) => {
								let name = nonAccentVietnamese(itemObj.value.name).toLocaleLowerCase();
								let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
								return name.includes(query)
							}" class="custom-v-select mt-2" clearable chips label="" focused closable-chips clear-on-select
								:placeholder="$t('EditProductComponent.chon_danh_muc_san_pham')"
								v-model="selectedCategories" :disabled="isSaving" v-on:update:modelValue="() => {
									productData.category_ids = selectedCategories.map(e => e.id);
								}" :menu-icon="''" return-object :items="dataShopCategories" multiple variant="plain">
								<template v-slot:item="{ props, item }">
									<v-list-item v-bind="props" :title="item.value.name"></v-list-item>
								</template>
								<template v-slot:chip="{ props, item }">
									<v-chip v-bind="props" class="chip">{{ item.value.name }}</v-chip>
								</template>
								<template v-slot:no-data>
									<v-list-item
										:title="$t('EditProductComponent.khong_tim_thay_danh_muc')"></v-list-item>
								</template>
								<template v-slot:append-inner>
									<Icon name="mdi:chevron-down"></Icon>
								</template>
							</v-autocomplete>
						</client-only>
					</div>

					<!-- Edit Price -->
					<div class="v-stack product-content-edit">
						<span class='label'>
							{{ $t('EditProductComponent.gia') }}
						</span>
						<client-only>
							<input type="text" inputmode="numeric"
								:placeholder="$t('EditProductComponent.de_trong_neu_gia_can_lien_he')" v-on:input="($event: any) => {
									productData.price = parseDecimal2Digits(productData.price?.toString())
									validatePrice()
								}" v-model="productData.price" class="input-custom price-input" max=1000000000000>
						</client-only>

						<span class="price-text" v-if="productData.price && validDecimal2Digits(productData.price?.toString())">{{
							formatCurrency(parseDecimal2Digits(productData.price?.toString()),
								shopData.currency)
						}}</span>
						<span class='error-message' v-if="priceErr">{{ priceErr }}</span>
					</div>


					<!-- Edit Price Off-->
					<div class="v-stack product-content-edit">
						<span class='label'>
							{{ $t('EditProductComponent.gia_uu_dai') }}
						</span>
						<client-only>
							<input type="text" inputmode="numeric"
								:placeholder="$t('EditProductComponent.de_trong_neu_khong_giam_gia')"
								v-model="productData.price_off" v-on:input="($event: any) => {
									productData.price_off = parseDecimal2Digits(productData.price_off?.toString())
									validatePriceOff()
								}" class="input-custom price-input" max=1000000000000>
						</client-only>

						<span class="price-text" v-if="productData.price_off && validDecimal2Digits(productData.price_off?.toString())">{{
							formatCurrency(parseDecimal2Digits(productData.price_off?.toString()),
								shopData.currency) }}</span>
						<span class='error-message' v-if="priceOffErr">{{ priceOffErr }}</span>
					</div>

					<!-- Edit Stock-->
					<!-- <div class="v-stack product-content-edit">
						<span class='label'>
							{{ $t('EditProductComponent.ton_kho') }}
						</span>
						<input type="text" :disabled="isSaving" v-model="productData.stock" max="1000000000000"
							inputmode="numeric"
							:placeholder="$t('EditProductComponent.de_trong_khi_khong_quan_ly_ton_kho')"
							class="input-custom price-input" v-on:input="($event: any) => {
								validateStock()
							}">
						<span class="price-text notice" v-if="productData.stock && !stockErr">{{
							formatNumber(productData.stock)
						}}</span>
						<span class="price-text notice" v-else-if="!stockErr">{{
							$t('EditProductComponent.khong_quan_ly_ton_kho')
						}}</span>
						<span class='error-message' v-if="stockErr">{{ stockErr }}</span>
					</div>					 -->
					<!-- Edit Commission Percent-->
					<div class="v-stack product-content-edit"  v-if="shopData?.settings.general?.commission_percent?.enabled == true">
						<span class='label'>
							{{ $t('EditProductComponent.commission_percent') }}
						</span>						
						<client-only>									
							<input type="number" inputmode="numeric"
								:placeholder="getCommissionPercentPlaceholder()" 
								:min="0" :max="100" step="0.5" 
								v-on:input="($event: any) => {
									const inputValue = $event.target.value?.trim();
									if (inputValue === '' || inputValue === null || inputValue === undefined) {
										productData.commission_percent = null;
									} else {
										productData.commission_percent = parseDecimal2Digits(inputValue);
									}
									validateCommissionPercent()
								}" v-model="productData.commission_percent" class="input-custom price-input">
						</client-only>

						<span class="price-text" v-if="productData.commission_percent && validDecimal2Digits(productData.commission_percent?.toString())">{{
							parseDecimal2Digits(productData.commission_percent?.toString())
						}}%</span>
						<span class='error-message' v-if="commissionPercentErr">{{ commissionPercentErr }}</span>
					</div>

					<!-- Product Enable, Feature -->
					<div class='v-stack enable-content'>
						<div class="h-stack product-content-edit edit-enable">
							<span class='label'>
								{{ $t('EditProductComponent.hien_thi_san_pham') }}
							</span>
							<v-switch v-model="productData.enable" :disabled="isSaving" flat
								color="var(--primary-color-1)" hide-details class="my-switches">
							</v-switch>
						</div>
						<div class='h-stack product-content-edit edit-enable'>
							<span class='label'>
								{{ $t('EditProductComponent.san_pham_dac_trung') }}
							</span>
							<v-switch v-model="productData.is_feature" :disabled="isSaving" flat
								color="var(--primary-color-1)" hide-details class="my-switches">
							</v-switch>
						</div>
					</div>
					<!-- Product Feature -->

					<!-- Action Edit -->
					<div class='h-stack form-actions-editing'>
						<button class='cancel-button' :disabled="isSaving" v-on:click="() => {
							// close()
							productData = JSON.parse(JSON.stringify(productDataBackup));
							setListImagesEdit();
							isEditing = false
						}">
							{{ $t('EditProductComponent.hoan_tac') }}
						</button>
						<button class='save-button' :disabled="isSaving" v-on:click="() => {
							if ($props.mode != 'agent') {
								updateProducts()
							} else {
								agentUpdateProducts()
							}
						}">
							<span v-show="!isSaving">{{ $t('EditProductComponent.luu') }}</span>
							<Icon name="eos-icons:loading" size="20" v-show="isSaving" />
						</button>
					</div>


					<!-- Children Product Info -->
					<div class='v-stack childen-product-info-container'
						v-if="productData.children_products && productData.children_products.length > 0">
						<p>{{ $t('EditProductComponent.san_pham_con') }}</p>

						<div v-for="(itemChildProduct, indexChildProduct) in productData.children_products"
							:key="'child_' + itemChildProduct.id + '_edit_' + indexChildProduct"
							class='v-stack child-product-item-edit'>
							<div class='h-stack edit-content'>
								<div class='v-stack avatar'>
									<img :src="(itemChildProduct.profile_picture_edit && itemChildProduct.profile_picture_edit.length) ?
										(itemChildProduct.profile_picture_edit) : itemChildProduct.profile_picture &&
											itemChildProduct.profile_picture.length ? (domainImage + itemChildProduct.profile_picture) :
											icon_for_product" />

									<div class='select-image' v-if="productData.type == appConst.product_type.private">
										<div>
											<input type="file" accept='image/*' :multiple="false" :disabled=isSaving
												v-on:change="($event: any) => {
													fileChangeInput($event, itemChildProduct)
												}" />
											<Icon name="tabler:library-photo"></Icon>
										</div>
									</div>
								</div>

								<div class='v-stack info'>
									<div class="v-stack product-content-edit">
										<span class='label'>
											{{ $t('EditProductComponent.ten') }}
										</span>
										<input :title="$t(' EditProductComponent.ten_san_pham_con')" name='product-name'
											:maxlength="appConst.max_text_short"
											:disabled="isSaving || productData.type != appConst.product_type.private"
											class='input-custom'
											:placeholder="$t('EditProductComponent.ten_san_pham_con')"
											:value="itemChildProduct.name" v-on:input="($event: any) => {
												let childrenProduct = productData.children_products;
												let itemIndex = childrenProduct.findIndex(function (e: any) {
													return e.id == itemChildProduct.id
												})
												childrenProduct[itemIndex].name = $event.target.value;
												if (!childrenProduct[itemIndex].name || !childrenProduct[itemIndex].name.length) {
													childrenProduct[itemIndex].nameErr = $t('EditProductComponent.vui_long_nhap_ten_san_pham');
												} else {
													childrenProduct[itemIndex].nameErr = '';

												}
												productData = {
													...productData,
													children_products: childrenProduct
												}
											}" />
										<span class='error-message'>{{ itemChildProduct.nameErr }}</span>
									</div>
									<div class="v-stack product-content-edit">
										<span class='label'>
											{{ $t('EditProductComponent.gia') }}
										</span>
										<input type="number" :value="itemChildProduct.price"
											class="input-custom price-input" :max="1000000000000" :maxlength="12"											v-on:input="($event: any) => {
												let childrenProduct = productData.children_products;
												let itemIndex = childrenProduct.findIndex(function (e: any) {
													return e.id == itemChildProduct.id
												})
												childrenProduct[itemIndex].price = parseFloat($event.target.value);
												if (childrenProduct[itemIndex].price == null
													|| childrenProduct[itemIndex].price == undefined
													|| childrenProduct[itemIndex].price == 0
													|| isNaN(childrenProduct[itemIndex].price)) {
													childrenProduct[itemIndex].priceErr = $t('EditProductComponent.vui_long_nhap_gia_muon_ban')
												}
												else {
													childrenProduct[itemIndex].priceErr = ''
												}
												productData = {
													...productData,
													children_products: childrenProduct
												}
											}"v-on:blur="() => {
												let childrenProduct = productData.children_products;
												let itemIndex = childrenProduct.findIndex(function (e: any) {
													return e.id == itemChildProduct.id
												})
												if (childrenProduct[itemIndex].price == null || childrenProduct[itemIndex].price == undefined || childrenProduct[itemIndex].price == 0
													|| isNaN(childrenProduct[itemIndex].price)
												) {
													childrenProduct[itemIndex].priceErr = $t('EditProductComponent.vui_long_nhap_gia_muon_ban');
												}
												else {
													childrenProduct[itemIndex].priceErr = ''
												}
												productData = {
													...productData,
													children_products: childrenProduct
												}
											}" />
										<span class="price-text" v-if="itemChildProduct.price">{{
											formatCurrency(parseDecimal2Digits(itemChildProduct.price?.toString()), productData.shop.currency) }}</span>
										<span class='error-message'>{{ itemChildProduct.priceErr }}</span>
									</div>

									<div class="v-stack product-content-edit">
										<span class='label'>
											{{ $t('EditProductComponent.gia_uu_dai') }}
										</span>
										<input type="number" :value="itemChildProduct.price_off"
											class="input-custom price-input"
											placeholder="{{ $t('EditProductComponent.de_trong_khi_khong_giam_gia') }}"
											:max="1000000000000" :maxlength="12" v-on:input="($event: any) => {
												let childrenProduct = productData.children_products;
												let itemIndex = childrenProduct.findIndex(function (e: any) {
													return e.id == itemChildProduct.id
												})
												childrenProduct[itemIndex].price_off = parseFloat($event.target.value);

												productData = {
													...productData,
													children_products: childrenProduct
												}
											}" />
										<span class="price-text" v-if="itemChildProduct.price_off">{{
											formatCurrency(parseDecimal2Digits(itemChildProduct.price_off?.toString()), productData.shop.currency)
										}}</span>
									</div>

									<div class="v-stack product-content-edit">
										<span class='label'>
											{{ $t('EditProductComponent.ton_kho') }}
										</span>
										<input type="number" :disabled="isSaving" :value="itemChildProduct.stock"
											max="1000000000000"
											:placeholder="$t('EditProductComponent.de_trong_khi_khong_quan_ly_ton_kho')"
											class="input-custom price-input" v-on:input="($event: any) => {
												itemChildProduct.stock = $event.target?.value.length ? parseFloat($event.target?.value) : null;
											}">
										<span class="price-text notice" v-if="itemChildProduct.stock != null">{{
											formatNumber(itemChildProduct.stock) }}</span>
										<span class="price-text notice" v-else>{{
											$t('EditProductComponent.khong_quan_ly_ton_kho')
										}}</span>
									</div>

									<div class="h-stack product-content-edit edit-enable">
										<span class='label'>
											{{ $t('EditProductComponent.hien_thi') }}
										</span>
										<v-switch v-model="itemChildProduct.enable" :disabled="isSaving" flat
											color="var(--primary-color-1)" hide-details class="my-switches">
										</v-switch>
									</div>
								</div>
							</div>

							<div class='h-stack form-actions'>
								<button class='delete-button' :disabled=itemChildProduct.isSaving v-on:click="() => {
									selectedChildProduct = itemChildProduct;
									showModalConfirmDeleteChildProduct = true
								}">
									{{ $t('EditProductComponent.xoa') }}
								</button>
								<button class='cancel-button' :disabled=itemChildProduct.isSaving v-on:click="() => {
									let childrenProduct = productData.children_products;
									let itemIndex = childrenProduct.findIndex(function (e: any) {
										return e.id == itemChildProduct.id
									})
									let itemBackupIndex = productDataBackup.children_products.findIndex(function (e: any) {
										return e.id == itemChildProduct.id
									})
									childrenProduct[itemIndex] = JSON.parse(JSON.stringify(productDataBackup.children_products[itemBackupIndex]));
									productData = {
										...productData,
										children_products: childrenProduct
									}
								}">
									{{ $t('EditProductComponent.hoan_tac') }}
								</button>
								<button class='save-button' :disabled="itemChildProduct.isSaving" v-on:click="() => {
									if ($props.mode != 'agent') {
										updateChildProduct(itemChildProduct)
									}
									else {
										agentUpdateChildProduct(itemChildProduct)
									}

								}">
									<span v-if="!itemChildProduct.isSaving">{{ $t('EditProductComponent.luu') }}</span>
									<Icon name="eos-icons:loading" size="20" v-else />
								</button>
							</div>

						</div>
					</div>

				</div>



			</div>
			<div class='h-stack form-actions' v-if="!isEditing">
				<button class='cancel-button' :disabled=isSaving v-on:click="() => {
					showModalConfirmDelete = true
				}">
					{{ $t('EditProductComponent.xoa') }}
				</button>
				<button class='save-button' :disabled=isSaving v-on:click="() => {
					isEditing = true
				}">
					{{ $t('EditProductComponent.chinh_sua') }}
				</button>
				<button class='view-button' :disabled=isSaving v-on:click="() => {
					router.push(appRoute.ProductComponent + '/' + (productData.slug?.length ? productData.slug : productData.id))
				}">
					{{ $t('EditProductComponent.xem') }}
				</button>
			</div>

			<VueFinalModal class="my-modal-container"
				content-class="v-stack my-modal-content-container confirm-delete-product" :overlay-behavior="'persist'"
				v-model="showModalConfirmDelete" v-on:closed="() => {
					showModalConfirmDelete = false
				}" contentTransition="vfm-slide-up">
				<div class='v-stack'>
					<span class='delete-product-message'>
						{{ $t('EditProductComponent.xoa_san_pham') }} {{ " " }}
						<span class='delete-product-name'>
							{{ showTranslateProductName(productData) }}
						</span>?
					</span>
				</div>
				<div class='h-stack confirm-modal-buttons'>
					<button class='reject-button' :disabled="isSaving" v-on:click="() => {
						showModalConfirmDelete = false
					}">
						{{ $t('EditProductComponent.khong') }}
					</button>
					<button class='accept-button' :disabled="isSaving" v-on:click="() => {
						deleteProduct();
					}">
						{{ $t('EditProductComponent.co') }}
					</button>
				</div>
			</VueFinalModal>

			<VueFinalModal class="my-modal-container"
				content-class="my-modal-content-container v-stack confirm-delete-product" :overlay-behavior="'persist'"
				v-model="showModalConfirmDeleteChildProduct" v-on:closed="() => {
					showModalConfirmDeleteChildProduct = false
				}" contentTransition="vfm-slide-up">
				<div class='v-stack'>
					<span class='delete-product-message'>
						{{ $t('EditProductComponent.xoa_san_pham_con') }} {{ " " }}
						<span class='delete-product-name'>
							{{ showTranslateProductName(selectedChildProduct) }}
						</span>?
					</span>
				</div>
				<div class='h-stack confirm-modal-buttons'>
					<button class='reject-button' :disabled="isSaving" v-on:click="() => {
						showModalConfirmDeleteChildProduct = false
					}">
						{{ $t('EditProductComponent.khong') }}
					</button>
					<button class='accept-button' :disabled="isSaving" v-on:click="() => {
						deleteProductChild();
					}">
						{{ $t('EditProductComponent.co') }}
					</button>
				</div>
			</VueFinalModal>

			<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container"
				:overlay-behavior="'persist'" v-model="showCreateCategoryModal" v-on:closed="() => {
					showCreateCategoryModal = false
				}" contentTransition="vfm-fade">
				<CreateCategoryComponent :shopId="shopData.id" :shop_data="JSON.parse(JSON.stringify(shopData))"
					:dataCategories="dataShopCategories" v-on:close="(obj: any) => {
						showCreateCategoryModal = false;
						if (obj?.id) {
							productData.category_ids.push(obj?.id);
							getShopCategories();
						}
					}" :mode="props.mode" />
			</VueFinalModal>

		</div>
		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>
	</div>

</template>

<script lang="ts" setup>
import EXIF from 'exif-js';
import ISO6391 from 'iso-639-1';
import { VueDraggableNext } from 'vue-draggable-next'
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, nonAccentVietnamese, showTranslateProductName, showTranslateProductDescription, validNumber, validDecimal2Digits, parseDecimal2Digits } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import icon_for_product from '~/assets/image/icon-for-product.png';
import { AgentService } from '~/services/agentService/agentService';
import exifr from 'exifr';
import { RatingService } from '~/services/ratingService/ratingService';
import { HttpStatusCode } from 'axios';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import { ProductUpdateDto } from '~/assets/DTOs/productDtos';

var imgListTemp = [
	"https://i.pinimg.com/originals/f4/4f/4c/f44f4c12c4b8acc4447868116ec74489.jpg",
	"https://i.ytimg.com/vi/U2Y5zrbQeks/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLCl6TK3Z10axsqM9xlj3u8ev4EN6w",
	"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT1GJxuqn44qU0EU4Ndy77Y5ZD5NKNmZzUO7w&s",
	"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS3qffwlrIHS88D3-r5IkdzVnt4wB5ZrmRvzg&s"
]

var props = defineProps({
	isEditing: Boolean,
	productData: {},
	mode: null
});
var emit = defineEmits(['close']);
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

const { t, locale } = useI18n();

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var agentService = new AgentService();
var shopService = new ShopService();
var categoryService = new CategoryService();
var imageService = new ImageService();
var ratingService = new RatingService();
var searchProductTimeout: any;

var imageFilesName = ref(null as any);

var isEditing = ref(false);
var isSaving = ref(false);
var productId = ref((route.params.id ? route.params.id : null) as any);
var productData = ref(null as any);
var productDataBackup = ref();

var name = ref("");
var nameErr = ref("");
var priceErr = ref<any>("");
var priceOffErr = ref<any>("");
var stockErr = ref<any>("");
var commissionPercentErr = ref<any>("");
var dataShopCategories = ref([] as any[]);

var listImagesEdit = ref([] as any[]);

var selectedChildProduct = ref();
var showModalConfirmDelete = ref(false);
var showModalConfirmDeleteChildProduct = ref(false);
var shopData = ref(null as any);
var selectLanguage = ref(null as any);
var selectedCategories = ref([] as any[]);

var showCreateCategoryModal = ref(false);
var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);

onMounted(async () => {
	if (props.mode != 'agent') {
		productId.value = route.params.id ? route.params.id : null;
	}
	else {
		productId.value = route.params.product_id ? route.params.product_id : null;
	}
	await getProductDetail();
})

function getProductDetail() {
	productService.detailProduct(productId.value).then(async res => {
		productData.value = JSON.parse(JSON.stringify(res.body.data));

		// orientation.value = 0;
		productData.value.category_ids =
			productData.value.categories && productData.value.categories.length
				? productData.value.categories.map((item: any) => item.id)
				: [];
		productData.value.images = productData.value?.images.sort((a: any, b: any) => a.index - b.index);
		useSeoMeta({
			title: `${productData.value.shop.name} - ${showTranslateProductName(productData.value)}: ${t('EditProductComponent.chinh_sua')}`
		})
		shopData.value = productData.value.shop;
		shopData.value.language = shopData.value.language.at(0) != '[' ? shopData.value.language : JSON.parse(shopData.value.language);

		if (shopData.value.language.indexOf(appConst.defaultLanguage) == -1) {
			shopData.value.language.splice(0, 0, appConst.defaultLanguage);
		}
		if (shopData.value.language.indexOf('en') == -1) {
			shopData.value.language.splice(shopData.value.language.indexOf(appConst.defaultLanguage) + 1, 0, 'en');
		}
		selectLanguage.value = shopData.value.language[shopData.value.language.indexOf(locale.value)] ?? shopData.value.language[0] ?? appConst.defaultLanguage;


		await clearTranslationUnuse();
		await setListImagesEdit()
		selectLanguage.value = shopData.value.language[0];

		// let indexCurrentLanguage = productData.value.translation.findIndex(function (e: any) {
		// 	return e.language_code = selectLanguage.value;
		// });
		// if (indexCurrentLanguage == -1) {
		// 	productData.value.translation.push({
		// 		language_code: selectLanguage.value,
		// 		name: productData.value.name,
		// 		description: productData.value.notes,
		// 	})
		// }

		productDataBackup.value = JSON.parse(JSON.stringify(productData.value));
		await getShopCategories();
		await getProductRating()

	})
}
function setSelectedCategories() {
	let arr = dataShopCategories.value.filter(el => {

		return productData.value.category_ids.indexOf(el.id) != -1;
	})
	selectedCategories.value = JSON.parse(JSON.stringify(arr));
}
function clearTranslationUnuse() {
	let productTransTemp = JSON.parse(JSON.stringify(productData.value.translation));
	productTransTemp = productTransTemp.filter((e: any) => {
		return shopData.value.language.indexOf(e.language_code) != -1;
	});
	productData.value.translation = JSON.parse(JSON.stringify(productTransTemp))
}
function close(updated = false) {
	// emit('close', updated);
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
	// if (updated == true) {
	// 	nuxtApp.$emit('refresh_product_manage', true)
	// }
}

async function updateProducts() {
	isSaving.value = true;
	let checkName = await validateName();
	validateNote();
	let checkPrice = validatePrice();
	let checkPriceOff = validatePriceOff();
	let checkStock = validateStock();
	let checkCommissionPercent = validateCommissionPercent();
	if (checkName && checkPrice && checkPriceOff && checkStock && checkCommissionPercent) {
		let updateObj = new ProductUpdateDto(productData.value);
		updateObj.image_delete = handleGetListDeleteImage();
		updateObj.images = handleGetListUpdateImage();
		productService.updateProduct(updateObj).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				await handleSaveNewImage().then(() => {
					// isSaving.value = false;
					toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
					setTimeout(() => {
						close(true);	
					}, 1500);
					
				});
			}			else {
				// toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
				await validateName();
				await validateNote();
				await validatePrice();
				await validatePriceOff();
				await validateStock();
				await validateCommissionPercent();
				hightlightError();
				isSaving.value = false;
			}		}).catch(async err => {
			await validateName();
			await validateNote();
			await validatePrice();
			await validatePriceOff();
			await validateStock();
			await validateCommissionPercent();
			// toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
			hightlightError();
			isSaving.value = false;
		})
	}
	else {
		await validateName();
		await validateNote();
		await validatePrice();
		await validatePriceOff();
		await validateStock();
		await validateCommissionPercent();
		hightlightError()
		isSaving.value = false;
	}
}
async function agentUpdateProducts() {
	isSaving.value = true;
	let checkName = await validateName();
	validateNote();
	let checkPrice = validatePrice();
	let checkPriceOff = validatePriceOff();
	let checkStock = validateStock();
	let checkCommissionPercent = validateCommissionPercent();
	if (checkName && checkPrice && checkPriceOff && checkStock && checkCommissionPercent) {
		let updateObj = new ProductUpdateDto(productData.value);
		updateObj.image_delete = handleGetListDeleteImage();
		updateObj.images = handleGetListUpdateImage();
		agentService.agentUpdateProduct(updateObj).then(async res => {
			if (res.status == HttpStatusCode.Ok) {

				await handleSaveNewImage().then(() => {
					// isSaving.value = false;
					toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
					close(true);
				});

				// }listImagesEdit
			}			else {
				await validateName();
				await validateNote();
				await validatePrice();
				await validatePriceOff();
				await validateStock();
				await validateCommissionPercent();
				// toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
				hightlightError();
				isSaving.value = false;
			}		}).catch(async err => {
			await validateName();
			await validateNote();
			await validatePrice();
			await validatePriceOff();
			await validateStock();
			await validateCommissionPercent();
			// toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
			hightlightError();
		 isSaving.value = false;
		})
	}
	else {
		await validateName();
		await validateNote();
		await validatePrice();
		await validatePriceOff();
		await validateCommissionPercent();
		hightlightError();
		isSaving.value = false;
	}

}
function updateChildProduct(product: any) {

	let childrenProduct = productData.value.children_products;
	let itemIndex = childrenProduct.findIndex(function (e: any) {
		return e.id == product.id
	});
	product.isSaving = true;
	childrenProduct[itemIndex] = product;

	productData.value = {
		...productData.value,
		children_products: childrenProduct
	}

	product.category_ids = productData.value.category_ids;

	if (product.profile_picture_edit) {
		product.profile_picture = "";
	}

	// setState({ isSaving: true })
	productService.updateProduct(product).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			if (product.profile_picture_edit && product.profile_picture_edit.length) {
				let profile_picture = {
					path: product.profile_picture_edit,
					object_type: appConst.object_type.product,
					title: productData.value.name + " " + product.name + " profile_picture",
					description: productData.value.name + " " + product.name + " description",
					index: 0,
					orientation: product.orientation,
					isEdit: false,
					parent_id: product.id
				}
				imageService.insertImage(profile_picture).then(async resProfilePicture => {
					await getProductDetail();
					isSaving.value = false;
					isEditing.value = true;
					toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
				});
			}
			else {
				await getProductDetail();
				isSaving.value = false;
				isEditing.value = true;
				toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
			}
		}
		else {
			toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
			let childrenProduct = productData.value.children_products;
			let itemIndex = childrenProduct.findIndex(function (e: any) {
				return e.id == product.id
			});
			product.isSaving = false;
			childrenProduct[itemIndex] = product;

			productData.value = {
				...productData.value,
				children_products: childrenProduct
			}
		}
	}).catch(err => {
		toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
		let childrenProduct = productData.value.children_products;
		let itemIndex = childrenProduct.findIndex(function (e: any) {
			return e.id == product.id
		});
		product.isSaving = false;
		childrenProduct[itemIndex] = product;

		productData.value = {
			...productData.value,
			children_products: childrenProduct
		}
	})
}

function agentUpdateChildProduct(product: any) {
	let childrenProduct = productData.value.children_products;
	let itemIndex = childrenProduct.findIndex(function (e: any) {
		return e.id == product.id
	});
	product.isSaving = true;
	childrenProduct[itemIndex] = product;

	productData.value = {
		...productData.value,
		children_products: childrenProduct
	}

	product.category_ids = productData.value.category_ids;

	if (product.profile_picture_edit) {
		product.profile_picture = "";
	}

	// setState({ isSaving: true })
	agentService.agentUpdateProduct(product).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			if (product.profile_picture_edit && product.profile_picture_edit.length) {
				let profile_picture = {
					path: product.profile_picture_edit,
					object_type: appConst.object_type.product,
					title: productData.value.name + " " + product.name + " profile_picture",
					description: productData.value.name + " " + product.name + " description",
					index: 0,
					orientation: product.orientation,
					isEdit: false,
					parent_id: product.id
				}
				imageService.insertImage(profile_picture).then(async resProfilePicture => {
					await getProductDetail();
					isSaving.value = false;
					isEditing.value = true;
					toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
				});
			}
			else {
				await getProductDetail();
				isSaving.value = false;
				isEditing.value = true;
				toast.success(t('EditProductComponent.cap_nhat_san_pham_thanh_cong'));
			}
		}
		else {
			toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
			let childrenProduct = productData.value.children_products;
			let itemIndex = childrenProduct.findIndex(function (e: any) {
				return e.id == product.id
			});
			product.isSaving = false;
			childrenProduct[itemIndex] = product;

			productData.value = {
				...productData.value,
				children_products: childrenProduct
			}
		}
	}).catch(err => {
		toast.error(t('EditProductComponent.cap_nhat_san_pham_that_bai'));
		let childrenProduct = productData.value.children_products;
		let itemIndex = childrenProduct.findIndex(function (e: any) {
			return e.id == product.id
		});
		product.isSaving = false;
		childrenProduct[itemIndex] = product;

		productData.value = {
			...productData.value,
			children_products: childrenProduct
		}
	})
}

function getShopCategories() {
	categoryService.getCategoryByShopId(productData.value.shop_id).then(res => {
		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data));
		setSelectedCategories()
	})
}

async function fileChangeInput(fileInput: any, childProduct?: any) {
	if (fileInput.target.files.length) {
		if (fileInput.target.files.length > appConst.productImageMaxAmount - listImagesEdit.value.length) {
			toast.warn(t('EditProductComponent.so_luong_anh_toi_da', { amount: appConst.productImageMaxAmount }))
		}
		else {
			imageFilesName.value = fileInput.target.files;
			for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
				if (fileInput.target.files[i].size > appConst.image_size.max) {
					let imgErr = t('EditProductComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
					toast.error(imgErr);
				}
				else {
					const reader = new FileReader();
					reader.onload = async (e: any) => {
						const image = new Image();
						image.src = e.target.result;
						let orientationExif;
						if (fileInput.target.files[i].type != 'image/webp') {
							orientationExif = await exifr.orientation(image) || 0;
						}
						else orientationExif = 0;
						// orientation.value = orientationExif ? orientationExif : 0;
						if (childProduct && childProduct.id) {
							let childrenProduct = productData.value.children_products;
							let itemIndex = childrenProduct.findIndex(function (e: any) {
								return e.id == childProduct.id
							})
							childrenProduct[itemIndex].orientation = orientationExif ? orientationExif : 0;
							childrenProduct[itemIndex].profile_picture_edit = image.src;
							productData.value = {
								...productData.value,
								children_products: childrenProduct
							};
						}
						else {
							let newImg = {
								isEdit: true,
								object_type: appConst.object_type.product,
								orientation: orientationExif,
								description: null,
								enable: true,
								parent_id: productData.value.id,
								path: image.src,
								title: productData.value.name
							}
							listImagesEdit.value.push(newImg);
							// profile_picture.value = image.src;
							// orientation.value = orientationExif ? orientationExif : 0;
						}
					}
					await reader.readAsDataURL(fileInput.target.files[i]);
				}
			}
		}
	}
}

function deleteProduct() {
	isSaving.value = true;

	productService.deleteProduct(productData.value.id).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			isSaving.value = false;
			toast.success(t('EditProductComponent.xoa_san_pham_thanh_cong'));
			router.back();
		}
		else {
			toast.error(t('EditProductComponent.xoa_san_pham_that_bai'));
			isSaving.value = false;
		}
	}).catch(err => {
		toast.error(t('EditProductComponent.xoa_san_pham_that_bai'));
		isSaving.value = false;
	})
}

function deleteProductChild() {
	let childrenProduct = productData.value.children_products;
	let itemIndex = childrenProduct.findIndex((e: any) => {
		return e.id == selectedChildProduct.value.id
	});
	selectedChildProduct.value.isSaving = true;
	childrenProduct[itemIndex] = selectedChildProduct.value;

	productData.value = {
		...productData.value,
		children_products: childrenProduct
	}

	productService.deleteProduct(selectedChildProduct.value.id).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			isSaving.value = false;
			showModalConfirmDeleteChildProduct.value = false;
			await getProductDetail();
			toast.success(t('EditProductComponent.xoa_san_pham_thanh_cong'));
		}
		else {
			toast.error(t('EditProductComponent.xoa_san_pham_that_bai'));
			isSaving.value = false;
			showModalConfirmDeleteChildProduct.value = false;
		}
	}).catch(err => {
		toast.error(t('EditProductComponent.xoa_san_pham_that_bai'));
		isSaving.value = false;
		showModalConfirmDeleteChildProduct.value = false;
	})
}

function getPercentSaleOff(product: any) {
	if (product.price_off && product.price) return -Math.ceil(((product.price - product.price_off) / product.price) * 100 || 0);
	return 0
}

function hightlightError() {
	let els = document.getElementsByClassName("error-message");
	Array.prototype.forEach.call(els, function (el) {
		// Do stuff here
		el.classList.add("hight-light");
		setTimeout(() => {
			el.classList.remove("hight-light");
		}, 1000);
	});
}

function nameProductChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.name = text;
	// 	if (!productData.value.name || !productData.value.name.length) {
	// 		nameErr.value = 'Vui lòng nhập tên Tiếng Việt của sản phẩm';
	// 	} else {
	// 		nameErr.value = '';
	// 	}
	// }

	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		productData.value.translation[indexTranslate].name = text;
	}
	else {
		productData.value.translation.push({
			language_code: translation,
			name: text
		})
	}
}
function validateName() {
	let indexVietnamese = productData.value.translation.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && productData.value.translation[indexVietnamese]?.name?.length > 0) {
		nameErr.value = "";
		productData.value.name = productData.value.translation[indexVietnamese].name;
		return true
	}

	let indexFirstName = productData.value.translation.findIndex(function (el: any) {
		return el.name?.length > 0
	})
	if (indexFirstName != -1) {
		nameErr.value = "";
		productData.value.name = productData.value.translation[indexFirstName].name;
		return true
	}
	if (productData?.value?.name?.length) {
		nameErr.value = "";
		return true
	}
	nameErr.value = t('EditProductComponent.vui_long_nhap_ten_san_pham');
	return false;
}
function validateNote() {
	let indexVietnamese = productData.value.translation.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && productData.value.translation[indexVietnamese]?.description?.length > 0) {
		productData.value.notes = productData.value.translation[indexVietnamese].description;
		return true
	}

	let indexFirstDescription = productData.value.translation.findIndex(function (el: any) {
		return el.description?.length > 0
	})
	if (indexFirstDescription != -1) {
		productData.value.notes = productData.value.translation[indexFirstDescription].description;
		return true
	}
	productData.value.notes = "";
	return false;
}

function validatePrice() {
	if (productData.value?.price?.toString()) {
		priceErr.value = !validDecimal2Digits(productData.value?.price?.toString()) ? t('EditProductComponent.gia_phai_la_kieu_so_2_thap_phan') : null;
	}
	else {
		priceErr.value = null
	}

	return !priceErr.value;
}

function validatePriceOff() {
	if (productData.value?.price_off?.toString()) {
		priceOffErr.value = !validDecimal2Digits(productData.value?.price_off?.toString()) ? t('EditProductComponent.gia_phai_la_kieu_so_2_thap_phan') : null;
	}
	else {
		priceOffErr.value = null
	}

	return !priceOffErr.value
}
function validateStock() {
	if (productData.value?.stock) {
		stockErr.value = !validNumber(productData.value?.stock) ? t('EditProductComponent.ton_kho_phai_la_kieu_so') : null;
	}
	else {
		stockErr.value = null
	}

	return !stockErr.value
}
function validateCommissionPercent() {
	if (productData.value?.commission_percent?.toString()) {
		const value = parseFloat(productData.value.commission_percent.toString());
		if (!validDecimal2Digits(productData.value?.commission_percent?.toString())) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_phai_la_kieu_so_2_thap_phan');
		} else if (value < 0) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_phai_lon_hon_0');
		} else if (value > 100) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_khong_vuot_qua_100');
		} else {
			commissionPercentErr.value = null;
		}
	}
	else {
		commissionPercentErr.value = null
	}

	return !commissionPercentErr.value
}

function getCommissionPercentPlaceholder() {
	const { t } = useI18n();
	
	// Check if shop settings have commission percent enabled and with a value
	if (shopData.value?.settings?.general?.commission_percent?.enabled && 
		shopData.value?.settings?.general?.commission_percent?.value !== null &&
		shopData.value?.settings?.general?.commission_percent?.value !== undefined) {
		
		const shopCommissionPercent = shopData.value.settings.general.commission_percent.value;
		return `${t('EditProductComponent.de_trong_neu_dung_commission_cua_shop')} (${shopCommissionPercent}%)`;
	}
	
	// Default placeholder when shop commission is not enabled or has no value
	return t('EditProductComponent.de_trong_neu_dung_commission_cua_shop');
}

function noteProductChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.notes = text;
	// }

	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		productData.value.translation[indexTranslate].description = text;
	}
	else {
		productData.value.translation.push({
			language_code: translation,
			description: text
		})
	}
}
function getNameTranslate(language: string) {
	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? productData.value.translation[indexTranslate].name : '';
}
function getNoteTranslate(language: string) {
	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? productData.value.translation[indexTranslate].description : '';
}
async function getProductRating() {
	await ratingService.calcObjectRating(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.ratings = res.body.data
		}

	})
	await ratingService.listRatingByObject(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.comment_count = res.body.data.count;
			productData.value.comments = res.body.data.result;
		}

	})
}

async function setListImagesEdit() {
	var listTemp: any = [];
	productData.value.images.forEach(async (img: any) => {
		let newImg = {
			isEdit: true,
			object_type: img.object_type,
			orientation: img.orientation,
			description: img.description,
			enable: true,
			parent_id: productData.value.id,
			path: domainImage + img.path,
			title: img.title,
			index: img.index,
			is_profile_picture: img.is_profile_picture ? img.is_profile_picture : false,
			id: img.id
		}
		listTemp.push(newImg);
	});
	listImagesEdit.value = JSON.parse(JSON.stringify(listTemp));
}
function handleGetListDeleteImage() {
	let listDelete: any[] = [];
	productData.value.images.forEach((img: any, index: number) => {
		let indexInCurrentList = listImagesEdit.value.findIndex(function (e) {
			return e.id == img.id;
		})
		if (indexInCurrentList == -1) {
			listDelete.push({ id: img.id })
		}
	});
	return listDelete;
}

function handleGetListUpdateImage() {
	let listUpdate: any[] = [];
	productData.value.images.forEach((img: any, index: number) => {
		let indexInCurrentList = listImagesEdit.value.findIndex(function (e) {
			return e.id == img.id;
		})
		if (indexInCurrentList != -1) {
			let updateImg;
			updateImg = {
				id: img.id,
				title: img.title,
				index: indexInCurrentList,
				is_profile_picture: indexInCurrentList == 0 ? true : false
			}
			listUpdate.push(updateImg)
		}
	});
	return listUpdate;
}

async function handleSaveNewImage() {
	await Promise.all(
		listImagesEdit.value.map(async (img, index) => {
			img.index = index;
			img.is_profile_picture = index == 0 ? true : false;
			if (!img.id) {
				await imageService.insertImage(img)
			}
		})
	)
}
</script>

<style lang="scss" src="./EditProductStyles.scss"></style>