<template>
	<div class="manage-category-container" v-if="!isErrored">
		<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					// router.back()
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.ManageCategoryComponent }}</h3>
			<div class="header-right">
			</div>
		</div> -->
		<HeaderComponent :title="$t('AppRouteTitle.ManageCategoryComponent')">

		</HeaderComponent>
		<div class='v-stack list-category-container' v-if="!refreshing">
			<v-expansion-panels variant="accordion" v-if="listCategory.length">
				<VueDraggableNext :delay="500" class="drag-drop-container" :list="listCategory"
					v-on:change="() => { updateIndexList() }">
					<v-expansion-panel class="item-category-accordion" v-for="(itemCategory) in listCategory"
						:key="itemCategory.id">
						<v-expansion-panel-title class="accordion-header">
							<span class="stt">{{ itemCategory.index }}</span>
							{{ itemCategory.name }}
							<span>
								({{ itemCategory.products.length }} sp)
							</span>
						</v-expansion-panel-title>
						<v-expansion-panel-text>
							<div class='h-stack'>
								<button class='item-category-action view-product' v-on:click="() => {
									selectedCategoryObj = JSON.parse(JSON.stringify(itemCategory));
									showProductsModal = true
								}">
									<span>
										<Icon name="ion:eye" />
									</span> Sản phẩm
								</button>
								<button class='item-category-action edit' v-on:click="() => {
									categoryEditingObj = JSON.parse(JSON.stringify(itemCategory));
									showEditCategoryModal = true
								}">
									<span>
										<Icon name="fa6-regular:pen-to-square"></Icon>
									</span> Sửa
								</button>
								<button class='item-category-action delete' v-on:click="() => {
									categoryDeleteObj = JSON.parse(JSON.stringify(itemCategory));
									showDeleteCategoryModal = true
								}">
									<span>
										<Icon name="material-symbols:delete-forever-outline-rounded"></Icon>
									</span> Xóa
								</button>
							</div>
						</v-expansion-panel-text>
					</v-expansion-panel>
				</VueDraggableNext>

			</v-expansion-panels>
			<div class='categories-list-empty' v-else-if="!refreshing">
				<img loading="lazy" :src="list_empty" :placeholder="list_empty" alt="Danh sách trống" />

				<span>
					Danh sách trống
				</span>
			</div>
		</div>
		<div class="loading" v-else>
			Đang tải...
		</div>
		<button class='add-category-button' :disabled=refreshing v-on:click="() => {
			showCreateCategoryModal = true
		}">
			<Icon name="ic:sharp-add"></Icon>
			Thêm danh mục
		</button>
		<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
			v-model="showCreateCategoryModal" v-on:closed="() => {
				showCreateCategoryModal = false
			}" contentTransition="vfm-fade">
			<CreateCategoryComponent :shopId="shopData.id" :shop_data="JSON.parse(JSON.stringify(shopData))" :dataCategories="listCategory" v-on:close="() => {
				showCreateCategoryModal = false;
				getListCategory();
			}" />
		</VueFinalModal>
		<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
			v-model="showEditCategoryModal" v-on:closed="() => {
				showEditCategoryModal = false
			}" contentTransition="vfm-fade">
			<EditCategoryComponent :categoryData="JSON.parse(JSON.stringify(categoryEditingObj))"
				:shop_data="JSON.parse(JSON.stringify(shopData))"
				:dataCategories="JSON.parse(JSON.stringify(listCategory))" v-on:close="() => {
					showEditCategoryModal = false;
					getListCategory();
				}" />
		</VueFinalModal>

		<VueFinalModal class="my-modal-container"
			content-class="my-modal-content-container v-stack category-content-container products-category-modal" :overlay-behavior="'persist'"
			v-model="showProductsModal" v-on:closed="() => {
				showProductsModal = false
			}" contentTransition="vfm-fade">
			<ProductsCategoryComponent :categoryData="JSON.parse(JSON.stringify(selectedCategoryObj))"
				:shopData="JSON.parse(JSON.stringify(shopData))" v-on:close="(e) => {
					if (e) {
						getListCategory();
					}
					showProductsModal = false;
				}" />
		</VueFinalModal>

		<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
			v-model="showDeleteCategoryModal" v-on:closed="() => {
				showDeleteCategoryModal = false
			}" contentTransition="vfm-fade">
			<div class='v-stack delete-category-content'>
				<span class='delete-category-title'>
					Xóa danh mục?
				</span>

				<span class='delete-category-message'>
					Bạn chắc chắn muốn xóa danh mục
					<span class='category-name'>
						{{
							categoryDeleteObj
								? categoryDeleteObj.name
								: ""
						}}
					</span>?
				</span>
			</div>
			<div class='h-stack confirm-modal-buttons'>
				<button class='reject-button' :disabled=isUpdating v-on:click="() => {
					showDeleteCategoryModal = false;
				}">
					Không
				</button>
				<button class='accept-button' :disabled=isUpdating v-on:click="() => {
					deleteCategory(categoryDeleteObj);
				}">
					Có
				</button>
			</div>
		</VueFinalModal>
	</div>
	<div class="manage-category-container" v-else>
		<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}" >
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.ManageCategoryComponent }}</h3>
			<div class="header-right">
			</div>
		</div> -->
		<HeaderComponent :title="$t('AppRouteTitle.ManageCategoryComponent')">

		</HeaderComponent>
		<div class=" access-denied">
			<img loading="lazy" :src="none_shop" :placeholder="none_shop" alt="Từ chối quyền truy cập" />
			<span>
				Bạn chưa đăng ký cửa hàng
			</span>
		</div>

	</div>
</template>

<script lang="ts" setup>
const { t } = useI18n()
useSeoMeta({
	title: t('AppRouteTitle.ManageCategoryComponent')
})
var router = useRouter();
var route = useRoute();
var props = defineProps({
	shopData: {}
})

import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg"

import { VueDraggableNext } from 'vue-draggable-next'
import { toast } from 'vue3-toastify';
import { VueFinalModal } from 'vue-final-modal';
import { appConst } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import ProductsCategoryComponent from './productsCategory/ProductsCategoryComponent.vue';
import EditCategoryComponent from './editCategory/EditCategoryComponent.vue';
import { HttpStatusCode } from "axios";

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var imageService = new ImageService();
var shopService = new ShopService();
var searchProductTimeout: any;
var categoryService = new CategoryService();

var isErrored = ref(false);
var shopData = ref((props.shopData ? props.shopData : null) as any);
var listCategory = ref([] as any[]);
var refreshing = ref(false);
var isSaving = ref(false);
var isUpdating = ref(false);
var showCreateCategoryModal = ref(false);
var showEditCategoryModal = ref(false);
var showDeleteCategoryModal = ref(false);
var showProductsModal = ref(false);
var categoryEditingObj = ref(null as any);
var selectedCategoryObj = ref(null as any);
var categoryDeleteObj = ref(null as any);
var isUpdatingIndex = ref(false);

onMounted(async () => {
	await getMyShop();

})
function getMyShop() {
	refreshing.value = true;
	shopService.myShop().then(async res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
			shopData.value = res.body.data;
			getListCategory()
		}
		else if (res.status == 401) {
			toast.error("Bạn chưa đăng ký cửa hàng!");
			isErrored.value = true;
			refreshing.value = false;
		}
	})
}
function getListCategory() {
	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
		listCategory.value = JSON.parse(JSON.stringify(res.body.data))
		refreshing.value = false;
	})
}

function close() {
	router.back();
}

function deleteCategory(itemCategory: any) {
	categoryService.removeCategory(itemCategory.id).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			getListCategory()
		}
	})
}

function updateIndexList() {
	isUpdatingIndex.value = true;
	listCategory.value.map((itemCategory, index) => itemCategory.index = index + 1);
	saveIndexUpdate();
}

function saveIndexUpdate() {
	let arr = listCategory.value.map((item: any) => { return { id: item.id, index: item.index } });
	// refreshing.value = true
	categoryService.updateIndex(arr).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			// toast.success("Cập nhật thứ tự thành công");
			// getListCategory();
		}
		else {
			toast.success("Cập nhật thứ tự thất bại\nVui lòng thử lại sau");
		}
		isUpdatingIndex.value = false;
	})
}
</script>

<style lang="scss" src="./ManageCategoryStyles.scss"></style>