<template>
  <div class="public-container">
    <div class="policy-container">
      <!-- <div class='title-header'>
      <div class="header-left">
        <button class="back-button" v-on:click="() => {
          close();
        }">
          <Icon name="lucide:chevron-left" />
        </button>
      </div>
      <h3>{{ appRouteTitle.PolicyComponent }}</h3>
      <div class="header-right"></div>
    </div> -->
      <SubHeaderV2Component :title="$t('AppRouteTitle.PolicyComponent')">
      </SubHeaderV2Component>
      <div class="policy-content-container">
        <div ref="policyContent"></div>
        <!-- {{ policyContent }} -->
      </div>
    </div>
  </div>

</template>
<style lang="scss" src="./PolicyStyles.scss" scoped></style>
<script lang="ts" setup>
import axios from 'axios';
import { ref } from 'vue'
import { appRoute } from '~/assets/appRoute';
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

var policyContent = ref(null as any);

onMounted(() => {
  fetchHtmlContent();
})
async function fetchHtmlContent() {
  try {
    const response = await axios.get('https://s3.remagan.com/pro.remagan.uploads/static/android_policy.html');
    const parser = new DOMParser()
    policyContent.value.innerHTML = parser.parseFromString(response.data, 'text/html').body.innerHTML;
  } catch (error) {
    console.error('Error fetching HTML content:', error)
  }
}
function close() {
  router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
</script>