<template>
    <div class="public-container" v-on:scroll="listOrderScroll(tabIndex != 'all' ? tabIndex : null)">
        <div class='my-shop-order-container'>
            <SubHeaderV2Component :title="$t('AppRouteTitle.ManageOrdersComponent')">
                <template v-slot:header_middle>
                    <div class="order-manage-shop-name">
                        <h3>{{ $t('AppRouteTitle.ManageOrdersComponent') }}</h3>
                        <span>{{ currentShopData?.name }}</span>
                    </div>
                </template>
                <template v-slot:header_right>
                    <button :title="t('ManageOrdersComponent.chon_cua_hang')" v-if="listShopManaged?.length" v-on:click="() => {
                        if (listShopManaged?.length > 0) showSelectShopModal = true
                    }">
                        <Icon name="mdi:shop-settings-outline"></Icon>
                    </button>
                    <button class="filter-button" :class="{ 'active': checkFilterActive() }" v-on:click="async () => {
                        showFilterMenu = !showFilterMenu
                    }">
                        <Icon name="ph:funnel" />
                    </button>
                    <button v-on:click="async () => {
                        refreshListOrder()
                    }">
                        <Icon name="lets-icons:refresh" />
                    </button>
                </template>
            </SubHeaderV2Component>
            <div class="sticky-header" v-if="currentShopData?.id">
                <div class="search-bar-container">
                    <div class='h-stack search-bar'>
                        <Icon name="ion:search" size="20" v-show="!searchOrderLoading" />
                        <Icon name="eos-icons:loading" size="20" v-show="searchOrderLoading" />

                        <input type="search" :value="search_text"
                        :maxlength="appConst.max_text_short"
                            :placeholder="$t('ManageOrdersComponent.tim_theo_ma_don_hang')" v-on:input="async ($event: any) => {
                                search_text = $event.target.value;
                                searchOrders();
                            }" />
                    </div>
                </div>

                <v-tabs v-model="tabIndex" hide-slider class="orders-tab">
                    <v-tab class="order-tab-title" :class="tabIndex == 'all' ? 'active' : ''" value="all" key="all"
                        v-on:click="async () => {
                            tabIndex = 'all';
                            router.replace({
                                ...route,
                                hash: ''
                            });
                        }">
                        <div class="tab-title">
                            {{ $t('ManageOrdersComponent.tat_ca') }}
                            <!-- <em>{{ getStatusCount() }}</em> -->
                        </div>
                    </v-tab>

                    <v-tab v-for="(key, indexTab) in Object.keys(appConst.order_status)" class="order-tab-title"
                        v-on:click="async () => {
                            tabIndex = key;
                            router.replace({
                                ...route,
                                hash: '#' + key
                            });
                        }" :class="tabIndex == key ? 'active' : ''" :value="key" :key="key" :id="'tab_' + key">
                        <div class="tab-title">
                            {{ $t('ManageOrdersComponent.' + appConst.order_status[key].nameKey) }}
                            <!-- <em>{{ getStatusCount(appConst.order_status[key].value) }}</em> -->
                        </div>
                    </v-tab>

                </v-tabs>
            </div>

            <div class='v-stack my-shop-order-view' v-if="currentShopData?.id || isRefreshing">
                <client-only>
                    <v-window v-model="tabIndex" class="tab-content-container">
                        <v-window-item value="all" id="tab_content_all" :key="'tab_content_all'">
                            <div v-for="(itemOrder, indexSelected) in listOrder['all'] " :key="'all_' + itemOrder.id"
                                class='v-stack item-order' v-if="listOrder['all'] && listOrder['all'].length > 0">
                                <nuxt-link :to="{
                                    path: props.mode != 'agent'
                                        ? appRoute.MyShopOrderDetailComponent.replace(':id', itemOrder.id) :
                                        appRoute.AgentOrderDetailComponent.replace(':shop_id', currentShopData?.slug ?? currentShopData?.id).replaceAll(':id', itemOrder.id)
                                }" class='order-button-container'>
                                    <div class='v-stack'>
                                        <div class='h-stack'>
                                            <span class='customer-name'>
                                                {{ itemOrder.customer_name }}
                                            </span>
                                            <span class="is-new"
                                                v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
                                                {{ $t('ManageOrdersComponent.moi') }}
                                            </span>
                                            <span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
                                                ? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
                                                : ' taken')">
                                                {{
                                                    $t('ManageOrdersComponent.' +
                                                        appConst.order_status[
                                                            parseInt(itemOrder.status.toString()) != 3
                                                                ? Object.keys(appConst.order_status).find(key =>
                                                                    appConst.order_status[key].value ==
                                                                    parseInt(itemOrder.status)) as string
                                                                : "taken"
                                                        ].nameKey
                                                    )
                                                }}
                                            </span>
                                        </div>
                                        <div class='h-stack order-detail-advanced'>
                                            <span class='ordered-at'>
                                                {{
                                                    moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
                                                }}
                                            </span>
                                            <span class='short-code'>
                                                [{{ itemOrder.short_code }}]
                                            </span>
                                        </div>
                                        <div class='h-stack'>
                                            <span class="customer-address">
                                                {{ itemOrder.address }}
                                                <nuxt-link :to="directionToCustomer(itemOrder)" v-on:click="($event: Event) => {
                                                    $event.stopPropagation()
                                                }" target="_blank">
                                                    ({{ $t('ManageOrdersComponent.chi_duong') }})
                                                </nuxt-link>
                                            </span>
                                            <span class='customer-phone'>
                                                {{ itemOrder.customer_phone }}
                                            </span>
                                        </div>
                                        <div class='h-stack'>
                                            <span class='title'>{{ $t('ManageOrdersComponent.tong_cong') }}</span>
                                            <span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
                                                currentShopData?.currency)
                                                }}</span>
                                        </div>
                                        <div class="h-stack">
                                            <span class='title delivery'>{{
                                                $t('ManageOrdersComponent.thong_tin_giao_hang') }}</span>
                                            <div class="delivery-info" v-if="itemOrder.delivery_type">
                                                {{ $t('ManageOrdersComponent.khach_hang_tu_toi_lay') }}
                                            </div>
                                            <div class="delivery-info" v-else-if="itemOrder.delivery">
                                                <div class="driver-info" v-if="itemOrder.delivery?.driver_id">
                                                    <img :src="itemOrder?.delivery?.driver?.profile_picture
                                                        ? ((appConst.provider_img_domain.some(e => itemOrder?.delivery?.driver?.profile_picture?.includes(e))) ? itemOrder?.delivery?.driver?.profile_picture : (domainImage + itemOrder?.delivery?.driver?.profile_picture))
                                                        : non_avatar" alt="" />
                                                    <span
                                                        v-html="$t(`ManageOrdersComponent.trang_thai_giao_hang_${itemOrder.delivery.status}`, {
                                                            driver_name: 'driver_name'
                                                        }).replaceAll('driver_name', `<span class='total'>${itemOrder.delivery?.driver?.name}</span>`)"></span>
                                                </div>
                                                <div v-else class="none">
                                                    {{ $t('ManageOrdersComponent.chua_chon_shipper') }}
                                                </div>
                                            </div>
                                            <div class="delivery-partner-info" v-else-if="itemOrder?.delivery_partner">
                                                <img class="partner-logo"
                                                    :src="itemOrder?.delivery_partner?.information?.logo"
                                                    v-if="itemOrder?.delivery_partner?.information?.logo"
                                                    :alt="`logo ${itemOrder?.delivery_partner?.name}`">
                                                <span class="partner-name" v-else>
                                                    {{ itemOrder?.delivery_partner?.name }}
                                                </span>
                                                <div class="none"
                                                    v-if="!itemOrder.delivery && itemOrder?.delivery_partner?.name.toLowerCase().includes('remagan')">
                                                    - {{ $t('ManageOrdersComponent.chua_tao') }}
                                                </div>
                                            </div>
                                            <div v-else class="delivery-info">
                                                <div class="none">
                                                    {{ $t('ManageOrdersComponent.chua_tao') }}
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </nuxt-link>
                                <div class='h-stack order-action'
                                    v-if="parseInt(itemOrder.status.toString()) <= appConst.order_status.waiting.value">
                                    <button class='reject-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                            showModalConfirmCancelOrder = true
                                        }">
                                        <span v-if="!itemOrder.deleting">
                                            {{ $t('ManageOrdersComponent.huy_bo') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                    <button class='accept-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                            itemOrder.isUpdating = true;
                                            handleUpdateOrderStatus(itemOrder, appConst.order_status.confirmed.value)
                                        }">
                                        <span v-if="!itemOrder.isUpdating">
                                            {{ $t('ManageOrdersComponent.xac_nhan') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                </div>
                                <div v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                    class='h-stack order-action'>
                                    <button class='reject-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                            showModalConfirmCancelOrder = true;
                                        }">
                                        <span v-if="!itemOrder.deleting">
                                            {{ $t('ManageOrdersComponent.huy_bo') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                    <button class='accept-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                            itemOrder.isUpdating = true;
                                            handleUpdateOrderStatus(itemOrder, appConst.order_status.taken.value);
                                        }">
                                        <span v-if="!itemOrder.isUpdating">
                                            {{ $t('ManageOrdersComponent.da_giao') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                </div>

                            </div>
                            <div class='v-stack empty-list' v-else>
                                <img loading="lazy" :src="list_empty" :placeholder="list_empty"
                                    :alt="$t('ManageOrdersComponent.danh_sach_trong')" />
                                <span>
                                    {{ $t('ManageOrdersComponent.khong_tim_thay_don_hang_nao') }}
                                </span>
                                <span class='refresh' v-on:click="() => {
                                    handleGetListOrder()
                                }
                                    ">
                                    {{ $t('ManageOrdersComponent.lam_moi') }}
                                </span>
                            </div>
                            <span class='load-more' v-if="loadMore || isRefreshing">
                                {{ $t('ManageOrdersComponent.dang_tai') }}
                            </span>
                            <div id="last_of_list_all"></div>
                        </v-window-item>


                        <v-window-item :id="'tab_content_' + key" :key="'tab_content_' + key"
                            v-for="(key, indexTab) in Object.keys(appConst.order_status)" :value="key">
                            <div v-for="(itemOrder, indexSelected) in listOrder[key] " :key="key + '_' + itemOrder.id"
                                class='v-stack item-order' v-if="listOrder[key] && listOrder[key].length > 0">
                                <nuxt-link :to="{
                                    path: props.mode != 'agent'
                                        ? appRoute.MyShopOrderDetailComponent.replace(':id', itemOrder.id) :
                                        appRoute.AgentOrderDetailComponent.replace(':shop_id', currentShopData?.slug ?? currentShopData?.id).replaceAll(':id', itemOrder.id)
                                }" class='order-button-container'>
                                    <div class='v-stack'>
                                        <div class='h-stack'>
                                            <span class='customer-name'>
                                                {{ itemOrder.customer_name }}
                                            </span>
                                            <span class="is-new"
                                                v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
                                                {{ $t('ManageOrdersComponent.moi') }}
                                            </span>
                                            <span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
                                                ? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
                                                : ' taken')">
                                                {{
                                                    $t('ManageOrdersComponent.' +
                                                        appConst.order_status[
                                                            parseInt(itemOrder.status.toString()) != 3
                                                                ? Object.keys(appConst.order_status).find(key =>
                                                                    appConst.order_status[key].value ==
                                                                    parseInt(itemOrder.status)) as string
                                                                : "taken"
                                                        ].nameKey
                                                    )
                                                }}
                                            </span>
                                        </div>
                                        <div class='h-stack order-detail-advanced'>
                                            <span class='ordered-at'>
                                                {{
                                                    moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
                                                }}
                                            </span>
                                            <span class='short-code'>
                                                [{{ itemOrder.short_code }}]
                                            </span>
                                        </div>
                                        <div class='h-stack'>
                                            <span class="customer-address">
                                                {{ itemOrder.address }}
                                                <nuxt-link class="direction" :to="directionToCustomer(itemOrder)"
                                                    v-on:click="($event: Event) => {
                                                        $event.stopPropagation()
                                                    }" target="_blank">
                                                    ({{ $t('ManageOrdersComponent.chi_duong') }})
                                                </nuxt-link>
                                            </span>
                                            <span class='customer-phone'>
                                                {{ itemOrder.customer_phone }}
                                            </span>
                                        </div>

                                        <div class='h-stack'>
                                            <span class='title'>{{ $t('ManageOrdersComponent.tong_cong') }}</span>
                                            <span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
                                                currentShopData.currency)
                                                }}</span>
                                        </div>
                                        <div class="h-stack">
                                            <span class='title delivery'>{{
                                                $t('ManageOrdersComponent.thong_tin_giao_hang') }}</span>
                                            <div class="delivery-info" v-if="itemOrder.delivery_type">
                                                {{ $t('ManageOrdersComponent.khach_hang_tu_toi_lay') }}
                                            </div>
                                            <div class="delivery-info" v-else-if="itemOrder.delivery">
                                                <div class="driver-info" v-if="itemOrder.delivery?.driver_id">
                                                    <img :src="itemOrder?.delivery?.driver?.profile_picture
                                                        ? ((appConst.provider_img_domain.some(e => itemOrder?.delivery?.driver?.profile_picture?.includes(e))) ? itemOrder?.delivery?.driver?.profile_picture : (domainImage + itemOrder?.delivery?.driver?.profile_picture))
                                                        : non_avatar" alt="" />
                                                    <span
                                                        v-html="$t(`ManageOrdersComponent.trang_thai_giao_hang_${itemOrder.delivery.status}`, {
                                                            driver_name: 'driver_name'
                                                        }).replaceAll('driver_name', `<span class='total'>${itemOrder.delivery?.driver?.name}</span>`)"></span>
                                                </div>
                                                <div v-else class="none">
                                                    {{ $t('ManageOrdersComponent.chua_chon_shipper') }}
                                                </div>
                                            </div>
                                            <div class="delivery-partner-info" v-else-if="itemOrder?.delivery_partner">
                                                <img class="partner-logo"
                                                    :src="itemOrder?.delivery_partner?.information?.logo"
                                                    v-if="itemOrder?.delivery_partner?.information?.logo"
                                                    :alt="`logo ${itemOrder?.delivery_partner?.name}`">
                                                <span class="partner-name" v-else>
                                                    {{ itemOrder?.delivery_partner?.name }}
                                                </span>
                                                <div class="none"
                                                    v-if="!itemOrder.delivery && itemOrder?.delivery_partner?.name.toLowerCase().includes('remagan')">
                                                    - {{ $t('ManageOrdersComponent.chua_tao') }}
                                                </div>
                                            </div>
                                            <div v-else class="delivery-info">
                                                <div class="none">
                                                    {{ $t('ManageOrdersComponent.chua_tao') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </nuxt-link>
                                <div class='h-stack order-action'
                                    v-if="parseInt(itemOrder.status.toString()) == appConst.order_status.waiting.value">
                                    <button class='reject-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                            showModalConfirmCancelOrder = true
                                        }">
                                        <span v-if="!itemOrder.deleting">
                                            {{ $t('ManageOrdersComponent.huy_bo') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                    <button class='accept-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            itemOrder.isUpdating = true;
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                            handleUpdateOrderStatus(itemOrder, appConst.order_status.confirmed.value)
                                        }">
                                        <span v-if="!itemOrder.isUpdating">
                                            {{ $t('ManageOrdersComponent.xac_nhan') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                </div>
                                <div v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                    class='h-stack order-action'>
                                    <button class='reject-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                            showModalConfirmCancelOrder = true;
                                        }">
                                        <span v-if="!itemOrder.deleting">
                                            {{ $t('ManageOrdersComponent.huy_bo') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                    <button class='accept-button'
                                        :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                            selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                            itemOrder.isUpdating = true;
                                            handleUpdateOrderStatus(itemOrder, appConst.order_status.taken.value);
                                        }">
                                        <span v-if="!itemOrder.isUpdating">
                                            {{ $t('ManageOrdersComponent.da_giao') }}
                                        </span>
                                        <Icon name="eos-icons:loading" size="20" v-else />
                                    </button>
                                </div>
                            </div>
                            <div class='v-stack empty-list' v-else>
                                <img loading="lazy" :src="list_empty" :placeholder="list_empty"
                                    :alt="$t('ManageOrdersComponent.danh_sach_trong')" />
                                <span>
                                    {{ $t('ManageOrdersComponent.khong_tim_thay_don_hang_nao') }}
                                </span>
                                <span class='refresh' v-on:click="() => {
                                    handleGetListOrder()
                                }
                                    ">
                                    {{ $t('ManageOrdersComponent.lam_moi') }}
                                </span>
                            </div>
                            <span class='load-more' v-if="loadMore || isRefreshing">
                                {{ $t('ManageOrdersComponent.dang_tai') }}
                            </span>
                            <div :id="'last_of_list_' + key"></div>
                        </v-window-item>

                    </v-window>
                </client-only>



            </div>
            <NoneMyShopComponent v-else-if="isRefreshing == false" :show_header="false" :mode="props.mode"
                :message="props.mode != 'agent' ? $t('ManageOrdersComponent.chua_dang_ky_cua_hang') : $t('ManageOrdersComponent.cua_hang_khong_ton_tai')">
            </NoneMyShopComponent>
            <!-- <div class="v-stack my-shop-order-view" v-else-if="isRefreshing == false">
                <div class='register-shop-content-button'>
                    <img loading="lazy" :src='none_shop' :placeholder="none_shop"
                        :alt="$t('ManageOrdersComponent.chua_dang_ky_cua_hang')" />
                    <span>
                        {{ $t('ManageOrdersComponent.cua_hang_khong_ton_tai') }}
                    </span>
                    <nuxt-link :to="appRoute.RegisterShopComponent">
                        <button>
                            {{ $t('ManageOrdersComponent.tao_moi') }}
                        </button>
                    </nuxt-link>
                </div>
            </div> -->

            <VueFinalModal class="my-modal-container" content-class="v-stack confirm-cancel-order-modal" :overlay-behavior="'persist'"
                v-model="showModalConfirmCancelOrder" v-on:closed="() => { showModalConfirmCancelOrder = false }"
                contentTransition="vfm-slide-up">
                <div class='v-stack cancel-order-content'>
                    <span class='cancel-order-title'>
                        {{ $t('ManageOrdersComponent.tu_choi_nhan_don_hang') }}
                    </span>
                    <span class='cancel-order-message'>
                        {{ $t('ManageOrdersComponent.huy_don_hang') }}
                        <span class='order-code'>
                            {{
                                selectedOrder && selectedOrder.short_code
                                    ? selectedOrder.short_code
                                    : ""
                            }}
                        </span>
                    </span>
                </div>
                <div class='h-stack confirm-modal-buttons'>
                    <button class='reject-button' :disabled="isUpdating" v-on:click="() => {
                        showModalConfirmCancelOrder = false;
                        selectedOrder
                    }">
                        {{ $t('ManageOrdersComponent.khong') }}
                    </button>
                    <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
                        handleUpdateOrderStatus(selectedOrder, appConst.order_status.cancel.value);
                        showModalConfirmCancelOrder = false;
                        selectedOrder = null;
                    }">
                        {{ $t('ManageOrdersComponent.co') }}
                    </button>
                </div>
            </VueFinalModal>
            <button class="create-order-button" v-if="currentShopData?.id" v-on:click="() => {
                if (props.mode != 'agent') {
                    router.push(appRoute.MyShopCreateOrderComponent)
                }
                else {
                    router.push(appRoute.AgentCreateOrderComponent.replaceAll(':shop_id', currentShopData?.slug ?? currentShopData?.id))
                }
            }">
                <Icon name="ic:sharp-add"></Icon>
                {{ $t('ManageOrdersComponent.tao_don_hang') }}
            </button>
        </div>

        <v-overlay v-model="showFilterMenu" :z-index="100" :absolute="false" contained key="show_filter_menu"
            class="filter-menu-overlay-container" content-class='filter-menu-container' no-click-animation
            v-on:click:outside="() => {
                showFilterMenu = false;
                filterTimeTemp = filterTime;
            }">
            <div class="filter-menu-content">
                <div class="filter-title">
                    {{ $t('ManageOrdersComponent.loc_don_hang') }}
                </div>
                <div class="time-filter">
                    <div class="item-time-filter" :class="{ 'active': filterTimeTemp == item.value }"
                        v-for="item of dataFilterTime" v-on:click="() => { filterTimeTemp = item.value }">
                        <Icon name="ic:sharp-radio-button-checked" v-if="filterTimeTemp == item.value"></Icon>
                        <Icon name="ic:sharp-radio-button-unchecked" v-else></Icon>
                        <span>{{ $t('ManageOrdersComponent.' + item.nameKey) }}</span>
                    </div>
                    <div class="h-stack select-date" v-if="filterTimeTemp == 5">
                        <input name="start-day" class="input-custom" id="start-day" type="date" :value="start_date"
                            placeholder="{{ $t('ManageOrdersComponent.chua_cung_cap') }}"
                            :max="moment(end_date ? end_date : new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                                start_date = event.target.value;
                            }" />
                        <Icon name="material-symbols:arrow-forward-rounded"></Icon>
                        <input name="end-day" class="input-custom" id="end-day" type="date" :value="end_date"
                            placeholder="{{ $t('ManageOrdersComponent.chua_cung_cap') }}"
                            :min="moment(start_date ? start_date : new Date()).format('YYYY-MM-DD')"
                            :max="moment(new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                                end_date = event.target.value;
                            }" />
                    </div>
                    <div class="h-stack footer">
                        <button class="close" v-on:click="() => {
                            showFilterMenu = false;
                            filterTimeTemp = filterTime;
                        }">
                            {{ $t('ManageOrdersComponent.dong') }}
                        </button>
                        <button class="apply" v-on:click="() => {
                            showFilterMenu = false;
                            if (filterTimeTemp != filterTime || filterTimeTemp == 5) {
                                filterTime = filterTimeTemp;
                                switch (filterTime as any) {
                                    case 0: case null: {
                                        start_date = null;
                                        end_date = null;
                                        break;
                                    }
                                    case 1: {
                                        start_date = moment(new Date()).format(appConst.formatDate.toSave);
                                        end_date = moment(new Date()).format(appConst.formatDate.toSave);
                                        break;
                                    }
                                    case 2: {
                                        start_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
                                        end_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
                                        break;
                                    }
                                    case 3: {
                                        start_date = moment().startOf('month').format(appConst.formatDate.toSave);
                                        end_date = moment().endOf('month').format(appConst.formatDate.toSave);
                                        break;
                                    }
                                    case 4: {
                                        start_date = moment().add(-1, 'months').startOf('month').format(appConst.formatDate.toSave);
                                        end_date = moment().add(-1, 'months').endOf('month').format(appConst.formatDate.toSave);
                                        break;
                                    }
                                    case 5: {
                                        start_date = start_date ? moment(start_date).format(appConst.formatDate.toSave) : null;
                                        end_date = end_date ? moment(end_date).format(appConst.formatDate.toSave) : null;
                                        break;
                                    }
                                    default: break;
                                }
                            }
                            handleGetListOrder()
                        }">
                            {{ $t('ManageOrdersComponent.ap_dung') }}
                        </button>
                    </div>
                </div>
            </div>
        </v-overlay>

        <CustomSelectComponent v-if="showSelectShopModal" :_key="'select_shop_chat_manage'"
            :list_item="listShopManaged ?? []" :field_value="'id'" :field_title="'name'" :multiple="false"
            :title="$t('ManageOrdersComponent.chon_cua_hang')" :class="'my-custom-select custom-shop-select'"
            :searchable="false" :model_value="currentShopData?.id ?? myShopData?.id ?? shop_id" v-on:close="() => {
                showSelectShopModal = false
            }" v-on:model:update="(e) => {
                setSelectedShop(e)
            }">
            <template v-slot:title_icon_left>
                <Icon name="solar:hamburger-menu-linear"></Icon>
            </template>
            <template v-slot:render_item_option="{ item }">
                <div class="custom-shop-select-option">
                    <AvatarComponent class="select-shop-logo" :imgTitle="item.name" :imgStyle="item.logo?.style"
                        :imgSrc="item.logo
                            ? (domainImage + item.logo.path)
                            : item.banner
                                ? (domainImage + item.banner.path)
                                : ''
                            " :width="40" :height="40" />
                    <span>{{ item.name }}</span>
                </div>

            </template>
        </CustomSelectComponent>
    </div>

</template>
<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    shop_id: null,
    mode: null
})
var nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.ManageOrdersComponent')
});
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';

import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg"
import non_avatar from '~/assets/image/non-avatar.jpg';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
import { AgentService } from '~/services/agentService/agentService';


var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var shopService = new ShopService();
var mqttSerivce = new MqttService();
var agentService = new AgentService();
var searchOrderTimeout: any;

var loadMoreTimeOut: any;
var searchTimeout: any;
var dataFilterTime = [
    {
        value: 0 || null,
        name: "Toàn thời gian",
        nameKey: "toan_thoi_gian",
    },
    {
        value: 1,
        name: "Hôm nay",
        nameKey: "hom_nay",
    },
    {
        value: 2,
        name: "Hôm qua",
        nameKey: "hom_qua",
    },
    {
        value: 3,
        name: "Tháng này",
        nameKey: "thang_nay",
    },
    {
        value: 4,
        name: "Tháng trước",
        nameKey: "thang_truoc",
    },
    {
        value: 5,
        name: "Thời gian khác",
        nameKey: "thoi_gian_khac",
    },
];

var refreshTimeout: any;

var showModalConfirmCancelOrder = ref(false);
var selectedOrder = ref(null as any);
var search_text = ref("")
var tabIndex = ref(route.hash?.length ? route.hash.replaceAll("#", '') : 'all');
var searchOrderLoading = ref(false);
var listOrder = ref({} as any);
var countOrderByStatus = ref([] as any[]);
var isRefreshing = ref(true);
var showFilterMenu = ref(false);
var filterTime = ref(null);
var filterTimeTemp = ref(null as any);
var start_date = ref(null as any);
var end_date = ref(null as any);
var loadMore = ref(false);
var isUpdating = ref(false);
var searchFocus = ref(false);

var shop_id = ref<any>(route.params?.shop_id ?? null);
var myShopData = useState<any>('my_shop', () => { });
var listShopManaged = useState<any>('list_shop_managed', () => { return [] });
var currentShopData = ref<any>(null);
var showSelectShopModal = ref(false);

watch(
    () => [route.params?.id, route.hash],
    async (newQuery, oldQuery) => {
        shop_id.value = route.params?.id ?? null;
        tabIndex.value = route.hash.length ? route.hash.replaceAll('#', '') : 'all'
    },
    { deep: true }
);

watch(
    () => [shop_id.value],
    async (newQuery, oldQuery) => {
        // await handleSetCurrentShop();
        // handleGetListOrder('shop id changes');
    },
    { deep: true }
);

watch(
    () => [tabIndex.value],
    async (newQuery, oldQuery) => {
        handleGetListOrder();
    },
    { deep: true }
);

onMounted(async () => {
    nuxtApp.$listen('refresh_order_manage', () => {
        handleGetListOrder();
    })
    await init().then(async () => {
        shop_id.value = route.params?.id ?? null;
        // tabIndex.value = route.hash?.length ? route.hash.replaceAll("#", '') : 'all';
        await handleSetCurrentShop()
        handleGetListOrder();

    });

})

onUnmounted(() => {
    if (myShopData.value) {
        mqttSerivce.unsubscribe(appConst.mqtt_topic.shop.replaceAll(':shop_id', myShopData.value?.id));
    }
    listShopManaged.value.forEach((element: any) => {
        mqttSerivce.unsubscribe(appConst.mqtt_topic.shop.replaceAll(':shop_id', element?.id));
    });
})

async function init() {
    return Promise.all([
        await getMyShop(),
        await getListShop()
    ])

}
function getMyShop() {
    return new Promise((resolve) => {
        if (myShopData.value?.id) {
            resolve(myShopData.value);
        }
        else {
            shopService.myShop().then(async res => {
                if (res.status && res.status == HttpStatusCode.Ok && res.body?.data) {
                    myShopData.value = JSON.parse(JSON.stringify(res.body.data));
                    resolve(myShopData.value)
                }
                else {
                    myShopData.value = null;
                    resolve(null);
                }
            })
        }
    })

}

function getListShop() {
    return new Promise((resolve) => {
        if (listShopManaged.value.length) {
            resolve(listShopManaged.value)
        }
        else {
            agentService.listShopManage('', 1000, 0).then(res => {
                if (res.status == HttpStatusCode.Ok) {
                    listShopManaged.value = JSON.parse(JSON.stringify(res.body.data));
                    // listShopManaged.value.forEach((element:any) => {
                    //     mqttSerivce.subscribe(appConst.mqtt_topic.shop.replaceAll(':shop_id', element?.id), (mes) => {
                    //         if(mes?.url == element?.id){
                    //             nuxtApp.$emit('refresh_order_manage');
                    //         }
                    //     })
                    // });
                    if (myShopData.value?.id) {
                        listShopManaged.value = [
                            myShopData.value,
                            ...JSON.parse(JSON.stringify(res.body.data))
                        ];
                    }

                }
                else {
                    listShopManaged.value = [];
                }
                resolve(listShopManaged.value);
            })
        }

    })

}

function close() {
    router.back();
}

function getListOrder(status?: number | null) {
    isRefreshing.value = true;
    if (myShopData.value?.id) {
        orderService.orderByShopId(myShopData.value?.id, 0, 20, status, search_text.value, start_date.value, end_date.value).then(async res => {

            if (res.status && res.status == HttpStatusCode.Ok) {
                let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
                statusName = statusName ? statusName : "all";
                document.getElementById('tab_content_' + statusName)?.scrollTo({
                    behavior: 'smooth',
                    top: 0
                });
                let list = JSON.parse(JSON.stringify(listOrder.value));

                list[statusName] = JSON.parse(JSON.stringify(res.body.data.result));
                let index = countOrderByStatus.value.findIndex(function (e: any) {
                    return e.status == status;
                })
                if (index != -1) {
                    countOrderByStatus.value[index].count = res.body.data.count
                }
                else {
                    countOrderByStatus.value.push({
                        status: status,
                        count: res.body.data.count
                    })
                }
                listOrder.value = JSON.parse(JSON.stringify(list));
            }
            isRefreshing.value = false;
        })
    }
    else {
        isRefreshing.value = false;
    }
}

function getListOrderAgent(status?: number | null) {
    isRefreshing.value = true;
    if (currentShopData.value?.id) {
        agentService.orderByAgentId(currentShopData.value?.id, 0, 20, status, search_text.value, start_date.value, end_date.value).then(async res => {
            if (res.status && res.status == HttpStatusCode.Ok) {
                let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
                statusName = statusName ? statusName : "all";
                document.getElementById('tab_content_' + statusName)?.scrollTo({
                    behavior: 'smooth',
                    top: 0
                });
                let list = JSON.parse(JSON.stringify(listOrder.value));

                list[statusName] = JSON.parse(JSON.stringify(res.body.data.result));
                let index = countOrderByStatus.value.findIndex(function (e: any) {
                    return e.status == status;
                })
                if (index != -1) {
                    countOrderByStatus.value[index].count = res.body.data.count
                }
                else {
                    countOrderByStatus.value.push({
                        status: status,
                        count: res.body.data.count
                    })
                }
                listOrder.value = JSON.parse(JSON.stringify(list));
            }
            else if (res.status == HttpStatusCode.Unauthorized) {
                // router.push(appRoute.LoginComponent)
            }
            isRefreshing.value = false;
        })
    }
    else{
        isRefreshing.value = false;
    }
}

function handleGetListOrder(from?: any) {
    if (props.mode != 'agent') {
        getListOrder(tabIndex.value != 'all' ? appConst.order_status[tabIndex.value].value : null);
    }
    else {
        getListOrderAgent(tabIndex.value != 'all' ? appConst.order_status[tabIndex.value].value : null)
    }
}

function handleSetCurrentShop() {
    return new Promise((resolve) => {
        if (props.mode != 'agent') {
            if (myShopData.value?.id) {
                currentShopData.value = JSON.parse(JSON.stringify(myShopData.value));
            }
            else {
                if (listShopManaged.value[0]) {
                    // router.replace({
                    //     ...route,
                    //     query: {
                    //         shop_id: listShopManaged.value[0].id
                    //     }
                    // })
                    router.push({
                        path: appRoute.ManageOrdersComponent.replaceAll(':shop_id', listShopManaged.value[0].id),
                        hash: route.hash
                    })
                }
                else {
                    currentShopData.value = null
                }

            }
            resolve(currentShopData.value);
        }
        else {
            let indexSelected = listShopManaged.value.findIndex(function (e: any) {
                return e.id == shop_id.value || e.slug == shop_id.value;
            });
            if (indexSelected != -1) {
                currentShopData.value = JSON.parse(JSON.stringify(listShopManaged.value[indexSelected]));
            }
            else {
                currentShopData.value = null;
            }
            resolve(currentShopData.value);
        }
    })

}

function handleGetMoreInAList() {
    if (props.mode != 'agent') {
        getMoreOrderInAList(listOrder.value[tabIndex.value ? tabIndex.value : "all"].length, 20, tabIndex.value != 'all' ? appConst.order_status[tabIndex.value].value : null)
    }
    else {
        getMoreOrderInAListAgent(listOrder.value[tabIndex.value ? tabIndex.value : "all"].length, 20, tabIndex.value != 'all' ? appConst.order_status[tabIndex.value].value : null)
    }
}
function getMoreOrderInAList(offset = 0, limit = 20, status?: number | null) {
    let index = countOrderByStatus.value.findIndex(function (e: any) {
        return e.status == (status ? status : undefined);
    });
    if (offset < countOrderByStatus.value[index].count) {
        clearTimeout(loadMoreTimeOut);

        loadMoreTimeOut = setTimeout(() => {
            loadMore.value = true;
            let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
            orderService.orderByShopId(currentShopData.value?.id, offset, limit, status).then(async res => {

                if (res.status && res.status == HttpStatusCode.Ok) {

                    statusName = statusName ? statusName : "all";
                    let list = JSON.parse(JSON.stringify(listOrder.value));
                    list[statusName] = [...list[statusName], ...res.body.data.result];

                    loadMore.value = false;
                    listOrder.value = JSON.parse(JSON.stringify(list));
                }

            })

        }, 500)
    }

}

function getMoreOrderInAListAgent(offset = 0, limit = 20, status?: number | null) {
    let index = countOrderByStatus.value.findIndex(function (e: any) {
        return e.status == (status ? status : undefined);
    });

    if (offset < countOrderByStatus.value[index].count) {
        clearTimeout(loadMoreTimeOut);

        loadMoreTimeOut = setTimeout(() => {
            loadMore.value = true;
            let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
            agentService.orderByAgentId(currentShopData.value?.id, offset, limit, status).then(async res => {

                if (res.status && res.status == HttpStatusCode.Ok) {

                    statusName = statusName ? statusName : "all";
                    let list = JSON.parse(JSON.stringify(listOrder.value));
                    list[statusName] = [...list[statusName], ...res.body.data.result];

                    loadMore.value = false;
                    listOrder.value = JSON.parse(JSON.stringify(list));
                }

            })

        }, 500)
    }

}


// getCountOrderByStatus() {
//     orderService.countOrderByStatus(shopData.id).then(async res => {
//         await setState({
//             countOrderByStatus: res.body.data
//         })
//     })
// }

function getStatusCount(status?: number | null) {
    let index = countOrderByStatus.value.findIndex(function (e: any) {
        return e.status == status
    })

    if (index != -1) {
        if (countOrderByStatus.value[index].count < 100) return countOrderByStatus.value[index].count;
        if (countOrderByStatus.value[index].count >= 100) return '99+';
    }
    return 0;
}

var isCloseToBottom = (layoutMeasurement: any, contentOffset: any, contentSize: any) => {
    const paddingToBottom = 20;
    return layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom;
};

function handleUpdateOrderStatus(orderItem: any, newStatus: number) {
    if (props.mode != 'agent') {
        updateStatusOrder(orderItem, newStatus);
    }
    else {
        updateStatusOrderAgent(orderItem, newStatus);
    }
}
async function updateStatusOrder(orderItem: any, newStatus: number) {
    isUpdating.value = true;

    let order = JSON.parse(JSON.stringify(orderItem));
    order.status = newStatus != 3 ? newStatus : 4;
    order.items = [];
    await orderItem.items.forEach((e: any) => {
        order.items.push(e.pivot)
    })

    orderService.updateOrder(order).then(res => {
        isUpdating.value = false;
        orderItem.isUpdating = false;
        selectedOrder.value = null
        handleGetListOrder();
    }).catch(() => {
        isUpdating.value = false;
        orderItem.isUpdating = false;
        selectedOrder.value = null
    })
}

async function updateStatusOrderAgent(orderItem: any, newStatus: number) {
    isUpdating.value = true;

    let order = JSON.parse(JSON.stringify(orderItem));
    order.status = newStatus != 3 ? newStatus : 4;
    order.items = [];
    await orderItem.items.forEach((e: any) => {
        order.items.push(e.pivot)
    })

    agentService.agentUpdateOrder(order).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;
            handleGetListOrder()
        }
        else if (res.status == HttpStatusCode.BadRequest) {
            toast.warn(t('AgentOrderManageComponent.ban_khong_co_quyen_truy_cap'));
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;
        }
        else {
            // toast.warn("Cập nhật thất bại. Vui lòng thử lại sau");
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;
        }
        init();
    }).catch(err => {
        // toast.error("Cập nhật thất bại. Vui lòng thử lại sau");
        isUpdating.value = false;
        orderItem.isUpdating = false;
        selectedOrder.value = null;
    })
}

function searchOrders() {
    searchFocus.value = true;
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        handleGetListOrder()
    }, 500)
}

function listOrderScroll(key: string | null = null) {
    let el = document.getElementById('last_of_list_' + (key ? key : 'all'))?.getBoundingClientRect().bottom;

    if (el && (el <= window.innerHeight + 300)) {
        handleGetMoreInAList();
    }
}
function refreshListOrder() {
    clearTimeout(refreshTimeout);
    refreshTimeout = setTimeout(() => {
        handleGetListOrder()
    }, 500);
}

function checkNewOrder(order: any) {
    var orderCreatedAt = moment(order.created_at).valueOf();
    var currentTime = moment().valueOf();
    let count = currentTime - orderCreatedAt;
    if (Math.round(count / (60 * 60 * 24)) < 60)
        return true;
    return false;
}

function callToBuyer(order: any) {
    window.open(`tel:${validPhone(order.customer_phone)}`)
}

function checkFilterActive() {
    if (filterTime.value || (start_date.value && end_date.value)) return true;
    return false;
}

function setSelectedShop(shop_id$: any) {
    if (shop_id$ != shop_id.value) {
        if (shop_id$ == myShopData.value?.id) {
            shop_id.value = shop_id$;
            router.push({
                hash: route.hash,
                path: appRoute.ManageOrdersComponent,
            })
            // router.replace({
            //     ...route,
            //     query: {
            //         shop_id: shop_id$
            //     },
            // })

        }
        else {
            let indexSelected = listShopManaged.value.findIndex(function (e: any) {
                return e.id == shop_id$;
            });
            if (indexSelected != -1) {
                // router.replace({
                //     ...route,
                //     query: {
                //         shop_id: shop_id$
                //     }
                // })
                router.push({
                    // path: appRoute.ManageOrdersComponent.replaceAll(':shop_id', shop_id$),
                    path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', listShopManaged.value?.[indexSelected].slug ?? listShopManaged.value?.[indexSelected].id),
                    hash: route.hash
                })
            }
        }
    }

}

function directionToCustomer(order: any) {
    // await getUserLocation();
    let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${order?.shops?.latitude},${order?.shops?.longitude}&destination=${order?.customer_latitude},${order?.customer_longitude}&trabelmode=bicycling`;
    return textToDirections ?? "";
}
</script>

<style lang="scss" src="./ManageOrdersStyles.scss"></style>