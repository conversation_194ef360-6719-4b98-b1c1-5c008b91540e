<template>
	<div class="show-product-container">
		<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					close();
				}">
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.ShopProductsComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
		<SubHeaderV2Component :title="$t('AppRouteTitle.ShopProductsComponent')">
		</SubHeaderV2Component>
		<div class='categories-container' v-if="shopData && shopData.id">
			<v-tabs hide-slider class="categories-tab" v-if="dataShopCategories && dataShopCategories.length">
				<v-tab v-for="(itemCategory, indexTab) in dataShopCategories" class="category-item-tab" v-on:click="async () => {
					let selected = checkCategoryFilter(itemCategory.id);
					if (selected) {
						filterCategory = null;
					}
					else {
						filterCategory = itemCategory.id
					}

					await getShopProduct(0, 20);
				}" :class="checkCategoryFilter(itemCategory.id) ? 'active' : ''" :value="itemCategory.id" :key="itemCategory.id"
					:id="'tab_' + itemCategory.id">
					<div class="tab-title">
						<span class='name'>
							{{ itemCategory.name }}
						</span>
					</div>
				</v-tab>
			</v-tabs>
			<!-- <button v-for="(itemCategory, index) in dataShopCategories" class='category-item-button'
				:class="(checkCategoryFilter(itemCategory.id) ? 'active' : '')" :key="itemCategory.id" v-on:click="async ($event) => {
					let selected = checkCategoryFilter(itemCategory.id);
					if (selected) {
						filterCategory = null;
					}
					else {
						filterCategory = itemCategory.id
					}

					await getShopProduct(0, 20);
				}">
				<span class='name'>
					{{ itemCategory.name }}

				</span>
			</button> -->
		</div>
		<div class="h-stack search-bar-container" v-if="shopData && shopData.id">
			<button class='search-button' :disabled=filterProductLoading>
				<Icon name="ion:search" size="20" v-show="!filterProductLoading" />
				<Icon name="eos-icons:loading" size="20" v-show="filterProductLoading" />
			</button>
			<input type="search" name='search-text' placeholder='Tìm sản phẩm...' autoComplete='off'
				class='search-input-container' :value="search_text" v-on:input="($event: any) => {
					search_text = $event.target.value;
					filterProductLoading = true;
					searchProduct()
				}" />
		</div>
		<div class="v-stack list-product-container" v-if="shopData && shopData.id" id="list_product_container"
			v-on:scroll="() => productContainerScroll()">

			<span class="list-product-amount">
				Hiển thị {{ listProduct ? listProduct.length : 0 }}/{{ countProducts ? countProducts : 0 }} sản phẩm
			</span>
			<span class="product-loading" v-if="isRefreshing">
				Đang tải...
			</span>
			<div class="v-stack none-list-product" v-if="!isRefreshing && (!listProduct || !listProduct.length)">
				<img loading="lazy" :src='list_empty' :placeholder="list_empty" alt="Không có sản phẩm" />
				<span>
					Không tìm thấy sản phẩm phù hơp!
				</span>
			</div>

			<div class="list-product-show" v-if="!isRefreshing && listProduct && listProduct.length">

				<div class="product-item" v-for="(itemProduct, index) in listProduct" :key=itemProduct.id>
					<img loading="lazy" :src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ?
						(domainImage + itemProduct.profile_picture) : icon_for_product" :placeholder="icon_for_product"
						:alt="itemProduct.name" />
					<div class="v-stack product-detail">
						<span class="name">
							{{ itemProduct.name }}
						</span>
						<span class="price">
							<!-- {{ formatCurrency(itemProduct.price || 0, shopData.currency) }} -->

							{{
								(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
									formatCurrency(parseFloat(itemProduct.price_off), shopData.currency) :
									(parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ? 'Giá liên hệ' :
										formatCurrency(parseFloat(itemProduct.price), shopData.currency) }} <em class="off"
								v-if="(itemProduct.price_off != null && parseFloat(itemProduct.price_off) < parseFloat(itemProduct.price))">
								{{
									itemProduct.price != null ? formatCurrency(itemProduct.price != null ?
										parseFloat(itemProduct.price) : 0, shopData.currency) : 'Giá liên hệ'
								}}</em>
						</span>


						<button class="action" v-on:click="() => {
							showEditProductModal = true,
								selectedProduct = JSON.parse(JSON.stringify(itemProduct))
						}">
							Chi tiết
						</button>

					</div>
				</div>

			</div>
			<span class='loading-more' v-if="loadMore">
				Đang tải thêm...
			</span>
			<div id='last_of_list'></div>
		</div>
		<div class="access-denied" v-if="!(isRefreshing || (shopData && shopData.id))">
			<img loading="lazy" :src="none_shop" :placeholder="none_shop" alt="Từ chối quyền truy cập" />
			<span>
				Bạn chưa đăng ký cửa hàng
			</span>
		</div>
		<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'" content-class="my-modal-content-container v-stack edit-product-modal"
			v-model="showEditProductModal" v-on:closed="() => {
				showEditProductModal = false
			}" contentTransition="vfm-slide-up">
			<EditProductComponent :productData="JSON.parse(JSON.stringify(selectedProduct))" v-on:close="(event: any) => {
				showEditProductModal = false
				if (event) {
					getShopProduct();
				}

			}"></EditProductComponent>
		</VueFinalModal>
		<!-- <Modal class='create-private-product-modal' centered show={state.showEditProductModal}>
			<EditProductComponent productData={JSON.parse(JSON.stringify(state.selectedProduct))} close={}
				/>
		</Modal> -->
	</div>
</template>

<script lang="ts" setup>
import EditProductComponent from '../editProduct/EditProductComponent.vue'
import { VueFinalModal } from 'vue-final-modal';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import icon_for_product from '~/assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg"
import none_shop from "~/assets/image/none-shop.jpg"
import { HttpStatusCode } from 'axios';

const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.ShopProductsComponent')
});
var router = useRouter();
var route = useRoute();

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var categoryService = new CategoryService();
var searchProductTimeout: any;
var loadMoreTimeOut: any;

var shopData = ref(null as any);
var listProduct = ref([] as any[]);
var selectedProduct = ref({});
var dataShopCategories = ref([] as any[]);
var productSortBy = ref();
var countProducts = ref(0);
var isRefreshing = ref(true);
var loadMore = ref(false);
var filterProductLoading = ref(false);
var search_text = ref("");
var filterCategory = ref(null);
var showEditProductModal = ref(false)

onMounted(() => {
	getMyShop();
})
function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

function getMyShop() {
	isRefreshing.value = true;
	shopService.myShop().then(res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
			isRefreshing.value = false;
			shopData.value = res.body.data;
			getListCategory();
			getShopProduct()
		}
		else {
			isRefreshing.value = false;
			shopData.value = null;
		}
		return
	})
}

function getShopProduct(offset?: any, limit?: any) {
	loadMore.value = true;
	filterProductLoading.value = true;
	offset = offset != null ? offset : 0;

	limit = limit != null ? limit : 20;

	let categories: any[] = filterCategory.value ? [filterCategory.value] : [];

	shopService.searchProductsInShop(
		search_text.value,
		shopData.value.id,
		categories,
		limit,
		offset,
		productSortBy.value,
		null,
		true
	).then(res => {
		listProduct.value = JSON.parse(JSON.stringify(res.body.data.result));
		countProducts.value = res.body.data.count;
		document.getElementById('list_product_container')?.scrollTo({ top: 0, behavior: 'smooth' })
		isRefreshing.value = false;
		loadMore.value = false;
		filterProductLoading.value = false;
	}).catch(err => {
		filterProductLoading.value = false;
		isRefreshing.value = false;
		loadMore.value = false;
	})
}
function searchProduct() {
	clearTimeout(searchProductTimeout);
	searchProductTimeout = setTimeout(() => {
		getShopProduct(0, 20);
	}, 500)
}
function productContainerScroll() {
	let el = document.getElementById('last_of_list')?.getBoundingClientRect().bottom;
	if (el && el <= window.innerHeight + 10) { loadMoreProduct(); }
}
function loadMoreProduct() {
	clearTimeout(loadMoreTimeOut);


	if (listProduct.value.length < countProducts.value) {
		loadMore.value = true;
		loadMoreTimeOut = setTimeout(() => {
			let categories = filterCategory.value ? [filterCategory.value] : [];
			shopService.searchProductsInShop(
				search_text.value,
				shopData.value.id,
				categories,
				20,
				listProduct.value.length,
				productSortBy.value,
				null,
				true
			).then(res => {
				let list = [...listProduct.value, ...res.body.data.result];
				listProduct.value = list;
				loadMore.value = false;
			}).catch(err => {
				loadMore.value = false;
			})
		}, 500);

	}
	else {
		loadMore.value = false;
	}

}

function getListCategory() {
	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
	})
}

function checkCategoryFilter(id: any) {
	if (id) {
		return filterCategory.value == id ? true : false
	}
	return false
}
</script>

<style lang="scss" src="./ShopProductsStyles.scss"></style>