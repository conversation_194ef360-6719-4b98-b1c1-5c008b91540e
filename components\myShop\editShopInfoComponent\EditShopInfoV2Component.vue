<template>
  <div class="public-container">
    <div class="edit-shop-info-shop-container" v-if="shopData?.id">
      <SubHeaderV2Component class="edit-shop-info-shop-header">
        <template v-slot:header_middle>
          <h3>{{ $t('EditShopInfoComponent.chinh_sua', { name: shopData?.name }) }}</h3>
        </template>
      </SubHeaderV2Component>
      <div class="edit-shop-info-shop-content" v-if="shopData?.id">

        <div class="edit-shop-info-shop-content-container">
          <div class="v-stack shop-content">

            <div class="shop-banner-container">
              <!-- <img class="selected-banner" :src="banner" alt="" :style="{
                transform: banner ? `scale(${bannerScale}) translateX(${bannerTranslateX}%) translateY(${bannerTranslateY}%)` : 'none'
              }" v-if="banner" /> -->
              <img class="selected-banner" :src="banner" v-if="banner" />
              <img :src="no_image" :alt="$t('EditShopInfoComponent.chua_chon_anh')" v-if="!banner" />
            </div>
            <div class="content-label">
              <span class="optional label-input">{{ $t('EditShopInfoComponent.anh_bia') }} </span>

              <span class="label-advanced"> {{ $t('EditShopInfoComponent.anh_bia_kich_thuoc_de_xuat') }} </span>
            </div>

            <div class="image-actions">
              <button class="select-image-button">
                {{ $t('EditShopInfoComponent.doi_anh') }}
                <input type="file" accept="image/*" :multiple="false" v-on:change="($event: any) => {
                  fileChangeBanner($event);
                }" ref="bannerFileName" />
              </button>
              <button class="edit-image-button" :disabled="!banner || !banner.length" :class="{ 'active': editBanner }"
                v-on:click="() => {
                  editBanner = !editBanner
                }" v-if="false">
                {{ $t('EditShopInfoComponent.dieu_chinh') }}
              </button>
              <button class="delete-image-button" :disabled="!banner || !banner.length" v-on:click="() => {
                banner = '';
                bannerFileName.value = null;
                editBanner = false;
                bannerScale = 1;
                isBannerChange = true;
                bannerTranslateX = 0;
                bannerTranslateY = 0;
                orientationBanner = 0;
              }">
                {{ $t('EditShopInfoComponent.xoa_anh') }}
              </button>
            </div>
            <div class="edit-image" v-if="editBanner">
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.lon_nho') }}</span>
                <v-slider class="edit-image-slider" v-model="bannerScale" :max="2" :min="0.01" :step="0.01" thumb-label
                  v-on:update:modelValue="() => {
                    isBannerChange = true;
                  }" color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" key="banner_scale" id="banner_scale">
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ (modelValue * 100).toFixed() }}%
                  </template>
                </v-slider>
              </div>
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.tren_duoi') }}</span>
                <v-slider class="edit-image-slider" v-model="bannerTranslateY" :max="100" :min="-100" :step="1"
                  color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" key="banner_translate_y"
                  v-on:update:modelValue="() => {
                    isBannerChange = true;
                  }" id="banner_translate_y" thumb-label>
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ modelValue }}%
                  </template>
                </v-slider>
              </div>
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.trai_phai') }}</span>
                <v-slider class="edit-image-slider" v-model="bannerTranslateX" :max="100" :min="-100" :step="1"
                  thumb-label color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" key="banner_translate_x"
                  v-on:update:modelValue="() => {
                    isBannerChange = true;
                  }" id="banner_translate_x">
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ modelValue }}%
                  </template>
                </v-slider>
              </div>
            </div>
          </div>
          <div class="v-stack shop-content">

            <div class="shop-logo-container">
              <img class="selected-logo" :src="logo" alt="" :style="{
                transform: logo ? `scale(${logoScale}) translateX(${logoTranslateX}%) translateY(${logoTranslateY}%)` : 'none'
              }" v-if="logo" />
              <img :src="no_image" :alt="$t('EditShopInfoComponent.chua_chon_anh')" v-if="!logo" />
            </div>
            <div class="content-label v-stack">
              <span class="optional label-input">{{ $t('EditShopInfoComponent.anh_dai_dien') }}</span>

              <span class="label-advanced"> {{ $t('EditShopInfoComponent.anh_dai_dien_kich_thuoc_de_xuat') }} </span>
            </div>
            <div class="image-actions">
              <button class="select-image-button">
                {{ $t('EditShopInfoComponent.doi_anh') }}
                <input type="file" accept="image/*" :multiple="false" v-on:change="($event: any) => {
                  fileChangeLogo($event);
                }" ref="logoFileName" />
              </button>
              <button class="edit-image-button" :disabled="!logo || !logo.length" :class="{ 'active': editLogo }"
                v-on:click="() => {
                  editLogo = !editLogo
                }">
                {{ $t('EditShopInfoComponent.dieu_chinh') }}
              </button>
              <button class="delete-image-button" :disabled="!logo || !logo.length" v-on:click="() => {
                logo = '';
                logoFileName.value = null;
                logoScale = 1;
                logoTranslateX = 0;
                logoTranslateY = 0;
                editLogo = false;
                isLogoChange = true;
                orientationLogo = 0;
              }">
                {{ $t('EditShopInfoComponent.xoa_anh') }}
              </button>
            </div>
            <div class="edit-image" v-if="editLogo">
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.lon_nho') }}</span>
                <v-slider class="edit-image-slider" v-model="logoScale" :max="2" :min="0.01" :step="0.01" thumb-label
                  v-on:update:modelValue="() => {
                    isLogoChange = true
                  }" color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" key="logo_scale" id="logo_scale">
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ (modelValue * 100).toFixed() }}%
                  </template>
                </v-slider>
              </div>
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.tren_duoi') }}</span>
                <v-slider class="edit-image-slider" v-model="logoTranslateY" :max="100" :min="-100" :step="1"
                  color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" key="logo_translate_y"
                  v-on:update:modelValue="() => {
                    isLogoChange = true
                  }" id="logo_translate_y" thumb-label>
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ modelValue }}%
                  </template>
                </v-slider>
              </div>
              <div class="action-edit-image">
                <span class="label-edit">{{ $t('EditShopInfoComponent.trai_phai') }}</span>
                <v-slider class="edit-image-slider" v-model="logoTranslateX" :max="100" :min="-100" :step="1"
                  color="#bbbbbb" track-fill-color="#bbbbbb" track-size="2px" thumb-label key="logo_translate_x"
                  v-on:update:modelValue="() => {
                    isLogoChange = true
                  }" id="logo_translate_x">
                  <template v-slot:thumb-label="{ modelValue }">
                    {{ modelValue }}%
                  </template>
                </v-slider>
              </div>
            </div>
          </div>


          <div class="v-stack shop-content p-0">
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.ten_cua_hang') }}</span>
                <input :title="$t('EditShopInfoComponent.ten_cua_hang')" name="shop-name" class="input-shop"
                  :maxlength="appConst.max_text_short"
                  :placeholder="$t('EditShopInfoComponent.nhap_ten_cua_hang')" :value="name || ''" v-on:input="($event: any) => {
                    name = $event.currentTarget.value
                    if (!name || !name.length) {
                      nameErr = $t('EditShopInfoComponent.vui_long_nhap_ten_cua_hang')
                    } else {
                      nameErr = ''
                    }
                  }" v-on:blur="() => {
                    if (!name || !name.length) {
                      nameErr = $t('EditShopInfoComponent.vui_long_nhap_ten_cua_hang')
                    } else {
                      nameErr = ''
                    }
                  }" />
              </div>
              <span class="error-message">{{ nameErr }}</span>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.linh_vuc') }}</span>
                <v-select label="" :placeholder="$t('EditShopInfoComponent.thiet_lap')" :filter-mode="'intersection'"
                  v-model="business_type_id" :item-value="'id'" v-on:update:focused="(e) => {
                    if (e == false) {
                      if (business_type_id) {
                        businessTypeIdErr = ''
                      }
                      else {
                        businessTypeIdErr = $t('EditShopInfoComponent.vui_long_chon_loai_hinh_kinh_doanh');
                      }
                    }

                  }" :item-title="'name'" :no-data-text="$t('EditShopInfoComponent.khong_tim_thay')"
                  class="language-select" :menu-icon="''" :items="dataBusinessType" variant="plain">
                  <template v-slot:append-inner>
                    <!-- <Icon name="mdi:chevron-down"></Icon> -->
                  </template>
                </v-select>
              </div>
              <span class="error-message">{{ businessTypeIdErr }}</span>
            </div>
            <div class="shop-info-content" v-if="false">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.email') }}</span>
                <input :title="$t('EditShopInfoComponent.email')" name="shop-email" class="input-shop"
                  :placeholder="$t('EditShopInfoComponent.nhap_dia_chi_email')" :value="email || ''" v-on:input="($event: any) => {
                    email = $event.currentTarget.value;
                    emailValidation();
                  }" v-on:blur="() => {
                    emailValidation();
                  }" />
              </div>

              <span class="error-message">{{ emailErr }}</span>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.so_dien_thoai') }}</span>
                <input :title="$t('EditShopInfoComponent.so_dien_thoai')" name="shop-phone" class="input-shop"
                  type="phone" :placeholder="$t('EditShopInfoComponent.nhap_so_dien_thoai')" :value="phone || ''"
                  v-on:input="($event: any) => {
                    phone = validPhone($event.currentTarget.value);
                    phoneValidation();
                  }" v-on:blur="() => {
                    phoneValidation();
                  }" />
              </div>
              <span class="error-message">{{ phoneErr }}</span>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.ngon_ngu') }}</span>
                <v-autocomplete chips :closable-chips="false" :clear-on-select="true" label=""
                  :placeholder="$t('EditShopInfoComponent.thiet_lap')" v-model="selectLanguage" class="language-select"
                  :counter="5" :custom-filter="filterLanguage" reverse :menu-icon="''" v-on:update:focused="(e) => {
                    if (!selectLanguage.length) {
                      selectLanguageErr = $t('EditShopInfoComponent.vui_long_chon_ngon_ngu')
                    } else {
                      selectLanguageErr = ''
                    }
                  }" persistent-clear return-object :items="appConst.langCodes" multiple variant="plain"
                  v-on:update:modelValue="() => {
                    if (selectLanguage.length > appConst.max_language_shop) {
                      selectLanguage.splice(selectLanguage.length - 1, 1)
                    }
                  }">
                  <template v-slot:item="{ props, item }">
                    <v-list-item v-bind="props" :title="ISO6391.getNativeName(item.value)"
                      :subtitle="ISO6391.getName(item.value)"></v-list-item>
                  </template>
                  <template v-slot:chip="{ props, item }">
                    <v-chip v-bind="props" class="chip-language" v-on:click="() => {
                      selectLanguage.splice(selectLanguage.indexOf(item.value), 1)
                    }">{{ ISO6391.getNativeName(item.value) }} <Icon name="iconamoon:close-circle-1-light"></Icon>
                    </v-chip>
                  </template>
                  <template v-slot:append-inner>
                    <!-- <Icon name="mdi:chevron-down"></Icon> -->
                  </template>
                </v-autocomplete>
              </div>
              <span class="error-message">{{ selectLanguageErr }}</span>
            </div>

            <div class="shop-info-content">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.gio_mo_cua') }}</span>
                <button class="input-shop open-hours" v-on:click="() => {
                  showSelectOpenTimeModal = true
                }">
                  <span v-if="open_hours">{{ formatOpeningHours(open_hours) }}</span>
                  <span v-else class="placeholder">{{ $t('EditShopInfoComponent.thiet_lap') }}</span>
                </button>
              </div>
              <span class="error-message">{{ openHoursErr }}</span>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.don_vi_tien_te') }}</span>
                <v-select label="" :placeholder="$t('EditShopInfoComponent.don_vi_tien_te_cua_san_pham')"
                  v-model="selectCurrency" class="language-select" :counter="5" :menu-icon="''"
                  v-on:update:focused="(e) => { }" persistent-clear return-object :items="currencyList" variant="plain">
                  <template v-slot:item="{ props, item }">
                    <v-list-item v-bind="props" :title="item.value.name"
                      :subtitle="item.value.currencySign"></v-list-item>
                  </template>
                  <template v-slot:selection="{ item }">
                    <span>{{ item.value.name }}</span>
                  </template>
                  <template v-slot:append-inner>
                    <!-- <Icon name="mdi:chevron-down"></Icon> -->
                  </template>
                </v-select>
              </div>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.gioi_thieu') }}</span>
              </div>
            </div>
            <div class="shop-info-content">
              <div class="input-content bio">
                <UTextarea autoresize :maxrows="5" :title="$t('EditShopInfoComponent.gioi_thieu_placeholder')"
                  name="shop-description" class="text-area-shop input-shop"
                  :placeholder="$t('EditShopInfoComponent.gioi_thieu_placeholder')" v-model="description">
                  <em class="counter">{{ description ? description.length : 0 }} / 5000</em>
                </UTextarea>

              </div>
            </div>
          </div>

          <div class="v-stack shop-content p-0">
            <div class="shop-info-content">
              <div class="input-content">
                <span class="required label-input">{{ $t('EditShopInfoComponent.dia_chi') }}</span>
              </div>

            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <AddressSearchingInputComponent :address="address" v-on:input="(event: any) => {
                  address = event;
                  if (!address || !address.length) {
                    addressErr = $t('EditShopInfoComponent.vui_long_nhap_dia_chi');
                  } else {
                    addressErr = '';
                  }
                }" v-on:select="(obj: any) => {
                  selectAddressSuggest(obj);
                  addressErr = '';
                }" />
                <span class="error-message">{{ addressErr }}</span>
              </div>
            </div>
            <div class="map-container">
              <client-only>
                <LMap id="leaflet_map" v-on:ready="(e: any) => {
                  leafletMap = e;
                  leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                  initLeafletMap();
                }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :ondragstart="() => {
                  disabledDragTab = true;
                }" :ondragend="() => {
                  disabledDragTab = false;
                }" v-on:update:bounds="async (bounds: any) => {
                  latitude = leafletMap.getCenter().lat;
                  longitude = leafletMap.getCenter().lng;
                }" :options="{ zoomControl: false }" :world-copy-jump="true" :use-global-leaflet="true">
                  <LControlZoom position="bottomright"></LControlZoom>
                  <span class="current-location-leaflet" :title="$t('EditShopInfoComponent.vi_tri_cua_ban')" v-on:click="() => {
                    gotoCurrentLocationLeaflet();
                  }">
                    <Icon name="line-md:my-location-loop" class="my-location-icon" />
                  </span>
                  <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
                    data-placement="right" :title="$t('EditShopInfoComponent.nhan_de_chuyen_loai_map')" v-bind:style="{
                      backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                    }" v-on:click="(event: any) => {
                      if (event.isTrusted) {
                        if (leafletMapTileUrl == appConst.leafletMapTileUrl.roadmap) {
                          leafletMapTileUrl = appConst.leafletMapTileUrl.hyprid;
                          mapTypeTitle = $t('EditShopInfoComponent.ve_tinh');
                          mapType = 'hyprid';
                          buttonMapTileBackgound = map_sateline;
                        } else if (leafletMapTileUrl == appConst.leafletMapTileUrl.hyprid) {
                          leafletMapTileUrl = appConst.leafletMapTileUrl.streetmap;
                          mapTypeTitle = $t('EditShopInfoComponent.co_dien');
                          mapType = 'hyprid';
                          buttonMapTileBackgound = map_streetmap;
                        } else if (leafletMapTileUrl == appConst.leafletMapTileUrl.streetmap) {
                          leafletMapTileUrl = appConst.leafletMapTileUrl.roadmap;
                          mapTypeTitle = $t('EditShopInfoComponent.ve_tinh_nhan');
                          mapType = 'roadmap';
                          buttonMapTileBackgound = map_sateline;
                        }
                      } else event.preventDefault();
                    }">
                    <span>{{ mapTypeTitle }}</span>
                  </div>
                  <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                    :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base" name="GoogleMap">
                  </LTileLayer>
                  <div class="marker-location">
                    <img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon" alt="" />
                  </div>
                </LMap>
              </client-only>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.tinh_thanh_pho') }}</span>
                <button class="custom-select province" :class="{
                  'selected': province_id,
                }
                  " v-on:click="() => {
                    showProvinceSelect = true
                  }">
                  <span v-if="province_id">
                    {{ dataProvince.filter((e: any) => {
                      return e.id == province_id
                    })[0]?.name }}
                  </span>
                  <span v-else>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>

                </button>
                <!-- <v-autocomplete label="" :placeholder="$t('EditShopInfoComponent.chon_tinh_thanh_pho')"
                  :persistent-placeholder=true :filter-mode="'intersection'" v-model="province_id" :item-value="'id'"
                  :item-title="'name'" :no-data-text="$t('EditShopInfoComponent.khong_tim_thay')"
                  class="language-select" :menu-icon="''" :items="dataProvince" variant="plain" v-on:update:modelValue="() => {
                    district_id = null;
                    ward_id = null;
                    dataDistrict = [];
                    dataWard = []
                    if (province_id) {
                      getDistrictsByProvinceId();
                    }
                  }">
                  <template v-slot:append-inner>
                    <Icon name="mdi:chevron-down"></Icon>
                  </template>
                </v-autocomplete> -->

              </div>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.quan_huyen') }}</span>
                <button class="custom-select district" :class="{
                  'selected': district_id,
                }
                  " v-on:click="() => {
                    showDistrictSelect = true
                  }">
                  <span v-if="district_id">
                    {{ dataDistrict.filter((e: any) => {
                      return e.id == district_id
                    })[0]?.name }}
                  </span>
                  <span v-else>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>

                </button>
              </div>
            </div>
            <div class="shop-info-content">
              <div class="input-content">
                <span class="optional label-input">{{ $t('EditShopInfoComponent.phuong_xa') }}</span>
                <button class="custom-select ward" :class="{
                  'selected': ward_id,
                }
                  " v-on:click="() => {
                    showWardSelect = true
                  }">
                  <span v-if="ward_id">
                    {{ dataWard.filter((e: any) => {
                      return e.id == ward_id
                    })[0]?.name }}
                  </span>
                  <span v-else>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>

                </button>
              </div>
            </div>
          </div>
          <div class="h-stack shop-content p-0">
            <button class="submit-button" :disabled="isEditingShop" v-on:click="() => {
              if (mode != 'agent') {
                editShop();
              }
              else {
                agentEditShop()
              }

            }">
              <Icon name="eos-icons:loading" size="20" v-show="isEditingShop" />
              {{ $t('EditShopInfoComponent.hoan_thanh') }}
            </button>
          </div>
          <span class="notice">
            <Icon name="solar:info-circle-bold"></Icon>
            <span
              v-html="$t('EditShopInfoComponent.cac_thong_tin_bat_buoc', { char: ':char' }).replaceAll(':char', `<em>(*)</em>`)"></span>
          </span>

        </div>
      </div>

    </div>
    <NoneMyShopComponent v-else-if="!isRefreshing" :show_header="true" :title="$t('AppRouteTitle.EditShopInfoComponent')">
    </NoneMyShopComponent>
    <CustomSelectComponent v-if="showProvinceSelect" :_key="'select_province'"
      :select_placeholder="$t('EditShopInfoComponent.thiet_lap')" :list_item="dataProvince" :field_value="'id'"
      :field_title="'name'" :multiple="false" :title="$t('EditShopInfoComponent.tinh_thanh_pho')"
      :class="'my-custom-select'" :searchable="false" :model_value="province_id" v-on:close="() => {
        showProvinceSelect = false
      }" v-on:model:update="(e) => {
        province_id = e;
        district_id = null;
        ward_id = null;
        dataDistrict = [];
        dataWard = []
        if (province_id) {
          getDistrictsByProvinceId();
        }
      }">
      <template v-slot:placeholder>
        <div class="h-stack">
          <span>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>
          <Icon name="mdi:chevron-down"></Icon>
        </div>
      </template>
      <template v-slot:title_icon_left>
        <Icon name="solar:hamburger-menu-linear"></Icon>
      </template>
    </CustomSelectComponent>
    <CustomSelectComponent :_key="'select_district'" v-if="showDistrictSelect"
      :select_placeholder="$t('EditShopInfoComponent.thiet_lap')" :list_item="dataDistrict" :field_value="'id'"
      :field_title="'name'" :multiple="false" :title="$t('EditShopInfoComponent.quan_huyen')"
      :class="'my-custom-select'" :searchable="false" :model_value="district_id" v-on:close="() => {
        showDistrictSelect = false
      }" v-on:model:update="(e) => {
        district_id = e;
        ward_id = null;
        dataWard = []
        if (district_id) {
          getWardsByDistrictId();
        }
      }">
      <template v-slot:placeholder>
        <div class="h-stack">
          <span>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>
          <Icon name="mdi:chevron-down"></Icon>
        </div>
      </template>
      <template v-slot:title_icon_left>
        <Icon name="solar:hamburger-menu-linear"></Icon>
      </template>
    </CustomSelectComponent>
    <CustomSelectComponent :_key="'select_ward'" v-if="showWardSelect"
      :select_placeholder="$t('EditShopInfoComponent.chon_tinh_thanh_pho')" :list_item="dataWard" :field_value="'id'"
      :field_title="'name'" :multiple="false" :title="$t('EditShopInfoComponent.phuong_xa')" :class="'my-custom-select'"
      :searchable="false" :model_value="ward_id" v-on:close="() => {
        showWardSelect = false
      }" v-on:model:update="(e) => {
        ward_id = e;
      }">
      <template v-slot:placeholder>
        <div class="h-stack">
          <span>{{ $t('EditShopInfoComponent.thiet_lap') }}</span>
          <Icon name="mdi:chevron-down"></Icon>
        </div>
      </template>
      <template v-slot:title_icon_left>
        <Icon name="solar:hamburger-menu-linear"></Icon>
      </template>
    </CustomSelectComponent>


  </div>
  <SelectOpenTimeComponent v-if="showSelectOpenTimeModal" :title="$t('EditShopInfoComponent.gio_mo_cua')"
    :dataOpenTime="JSON.parse(JSON.stringify(open_hours))" v-on:close="() => {
      showSelectOpenTimeModal = false
    }" v-on:submit="async (e: any) => {
      open_hours = e
      showSelectOpenTimeModal = false
    }"></SelectOpenTimeComponent>
</template>

<script lang="ts" setup>
import axios, { HttpStatusCode } from "axios";
import ISO6391 from 'iso-639-1';
import { Buffer } from "buffer";
import { toast } from "vue3-toastify";
import { appConst, appDataStartup, currencySign, domainImage, filterLanguage, formatOpeningHours, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import no_image from "~/assets/image/no-image.webp";
import { AgentService } from "~/services/agentService/agentService";
import exifr from "exifr";
import SelectOpenTimeComponent from "../selectOpenTime/SelectOpenTimeComponent.vue";
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { CircleStencil, Cropper, Preview, RectangleStencil } from 'vue-advanced-cropper'
import type { _aspectRatio, _height } from "#tailwind-config/theme";

const nuxtApp = useNuxtApp();
const { t } = useI18n()
useSeoMeta({
  title: t('AppRouteTitle.EditShopInfoComponent'),
});
var router = useRouter();
var route = useRoute();

var emit = defineEmits(["close"]);
var props = defineProps({
  shopData: {},
  profileData: {},
  mode: null
});

var shop_id = ref((route.params.id ? route.params.id : null) as any);

var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('EditShopInfoComponent.ve_tinh_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var buttonMapTileBackgound = ref(map_sateline);
// var buttonMapTileBackgound = ref(map_streetmap);

var authService = new AuthService();
var userService = new UserService();
var agentService = new AgentService();
var placeService = new PlaceService();
var shopService = new ShopService();
var imageService = new ImageService();

var dataBusinessType = ref(appDataStartup.listBusinessType as any);
var dataProvince = ref(appDataStartup.listProvince as any);

var tabIndex = ref("info");

var name = ref("");
var nameErr = ref("");
var email = ref("");
var emailErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var address = ref("");
var addressSelected = ref("");
var addressErr = ref("");
var slug = ref("");
var slugErr = ref("");
var banner = ref("");
var bannerFileName = ref(null as any);
var isBannerChange = ref(false);
var logo = ref("");
var logoFileName = ref(null as any);
var isLogoChange = ref(false);
var description = ref("");
var business_type_id = ref("");
var businessTypeIdErr = ref("");
var latitude = ref(null as any);
var longitude = ref(null as any);
var selectLanguage = ref([] as any);
var selectLanguageErr = ref("")

var locationErr = ref("");
var province_id = ref(null as any);
var provinceIdErr = ref("");
var district_id = ref(null as any);
var districtIdErr = ref("");
var ward_id = ref(null as any);
var wardIdErr = ref("");
var open_hours = ref(null as any);
var openHoursErr = ref("");
var shopData = ref(
  props && props.shopData ? JSON.parse(JSON.stringify(props.shopData)) : null
);
var profileData = ref(
  props && props.profileData
    ? JSON.parse(JSON.stringify(props.profileData))
    : null
);

var disabledDragTab = ref(false);
var dataDistrict = ref([] as any[]);
var dataWard = ref([] as any[]);
var showMap = ref(false);
var isEditingShop = ref(false);
var orientationLogo = ref(0);
var orientationBanner = ref(0);

var editLogo = ref(false);
var logoScale = ref(1);
var logoTranslateX = ref(0);
var logoTranslateY = ref(0);

var editBanner = ref(false);
var bannerScale = ref(1);
var bannerTranslateX = ref(0);
var bannerTranslateY = ref(0);

var currencyList = ref([] as any[]);
var selectCurrency = ref(null as any);

var webInApp = ref(null as any);
var mode = ref(props.mode ? props.mode : null);

var showSelectOpenTimeModal = ref(false);
var showProvinceSelect = ref(false);
var showDistrictSelect = ref(false);
var showWardSelect = ref(false);

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

// watch(() => [user_latitude.value, user_longitude?.value], () => {
// 	latitude.value = user_latitude?.value;
// 	longitude.value = user_longitude?.value;
// });
var isRefreshing = ref(false);

onBeforeMount(async () => {
  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
  nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
    dataProvince.value = JSON.parse(JSON.stringify(appDataStartup.listProvince));
    dataBusinessType.value = JSON.parse(JSON.stringify(appDataStartup.listBusinessType))
  });

  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})
onUnmounted(() => {
  nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onMounted(async () => {
  isRefreshing.value = true;
  if (mode.value != 'agent') {
    await getMyShop();
  }
  else {
    await getShopDetailByAgent()
  }
  // await getMyShop();
  if (shopData.value) {
    init();
  }
  isRefreshing.value = false;

  document.querySelectorAll('.required').forEach(el => {
    el.setAttribute('data-required', ` (*)`)
  })
  document.querySelectorAll('.optional').forEach(el => {
    el.setAttribute('data-optional', ` (${t('EditShopInfoComponent.tuy_chon')})`)
  })
});
async function getMyShop() {
  let shopData$ = await shopService.myShop();

  if (shopData$?.status == HttpStatusCode.Ok && shopData$?.body?.data) {
    shopData.value = JSON.parse(JSON.stringify(shopData$.body.data));
  }
  else if (shopData$.status == 401) {
    router.push({
      path: appRoute.LoginComponent,
      query: {
        redirect: JSON.stringify(route.fullPath)
      }
    })
  }
  else {
    toast.error(t('EditShopInfoComponent.ban_khong_co_quyen_truy_cap'))
  }
  // shopService.myShop().then(async res => {
  // 	if (res.status && res.status == HttpStatusCode.Ok) {
  // 		shopData.value = res.body.data;
  //     init();
  // 	}
  // 	else {
  // 		shopData.value = null
  // 	}

  // })
}
async function getShopDetailByAgent() {
  let shopData$ = await agentService.agentShopDetail(shop_id.value)
  if (shopData$.status == HttpStatusCode.Ok) {
    shopData.value = shopData$.body.data;
    useSeoMeta({
      title: t('EditShopInfoComponent.chinh_sua', {
        name: shopData.value.name
      })
    })
  }
  else if (shopData$.status == 401) {
    router.push({
      path: appRoute.LoginComponent,
      query: {
        redirect: JSON.stringify(route.fullPath)
      }
    })
  }
  else {
    toast.error(t('EditShopInfoComponent.ban_khong_co_quyen_truy_cap'))
  }
}
async function init() {
  name.value = shopData.value?.name;
  address.value = shopData.value?.address;
  slug.value = shopData.value?.slug;
  phone.value = validPhone(shopData.value?.phone);
  // banner = shopData.banner;
  description.value = shopData.value?.description;
  business_type_id.value = shopData.value?.business_type_id;
  latitude.value = shopData.value?.latitude;
  longitude.value = shopData.value?.longitude;
  province_id.value = shopData.value?.province_id;
  district_id.value = shopData.value?.district_id;
  ward_id.value = shopData.value?.ward_id;
  open_hours.value = JSON.parse(shopData.value?.open_hours);
  if (shopData.value?.language.at(0) != '[') {
    selectLanguage.value = [shopData.value?.language];
  }
  else {
    selectLanguage.value = JSON.parse(shopData.value?.language);
  }
  Object.keys(currencySign).forEach((key: any) => {
    currencyList.value.push({
      key: key,
      code: currencySign[key].code,
      name: currencySign[key].name,
      currencySign: currencySign[key].currencySign,
    })
  });

  selectCurrency.value = currencyList.value.find((e) => {
    return e.key == shopData.value.currency
  }) || currencyList.value[0];
  // selectLanguage.value = JSON.parse(JSON.stringify(shopData.value.language));
  if (shopData.value.banner) {
    convertBannerImageToBase64(domainImage + shopData.value.banner.path);
    getBannerStyle()
  }
  if (shopData.value.logo) {
    convertLogoImageToBase64(domainImage + shopData.value.logo.path)
    getLogoStyle()
  }

  await getDistrictsByProvinceId();
  await getWardsByDistrictId();
}
async function initLeafletMap() {
  // markersCluster = new nuxtApp.$L.MarkerClusterGroup({
  // 	maxClusterRadius: 5,
  // 	iconCreateFunction: (cluster) => createClusterElement(cluster),
  // }).addTo(leafletMap);
  await setCurrentLocationLeaflet();
  (leafletMap as any)["gestureHandling"].enable();
}
function convertBannerImageToBase64(imgUrl: string) {
  axios
    .get(imgUrl, {
      responseType: "arraybuffer",
    })
    .then((response) => {
      const buffer = Buffer.from(response.data, "base64").toString("base64");
      banner.value = "data:image/webp" + ";base64," + buffer;
    })
    .catch((ex) => {
      console.error(ex);
    });
}

function convertLogoImageToBase64(imgUrl: string) {
  axios
    .get(imgUrl, {
      responseType: "arraybuffer",
    })
    .then((response) => {
      const buffer = Buffer.from(response.data, "base64").toString("base64");
      logo.value = "data:image/webp" + ";base64," + buffer;
    })
    .catch((ex) => {
      console.error(ex);
    });
}

async function editShop() {
  isEditingShop.value = true;
  let check = await checkValidate();
  // let check = false;
  if (!check) {
    let body = {
      language: JSON.stringify(selectLanguage.value),
      name: name.value,
      business_type_id: business_type_id.value,
      address: address.value,
      province_id: province_id.value,
      description: description.value,
      district_id: district_id.value,
      ward_id: ward_id.value,
      latitude: latitude.value,
      longitude: longitude.value,
      user_id: shopData.value.user_id,
      currency: selectCurrency.value.key || currencyList.value[0].key,
      phone: validPhone(phone.value),
      open_hours: JSON.stringify(open_hours.value),
      id: shopData.value.id,
    };
    shopService
      .edit(body)
      .then(async (res) => {
        if (res.status == HttpStatusCode.Ok) {

          if ((isBannerChange.value) || (isLogoChange.value)) {
            try {
              if (banner.value && banner.value.length && isBannerChange.value) {
                let banner$ = {
                  path: banner.value,
                  object_type: appConst.object_type.shop,
                  image_type: 'banner',
                  title: name.value + " banner",
                  description: name.value + " description",
                  index: 0,
                  orientation: orientationBanner.value,
                  isEdit: false,
                  parent_id: res.body.data.id,
                  style: `scale(${bannerScale.value}) translateX(${bannerTranslateX.value}%) translateY(${bannerTranslateY.value}%)`
                }
                await imageService.insertImage(banner$).then(res => {
                  if (res.status == HttpStatusCode.Ok) {
                    // toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
                  }
                  else {
                    toast.error(t('EditShopInfoComponent.cap_nhat_banner_that_bai'));
                  }
                  // isEditingShop.value = false;
                });
              }
              if (logo.value && logo.value.length && isLogoChange.value) {
                let logo$ = {
                  path: logo.value,
                  object_type: appConst.object_type.shop,
                  image_type: 'logo',
                  title: name.value + " logo",
                  description: name.value + " description",
                  index: 0,
                  orientation: orientationLogo.value,
                  isEdit: false,
                  parent_id: res.body.data.id,
                  style: `scale(${logoScale.value}) translateX(${logoTranslateX.value}%) translateY(${logoTranslateY.value}%)`
                }
                await imageService.insertImage(logo$).then(res => {
                  if (res.status == HttpStatusCode.Ok) {
                    // toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
                  }
                  else {
                    toast.error(t('EditShopInfoComponent.cap_nhat_logo_that_bai'));
                  }
                  // isEditingShop.value = false;
                });
              }
            }
            finally {
              toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
              // isEditingShop.value = false;
              close(true);
            }
          }
          else {
            toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
            // isEditingShop.value = false;
            close(true);
          }

        }
        else {
          toast.error(t('EditShopInfoComponent.cap_nhat_cua_hang_that_bai'))
          isEditingShop.value = false;
        }
      })
      .catch((err) => {
        toast.error(t('EditShopInfoComponent.cap_nhat_cua_hang_that_bai'));
        isEditingShop.value = false;
      });
  } else {
    toast.error(t('EditShopInfoComponent.dien_day_du_thong_tin'));
    isEditingShop.value = false;
  }
}

async function agentEditShop() {
  isEditingShop.value = true;
  let check = await checkValidate();
  // let check = false;
  if (!check) {
    let body = {
      language: JSON.stringify(selectLanguage.value),
      name: name.value,
      business_type_id: business_type_id.value,
      address: address.value,
      province_id: province_id.value,
      district_id: district_id.value,
      ward_id: ward_id.value,
      latitude: latitude.value,
      longitude: longitude.value,
      phone: validPhone(phone.value),
      currency: selectCurrency.value.key || currencyList.value[0].key,
      id: shopData.value.id,
      open_hours: JSON.stringify(open_hours.value),
      description: description.value
    };
    agentService
      .editShopAgent(body)
      .then(async (res) => {
        if (res.status == HttpStatusCode.Ok) {
          if ((isBannerChange.value) || (isLogoChange.value)) {
            try {
              if (banner.value && banner.value.length && isBannerChange.value) {
                let banner$ = {
                  path: banner.value,
                  object_type: appConst.object_type.shop,
                  image_type: 'banner',
                  title: name.value + " banner",
                  description: name.value + " description",
                  index: 0,
                  orientation: orientationBanner.value,
                  isEdit: false,
                  parent_id: res.body.data.id,
                  style: `scale(${bannerScale.value}) translateX(${bannerTranslateX.value}%) translateY(${bannerTranslateY.value}%)`
                }
                await imageService.insertImage(banner$).then(res => {
                  if (res.status == HttpStatusCode.Ok) {
                    // toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
                  }
                  else {
                    toast.error(t('EditShopInfoComponent.cap_nhat_banner_that_bai'));
                  }
                  isEditingShop.value = false;
                });
              }
              if (logo.value && logo.value.length && isLogoChange.value) {
                let logo$ = {
                  path: logo.value,
                  object_type: appConst.object_type.shop,
                  image_type: 'logo',
                  title: name.value + " logo",
                  description: name.value + " description",
                  index: 0,
                  orientation: orientationLogo.value,
                  isEdit: false,
                  parent_id: res.body.data.id,
                  style: `scale(${logoScale.value}) translateX(${logoTranslateX.value}%) translateY(${logoTranslateY.value}%)`
                }
                await imageService.insertImage(logo$).then(res => {
                  if (res.status == HttpStatusCode.Ok) {
                    // toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
                  }
                  else {
                    toast.error(t('EditShopInfoComponent.cap_nhat_logo_that_bai'));
                  }
                  isEditingShop.value = false;
                });
              }
            }
            finally {
              toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
              // isEditingShop.value = false;
              close(true);
            }
          }
          else {
            toast.success(t('EditShopInfoComponent.cap_nhat_cua_hang_thanh_cong'));
            // isEditingShop.value = false;
            close(true);
          }
        } else {
          toast.error(t('EditShopInfoComponent.cap_nhat_cua_hang_that_bai'));
          isEditingShop.value = false;
        }
      })
      .catch((err) => {
        toast.error(t('EditShopInfoComponent.cap_nhat_cua_hang_that_bai'));
        isEditingShop.value = false;
      });
  } else {
    toast.error(t('EditShopInfoComponent.dien_day_du_thong_tin'));
    isEditingShop.value = false;
  }
}
function close(updated = false) {
  // emit("close", updated);
  setTimeout(() => {
    router.options.history.state.back ? router.back() : router.push(appRoute.MyShopComponent)
    if (updated) {
      setTimeout(() => {
        nuxtApp.$emit(appConst.event_key.shop_updated);
      }, 2000);
    }
  }, 1500);

}
async function checkValidate() {
  let error = false;
  // if (!province_id.value) {
  //   error = true;
  //   provinceIdErr.value = "Vui lòng chọn Tỉnh/Thành phố"
  // }
  // else {
  //   provinceIdErr.value = ""
  // }

  // if (!district_id.value) {
  //   error = true
  //   districtIdErr.value = "Vui lòng chọn Quận/Huyện"
  // }
  // else {
  //   districtIdErr.value = ""
  // }

  // if (!ward_id.value) {
  //   error = true
  //   wardIdErr.value = "Vui lòng chọn Xã/Phường"
  // }
  // else {
  //   wardIdErr.value = ""
  // }

  if (!name.value || !name.value.length) {
    error = true;
    nameErr.value = t('EditShopInfoComponent.vui_long_nhap_ten_cua_hang');
    tabIndex.value = "info";
  } else {
    nameErr.value = "";
  }

  if (!business_type_id.value || !business_type_id.value.length) {
    error = true;
    businessTypeIdErr.value = t('EditShopInfoComponent.vui_long_chon_loai_hinh_kinh_doanh');
    tabIndex.value = "info";
  } else {
    businessTypeIdErr.value = "";
  }

  if (!address.value || !address.value.length) {
    error = true;
    addressErr.value = t('EditShopInfoComponent.vui_long_nhap_dia_chi');
  } else {
    addressErr.value = "";
  }

  if (!selectLanguage.value || !selectLanguage.value.length) {
    error = true;
    selectLanguageErr.value = t('EditShopInfoComponent.vui_long_chon_ngon_ngu');
  } else {
    selectLanguageErr.value = "";
  }

  await phoneValidation();
  if (phoneErr.value && phoneErr.value.length) {
    error = true;
  }

  if (!latitude.value || !longitude.value) {
    error = true;
    locationErr.value = t('EditShopInfoComponent.vui_long_chon_vi_tri');
  } else {
    locationErr.value = "";
  }

  // if (!open_hours.value) {
  //   error = true;
  //   openHoursErr.value = t('EditShopInfoComponent.vui_long_chon_gio_mo_cua');
  // } else {
  //   openHoursErr.value = "";
  // }

  return error;
}

function setCurrentLocationLeaflet() {
  if (latitude.value && longitude.value) {
    leafletMap.setView([latitude.value, longitude.value], 13);
    // setLocationLeafletMarker(latitude.value, longitude.value);
  }
  else {
    leafletMap.setView([user_latitude.value ?? appConst.defaultCoordinate.latitude, user_longitude.value ?? appConst.defaultCoordinate.longitude], 13);
  }
  // else if ("geolocation" in navigator) {
  //   navigator.geolocation.getCurrentPosition(
  //     async (position) => {
  //       latitude.value = position.coords.latitude;
  //       longitude.value = position.coords.longitude;
  //       leafletMap.setView([latitude.value, longitude.value], 13);
  //       // setLocationLeafletMarker(latitude.value, longitude.value);
  //     },
  //     async (error) => {
  //       latitude.value = appConst.defaultCoordinate.latitude;
  //       longitude.value = appConst.defaultCoordinate.longitude;
  //       leafletMap.setView([latitude.value, longitude.value], 13);
  //       // setLocationLeafletMarker(latitude.value, longitude.value);
  //     },
  //     {
  //       enableHighAccuracy: false, // Use less accurate but faster methods
  //       timeout: 5000, // Set a timeout (in milliseconds)

  //     }
  //   );
  // }
}
function setLocationLeafletMarker(lat: number, lng: number) {
  if (localeMarkerLeaflet) {
    localeMarkerLeaflet.remove();
  }

  localeMarkerLeaflet = nuxtApp.$L.marker([lat, lng], {
    icon: new nuxtApp.$L.Icon({
      iconUrl: marker_location_icon,
      iconSize: appConst.markerCustom.defaultIcon.size,
      className: appConst.markerCustom.defaultIcon.class,
    }),
    draggable: true,
  });
  localeMarkerLeaflet.addTo(leafletMap);
  latitude.value = lat;
  longitude.value = lng;
  getUserAddress();

  localeMarkerLeaflet.addEventListener("click", (event: L.LeafletEvent) => {
    //   isShowCoordinateLocation = true;
    //   document.getElementById("leaflet-detail-auto-click").click();
  });

  localeMarkerLeaflet.addEventListener("moveend", async (event) => {
    latitude.value = event.target._latlng.lat;
    longitude.value = event.target._latlng.lng;
    await getUserAddress();
  });
}
async function gotoCurrentLocationLeaflet(event?: Event) {
  if (!event || event.isTrusted == true) {

    // if ("geolocation" in navigator) {
    //   navigator.geolocation.getCurrentPosition(
    //     (position) => {
    //       leafletMap.flyTo(
    //         [position.coords.latitude, position.coords.longitude],
    //         17
    //       );
    //       latitude.value = position.coords.latitude;
    //       longitude.value = position.coords.longitude;

    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     (error) => {
    //       toast.warning(t('EditShopInfoComponent.chua_cung_cap_vi_tri'), {
    //         autoClose: 1000,
    //         hideProgressBar: true,
    //       });
    //       latitude.value = appConst.defaultCoordinate.latitude;
    //       longitude.value = appConst.defaultCoordinate.longitude;
    //       leafletMap.flyTo([latitude.value, longitude.value], 17);

    //       // setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     {
    //       enableHighAccuracy: false, // Use less accurate but faster methods
    //       timeout: 5000, // Set a timeout (in milliseconds)

    //     }
    //   );
    // }

    leafletMap.flyTo(
      [user_latitude.value, user_longitude.value],
      17
    );
    latitude.value = user_latitude.value;
    longitude.value = user_longitude.value;
  }
}
function selectAddressSuggest(addressSelectSuggest: any) {

  address.value = addressSelectSuggest.address;
  latitude.value = addressSelectSuggest.latitude;
  longitude.value = addressSelectSuggest.longitude;
  province_id.value = addressSelectSuggest.province_id;
  district_id.value = addressSelectSuggest.district_id;
  ward_id.value = addressSelectSuggest.ward_id;
  flyToLocation(addressSelectSuggest.latitude, addressSelectSuggest.longitude)
  getDistrictsByProvinceId();
  getWardsByDistrictId()
}
function flyToLocation(lat: any, lng: any) {
  if (leafletMap) leafletMap.flyTo([lat, lng], 17, {
    duration: 1
  });
}
function getUserAddress() {
  setProvinceDistrictByLocation();
}
async function setProvinceDistrictByLocation() {
  placeService
    .myGeocoderByLatLngToAddress(latitude.value, longitude.value)
    .then((res: any) => {
      if (res.body.data && res.body.data.length) {
        address.value = res.body.data[0].address
          ? res.body.data[0].address
          : "";
        // address = res.body.body.data[0].address ? res.body.body.data[0].address : "";
        province_id.value = res.body.data[0].province_id
          ? res.body.data[0].province_id
          : null;
        district_id.value = res.body.data[0].district_id
          ? res.body.data[0].district_id
          : null;
        ward_id.value = res.body.data[0].ward_id
          ? res.body.data[0].ward_id
          : null;
        getDistrictsByProvinceId();
        getWardsByDistrictId();
      }
    });
}
async function getDistrictsByProvinceId() {
  let dataProvince$ = await placeService.provinceDetail(province_id.value);
  if (dataProvince$.status == HttpStatusCode.Ok) {
    dataDistrict.value = JSON.parse(
      JSON.stringify(dataProvince$.body.data.districts)
    );
  }
  else {
    dataDistrict.value = [];
  }
}
async function getWardsByDistrictId() {
  let dataDistrict$ = await placeService.districtDetail(district_id.value);
  if (dataDistrict$.status == HttpStatusCode.Ok) {
    dataWard.value = JSON.parse(JSON.stringify(dataDistrict$.body.data.wards));
  }
  else {
    dataWard.value = [];
  }
}

// async function fileChangeInput(fileInput: any) {
//   if (fileInput.target.files.length) {
//     // EXIF.getData(fileInput.target.files[0], (data:any)=>{
//     // })
//     const reader = new FileReader();
//     reader.onload = async (e: any) => {
//       const image = new Image();
//       image.src = e.target.result;
//       banner.value = image.src;
//       orientation.value = fileInput.target.files[0].exifdata
//         ? fileInput.target.files[0].exifdata.Orientation
//         : null;
//     };
//     await reader.readAsDataURL(fileInput.target.files[0]);
//   }
// }

async function fileChangeLogo(fileInput: any) {

  if (fileInput.target.files.length) {
    if (fileInput.target.files[0].size > appConst.image_size.max) {
      let imgErr = t('EditShopInfoComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
      toast.error(imgErr);
    }
    else {
      isLogoChange.value = true;
      logoFileName.value = fileInput.target.files[0];
      logoScale.value = 1;
      logoTranslateX.value = 0;
      logoTranslateY.value = 0;
      const reader = new FileReader();

      reader.onload = async (e: any) => {
        let imageFile = e.target;
        const image = new Image();
        // image.onload = function () {

        //   var canvas = document.createElement('canvas');
        //   var ctx = canvas.getContext('2d') as any;
        //   var width = image.width;
        //   var height = image.height;
        //   var aspectRatio = width / height;
        //   if (width > height) {
        //     if (width > 2000) {
        //       width = 2000;
        //       height = width / aspectRatio;
        //     }
        //   } else {
        //     if (height > 2000) {
        //       height = 2000;
        //       width = height * aspectRatio;
        //     }
        //   }
        //   canvas.width = width;
        //   canvas.height = height;
        //   ctx.drawImage(image, 0, 0, width, height);

        //   // EXIF.getData(fileInput.target.files[0], (data:any)=>{
        //   // })

        //   logo.value = canvas.toDataURL(imageFile.type);
        // }
        image.src = e.target.result;
        logo.value = e.target.result;
        let orientationExif;
        if (fileInput.target.files[0].type != 'image/webp') {
          orientationExif = await exifr.orientation(image) || 0;
        }
        else orientationExif = 0;
        orientationLogo.value = orientationExif ? orientationExif : 0;
        logoFileName.value = fileInput.target.files[0].name;
      };
      await reader.readAsDataURL(fileInput.target.files[0]);
    }
  }
}

async function fileChangeBanner(fileInput: any) {
  isBannerChange.value = true;
  if (fileInput.target.files.length) {
    if (fileInput.target.files[0].size > appConst.image_size.max) {
      let imgErr = t('EditShopInfoComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
      toast.error(imgErr);
    }
    else {
      bannerFileName.value = fileInput.target.files;
      bannerScale.value = 1;
      bannerTranslateX.value = 0;
      bannerTranslateY.value = 0;

      const reader = new FileReader();
      reader.onload = async (e: any) => {
        let imageFile = e.target;
        const image = new Image();
        // image.onload = function () {

        //   var canvas = document.createElement('canvas');
        //   var ctx = canvas.getContext('2d') as any;
        //   var width = image.width;
        //   var height = image.height;
        //   var aspectRatio = width / height;
        //   if (width > height) {
        //     if (width > 2000) {
        //       width = 2000;
        //       height = width / aspectRatio;
        //     }
        //   } else {
        //     if (height > 2000) {
        //       height = 2000;
        //       width = height * aspectRatio;
        //     }
        //   }
        //   canvas.width = width;
        //   canvas.height = height;
        //   ctx.drawImage(image, 0, 0, width, height);

        //   // EXIF.getData(fileInput.target.files[0], (data:any)=>{
        //   // })

        //   banner.value = canvas.toDataURL(imageFile.type);
        // }

        image.src = e.target.result;
        banner.value = e.target.result;
        let orientationExif;
        if (fileInput.target.files[0].type != 'image/webp') {
          orientationExif = await exifr.orientation(image) || 0;
        }
        else orientationExif = 0;
        orientationBanner.value = orientationExif ? orientationExif : 0;

        bannerFileName.value = fileInput.target.files[0].name;
      };
      await reader.readAsDataURL(fileInput.target.files[0]);
    }
  }
}
function emailValidation() {
  let re = appConst.validateValue.email;
  if (email.value.length) {
    if (!re.test(email.value)) {
      emailErr.value = t('EditShopInfoComponent.email_khong_dung_dinh_dang');
      return;
    } else {
      emailErr.value = "";
    }
  }
}
function phoneValidation() {
  let re = appConst.validateValue.phone;
  if (!validPhone(phone.value)?.length) {
    phoneErr.value = t('EditShopInfoComponent.vui_long_nhap_sdt');
    return;
  }
  if (!re.test(validPhone(phone.value))) {
    phoneErr.value = t('EditShopInfoComponent.sdt_khong_dung');
    return;
    // } else if(!phoneConfirmed.value) {
    //   phoneErr.value = "SĐT chưa được xác thực";
    //   return;
  }
  else {
    phoneErr.value = "";
  }
}

function getBannerStyle() {
  if (shopData.value.banner && shopData.value.banner.style?.length) {
    let styleArr = shopData.value.banner.style.split(" ");

    bannerScale.value = parseFloat(styleArr[0].replace(/[^\d\.]*/g, ''));
    bannerTranslateX.value = parseFloat(styleArr[1].replace(/[^\d\.\-]*/g, ''));
    bannerTranslateY.value = parseFloat(styleArr[2].replace(/[^\d\.\-]*/g, ''));
  }
  return;
}
function getLogoStyle() {
  if (shopData.value.logo && shopData.value.logo.style?.length) {
    let styleArr = shopData.value.logo.style.split(" ");

    logoScale.value = parseFloat(styleArr[0].replace(/[^\d\.]*/g, ''));
    logoTranslateX.value = parseFloat(styleArr[1].replace(/[^\d\.\-]*/g, ''));
    logoTranslateY.value = parseFloat(styleArr[2].replace(/[^\d\.\-]*/g, ''));
  }
  return;
}
</script>

<style lang="scss" src="./EditShopInfoV2Styles.scss"></style>
