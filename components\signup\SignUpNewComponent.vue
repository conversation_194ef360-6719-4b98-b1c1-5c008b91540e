<template>
  <div class="public-container">
    <div class="signup-container">
      <div class="login-btn">
        <!-- <button class="back-home" v-on:click="() => { close(); }">
          <Icon name="lucide:chevron-left" /> {{ $t('SignupComponent.ve_trang_chu') }}
        </button> -->
        <nuxt-link :to="appRoute.LoginComponent">{{ $t('SignupComponent.dang_nhap') }}</nuxt-link>
      </div>
      <span class='signup-title'>
        {{ $t('SignupComponent.dang_ky') }}
      </span>
      <span class='signup-slogan'>
        {{ $t('SignupComponent.chao_mung_ban_tham_gia_vao_re_ma_gan') }}
      </span>
      <div class="signup-protocol">
        <button class="signup-protocol-btn active"
          v-on:click="() => { protocol = protocol == 'phone' ? 'email' : 'phone' }">
          {{ protocol == 'phone' ? $t('SignupComponent.dang_ky_bang_email') :
            $t('SignupComponent.dang_ky_bang_so_dien_thoai') }}
        </button>
      </div>

      <div class="content-container" id="confirm_phone">
        <div class='v-stack' v-if="protocol == 'phone'">
          <span class='label'>
            {{ $t('SignupComponent.so_dien_thoai') }}
          </span>
          <div class="content-input-group">
            <button class="nation-code">
              +84
            </button>
            <input :title="$t('SignupComponent.so_dien_thoai')" type="phone" maxLength=255 class='content-input'
              autocomplete="off" :placeholder="$t('SignupComponent.nhap_so_dien_thoai')" v-model="phone" v-on:input="($event: any) => {
                phone = validPhone($event.currentTarget.value);
                phoneValidation();
              }" v-on:blur="() => { phoneValidation(); }" />
          </div>
          <span class='error-message'>{{ phoneErr }}</span>
        </div>
        <div class='v-stack' v-if="protocol == 'phone'" v-show="phone?.length">
          <span class='label'>
            {{ $t('SignupComponent.ma_xac_thuc_dien_thoai_di_dong') }}
          </span>
          <div class="content-input-group">
            <input :title="$t('SignupComponent.ma_xac_thuc_so_dien_thoai')" type="phone" maxLength=255
              class='content-input' :placeholder="$t('SignupComponent.nhap_ma_xac_thuc')" v-model="phoneConfirmCode"
              v-on:input="($event: any) => {
                phoneConfirmCode = $event.currentTarget.value;
                phoneConfirmValidation();
              }" />
            <button class="send-code" :disabled="phoneErr?.length > 0 || isSignuping"
              v-on:click="() => { sendCodeOTP() }" v-if="!time_remaining">{{ $t('SignupComponent.lay_ma') }}</button>

            <button v-if="time_remaining > 0" class="send-code" disabled>{{ $t('SignupComponent.lay_lai_sau') }}&nbsp;
              <vue-countdown :time="time_remaining * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                time_remaining = 0;
              }" v-on:progress="({ totalSeconds }) => { }">
                {{ minutes ? minutes + $t('SignupComponent.phut') : '' }} {{ seconds }} {{ $t('SignupComponent.giay') }}
              </vue-countdown>
            </button>
          </div>

          <span class='h-stack'>
            <span class="error-message">{{ phoneConfirmCodeErr }}</span>
            <label class='checkbox-input-label'>
              <v-switch v-model="agent" flat color="var(--primary-color-1)" hide-details
                class="my-switches"></v-switch>
              <span>{{ $t('SignupComponent.lay_ma_qua') }} {{ agent ? $t('SignupComponent.zalo') :
                $t('SignupComponent.sms') }}</span>
            </label>
          </span>
        </div>
        <div class='v-stack' v-if="protocol == 'email'">
          <span class='label'>
            {{ $t('SignupComponent.dia_chi_email') }}
          </span>
          <div class="content-input-group">
            <input :title="$t('SignupComponent.dia_chi_email')" maxLength=255 class='content-input'
              :placeholder="$t('SignupComponent.nhap_email')" v-model="email" v-on:input="($event: any) => {
                email = nonAccentVietnamese($event.currentTarget.value);
                emailValidation();
              }" v-on:blur="() => { emailValidation(); }" />
          </div>
          <span class='error-message'>{{ emailErr }}</span>
        </div>
        <div class='v-stack' v-if="protocol == 'email'" v-show="email?.length">
          <span class='label'>
            {{ $t('SignupComponent.ma_xac_thuc_email') }}
          </span>
          <div class="content-input-group">
            <input :title="$t('SignupComponent.ma_xac_thuc_email')" type="phone" maxLength=255 class='content-input'
              :placeholder="$t('SignupComponent.nhap_ma_xac_thuc')" v-model="emailConfirmCode" v-on:input="($event: any) => {
                emailConfirmCode = $event.currentTarget.value;
                emailConfirmValidation();
              }" />
            <button class="send-code" :disabled="emailErr?.length > 0 || isSignuping"
              v-on:click="() => { sendCodeOTP() }" v-if="!time_remaining">{{ $t('SignupComponent.lay_ma') }}</button>
            <button v-if="time_remaining > 0" class="send-code" disabled>{{ $t('SignupComponent.lay_lai_sau') }}&nbsp;
              <vue-countdown :time="time_remaining * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                time_remaining = 0;
              }" v-on:progress="({ totalSeconds }) => { }">
                {{ minutes ? minutes + $t('SignupComponent.phut') : '' }} {{ seconds }} {{ $t('SignupComponent.giay') }}
              </vue-countdown>
            </button>
          </div>
          <span class='error-message'>{{ emailConfirmCodeErr }}</span>
        </div>
        <div class='v-stack'>
          <span class='label'>
            {{ $t('SignupComponent.mat_khau') }}
          </span>
          <div class='content-input-group'>
            <input :title="$t('SignupComponent.mat_khau')" maxLength=255 class='content-input'
              :placeholder="$t('SignupComponent.nhap_mat_khau')" :type="passwordShow ? 'text' : 'password'"
              v-model="password" :disabled="isSignuping" v-on:input="($event: any) => {
                password = $event.currentTarget.value;
                passwordValidation();
              }" v-on:blur="() => { passwordValidation(); }" />
            <button v-on:click="() => { passwordShow = !passwordShow }">
              <Icon name="mage:eye-closed" v-if="!passwordShow" />
              <Icon name="mage:eye" v-if="passwordShow" />
            </button>
          </div>
          <span class="error-message" :class="{ 'success': passwordErr1 == false }">
            <Icon name="ph:dot-outline-fill" v-if="passwordErr1"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('SignupComponent.phai_chua_so_chu_cai_ky_tu_dac_biet') }}&nbsp;
            <v-tooltip location="bottom" open-on-click
              :text="`${$t('SignupComponent.ky_tu_dac_biet')}: !@#$%^&*()_+-=[]{};:|,.<>?\\`">
              <template v-slot:activator="{ props }">
                <span v-bind="props">
                  <Icon name="bi:question-circle"></Icon>
                </span>
              </template>
            </v-tooltip>
          </span>
          <span class="error-message" :class="{ 'success': passwordErr2 == false }">
            <Icon name="ph:dot-outline-fill" v-if="passwordErr2"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('SignupComponent.phai_tu_6_20_ky_tu') }}
          </span>
        </div>

        <div class='v-stack' v-show="password && password?.length > 0">
          <span class='label'>
            {{ $t('SignupComponent.xac_nhan_mat_khau') }}
          </span>
          <div class='content-input-group'>
            <input :title="$t('SignupComponent.mat_khau')" maxLength=255 class='content-input'
              :placeholder="$t('SignupComponent.nhap_lai_mat_khau')" :type="passwordConfirmShow ? 'text' : 'password'"
              :value="passwordConfirmation" :disabled="isSignuping" v-on:input="($event: any) => {
                passwordConfirmation = $event.currentTarget.value;
                confirmPasswordValidation();
              }" v-on:blur="() => { confirmPasswordValidation(); }" />
            <button v-on:click="() => { passwordConfirmShow = !passwordConfirmShow }">
              <Icon name="mage:eye-closed" v-if="!passwordConfirmShow" />
              <Icon name="mage:eye" v-if="passwordConfirmShow" />
            </button>
          </div>
          <span class='error-message'>{{ passwordConfirmationErr }}</span>
        </div>
        <div class="v-stack" v-if="false">
          <span class="label">{{ $t('SignupComponent.ten_dang_nhap') }}</span>
          <div class='content-input-group'>
            <input :title="$t('SignupComponent.ten_dang_nhap')" :maxLength="255" autoComplete="new-password"
              class="content-input" :placeholder="$t('SignupComponent.username_a')" :value="userName"
              :disabled="isSignuping" v-on:input="($event: any) => {
                userName = nonAccentVietnamese($event.currentTarget.value);
                if (!userName || !userName?.length) {
                  userNameErr = $t('SignupComponent.vui_long_nhap_ten_dang_nhap');
                } else {
                  userNameValidation();
                }
              }" v-on:blur="() => {
              if (!userName || !userName?.length) {
                userNameErr = $t('SignupComponent.vui_long_nhap_ten_dang_nhap');
              } else {
                userNameValidation();
              }
            }" />
          </div>
          <span class="error-message">{{ userNameErr }}</span>
        </div>
        <div class="v-stack">
          <span class="label">{{ $t('SignupComponent.ten') }}</span>
          <div class="content-input-group">
            <input :title="$t('SignupComponent.ten')" :maxLength="255" autoComplete="new-password"
              class="content-input" :placeholder="$t('SignupComponent.ten_placeholder')" :value="name"
              :disabled="isSignuping" v-on:input="($event: any) => {
                name = $event.currentTarget.value;
                nameValidation();
              }" v-on:blur="() => { nameValidation(); }" />
          </div>
          <span class="error-message">{{ nameErr }}</span>
        </div>

        <button class='button-action' :disabled="isSignuping" v-on:click="() => { signup() }">
          {{ $t('SignupComponent.dang_ky') }}
        </button>
      </div>
      <div class="contact">
        {{ $t('SignupComponent.ban_can_ho_tro_them') }} <nuxt-link :to="appRoute.AboutComponent">{{
          $t('SignupComponent.lien_he_voi_chung_toi') }}</nuxt-link>
      </div>
      <VueFinalModal class="my-modal-container" content-class="modal-content-container v-stack" :click-to-close="false"
        v-model="showModalConfirmOTP" v-on:closed="() => { showModalConfirmOTP = false }" :overlay-behavior="'persist'"
        contentTransition="vfm-slide-up">
        <OTPConfirmComponent :data="protocol == 'phone' ? phone : email" :type="protocol" v-on:close="(e: any) => {
          if (e) {
            if (protocol == 'phone') {
              phoneConfirmed = true;
              phoneConfirmedErr = '';
            } else if (protocol == 'email') {
              emailConfirmed = true;
              emailConfirmedErr = '';
            }
            showModalConfirmOTP = false;
            step = 2;
          } else {
            showModalConfirmOTP = false;
          }
        }"></OTPConfirmComponent>
      </VueFinalModal>
    </div>
  </div>


</template>

<script lang="ts" setup>
const { t } = useI18n();
useSeoMeta({
  title: t('AppRouteTitle.SignupComponent'),
});
const router = useRouter();
var route = useRoute();
// var props = defineProps({
// 	categoryData: "" as any,
// 	dataCategories: {}
// })

var emit = defineEmits(["close"]);

import { toast } from "vue3-toastify";
import { appConst, encryptPassword, nonAccentVietnamese, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { VueFinalModal } from 'vue-final-modal';
import { AuthService } from "~/services/authService/authService";
import { UserService } from "~/services/userService/userService";
import VueCountdown from '@chenfengyuan/vue-countdown';
import OTPConfirmComponent from "../otpConfirm/OTPConfirmComponent.vue";
import icon_square from "~/assets/image/icon_square_circle_300x300.png";
import { PublicService } from "~/services/publicService/publicService";
import { HttpStatusCode } from "axios";
var authService = new AuthService();
var userService = new UserService();
let publicService = new PublicService();

var protocol = useState('login_protocol', () => {
  return 'phone'
})
var time_remaining = useState('otp_cooldown_register', () => { return 0 });
var step = ref(1);

var name = ref("");
var nameErr = ref("");
var email = ref("");
var emailErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var phoneConfirmed = ref(false);
var phoneConfirmCode = ref(null as any);
var phoneConfirmedErr = ref("");
var phoneConfirmCodeErr = ref("");
var emailConfirmed = ref(false);
var emailConfirmCode = ref(null as any);
var emailConfirmedErr = ref("");
var emailConfirmCodeErr = ref("");
var userName = ref("");
var userNameErr = ref("");
var password = ref("");
// var passwordErr = ref("");
var passwordErr1 = ref(true);
var passwordErr2 = ref(true);
var passwordConfirmation = ref("");
var passwordConfirmationErr = ref("");
var passwordShow = ref(false);
var passwordConfirmShow = ref(false);
var timeout = ref(null);
var gender = ref(null);
var agent = ref(true);
var dateOfBirth = ref("");
var isSignuping = ref(false);

var showModalConfirmOTP = ref(false);

var checkExistingUserNameTimeout: any;
var checkExistingEmailTimeout: any;
var checkExistingPhoneTimeout: any;

function validateForm() {
  if (protocol.value == 'email') {
    emailValidation();
    emailConfirmValidation();
  }
  else {
    emailErr.value = "";
  }
  if (protocol.value == 'phone') {
    phoneValidation();
    phoneConfirmValidation();
  }
  else {
    phoneErr.value = "";
  }

  passwordValidation();
  confirmPasswordValidation();
  userNameValidation();
  nameValidation();

  if (
    (protocol.value == 'phone' ? phoneConfirmCode.value?.length : true) &&
    (protocol.value == 'email' ? emailConfirmCode.value?.length : true) &&
    !emailErr.value?.length &&
    !phoneErr.value?.length &&
    !passwordErr1.value &&
    !passwordErr2.value &&
    !passwordConfirmationErr.value?.length &&
    !nameErr.value?.length
  ) return true;
  return false;
}
// function checkSignupButton() {
//   if (
//     (protocol.value == 'phone' ? phoneConfirmCode.value?.length : true) &&
//     (protocol.value == 'email' ? emailConfirmCode.value?.length : true) &&
//     (protocol.value == 'email' ? !emailErr.value.length : true) &&
//     (protocol.value == 'phone' ? !phoneErr.value.length : true) &&
//     !passwordErr1.value &&
//     !passwordErr2.value &&
//     !passwordConfirmationErr.value.length &&
//     !nameErr.value.length
//   ) return true;
//   return false;
// }
async function signup() {

  if (
    await validateForm() == true
  ) {
    isSignuping.value = true;
    let body;
    if (protocol.value == 'phone') {
      body = {
        otp: phoneConfirmCode.value,
        userName: userName.value,
        password: await encryptPassword(password.value),
        password_confirmation: await encryptPassword(passwordConfirmation.value),
        name: name.value,
        phone: validPhone(phone.value),
        gender: gender.value,
        date_of_birth: dateOfBirth.value,
        agent: agent.value ? 'zalo' : 'phone'
      }
    }
    else {
      body = {
        otp: emailConfirmCode.value,
        userName: userName.value,
        password: await encryptPassword(password.value),
        password_confirmation: await encryptPassword(passwordConfirmation.value),
        name: name.value,
        email: email.value,
        gender: gender.value,
        date_of_birth: dateOfBirth.value,
        agent: 'email'
      }
    }
    let signup = await authService.signup(body);
    if (signup.status == HttpStatusCode.Ok) {
      // Alert.alert("đăng ký thành công:");
      try {
        toast.success(t('SignupComponent.dang_ky_thanh_cong'));
        isSignuping.value = false;
      }
      finally {
        await router.push({
          path: appRoute.LoginComponent,
          state: {
            phone: protocol.value == 'phone' ? validPhone(phone.value) : '',
            email: protocol.value == 'email' ? email.value : '',
            password: password.value,
            protocol: protocol.value
          }
        });
      }
    } else {
      isSignuping.value = false;
      // toast.error("Đăng ký thất bại\n");

      let indexPhoneFailed = signup.errors.findIndex(function (er: any) {
        return er.field == 'phone'
      })
      if (indexPhoneFailed != -1) {
        phoneErr.value = signup.errors[indexPhoneFailed]?.error_message.replaceAll("phone", t('SignupComponent.so_dien_thoai'))
      }

      let indexUserNameFailed = signup.errors.findIndex(function (er: any) {
        return er.field == 'user_name'
      })
      if (indexUserNameFailed != -1) {
        userNameErr.value = signup.errors[indexUserNameFailed]?.error_message.replaceAll("user_name", t('SignupComponent.ten_dang_nhap'))
      }

      let indexNameFailed = signup.errors.findIndex(function (er: any) {
        return er.field == 'name'
      })
      if (indexNameFailed != 1) {
        nameErr.value = signup.errors[indexNameFailed]?.error_message.replaceAll("name", t('SignupComponent.ten'))
      }

      let indexOTPFailed = signup.errors.findIndex(function (er: any) {
        return er.field == 'otp'
      });
      
      if (indexOTPFailed != -1) {
        if (protocol.value == 'phone') {
          phoneConfirmCodeErr.value = t('SignupComponent.ma_xac_thuc_sai');
          emailConfirmCodeErr.value = "";
        }
        if (protocol.value == 'email') {
          emailConfirmCodeErr.value = t('SignupComponent.ma_xac_thuc_sai');
          phoneConfirmCodeErr.value = "";
        }
      }
      hightlightError();

    }
  }
  else {
    // toast.error("Đăng ký thất bại\nVui lòng kiểm tra lại");
    hightlightError();
    isSignuping.value = false;
  }

}
function nameValidation() {
  if (!name.value?.length) {
    nameErr.value = t('SignupComponent.vui_long_nhap_ten');
  } else {
    nameErr.value = "";
  }
}
function userNameValidation() {
  let re = appConst.validateValue.userName;
  if (!re.test(userName.value)) {
    userNameErr.value = t('SignupComponent.ten_dang_nhap_regex');
  } else {
    userNameErr.value = "";
    clearTimeout(checkExistingUserNameTimeout);
    checkExistingUserNameTimeout = setTimeout(() => {
      checkExistingUserName();
    }, 500);
  }
}
async function checkExistingUserName() {
  let body = {
    key: userName.value,
    type: appConst.check_exist_enum.user_name,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    userNameErr.value = t('SignupComponent.ten_dang_nhap_da_duoc_su_dung');
  } else {
    userNameErr.value = "";
  }
}
function phoneValidation() {
  let re = appConst.validateValue.phone;
  if (!validPhone(phone.value)?.length) {
    phoneErr.value = t('SignupComponent.vui_long_nhap_so_dien_thoai');
    return;
  }
  if (phone.value[0] != '0' && phone.value[0] != '+') {
    phone.value = "0".concat(phone.value)
  }
  if (!re.test(validPhone(phone.value))) {
    phoneErr.value = t('SignupComponent.so_dien_thoai_khong_dung');
    return;
  }
  else {
    clearTimeout(checkExistingPhoneTimeout);
    checkExistingPhoneTimeout = setTimeout(() => {
      checkExistingPhone();
    }, 500);
  }
}
async function checkExistingPhone() {
  let body = {
    key: validPhone(phone.value),
    type: appConst.check_exist_enum.phone,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    phoneErr.value = t('SignupComponent.sdt_da_duoc_su_dung');
  } else {
    phoneErr.value = "";
  }
}
function emailValidation() {
  let re = appConst.validateValue.email;
  if (email.value?.length) {
    if (!re.test(email.value)) {
      emailErr.value = t('SignupComponent.email_khong_dung_dinh_dang');
      return;
    } else {
      clearTimeout(checkExistingEmailTimeout);
      checkExistingEmailTimeout = setTimeout(() => {
        checkExistingEmail();
      }, 500);
    }
  }
  else {
    emailErr.value = t('SignupComponent.vui_long_nhap_email');
  }
}
function emailConfirmValidation() {
  if (!emailConfirmCode.value || !emailConfirmCode.value?.length) {
    emailConfirmCodeErr.value = t('SignupComponent.vui_long_nhap_ma_xac_thuc');
  } else {
    emailConfirmCodeErr.value = "";
  }
}
function phoneConfirmValidation() {
  if (!phoneConfirmCode.value || !phoneConfirmCode.value?.length) {
    phoneConfirmCodeErr.value = t('SignupComponent.vui_long_nhap_ma_xac_thuc');
  } else {
    phoneConfirmCodeErr.value = "";
  }
}
// function passwordValidation() {
//   if (!password.value.length) {
//     passwordErr.value = "Vui lòng nhập mật khẩu";
//   } else {
//     let re = appConst.validateValue.password;
//     if (password.value.length && !re.test(password.value)) {
//       passwordErr.value =
//         "Mật khẩu có ít nhất 8 ký tự, phải bao gồm chữ và số và không được có khoảng trắng";
//       return;
//     } else {
//       passwordErr.value = "";
//     }
//   }
// }

function confirmPasswordValidation() {
  if (!passwordConfirmation.value || !passwordConfirmation.value?.length) {
    passwordConfirmationErr.value = t('SignupComponent.vui_long_nhap_mat_khau_xac_nhan');
  } else if (passwordConfirmation.value != password.value) {
    passwordConfirmationErr.value = t('SignupComponent.nhap_lai_mat_khau_khong_khop');
  } else {
    passwordConfirmationErr.value = "";
  }
}
async function checkExistingEmail() {
  let body = {
    key: email.value,
    type: appConst.check_exist_enum.email,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    emailErr.value = t('SignupComponent.email_da_duoc_su_dung');
  } else {
    emailErr.value = "";
  }
}

function passwordValidation() {
  let re = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{1,}$/;
  if (!re.test(password.value)) {
    passwordErr1.value = true;
  }
  else {
    passwordErr1.value = false;
  }
  if (password.value?.length < 6 || password.value?.length > 20) {
    passwordErr2.value = true;
  }
  else {
    passwordErr2.value = false;
  }

}
function close() {
  router.push(appRoute.HomeComponent);
}

function sendCodeOTP() {
  isSignuping.value = true;
  publicService.getOTP(
    protocol.value == 'phone' ? validPhone(phone.value) : email.value,
    protocol.value,
    agent.value
  ).then(res => {
    if (res.status == HttpStatusCode.Ok) {
      time_remaining.value = res.body?.data?.otp_cooldown;
    }
    else {
      time_remaining.value = res.otp_cooldown;
    }
    isSignuping.value = false;
  })
}


function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    // Do stuff here
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}
</script>

<style lang="scss" src="./SignUpNewStyles.scss"></style>
