.public-container.h-fit.hide {
  height: 0;
  overflow: hidden;
}
.footer-container {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  box-shadow: 0px 0px 7px -1px rgb(0, 0, 0, 0.2);
  z-index: 1000;
  margin-top: auto;
  max-width: var(--max-width-view);
  width: 100%;
  // max-height: 50px;

  .footer-button {
    flex: 1;
    border: none;
    background: white;
    color: var(--color-text-note);
    font-size: 1em;
    display: flex;
    height: 100%;
    min-height: 65px;
    max-height: 65px;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    padding-bottom: 10px;
    // padding: 10px 0 10px;
    font-weight: bold;
    overflow: visible;
  
    > img {
      width: 2em;
      height: 2em;
      filter: grayscale(1);
    }
  
    & > em {
      border-radius: 2em;
      color: white;
      background: var(--color-button-error);
      min-width: 20px;
      height: 20px;
      font-size: 0.8em;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-style: normal;
      position: absolute;
      top: 5px;
      right: 20%;
      line-height: 1;
      box-shadow: 0 0 0px 2px white;
      font-weight: 500;
  
      & > span {
        font-size: 0.8em;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .footer-button > svg {
    font-size: 2em;
  }
  .footer-button > span {
    font-size: 0.8em;
    display: flex;
  }
  .footer-button.active:not(.around-button) {
    /* background-color: var(--primary-color-1); */
    color: var(--primary-color-1);
    box-shadow: inset 0px 3px 0px -1px;

    & > img {
      filter: grayscale(0);
    }
  }
  .footer-button.active.around-button {
    /* background-color: var(--primary-color-1); */
    color: var(--primary-color-1);

    & > img {
      filter: grayscale(0);
    }
  }
  .search-footer-button {
    padding: 0;
    width: 40px;
    height: 40px;
    color: white;
    background: linear-gradient(
      to bottom right,
      #f8a482 30%,
      var(--primary-color-1)
    );
    border-radius: 50%;
    padding: 5px;
    // border: 5px solid transparent;
    margin-top: -15px;
    z-index: 2;
    // position: absolute;
    // top: -10px;
  }

  .wave {
    // position: absolute;
    // top: 0%;
    // left: 50%;
    // background: white;
    // box-shadow: -5px -5px 5px -5px rgba(0, 0, 0, 0.2);
    // width: 100%;
    // height: 100%;
    // width: 40px;
    // border-radius: 46% 0px;
    // transform: translate(-50%, -50%) rotate(45deg) skew(-15deg, -15deg);
    position: absolute;
    top: -25%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 0;
    width: 100px;
    max-width: 100%;

    & > img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  @media screen and (max-width: 481px) {
    .footer-button {
      min-height: 55px !important;
      max-height: 55px !important;
      padding-bottom: 0 !important;
    }
    .wave {
      top: -23.5% !important;
    }
  }
  @keyframes shakeCart {
    25% {
      transform: translateX(6px);
    }
    50% {
      transform: translateX(-4px);
    }
    75% {
      transform: translateX(2px);
    }
    100% {
      transform: translateX(0);
    }
  }
  .shake {
    animation: shakeCart 0.4s ease-in-out forwards;
  }
}
.footer-container > a {
  width: 100%;
  text-align: center;
  text-decoration: none;
  display: flex;
}
