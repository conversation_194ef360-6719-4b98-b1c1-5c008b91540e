
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class MaterialService extends BaseHTTPService {

  add(name = "", name_en = "", packing = "", brand ="", origin = "", unit = "", barcode = ""){
    let body = {
      name: name,
      name_en: name_en,
      packing: packing,
      brand: brand,
      origin: origin,
      unit: unit,
      barcode: barcode
    }
    let url = appConst.apiURL.materialAdd
    // let url = "http://clomart.xampps/api/v1/material/add";
    return this.https("POST", url, body);
  }

  list(){
    let url = appConst.apiURL.materialList
    // let url = "http://clomart.xampps/api/v1/material/list";
    return this.https("GET", url);
  }
}