.save-data-before-leave-container{
    padding: 10px;
    background: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
    width: 500px;
    max-width: 90dvw;
    position: relative;

    & > .close-button{
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 20px;
        
    }
    & > h4{
        text-align: center;
        font-weight: 700;
        font-size: 20px;
        color: #545454;
    }

    & > span{
        font-size: 17px;
        font-weight: 500;
    }

    & > .actions {
        font-size: 15px;
        font-weight: 500;
        color: #545454;
        padding: 5px 0;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        width: 100%;

        & > button{
            border: thin solid;
            padding: 5px 15px;
            border-radius: 2em;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 150px;
            font-weight: 600;

            &.leave{
                color: #7e7e7e;
            }

            &.save-and-leave{
                color: white;
                background: var(--primary-color-1);
                border: thin solid var(--primary-color-1);
            }
        }
    }

}