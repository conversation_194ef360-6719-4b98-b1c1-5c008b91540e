.signup-container,
.skeleton-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  padding: 0 25px 10px 25px;
  overflow: auto;
  font-size: 17px;
  max-width: 720px !important;

  .login-btn {
    text-align: right;
    color: var(--primary-color-1);
    font-weight: 600;
    // margin-bottom: 10px;
    margin-left: auto;
    display: flex;
    align-items: center;
  
    padding: 10px 0;
    background: white;

    & > .back-home {
      display: flex;
      align-items: center;
      font-size: 15px;
      margin-right: auto;
      color: #414141;
      & > svg {
        font-size: 30px;
      }
    }
  }
  .signup-title {
    font-size: 1.7em;
    font-weight: 800;
    text-align: left;
  }

  .signup-slogan {
    font-size: 1em;
    font-weight: 500;
    margin-left: 0;
    text-align: left;
  }

  .signup-protocol {
    display: flex;
    gap: 25px;
    margin: 30px 0 0;
    justify-content: flex-end;

    & > .signup-protocol-btn {
      color: #7b7b7b;
      font-weight: 600;
    }

    & > .signup-protocol-btn.active {
      color: var(--primary-color-1);
    }
  }
  .tab-content-container{
    flex: 1;
    display: flex;
    > div {
        flex: 1
    }
  }
  .content-container {
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease-in-out;
    & > .v-stack {
      margin-top: 10px;
    }
    & > .v-stack:last-of-type {
      margin-bottom: 15px;
    }
    .content-input {
      background: transparent;
      border: none;
      outline: none;
      padding: 10px 0;
      border-radius: inherit;
    }
    & .checkbox-input-label {
      gap: 5px;
      display: flex;
      cursor: pointer;
      user-select: none;
      font-weight: 500;
      color: var(--primary-color-1);
      font-size: 20px;     
      align-items: center;
      justify-content: flex-end;   
      margin-left: auto;

      & span {
        font-size: 17px;
        color: var(--primary-color-1);
      }

      & em {
        color: var(--primary-color-1);
        font-size: 14px;
        line-height: normal;
        align-self: center;
      }
    }
    .label {
      color: var(--color-text-black);
      margin: 5px 0;
      font-size: 15px;
    }
    .content-input-group:focus-within {
      border-color: var(--primary-color-1) !important;
    }
    .content-input-group:hover{
      border-color: var(--color-text-black);
    }
    .content-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border: thin solid var(--color-text-note);
      padding-left: 10px;
      border-radius: 5px;

      & > input {
        flex: 1;
        overflow: auto;
      }
      & > input:-webkit-autofill {
        background-color: initial !important;
      }
      & > button {
        background: transparent;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: var(--primary-color-1);
      }

      & > button.nation-code {
        color: var(--color-text-black);
        height: 20px;
        width: fit-content;
        padding-right: 15px;
        margin-right: 15px;
        border-right: thin solid #ddd;
        border-radius: 0;
      }

      & > button.send-code{
        width: fit-content;
        min-width: fit-content;
        white-space: nowrap;
        margin: 0 10px;
        font-weight: 700;
        font-size: 15px;
      }
    }

    .forget-password-text {
      font-size: 1em;
      font-weight: 500;
      align-self: flex-end;
      margin-top: 5px;
      cursor: pointer;
    }

    & .button-action {
      margin-top: 15px;
      margin-bottom: 15px;
      background-color: var(--primary-color-1);
      border-radius: 5px;
      padding: 10px;
      position: relative;
      text-align: center;
      border: none;
      color: white;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1em;
      line-height: 1;
      height: fit-content;
      text-transform: none;

      img {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }
    }
    & > .button-action:disabled {
      // background-color: #a7a7a7;
      opacity: 0.4;
    }

    & > .back-step {
      color: var(--primary-color-1);
    }

    & .error-message.success{
      font-style: normal;
      color: green;
    }
    @keyframes high-light {
      50% {
        opacity: 0.0;
      }
    }
    & .error-message.hight-light:not(.success){
      transform-origin: 0 0;
      animation: high-light .5s ease-in-out infinite;
    }
  }

  .others-protocol {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    & > span {
      width: 100%;
      display: flex;
      gap: 10px;
      justify-content: center;
      text-align: center;
      color: #a1a1a1;

      & > .line {
        flex: 1;
        height: 1px;
        background: #ccc;
        margin: auto;
      }
    }

    & > .button-action {
      margin-top: 15px;
      background-color: white;
      border: thin solid #ccc;
      border-radius: 5px;
      padding: 10px;
      position: relative;
      text-align: center;
      color: var(--primary-color-1);
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1em;
      line-height: 1;
      height: fit-content;
      text-transform: none;
      width: 100%;

      img {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }
    }
    .button-action:disabled {
      // background-color: #a7a7a7;
      opacity: 0.4;
    }
  }

  .contact {
    width: 100%;
    text-align: center;
    margin-top: auto;
    margin-bottom: 15%;

    & > a {
      color: var(--primary-color-1);
    }
  }
}
