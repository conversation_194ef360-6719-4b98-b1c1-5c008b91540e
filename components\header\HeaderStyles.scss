.title-header {
  // font-size: 1.6em;
  padding: 5px 0;
  margin: 0;
  text-align: center;
  width: 100%;
  border-bottom: thin solid #ccc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  width: 100%;
  max-width: var(--max-width-view);
  max-height: 50px;

  & h3 {
    margin: 0;
  }

  & .header-left {
    display: flex;
    padding-left: 5px;
    justify-content: left;
    gap: 5px;
    flex: 1;
    // margin-right: auto;

    & > button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }

  & .header-right {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    flex: 1;
    padding-right: 5px;

    & > button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }
}