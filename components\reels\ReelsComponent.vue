<template>
  <div class="reels-container">
    <Swiper v-if="reelsData?.length" @slideChange="(e: any) => {
      selectedReel = reelsData[e.activeIndex];
      currentIndex = e.activeIndex;
      if (e.activeIndex >= reelsData?.length - 2) {
        getAReel()
      }
    }" :initialSlide="currentIndex" @init="() => {
      selectedReel = reelsData[0];
    }" :spaceBetween="10" :direction="'vertical'" class="my-carousel stack-carousel" :modules="[]" :slides-per-view="1"
      :loop="false" :effect="'creative'" :navigation="false" :autoplay="false" key="reels-carousel">
      <SwiperSlide v-for="(itemReel, index) of reelsData" :key="'reel_' + itemReel?.id">
        <div class="item-stack-slide">
          <img loading="lazy" :src="itemReel && itemReel.profile_picture
            ? domainImage + itemReel.profile_picture
            : icon_for_product
            " :placeholder="icon_for_product" :alt="itemReel.name" />
          <div class="product-info">
            <span class="shop-name" v-show="itemReel.shop">{{ itemReel.shop?.name }}</span>
            <span class="name">{{ itemReel.name }}</span>
            <span class="price">
              {{
                (itemReel.price_off != null && itemReel.price_off < itemReel.price) ?
                  formatCurrency(parseFloat(itemReel.price_off), itemReel.shop ? itemReel.shop.currency :
                    itemReel.currency) : (parseFloat(itemReel.price) == 0 || itemReel.price == null) ?
                    $t('ReelsComponent.gia_lien_he') : formatCurrency(parseFloat(itemReel.price), itemReel.shop ?
                      itemReel.shop.currency : itemReel.currency) }} </span>
                <span class="origin-price" v-if="(itemReel.price_off != null && itemReel.price_off < itemReel.price)">
                  {{
                    (parseFloat(itemReel.price) == 0 || itemReel.price ==
                      null)
                      ? $t('ReelsComponent.gia_lien_he')
                      : formatCurrency(parseFloat(itemReel.price), itemReel.shop ?
                        itemReel.shop.currency : itemReel.currency)
                  }}
                </span>
                <button class="add-to-cart" v-on:click="async () => {
                  // await checkSelectedProductQuantity()
                  showSelectedReel = true;
                }">
                  {{ $t('ReelsComponent.them_vao_gio') }}
                </button>
          </div>

          <div class="share-button" v-if="false">
            <v-menu class="bootstrap-dropdown-container" location="bottom right">
              <template v-slot:activator="{ props }">
                <button class="share-button" dark v-bind="props" title="{{ $t('ReelsComponent.chia_se') }}">
                  <Icon name="ph:share-network-bold" class='share' />
                </button>
              </template>

              <v-list>
                <v-list-item key="copy_url" class="share-dropdown-item" v-on:click="() => {
                  copyToClipboard(domain + appRoute.ProductComponent + '/' + selectedReel?.id);
                }">
                  <v-list-item-title>{{ $t('ReelsComponent.sao_chep_dia_chi') }}</v-list-item-title>
                </v-list-item>
                <v-list-item key="share_to_facebook" class="share-dropdown-item" v-on:click="() => {
                  shareToFacebook()
                }">
                  <v-list-item-title>{{ $t('ReelsComponent.chia_se_qua_facebook') }}</v-list-item-title>
                </v-list-item>
                <v-list-item key="share_to_zalo" class="share-dropdown-item" v-if="!webInApp" v-on:click="() => {
                  shareToZalo();
                }">
                  <v-list-item-title>{{ $t('ReelsComponent.chia_se_qua_zalo') }}</v-list-item-title>
                </v-list-item>
                <v-list-item key="share_to_app" class="share-dropdown-item" v-if="webInApp" v-on:click="() => {
                  shareToApp();
                }">
                  <v-list-item-title>{{ $t('ReelsComponent.khac') }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>

    <v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained content-class='spinner-container'
      persistent scrim="#fff" key="loading" no-click-animation>
      <Icon name="eos-icons:loading"></Icon>
    </v-overlay>

    <div class="zalo-share-button" id="share_zalo_button" v-if="false"
      :data-href="domain + appRoute.ProductComponent + '/' + selectedReel?.id" :data-oaid="zaloConfig.appIDZaloLogin"
      :data-customize='true'>
      {{ $t('ReelsComponent.chia_se_qua_zalo') }}
    </div>
  </div>

  <AddProductToCartComponent v-if="showSelectedReel" v-on:close="() => {
    showSelectedReel = false
  }" :selectedProduct="selectedReel">
  </AddProductToCartComponent>
</template>

<script lang="ts" setup>
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
  appConst,
  appDataStartup,
  baseLogoUrl,
  domain,
  domainImage,
  formatCurrency,
  formatNumber,
  zaloConfig
} from "~/assets/AppConst";
import appRoute from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import non_avatar from "~/assets/image/non-avatar.jpg";
import { PublicService } from "~/services/publicService/publicService";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import type { CartDto } from "~/assets/appDTO";

const router = useRouter();
const route = useRoute();
const nuxtApp = useNuxtApp();
const { t } = useI18n();

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var imageService = new ImageService();
var publicService = new PublicService();

var userLocation = ref();

var radius = ref(null as any);
var search = ref("" as any);

var cartData = ref();

var reelsData = useState('reelsData', () => { return [] as any });
var currentIndex = useState('currentIndex', () => { return 0 })
var selectedReel = ref();
var latitude = ref();
var longitude = ref();
var webInApp = ref(null as any);
var showSelectedReel = ref(false);
var showModalConfirmResetCart = ref(false);
var showModalConfirm18Age = ref(false);

var refreshing = ref(false);
useSeoMeta({
  title: t('AppRouteTitle.ReelsComponent'),
});

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
watch(() => [user_latitude.value, user_longitude?.value], () => {
  latitude.value = user_latitude?.value;
  longitude.value = user_longitude?.value;
});
onUnmounted(() => {
  nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onBeforeMount(async () => {
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})

onMounted(async () => {
  refreshing.value = true;

  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

  let cartData$ = JSON.parse(
    localStorage.getItem(appConst.storageKey.cart) as string
  );
  cartData.value = cartData$;

  // userLocation.value = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);
  await getCurrentLocationOfUser();
  if (!reelsData.value.length) {

    await getAReel();
    getAReel();
    getAReel();

  }

  loadScript()
  refreshing.value = false;
});

function getAReel() {
  publicService.reels(user_latitude.value, user_longitude.value, radius.value, search.value).then(res => {
    if (res.status == 200) {
      reelsData.value.push(res.body.data[0]);
    }

  }).catch((err) => {
  })
}

var getCurrentLocationOfUser = () => {
  return new Promise((resolve) => {

    // if ("geolocation" in navigator) {
    //   navigator.geolocation.getCurrentPosition(
    //     (position) => {
    //       latitude_user.value = position.coords.latitude.toString();
    //       longitude_user.value = position.coords.longitude.toString()
    //       resolve(true)
    //     },
    //     (error) => {
    //       latitude_user.value = appConst.defaultCoordinate.latitude.toString();
    //       longitude_user.value = appConst.defaultCoordinate.longitude.toString()
    //       resolve(true)
    //     },
    //     {
    //       enableHighAccuracy: false, // Use less accurate but faster methods
    //       timeout: 5000, // Set a timeout (in milliseconds)

    //     }
    //   );
    // }
    latitude.value = user_latitude.value;
    longitude.value = user_longitude.value;
    resolve(true)
  })
}

function copyToClipboard(text: string) {
  if (!webInApp.value) {
    window.navigator.clipboard.writeText(text);
  }
  else {
    nuxtApp.$emit(appConst.event_key.send_request_to_app, {
      action: appConst.webToAppAction.copyToClipboard,
      data: text
    })
  }
  toast.info(t('ReelsComponent.da_sao_chep_lien_ket'), {
    hideProgressBar: true,
    autoClose: 1000
  })
}
function loadScript() {
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = 'https://sp.zalo.me/plugins/sdk.js';
  script.async = false;
  script.defer = true;
  script.id = "zalo_share";
  document.body.appendChild(script);
}

function shareToZalo() {
  document.getElementById('share_zalo_button')?.click();
}
function shareToApp() {
  let titleShop = selectedReel.value.shop && selectedReel.value.shop.id
    ? selectedReel.value.shop.name + " - "
    : "";
  let messageShare = {
    message: titleShop + selectedReel.value.name + "\n",
    url: domain + "/product/" + selectedReel.value.id,
    filename: selectedReel.value.profile_picture ? domainImage + selectedReel.value.profile_picture : baseLogoUrl
  }

  nuxtApp.$emit(appConst.event_key.send_request_to_app, {
    action: appConst.webToAppAction.share,
    data: messageShare
  })
}
function shareToFacebook() {
  let url = domain + appRoute.ProductComponent + "/" + selectedReel.value.id;
  let hrefLinkShare = "https://www.facebook.com/sharer/sharer.php?u=" + url;
  var x = (window.outerWidth) / 2 - 350;
  var y = (window.outerHeight) / 2 - 300;
  var responsive = "width=700,height=600,left=" + x + ",top=" + y
  window.open(hrefLinkShare, 'name', responsive)
}

</script>

<style lang="scss" src="./ReelsStyles.scss"></style>
