// const { environment } = require("../src/assets/environment/environment");
// import { appRoute } from '../assets/appRoute';
importScripts(
  "https://www.gstatic.com/firebasejs/10.3.1/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/10.3.1/firebase-messaging-compat.js"
);

const initFirebase = async () => {
  try {
    const secretTokenResponse = await fetch('/api/get-firebase-secret-key');
    if (!secretTokenResponse.ok) throw new Error("Failed to fetch secret key");

    const secretToken = await secretTokenResponse.text();
    const response = await fetch("/api/firebase-config", {
      headers: { 'x-firebase-config-secret': secretToken }
    });

    if (!response.ok) throw new Error("Failed to fetch Firebase config");

    const firebaseConfig = await response.json();
    firebase.initializeApp(firebaseConfig);
    const messaging = firebase.messaging();

    messaging.onBackgroundMessage(async function (payload) {
      await clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
        clientList.forEach(client => {
          client.postMessage({ type: 'new-notification', payload });
        });
      });

      return await self.registration.showNotification(payload.data.title, {
        body: payload.data.body,
        icon: payload.data.icon,
        data: payload.data,
      });
    });

  } catch (error) {
    console.error("Error initializing Firebase:", error);
  }
};

// self.addEventListener('activate', (event) => {
//   event.waitUntil(
//     caches.keys().then((cacheNames) => {
//       return Promise.all(
//         cacheNames
//           .filter((cacheName) => cacheName.includes("firebase-messaging"))
//           .map((cacheName) => caches.delete(cacheName))
//       );
//     }).then(() => {
//       self.clients.claim();
      
//     }));
// });

// self.addEventListener("install", async () => {
//   self.skipWaiting();
// });
initFirebase();
self.addEventListener('notificationclick', (event) => {
  // Open a new window or focus an existing one
  event.waitUntil(
    clients.matchAll({ type: 'window' }).then((clientList) => {
      // mở tab mới
      if (clients.openWindow) {
        let url;
        let param = event.notification.data.url; // 
        let query;
        if (event.notification.data.target_url) {
          url = event.notification.data.target_url;
        }
        else {
          switch (event.notification.data.type) {
            case 'order_new':
            case 'order_failed':
            case 'order_cancel':
              url = '/my-shop/orders/:id';
              url = url.replaceAll(':id', param);
              break;

            case 'agent_order_new':
            case 'agent_order_failed':
            case 'agent_order_cancel':
              url = '/agent/order/:id';
              url = url.replaceAll(':id', param);
              break;

            case 'order_status':
            case 'order_status_in_process':
            case 'order_status_ready_to_deliver':
            case 'order_status_done':
            case 'order_status_cancel':
              url = '/my-orders/:id';
              url = url.replaceAll(':id', param);
              break;

            case 'delivery_new':
            case 'delivery':
              url = '/driver-tools/delivery-history/:id';
              url = url.replaceAll(':id', param);
              break;

            case 'message_new':
            case 'mesage_new':
              url = '/chat';
              query = event.notification.data.url;
              url = `${url}?channel=${query}`;
              break;

            case 'shop_message_new':
            case 'shop_mesage_new':
              url = '/my-shop/chat';
              query = event.notification.data.url;
              url = `${url}?channel=${query}`;
              break;

            case 'agent_message_new':
              url = '/agent/shop/:id/chat';

              let extra = JSON.parse(event.notification.data.extra);
              param = extra?.shop_id;
              url = url.replaceAll(':id', param);
              query = event.notification.data.url;
              url = `${url}?channel=${query}`;
              break;

            default:
              url = '/profile'

          }
        }

        return clients.openWindow(url);
      }
    })
  );
});