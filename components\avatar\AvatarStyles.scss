.avatar-container {
  --width-view: 200;
  border-radius: 50%;
  box-shadow: 0 0 1em rgb(0, 0, 0, 0.1);
  aspect-ratio: 1;
  object-fit: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  & > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .logo-origin-container {
    --width-origin: 200px;
    --height-origin: 200px;
    --scale-origin: 200;
    --width-view: 200px;

    transform: scale(calc(var(--width-view) / var(--scale-origin))) !important;
    width: var(--width-origin);
    height: var(--height-origin);
    // transform: scale(1);
    transform-origin: center;
    display: flex;
    background: white;
    align-items: center;
    justify-content: center;

    & > img {
      object-fit: cover;
      width: auto !important;
      height: auto !important;
      max-height: unset !important;
      min-height: unset !important;
    }
  }

  .logo-origin-container.none-style {
    width: 100%;
    height: 100%;
    transform: none !important;

    > img {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
    }
  }
}
