import { appConst } from "~/assets/AppConst";

export interface ChannelDTO {
  id?: string;
  name?: string | null;
  type: channel_type;
  avatar?: string;
  members: ChannelConnectDTO[];
  status?: number;
  latest_message?: MessageDTO;
  viewed?: boolean;
}

export interface ChannelConnectDTO {
  channel_id?: string | null;
  member_id: string | null;
  member_type: member_type | null;
  created_at?: string | null;
  updated_at?: string | null;
  member: Member | null;
}

export interface Member {
  id: string;
  name: string;
  email: string | null;
  role_id: member_role;
  created_by: string | null;
  phone: string | null;
  user_name: string;
  gender: boolean;
  date_of_birth: string | null;
  identity_card: string | null;
  province_id: string | null;
  district_id: string | null;
  ward_id: string | null;
  address: string | null;
  provider_name: provider_name | null;
  provider_id: string | null;
  profile_picture: string;
  is_new: boolean;
  enable: boolean;
  description: string | null;
  background_picture: string | null;
  custom_path: string;
  deleted_at: string | null;
  notes: string | null;
  latitude: string | null;
  longitude: string | null;
  total_rating: number | null;
  user_status: user_status;

  channel_id: string;
  created_at: string;
  member_id: string;
  member_type: member_type;
  updated_at: string;
  logo: any;
  banner: any;
}

export interface MessageDTO {
  id: string;
  channel_id: string;
  member_id: string;
  member_type: member_type;
  content: any;
  created_at?: string;
  updated_at?: string;
  send_err?: boolean;
  status?: any
}

export interface NotificationDTO {
  mess: {
    body?: string;
    collapseKey?: string;
    icon?: string;
    title?: string;
    type: string;
    url: string;
  },
  user_id?: string,
  topic?: string
}

export enum user_status {
  online = 1,
  offline = 0,
}

export enum member_type {
  user = "user",
  shop = "shop",
  agent = "agent_shop"
}

export enum member_role {
  admin = appConst.role_enum.admin,
  user = appConst.role_enum.user,
  owner = appConst.role_enum.owner,
  agent = appConst.role_enum.agent,
  driver = appConst.role_enum.driver,
}

export enum provider_name {
  google = appConst.provider_name.google as any,
  zalo = appConst.provider_name.zalo as any,
  apple = appConst.provider_name.apple as any,
}

export enum channel_type {
  user = 1,
  group = 2,
}
