<template>
	<div class="public-container h-fit">
		<div class="footer-container">
			<button class='footer-button' :class="{ 'active': checkActive() == appRoute.HomeComponent }" v-on:click="async () => {
				// let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.HomeComponent) as string);
				// if (checkActive() != appRoute.HomeComponent && checkActive() != (appRoute.HomeComponent + '_discovery')) {
				// 	// router.push(appRoute.HomeComponent);

				// 	await router.push({
				// 		path: appRoute.HomeComponent,
				// 		query: {
				// 			isShowList: 'true'
				// 		}
				// 	})
				// 	nuxtApp.$emit('home_click', true)
				// }
				// else {
				// 	nuxtApp.$emit('home_click', true)
				// }
				await router.push({
					path: appRoute.HomeComponent
				})
			}">
				<!-- <Icon name="ion:home-outline" size="1.5em" /> -->
				<img loading="lazy" :src="home_icon" :placeholder="home_icon" alt="" />
				<!-- <Icon name="iconoir:map" size="1.5em" />
			<Icon name="iconoir:menu" v-show="checkActive() == true && isShowList == false" size="1.5em" /> -->
				<span>
					{{ $t("FooterComponent.trang_chu") }}
					<!-- Trang chủ -->
				</span>
			</button>

			<button class='footer-button' :class="{ 'active': checkActive() == appRoute.ReelsComponent }" v-on:click="async () => {
				router.push(appRoute.ReelsComponent);
				// searchAll();
			}">
				<!-- <div class="wave">
				<img loading="lazy" src="~/assets/image_13_3_2024/wave.png" alt="">
			</div> -->
				<!-- <Icon name="ic:baseline-saved-search"> </Icon>-->
				<Icon name="ion:social-youtube-outline"></Icon>


				<span>
					{{ $t("FooterComponent.kham_pha") }}
					<!-- Khám phá -->
				</span>

			</button>

			<button class='footer-button around-button' :class="{ 'active': checkActive() == appRoute.AroundComponent }" v-on:click="async () => {
				// await router.push({
				// 	path: appRoute.AroundComponent,
				// })
				if (checkActive() != appRoute.AroundComponent) {
					// router.push(appRoute.HomeComponent);
					// nuxtApp.$emit('home_click', false)
					await router.push({
						path: appRoute.AroundComponent,
						// query: {
						// 	isShowList: 'false'
						// }
					})
				}
				else {
					nuxtApp.$emit('home_click')
				}

			}">
				<div class="wave">
					<img :src="wave" :placeholder="wave" alt="" />
				</div>
				<Icon name="iconoir:map" class="search-footer-button" />
				<span>
					{{ $t("FooterComponent.gan_day") }}
					<!-- Gần đây -->
				</span>

			</button>

			<button class='footer-button' :class="{ 'active': checkActive() == appRoute.CartComponent }" v-on:click="() => {
				router.push(appRoute.CartComponent);
			}">
				<Icon name="bi:basket2-fill" id="cart_footer" />
				<span>
					{{ $t("FooterComponent.gio_hang") }}
					<!-- Giỏ hàng -->
				</span>
				<em v-if="cartData && cartData.length">
					{{ cartData.length <= 10 ? cartData.length : "10+" }} </em>

			</button>
			<button class='footer-button' :class="{ 'active': checkActive() == appRoute.ProfileComponent }" v-on:click="() => {
				router.push(appRoute.ProfileComponent);
			}">
				<Icon name="iconoir:user" />
				<span>
					{{ $t("FooterComponent.toi") }}
					<!-- Tôi -->
				</span>

			</button>
		</div>
	</div>

</template>

<style lang="scss" src="./FooterStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appConst, appDataStartup } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const localePath = useLocalePath();
let dataCategory = appDataStartup.listCategory;
let dataFilterSort = [
	{
		label: "Giá từ thấp đến cao",
		value: 1,
	},
	{
		label: "Giá từ cao đến thấp",
		value: 2,
	},
	{
		label: "Mới nhất",
		value: 3,
	},
	// {
	//     label: "Sản phẩm bán chạy",
	//     value: 4
	// },
	// {
	//     label: "Sản phẩm khuyến mãi",
	//     value: 5
	// }
];
let authService = new AuthService();
let userService = new UserService();
let placeService = new PlaceService();
let shopService = new ShopService();
let productService = new ProductService();
let isShowList = ref(true);
let cartData = ref();
onUnmounted(async () => { });
onMounted(async () => {

	// userService.profileInfo().then(res => {
	// })
	// isShowList.value = true;
	// nuxtApp.$listen('home_click', (e) => {
	// 	isShowList.value = !isShowList.value;
	// })
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartNumber();
	})

	// nuxtApp.$listen(appConst.event_key.cart_add_animation, (params: any) => {
	// 	runAnimationAddToCart(params.containerId, params.imgId)
	// })
	getCartNumber();
});

function checkActive() {
	if (route.path.includes(appRoute.AroundComponent)) return appRoute.AroundComponent;
	if (route.path.includes(appRoute.HomeComponent)) return appRoute.HomeComponent;
	if (route.path.includes(appRoute.ProfileComponent)) return appRoute.ProfileComponent;
	if (route.path.includes(appRoute.CartComponent)) return appRoute.CartComponent;
	if (route.path.includes(appRoute.SearchComponent)) return appRoute.SearchComponent;
	if (route.path.includes(appRoute.ReelsComponent)) return appRoute.ReelsComponent;
	return '';
	// switch (route.path) {
	// 	case appRoute.AroundComponent:
	// 		return appRoute.AroundComponent
	// 	case appRoute.HomeComponent:
	// 		return appRoute.HomeComponent;
	// 	case appRoute.ProfileComponent:
	// 		return appRoute.ProfileComponent;
	// 	case appRoute.CartComponent:
	// 		return appRoute.CartComponent;
	// 	case appRoute.SearchComponent:
	// 		return appRoute.SearchComponent;
	// 	case appRoute.ReelsComponent:
	// 		return appRoute.ReelsComponent;
	// 	default: return '';
	// }
	// if (route.path.includes("profile"))
	// 	return false;
	// if (route.path.includes("home"))
	// 	return true;
	// return route.path;
}

function getCartNumber() {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$;
}

async function searchAll() {
	await router.push({
		path: appRoute.SearchComponent,
	})
	// if (checkActive() != appRoute.HomeComponent && checkActive() != (appRoute.HomeComponent + '_discovery')) {
	// 	await router.push({
	// 		path: appRoute.HomeComponent,
	// 	})
	// 	setTimeout(() => {
	// 		nuxtApp.$emit('search_focus')
	// 	}, 500);
	// }
	// else {
	// 	nuxtApp.$emit('search_focus')
	// }
}


function runAnimationAddToCart(containerElId: string, imgElId: string) {
	let valueElement = document.getElementById(containerElId) as HTMLElement;

	let imgEl = document.getElementById(imgElId) as HTMLElement;
	let elClone = imgEl.cloneNode(true) as HTMLElement;
	elClone.style.background = 'white';
	valueElement.appendChild(elClone);

	let begintop = imgEl?.offsetTop;
	let beginLeft = imgEl?.offsetLeft;
	let beginWidth = imgEl?.offsetWidth;

	let destinationElement = document.getElementById('cart_footer');
	let destinationTop = destinationElement?.getBoundingClientRect().top;
	let destinationLeft = destinationElement?.getBoundingClientRect().left;

	elClone.style.position = 'absolute'
	elClone.style.top = begintop?.toString() + "px";
	elClone.style.left = (beginLeft)?.toString() + "px";
	elClone.style.zIndex = '10000';
	elClone.style.transition = 'scale .3s ease-in-out, top 2s ease, left 2s ease'

	elClone.style.borderRadius = '100px'
	setTimeout(() => {
		elClone.style.scale = '.15'
		elClone.style.top = `${destinationTop}px`;
		elClone.style.left = `${destinationLeft}px`;

		setTimeout(() => {
			elClone.remove();
			document.getElementById('cart_footer')?.classList.add('shake');
			setTimeout(() => {
				document.getElementById('cart_footer')?.classList.remove('shake')
			}, 500);
		}, 400)

	}, 200);
}
</script>
