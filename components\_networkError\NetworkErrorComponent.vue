<template>
    <VueFinalModal 
        v-model="showModal" 
        class="network-error-modal"
        content-class="network-error-modal-content"
        overlay-class="network-error-modal-overlay"
        :z-index-base="9999"
        :lock-scroll="true"
        :click-to-close="false"
        :esc-to-close="false"
    >
        <div class="network-error-container my-modal-container vfm vfm--fixed vfm--inset">
            <div class="network-error-icon">
                <Icon name="material-symbols:wifi-off" size="48" />
            </div>
            <div class="network-error-content">
                <h3>{{ $t('NetworkError.title') }}</h3>
                <p>{{ $t('NetworkError.message') }}</p>
            </div>
            <div class="network-error-actions">
                <button class="reload-button" @click="reloadPage">
                    <Icon name="material-symbols:refresh" size="20" />
                    {{ $t('NetworkError.reload') }}
                </button>
                <button class="close-button" @click="closeModal">
                    {{ $t('NetworkError.close') }}
                </button>
            </div>
        </div>
    </VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import { computed, watchEffect } from 'vue';

const { t } = useI18n();
const { showNetworkError, hide, reload } = useNetworkError();

const showModal = computed(() => showNetworkError.value);

// Add debugging
watchEffect(() => {
  console.log('NetworkError component - showModal:', showModal.value, 'showNetworkError:', showNetworkError.value);
});

// Test function for debugging
if (process.client) {
  (window as any).testNetworkError = () => {
    console.log('Testing network error modal');
    const { show } = useNetworkError();
    show();
  };
}

// Add debugging
watchEffect(() => {
  console.log('NetworkError component - showModal:', showModal.value, 'showNetworkError:', showNetworkError.value);
});

// Test function for debugging
if (process.client) {
  (window as any).testNetworkError = () => {
    console.log('Testing network error modal');
    const { show } = useNetworkError();
    show();
  };
}

function reloadPage() {
    reload();
}

function closeModal() {
    hide();
}
</script>

<style lang="scss" scoped>
/* Global modal styles */

:deep(.network-error-modal) {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999 !important;
}

:deep(.network-error-modal-overlay) {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998 !important;
}

:deep(.network-error-modal-content) {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 90vw;
    max-height: 90vh;
    z-index: 10000 !important;
    
    @media (prefers-color-scheme: dark) {
        background: #1f2937;
        color: white;
    }
}

.network-error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 24px;
    min-width: 300px;
    max-width: 400px;
    background: #fff;

    .network-error-icon {
        margin-bottom: 16px;
        color: #ef4444;
    }

    .network-error-content {
        margin-bottom: 24px;

        h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            
            @media (prefers-color-scheme: dark) {
                color: #f9fafb;
            }
        }

        p {
            font-size: 0.95rem;
            color: #6b7280;
            line-height: 1.5;
            
            @media (prefers-color-scheme: dark) {
                color: #d1d5db;
            }
        }
    }

    .network-error-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;

        .reload-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #2563eb;
            }

            &:active {
                transform: translateY(1px);
            }
        }

        .close-button {
            background-color: transparent;
            color: #6b7280;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 10px 24px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background-color: #f9fafb;
                border-color: #9ca3af;
                color: #374151;
            }
            
            @media (prefers-color-scheme: dark) {
                color: #d1d5db;
                border-color: #4b5563;
                
                &:hover {
                    background-color: #374151;
                    border-color: #6b7280;
                    color: #f9fafb;
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .network-error-container {
        padding: 20px;
        min-width: 280px;

        .network-error-content {
            h3 {
                font-size: 1.125rem;
            }

            p {
                font-size: 0.9rem;
            }
        }
    }
}
</style>
