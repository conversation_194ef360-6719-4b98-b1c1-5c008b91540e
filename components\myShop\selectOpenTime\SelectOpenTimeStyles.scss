.select-open-time-container {
  max-height: 700px;
  height: fit-content;
  min-height: 500px;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  overflow: hidden;
  background-color: white;

  & > .select-open-time-content-container{
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-bottom: 10px;

    & > .item-open-time{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      gap: 10px;

      & > .day-name{  
        width: 20%;
        font-weight: 600;
      }

      & > .open-time-value{
        flex: 1;
        display: flex;
        justify-content: flex-end;
        text-align: center;
        color: var(--primary-color-1);
        font-weight: bold;
      }
      & > button:hover{
        background-color: #e6e6e6;
      }
      & > button{
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        color: var(--primary-color-1);
        font-size: 20px;
      }
    }
    & > .item-open-time.odd{
      background-color: #f5f6fa;
    }

    & > .edit-options-carousel{
      display: flex;
      gap: 10px;
      overflow: hidden;
      padding: 10px;
      width: 100%;
      user-select: none;

      & .edit-option-item {
        border: thin solid #ccc;
        color: #1a73e8;
        border-radius: 2em;
        padding: 5px 15px;
        white-space: nowrap;
        width: fit-content;
        cursor: pointer;
      }
      & > button:hover{
        background: #f4f5fa;
      }
    }
  }
  & > .action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;
    user-select: none;

    & > button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }
    & > .cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }
    & > .save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    & > .cancel-button:disabled {
      color: var(--color-text-note);
      border-color: var(--color-text-note);
      opacity: 1;
    }
    & > .save-button:disabled {
      background: #ccc;
      color: var(--primary-color-1);
      border-color: #ccc;
      opacity: 1;
    }
  }
}

.select-open-time-overlay{
  display: none;
}