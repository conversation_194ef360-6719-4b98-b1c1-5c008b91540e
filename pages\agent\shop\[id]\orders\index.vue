<template>
    <ManageOrdersComponent :mode="'agent'" v-if="is_authorized = true"/>
    <UnauthorizedComponent v-else-if="is_authorized == false"></UnauthorizedComponent>
</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import { AuthService } from '~/services/authService/authService';

const nuxtApp = useNuxtApp();
const route = useRoute();
var authService = new AuthService();
var is_authorized = ref<any>(null);
onBeforeMount(async ()=>{
    nuxtApp.$emit(appConst.event_key.show_footer, false);
    let author =  (await authService.checkAuthorize(appConst.role_enum.agent) || await authService.checkAuthorize(appConst.role_enum.admin) || await authService.checkAuthorize(appConst.role_enum.admin2));
    is_authorized.value = author
})
</script>