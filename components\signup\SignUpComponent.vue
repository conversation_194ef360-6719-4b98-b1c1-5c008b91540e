<template>
  <div class="stack signup-container">
    <div class="h-stack">
      <img loading="lazy" :src="icon_square" :placeholder="icon_square" alt="Rẻ mà gần" class="signup-icon" />

      <span class="signup-slogan"> <PERSON><PERSON><PERSON> k<PERSON> </span>
    </div>
    <div class="v-stack content-container">

      <!-- name -->
      <div class="v-stack">
        <span class="required label"> Tên </span>
        <input title="Tên" name="name" :maxLength="255" autoComplete="new-password" class="input-custom"
          placeholder="Nguyễn Văn A" :value="name" v-on:input="($event: any) => {
            name = $event.currentTarget.value;
            nameValidation()
          }" v-on:blur="() => {
            nameValidation();
          }
            " />
        <span class="error-message">{{ nameErr }}</span>
      </div>

      <!-- phone -->
      <div class="v-stack">
        <span class="required label"> S<PERSON> điện thoại </span>
        <div class="password-input-group">
          <input title="Số điện thoại" name="phone-number" type="phone" :maxLength="255" autoComplete="new-password"
            class="input-custom" placeholder="0123456789" :disabled="phoneConfirmed" :value="phone" v-on:input="($event: any) => {
              phone = validPhone($event.currentTarget.value);
              if (!userName?.length) {
                userName = $event.currentTarget.value;
                if (!userName || !userName.length) {
                  userNameErr = 'Vui lòng nhập tên đăng nhập';
                } else {
                  userNameValidation();
                }
              }
              if (!phone || !phone.length) {
                phoneErr = 'Vui lòng nhập số điện thoại'
              }
              else { phoneValidation(); }
            }" v-on:blur="() => {
              if (!phone || !phone.length) {
                phoneErr = 'Vui lòng nhập số điện thoại';
              } else {
                phoneValidation();
              }
            }" />
          <button :disabled="!phone.length || phoneConfirmed || phoneErr?.length > 0"
            :title="phoneConfirmed ? 'SĐT đã xác thực' : 'Xác thực SĐT'" :class="phoneConfirmed ? 'checked' : ''"
            v-on:click="() => {
              if (!phoneConfirmed) {
                showModalConfirmOTP = true;
              }
            }
              ">
            <Icon name="material-symbols:call-quality" v-if="!phoneConfirmed" title="Xác thực SĐT" />
            <Icon name="mdi:phone-check" v-if="phoneConfirmed" title="SĐT đã được xác thực" />
          </button>
        </div>

        <span class="error-message">{{ phoneErr }}</span>
        <span class="error-message">{{ phoneConfirmedErr }}</span>
        <!-- <span class="error-message" v-if="!phoneConfirmed && !phoneErr.length && phone.length">SĐT chưa được xác thực</span> -->
      </div>

      <!-- password -->
      <div class="v-stack">
        <span class="required label"> Mật khẩu </span>
        <div class="password-input-group">
          <input title="Mật khẩu" name="password" :maxLength="255" autoComplete="new-password" class="input-custom"
            placeholder="******" :type="passwordShow ? 'text' : 'password'" :value="password" v-on:input="($event: any) => {
              password = $event.currentTarget.value;
              if (!password || !password.length) {
                passwordErr = 'Vui lòng nhập mật khẩu'
              }
              else {
                passwordValidation();
              }
            }" v-on:blur="() => {
              if (!password || !password.length) {
                passwordErr = 'Vui lòng nhập mật khẩu';
              } else {
                passwordValidation();
              }
            }
              " />
          <button v-on:click="() => {
            passwordShow = !passwordShow;
          }
            ">
            <Icon name="ion:eye" v-if="!passwordShow" />
            <Icon name="ion:eye-off" v-if="passwordShow" />
          </button>
        </div>

        <span class="error-message">{{ passwordErr }}</span>
      </div>

      <!-- confirm password -->
      <div class="v-stack">
        <span class="required label"> Xác nhận mật khẩu </span>
        <div class="password-input-group">
          <input title="Xác nhận Mật khẩu" name="password" :maxLength="255" autoComplete="new-password"
            class="input-custom" placeholder="******" :type="passwordConfirmShow ? 'text' : 'password'"
            :value="passwordConfirmation" v-on:input="($event: any) => {
              passwordConfirmation = $event.currentTarget.value;
              confirmPasswordValidation();
            }" v-on:blur="() => {
              confirmPasswordValidation();
            }
              " />
          <button v-on:click="() => {
            passwordConfirmShow = !passwordConfirmShow;
          }
            ">
            <Icon name="ion:eye" v-if="!passwordConfirmShow" />
            <Icon name="ion:eye-off" v-if="passwordConfirmShow" />
          </button>
        </div>

        <span class="error-message">{{ passwordConfirmationErr }}</span>
      </div>

      <!-- user name -->
      <div class="v-stack">
        <span class="required label"> Tên đăng nhập </span>
        <input title="Tên đăng nhập" name="user-name" :maxLength="255" autoComplete="new-password" class="input-custom"
          placeholder="username_a" :value="userName" v-on:input="($event: any) => {
            userName = $event.currentTarget.value;
            if (!userName || !userName.length) {
              userNameErr = 'Vui lòng nhập tên đăng nhập';
            }
            else {
              userNameValidation();
            }
          }" v-on:blur="() => {
            if (!userName || !userName.length) {
              userNameErr = 'Vui lòng nhập tên đăng nhập';
            } else {
              userNameValidation();
            }
          }
            " />
        <span class="error-message">{{ userNameErr }}</span>
      </div>

      <!-- email -->
      <div class="v-stack">
        <span class="label"> Email </span>
        <input title="Email" name="email" :maxLength="255" autoComplete="new-password" class="input-custom"
          placeholder="<EMAIL>" :value="email" v-on:input="($event: any) => {
            email = $event.currentTarget.value;
            emailValidation();
          }" v-on:blur="() => {
            emailValidation();
          }
            " />
        <span class="error-message">{{ emailErr }}</span>
      </div>

      <button class="button-action signup-button" :disabled="isSignuping" v-on:click="() => {
        signup();
      }
        ">
        Đăng ký
      </button>
      <div class="h-stack login-button-container">
        <span>Đã có tài khoản?</span>
        <nuxt-link :to="appRoute.LoginComponent">
          <span class="login-button">Đăng nhập</span>
        </nuxt-link>
      </div>
      <span class="back-home" v-on:click="() => {
        router.push(appRoute.HomeComponent);
      }
        ">
        Quay lại Trang chủ
      </span>
    </div>

    <VueFinalModal class="my-modal-container" content-class="modal-content-container v-stack" :click-to-close="false" :overlay-behavior="'persist'"
      v-model="showModalConfirmOTP" v-on:closed="() => {
        showModalConfirmOTP = false
      }" contentTransition="vfm-slide-up">

      <OTPConfirmComponent :phone="phone" v-on:close="(e: any) => {
        if (e) {
          phoneConfirmed = true;
          phoneConfirmedErr = '';
          showModalConfirmOTP = false;
          // getMyShop();
        }
        else {
          showModalConfirmOTP = false;
        }

      }"></OTPConfirmComponent>
    </VueFinalModal>
  </div>
</template>

<script lang="ts" setup>

var router = useRouter();
var route = useRoute();
const { t } = useI18n()
// var props = defineProps({
// 	categoryData: "" as any,
// 	dataCategories: {}
// })
useSeoMeta({
  title: t('AppRouteTitle.SignupComponent'),
});
var emit = defineEmits(["close"]);

import { toast } from "vue3-toastify";
import { appConst, encryptPassword, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { VueFinalModal } from 'vue-final-modal';
import { AuthService } from "~/services/authService/authService";
import { UserService } from "~/services/userService/userService";
import OTPConfirmComponent from "../otpConfirm/OTPConfirmComponent.vue";
import icon_square from "~/assets/image/icon_square_circle_300x300.png";
import { HttpStatusCode } from "axios";
var authService = new AuthService();
var userService = new UserService();

var name = ref("");
var nameErr = ref("");
var email = ref("");
var emailErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var phoneConfirmed = ref(false);
var phoneConfirmedErr = ref("");
var userName = ref("");
var userNameErr = ref("");
var password = ref("");
var passwordErr = ref("");
var passwordConfirmation = ref("");
var passwordConfirmationErr = ref("");
var passwordShow = ref(false);
var passwordConfirmShow = ref(false);
var timeout = ref(null);
var gender = ref(null);
var dateOfBirth = ref("");
var isSignuping = ref(false);

var showModalConfirmOTP = ref(false);

var checkExistingUserNameTimeout: any;
var checkExistingEmailTimeout: any;

async function signup() {
  emailValidation();
  phoneValidation();
  passwordValidation();
  confirmPasswordValidation();
  userNameValidation();
  nameValidation();
  if (!phoneConfirmed.value) {
    phoneConfirmedErr.value = "Số điện thoại chưa được xác nhận";
  }
  if (
    !phoneConfirmedErr.value.length &&
    !emailErr.value.length &&
    !phoneErr.value.length &&
    !passwordErr.value.length &&
    !passwordConfirmationErr.value.length &&
    !nameErr.value.length &&
    !userNameErr.value.length
  ) {
    isSignuping.value = true;
    let signup = await authService.signup({
      userName: userName.value,
      password: await encryptPassword(password.value),
      password_confirmation: passwordConfirmation.value,
      name: name.value,
      email: email.value,
      phone: validPhone(phone.value),
      gender: gender.value,
      date_of_birth: dateOfBirth.value,
    });
    if (signup.status == HttpStatusCode.Ok) {
      // Alert.alert("đăng ký thành công:");
      toast.success(t('SignupComponent.dang_ky_thanh_cong'));
      router.push({
        path: appRoute.LoginComponent,
        // params: {
        //   userName: userName.value,
        //   password: password.value
        // }
      });
    } else {
      toast.success(signup.body?.message ?? t('SignupComponent.dang_ky_that_bai'));
      if (signup.body[0].includes('phone')) {
        phoneErr.value = signup.body[0].replaceAll("phone", "Số điện thoại")
      }
      if (signup.body[0].includes('user_name')) {
        userNameErr.value = signup.body[0]
      }
      if (signup.body[0].includes('name')) {
        nameErr.value = signup.body[0]
      }
      isSignuping.value = false
    }
  }
  else {
    isSignuping.value = false;
  }

}
function nameValidation() {
  if (!name.value.length) {
    nameErr.value = "Vui lòng nhập tên người dùng";
  } else {
    nameErr.value = "";
  }
}
function userNameValidation() {
  let re = appConst.validateValue.userName;
  if (!re.test(userName.value)) {
    userNameErr.value =
      'Tên đăng nhập tối thiểu 8 ký tự gồm chữ cái, số và dấu gạch dưới "_"';
  } else {
    userNameErr.value = "";
    clearTimeout(checkExistingUserNameTimeout);
    checkExistingUserNameTimeout = setTimeout(() => {
      checkExistingUserName();
    }, 500);
  }
}
async function checkExistingUserName() {
  let body = {
    key: userName.value,
    type: appConst.check_exist_enum.user_name,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    userNameErr.value = "Tên đăng nhập đã được sử dụng";
  } else {
    userNameErr.value = "";
  }
}
function phoneValidation() {
  let re = appConst.validateValue.phone;
  if (!validPhone(phone.value)?.length) {
    phoneErr.value = "Vui lòng nhập SĐT";
    return;
  }
  if (!re.test(validPhone(phone.value))) {
    phoneErr.value = "SĐT không đúng";
    return;
  }
  else {
    phoneErr.value = "";
    checkExistingPhone();
  }
}
async function checkExistingPhone() {
  let body = {
    key: validPhone(phone.value),
    type: appConst.check_exist_enum.phone,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    phoneErr.value = t('ProfileInfoComponent.sdt_da_duoc_su_dung');
  }
  else {
    phoneErr.value = "";
  }
}

function emailValidation() {
  let re = appConst.validateValue.email;
  if (email.value.length) {
    if (!re.test(email.value)) {
      emailErr.value = "Email không đúng định dạng";
      return;
    } else {
      emailErr.value = "";
      clearTimeout(checkExistingEmailTimeout);
      checkExistingEmailTimeout = setTimeout(() => {
        checkExistingEmail();
      }, 500);
    }
  }
}

function passwordValidation() {
  if (!password.value.length) {
    passwordErr.value = "Vui lòng nhập mật khẩu";
  } else {
    let re = appConst.validateValue.password;
    if (password.value.length && !re.test(password.value)) {
      passwordErr.value =
        "Mật khẩu có ít nhất 8 ký tự, phải bao gồm chữ và số và không được có khoảng trắng";
      return;
    } else {
      passwordErr.value = "";
    }
  }
}

function confirmPasswordValidation() {
  if (!passwordConfirmation.value || !passwordConfirmation.value.length) {
    passwordConfirmationErr.value = "Vui lòng nhập mật khẩu xác nhận";
  } else if (passwordConfirmation.value != password.value) {
    passwordConfirmationErr.value = "Mật khẩu xác nhận không đúng";
  } else {
    passwordConfirmationErr.value = "";
  }
}
async function checkExistingEmail() {
  let body = {
    key: email.value,
    type: appConst.check_exist_enum.email,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check) {
    emailErr.value = "Email đã được sử dụng";
  } else {
    emailErr.value = "";
  }
}
</script>

<style lang="scss" src="./SignUpStyles.scss"></style>
