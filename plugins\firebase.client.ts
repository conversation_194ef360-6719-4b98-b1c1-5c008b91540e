import { initializeApp } from 'firebase/app'

import { getAuth } from "firebase/auth"
import { getFirestore } from 'firebase/firestore'
import { getAnalytics } from "firebase/analytics"
import { getMessaging, isSupported } from 'firebase/messaging'
import { appFireBaseConfig } from '~/assets/AppConst'
import firebase from 'firebase/compat/app'

const firebaseConfig = appFireBaseConfig

const app = initializeApp(firebaseConfig)
// let check = isSupported();
// var messagings:any;
// check.then((e)=>{
//     messagings = e ? getMessaging(app) : null
// })


export default defineNuxtPlugin(async ()=>{
    let check = await isSupported();
    const messagings = check ? getMessaging(app) : null;
    return{
        provide:{
            messaging: messagings
        }
    }
})