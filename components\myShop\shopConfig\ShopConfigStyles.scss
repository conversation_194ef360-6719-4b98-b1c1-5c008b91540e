.show-config-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-content-view-1024);
  background: var(--color-background-2);
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: auto;

  &>.shop-config-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    background: #f5f6fa;

    &>.config-item-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
      border-radius: 10px;
      background: white;
      padding: 10px;
      overflow: hidden;


      &>.config-item {
        display: flex;
        flex-direction: column;
        gap: 10px;

        &>.label {
          color: #565656;
          font-size: 17px;
        }

        &>.config-item-children {
          padding: 0 0 0 15px;

          & label {
            font-weight: 400;
            color: #565656;
            font-size: 15px;
          }

          &>.config-item-child {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            flex-wrap: wrap;
            padding: 10px 0;
            column-gap: 10px;

            & .edit-config {
              --button-color: #565656;
              display: flex;
              align-items: center;
              gap: 10px;
              cursor: pointer;
              color: var(--button-color);
              font-size: 20px;
              width: 30px;
              height: 30px;
              border-radius: 10px;
              justify-content: center;
              margin-left: auto;

              &:hover {
                background: color-mix(in srgb, var(--button-color) 10%, transparent);
              }
            }

            & .delete-config {
              --button-color: var(--primary-color-2);
              display: flex;
              align-items: center;
              gap: 10px;
              cursor: pointer;
              color: var(--button-color);
              font-size: 20px;
              width: 30px;
              height: 30px;
              border-radius: 10px;
              justify-content: center;
              margin-left: auto;

              &:hover {
                background: color-mix(in srgb, var(--button-color) 10%, transparent);
              }
            }

            &>label {
              flex: 30%;
              font-weight: 600;
            }

            &>label.delivery-fee-label {
              align-self: flex-start;
            }

            &>.config-item-child-value {
              color: #565656;
              font-weight: 600;
              // padding: 3px 10px;
              width: 100%;
              flex: 65%;
              display: flex;
              align-items: center;
              gap: 5px;
              margin-top: 5px;

              & .switch-config {
                display: flex;
                gap: 10px;
                align-items: center;
                cursor: pointer;
                height: 30px;
              }

              &>.config-value {
                background: #f5f6fb;
                flex: 1;
                border-radius: 5px;
                font-size: 15px;
                font-weight: 600;
                display: flex;
                align-items: center;
                padding: 0 10px;
                // height: 35px;

                &.none-config {
                  font-size: 15px;
                  font-style: italic;
                  color: #838383;
                }
              }

              &>.delivery-fee-item {
                display: flex;
                flex: 1;
                width: 100%;
                background: #f5f6fa;
                padding: 10px;
                border-radius: 5px;

                &>.delivery-fee-item-content {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;

                  &>.delivery-fee-item-name {
                    font-size: 15px;
                    font-weight: 700;


                  }

                  &>.delivery-fee-item-description {
                    font-size: 13px;
                    font-weight: 400;
                    font-style: italic;
                    color: #838383;
                  }

                  &>.delivery-fee-item-value {
                    color: var(--primary-color-1);
                    display: flex;
                    align-items: center;
                    font-size: 17px;
                    font-weight: 700;
                    gap: 3px;
                    font-style: normal;
                  }

                  &>.delivery-fee-item-max-value {
                    color: #545454;
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    font-weight: 700;
                    gap: 3px;
                    font-style: normal;
                  }

                  &>.delivery-fee-item-condition-count {
                    font-size: 15px;
                    font-weight: 600;
                    font-style: italic;
                    color: var(--primary-color-2);
                  }

                  &>.actions {
                    display: flex;
                    flex-wrap: wrap;

                    &>.delivery-fee-item-enable {
                      display: flex;
                      align-items: center;
                      justify-content: flex-start;
                      gap: 5px;
                      height: 30px;
                    }
                  }
                }

                &>.delivery-fee-item-action {
                  display: flex;
                  flex-direction: column;
                  gap: 3px;
                }
              }

              & .message-option {
                display: flex;
                flex-direction: column;
                height: auto;
                text-align: left;
                align-items: flex-start;
                padding: 5px 10px;

                &>span {
                  font-size: 13px;
                  color: #838383;
                  display: flex;
                  gap: 5px;

                  &>span {
                    color: #545454;
                    white-space: break-spaces;
                    display: -webkit-box;
                    -webkit-line-clamp: 4;
                    line-clamp: 4;
                    overflow: hidden;
                    -webkit-box-orient: vertical;
                  }

                  &>em {
                    color: #aeaeae;
                  }
                }
              }
            }

            &>.config-item-child-value.holiday-config {
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              margin-top: 5px;

              &>.holiday-config-item {
                display: flex;
                flex: 1;
                width: 100%;
                background: #f5f6fa;
                padding: 5px 10px;
                border-radius: 5px;

                &.config-value {
                  padding: 0 10px;
                  height: 35px;
                }

                &>.holiday-config-value {
                  display: flex;
                  flex-direction: column;
                  flex: 1;
                  gap: 5px;

                  &>.label {
                    font-size: 13px;
                    font-weight: 600;
                    color: #838383;
                  }

                  &>.day-off-container {
                    display: flex;
                    gap: 5px;
                    flex-wrap: wrap;

                    &.empty {
                      color: #aeaeae;
                      font-size: 13px;
                      font-style: italic;

                    }

                    &>.day-off-item {
                      padding: 0 10px;
                      color: #545454;
                      height: 25px;
                      font-weight: 700;

                      &.in-week {
                        color: var(--primary-color-2);
                      }

                      &.in-month {
                        color: var(--primary-color-1);

                        &>.v-chip__underlay {
                          background: var(--primary-color-1);
                        }
                      }

                      &>.v-chip__underlay {
                        background: var(--primary-color-2);
                      }
                    }
                  }
                }
              }
            }

            &>.config-item-child-value.status-config {
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              margin-top: 5px;
              background: #f5f6fb;
              border-radius: 5px;

              &>.reason-close {
                padding: 10px;
                display: flex;
                flex-direction: column;
                gap: 5px;
                flex: 1;
                width: 100%;
              }

              & .reason-item {
                display: flex;
                flex: 1;
                align-items: flex-start;
                margin: 5px 0;

                &>label {
                  color: #868686;
                  font-size: 13px;
                  font-weight: 600;
                  width: 20%;
                }

                & .text-area-custom {
                  width: 100%;
                  background-color: white;
                  border-radius: 5px;
                  border: none;
                  outline: none;
                  color: #545454;
                  font-weight: 400;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  display: flex;
                  padding: 0;
                  font-size: 13px;
                  flex: 1;
                  margin-left: 10px;

                  &>textarea {
                    width: 100%;
                    padding: 10px;
                    outline: none;
                    height: 100% !important;
                    resize: none;
                  }

                  &:disabled {
                    background: #f5f6fa;
                  }
                }
              }
            }

            &>.config-item-child-value.delivery-fee-config {
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              // margin-top: 5px;

              &.empty {
                &>.delivery-fee-item {
                  color: #838383;
                  font-size: 15px;
                  font-style: italic;
                }
              }
            }

            &>.config-item-child-value.view-config {
              padding: 5px 10px;
              background: #f5f6fb;
              border-radius: 5px;
              align-items: flex-start;

              &>.config-value {
                padding: 0;
                gap: 10px;
                flex-wrap: wrap;
                flex-direction: column;
                align-items: flex-start;

                &>.label {
                  color: var(--primary-color-1);
                  font-weight: 700;
                }

                &>.colors {
                  display: flex;
                  gap: 5px;

                  &>.color-preview {
                    width: 20px;
                    height: 20px;
                    margin: 10px;
                    border: 2px solid white;
                    box-shadow: 0 0 3px 3px rgba($color: #000, $alpha: .05);
                    border-radius: 5px;
                  }
                }
              }
            }

            @media screen and (max-width:500px) {
              flex-direction: column;
              align-items: flex-start;

              &>div,
              >label {
                flex: 1;
              }
            }

            &>.config-item-footer {
              width: 100%;
              display: flex;
              justify-content: flex-end;
              margin-top: 10px;
              gap: 10px;

              &>button {
                display: flex;
                align-items: center;
                color: var(--primary-color-1);
                font-size: 17px;
                font-weight: 600;
                // font-style: italic;
                gap: 5px;
                padding: 2px 10px 2px 5px;
                border-radius: 5px;

                &:hover {
                  background: color-mix(in srgb, var(--primary-color-1) 10%, transparent);
                }
              }
            }
          }
        }

      }
    }
  }

  &>.shop-config-footer {
    position: sticky;
    bottom: 0;
    padding: 10px 15px;
    background: linear-gradient(to right, #f5f6fa, white 60%);
    display: flex;
    justify-content: flex-end;
    gap: 10px;

    &>.save-button {
      padding: 5px 20px;
      border-radius: 7px;
      background: var(--primary-color-1);
      color: white;
      font-size: 15px;
      font-weight: 600;
      min-width: 100px;
    }

    &>.undo-button {
      padding: 5px 20px;
      border-radius: 7px;
      background: transparent;
      border: thin solid;
      color: var(--primary-color-1);
      font-size: 15px;
      font-weight: 600;
      min-width: 100px;
    }
  }
}