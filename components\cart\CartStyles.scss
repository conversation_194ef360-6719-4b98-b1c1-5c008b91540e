.cart-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-content-view-1024) !important;
  background: #f5f4f9;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 17px;

  .empty-cart-avt {
    margin: 0 10px;
    justify-content: center;
    width: 250px;
    height: 250px;
    object-fit: contain;
  }

  .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding-top: 30px;
    flex: 1;
    // background: var(--color-background-2);
    max-width: var(--max-width-content-view-1024);
    margin: auto;
  }

  .empty-cart > .text {
    color: var(--color-text-black);
    font-weight: bold;
    font-size: 1.2em;
  }

  .empty-cart > .action {
    color: var(--color-text-note);
    font-style: italic;
    font-size: 1em;
    border: none;
    background: transparent;
  }
  & > .cart-info-container {
    display: flex;
    flex-direction: column;
    background-color: #f5f4f9;
    // overflow: auto;
    font-size: 15px;
    padding: 10px;
    width: 100%;
    flex: 1;
    max-width: var(--max-width-content-view-1024);
    margin: 0 auto;

    & .line {
      height: 1px;
      width: calc(100% - 10px * 2);
      background: #f3f3f3;
      margin: auto;
    }

    & > .cart-list-item-container {
      display: flex;
      // flex-direction: column;
      flex-direction: row;
      flex-wrap: wrap;
      background: white;
      margin-bottom: 10px;
      border-radius: 7px;

      & > .cart-overview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        color: black;
        width: 100%;
        // border-bottom: thin solid #e4e4e4;

        & > .check-all {
          display: flex;
          align-items: center;
          // font-size: 1rem;
          font-weight: 400;
          gap: 5px;
          cursor: pointer;
          user-select: none;

          & > svg.checked {
            color: var(--primary-color-1);
          }
          & > svg {
            color: #A8A7A7;
            font-size: 25px;
          }
        }

        & > .clear-all {
          color: #a8a7a7;
          font-size: 25px;
          display: flex;
        }
      }

      & > .cart-item-container {
        --cart-item-width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 10px;
        color: black;
        gap: 10px;
        position: relative;
        width: var(--cart-item-width);

        // @media screen and (min-width: 1001px) {
        //   --cart-item-width: calc(50%);
        // }

        & > .checkbox {
          margin-top: 35px;
          transform: translateY(-50%);
          animation: none !important;

          & > svg.checked {
            color: var(--primary-color-1);
          }
          & > svg {
            color: #A8A7A7;
            font-size: 25px;
          }
        }
        & > img {
          width: 70px;
          height: 70px;
          aspect-ratio: 1;
          border-radius: 5px;
          object-fit: cover;
          background: var(--color-background-2);
        }

        & > .item-cart-detail {
          align-items: flex-start;
          flex: 1;
          overflow: hidden;

          & > .item-cart-product-name {
            font-weight: 500;
            color: black;
            line-height: normal;
            display: -webkit-box;
            line-clamp: 1;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 15px;
          }

          & > .product-price {
            color: black;
            font-weight: 700;
            font-size: 17px;
            display: flex;
            flex-direction: column;
            line-height: 20px;
            margin-top: 25px;
            width: 100%;

            & > em {
              color: #a8a7a7;
              text-decoration: line-through;
              font-weight: 400;
              font-size: 13px;
              line-height: 15px;
              min-height: 15px;
              font-style: normal;
              opacity: 0;
              max-width: 55%;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            & > em.show{
              opacity: 1;
            }
          }

          & > .item-cart-quantity {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            margin-top: -10px;

            & > span {
              margin: 0 10px;
            }

            & > .quantity-button {
              color: black;
              border: none;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 30px;
              height: 30px;
              padding: 0;
              font-size: 17px;
              background: #f3f3f3;
            }

            & > .quantity-button:disabled {
              opacity: 0.5;
            }

            & > .delete-item-cart {
              margin-left: 5px;
              color: var(--primary-color-2);
              // align-self: flex-end;
            }

            .price-input {
              // border: thin solid var(--color-text-note);
              border-width: 0 0 1px 0;
              width: 40px;
              height: 30px;
              text-align: center;
              font-weight: 600;
              outline: none;
              display: flex;
              font-size: 15px;
              background: #fbfbfb;
            }
          }

          & > .note-input {
            width: 100%;
            outline: none;
            background: #F4F4F4;
            border-radius: 3px;
            margin-top: 5px;
            padding: 5px;
            font-size: 13px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: #A8A7A7;

            & input{
              outline: none;
              width: 100%;
            }

            & svg{
              margin-right: 4px;
              font-size: 20px;
            }
          }
        }

        &:not(:last-child)::after {
          content: "";
          height: 1px;
          width: calc(100% - 10px * 2);
          background: #f3f3f3;
          position: absolute;
          bottom: 0;
        }
      }
    }

    & > .shop-detail-container {
      display: flex;
      flex-direction: column;
      padding: 10px;
      background: white;
      margin-bottom: 10px;
      border-radius: 7px;

      & > .shop-info {
        display: flex;
        gap: 10px;

        & > .shop-name-address {
          display: flex;
          flex-direction: column;
          font-size: 15px;
          line-height: 1.2;
          flex: 1;

          & .name {
            color: black;
            font-weight: 700;
            margin-bottom: 3px;
          }

          & .address {
            color: #545454;
            font-size: 13px;
            font-weight: 500;
          }

          & .closed {
            font-size: 13px;
            font-style: italic;
            color: var(--primary-color-2);
            line-height: 1;
            flex: 1;
            text-align: right;
            justify-content: left;
            margin-top: 5px;
            width: 100%;
            font-weight: 700;
            display: flex;
            align-items: center;
          }
        }

        & > .cart-shop-logo{
          border-radius: 5px;
        }

        & .chat-to-shop{
          color: var(--primary-color-1);
          height: 30px;
          padding: 0 10px;
          font-weight: 700;
          text-transform: none;
          margin-left: auto;
          align-self: flex-start;

          & svg{
            min-width: 20px;
            width: 20px;
          }
        }
      }

      
    }
  }
  & > .cart-check-container {
    padding: 0 10px;
    background-color: white;
    align-items: center;
    justify-content: space-between;
    color: white;
    font-size: 1em;
    margin-top: auto;
    font-weight: 500;
    display: flex;
    position: sticky;
    bottom: 0;
    box-shadow: 0 -4px 4px 0 rgb(0, 0, 0, 5%);
    width: 100%;
    max-width: var(--max-width-content-view-1024);
    margin: 0 auto;

    & > .cart-check-content {
      padding: 10px 0 15px 0;
      align-items: center;
      justify-content: space-between;
      color: white;
      font-size: 17px;
      margin-top: auto;
      font-weight: 500;
      display: flex;
      flex-direction: column;
      flex: 1;

      & > .total {
        color: #575757;
        margin-bottom: 15px;
        font-size: 1em;
        justify-content: space-between;
        display: flex;
        width: 100%;

        & > span {
          font-size: 15px;
          color: black;
          line-height: normal;
          font-weight: 700;

          & > em{
            font-weight: 400;
            font-style: normal;
          }
        }
        & > .price {
          color: var(--primary-color-2);
          font-weight: 800;
          font-size: 15px;
        }

        & > .none-price {
          color: var(--primary-color-2);
          font-weight: 500;
        }
      }

      & > button {
        width: 100%;
        height: 100%;
        margin-left: auto;
        border-radius: 5px;
        background-color: var(--primary-color-1);
        padding: 10px;
        color: white;
        font-weight: 500;
        border: none;
        gap: 5px;
        font-size: 1em;
        display: flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
      }
    }
  }
}
.delete-cart-item-name {
  color: var(--color-button-special);
}
.delete-cart-message {
  font-size: 1.3em;
  // color: var(--primary-color-1);
  text-align: center;
}

// .my-modal-container > .my-modal-content-container {
//   min-height: unset;
// }

.delete-product-in-cart-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}
