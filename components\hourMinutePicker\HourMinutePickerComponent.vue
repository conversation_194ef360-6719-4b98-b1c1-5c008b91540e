<template>
	<v-overlay v-model="showHourMinutePicker" location="bottom" contained :z-index="1001" key="show_hour_minute_select"
		class="hour-minute-picker-overlay-container" persistent content-class='hour-minute-picker-container'
		no-click-animation>
		<HeaderComponent :title="$props.title ? $props.title : $t('AppRouteTitle.HourMinutePickerComponent')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close" v-on:click="() => {
					close()
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="hour-minute-picker-content-container">
			<div class="picker-content hour-picker" v-if="showHourPicker">
				<span>{{ $t('HourMinutePickerComponent.gio') }}</span>
				<div class="picker-container">
					<Swiper class="my-carousel picker-buttons-carousel" :modules="[]" :centeredSlides="true"
						:direction="'vertical'" :centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
						:effect="'creative'" :autoplay="false" key="hour-picker-carousel"
						:initialSlide="selectHourIndex" @slideChange="(e: any) => {

							selectHour = hourList[e.activeIndex].value;
							selectHourIndex = e.activeIndex;
						}" @init="(e: any) => {
							hourCarouselButton = e;
						}">
						<SwiperSlide class="picker-item" v-for="(itemHour, indexHour) of hourList" v-on:click="async () => {
							hourCarouselButton.slideTo(indexHour)
						}">
							<div class="tab-title">
								<span class='name'>
									{{ itemHour.displayName }}
								</span>
							</div>
						</SwiperSlide>
					</Swiper>
				</div>

			</div>
			<span>:</span>
			<div class="picker-content minute-picker" v-if="showMinutePicker">
				<span>{{ $t('HourMinutePickerComponent.phut') }}</span>
				<div class="picker-container">
					<Swiper class="my-carousel picker-buttons-carousel" :modules="[SwiperFreeMode]"
						:centeredSlides="true" :freeMode="{
							enabled: true,
							sticky: true
						}" :direction="'vertical'" :centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
						:effect="'creative'" :autoplay="false" :initialSlide="selectMinuteIndex"
						key="minute-picker-carousel" @slideChange="(e: any) => {
							selectMinute = minuteList[e.activeIndex].value;
							selectMinuteIndex = e.activeIndex
						}" @init="(e: any) => {
						minuteCarouselButton = e;
					}">
						<SwiperSlide class="picker-item" v-for="(itemMinute, indexMinute) of minuteList" v-on:click="async () => {
							minuteCarouselButton.slideTo(indexMinute)
						}">
							<div class="tab-title">
								<span class='name'>
									{{ itemMinute.displayName }}
								</span>
							</div>
						</SwiperSlide>
					</Swiper>
				</div>

			</div>
		</div>
		<div class="footer-actions">
			<button class="submit-button" v-on:click="() => {
				submit();
			}">
				{{ $t('HourMinutePickerComponent.chon') }}
			</button>
		</div>
	</v-overlay>

</template>

<script lang="ts" setup>
import moment from 'moment';
const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
	title: null,
	startHour: null,
	endHour: null,
	initialHour: null,
	initialMinute: null,
	showHourPicker: null,
	showMinutePicker: null,
	stepMinute: null,
	firstStepMinute: null
})

var showHourMinutePicker = ref(false);
var hourList = ref([] as any);
var minuteList = ref([] as any);
var showHourPicker = ref(props.showHourPicker ? props.showHourPicker : true);
var showMinutePicker = ref(props.showMinutePicker ? props.showMinutePicker : true);

var hourCarouselButton = ref();
var minuteCarouselButton = ref();

var selectHourIndex = ref(0);
var selectMinuteIndex = ref(0);
var selectHour = ref(null as any);
var selectMinute = ref(null as any);
onMounted(async () => {
	if (showHourPicker.value) {
		await hourListConstructor();
		await setFirstSelectHour();
	}
	if (showMinutePicker.value) {
		await minuteListConstructor();
		await setFirstSelectMinute();
	}
	showHourMinutePicker.value = true;
})

function hourListConstructor(intervalHour = 1) {
	let startHour = props.startHour;
	let endHour = props.endHour;
	var hours = [];
	if (startHour > endHour) {
		startHour = props.endHour;
		endHour = props.startHour;
	}
	for (let i = startHour; i <= endHour; i += intervalHour) {
		hours.push({
			value: i,
			displayName: moment().hours(i).format("HH")
		});
	}
	hourList.value = hours;

}

function minuteListConstructor(intervalMinutes = props.stepMinute ? props.stepMinute : 1) {
	var slots: any = [];

	for (let i = 0; i <= 59; i += intervalMinutes) {
		slots.push({
			value: i,
			displayName: moment().minutes(i).format("mm")
		})
	}


	minuteList.value = slots;
}
function setFirstSelectHour() {

	let initHourIndex = hourList.value.findIndex(function (e: any) {
		return e.displayName == props.initialHour;
	});
	selectHourIndex.value = initHourIndex != -1 ? initHourIndex : 0;
	selectHour.value = hourList.value[selectHourIndex.value]?.value;
}
function setFirstSelectMinute() {
	let initMinuteIndex = minuteList.value.findIndex(function (e: any) {
		return e.displayName == props.initialMinute
	});
	selectMinuteIndex.value = initMinuteIndex != -1 ? initMinuteIndex : 0;
	selectMinute.value = minuteList.value[selectMinuteIndex.value].value;
}

function close(value?: any) {
	showHourMinutePicker.value = false;
	emit('close', value);
}
function submit() {
	showHourMinutePicker.value = false;
	emit('submit', `${hourList.value[selectHourIndex.value].displayName}:${minuteList.value[selectMinuteIndex.value].displayName}`)
	// if (selectHour.value && selectMinute.value)
	// 	emit('submit', `${hourList.value[selectHourIndex.value].displayName}:${minuteList.value[selectMinuteIndex.value].displayName}`)
	// else {
	// 	emit('submit', null)
	// }
}
</script>

<style lang="scss" src="./HourMinutePickerStyles.scss"></style>