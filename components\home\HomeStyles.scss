/* @import url('../../App.scss'); */
@media screen and (max-width: 500px) {
  .list-map-buttons {
    left: unset;
    right: 0;
  }
}

.home-container {
  // width: 100%;
  height: 100%;
  flex: 1;
  /* min-height: inherit; */
  // border-radius: 10px 10px 0 0;
  // max-width: var(--max-width-view);
  background: white;
  margin: 0;
  overflow: auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;

  // display: flex;
  // flex-direction: column;
  // flex: 1;
  // background-color: white;
  // position: relative;
  // padding: 10px;
  // overflow: auto;
  // font-size: calc(var(--font-size) * 1.3);
  // background-color: var(--color-background-2);

  & .location-and-search {
    // height: 20%;
    // min-height: 100px;
    width: 100%;
    background-size: 100%;
    background: transparent;
    // background-image: url("~/assets/image_13_3_2024/patter.jpg");
    // background-image: url("~/assets/image/imgpsh_fullsize_anim.png");
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 18px;
    // padding: 0 0 25px;
    transition: min-height 0.5s ease;

    .background-image {
      
      width: 200%;
      aspect-ratio: 1 / 1;
      position: absolute;
      background-position: 50% 50%;
      top: -150%;
      background-image: url('~/assets/image/imgpsh_fullsize_anim.jpg');
      animation: fullrotate 5s infinite ease-out;
      background-size: 100%;

      @keyframes fullrotate {
        0% {
          -webkit-transform: rotate(0);
          transform: rotate(0);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    }

    .location-text {
      padding: 5px 20px;
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      text-align: left;
      width: 100%;
      color: white;
      & > img {
        margin: auto;
        height: 40px;
        filter: drop-shadow(0 0 1px white);
      }
      & .address {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }

    & .search-bar-container {
      // z-index: 99;
      // align-items: center;
      // position: absolute;
      // top: 100%;
      // transform: translateY(-50%);
      // width: calc(100% - 40px);
      padding-bottom: 10px !important;

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        margin: auto;

        background: white;
        overflow: hidden;
        //   padding: 0 20px;
        box-shadow: 1px 1px 3px -1px;
        //   width: 100%;
      }

      & .search-input-container {
        width: 100%;
        height: 40px;
        border-color: transparent;
        outline: none;
      }
    }
    // & .search-container {
    //   border-radius: 15px;
    //   background: white;
    //   overflow: hidden;
    //   padding: 0 20px;
    //   box-shadow: 1px 1px 3px -1px;
    //   width: 100%;
    // }
    // & .search-input-container {
    //   width: 100%;
    //   height: 50px;
    //   // background-color: #f5f6fa;
    //   // border-radius: 0 5px 5px 0 !important;
    //   border-color: transparent;
    //   outline: none;
    // }

    & .search-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .clear-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .voice {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
      font-size: 1.25em;
    }
  }
  & .location-and-search.scale-down {
    // height: fit-content;
    // min-height: 0;
    padding-bottom: 0;

    & .search-bar-container {
      position: relative;
      width: calc(100% - 80px);
      top: unset;
      transform: none;
      border-radius: 2em;
      padding: 0 5px;
      width: 100%;

      & > button {
        width: 40px;
        height: 100%;
        color: white;
        font-size: 1em;
        position: relative;
      }

      & > .cart-in-search {
        animation: none;
        & > em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 15px;
          height: 15px;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          position: absolute;
          bottom: 2px;
          right: 2px;
          line-height: 1;
          box-shadow: 0 0 0px 1px white;
          font-weight: 500;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        margin: auto;
      }

      & .search-input-container {
        height: 40px;
      }
    }
  }

  .empty-container {
    flex: 1;
    padding: 15px;
    margin: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .empty-image {
    margin: 0px 10px;
    justify-content: center;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    object-fit: contain;
  }

  .empty-text {
    margin: 10px 0 0;
    font-size: 1.5em;
    text-align: center;
    color: var(--primary-color-1);
  }

  .empty-action {
    color: var(--primary-color-1);
    text-align: center;
    font-size: 1.3em;
    cursor: pointer;
    padding: 0;
  }

  .location-value {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
  }

  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 40px;
    padding: 0 10px;
    aspect-ratio: 1;
    background: var(--primary-color-1);
    border-radius: 5px;
    color: white;
    border: none;
    font-size: 1.3em;
  }

  .filter-result-container {
    display: flex;
    width: 100%;
    flex: 1;
    position: relative;
  }

  #leaflet_map {
    width: 100%;
    height: 500px;
    min-height: 500px;
    /* height: 100%; */
    outline: none;
    z-index: 1;
  }

  .dashboard {
    // position: absolute;
    // top: 0;
    // left: 0;
    flex: 1;
    z-index: 2;
    width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    background: #f7f7f7;
    padding: 0 0 5px;
    overflow: hidden;
  }

  .list-result {
    z-index: 3;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: white;
    padding: 10px;

    & > .result-item-container {
      display: flex;
      border: thin solid transparent;
      border-radius: 10px;

      // box-shadow: 2px 4px 8px 0 rgba(0, 0, 0, 0.15);
      gap: 10px;
      background: white;
      color: #2f3640;
      position: relative;

      & a {
        color: unset;
      }

      & > .product-content {
        padding: 15px 10px 10px 15px;
        border-radius: 10px;
        gap: 5px;
        cursor: pointer;

        & > .product-detail {
          display: flex;
          flex: 1;
          gap: 10px;
          width: 100%;
          align-items: flex-start;

          & > a {
            display: flex;
            flex: 1;
            gap: 10px;

            align-items: flex-start;

            & > img {
              width: 25%;
              border-radius: 10px;
              aspect-ratio: 1;
              max-height: 200px;
              object-fit: cover;
            }

            & > .product-info {
              display: flex;
              flex-direction: column;
              text-align: left;
              align-self: stretch;
              padding: 5px 0;
              justify-content: space-between;
              flex: 1;

              & > .product-name {
                color: #595959;
                font-size: 1.1em;
                font-weight: bold;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                overflow: hidden;
              }

              & .product-price {
                color: #ed1b24;
                font-size: 1.2em;
                font-weight: bold;
                margin-top: auto !important;
              }

              & .product-origin-price {
                color: #898989;
                font-size: 1em;
                font-weight: bold;
                text-decoration: line-through;
              }

              & .add-to-cart {
                background: #ed1b24;
                color: white;
                font-size: 1.2em;
                border-radius: 2em;
                padding: 5px 15px;
                margin-left: auto;
              }
            }
          }
        }

        & > .shop-detail {
          width: 100%;
          justify-content: space-between;

          & > a {
            display: flex;
            width: 100%;
            justify-content: space-between;
            font-weight: bold;

            & > .shop-name {
              gap: 5px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              padding-right: 5px;

              & > svg {
                vertical-align: baseline;
              }
            }

            & > .shop-distance {
              display: flex;
              align-items: flex-end;
              font-style: italic;
              font-size: 0.9em;
              color: #6f6e6e;
              white-space: nowrap;
              flex-wrap: wrap;
            }
          }
        }
      }

      // & .shop-content {
      //   display: flex;

      //   & > a {
      //     width: 100%;
      //     gap: 10px;
      //     display: flex;
      //     align-items: flex-start;
      //     justify-content: flex-start;
      //   }
      //   & img {
      //     width: 25%;
      //     border-radius: 5px;
      //     aspect-ratio: 1;
      //     max-height: 200px;
      //     object-fit: cover;
      //   }

      //   & .shop-info {
      //     display: flex;
      //     flex-direction: column;
      //     justify-content: flex-start;
      //     align-items: flex-start;
      //     flex: 1;
      //     gap: 10px;
      //     text-align: left;

      //     & > .shop-name {
      //       color: var(--primary-color-1);
      //       font-weight: bold;
      //       font-size: 1.1em;
      //     }

      //     & > .shop-address {
      //       color: var(--color-text-black);
      //       font-size: 0.9em;
      //     }

      //     & > .shop-distance {
      //       color: var(--color-text-note);
      //       font-size: 0.9em;
      //     }
      //   }
      // }
      // & .shop-products-container {
      //   width: 90%;
      //   margin-left: auto;

      //   & > a {
      //     width: 100%;
      //     gap: 10px;
      //     display: flex;
      //     align-items: flex-start;
      //     justify-content: flex-start;
      //   }
      //   & .product-content {
      //     width: 100%;
      //     display: flex;
      //     align-items: flex-start;
      //     justify-content: flex-start;
      //     gap: 10px;
      //     text-align: left;

      //     & img {
      //       width: 25%;
      //       aspect-ratio: 1;
      //       max-height: 200px;
      //       border-radius: 5px;
      //       object-fit: contain;
      //       background: var(--color-background-2);
      //     }

      //     & .product-info {
      //       display: flex;
      //       flex-direction: column;
      //       gap: 5px;

      //       & > .product-name {
      //         font-weight: 500;
      //         color: var(--color-text-black);
      //       }

      //       & > .product-price {
      //         font-size: 1.5em;
      //         color: var(--primary-color-1);
      //       }
      //     }
      //   }
      // }
    }

    & > .result-item-container:hover {
      background: linear-gradient(#fff7f8, #fff7f8) padding-box,
        linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
    }

    & > .result-item-container::after {
      content: "";
      height: 1px;
      width: 75%;
      background-color: #e5e5e5;
      position: absolute;
      bottom: 0;
      right: 0;
    }

    & > .result-item-container:hover::after {
      height: 0;
    }

    & > .empty-search,
    .search-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }

    & .search-placeholder > img {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }

    & .showed-all {
      color: #626262;
      font-style: italic;
      width: 100%;
      text-align: center;
      padding: 20px 0;
    }
  }
  .search-result {
    z-index: 3;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: #f4f4f4;

    & > .history-search {
      padding: 10px 15px;
      background: #fafafa;
      display: flex;
      flex-direction: column;
      gap: 10px;
      font-weight: 600;

      & > .list-history-search {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;

        & > button {
          border: thin solid #b2b2b2;
          padding: 8px 20px;
          border-radius: 10px;
          color: var(--color-text-black);
        }
      }
    }

    & > .categories-and-list-suggest {
      display: flex;
      // overflow: auto;
      position: relative;
      flex: 1;
      background: #fafafa;

      & > .categories {
        width: 15%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        background: #f4f4f4;
        height: fit-content;

        & > .item-category.active {
          border-right: 2px solid #ed1b24;
        }
        & > .item-category.active::after {
          content: "";
          width: 13px;
          height: 13px;
          background: #ed1b24;
          position: absolute;
          // border-radius: 5px 0;
          top: 50%;
          right: 0;
          transform: translate(50%, -50%) rotate(45deg);
        }
        & > .item-category {
          width: 100%;
          display: flex;
          flex-direction: column;
          background-color: white;
          padding: 10px 10px 5px;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: #626262;
          position: relative;
          overflow: hidden;

          & > img {
            width: 50px;
            height: 50px;
            aspect-ratio: 1;
            border-radius: 50%;
            background-color: #646464;
          }

          & > svg {
            width: 50px;
            height: 50px;
          }

          & > span {
            font-size: 0.8em;
          }
        }
      }
      & > .list-search-suggest-container {
        background: #fafafa;
        flex: 1;
        height: fit-content;
        min-height: 100%;
        display: flex;
        flex-wrap: wrap;

        & > .list-search-suggest {
          padding: 10px 15px;
          gap: 10px;
          flex: 1;
          height: fit-content;
          min-height: 100%;
          display: flex;
          flex-wrap: wrap;
          font-weight: 600;
          & > span {
            width: 100%;
          }
          & > .search-result-item-container {
            display: flex;
            width: calc(50% - 5px);
            align-self: normal;
            align-items: center;
            flex-direction: column;
            background: white;
            border-radius: 10px;
            color: #626262;
            box-shadow: 0 0 5px rgb(0 0 0 / 20%);

            & > img {
              border-radius: 10px 10px 0 0;
              width: 100%;
              max-height: 200px;
              aspect-ratio: 1;
              object-fit: cover;
            }

            & > .items-suggest-content {
              padding: 10px 10px 20px;
              display: flex;
              width: 100%;
              flex: 1;
              flex-direction: column;
              gap: 5px;
              justify-content: flex-end;

              & > .name {
                color: #626262;
                text-align: left;
                font-weight: 500;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
              & > .shop-name {
                display: flex;
                width: 100%;
                gap: 5px;
                align-items: baseline;
                overflow: hidden;
                font-weight: bold;
                font-style: italic;
                color: #399901;
                text-align: center;
                font-size: 0.9em;
                justify-content: center;
              }

              & > .price {
                text-align: center;
                color: #ed1b24;
                font-size: 1.3em;
                font-weight: bold;
              }

              & > .origin-price {
                text-align: center;
                color: #626262;
                font-size: 1.2em;
                text-decoration: line-through;
              }

              & > .add-to-cart {
                background: #ed1b24;
                color: white;
                font-size: 1.2em;
                border-radius: 2em;
                padding: 5px 15px;
              }
            }
          }

          & > .empty-search,
          .search-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
          }

          & .search-placeholder > img {
            width: 200px;
            height: 200px;
            object-fit: cover;
          }
        }

        & .showed-all {
          color: #626262;
          font-style: italic;
          width: 100%;
          text-align: center;
          padding: 20px 0;
        }
      }
    }
  }

  .search-result.show {
    display: flex;
  }
  .dashboard.show-search {
    height: 0;
    z-index: 1;
  }

  .loading-more {
    background-color: transparent;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .categories-filter-tab {
    text-transform: none;
    font-size: 18px;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    align-items: center;

    & .category-item-tab {
      margin: 5px;
      padding: 5px;
      // border: 2px solid var(--primary-color-1);
      align-self: flex-start;
      border-radius: 10px !important;
      height: unset !important;
      width: -moz-fit-content;
      width: fit-content;
      color: var(--color-text-note);
      text-align: center;
      font-size: 1em;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        & span {
          text-transform: none;
          // color: var(--color-text-black);
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 500;
        }
      }
    }

    & .category-item-tab.active {
      color: var(--primary-color-1);
      font-weight: bold;
    }

    & .v-slide-group__next,
    .v-slide-group__prev {
      color: var(--primary-color-1);
      min-width: unset;
    }
  }

  .advertisments {
    min-height: 190px;
    aspect-ratio: 2/1;
  }
  .content-list {
    padding: 20px;
    background: white;
    margin-bottom: 5px;
    display: flex;
    flex-direction: column;
  }

  .content-list.hot-deal {
    padding: 20px 0;

    & .stack-content-title {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 20px 10px 20px;

      & > span {
        font-weight: bold;
        font-size: 1.2em;
        display: flex;
        justify-content: space-between;

        & > a {
          color: black;
          font-weight: 500;
          font-size: 1rem;
          cursor: pointer;
        }
      }
      & > span + span {
        font-weight: 500;
        font-size: 0.9rem;
        cursor: pointer;
      }
    }
  }
  .content-list.best-around {
    background: transparent;
  }

  & .stack-content-title {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 0 10px 0;

    & > span {
      font-weight: bold;
      font-size: 1.2em;
      display: flex;
      justify-content: space-between;

      & > a {
        color: black;
        font-weight: 500;
        font-size: 1rem;
        cursor: pointer;
      }
    }
    & > span + span {
      font-weight: 500;
      font-size: 0.9rem;
      cursor: pointer;
    }
  }

  & .stack-content-list {
    display: flex;
    flex: 1;
    min-height: 200px;
    overflow: hidden;
    position: relative;

    & > .none-content-list {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      color: var(--color-text-note);
    }
  }

  & .my-carousel {
    width: 100%;
    height: 100%;

    & img {
      width: 100%;
      height: 100%;
      max-height: 150px;
      object-fit: cover;
    }

    & .my-adv-carousel-pagination {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: fit-content;
      margin-left: 10px;
      margin-bottom: 10px;
      align-items: center;
      display: flex;

      & > .swiper-pagination-bullet {
        background: #37363b;
        width: 15px;
        height: 15px;
        opacity: 0.4;
        border-radius: 50%;
      }

      & > .swiper-pagination-bullet[class*="active"] {
        opacity: 1;
      }
    }
  }
  & .stack-carousel {
    flex: 1;

    & > .carousel__viewport {
      height: 100%;
      display: flex;
      gap: 10px;

      & img {
        width: 100%;
        height: 100%;
        max-height: 200px;
        min-height: 200px;
        object-fit: contain;
      }
    }

    & .item-stack-slide {
      // margin-right: 10px;
      // padding: 0 5px !important;
      height: 100%;
      cursor: pointer;
    }

    & .item-stack-slide:last-child {
      padding-right: 0;
    }
  }
  & .item-stack {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #f0f0f0;
    position: relative;

    & > .distance {
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 10px;
      color: white;
      border-bottom: 2px solid white;
      border-right: 2px solid white;
      background: #f05976;
      border-radius: 15px 0;
      font-size: 0.9em;
    }
    & > img {
      flex: 1;
      background: var(--color-background-2);
      width: 100%;
      height: 100%;
      max-height: 200px;
      min-height: 200px;
      object-fit: cover;
    }

    & > .item-stack-content {
      display: flex;
      flex-direction: column;
      padding: 10px;
      text-align: left;
      font-size: 1em;

      & > .name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
      }

      & .price {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        color: #f05976;

        & > .off {
          color: var(--color-text-note);
          text-decoration: line-through;
          font-style: normal;
        }
      }
    }
  }

  & .stack-carousel.best-around-carousel {
    padding: 5px;

    & .item-stack-slide {
      padding: 5px 0px 30px;
      height: auto;
    }

    & .item-stack {
      overflow: visible;
      background: white;
      box-shadow: 0 0 5px rgb(0 0 0 / 20%);
    }

    // & img {
    //   min-height: 100px;
    //   max-height: 100px;
    //   border-radius: 15px 15px 0 0;
    //   object-fit: cover;
    // }

    & .shop-logo {
      // position: relative;
      // border-radius: 50%;
      // width: 75px !important;
      // min-width: 75px;
      // height: 75px !important;
      // border: 0;
      // display: flex;
      // align-items: center;
      // align-self: flex-start;
      // justify-content: center;
      // font-size: 1.1em;
      // padding: 0;
      // box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
      // overflow: hidden;
      margin: 10px auto;
      // width: 75px !important;
      // height: 75px !important;
      border-radius: 50%;
      box-shadow: 0 0 1em rgb(0, 0, 0, 0.1);
      aspect-ratio: 1;
      // max-height: 200px;
      // object-fit: cover;
      // display: flex;
      // align-items: center;
      // justify-content: center;
      // overflow: hidden;
      // & > .logo-origin-container {
      //   transform: scale(calc(75 / var(--scale-origin)));

      //   & > img {
      //     width: auto;
      //     height: auto;
      //     min-width: 100%;
      //     min-height: 100%;
      //   }
      // }
      // & > img {
      //   width: 100%;
      //   height: 100%;
      //   object-fit: cover;
      // }
    }

    & .item-stack-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      text-align: center;
      font-size: 1em;
      padding-bottom: 25px;
      position: relative;

      & > .name {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        text-overflow: ellipsis;
        font-weight: bold;
        white-space: normal;
      }

      & .distance {
        text-overflow: ellipsis;
      }
    }

    & .go-to-shop {
      position: absolute;
      width: fit-content;
      white-space: nowrap;
      left: calc(50%);
      bottom: 0;
      transform: translate(-50%, 50%);
      border-radius: 2em;
      background: linear-gradient(to right, #f79771 10%, #f05976 80%);
      color: white;
      padding: 5px 15px;
      font-size: 0.9em;
      display: flex;
      gap: 5px;
      align-items: center;
      justify-content: center;

      & > .svg {
        width: 1.3em;
        height: 1.3em;
      }
    }
  }

  & .stack-carousel.hot-deal-carousel {
    background-color: white;
    & .item-stack-slide {
      padding-right: 0;
    }

    & .item-stack {
      overflow: visible;
      border-radius: 0;
      background: white;
    }

    & img {
      min-height: 150px;
      max-height: 150px;
      border-radius: 0;
      object-fit: cover;
    }

    & .item-stack-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      text-align: center;
      font-size: 1em;
      padding-bottom: 15px;
      position: relative;

      & > .name {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        text-overflow: ellipsis;
        font-weight: bold;
        white-space: normal;
        color: #f05675;
      }

      & .address {
        color: #b2b5bb;
        font-style: italic;
        font-size: 0.9em;
      }

      & .hot-deal-info {
        display: flex;
        justify-content: space-between;
        text-transform: uppercase;
      }

      & .svg {
        width: 1.3em;
        height: 1.3em;
      }
    }

    & .hot-deal-carousel-pagination {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: fit-content;
      align-items: center;
      display: flex;

      & > .swiper-pagination-bullet {
        background: #b2b5bb;
        height: 2px;
        width: 15px;
        margin: 0;
        opacity: 1;
        border-radius: 0;
      }

      & > .swiper-pagination-bullet[class*="active"] {
        background-color: #f05675;
        height: 4px;
        width: 18px;
        border-radius: 5px;
      }
    }
  }

  & .product-stack-content {
    display: flex;
    justify-content: space-between;
    row-gap: 10px;
    flex-wrap: wrap;
    width: 100%;

    & > .item-stack {
      flex-basis: calc(50% - 5px);
      height: fit-content;

      // & > .item-stack-content > .name{
      //   display: -webkit-box;
      //   -webkit-line-clamp: 2;
      //   -webkit-box-orient: vertical;
      //   white-space: wrap;
      //   line-height: 1.2;
      // }
    }
  }
}
