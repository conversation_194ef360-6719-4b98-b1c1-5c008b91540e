.image-viewer-container{
  z-index: 10000;
}
.image-viewer-content-container {
  width: 100dvw;
  height: 100dvh;
  //   width: 100vw;
  //   height: 100vh;
  background-color: transparent;
  display: flex;
  background: rgb(0, 0, 0, 0.4);
  flex-direction: column;

  & > .title-header {
    background: transparent;
    color: white;
    width: 100%;
    max-width: unset;
    border: none;
  }

  & > .viewer-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    & .my-swiper {
      width: 100%;
      height: calc(100% - 100px);
    }

    & > .image-viewer-carousel {
      flex: 1;
      width: 100%;
      // height: 100%;
    }

    & .item-stack-slide {
      //   width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease-in-out;
      position: relative;

      & > img {
        width: auto;
        height: auto;
        max-width: calc(100% - 20px);
        max-height: calc(100% - 20px);
        object-fit: contain;
        transition: all .3s ease-in-out;
      }

      & > .fancy-box-container{
        width: 100%;
        height: 100%;
        padding: 10px;
      }
    }

    & .thumb-container {
      height: 100px;
      width: 100%;
      box-sizing: border-box;
      padding: 10px;
      background: rgb(0, 0, 0, 0.2);
    }

    .my-swiper-thumb {
      width: 100%;
      height: 100%;
      max-width: 1024px;

      & .item-swiper-thumb {
        border-radius: 10px;
        filter: brightness(0.5);
        background: rgb(0, 0, 0, 0.4);
      }

      & .swiper-slide-thumb-active {
        filter: none;
      }

      & img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        border-radius: 10px;
      }
    }
  }

  & .swiper-button-prev,
  .swiper-button-next {
    font-size: 30px;
    padding: 0 10px;
    width: fit-content;
  }
}
