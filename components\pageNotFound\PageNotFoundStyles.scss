.page-not-found-container{
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 15px;
    align-items: center;
    flex: 1;
    // height: 100% !important;
    height: auto !important;
    min-height: auto !important;
    background-color: white;
    position: relative;
    padding: 10px;
    margin: 0 !important;

    & > img {
        width: 80%;
        max-width: 400px;
    }
  
    & > span{
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        color: var(--primary-color-2);
    }
    & > button {
        background: var(--primary-color-1);
        color: white;
        border-radius: 2em;
        border: none;
        width: fit-content;
        padding: 5px 1em;
        font-size: 17px;
        font-weight: 400;
    }
}