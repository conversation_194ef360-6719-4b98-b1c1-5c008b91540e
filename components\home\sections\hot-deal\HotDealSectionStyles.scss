.hot-deal.content-list{
    & .item-stack-slide.best-around-stack {
        --item-stack-slide-width: 300px;
        width: var(--item-stack-slide-width);
        height: calc(var(--item-stack-slide-width)*2);
        padding: 0;
        position: relative;
        // border-radius: 0;
        border: none;
    
        & .item-stack-header {
          background: linear-gradient(to bottom,
              rgb(0, 0, 0, 1) 10%,
              transparent 65%);
          color: white;
          position: absolute;
          top: 0;
          width: 100%;
          padding: 5px;
          height: 15%;
          font-size: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
    
          &>span {
            display: flex;
            align-items: center;
            gap: 2px;
    
            &>svg {
              font-size: 15px;
            }
          }
        }
    
        & .item-stack-footer {
          background: linear-gradient(to top, rgb(0, 0, 0, 0.5), transparent);
          color: white;
          font-weight: 700;
          position: absolute;
          bottom: 0;
          width: 100%;
          padding: 5px;
          min-height: 15%;
          height: fit-content;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: flex-start;
    
          &>span {
            text-align: left;
            width: 100%;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            gap: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
    
        & .primary-content {
          width: 100%;
          height: 100%;
    
          &>img {
            object-fit: cover;
            width: 100%;
            height: 100%;
            max-height: unset;
          }
        }
    
        @media screen and (min-width: 1321px) {
          --item-stack-slide-width: 300px;
        }
    
        @media screen and (max-width: 1320px) and (min-width: 1200px) {
          --item-stack-slide-width: 300px;
        }
    
        @media screen and (max-width: 1200px) and (min-width: 721px) {
          --item-stack-slide-width: 300px;
        }
      }
}

