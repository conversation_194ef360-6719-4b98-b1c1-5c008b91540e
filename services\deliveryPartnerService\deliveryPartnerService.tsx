import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";
import type { member_type } from "~/components/chatManage/ChatDTO";

export class DeliveryPartnerService extends BaseHTTPService {
  shopListPartner(
    shop_id:string,
    offset = 0,
    limit = 1000
  ) {
    return this.https("GET", `${appConst.apiURL.listDeliveryPartnerOfShop}?shop_id=${shop_id}&limit=${limit}&offset=${offset}`);
  }

  getShopDeliveryPartnerInfo(shop_id: string, delivery_partner_id: string){
    let body = {
      shop_id: shop_id,
      delivery_partner_id: delivery_partner_id
    }
    return this.https("POST", `${appConst.apiURL.infoDeliveryPartnerOfShop}`, body, null, true);
  }

  addDeliveryPartnerToShop(body:any){
    return this.https("POST", appConst.apiURL.addDeliveryPartnerToShop, body, null, true);
  }

  updateDeliveryPartnerInfo(body:any){
    return this.https("POST", appConst.apiURL.updateDeliveryPartnerToShop, body, null, true);
  }
}
