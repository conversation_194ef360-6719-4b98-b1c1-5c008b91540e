<template>
	<!-- <NuxtLoadingIndicator /> -->
	<ProductV2Component />
</template>
<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import environment from '~/assets/environment/environment';

const nuxtApp = useNuxtApp();
onMounted(() => {
	nuxtApp.$emit(appConst.event_key.show_footer, false)
})
</script>
<!-- <script setup>
import { appConst } from '~/assets/AppConst';

const  slug = useRoute().params
console.log(slug)
const url = appConst.apiURL.deleteProduct + slug;
const { data: product } = await $fetch(url, { key: slug });
console.log(product.value);
</script> -->