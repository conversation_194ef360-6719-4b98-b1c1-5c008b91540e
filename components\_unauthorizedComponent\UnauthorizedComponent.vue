<template>
    <div class="public-container ">
        <div class="v-stack unauthorized-container">
            <SubHeaderV2Component class="register-shop-header"
                :title="props.title ?? $t('UnauthorizedComponent.ban_khong_co_quyen_truy_cap')"></SubHeaderV2Component>
            <div class="access-denied">
                <img loading="lazy" :src="access_denied" :placeholder="access_denied"
                    :alt="$t('UnauthorizedComponent.ban_khong_co_quyen_truy_cap')" />
                <em>
                    {{ props.message ?? $t('UnauthorizedComponent.ban_khong_co_quyen_truy_cap') }}
                </em>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import access_denied from "~/assets/image/access_denied.png";

const props = defineProps({
    title: null,
    message: null
})
const {t} = useI18n();
useSeoMeta({
    title: t('UnauthorizedComponent.ban_khong_co_quyen_truy_cap')
})
onMounted(() => {

});

onUnmounted(() => {
})

</script>

<style lang="scss" src="./UnauthorizedStyles.scss"></style>