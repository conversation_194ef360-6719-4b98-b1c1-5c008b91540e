<template>
	<div class="public-container">
		<div class='v-stack page-not-found-container'>
			<img loading="lazy" :src='page_not_found' :placeholder="page_not_found"
				:alt="$t('PageNotFoundComponent.khong_tim_thay_trang')" />
			<span>
				{{ props.title ?? $t('PageNotFoundComponent.khong_tim_thay_trang') }}
			</span>
			<button v-if="!props.hide_back_home" v-on:click="() => {
				router.push(appRoute.HomeComponent)
			}">
				{{ $t('PageNotFoundComponent.quay_lai_trang_chu') }}
			</button>
		</div>
	</div>


</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import type { CartDto } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';
import { ProductService } from '~/services/productService/productService';
import { UserService } from '~/services/userService/userService';
import { connectFirestoreEmulator } from "firebase/firestore";
import page_not_found from "~/assets/image/page-not-found.png"

const props = defineProps({
	title: null,
	hide_back_home: null
})
const router = useRouter();
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.PageNotFoundComponent'),
});
</script>
<style lang="scss" src="./PageNotFoundStyles.scss"></style>