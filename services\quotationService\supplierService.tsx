
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class SupplierService extends BaseHTTPService {

  add(name = "", phone = "", address ="", email = "", shop_id = ""){
    let body = {
      name: name,
      phone: phone,
      address: address,
      email: email,
      shop_id: shop_id,
    }
    let url = appConst.apiURL.supplierAdd
    // let url = "http://clomart.xampps/api/v1/supplier/add";
    return this.https("POST", url, body);
  }

  list(shop_id?:string){
    let body = {
      shop_id: shop_id,
    }
    let url = appConst.apiURL.supplierList
    // let url = "http://clomart.xampps/api/v1/supplier/list";
    return this.https("GET", url, body);
  }
}