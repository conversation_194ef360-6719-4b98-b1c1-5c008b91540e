// import this after install `@mdi/font` package
import '@mdi/font/css/materialdesignicons.css'

import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import { appConst } from '~/assets/AppConst';
import { en, vi, ru, ko } from 'vuetify/locale';
import ISO6391 from 'iso-639-1';

export default defineNuxtPlugin((nuxtApp) => {
  const i18n:any = nuxtApp.$i18n;
  const vuetify = createVuetify({
    locale: {
      locale: i18n.locale.value,
      fallback: appConst.defaultLanguage,
      messages: { en, vi, ru, ko }
    },
    // ... your configuration
  })
  watchEffect(() => {
    vuetify.locale.current.value = i18n.locale.value
  })
  nuxtApp.vueApp.use(vuetify)
})