import axios from "axios";
import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";

export class OrderService extends BaseHTTPService {  create(body: any) {
    // Get referral code from localStorage if available
    let referralCode = null;
    if (process.client) {
      referralCode = localStorage.getItem('referral_code');
    }

    let bodyRef = {
      ...body,
      customer_latitude: body.customer_latitude ? body.customer_latitude : body.latitude,
      customer_longitude: body.customer_longitude ? body.customer_longitude : body.longitude,
      referral_code: referralCode, // Include referral_code in order creation
      items: body.items.map((e: any) => {
        return {
          product_id: e.product_id,
          quantity: e.quantity,
          notes: e.notes,
        };
      }),
    };
    return this.https("POST", appConst.apiURL.orderCreate, bodyRef);
  }

  orderByShopId(
    shopId: string,
    offset = 0,
    limit = 20,
    status?: number | null,
    search_text?: string | null,
    start_date?: string | null,
    end_date?: string | null
  ) {
    let body = {
      shop_id: shopId,
      offset: offset,
      limit: limit,
      status: status,
      search_text: search_text,
      start_date: start_date,
      end_date: end_date,
    };
    return this.https("POST", appConst.apiURL.orderByShopId, body);
  }

  orderByCustomerId(
    customer_id: string,
    offset = 0,
    limit = 20,
    status: number | null,
    search_text?: string | null,
    start_date?: string | null,
    end_date?: string | null
  ) {
    let body = {
        customer_id: customer_id,
        offset: offset,
        limit: limit,
        status: status,
        search_text: search_text,
        start_date: start_date,
        end_date: end_date,
      };
    return this.https("POST", appConst.apiURL.orderByCustomerId, body);
  }

  orderByCustomerIdAndShopId(
    customer_id: string,
    shop_id: string,
    offset = 0,
    limit = 20,
    status: number | null,
    search_text?: string | null,
    start_date?: string | null,
    end_date?: string | null
  ) {
    let body = {
        customer_id: customer_id,
        shop_id: shop_id,
        offset: offset,
        limit: limit,
        status: status,
        search_text: search_text,
        start_date: start_date,
        end_date: end_date,
        mode: 'both'
      };
    return this.https("POST", appConst.apiURL.orderByCustomerIdAndShopId, body);
  }


  countOrderByStatus(shopId: string) {
    let body = {
      shop_id: shopId,
    };
    return this.https("POST", appConst.apiURL.countOrderByStatus, body);
  }

  updateOrder(order: any) {
    let body = {
      id: order.id,
      status: order.status,
      short_code: order.short_code,
      notes: order.notes,
      address: order.address,
      province_id: order.province_id,
      district_id: order.district_id,
      ward_id: order.ward_id,
      customer_id: order.customer_id,
      customer_name: order.customer_name,
      customer_phone: order.customer_phone,
      customer_latitude: order.customer_latitude ? order.customer_latitude : order.latitude,
      customer_longitude: order.customer_longitude ? order.customer_longitude : order.longitude,
      price: order.price,
      price_off: order.price_off,
      delivery_type: order.delivery_type,
      delivery_price: order.delivery_price,
      payment_method: order.payment_method,
      shop_id: order.shop_id,
      items: order.items,
      grand_total: order.grand_total,
      discount_amount: order.discount_amount,
      total_amount: order.total_amount,
      delivery: order.delivery,
      driver_id: order.driver_id,
      images: order.images,
      image_delete: order.image_delete
    };

    return this.https("POST", appConst.apiURL.orderUpdate, body);
  }

  detailOrder(orderId: string) {
    let url = appConst.apiURL.orderDetail + "/" + orderId;
    return this.https("GET", url);
  }
}
