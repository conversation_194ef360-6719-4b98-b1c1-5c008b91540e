.none-my-shop-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  align-items: center;
  justify-content: center;
  gap: 15px;

  // & > .title-header {
  //   width: 100%;
  //   // font-size: 1.6em;
  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .none-my-shop-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 10px;

    & > img {
      width: 300px;
      height: 300px;
      align-items: center;
      object-fit: contain;
      position: relative;
    }

    & span {
      font-size: 1.5em;
      font-weight: 500;
    }

    & button {
      background-color: var(--primary-color-1) ;
      border-radius: 2em !important;
      padding: 5px 20px !important;
      color: white;
      font-size: 17px !important;
      border: none;
    }
  }
}