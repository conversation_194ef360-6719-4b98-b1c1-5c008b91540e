<template>
	<div class="sale-off content-list">
		<div class="stack-content-title">
			<div class="section-title">
				<!-- <img :src="flash_sale" />
                                <client-only>
                                    <vue-countdown class="cool-down" :time="(moment().endOf('day').diff(moment()))"
                                        v-slot="{ hours, minutes, seconds }">
                                        <span>
                                            <em>{{ Math.floor(hours / 10) }}</em>
                                            <em>{{ hours % 10 }}</em>
                                        </span>
                                        : <span>
                                            <em>{{ Math.floor(minutes / 10) }}</em>
                                            <em>{{ minutes % 10 }}</em>
                                        </span>
                                        : <span>
                                            <em>{{ Math.floor(seconds / 10) }}</em>
                                            <em>{{ seconds % 10 }}</em>
                                        </span>
                                    </vue-countdown>
                                </client-only> -->
				<Icon name="solar:sale-outline"></Icon>
				<span>
					{{ $t('HomeV2Component.giam_gia') }}
				</span>
			</div>

			<nuxt-link :to="{
				path: appRoute.AroundComponent,
				query: {
					filter: JSON.stringify({
						...props.filter_data,
						is_sale_off: true
					})
				}
			}" class="view-all">
				{{ $t('HomeV2Component.xem_tat_ca') }}
				<Icon name="ic:round-chevron-right"></Icon>
			</nuxt-link>
		</div>

		<div class="stack-content-list">

			<!-- <Swiper v-if="listSaleOff?.length && !loadingSaleOff" class="my-carousel stack-carousel"
                                :modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
                                :loop="false" :effect="'creative'" :navigation="true" :freeMode=false :autoplay="false"
                                @init="(e) => {
                                    sale_off_el = e
                                }" :space-between="10" key="sale-off-carousel" ref="sale_off_el">
                                <SwiperSlide class="item-stack-slide" v-for="item of listSaleOff"
                                    :key="'sale_off_' + item.id">
                                    <img :src="hot_sale" class="top-left-tag" />
                                    <nuxt-link class="item-stack"
                                        :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                        :title="showTranslateProductName(item)">

                                        <img loading="lazy" :src="item && item.profile_picture
                                            ? domainImage + item.profile_picture
                                            : icon_for_product" :placeholder="icon_for_product" />
                                        <div class="item-stack-content">
                                            <span class="name">{{ showTranslateProductName(item) }}</span>
                                            <span class="price">
                                                <em class="off"
                                                    v-if="(item.price_off != null && item.price_off < item.price)">{{
                                                        (parseFloat(item.price) == 0 || item.price == null)
                                                            ? $t('HomeV2Component.gia_lien_he')
                                                            : formatCurrency(parseFloat(item.price), item.shop ?
                                                                item.shop.currency
                                                                : item.currency)
                                                    }}</em>
                                                {{
                                                    (item.price_off != null && item.price_off < item.price) ?
                                                        formatCurrency(parseFloat(item.price_off), item.shop ?
                                                            item.shop.currency : item.currency) : (parseFloat(item.price) == 0 ||
                                                                item.price == null) ? $t('HomeV2Component.gia_lien_he') :
                                                            formatCurrency(parseFloat(item.price), item.shop ?
                                                                item.shop.currency : item.currency) }} </span>
                                        </div>
                                    </nuxt-link>
                                </SwiperSlide>
                            </Swiper> -->
			<div class="stack-content-list-container" v-if="listSaleOff?.length && !loadingSaleOff">
				<div class="item-stack-slide" v-for="item of listSaleOff" :key="'sale_off_' + item.id"
					:id="'sale_off_' + item.id">
					<img :src="hot_sale" class="top-left-tag" />
					<nuxt-link class="item-stack"
						:to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
						:title="showTranslateProductName(item)">

						<img loading="lazy" :src="item && item.profile_picture
							? domainImage + item.profile_picture
							: icon_for_product" :placeholder="icon_for_product" />
						<div class="item-stack-content">
							<span class="name">{{ showTranslateProductName(item) }}</span>
							<span class="price">
								<em class="off" v-if="(item.price_off != null && item.price_off < item.price)">{{
									(parseFloat(item.price) == 0 || item.price == null)
										? $t('HomeV2Component.gia_lien_he')
										: formatCurrency(parseFloat(item.price), item.shop ?
											item.shop.currency
											: item.currency)
								}}</em>
								{{
									(item.price_off != null && item.price_off < item.price) ?
										formatCurrency(parseFloat(item.price_off), item.shop ? item.shop.currency :
											item.currency) : (parseFloat(item.price) == 0 || item.price == null) ?
											$t('HomeV2Component.gia_lien_he') : formatCurrency(parseFloat(item.price), item.shop
												? item.shop.currency : item.currency) }} </span>
						</div>
					</nuxt-link>
				</div>
			</div>
			<div v-else class="none-content-list">
				<!-- {{ loadingSaleOff ? '' : $t('HomeV2Component.chua_co_san_pham_giam_gia') }} -->
			</div>
			<v-overlay v-model="loadingSaleOff" :z-index="100" :absolute="false" contained
				content-class='spinner-container' persistent scrim="#fff" key="loading_sale_off" no-click-animation>
				<Icon name="eos-icons:loading"></Icon>
			</v-overlay>
		</div>
	</div>
</template>

<style lang="scss" src="./SaleOffSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();
const emit = defineEmits(['business_type_click'])
const props = defineProps({
	filter_data: null,
	enable_load: null
})

var publicService = new PublicService();

var sale_off_el = useState<any>(() => { return null });
var listSaleOff = useState<any>('dashboard_sale_off', () => { return [] });
var loadingSaleOff = ref(false);
watch(() => [props.enable_load], () => {
	if (props.enable_load) {
		initSaleOff()
	}
})
onBeforeMount(() => {

})
onUnmounted(async () => {
});
onMounted(async () => {
	// initSaleOff()
});

function initSaleOff() {
	if (props.filter_data.latitude_user != null && props.filter_data.longitude_user != null) {
		let body = {
			section: "sale_off", //suggest,sale_off,best_around,hot_deal
			latitude_user: props.filter_data.latitude_user,
			longitude_user: props.filter_data.longitude_user
		}
		if (!listSaleOff.value?.length) {
			loadingSaleOff.value = true;
			publicService.dashboard({ ...body, section: 'sale_off' }).then(res => {
				if (res.status == HttpStatusCode.Ok) {
					listSaleOff.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
				}

				loadingSaleOff.value = false;

			});
		}
	}

}

</script>
