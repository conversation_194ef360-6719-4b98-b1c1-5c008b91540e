// import firebase from "firebase";
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import {
  getMessaging,
  getToken,
  onMessage,
  deleteToken,
  isSupported,
} from "firebase/messaging";
import { AuthService } from "~/services/authService/authService";
import { appConst, appFireBaseConfig } from "~/assets/AppConst";

// var nuxtApp = useNuxtApp();
// const app = initializeApp(appFireBaseConfig);
// export var messaging = await getMessaging(app);
// isSupported().then(value => {
//   if(value){
//     messaging = getMessaging(app);
//   }
// })


// export const messaging = nuxtApp.$messaging
// const messaging = (async () => {
//   try {
//     const isSupportedBrowser = await isSupported();
//     if (isSupportedBrowser) {
//       return getMessaging(app);
//     }
//     return null;
//   } catch (err) {
//     return null;
//   }
// })();
var authService = new AuthService();
// if(firebase.)

// if ("serviceWorker" in navigator) {
//   navigator.serviceWorker.getRegistrations().then(() => {
//     navigator.serviceWorker
//       .register("./firebase-messaging-sw.js")
//       .then(function (registration) {

//       })
//       .catch(function (err) {
//       });
//   });
// }

export function requestPermission() {
  if ("Notification" in window) {
    Notification.requestPermission().then((permission) => {
      if (permission === "granted") {
        getFCMToken()
      } else {

      }
    });
  }
}

export async function getFCMToken() {
  await getToken(messaging as any)
    .then(async (currentToken) => {
      if (currentToken) {
        let userInfoStr = await localStorage.getItem(appConst.storageKey.userInfo);
        let userInfo = JSON.parse(userInfoStr as string);

        let localToken = await localStorage.getItem(appConst.storageKey.token);
        localStorage.setItem(appConst.storageKey.fcmToken, currentToken);
        if (userInfo != null) {
          let bodyFCMToken = {
            token: currentToken,
            token_type: 2,
            user_id: userInfo.id
          }
          let token = await authService.saveFCMToken(bodyFCMToken);
        }
      } else {
      }
    })
    .catch((err) => {
      console.error(err);
    });
}
export async function refreshToken() {
  await deleteToken(messaging as any);
  getToken(messaging as any).then(async (newToken) => {
    let infor: any;
    let userInfoStr = await localStorage.getItem(appConst.storageKey.userInfo);
    infor = JSON.parse(userInfoStr as string);

    localStorage.setItem(appConst.storageKey.fcmToken, newToken);
    if (infor != null) {
      let bodyFCMToken = {
        token: newToken,
        token_type: 2,
        user_id: infor.id,
      };
      let token = await authService.saveFCMToken(bodyFCMToken);
    }
  });
}
