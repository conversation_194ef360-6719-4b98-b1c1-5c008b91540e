<script setup lang="ts">
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber, showTranslateProductName } from '~/assets/AppConst';
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import icon_for_product from '~/assets/image/icon-for-product.png';
import { ref } from 'vue';
import ISO6391 from 'iso-639-1';

import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import marker_location_icon from "~/assets/image/marker-location.png";

const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

var shopService = new ShopService();
var categoryService = new CategoryService();

var shop_id = ref((route.params?.slug ? route.params.slug : null) as string);

var refreshing = ref(false);
var cartData = ref([] as CartDto[]);
var shopDetails: any;
var countProducts = ref(0);
var shopData = ref();
var dataShopProducts = ref([
] as any);

var dataShopCategories = ref([] as any);

var selectedProduct = ref(null as any);
var categoryShowing = ref(null as any);
var indexCategoryShowing = ref(0);
var step = ref(1);
var loadMore = ref(false);
var filterProductLoading = ref(false);
var filterCategory = ref(null);
var elegantMenuBackground = ref("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQFGRvUB8kJoFprXHDPc5ChfXXFMrqjYDjrD83BIFkvfYSPJCbY")


var categoryCarousel = ref();
var categoryCarouselButton = ref();
var showModalConfirmResetCart = ref(false);
var showModalConfirm18Age = ref(false);

var selectedLanguage = ref(null as any);

onUnmounted(async () => { });
onMounted(async () => {
	step.value = 0;
	refreshing.value = true;
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartData();
	})

	await getDetailShop();
});
onBeforeMount(async () => {

})
function getDetailShop() {
	refreshing.value = true;
	shopService.superMenuShop(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			useSeoMeta({
				title: `Menu ${shopData.value.name}`
			});

			dataShopCategories.value = shopData.value.categories;
			shopData.value.language = shopData.value.language.at(0) != '[' ? shopData.value.language : JSON.parse(shopData.value.language);
			let indexCurrentLanguage = shopData.value.language.findIndex(function (itemLang: any) {
				return itemLang == nuxtApp.$i18n.locale.value
			})
			selectedLanguage.value = shopData.value.language[indexCurrentLanguage != -1 ? indexCurrentLanguage : 0];
			// getShopProduct(0, 20);
			// getListCategory();
			await getCartData();
			checkProductsQuantityInCart()
			refreshing.value = false;
		}
		else {
			refreshing.value = false;
		}
	}).catch(err => {
		refreshing.value = false;
	})
}

function getShopProduct(offset: any = 0, limit: any = 20) {
	loadMore.value = true;
	filterProductLoading.value = true;
	offset = offset != null ? offset : dataShopProducts.value.length;

	limit = limit != null ? limit : 20;

	let categories: any[] = filterCategory.value ? [filterCategory.value] : [];

	shopService.searchProductsInShopClient(
		"",
		shopData.value.id,
		categories,
		limit,
		offset,
		null,
		null,
		false
	).then(res => {
		res.body.data.result.forEach((e: any) => {
			e.currentQuantity = getCurrentQuantityInCart(e)
		});
		dataShopProducts.value = JSON.parse(JSON.stringify(res.body.data.result));
		// productShowing.value = JSON.parse(JSON.stringify(res.body.data.result[0]))
		countProducts.value = res.body.data.count;
		refreshing.value = false;
		loadMore.value = false;
		filterProductLoading.value = false;

	}).catch(err => {
		filterProductLoading.value = false;
		refreshing.value = false;
		loadMore.value = false
	})
}

function getCartData() {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$?.length ? cartData$ : [];
}

function getCurrentQuantityInCart(itemProduct: any) {
	let index = cartData.value.findIndex(function (e: CartDto) {
		return e.product_id == itemProduct.id
	});
	return index != -1 ? cartData.value[index].quantity : 0
}

function checkProductsQuantityInCart() {
	dataShopCategories.value.forEach((elCategory: any) => {
		let listProduct = JSON.parse(JSON.stringify(elCategory.products));
		listProduct.forEach((e: any) => {
			e.currentQuantity = getCurrentQuantityInCart(e);
		});
		elCategory.products = JSON.parse(JSON.stringify(listProduct));
	})
}

function getListCategory() {
	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
	})
}
function changeSlideCategoryIndex(index = 0) {
	categoryCarousel.value.slideTo(index)
}
function changeSlideCategoryButtonIndex(index = 0) {
	categoryCarouselButton.value.slideTo(index)
}
async function checkAge() {
	let checkAge = await JSON.parse(JSON.stringify(localStorage.getItem(appConst.storageKey.confirm18Age)));
	return checkAge
}

async function checkAgeAndAddProductToCart() {
	let check = await checkAge();
	if (!check && selectedProduct.value.is_alcohol) {
		showModalConfirm18Age.value = true;
	}
	else {
		addProductToCart();
	}
}
async function addProductToCart() {
	let cartTemp = JSON.parse(JSON.stringify(cartData.value)) || [];
	if (cartTemp && cartTemp.length) { }
	let index = cartTemp.findIndex((e: any) => {
		return e.product_id == selectedProduct.value.id
	});
	if (index != -1 && selectedProduct.value.currentQuantity) {
		cartTemp[index].quantity = selectedProduct.value.currentQuantity;
		cartTemp[index].notes = selectedProduct.value.order_item_notes?.length ? selectedProduct.value.order_item_notes : "";
		if (selectedProduct.value.price_off != null && (selectedProduct.value.price_off < selectedProduct.value.price)) {
			cartTemp[index].price = selectedProduct.value.currentQuantity * selectedProduct.value.price_off
		}
		else if (selectedProduct.value.price != 0 && selectedProduct.value.price != null) {
			cartTemp[index].price = selectedProduct.value.currentQuantity * selectedProduct.value.price
		}
		else cartTemp[index].price = null;
		// setTimeout(() => {
		// 	close()
		// }, 500);
		runAnimationAddToCart(`elegant_menu_${step.value}`, `category_${categoryShowing.value.id}_product_detail_img_${selectedProduct.value.id}_${step.value}`)
	}
	else if (index != -1 && !selectedProduct.value.currentQuantity) {
		cartTemp.splice(index, 1);
		// setTimeout(() => {
		// 	close()
		// }, 500);
		runAnimationAddToCart(`elegant_menu_${step.value}`, `category_${categoryShowing.value.id}_product_detail_img_${selectedProduct.value.id}_${step.value}`)
	}
	else if (index == -1) {
		let priceTemp: any;
		if (selectedProduct.value.price_off != null && selectedProduct.value.price_off < selectedProduct.value.price) {
			priceTemp = selectedProduct.value.price_off * selectedProduct.value.currentQuantity
		}
		else if (selectedProduct.value.price != 0 && selectedProduct.value.price != null) {
			priceTemp = selectedProduct.value.price * selectedProduct.value.currentQuantity
		}
		else priceTemp = null;
		let cartItem: CartDto = {
			product_id: selectedProduct.value.id,
			product: selectedProduct.value,
			shop_id: shop_id.value,
			shop: shopData.value ? JSON.parse(JSON.stringify(shopData.value)) : null,
			quantity: selectedProduct.value.currentQuantity,
			price: priceTemp,
			notes: selectedProduct.value?.order_item_notes?.length ? selectedProduct.value?.order_item_notes : ""
		};
		if (!cartTemp.length || (cartTemp.length && cartTemp[0].shop_id == cartItem.shop_id)) {
			cartTemp.push(cartItem);
			// setTimeout(() => {
			// 	close()
			// }, 500);
			runAnimationAddToCart('elegant_menu_1', `category_${categoryShowing.value.id}_product_detail_img_${selectedProduct.value.id}_${step.value}`)
		}
		else if (cartTemp.length && cartTemp[0].shop_id != cartItem.shop_id) {
			showModalConfirmResetCart.value = true
		}
	}
	cartData.value = JSON.parse(JSON.stringify(cartTemp));
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));

	nuxtApp.$emit('cart_change');
}
async function resetCartAndAddProduct() {
	let cartItem: CartDto = {
		product_id: selectedProduct.value.id,
		product: selectedProduct.value,
		shop_id: shop_id.value,
		shop: shopData.value ? JSON.parse(JSON.stringify(shopData.value)) : null,
		quantity: selectedProduct.value.currentQuantity,
		price: selectedProduct.value.currentQuantity * selectedProduct.value.price,
		notes: selectedProduct.value?.order_item_notes?.length ? selectedProduct.value?.order_item_notes : ""
	};

	let cartTemp = [cartItem];
	cartData.value = JSON.parse(JSON.stringify(cartTemp));
	showModalConfirmResetCart.value = false;
	runAnimationAddToCart(`elegant_menu_${step.value}`, `category_${categoryShowing.value.id}_product_detail_img_${selectedProduct.value.id}_${step.value}`)
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));
	nuxtApp.$emit(appConst.event_key.cart_change);
	// setTimeout(() => {
	// 	close()
	// }, 500);
}

function runAnimationAddToCart(containerElId: string, imgElId: string) {
	// let valueElement = document.getElementById(containerElId) as HTMLElement;
	let valueElement = document.getElementById('container-root') as HTMLElement;
	let imgEl = document.getElementById(imgElId) as HTMLElement;
	let beginTop = imgEl?.getBoundingClientRect().top;
	let beginLeft = imgEl?.getBoundingClientRect().left;
	// let beginWidth = imgEl?.offsetWidth;

	let elClone = imgEl.cloneNode(true) as HTMLElement;
	elClone.id = imgEl.id + "_clone"
	elClone.style.setProperty('position', 'fixed');
	elClone.style.setProperty('width', '200px');
	elClone.style.setProperty('height', '200px');
	elClone.style.setProperty('object-fit', 'cover');
	// elClone.style.setProperty('height', '200px');
	elClone.style.setProperty('border-radius', '100%');

	// elClone.style.background='white';
	valueElement.appendChild(elClone);

	elClone.style.setProperty('top', `${beginTop}px`);
	elClone.style.setProperty('left', `${beginLeft}px`);
	elClone.style.setProperty('transform-origin', 'left top');
	elClone.style.zIndex = '10000';
	elClone.style.transition = 'scale .3s ease-in-out, top 1s ease, left 1s ease'
	let destinationElement = document.getElementById(`cart_header_${step.value}`) as HTMLElement;
	let destinationTop = destinationElement?.getBoundingClientRect().top;
	let destinationLeft = destinationElement?.getBoundingClientRect().left;
	// elClone.style.borderRadius = '100px'
	setTimeout(() => {
		elClone.style.setProperty('scale', '.15');
		elClone.style.setProperty('top', `${destinationTop}px`);
		elClone.style.setProperty('left', `${destinationLeft}px`);

		setTimeout(() => {
			elClone.remove();
			document.getElementById(`cart_header_${step.value}`)?.classList.add('shake');
			setTimeout(() => {
				document.getElementById(`cart_header_${step.value}`)?.classList.remove('shake')
			}, 500);
		}, 700)

	}, 200);
}

function getProductName(product: any) {
	let indexLanguage = product.translation.findIndex(function (e: any) {
		return e.language_code == selectedLanguage.value;
	})

	return indexLanguage != -1 ? product.translation[indexLanguage].name : product.name;
}
function getProductNote(product: any) {
	let indexLanguage = product.translation.findIndex(function (e: any) {
		return e.language_code == selectedLanguage.value;
	})

	return indexLanguage != -1 ? product.translation[indexLanguage].description : product.notes;
}
</script>
<template>
	<div class="public-container">
		<div class="elegant-menu-container">
			<v-window v-model="step" class="tab-content-container" :touch="false">
				<v-window-item id="tab_elegant_menu_0" :value="0">
					<div class="elegant-menu-start">
						<div class="menu-label">{{ $t('ElegantMenuComponent.menu') }}</div>
						<div class="shop-name">
							<!-- <AvatarComponent v-if="shopData?.logo_id" class="shop-logo" :imgTitle="shopData?.name"
        :imgStyle="shopData?.logo?.style" :imgSrc="shopData?.logo?.path?.length ? (domainImage + shopData?.logo?.path) : ''"
        :width="125" :height="125" /> -->
							{{ shopData?.name }}
						</div>
						<button :disabled="refreshing" v-on:click="() => { step = 1; }">{{
							$t('ElegantMenuComponent.bat_dau') }}</button>
					</div>
				</v-window-item>
				<v-window-item id="tab_elegant_menu_1" :value="1">
					<div class="elegant-menu-view" id="elegant_menu_1">
						<div class="title-header">
							<div class="header-left">
								<button class="back-button" v-on:click="() => {
									router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
								}">
									<Icon name="material-symbols:close-small-outline-rounded"></Icon>
								</button>
							</div>
							<div class="header-right">
								<button :title="$t('ElegantMenuComponent.toi_gio_hang')" class="cart-in-search"
									:id="'cart_header_1'" v-on:click="() => {
										router.push(appRoute.CartComponent);
									}">
									<Icon name="bi:basket2-fill" size="25"></Icon>
									<em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length
										: '10+' }}</em>
								</button>
								<v-menu class="language-dropdown-container" location="bottom right">
									<template v-slot:activator="{ props }" v>
										<button class="button-mode" dark v-bind="props">

											<Icon :name="appConst.flag_icons[selectedLanguage]"
												v-if="appConst.flag_icons[selectedLanguage]"></Icon>
											<span class="default-lang-icon" v-else>{{ selectedLanguage }}</span>
										</button>
									</template>
									<v-list>
										<v-list-item v-for="(lang, index) in shopData.language"
											:key="'language_' + lang" :active="selectedLanguage == lang"
											activeClass="active" v-on:click="() => {
												selectedLanguage = lang;
											}">
											<v-list-item-title class="lang-item">
												<Icon :name="appConst.flag_icons[lang]"
													v-if="appConst.flag_icons[lang]">
												</Icon>
												<span class="default-lang-icon" v-else>{{ lang }}</span>
												{{ ISO6391.getNativeName(lang) }}
											</v-list-item-title>
										</v-list-item>
									</v-list>
								</v-menu>
							</div>
						</div>
						<div class="products-content" v-if="dataShopCategories?.length">
							<div class="carousel-products-container">
								<Swiper class="my-carousel categories-carousel" :slides-per-view="1" :loop="false"
									:autoplay="false" :direction="'vertical'" :modules="[]" :navigation="false"
									@slideChange="(e: any) => {
										categoryShowing = dataShopCategories[e.activeIndex];
										indexCategoryShowing = e.activeIndex;
										selectedProduct = null;
										changeSlideCategoryButtonIndex(e.activeIndex);
									}" @init="(e: any) => {
									categoryCarousel = e;
									categoryShowing = dataShopCategories[0];
								}" :spaceBetween="20">
									<SwiperSlide class="item-category-stack-slide" v-bind:style="{
										backgroundImage: `url(${domainImage + itemCategory.profile_picture})`
									}" :key="'category_slide_' + itemCategory?.id" v-for="(itemCategory, indexCategory) of dataShopCategories">
										<Swiper class="my-carousel products-carousel"
											v-if="itemCategory.products?.length && indexCategory >= indexCategoryShowing - 1 && indexCategory <= indexCategoryShowing + 1"
											:modules="[]" :slides-per-view="'auto'" :loop="false" :autoplay="false"
											key="products-carousel" @init="(e: any) => {
												itemCategory.activeIndex = e.activeIndex;
											}" @slideChange="(e: any) => {
											selectedProduct = null;
											itemCategory.activeIndex = e.activeIndex;
										}" :grabCursor="true" :centeredSlides="true" :setWrapperSize="true" :breakpointsBas="'container'">
											<SwiperSlide class="item-stack-slide"
												v-for="(itemProduct, indexProduct) of itemCategory.products"
												:key="`category_${itemCategory?.id}_product_${itemProduct?.id}`">
												<div class="product-detail"
													v-if="indexProduct <= itemCategory.activeIndex + 1 && indexProduct >= itemCategory.activeIndex - 1">
													<div class="categories">
														<span>
															{{ showTranslateProductName(itemCategory) }} </span>
													</div>
													<div class="name">
														{{ getProductName(itemProduct) }}
													</div>
													<!-- <div class="notes">
													{{ getProductNote(itemProduct) }}
												</div> -->
												</div>
												<div class="product-item-container"
													v-if="indexProduct <= itemCategory.activeIndex + 1 && indexProduct >= itemCategory.activeIndex - 1"
													:id="`category_${itemCategory.id}_product_detail_${itemProduct.id}`">
													<img loading="lazy" class="product-logo" :src="(itemProduct && itemProduct.profile_picture)
														? (domainImage + itemProduct.profile_picture)
														// ? itemProduct.profile_picture
														: icon_for_product" alt="" :id="`category_${itemCategory.id}_product_detail_img_${itemProduct.id}_1`" />
													<div class="product-info">
														<!-- <span class="name">
														{{ getProductName(itemProduct) }}
													</span> -->
														<NuxtRating v-if="itemProduct.ratings" border-color="#f1d804"
															active-color="#f1d804" inactive-color="#fff"
															:rounded-corners="true" :border-width="5"
															:rating-spacing="5" :rating-step=".5" :rating-size="17"
															:rating-value="itemProduct.ratings || 0" class="rating" />
														<span class='price' :title="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
															formatCurrency(parseFloat(itemProduct.price_off), shopData?.currency) :
															(parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
																$t('ElegantMenuComponent.gia_lien_he') :
																formatCurrency(parseFloat(itemProduct.price), shopData?.currency)" v-on:click="() => {
																// this.addProductToCart(itemProduct)
																// router.push(appRoute.ProductComponent + '/' + itemProduct.id)
															}">
															{{
																(itemProduct.price_off != null && itemProduct.price_off <
																	itemProduct.price) ?
																	formatCurrency(parseFloat(itemProduct.price_off),
																		shopData?.currency) : (parseFloat(itemProduct.price) == 0
																			|| itemProduct.price == null) ?
																		$t('ElegantMenuComponent.gia_lien_he') :
																		formatCurrency(parseFloat(itemProduct.price),
																			shopData?.currency) }} <em class="off"
																:title="formatCurrency(itemProduct.price ? parseFloat(itemProduct.price) : 0, shopData?.currency)"
																v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
																{{
																	formatCurrency(itemProduct.price ?
																		parseFloat(itemProduct.price) : 0,
																		shopData?.currency)
																}}
																</em>
														</span>

														<!-- <div class="descriptions">
														{{ getProductNote(itemProduct) }}
													</div> -->

														<div class="show-note-button">
															<button v-on:click="() => {
																itemProduct.showNote = !itemProduct.showNote;
															}">
																{{ $t('ElegantMenuComponent.ghi_chu') }}
																<em class="text-length">({{
																	itemProduct.order_item_notes?.length ?? 0 }}/{{
																		appConst.max_text_short }})</em>
															</button>
														</div>
														<div class="notes" :class="{ 'show': itemProduct.showNote }">
															<textarea :maxlength="appConst.max_text_long"
																:placeholder="$t('ElegantMenuComponent.ghi_chu_placeholder')"
																:value="itemProduct?.order_item_notes" v-on:input="($event: any) => {
																	itemProduct.order_item_notes = $event.target.value
																}" name="note-cart" id="note-cart" class='note-order-input'></textarea>
														</div>

														<div class="quantity-actions">
															<button class='quantity-button left'
																:disabled="!itemProduct.currentQuantity || itemProduct.currentQuantity <= 0"
																v-on:click="() => {
																	itemProduct.currentQuantity = itemProduct.currentQuantity ? parseFloat(itemProduct.currentQuantity) - 1 : 0
																}">
																<Icon name="iconoir:minus-circle" />
															</button>
															<span>{{ formatNumber(itemProduct.currentQuantity ?
																itemProduct.currentQuantity : 0)
																}}</span>
															<button class='quantity-button right' v-on:click="() => {
																itemProduct.currentQuantity = itemProduct.currentQuantity ? parseFloat(itemProduct.currentQuantity) + 1 : 1;
															}">
																<Icon name="iconoir:plus-circle-solid" />
															</button>
														</div>

														<button class="add-to-cart"
															:disabled="!itemProduct.currentQuantity || itemProduct.currentQuantity <= 0"
															v-on:click="() => {
																selectedProduct = JSON.parse(JSON.stringify(itemProduct))
																checkAgeAndAddProductToCart()
															}">
															<Icon name="hugeicons:shopping-basket-add-01"></Icon>
														</button>
													</div>
												</div>
											</SwiperSlide>
										</Swiper>
										<div class="none-products" v-else>

											<span class="shop-name">{{ itemCategory?.name }}</span> {{
												$t('ElegantMenuComponent.chua_co_san_pham') }}
										</div>
									</SwiperSlide>
								</Swiper>

							</div>

						</div>
						<div v-else class="products-content none-menu">
							<AvatarComponent v-if="shopData?.logo_id" class="shop-logo" :imgTitle="shopData?.name"
								:imgStyle="shopData?.logo?.style" :imgSrc="shopData?.logo?.path?.length
									? (domainImage + shopData?.logo?.path)
									: ''" :width="125" :height="125" />
							<span class="shop-name">{{ shopData?.name }}</span> {{
								$t('ElegantMenuComponent.chua_tao_menu')
							}}
						</div>
						<div class="categories-content">
							<!-- <div class="category-item" :class="{ 'active': categoryShowing?.id == itemCategory?.id }"
							v-on:click="() => {
								changeSlideCategoryIndex(index);
							}" v-for="(itemCategory, index) of dataShopCategories">
							{{ itemCategory.name }}
						</div> -->
							<Swiper class="my-carousel category-buttons-carousel" :modules="[SwiperFreeMode]"
								:freeMode="true" :centeredSlides="true" :direction="'vertical'"
								:centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
								:effect="'creative'" :autoplay="false" key="category-carousel" @init="(e: any) => {
									categoryCarouselButton = e;
								}">
								<SwiperSlide class="category-item"
									v-for="(itemCategory, indexTab) of dataShopCategories" v-on:click="async () => {
										changeSlideCategoryIndex(indexTab);
									}" :class="{ 'active': categoryShowing?.id == itemCategory?.id }" :id="'tab_' + itemCategory.id">
									<div class="tab-title">
										<span class='name'>
											{{ itemCategory.name }}
										</span>
									</div>
								</SwiperSlide>
							</Swiper>
						</div>
					</div>
				</v-window-item>
				<v-window-item id="tab_elegant_menu_2" :value="2">
					<div class="elegant-menu-view" id="elegant_menu_2">
						<div class="title-header">
							<div class="header-left">
								<button v-on:click="() => { step = 1; }">
									<Icon name="material-symbols:arrow-left-alt-rounded"></Icon>
								</button>
							</div>
							<div class="header-right">
								<button :title="$t('ElegantMenuComponent.toi_gio_hang')" class="cart-in-search"
									:id="'cart_header_2'" v-on:click="() => {
										router.push(appRoute.CartComponent);
									}">
									<Icon name="bi:basket2-fill" size="25"></Icon>
									<em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length
										: '10+' }} </em>
								</button>
							</div>
						</div>
						<div class="products-content">
							<div class="selected-product-container">
								<div class="product-detail">
									<div class="name">
										{{ selectedProduct?.name }}
									</div>
								</div>
								<div class="product-item-container">
									<img loading="lazy" :src="(selectedProduct && selectedProduct.profile_picture)
										? (domainImage + selectedProduct.profile_picture)
										// ? selectedProduct.profile_picture
										: icon_for_product" alt="" :id="`category_${categoryShowing.id}_product_detail_img_${selectedProduct.id}_2`" />
									<div class="product-info">

										<!-- <span class="rating">đánh giá</span> -->
										<span class='price' :title="(selectedProduct.price_off != null && selectedProduct.price_off < selectedProduct.price) ?
											formatCurrency(parseFloat(selectedProduct.price_off),
												shopData?.currency) : (parseFloat(selectedProduct.price) == 0 ||
													selectedProduct.price == null) ? $t('ElegantMenuComponent.gia_lien_he') :
												formatCurrency(parseFloat(selectedProduct.price), shopData?.currency)" v-on:click="() => {
												// this.addProductToCart(selectedProduct)
												// router.push(appRoute.ProductComponent + '/' + selectedProduct.id)
											}">
											{{
												(selectedProduct.price_off != null && selectedProduct.price_off <
													selectedProduct.price) ?
													formatCurrency(parseFloat(selectedProduct.price_off),
														shopData?.currency) : (parseFloat(selectedProduct.price) == 0 ||
															selectedProduct.price == null) ? $t('ElegantMenuComponent.gia_lien_he') :
														formatCurrency(parseFloat(selectedProduct.price), shopData?.currency) }}
												<em class="off" :title="formatCurrency(selectedProduct.price ? parseFloat(selectedProduct.price) : 0,
													shopData?.currency)" v-if="(selectedProduct.price_off != null && selectedProduct.price_off < selectedProduct.price)">
												{{
													formatCurrency(selectedProduct.price ?
														parseFloat(selectedProduct.price) :
														0,
														shopData?.currency)
												}}</em>
										</span>

										<div class="quantity-actions">
											<button class='quantity-button left'
												:disabled="!selectedProduct.currentQuantity || selectedProduct.currentQuantity <= 0"
												v-on:click="() => {
													selectedProduct.currentQuantity = selectedProduct.currentQuantity ? parseFloat(selectedProduct.currentQuantity) - 1 : 0
												}">
												<Icon name="iconoir:minus-circle" />
											</button>
											<span>{{ formatNumber(selectedProduct.currentQuantity ?
												selectedProduct.currentQuantity : 0)
												}}</span>
											<button class='quantity-button right' v-on:click="() => {
												selectedProduct.currentQuantity = selectedProduct.currentQuantity ? parseFloat(selectedProduct.currentQuantity) + 1 : 1;
											}">
												<Icon name="iconoir:plus-circle-solid" />
											</button>
										</div>
										<div class="notes">
											<textarea :placeholder="$t('ElegantMenuComponent.ghi_chu_placeholder')"
												:maxlength="appConst.max_text_long"
												:value="selectedProduct?.order_item_notes" v-on:input="($event: any) => {
													selectedProduct.order_item_notes = $event.target.value

												}" name="note-cart" id="note-cart" class='note-order-input'></textarea>
										</div>

										<button class="add-to-cart"
											:disabled="!selectedProduct.currentQuantity || selectedProduct.currentQuantity <= 0"
											v-on:click="() => {
												selectedProduct = JSON.parse(JSON.stringify(selectedProduct))
												checkAgeAndAddProductToCart()
											}">
											<Icon name="hugeicons:shopping-basket-add-01"></Icon>
										</button>
									</div>
								</div>

							</div>

						</div>
					</div>
				</v-window-item>
			</v-window>

			<ResetCartComponent v-if="showModalConfirmResetCart" v-on:accept="() => { resetCartAndAddProduct(); }"
				v-on:reject="() => { showModalConfirmResetCart = false; }" v-on:close="() => {
					showModalConfirmResetCart = false;
				}"></ResetCartComponent>
			<Confirm18AgeComponent v-if="showModalConfirm18Age" v-on:accept="() => {
				showModalConfirm18Age = false;
				addProductToCart();
			}" v-on:reject="() => {
			showModalConfirm18Age = false;
		}" v-on:close="() => {
			showModalConfirm18Age = false;
		}">
			</Confirm18AgeComponent>
		</div>
	</div>

</template>

<style lang="scss" src="./ElegantMenuStyles.scss"></style>
