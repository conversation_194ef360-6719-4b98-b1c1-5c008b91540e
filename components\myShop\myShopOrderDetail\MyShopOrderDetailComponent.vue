<template>
    <div class="public-container">
        <div class='my-shop-order-detail-container'>
            <!-- <div class='title-header'>
            <div class='header-left'>
                <button class="back-button" v-on:click="() => {
                    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
                }">
                    <Icon name="lucide:chevron-left"/>
                </button>
            </div>

            <h3>
                {{ appRouteTitle.MyShopOrderDetailComponent }}
            </h3>
            <div class="h-stack header-right">
                <button v-show="order && parseInt(order.status.toString()) == appConst.order_status.waiting.value"
                    v-on:click="async () => {
                        showEditOrderModal = true
                    }">
                    <Icon name="bx:edit-alt" />
                </button>
            </div>


        </div> -->
            <SubHeaderV2Component :title="$t('AppRouteTitle.MyShopOrderDetailComponent')">
                <template v-slot:header_right>
                    <button id="edit-order-button" v-show="order && parseInt(order.status.toString()) <= appConst.order_status.confirmed.value"
                        v-on:click="async () => {
                            showEditOrderModal = true
                        }">
                        <Icon name="bx:edit-alt" />
                    </button>
                </template>
            </SubHeaderV2Component>
            <p class="loading" v-show="isRefreshing">
                {{ $t('MyShopOrderDetailComponent.dang_tai') }}
            </p>

            <div class='v-stack' v-if="!isRefreshing && (order && order.id)">
                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="basil:invoice-outline"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.ma_don') }}: {{
                                    order && order.short_code
                                        ? order.short_code
                                        : $t('MyShopOrderDetailComponent.chua_co_ma')
                                }}
                            </span>
                        </div>
                        <button class="action copy" v-on:click="() => {
                            toast.success($t('MyShopOrderDetailComponent.da_sao_chep_ma_don'));
                            copyToClipboard(order.short_code)
                        }">
                            {{ $t('MyShopOrderDetailComponent.sao_chep') }}
                        </button>
                    </div>
                </div>

                <div class="v-stack main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="solar:pen-new-round-outline"></Icon>
                            {{ $t('MyShopOrderDetailComponent.trang_thai') }}:
                            <span :class="'order-status ' + (parseInt(order.status.toString()) != 3
                                ? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(order.status)) as string
                                : ' taken')">
                                
                                {{ $t('MyShopOrderDetailComponent.' +
                                    appConst.order_status[
                                        parseInt(order.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key =>
                                                appConst.order_status[key].value == parseInt(order.status)) as string
                                            : "taken"
                                    ].nameKey
                                ) }}
                            </span>
                        </div>
                        <button class="action"
                            v-if="parseInt(order.status.toString()) >= appConst.order_status.confirmed.value"
                            v-on:click="() => {
                                showFullProcess = !showFullProcess
                            }">
                            {{ $t('MyShopOrderDetailComponent.mo_rong') }}
                        </button>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.cancel.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.cancel.value">
                            <span class="status-name">{{ $t('MyShopOrderDetailComponent.tu_choi_don') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.taken.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.taken.value">
                            <span class="status-name">{{ $t('MyShopOrderDetailComponent.giao_hang_thanh_cong') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.confirmed.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.confirmed.value">
                            <span class="status-name">{{ $t('MyShopOrderDetailComponent.xac_nhan') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.waiting.value || showFullProcess">
                            <span class="status-name">{{ $t('MyShopOrderDetailComponent.dat_hang') }}</span>
                            <span>
                                {{ moment(order.created_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:stack-hexagon-outline"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.anh_don_hang') }}
                                <!-- <em v-show="(parseInt(order.status.toString()) < appConst.order_status.taken.value)">
                                    (
                                    {{ $t('MyShopOrderDetailComponent.toi_thieu_x_anh', {
                                        amount:
                                            appConst.orderImageMinAmount
                                    })
                                    }}
                                    )</em> -->
                            </span>
                        </div>
                    </div>
                    <div class="h-stack primary-content images-content"
                        v-if="order.images?.length || parseInt(order.status.toString()) < appConst.order_status.taken.value"
                        id="images_content">
                        <div class="selected-image" v-for="(itemImg, index) in listImagesEdit">
                            <img :src="itemImg.path" class="handle-drag" v-on:click="() => {
                                indexImageActive = index;
                                showImageViewerModal = true;
                            }" />
                            <div class="action-overlay"
                                v-if="parseInt(order.status.toString()) < appConst.order_status.taken.value">
                                <button class="delete-image" v-on:click="() => {
                                    listImagesEdit.splice(index, 1)
                                }">
                                    <Icon name="bi:trash3-fill"></Icon>
                                </button>
                            </div>
                        </div>
                        <div class="select-image"
                            v-if="parseInt(order.status.toString()) < appConst.order_status.taken.value && listImagesEdit.length < appConst.orderImageMaxAmount">
                            <label>
                                <Icon name="ion:plus-round"></Icon>
                                <input type="file" accept='image/*' :multiple="true" v-on:change="($event: any) => {
                                    fileChangeInput($event)
                                }" ref="imageFilesName" />
                            </label>
                        </div>
                    </div>
                    <div class="h-stack primary-content images-content none-images"
                        v-if="!(order.images?.length || parseInt(order.status.toString()) < appConst.order_status.taken.value)">
                        <em>
                            {{ $t('MyOrderDetailComponent.cua_hang_chua_cung_cap') }}
                        </em>
                    </div>

                    <div class="h-stack images-content-actions" :disabled="isUpdating" v-show="checkEditImages()">
                        <button class="undo" v-on:click="() => {
                            setListImagesEdit();
                        }">
                            {{ $t('MyShopOrderDetailComponent.hoan_tac') }}
                        </button>
                        <button class="save-images" :disabled="isUpdating" v-on:click="() => {
                            handleSaveImages();
                        }">
                            <Icon name="eos-icons:loading" size="15" v-show="isUpdating" /> {{
                                $t('MyShopOrderDetailComponent.luu')
                            }}
                        </button>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:location-away-outline-rounded"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.nguoi_nhan') }}
                            </span>
                        </div>
                        <div class="h-stack action">
                            <nuxt-link class="call-customer" :to="directionToCustomer()" target="_blank">
                                <Icon name="el:map-marker"></Icon>
                                {{ $t('MyShopOrderDetailComponent.chi_duong') }}
                            </nuxt-link>
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`"
                                :target="webInApp ? '_blank' : ''" class="call-customer">
                                <Icon name="material-symbols:call"></Icon>
                                {{ $t('MyShopOrderDetailComponent.goi_dien') }}
                            </nuxt-link>
                            <button class="call-customer" v-if="order.customer_id" v-on:click="() => {
                                chatToCustomer()
                            }">
                                <Icon name="fluent:chat-24-regular"></Icon>
                                {{ $t('MyShopOrderDetailComponent.nhan_tin') }}
                            </button>
                        </div>

                    </div>
                    <div class="h-stack primary-content">
                        <img loading="lazy" class='content-detail user-avatar' :src="order && order.customer && order.customer.profile_picture
                            ? ((appConst.provider_img_domain.some(e => order.customer.profile_picture?.includes(e))) ? order.customer.profile_picture : domainImage + order.customer.profile_picture)
                            : non_avatar" :placeholder="non_avatar" alt="" />
                        <div class="content-detail">
                            <div class="customer-name" v-on:click="()=>{
                                showOrderReceiverInfoModal = true;
                            }">
                                <span>{{ order.customer_name }}</span>
                                <button class="open-info">
                                    ({{ $t('MyShopOrderDetailComponent.xem_chi_tiet') }})
                                </button>
                            </div>
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`"
                                :target="webInApp ? '_blank' : ''" class="content-detail customer-phone">
                                <span>{{ order.customer_phone }}</span>
                            </nuxt-link>
                            <div class="customer-address" v-on:click="()=>{
                                showOrderReceiverInfoModal = true;
                            }">
                                <span>{{ order.address }}</span>
                            </div>
                            <div class="customer-delivery-time" v-if="order?.delivery_time?.length">
                                <span>{{ $t('MyShopOrderDetailComponent.muon_nhan_hang_luc') }}: <em>{{
                                    moment(order.delivery_time).format("DD/MM/YYYY - HH:mm") }}</em></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:storefront-outline"></Icon>
                            <span>
                                {{ order.shops.name }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack order-items-container">
                        <div v-for="(itemOrder) in order.items" class='h-stack item-order-container'
                            :key="itemOrder.id">
                            <div class='v-stack item-order-avatar'>
                                <img loading="lazy"
                                    :src="itemOrder.profile_picture ? (domainImage + itemOrder.profile_picture) : icon_for_product"
                                    :placeholder="icon_for_product" alt="" />
                            </div>
                            <div class='v-stack item-order-detail'>
                                <span class='item-order-name'>
                                    {{
                                        itemOrder.parent_id ? (showTranslateProductName(itemOrder.parent_product) + ' - ') :
                                            ''
                                    }}
                                    {{ showTranslateProductName(itemOrder) }}
                                </span>

                                <div class='h-stack item-order-quantity-price'>
                                    <span>
                                        x{{ parseFloat(itemOrder.pivot.quantity).toString().replaceAll('.', ',') }}
                                    </span>
                                    |
                                    <span class="price">
                                        {{ (itemOrder.pivot.price_off != null) ? formatCurrency(parseFloat(itemOrder.pivot.price_off),
                                            order.shops.currency) : (parseFloat(itemOrder.price) == 0 ||
                                                itemOrder.price == null) ? $t('MyShopOrderDetailComponent.gia_lien_he') :
                                        formatCurrency(parseFloat(itemOrder.price), order.shops.currency) }} 
                                        <!-- Price change percentage badge -->
                                        <!-- <span v-if="getPriceChangeInfo(itemOrder).type !== 'none'"
                                            :class="`${getPriceChangeInfo(itemOrder).type}-percent`" >
                                            {{ getPriceChangeInfo(itemOrder).type === 'discount' ? '-' : '+' }}{{ getPriceChangeInfo(itemOrder).percentage }}%
                                        </span> -->
                                        <em
                                        class="off"
                                        v-if="(itemOrder.pivot.price_off != null)">
                                        {{ formatCurrency(itemOrder.price ? parseFloat(itemOrder.price) : 0,
                                            order.shops.currency) }}
                                        </em>
                                    </span>
                                </div>
                                <span class='item-order-note'>
                                    {{ itemOrder.pivot.notes }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class='main-stack v-stack' v-if="order.notes?.length">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:description-outline"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.ghi_chu_don_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <p class='content-detail'>{{ order.notes }}</p>
                    </div>
                </div>

                <div class="main-stack" v-if="false">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="carbon:wallet"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.hinh_thuc_thanh_toan') }}
                            </span>
                        </div>

                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ $t('MyShopOrderDetailComponent.thanh_toan_khi_nhan_hang') }}</span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="bi:basket2-fill"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.hinh_thuc_nhan_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ order.delivery_type ? $t('MyShopOrderDetailComponent.tu_toi_lay') :
                                $t('MyShopOrderDetailComponent.toi_can_giao_tan_noi') }}</span>
                        </div>
                    </div>
                </div>
                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="hugeicons:truck-delivery"></Icon>
                            <span>
                                {{ $t('MyShopOrderDetailComponent.thong_tin_giao_hang') }}
                            </span>
                        </div>
                        <button class="action" v-if="!order?.delivery_info?.status
                            || order?.delivery_info?.status == appConst.delivery_status.cancelled" v-on:click="() => {
                                showCreateDeliveryModal = true
                            }">
                            {{ $t('MyShopOrderDetailComponent.cap_nhat') }}
                        </button>
                        <button class="action" 
                        v-else-if="order?.delivery_info?.status == appConst.delivery_status.pending
                                || order?.delivery_info?.status == appConst.delivery_status.prepared
                                || order?.delivery_info?.status == appConst.delivery_status.confirmed
                                || order?.delivery_info?.status == appConst.delivery_status.picked_up
                                || order?.delivery_info?.status == appConst.delivery_status.in_transit
                                || order?.delivery_info?.status == appConst.delivery_status.cancelled
                                || order?.delivery_info?.status == appConst.delivery_status.failed"
                                v-on:click="() => {
                                showCancelDeliveryModal = true
                            }">
                            {{ $t('MyShopOrderDetailComponent.huy_don_giao') }}
                        </button>
                    </div>

                    <div class="h-stack primary-content delivery-content" v-if="order.delivery_info">
                        <div class="label-content">
                            {{ $t('MyShopOrderDetailComponent.chi_tiet_don_giao') }}
                        </div>
                        <div class="delivery-partner-info">
                            <img class="partner-logo" :src="order?.delivery_partner?.information?.logo"
                                v-if="order?.delivery_partner?.information?.logo"
                                :alt="`logo ${order?.delivery_partner?.name}`">
                            <span class="partner-name" v-else>
                                {{ order?.delivery_partner?.name }}
                            </span>
                        </div>
                        <div class="routes">
                            <div class="label">
                                {{ $t('MyShopOrderDetailComponent.lo_trinh') }}:
                            </div>
                            <div class="places-info">
                                <div class="place">
                                    <img :src="start_location_icon" class="icon-left" />
                                    <div class="place-content">
                                        <span>{{ order?.delivery_info?.name_from }} - {{
                                            order?.delivery_info?.phone_from }}</span>
                                        <em>{{ order?.delivery_info?.address_from }}</em>
                                    </div>
                                </div>
                                <div class="distance">

                                    <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon>
                                </div>
                                <div class="place">
                                    <img :src="destination_location_icon" class="icon-left" />
                                    <div class="place-content">
                                        <span>{{ order?.delivery_info?.name_to }} - {{
                                            validPhone(order?.delivery_info?.phone_to)
                                            }}</span>
                                        <em>{{ order?.delivery_info?.address_to }}</em>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-detail delivery-detail">
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.cod') }}:
                                </div>
                                <span class="data">
                                    {{ formatCurrency(order?.delivery_info?.cod_price || 0) }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.thoi_gian_lay_hang') }}:
                                </div>
                                <span class="data">
                                    {{ order?.delivery_info?.pickup_time ?
                                        moment(order?.delivery_info?.pickup_time).format("DD/MM/YYYY HH:mm") :
                                        $t('MyShopOrderDetailComponent.lay_ngay') }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.loai_dich_vu_giao') }}:
                                </div>
                                <span class="data">
                                    <span v-if="order.delivery_info.service">{{ order.delivery_info.service?.name }} -
                                    </span> {{
                                        order.delivery_info.distance }} km - {{
                                        formatCurrency(order.delivery_info?.total_amount || 0)
                                    }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.giao_trong') }}:
                                </div>
                                <span class="data" v-if="order?.delivery_info?.duration">
                                    {{ order?.delivery_info?.duration.toFixed(0) }} {{
                                        $t('MyShopOrderDetailComponent.phut') }}
                                </span>
                                <span class="data" v-else>{{ $t('MyShopOrderDetailComponent.chua_chon') }}</span>
                            </div>
                            <div class="content" v-if="order?.delivery_info?.package_info">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.thong_tin_goi_hang') }}:
                                </div>
                                <span class="data">
                                    <span v-if="order?.delivery_info?.package_info?.product_type?.length">{{
                                        order?.delivery_info?.package_info?.product_type }}</span>
                                    <span v-if="order?.delivery_info?.package_info?.weight?.length">{{
                                        $t(`MyShopOrderDetailComponent.${order?.delivery_info?.package_info?.weight}`)
                                    }}</span>
                                    <span v-if="order?.delivery_info?.package_info.size?.length">{{
                                        order?.delivery_info?.package_info.size }}</span>
                                </span>
                            </div>
                            <div class="content" v-if="order?.delivery_info?.special_require?.length">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.yeu_cau_dac_biet') }}:
                                </div>
                                <span class="data">
                                    <span class="special-item"
                                        v-for="itemSpecial in order?.delivery_info?.special_require">
                                        {{ $t(`MyShopOrderDetailComponent.${itemSpecial.key}`) }}
                                    </span>

                                </span>
                            </div>
                            <div class="content" v-if="order?.delivery_info?.notes?.length">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.ghi_chu_cho_tai_xe') }}:
                                </div>
                                <span class="data">
                                    {{ order?.delivery_info?.notes }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.trang_thai') }}:
                                </div>
                                <span class="data" :class="{
                                    'rejected': order?.delivery_info?.status == appConst.delivery_status.cancelled || order?.delivery_info?.status == appConst.delivery_status.failed,
                                    'successfully': order?.delivery_info?.status == appConst.delivery_status.delivered,
                                }">
                                    {{
                                        $t(`MyShopOrderDetailComponent.trang_thai_don_giao_${order?.delivery_info?.status}`)
                                    }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="h-stack primary-content delivery-content"
                        v-if="order.delivery_info && (order.delivery_partner?.name?.toLowerCase().includes('remagan') || !order.delivery_partner)">
                        <div class="label-content">
                            {{ $t(`MyShopOrderDetailComponent.tai_xe`) }}
                        </div>
                        <div class="h-stack content-detail" v-if="order?.delivery_info?.driver">
                            <img loading="lazy" class='content-detail user-avatar' :src="order?.delivery_info?.driver?.profile_picture?.length
                                ? (domainImage + order?.delivery_info?.driver?.profile_picture)
                                : non_avatar" :placeholder="non_avatar" alt="" />
                            <div class="user-detail">
                                <div class="customer-name">
                                    <span>{{ order?.delivery_info?.driver?.name }}</span>
                                </div>
                                <nuxt-link :to="`tel:${validPhone(order?.delivery_info?.driver?.phone)}`"
                                    :target="webInApp ? '_blank' : ''" class="content-detail customer-phone">
                                    <span>{{ order?.delivery_info?.driver?.phone }}</span>
                                </nuxt-link>
                                <!-- <div class="status"
                                    :class="{ 'rejected': order?.delivery_info?.status == appConst.delivery_status.cancelled }">
                                    <span>{{
                                        $t(`MyShopOrderDetailComponent.trang_thai_giao_hang_${order?.delivery_info?.status}`)
                                    }}</span>
                                </div> -->
                            </div>
                            <button class="driver-location"
                                v-if="order?.delivery_info?.status < appConst.delivery_status.delivered" v-on:click="() => {
                                    showDriverLocationModal = true
                                }">{{ $t(`MyShopOrderDetailComponent.vi_tri`) }}</button>
                        </div>
                        <div class="h-stack content-detail" v-else>
                            <img loading="lazy" class='content-detail user-avatar' :src="non_avatar"
                                :placeholder="non_avatar" alt="" />
                            <div class="user-detail">
                                <div class=" customer-name">
                                    <span>{{ $t(`MyShopOrderDetailComponent.chua_chon_shipper`) }}</span>
                                </div>
                                <button class="action" v-on:click="() => {
                                    selectAnotherDriverMode = true
                                    showCreateDeliveryModal = true
                                }">
                                    {{ $t('MyShopOrderDetailComponent.chon_shipper_khac') }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="h-stack primary-content delivery-content"
                        v-if="order.delivery_info?.shared_link?.length && !(order.delivery_partner?.name?.toLowerCase().includes('remagan'))">
                        
                        <nuxt-link class="driver-location-ahamove" target="_blank" :to="order.delivery_info?.shared_link">{{ $t(`MyShopOrderDetailComponent.vi_tri_shipper`) }}</nuxt-link>
                    </div>
                    <div class="h-stack primary-content delivery-content" v-if="!order.delivery_info">
                        <em>{{ $t('MyShopOrderDetailComponent.chua_tao_don_giao') }}</em>
                    </div>
                </div>
                <div class='main-stack'>
                    <div class='h-stack payment-detail'>
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.tam_tinh') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.total_amount || 0) }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail' v-if="order.discount_amount < 0">
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.giam_gia') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.discount_amount || 0) }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail'>
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.phi_van_chuyen') }}
                        </span>
                        <span class='data'>
                            {{ order.delivery_price ? formatNumber(order.delivery_price || 0) :
                                $t('MyShopOrderDetailComponent.chua_xac_dinh') }}
                            <em class="delivery-price-estimate">
                                {{$t('MyShopOrderDetailComponent.phi_van_chuyen_khach_da_chon') }}: 
                                <span>{{ order.delivery_price ? formatNumber(order.delivery_price_estimate || 0) : $t('MyShopOrderDetailComponent.chua_xac_dinh') }}</span>
                            </em>
                        </span>
                    </div>
                    <div class='h-stack payment-detail' v-if="Math.abs(order.delivery_discount)">
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.giam_phi_van_chuyen') }}
                        </span>
                        <span class='data'>
                            {{ order.delivery_discount ? formatNumber(order.delivery_discount || 0) :
                                $t('MyShopOrderDetailComponent.chua_xac_dinh') }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail total'>
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.thanh_tien') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.grand_total || 0) }}
                        </span>
                        <!-- <span class="note" v-if="!parseFloat(order.delivery_price)">
                            {{ $t('MyShopOrderDetailComponent.chua_bao_gom_phi_van_chuyen') }}
                        </span> -->
                    </div>
                </div>

                <div v-if="parseInt(order.status.toString()) < appConst.order_status.taken.value"
                    class='h-stack main-stack actions-stack'>
                    <button class='reject-button' :disabled="isUpdating" v-on:click="() => {
                        if (checkEditImages()) {
                            showWarningImageModel = true;
                        }
                        else {
                            showModalConfirmCancelOrder = true
                        }

                    }">
                        {{
                            parseInt(order.status.toString()) == appConst.order_status.waiting.value
                                ? $t('MyShopOrderDetailComponent.tu_choi_don')
                                : parseInt(order.status.toString()) == appConst.order_status.confirmed.value
                                    ? $t('MyShopOrderDetailComponent.huy_don')
                                    : $t('MyShopOrderDetailComponent.huy_don')
                        }}
                    </button>

                    <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
                        if (checkEditImages()) {
                            showWarningImageModel = true;
                        }
                        else {
                            showModalConfirmCancelOrder = false;
                            isUpdating = true;
                            if (parseInt(order.status.toString()) <= appConst.order_status.taken.value) {
                                handleUpdateStatusOrder(parseInt(order.status.toString()) + 1);
                            }
                        }

                    }">
                        {{
                            parseInt(order.status.toString()) == appConst.order_status.waiting.value
                                ? $t('MyShopOrderDetailComponent.nhan_don')
                                : parseInt(order.status.toString()) == appConst.order_status.confirmed.value
                                    ? $t('MyShopOrderDetailComponent.da_giao')
                                    : $t('MyShopOrderDetailComponent.xong')
                        }}
                    </button>

                </div>

                <div class='h-stack main-stack actions-stack'
                    v-if="parseInt(order.status.toString()) >= appConst.order_status.taken.value">
                    <button class='reject-button view-another' v-on:click="() => {
                        updated = true;
                        if (props.mode != 'agent') {
                            router.push(appRoute.ManageOrdersComponent + '#waiting')
                        }
                        else {
                            router.push(appRoute.AgentOrderManageComponent.replaceAll(':shop_id', order?.shops?.slug ?? order?.shops?.id) + '#waiting')
                        }
                    }">
                        {{ $t('MyShopOrderDetailComponent.xem_don_khac') }}
                    </button>
                </div>

                <VueFinalModal class="my-modal-container" content-class="confirm-cancel-order-modal" :overlay-behavior="'persist'"
                    v-model="showModalConfirmCancelOrder" v-on:closed="() => {
                        showModalConfirmCancelOrder = false
                    }" contentTransition="vfm-slide-up">
                    <div class='v-stack cancel-order-content'>
                        <span class='cancel-order-title'>
                            {{ $t('MyShopOrderDetailComponent.tu_choi_nhan_don_hang') }}
                        </span>
                        <span class='cancel-order-message'>
                            {{ $t('MyShopOrderDetailComponent.huy_don_hang') }}
                            <span class='order-code'>
                                {{
                                    order && order.short_code
                                        ? order.short_code
                                        : ""
                                }}
                            </span>
                        </span>
                    </div>
                    <div class='h-stack confirm-modal-buttons'>
                        <button class='reject-button' :disabled="isUpdating" v-on:click="() => {
                            showModalConfirmCancelOrder = false
                        }">
                            {{ $t('MyShopOrderDetailComponent.khong') }}
                        </button>
                        <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
                            handleUpdateStatusOrder(appConst.order_status.cancel.value);
                            showModalConfirmCancelOrder = false;
                        }">
                            {{ $t('MyShopOrderDetailComponent.co') }}
                        </button>
                    </div>
                </VueFinalModal>

                <VueFinalModal class="my-modal-container" content-class="my-modal-content-container form-modal edit-order-modal" :overlay-behavior="'persist'"
                    v-model="showEditOrderModal" v-on:closed="() => {
                        showEditOrderModal = false
                    }" contentTransition="vfm-slide-up">
                    <MyShopEditOrderComponent :orderId="order.id" :mode="$props.mode" v-on:close="($event: any) => {
                        if ($event == true) {
                            init()
                        }
                        showEditOrderModal = false
                    }"></MyShopEditOrderComponent>
                </VueFinalModal>

                <VueFinalModal class="my-modal-container" content-class="v-stack warning-container" :overlay-behavior="'persist'"
                    :click-to-close="true" v-model="showWarningImageModel" contentTransition="vfm-fade" v-on:closed="() => {
                        showWarningImageModel = false;
                    }">
                    <div>
                        <div class='v-stack'>
                            <span class='warning-title'>
                                {{ $t('MyShopOrderDetailComponent.ban_chua_luu_anh') }}
                            </span>
                            <span class='warning-message'>
                                {{ $t('MyShopOrderDetailComponent.thao_tac_anh_don_hang_chua_luu') }}
                            </span>
                        </div>
                        <div class='h-stack warning-modal-buttons'>
                            <button class='review-button' :disabled="isUpdating" v-on:click="() => {
                                showWarningImageModel = false;
                                focusImagesSection()
                            }">
                                {{ $t('MyShopOrderDetailComponent.xem_lai') }}
                            </button>
                        </div>
                    </div>
                </VueFinalModal>

                <CancelDeliveryComponent v-if="showCancelDeliveryModal" v-on:close="async (updated) => {
                    showCancelDeliveryModal = false;
                    if (updated) {
                        if (order?.delivery_info?.driver?.id) {
                            mqttService.publish(order?.delivery_info?.driver?.id, JSON.stringify({
                                mess: {
                                    type: appConst.notification_type.delivery_cancel,
                                    url: order?.delivery_info?.id
                                }
                            }))
                        }
                        await init()

                    }
                }" :order_id="order?.id ?? orderId" :orderData="JSON.parse(JSON.stringify(order))" :mode="props.mode">
                </CancelDeliveryComponent>
            </div>

            <div class='not-existing-order' v-else-if="!isRefreshing">
                <img loading="lazy" class="empty-image" :src='none_result' :placeholder="none_result" />

                <p class="empty-text">
                    {{ $t('MyShopOrderDetailComponent.khong_tim_thay_don_hang') }}
                </p>
                <button class='accept-button' v-on:click="() => {
                    router.push({ path: appRoute.ManageOrdersComponent, hash: '#waiting' })
                }">
                    {{ $t('MyShopOrderDetailComponent.xem_don_khac') }}
                </button>
            </div>
            
        </div>

        <v-overlay v-model="showCreateDeliveryModal" location="bottom" contained :z-index="1001"
            key="show_create_delivery" class="create-delivery-overlay-container" persistent
            content-class='create-delivery-modal-container' no-click-animation>
            <CreateDeliveryComponent :mode="selectAnotherDriverMode ? 'select_driver' : 'update_order'" 
                :order_data="JSON.parse(JSON.stringify(order))"
                :initData="{
                id: order?.delivery_info?.id,
                order_id: order?.id,
                driver_id: order?.delivery_info?.driver?.id,
                status: order?.delivery_info?.status,
                duration: order?.delivery_info?.duration,
                pickup_time: order?.delivery_info?.pickup_time,
                notes: order?.delivery_info?.notes,
                latitude_from: order?.delivery_info?.latitude_from ?? order?.shops?.latitude,
                longitude_from: order?.delivery_info?.longitude_from ?? order?.shops?.longitude,
                latitude_to: order?.delivery_info?.latitude_to ?? order?.customer_latitude,
                longitude_to: order?.delivery_info?.longitude_to ?? order?.customer_longitude,
                distance: order?.delivery_info?.distance ?? order?.delivery_distance,
                address_from: order?.delivery_info?.address_from ?? order?.shops?.address,
                name_from: order?.delivery_info?.name_from ?? order?.shops?.name,
                phone_from: validPhone(order?.delivery_info?.phone_from?.length ? order?.delivery_info?.phone_from : order?.shops?.phone),
                address_to: order?.delivery_info?.address_to ?? order?.address,
                name_to: order?.delivery_info?.name_to ?? order?.customer_name,
                phone_to: validPhone(order?.delivery_info?.phone_to?.length ? order?.delivery_info?.phone_to : order?.customer_phone),
                status_time: JSON.stringify(order?.delivery_info?.status_time),
                package_info: JSON.stringify(order?.delivery_info?.package_info),
                special_require: JSON.stringify(order?.delivery_info?.special_require),
                total_amount: order?.delivery_info?.total_amount,
                discount_id: order?.delivery_info?.discount_id,
                discount_amount: order?.delivery_info?.discount_amount,
                grand_total: order?.delivery_info?.grand_total,
                cod_price: order?.delivery_info?.cod_price ?? order.grand_total ?? null,
                driver: order?.delivery_info?.driver,
                delivery_price: order?.delivery_price,
                delivery_price_estimate: order?.delivery_price_estimate,
                shop_id: order?.shops?.id,
                delivery_partner_id: order?.delivery_partner_id,
                delivery_partner: order?.delivery_partner,
                shopData: order?.shops ? JSON.parse(JSON.stringify(order?.shops)) : null,
                pending_time: order?.delivery_info?.pending_time ?? null,
            }" v-on:close="() => {
                showCreateDeliveryModal = false;
                selectAnotherDriverMode = false;
            }" v-on:submit="(data: any) => {
                showCreateDeliveryModal = false;
                selectAnotherDriverMode = false;
                if (data == true) {
                    updated = true;
                    init();
                }
            }"></CreateDeliveryComponent>
        </v-overlay>
        <ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal" :listObject="listImagesEdit.map(e => {
            return {
                ...e,
                isBase64: true
            }
        })" :indexActive="indexImageActive" v-on:close="(e: any) => {
            showImageViewerModal = false
        }"></ImageViewerComponent>
        <v-overlay v-model="showChatToCustomer" :z-index="100" 
            :absolute="false" :close-on-back="true" contained
            key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
            no-click-animation v-on:click:outside="() => {
                showChatToCustomer = false;
            }">
            <ChatDetailComponent v-if="showChatToCustomer" 
                :receiver_id="order.customer?.id ?? order.customer_id"
                :order_link="order?.short_code ?? order?.id" :order_detail="order"
                :chat_info="chatToCustomerInfo" :receiver_type="true" :mode="member_type.shop" v-on:close="() => {
                    showChatToCustomer = false;
                }"></ChatDetailComponent>
        </v-overlay>

        <v-overlay v-model="showDriverLocationModal" :z-index="100" :absolute="false" :close-on-back="true" contained
            key="show_chat_detail" class="driver-location-overlay-container"
            content-class='driver-location-modal-container' no-click-animation v-on:click:outside="() => {
                showDriverLocationModal = false;
            }">
            <div class="driver-location-container">
                <HeaderComponent :title="$t('MyShopOrderDetailComponent.vi_tri_shipper')">
                    <template v-slot:header_left></template>
                    <template v-slot:header_right>
                        <button class="close" v-on:click="() => {
                            showDriverLocationModal = false
                        }">
                            <Icon name="clarity:times-line" size="25"></Icon>
                        </button>
                    </template>
                </HeaderComponent>
                <div class="delivery-detail-info" id="delivery_detail_container">
                    <div class="delivery-info">
                        <span class="delivery-code"> #{{ order?.delivery_info?.short_code }} </span>
                        <span class="delivery-status" :class="{
                            'pending': order?.delivery_info?.status == appConst.delivery_status.pending,
                            'confirmed': order?.delivery_info?.status == appConst.delivery_status.confirmed,
                            'cancel': order?.delivery_info?.status == appConst.delivery_status.cancelled || order?.delivery_info?.status == appConst.delivery_status.failed,
                            'delivered': order?.delivery_info?.status == appConst.delivery_status.delivered
                        }">{{ $t(`DeliveryDetailComponent.trang_thai_giao_hang_${order?.delivery_info?.status}`)
                            }}</span>
                        <div class="places-info">
                            <div class="place">
                                <img :src="start_location_icon" class="icon-left" />
                                <div class="place-content">
                                    <span>{{ order?.delivery_info?.name_from }} - {{ order?.delivery_info?.phone_from
                                        }}</span>
                                    <em>{{ order?.delivery_info?.address_from }}</em>
                                </div>
                            </div>
                            <div class="distance">

                                <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon> <em
                                    class="distance">{{
                                        order?.delivery_info?.distance }} km</em>
                            </div>
                            <div class="place">
                                <img :src="destination_location_icon" class="icon-left" />
                                <div class="place-content">
                                    <span>{{ order?.delivery_info?.name_to }} - {{
                                        validPhone(order?.delivery_info?.phone_to)
                                        }}</span>
                                    <em>{{ order?.delivery_info?.address_to }}</em>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="map-container" id="driver_map_container">
                    <client-only>
                        <LMap id="driver_leaflet_map" v-on:ready="(e: any) => {
                            leafletMap = e;
                            leafletMap.setView([driver_latitude ?? appConst.defaultCoordinate.latitude, driver_longitude ?? appConst.defaultCoordinate.longitude], 17);
                            leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                            initDriverLeafletMap();
                        }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }"
                            :world-copy-jump="true" :use-global-leaflet="true">
                            <LControlZoom position="bottomright"></LControlZoom>

                            <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
                                name="GoogleMap">
                            </LTileLayer>
                        </LMap>
                    </client-only>
                </div>

            </div>
        </v-overlay>

        <OrderReceiverInfoComponent v-if="showOrderReceiverInfoModal"
            v-on:close="()=>{
                showOrderReceiverInfoModal = false;
            }"
            :init_data="{
                name: order.customer_name,
                phone: order.customer_phone,
                address: order.address,
                latitude: order.customer_latitude,
                longitude: order.customer_longitude,
                images: isString(order.extra_data) ? JSON.parse(order.extra_data)?.location?.images : order.extra_data?.location?.images,
                note: isString(order.extra_data) ? JSON.parse(order.extra_data)?.location?.note : order.extra_data?.location?.note
            }"
        ></OrderReceiverInfoComponent>
    </div>

</template>

<script lang="ts" setup>
import exifr from 'exifr';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { VueFinalModal } from 'vue-final-modal';
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_product from '../../assets/image/icon-for-product.png';
import none_result from "~/assets/image/none-result.webp";
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, showTranslateProductName, showTranslateProductDescription, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import MyShopEditOrderComponent from '../myShopEditOrder/MyShopEditOrderComponent.vue';
import { DeliveryService } from '~/services/orderService/deliveryService';
import { HttpStatusCode } from 'axios';
import { VueDraggableNext } from 'vue-draggable-next';
import { ImageService } from '~/services/imageService/imageService';
import { channel_type, member_type, type ChannelDTO } from '~/components/chatManage/ChatDTO';
import { MqttService } from '~/services/mqttService/mqttService';
import driver_marker_location_icon from "~/assets/image/driver-marker.png"
import start_location_icon from "~/assets/image/start-marker-2.png";
import destination_location_icon from "~/assets/image/destination-marker-2.png";
import { AgentService } from '~/services/agentService/agentService';

const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    shopData: {},
    updated: null,
    mode: null
})
var nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.MyShopOrderDetailComponent')
});

var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var shopService = new ShopService();
var deliveryService = new DeliveryService();
var imageService = new ImageService();
var mqttService = new MqttService();
var agentService = new AgentService();
var searchOrderTimeout: any;

var loadMoreTimeOut: any;
var searchTimeout: any;

var showModalConfirmCancelOrder = ref(false);
var showFindShipper = ref(false);
var showCreateDeliveryModal = ref(false);
var selectAnotherDriverMode = ref(false);
var orderId = ref<any>(props.mode == 'agent' ? (route.params.order_id ?? null) : (route.params.id ?? null));
var order = ref();
var isRefreshing = ref(true);
var isUpdating = ref(false);
var showEditOrderModal = ref(false);
var updated = ref((route.params && route.params.updated) ? route.params.updated : false);
var webInApp = ref(null as any);

var showFullProcess = ref(false);

var listImagesEdit = ref([] as any[]);
var imageFilesName = ref(null as any);
var indexImageActive = ref(0);
var showImageViewerModal = ref(false);
var showWarningImageModel = ref(false);
var showChatToCustomer = ref(false);
var chatToCustomerInfo = ref<ChannelDTO>();
var showDriverLocationModal = ref(false);
var driver_latitude = ref<any>();
var driver_longitude = ref<any>();
var driver_orient_alpha = ref<any>();

var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var destinationMarkerLeaflet: L.Marker;
var startMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);

let my_latitude = ref(null as any);
let my_longitude = ref(null as any);

let latitude_start = ref(null as any);
let longitude_start = ref(null as any);

let latitude_destination = ref(null as any);
let longitude_destination = ref(null as any);
let control: any;
var lastPublishMoving = ref<any>(null);

var showCancelDeliveryModal = ref(false);
var showOrderReceiverInfoModal = ref(false);

onMounted(async () => {
    isRefreshing.value = true;
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
    init();
    mqttService.subscribe(appConst.mqtt_topic.order.replaceAll(':order_id', orderId.value ?? order.value.id), (mess) => {
        let message = mess.mess;
        if (message.type == 'delivery_update' || message.type == 'order_update' || message.type == 'delivery_completed') {
            if (props.mode != 'agent') {
                orderService.detailOrder(orderId.value ?? order.value.id).then(res => {
                    if (res.status == HttpStatusCode.Ok) {
                        order.value = JSON.parse(JSON.stringify(res.body.data));
                        getOrderDelivery().then(() => {
                            setControlRouteMachine();
                        });
                    }
                })
            }
            else {
                agentService.agentOrderDetail(orderId.value ?? order.value.id).then(res => {
                    if (res.status == HttpStatusCode.Ok) {
                        order.value = JSON.parse(JSON.stringify(res.body.data));
                        getOrderDelivery().then(() => {
                            setControlRouteMachine();
                        });
                    }
                })
            }
        }
        if (message.type == 'driver_moving') {
            driver_latitude.value = message.driver_latitude;
            driver_longitude.value = message.driver_longitude;
            driver_orient_alpha.value = message.driver_orient_alpha ?? 0;
            if (!lastPublishMoving.value || (Date.now() - lastPublishMoving.value > 3000)) {
                lastPublishMoving.value = Date.now();
                if (control?.getWaypoints()?.length > 0) {
                    console.log('co control')
                    let currentWaypoint = control.getWaypoints();
                    currentWaypoint[0].latLng = new nuxtApp.$L.LatLng(driver_latitude.value, driver_longitude.value);
                    control.setWaypoints(currentWaypoint);
                    control.route();
                }
                else {
                    console.log('ko co control')
                    setControlRouteMachine();
                }
            }

            if (localeMarkerLeaflet) {
                localeMarkerLeaflet.setLatLng([driver_latitude.value, driver_longitude.value]);
                localeMarkerLeaflet.setRotationAngle(driver_orient_alpha.value);
            }

        }
    })
});
onUnmounted(() => {
    mqttService.unsubscribe(appConst.mqtt_topic.order.replaceAll(':order_id', orderId.value ?? order.value.id))
})
async function init() {
    if (props.mode == 'agent') {
        await getDetailOrderAgent(orderId.value);
    }
    else {
        await getDetailOrder(orderId.value);
    }

    updated.value = props ? props.updated : false
}
function getDetailOrder(id: string) {

    orderService.detailOrder(id).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isRefreshing.value = false;
            order.value = JSON.parse(JSON.stringify(res.body.data));

            latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
            longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

            latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
            longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;
            getOrderDelivery().then(() => {
                setControlRouteMachine();
            });
            setListImagesEdit()
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.don_hang_khong_ton_tai'));
            isRefreshing.value = false;
            // close();
        }
    })
}

function getDetailOrderAgent(id: string) {
    isRefreshing.value = true;
    agentService.agentOrderDetail(id).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isRefreshing.value = false;
            order.value = JSON.parse(JSON.stringify(res.body.data));

            latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
            longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

            latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
            longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;
            getOrderDelivery().then(() => {
                setControlRouteMachine();
            });
            setListImagesEdit()
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.don_hang_khong_ton_tai'));
            isRefreshing.value = false;
            // close();
        }
    })
}

async function setListImagesEdit() {
    var listTemp: any = [];
    order.value?.images.forEach(async (img: any) => {
        let newImg = {
            isEdit: true,
            object_type: img.object_type,
            orientation: img.orientation,
            description: img.description,
            enable: true,
            parent_id: order.value.id,
            path: domainImage + img.path,
            title: img.title,
            index: img.index,
            is_profile_picture: img.is_profile_picture ? img.is_profile_picture : false,
            id: img.id
        }
        listTemp.push(newImg);
    });
    listImagesEdit.value = JSON.parse(JSON.stringify(listTemp));
}
function close() {
    router.back()
    // backHandler();
}

function paymentStatusStyle(status: number) {
    switch (status) {
        case appConst.order_status.waiting.value:
            return "cancel";
        case appConst.order_status.confirmed.value:
            return "cancel";
        // case appConst.order_status.ready.value:
        //     return "cancel";
        case appConst.order_status.taken.value:
            return 'taken';
        case appConst.order_status.return.value:
            return 'return';
        case appConst.order_status.cancel.value:
            return "cancel";
        default:
            return 'taken';
    }
}


function copyToClipboard(text: string) {
    if (!webInApp.value) {
        window.navigator.clipboard.writeText(text);
    }
    else {
        nuxtApp.$emit(appConst.event_key.send_request_to_app, {
            action: appConst.webToAppAction.copyToClipboard,
            data: text
        })
    }
}

async function updateStatusOrder(newStatus: number) {


    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.status = newStatus != 3 ? newStatus : 4;
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    })

    orderService.updateOrder(orderTmp).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            updated.value = true;
            init();
            let messOrder = {
                mess: {
                    type: 'order_update',
                    url: order.value?.id
                },
                topic: appConst.mqtt_topic.order.replaceAll(':order_id', order.value?.order?.id),
            }
            mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', order.value?.order?.id), JSON.stringify(messOrder))
        }
        else if (res.status == '401') {
            toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}

async function updateStatusOrderAgent(newStatus: number) {

    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.status = newStatus != 3 ? newStatus : 4;
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    })
    agentService.agentUpdateOrder(orderTmp).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            updated.value = true;
            init();
            let messOrder = {
                mess: {
                    type: 'order_update',
                    url: order.value?.id
                },
                topic: appConst.mqtt_topic.order.replaceAll(':order_id', order.value?.order?.id),
            }
            mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', order.value?.order?.id), JSON.stringify(messOrder))
        }
        else if (res.status == '401') {
            toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}

function handleUpdateStatusOrder(newStatus: any) {
    if (props.mode == 'agent') {
        updateStatusOrderAgent(newStatus);
    }
    else {
        updateStatusOrder(newStatus);
    }
}

function getOrderDelivery() {
    return new Promise((resolve) => {
        if (order.value.delivery?.id) {
            order.value.delivery_info = JSON.parse(JSON.stringify(order.value.delivery));
            order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
            order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

            latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
            longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

            latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
            longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

            resolve(true);
        }
        else if (order.value?.delivery_partner) {
            let body = {
                partner: order.value?.delivery_partner?.name?.toLowerCase().includes('remagan') ? 'remagan' : order.value?.delivery_partner?.name?.toLowerCase(),
                shop_id: order.value?.shop_id ?? order.value?.shops?.id,
                order_id: order.value?.id ?? orderId.value,
                delivery_partner_id: order.value?.delivery_partner_id
            }
            deliveryService.detailV2ByDeliveryId(body).then((res) => {
                if (res.status == HttpStatusCode.Ok) {
                    order.value.delivery_info = JSON.parse(JSON.stringify(res.body.data));
                    order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
                    order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

                    latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
                    longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

                    latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
                    longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

                    resolve(true);
                }
                else {
                    resolve(false);
                }
            })
        }
        else {
            deliveryService.detailByOrderId(orderId.value).then((res) => {
                if (res.status == HttpStatusCode.Ok) {
                    order.value.delivery_info = JSON.parse(JSON.stringify(res.body.data));
                    order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
                    order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

                    latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
                    longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

                    latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
                    longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

                    resolve(true);
                }
                else {
                    resolve(false);
                }
            })
        }

    })

}

function updateDriver(driver: any) {
    deliveryService.updateDriverId(order.value?.delivery_info?.id, driver.id).then(() => {
        init();
    })
}
// Helper function to calculate price difference percentage
function getPriceChangeInfo(itemOrder: any) {
  const originalPrice = parseFloat(itemOrder.price) || 0;
  const currentPrice = parseFloat(itemOrder.pivot.price_off) || 0;
  
  if (originalPrice === 0 || currentPrice === originalPrice) {
    return { type: 'none', percentage: 0 };
  }
  
  const percentChange = Math.round(((Math.abs(currentPrice - originalPrice) / originalPrice) * 100));
  
  if (currentPrice < originalPrice) {
    return { type: 'discount', percentage: percentChange };
  } else {
    return { type: 'increase', percentage: percentChange };
  }
}
async function fileChangeInput(fileInput: any, childProduct?: any) {
    if (listImagesEdit.value?.length + fileInput.target.files.length > appConst.orderImageMaxAmount) {
        toast.warning(t('MyShopOrderDetailComponent.so_luong_anh_toi_da', { amount: appConst.orderImageMaxAmount }))
    }
    else if (fileInput.target.files.length) {
        // if ((fileInput.target.files.length + listImagesEdit.value.length) < appConst.orderImageMinAmount) {
        //     toast.warn(t('MyShopOrderDetailComponent.so_luong_anh_toi_thieu', { amount: appConst.orderImageMinAmount }))
        // }
        // else {

        // }
        imageFilesName.value = fileInput.target.files;
        for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
            if (fileInput.target.files[i].size > appConst.image_size.max) {
                let imgErr = t('MyShopOrderDetailComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
                toast.error(imgErr);
            }
            else {
                const reader = new FileReader();
                reader.onload = async (e: any) => {
                    const image = new Image();
                    image.src = e.target.result;
                    let orientationExif;
                    if (fileInput.target.files[i].type != 'image/webp') {
                        orientationExif = await exifr.orientation(image) || 0;
                    }
                    else orientationExif = 0;
                    // orientation.value = orientationExif ? orientationExif : 0;
                    let newImg = {
                        isEdit: true,
                        object_type: appConst.object_type.order,
                        orientation: orientationExif,
                        description: null,
                        enable: true,
                        parent_id: order.value.id,
                        path: image.src,
                        title: `${order.value.short_code}_img_${listImagesEdit.value.length}`
                    }
                    listImagesEdit.value.push(newImg);
                }
                await reader.readAsDataURL(fileInput.target.files[i]);
            }
        }
    }
}

function checkEditImages() {
    let listNew = listImagesEdit.value.filter(e => !e.id);
    if (listNew?.length > 0) return true;
    if (listImagesEdit.value.length != order.value.images.length) return true;
    return false;
}
function handleGetListDeleteImage() {
    let listDelete: any[] = [];
    order.value.images.forEach((img: any, index: number) => {
        let indexInCurrentList = listImagesEdit.value.findIndex(function (e) {
            return e.id == img.id;
        })
        if (indexInCurrentList == -1) {
            listDelete.push({ id: img.id })
        }
    });
    return listDelete;
}
async function handleSaveImages() {
    if (listImagesEdit.value?.length < appConst.orderImageMinAmount) {
        toast.warn(t('MyShopOrderDetailComponent.so_luong_anh_toi_thieu', { amount: appConst.orderImageMinAmount }))
    }
    else {
        if (props.mode != 'agent') {
            saveImages();
        }
        else {
            saveImagesAgent();
        }
    }

}

async function saveImages() {
    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    });

    orderTmp.image_delete = await handleGetListDeleteImage();

    orderService.updateOrder(orderTmp).then(async res => {
        if (res.status == HttpStatusCode.Ok) {
            // isUpdating.value = false;
            updated.value = true;

            await Promise.all(
                listImagesEdit.value.map(async (img, index) => {
                    img.index = index;
                    img.is_profile_picture = index == 0 ? true : false;
                    if (!img.id) {
                        await imageService.insertImage(img)
                    }
                })
            ).then(() => {
                init();
                isUpdating.value = false;
            })
        }
        else if (res.status == '401') {
            toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}
async function saveImagesAgent() {
    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    });

    orderTmp.image_delete = await handleGetListDeleteImage();

    agentService.agentUpdateOrder(orderTmp).then(async res => {
        if (res.status == HttpStatusCode.Ok) {
            // isUpdating.value = false;
            updated.value = true;

            await Promise.all(
                listImagesEdit.value.map(async (img, index) => {
                    img.index = index;
                    img.is_profile_picture = index == 0 ? true : false;
                    if (!img.id) {
                        await imageService.insertImage(img)
                    }
                })
            ).then(() => {
                init();
                isUpdating.value = false;
            })
        }
        else if (res.status == '401') {
            toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}
function focusImagesSection() {
    document.getElementById('images_content')?.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    })
}

function chatToCustomer() {
    chatToCustomerInfo.value = {
        members: [{
            member_id: order.value?.customer?.id ?? order.value?.customer_id,
            member: JSON.parse(JSON.stringify(order.value.customer)),
            member_type: member_type.user
        }],
        name: null,
        type: channel_type.user,
        avatar: order.value?.customer?.profile_picture
    };
    showChatToCustomer.value = true;
}

async function initDriverLeafletMap() {

    // leafletMap.setView([
    //   my_latitude.value,
    //   my_longitude.value
    // ], 17);
    // fitToDirection();

    await addDestinationMarker();
    await addStartMarker();
    if (order.value.delivery_info?.status < appConst.delivery_status.delivered) {
        await addDriverLocationMarker();
    }
    setTimeout(async () => {
        await setControlRouteMachine();
    }, 1000);
}
async function setControlRouteMachine() {
    let plan: any;
    if (order.value?.delivery_info?.status == appConst.delivery_status.pending || order.value?.delivery_info?.status == appConst.delivery_status.delivered) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
            nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
        ], {
            createMarker: () => (false),
        })
    }
    else if (order.value?.delivery_info?.status == appConst.delivery_status.confirmed) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
            nuxtApp.$L.latLng(latitude_start.value, longitude_start.value)
        ], {
            createMarker: () => (false),
        })
    }
    else if (order.value?.delivery_info?.status == appConst.delivery_status.picked_up) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
            nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
        ], {
            createMarker: () => (false),
        })
    }

    if (control) {
        control.remove();
        control = null;
        // control.setPlan(plan)
    }
    setTimeout(() => {
        control = new nuxtApp.$L.Routing.Control({
            waypointMode: 'connect',
            router: nuxtApp.$L.Routing.osrmv1({
                serviceUrl: appConst.urlOSRMv1,
                requestParameters: {
                    overview: 'full',
                    annotations: true,
                    steps: true,
                    alternatives: 2,
                },
                profile: 'bike',
                useHints: false,
            }),
            fitSelectedRoutes: 'smart',
            plan: plan,
            autoRoute: false,
            routeWhileDragging: false,
            lineOptions: {
                missingRouteTolerance: 10,
                extendToWaypoints: true,
                addWaypoints: false,
                styles: [{
                    color: 'var(--primary-color-2)',
                    weight: 5,
                }]
            },
            altLineOptions: {
                missingRouteTolerance: 10,
                extendToWaypoints: false,
                addWaypoints: false,
                styles: [{
                    opacity: .8,
                    color: '#545454',
                    weight: 4,
                    dashArray: '10, 10'
                }]
            },
            useZoomParameter: true,
            showAlternatives: true,

        });

        control.on('routingerror', () => {
            // noneRoute.value = true;
            control.remove()
        })
        control.on('routesfound', () => {
            control?.addTo(leafletMap);
        })
        control.route();
    }, 500);

}
function addDriverLocationMarker() {
    let iconDriver = new nuxtApp.$L.Icon({
        iconUrl: driver_marker_location_icon,
        iconSize: appConst.markerCustom.driverIcon.size,
        className: appConst.markerCustom.driverIcon.class,
    });
    localeMarkerLeaflet = nuxtApp.$L.marker([driver_latitude.value ?? appConst.defaultCoordinate.latitude, driver_longitude.value ?? appConst.defaultCoordinate.longitude], {
        icon: iconDriver,
        rotationOrigin: appConst.markerCustom.driverIcon.rotatePosition,
        rotationAngle: driver_orient_alpha.value
    });
    localeMarkerLeaflet.addTo(leafletMap);
}
function addStartMarker() {
    startMarkerLeaflet = nuxtApp.$L.marker([latitude_start.value, longitude_start.value], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: start_location_icon,
            iconSize: [30, 45],
            className: appConst.markerCustom.defaultIcon.class,
        }),
    });
    startMarkerLeaflet.addTo(leafletMap);
}
function addDestinationMarker() {
    destinationMarkerLeaflet = nuxtApp.$L.marker([latitude_destination.value, longitude_destination.value], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: destination_location_icon,
            iconSize: [30, 45],
            className: appConst.markerCustom.defaultIcon.class,
        }),
    });
    destinationMarkerLeaflet.addTo(leafletMap);
}

function directionToCustomer() {
    // await getUserLocation();
    let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${latitude_start.value},${longitude_start.value}&destination=${latitude_destination.value},${longitude_destination.value}&trabelmode=bicycling`;
    return textToDirections ?? "";
}
</script>

<style lang="scss" src="./MyShopOrderDetailStyles.scss"></style>