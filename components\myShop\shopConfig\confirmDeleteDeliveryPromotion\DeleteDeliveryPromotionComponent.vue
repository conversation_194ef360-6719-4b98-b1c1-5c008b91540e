<template>
	<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
		content-class="v-stack modal-content-container delete-delivery-promotion-container"
		v-model="showDeleteDeliveryPromotionModal" v-on:closed="() => {
			close()
		}" contentTransition="vfm-fade">
		<div class='v-stack delete-delivery-promotion-content'>
			<span class='delete-delivery-promotion-title'>
				{{ $t('DeleteDeliveryPromotionComponent.xoa_khuyen_mai') }}
			</span>
			<span class="delete-delivery-promotion-message">
				{{
					delivery_propmotion_data.name?.[locale]?.length ? delivery_propmotion_data.name?.[locale]
						: delivery_propmotion_data.name?.vi?.length ? delivery_propmotion_data.name.vi
							: checkFirstValueNotEmpty(delivery_propmotion_data.name)
				}}
			</span>
		</div>
		<div class='h-stack confirm-modal-buttons'>
			<button class='reject-button' v-on:click="() => {
				close()
			}">
				{{ $t('DeleteDeliveryPromotionComponent.khong') }}
			</button>
			<button class='accept-button' v-on:click="() => {
				submit()
			}">
				{{ $t('DeleteDeliveryPromotionComponent.co') }}
			</button>
		</div>

	</VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';


const nuxtApp = useNuxtApp();
const emit = defineEmits(['close', 'submit'])
const { t, locales, locale } = useI18n()
const props = defineProps({
	title: null,
	mode: null,
	shop_id: null,
	delivery_propmotion_data: null
});

var showDeleteDeliveryPromotionModal = ref(false)
onBeforeMount(async () => {

})

onMounted(() => {
	showDeleteDeliveryPromotionModal.value = true
})
function close() {
	emit('close')
}

function submit() {
	emit('submit')
}

function checkFirstValueNotEmpty(object: any) {
	// Loop through all keys in the object
	for (const key in object) {
		console.log(key)
		if (object[key]) {
			// Return the first key that has a non-empty value
			return object[key]
		}
	}
	// If no key has a non-empty value, return null
	return object.vi;
}

</script>

<style lang="scss" src="./DeleteDeliveryPromotionStyles.scss"></style>