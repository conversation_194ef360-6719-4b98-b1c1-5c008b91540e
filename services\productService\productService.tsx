import axios from "axios";
import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";

export class ProductService extends BaseHTTPService {
    
    searchCancelToken: any;
    detailProduct(id: string){
        let url = appConst.apiURL.productDetail + id
        return this.https('GET', url);
    }

    clientDetailProduct(id: string){
        let url = appConst.apiURL.publicProductDetail + id
        return this.https('GET', url);
    }
    
    relatedProducts(id: any){
        let url = appConst.apiURL.relatedProduct.replaceAll(':id',id); 
        return this.https('GET', url);
    }

    searchBase(search_text: any, offset = 0, limit = 25) {
        if (typeof (this.searchCancelToken) != typeof undefined) {
            this.searchCancelToken.cancel()
        }
        this.searchCancelToken = axios.CancelToken.source();
        let body = {
            search: search_text,
            limit: limit,
            offset: offset
        }
        return this.https('POST', appConst.apiURL.productSearchBase, body, this.searchCancelToken.token);
    }

    addProductFromSystem(body:any){
        let bodyRef = {
            shop_id: body.shop_id,
            list_product: body.list_product.map((el:any)=>{
                return {
                    id: el.id,
                    name: el.name,
                    profile_picture: el.profile_picture,
                    price: el.price,
                    existing: el.existing,
                    selected: el.selected,
                    system_price: el.system_price,
                    stock: el.stock
                }
            })
        }
        return this.https('POST', appConst.apiURL.addProductFromSystem, bodyRef, null, true);
    }

    createPrivateProduct(body:any){
        return this.https('POST', appConst.apiURL.createPrivateProduct, body, null, true);
    }

    updateProduct(body:any){
        return this.https('POST', appConst.apiURL.updateProduct, body, null, true);
    }

    deleteProduct(id:string){
        let body = {
            id: id
        }
        return this.https('POST', appConst.apiURL.deleteProduct, body, null, true);
    }
}