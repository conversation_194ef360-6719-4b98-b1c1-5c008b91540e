.change-permission-container {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  padding: 0;
  // min-height: 40dvh;
  border-radius: 10px;
  background-color: white;

  & > .title-header {
    background: transparent;
  }

  & > .change-permission-content {
    display: flex;
    flex-direction: column;
    padding: 0 15px 15px;

    & > .label {
      font-size: 17px;
      color: var(--primary-color-2);
      font-weight: 600;
      font-style: italic;
    }

    & > .change-step {
      display: flex;
      gap: 5px;
      flex-direction: column;

      & > .browser-name {
        font-weight: 600;
        font-size: 15px;
      }

      & > .step-breadcrumbs {
        display: flex;
        gap: 5px;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        flex-wrap: wrap;
        background: var(--secondary-color-1);
        padding: 3px 10px;
        border-radius: 10px;

        & > span {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;

          & > .step-title {
            font-size: 17px;
            font-weight: 300;
            & > em {
              color: var(--primary-color-1);
              font-weight: 700;
            }
          }
        }
        & > svg {
          font-size: 20px;
          color: var(--primary-color-2);
        }
      }

      & > .quick-link {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        gap: 10px;

        & > span {
          flex: 1;
          padding: 4px 10px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          background: #f5f6fa;
          border-radius: 5px;
        }

        & > button {
          padding: 4px 10px;
          border-radius: 2em;
          background-color: var(--secondary-color-2);
          display: flex;
          align-items: center;
          gap: 5px;
        }
        & > button.active{
            background: var(--primary-color-1);
            color: white;
        }
      }
    }
  }

  & > .footer-actions {
    display: flex;
    padding: 5px 10px;
    justify-content: flex-end;

    & > .close-button {
      color: var(--primary-color-2);
      font-weight: 600;
      border: thin solid;
      padding: 0px 10px;
      border-radius: 2em;
    }
  }
}
