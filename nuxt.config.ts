import { appConst, baseLogoUrl, domainImage } from "./assets/AppConst";
import vuetify, { transformAssetUrls } from "vite-plugin-vuetify";
import appRoute from "./assets/appRoute";
import environment from "./assets/environment/environment";
import { VitePWA } from 'vite-plugin-pwa'
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  nitro: {
    storage: {
      redis: {
        driver: 'redis',
        host: environment.redis.host || '*************',
        port: environment.redis.port || 6378,
        password: environment.redis.password || '98we3y54fdfgG4y1I5xd',
        db: 0,             // Redis database index
        ttl: 86400,
        compress: true
      },
    },
    routeRules: {
      // '/home': {cache: { base: "redis",swr: true,name: environment.redis.prefix,},headers: {'x-custom-header': 'home-page',}},
      // '/en/home': {cache: { base: "redis",swr: true,name: environment.redis.prefix,},headers: {'x-custom-header': 'home-page',}},
      // '/ko/home': {cache: { base: "redis",swr: true,name: environment.redis.prefix,},headers: {'x-custom-header': 'home-page',}},
      // '/ru/home': {cache: { base: "redis",swr: true,name: environment.redis.prefix,},headers: {'x-custom-header': 'home-page',}},
      "/product/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/en/product/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/ko/product/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/ru/product/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/shop/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/en/shop/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/ko/shop/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/ru/shop/**": { cache: { base: "redis", swr: true, name: environment.redis.prefix, } },
      "/firebase-messaging-sw.js": {
        headers: {
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        },
        cache: false
      }
    },
  },
  app: {
    head: {
      charset: "utf-8",
      viewport:
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover, user-scalable=no, shrink-to-fit=no, interactive-widget=resizes-content",
      link: [
        {
          rel: "icon",
          type: "image/png",
          href: "/icon_square_v2_favicon_transparent.png",
        },
        { rel: "preconnect", href: "https://fonts.googleapis.com" },
        {
          rel: "preconnect",
          href: "https://fonts.gstatic.com",
          crossorigin: "",
        },
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Bellota:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&family=Comfortaa:wght@300..700&family=Cormorant:ital,wght@0,300..700;1,300..700&family=Lobster&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Noto+Serif:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Pacifico&family=Pangolin&family=Prata&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Shantell+Sans:ital,wght@0,300..800;1,300..800&family=Tinos:ital,wght@0,400;0,700;1,400;1,700&display=swap",
        },
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Noto+Serif+Display:ital,wght@0,100..900;1,100..900&display=swap",
        },
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Smooch+Sans:wght@100..900&display=swap",
        },
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap",
        },
      ],
      meta: [
        { name: "HandheldFriendly", content: true },
        {
          name: "viewport",
          content:
            "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover, user-scalable=no, shrink-to-fit=no",
        },
        { property: "og:image", content: baseLogoUrl },
        { property: "og:url", content: "https://remagan.com/" },
        {
          property: "og:description",
          content: `Rẻ mà gần (remagan.com) là ứng dụng mua sắm tiện lợi, giúp bạn dễ dàng tìm kiếm và mua hàng với giá rẻ và gần nhất. Chúng tôi không chỉ hỗ trợ người mua, mà còn là cầu nối cho các doanh nghiệp nhỏ và vừa tiếp cận khách hàng thông qua một phương tiện đơn giản và hiệu quả. 
        Với mục tiêu tối ưu hóa giá cả, Rẻ mà gần (remagan.com) ưu tiên mang đến cho người mua những ưu đãi vô cùng hấp dẫn với chiết khấu siêu cạnh tranh, vượt trội so với các ứng dụng khác. Khám phá sự thuận tiện và giá trị vô song cùng Rẻ mà gần (remagan.com) ngay hôm nay!
        #Remagan re ma gan #MuaSắm #ƯuĐãi #TiệnLợi #re_ma_gan #re #ma #gan`,
        },
        {
          property: "og:title",
          content: "Rẻ mà gần | Mua và bán hàng online thật dễ dàng",
        },
        { property: "og:image:width", content: 720 },
        { property: "og:image:height", content: 400 },
      ],
      script: [
        {
          src: "https://accounts.google.com/gsi/client",
          async: true,
          defer: true,
        },
      ],
    },
  },

  devtools: { enabled: environment.production !== true && environment.DEVTOOLS === 1 },
  ssr: true,

  modules: [
    "@vueuse/nuxt",
    "@nuxt/icon",
    "nuxt3-leaflet",
    "@nuxtjs/device",
    "@nuxt/ui",
    "nuxt-vue3-google-signin",
    // "vue3-carousel-nuxt",
    "nuxt-lodash",
    "nuxt-swiper",
    "@nuxt/image",
    "@stefanobartoletti/nuxt-social-share",
    "nuxt-multi-cache",
    "@nuxtjs/i18n",
    "nuxt-rating",
    "@vite-pwa/nuxt",
    "nuxt-emoji-picker",
    // ['@kimyvgy/nuxt-page-cache', {
    //   enabled: true,
    //   useHostPrefix: true,
    //   pages: [
    //     /^\/$/, 
    //     '/home',
    //     /^\/product.+$/g,
    //     /^\/shop.+$/g
    //   ],
    //   store: {
    //     type: 'redis',
    //     host: environment.redis.host,
    //     port: environment.redis.port,
    //     password: environment.redis.password,
    //     db: environment.redis.db,
    //     prefix: environment.redis.prefix
    //   },
    //   key(route:any, context:any){
    //     if (/^\/product\/.+/.test(context.req.url)) {
    //       return { key: `${environment.redis.prefix}_product_${context.req.url}`, ttl: 60 * 10 } // ttl 10p với trang /product/*
    //     }
    //     if (/^\/shop\/.+/.test(context.req.url)) {
    //       return { key: `${environment.redis.prefix}_shop_${context.req.url}`, ttl: 60 * 10 } // ttl 10p với trang /product/*
    //     }
    //     return context.req.url
    //   }
    // }],
    (_options, nuxt) => {
      nuxt.hooks.hook("vite:extendConfig", (config) => {
        // @ts-expect-error
        config.plugins.push(vuetify({ autoImport: true }));
      });
    },
    // [
    //   "@vee-validate/nuxt",
    //   {
    //     // disable or enable auto imports
    //     autoImports: true,
    //     // Use different names for components
    //     componentNames: {
    //       Form: "VeeForm",
    //       Field: "VeeField",
    //       FieldArray: "VeeFieldArray",
    //       ErrorMessage: "VeeErrorMessage",
    //     },
    //   },
    // ],
  ],
  icon: {
    mode: "svg",
  },

  googleSignIn: {
    clientId: environment.GoogleIDApp,
    use_fedcm: true,
  },

  runtimeConfig: {
    googleClientId: environment.GoogleIDApp,
    public: {
      appVersion: "2.3.5",
    },
  },

  css: [
    "~/App.scss",
    "~/AppV2.scss",
    "leaflet/dist/leaflet.css",
    "leaflet-gesture-handling/dist/leaflet-gesture-handling.css",
    "leaflet-routing-machine/dist/leaflet-routing-machine.css",
    "vue3-toastify/dist/index.css",
    "vue-final-modal/style.css",
    "@vuepic/vue-datepicker/dist/main.css",
    "vue-advanced-cropper/dist/style.css",
    "@fancyapps/ui/dist/panzoom/panzoom.css",
    "@fancyapps/ui/dist/fancybox/fancybox.css",
  ],

  plugins: [
    "~/plugins/mitt.client.ts",
    "~/plugins/firebase.client.ts",
    "~/plugins/toastify.client.ts",
    "~/plugins/leaflet.client.ts",
    "~/plugins/vue-final-modal.client.ts",
    "~/plugins/exif.ts",
    "~/plugins/router.ts",
    "~/plugins/cache.server.ts",
    "~/plugins/fancybox.client.ts",
    "~/directives/longpress.ts",
    "~/plugins/disable-console.client.ts",
    "~/plugins/fingerprint.client.ts"
    // '~/assets/AppConst.ts',
    // '~/assets/appRoute.ts'
  ],

  tailwindcss: {
    cssPath: false,
  },

  routeRules: {
    "/": { prerender: true, redirect: appRoute.HomeComponent },
    "/my-shop-order": { redirect: appRoute.ManageOrdersComponent },
    "/my-shop-order/:id": { redirect: appRoute.ManageOrdersComponent },
    "/product/**": {
      swr: true,
      // headers: { "cache-control": `public, max-age=${60 * 60}` },
      cache: { swr: true, maxAge: 60 * 10 },
      prerender: true,
    },
    "/shop/**": {
      swr: true,
      // headers: { "cache-control": `public, max-age=${60 * 60}` },
      cache: { swr: true, maxAge: 60 * 60 },
      prerender: true,
    },
    "/api/**": { cors: true },
    "/*.scss": {
      swr: true,
      headers: {
        "cache-control": `public, max-age=${60 * 60 * 24 * 7}`,
      },
      cache: { swr: true, maxAge: 60 * 60 * 24 * 7 },
    },
  },

  build: {
    transpile: ["vuetify", "@vuepic/vue-datepicker"],
  },
  pwa: {
    injectRegister: "auto",
    registerType: "autoUpdate",
    devOptions: {
      enabled: false,
    },
    workbox: {
      clientsClaim: true,
      // skipWaiting: true,
      maximumFileSizeToCacheInBytes: 5000000,
    }
  },
  vite: {
    vue: {
      template: {
        transformAssetUrls,
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
        },
      },
    },
    build: {
      minify: true,
      // minify: 'esbuild',
      // chunkSizeWarningLimit: 2000,
      // rollupOptions: {
      //   output: {
      //     manualChunks(id) {
      //       if (id.includes('node_modules')) {
      //         return 'vendor'; // Combine all dependencies into a single vendor file
      //       }
      //       // Group smaller files into larger chunks
      //       if (id.includes('/components/')) {
      //         return 'components';
      //       }
      //       // Custom logic to merge small JS files into larger chunks
      //       if (id.includes('/pages/')) {
      //         const pageName = id.split('/pages/')[1].split('/')[0];
      //         return `page-${pageName}`; // Create a separate chunk for each page
      //       }
      //       // New logic to combine small files into a single chunk
      //       if (id.includes('/utils/') || id.includes('/helpers/')) {
      //         return 'utils'; // Combine utility/helper files into a single chunk
      //       }
      //     },
      //   },
      // },
    },
    plugins: [
      VitePWA({
        registerType: 'autoUpdate',
        workbox: {
          // skipWaiting: true,
          clientsClaim: true,
          maximumFileSizeToCacheInBytes: 5000000,
        }
      })
    ]
  },
  components: {
    global: true,
    dirs: [
      {
        path: "~/components",
        pathPrefix: false,
        prefetch: true,
        // preload: true,
      },
    ],
  },

  // components: [
  //
  // ],

  typescript: {
    tsConfig: {
      include: [
        "**/.*",
        "**/*",
        "./src/*.js",
        "./src/*.ts",
        "./src/*.vue",
        "./src/*.ext",
      ],
    },
  },

  devServer: {
    host: "0.0.0.0",
  },
  multiCache: {
    route: {
      enabled: false
    },
    component: {
      enabled: false,

    },
    data: {
      enabled: false,
    },

    // Default cache settings
    // default: {
    //   type: 'memory', // or 'redis' if you're using Redis
    //   max: 100, // Maximum number of items in cache
    //   ttl: 60 * 60 * 24, // Time to live in seconds (1 day)
    // },

    // // Caching HTML responses
    // page: {
    //   type: 'memory',
    //   key: (req:any) => req.url, // Cache based on the request URL
    //   ttl: 60 * 60 * 24, // Cache for 1 hour
    // },

    // // Caching CSS
    // static: {
    //   type: 'memory',
    //   key: (req:any) => req.url, // Cache based on request URL (for static assets)
    //   ttl: 60 * 60 * 24 * 7, // Cache for 1 week
    //   extensions: ['css', 'scss', 'sass', 'json'], // Cache CSSs files
    // },
    // assets: {
    //   type: 'memory', // or 'redis'
    //   key: (req:any) => req.url, // Cache based on request URL
    //   ttl: 60 * 60 * 24 * 30, // Cache for 30 days
    //   extensions: ['png', 'jpg', 'jpeg', 'gif', 'svg', 'woff', 'woff2', 'ttf', 'eot'], // Specify file types to cache
    // },
    // component: {
    //   enabled: true
    // }
  },

  i18n: {
    locales: [
      {
        code: "vi",
        name: "Tiếng Việt",
        language: "vi-VN",
        file: "vi.json",
      },
      {
        code: "en",
        name: "English",
        language: "en-US",
        file: "en.json",
      },
      {
        code: "ru",
        name: "Russian",
        language: "ru-RU",
        file: "ru.json",
      },
      {
        code: "ko",
        name: "Korean",
        language: "ko-KR",
        file: "ko.json",
      },
    ],
    // lazy: true,
    langDir: "locales/",
    defaultLocale: "vi",
    // customRoutes: undefined,
    strategy: "prefix_and_default",
    detectBrowserLanguage: false,
  },

  compatibilityDate: "2024-10-11",
});
