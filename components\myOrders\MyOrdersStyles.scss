.my-orders-container{
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--color-background-2);
  position: relative;
  align-items: center;
  justify-content: center;
  overflow: auto;
  font-weight: 600;
  width: 100%;
  max-width: var(--max-width-content-view-1024) !important;

  & .header-right {
    // display: flex;
    // justify-content: flex-end;
    // gap: 5px;
    // flex: 1;
    // padding-right: 5px;
    // margin-left: auto;


    // & > button {
    //   background: var(--color-background-2);
    //   border-radius: 50%;
    //   padding: 0;
    //   width: 40px;
    //   height: 40px;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   border: none;
    // }

    & .filter-button.active{
      color: var(--primary-color-2) !important;
    }
  }
  & > .my-orders-content {
    flex: 1;
    width: 100%;
    overflow: auto;
  }
  & .item-order {
    padding: 10px;
    margin-top: 10px;
    align-items: center;
    width: 100%;
    gap: 5px;
    background-color: white;
    border: thin solid #ccc;
    border-radius: 5px;

    & .order-button-container {
      padding: 0;
      align-items: flex-start;
      background: white;
      // color: var(--primary-color-1);
      gap: 5px;
      font-size: 1em;
      width: 100%;
      height: 100%;
      border: none;

      & > .v-stack {
        width: 100%;

        & > .h-stack {
          flex: 1;
          justify-content: space-between;
          width: 100%;
        }

        & > .h-stack.order-detail-advanced {
          justify-content: flex-start;
        }
        & .is-new {
          font-size: 10px;
          text-transform: uppercase;
          color: white;
          padding: 5px 10px;
          margin: 0 10px 0 auto;
          background: #1a95ff;
          border-radius: 5px;
          align-items: center;
          display: flex;
        }
        & > .shop-name{
          color: var(--primary-color-1);
          font-weight: bold;
          justify-content: flex-start;
          gap: 5px;

          & > a{
            color: #545454;
            font-style: italic;
            font-weight: 700;
          }
        }

        & .order-status {
          padding: 5px;
          border-radius: 5px;
          justify-content: center;

          &.waiting {
            color: rgb(164, 171, 182);
            background-color: rgba(164, 171, 182, 0.25);
          }

          &.confirmed {
            color: rgb(230, 149, 0);
            background-color: rgba(230, 149, 0, 0.25);
          }

          &.ready {
            color: rgb(45, 204, 255);
            background-color: rgba(45, 204, 255, 0.25);
          }

          &.taken {
            color: rgb(86, 240, 0);
            background-color: rgba(86, 240, 0, 0.25);
          }

          &.cancel {
            color: rgb(255, 56, 56);
            background-color: rgba(255, 56, 56, 0.25);
          }
        }

        & .customer-name {
          font-weight: 600;
          color: var(--primary-color-1);
        }

        & .customer-phone {
          font-weight: 600;
        }

        & .short-code {
          margin-left: auto;
          font-size: 1.1em;
        }

        & .delivery-info {
          display: flex;
          gap: 5px;

          & > .driver-info {
            display: flex;
            gap: 5px;
            align-items: center;

            & > img {
              width: 40px;
              min-width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
              align-self: baseline;
            }
          }

          & > .none {
            margin-left: auto;
            font-size: 15px;
            color: #79787d;
            font-style: italic;
          }
        }

        & .delivery-partner-info{
          display: flex;
          gap: 5px;
          align-items: flex-end;
          line-height: normal;

          & > img{
            height: 25px;
            width: auto;
            object-fit: contain;
          }
          & > .none {
            margin-left: auto;
            font-size: 15px;
            color: #79787d;
            font-style: italic;
          }
        }

        & .title {
          color: var(--color-text-note);
        }

        & .total {
          font-weight: 600;
          color: var(--primary-color-1);
        }
      }
    }
  }
  & .orders-tab {
    text-transform: none;
    font-weight: 600;
    font-size: 1em;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    min-height: 50px;
    background: white;

    & .order-tab-title {
      text-transform: none;
      border-bottom: 2px solid transparent;
      color: #79787d;
      font-weight: 600;
      gap: 5px;
      font-size: 1em;
      background-color: white;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        // & > span {
        //   width: 25px;
        //   height: 25px;
        //   background: var(--color-button-special);
        //   color: white;
        //   border-radius: 50%;
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   font-weight: 500;

        //   & > span {
        //     font-size: 0.8em;
        //   }
        // }

        & em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 25px;
          height: 25px;
          font-size: 0.8em;
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 600;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    & .order-tab-title.active {
      font-weight: 600;
      border-bottom: 2px solid var(--primary-color-1);
    }
  }
  & .tab-content-container {
    width: 100%;
    flex: 1;
    display: flex;

    & div[class*="v-window"] {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    & div.v-window-item {
      padding: 0 5px;
      overflow: auto;
    }
  }
  & .empty-list {
    height: 100%;
    flex: 1;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 15px;

    & > img {
      margin: 10px auto;
      justify-content: center;
      border-radius: 50%;
      height: 250px;
      width: 250px;
      object-fit: contain;
    }

    & > span {
      font-size: 1.5em;
      color: var(--color-text-note);
      text-align: center;
    }
  }
  & .sticky-header{
    width: 100%;
    position: sticky;
    top: 50px;
    z-index: 1;
  }
  & .search-bar-container {
    width: 100%;
    padding: 10px 15px;
    color: var(--color-text-note);
    font-weight: 500;
    background-color: var(--color-background-2);
    & > .search-bar {
      background-color: white;
      gap: 5px;
      padding: 5px 10px;
      border-radius: 5px;

      & > input {
        margin-top: 0;
        border: none;
        display: flex;
        flex: 1;
        outline: none;
      }
    }
  }
  & .search-category-empty {
    color: var(--color-text-note);
  }
  & .refresh {
    color: var(--primary-color-1) !important;
    font-weight: 600;
    cursor: pointer;
  }
  & .load-more {
    position: absolute;
    bottom: 0;
    left: 0;
    padding-bottom: 15px;
    width: 100%;
    text-align: center;
    background: white;
  }
}
.filter-menu-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .filter-menu-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    max-width: var(--max-width-content-view-1024);
    transform: translateX(-50%);

    & > .filter-menu-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      text-align: center;
      padding: 10px 10px 20px 10px;

      & > .filter-title{
        font-size: 1.3em;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color-1);
        position: relative;

        & > button{
          position: absolute;
          right: 0;
          color: var(--color-text-black);
          display: flex;
          align-items: center;
          top: 50%;
          font-size: 1.5em;
          transform: translateY(-50%);
          animation: none;
        }
      }

      & > .time-filter{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 10px;
        font-size: 1.3em;
        gap: 10px;

        & > .item-time-filter{
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        & > .item-time-filter.active{
          color: var(--primary-color-1);
        }

        & > .select-date{
          width: 100%;
          justify-content: space-evenly;
        }

        & > .footer{
          display: flex;
          justify-content: space-evenly;
          width: 100%;

          & > button{
            width: 35%;
            padding: 5px 10px;
            display: flex;
            border-radius: 10px;
            white-space: nowrap;
            justify-content: center;
            align-items: center;
            color: var(--primary-color-1);
            border: thin solid var(--primary-color-1);
          }

          & > button.apply{
            background-color: var(--primary-color-1);
            color: white;
          }
        }
      }
    }
  }

}