<script setup lang="ts">
import { useDraggable, useDebounce } from '@vueuse/core'
import { channel_type, member_type, type ChannelDTO } from "~/components/chatManage/ChatDTO";
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber, nonAccentVietnamese, showTranslateProductName, showTranslateProductDescription, validPhone, formatNumberToShort, checkFirstValueNotEmpty } from '~/assets/AppConst';
import { increase_view_obj_type, InteractionObjectType, type CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import banner_temp from '~/assets/image/remagan-banner-19_1.png';
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import non_avatar from '~/assets/image/non-avatar.jpg';
import list_empty from "~/assets/image/list-empty-2.jpg"
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import discount_tag from "~/assets/image/sale_off_badge_background.png";
import shop_banner_temp from "~/assets/imageV2/logo-landscape-1024.jpg";
import shop_bg from "~/assets/imageV2/shop-bg.jpg";
import category_temp from "~/assets/imageV2/shop-bg.jpg";

import { VueFinalModal } from 'vue-final-modal';
import { AgentService } from '~/services/agentService/agentService';
import environment from '~/assets/environment/environment';
import { settings } from 'firebase/analytics';
import { InteractionService } from "~/services/interactionService/interactionService";
import { HttpStatusCode } from "axios";
import { condition_relationship } from '~/components/myShop/shopConfig/ShopConfigDTO';

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n()
var props = defineProps({
	mode: null,
	shopGet: null,
	shopStyle: null
})
var router = useRouter();
var route = useRoute();
const dataId = props.mode != 'agent' ? route.params.slug : route.params.id;
// var shopGet = await fetch(appConst.apiURL.detailShop + '/' + dataId);
var shopDetails = ref<any>(props.shopGet ?? null);
var shopData = ref();
var dataShopProducts = ref([] as any);
var dataShopProductsSaleOff = ref([] as any[]);
var dataShopProductsFeature = ref([] as any[]);
var dataShopCategories = useState(`data_shop_categories_${dataId}`, () => { return [] as any[] });
var dataShopCategoriesBackup = useState(`data_shop_categories_backup_${dataId}`, () => { return [] as any[] });
var open_time_list = ref([] as any);

// const { data, refresh } = await useLazyFetch(appConst.apiURL.detailShop + '/' + dataId, {
// 	key: `shop_detail_${dataId}`,
// 	headers: {
// 		Origin: `https://${environment.domain}`,
// 	},
// 	cache: 'default',
// 	getCachedData: (key) => {
// 		// Check if the data is already cached in the Nuxt payload
// 		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
// 			return nuxtApp.payload.data[key]
// 		}

// 		// Check if the data is already cached in the static data
// 		if (nuxtApp.static.data[key]) {
// 			return nuxtApp.static.data[key]
// 		}

// 		return null
// 	}
// });
// let shopGet: any = data.value;
// const { data, refresh, execute } = await useLazyFetch(appConst.apiURL.detailShop + '/' + dataId, {
// 	key: `shop_space_${dataId}`,
// 	// ttl: 600,
// 	headers: {
// 		Origin: `https://${environment.domain}`
// 	},
// 	cache: 'default',
// 	getCachedData: (key) => {
// 		// Check if the data is already cached in the Nuxt payload
// 		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
// 			// if (route.query?.flush == '1') {
// 			// 	console.log('flush api cache', dataId);
// 			// 	delete nuxtApp.payload.data[`shop_detail_${dataId}`]; // Remove the specific key
// 			// 	return null
// 			// }
// 			// console.log("using nuxt payload for api", nuxtApp.payload.data[key].body.data);
// 			// return nuxtApp.payload.data[key]
// 			if (route.query?.flush == '1') {
// 				return null;
// 			} else {
// 				return nuxtApp.payload.data[key];
// 			}
// 		}
// 		// Check if the data is already cached in the static data
// 		if (nuxtApp.static.data[key]) {
// 			// if (route.query?.flush == '1') {
// 			// 	console.log('flush api cache', dataId);
// 			// 	delete nuxtApp.payload.data[`shop_detail_${dataId}`]; // Remove the specific key
// 			// 	return null
// 			// }
// 			// return nuxtApp.static.data[key]
// 			if (route.query?.flush == '1') {
// 				return null;
// 			}
// 			return nuxtApp.static.data[key];
// 		}
// 		return null
// 	}
// });

// let shopGet: any = data.value;

// if (shopGet?.status == 200) {
// 	shopDetails.value = await JSON.parse(JSON.stringify(shopGet?.body.data));
// 	shopData.value = shopDetails.value;
// 	open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
// 	// dataShopProducts.value = shopDetails.value.products;
// 	// getProductsWithCategory();
// 	let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
// 	useServerSeoMeta({
// 		ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
// 		title: () => `${shopDetails.value.name} | Rẻ mà gần`,
// 		description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
// 		ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
// 		ogUrl: () => domain + route.fullPath,
// 		ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
// 		ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
// 	});

// 	const dataProducts = await useLazyFetch(appConst.apiURL.getProductWithCategoryInShop.replaceAll(':shop_id', shopDetails.value?.id), {
// 		key: `shop_products_space_${shopDetails.value?.id}`,
// 		// ttl: 600,
// 		headers: {
// 			Origin: `https://${environment.domain}`
// 		},
// 		cache: 'default',
// 		getCachedData: (key) => {
// 			// Check if the data is already cached in the Nuxt payload
// 			if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
// 				if (route.query?.flush == '1') {
// 					return null;
// 				} else {
// 					return nuxtApp.payload.data[key];
// 				}
// 			}
// 			// Check if the data is already cached in the static data
// 			if (nuxtApp.static.data[key]) {
// 				if (route.query?.flush == '1') {
// 					return null;
// 				}
// 				return nuxtApp.static.data[key];
// 			}
// 			return null
// 		}
// 	});
// 	if (dataProducts.data?.value) {
// 		let products = JSON.parse(JSON.stringify(dataProducts.data?.value));
// 		dataShopCategories.value = products.body?.data?.categories.filter((e: any) => e.products.length > 0);
// 		dataShopCategoriesBackup.value = products.body?.data?.categories.filter((e: any) => e.products.length > 0);
// 	}
// }

var showSelectedProduct = ref(false);


var authService = new AuthService();
var userService = new UserService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var interactionService = new InteractionService();
var bannerElement: any;

var loadMoreTimeOut: any;
var searchProductTimeout: any;

var userProfile = ref(null as any);
var refreshing = ref(false);
var isErrored = ref(false);

var countProducts = ref(0);
var shop_id = ref((route.params && props.mode != 'agent' ? (route.params.slug ? route.params.slug : null) : (route.params.id ? route.params.id : null)) as any);
var showGrid = useState('shop_detail_grid_view', () => {
	return false
});
var productSortBy = ref(6);
var cartData = ref([] as CartDto[]);
var loadMore = ref(false);
var filterProductLoading = ref(false);
var selectedProduct = ref(null as any);
var showEditProductModal = ref(false);
var showEditShopInfoModal = ref(false);

var search_text = useState(`search_text_${shop_id.value}`, () => { return "" });
const search_debounced = useDebounce(search_text, 500)
var showSearchBar = ref(false);
var filterCategory = ref(null);

var showDescription = ref(true);
// var showFullDescription = ref(false);
// var checkShowFullable = ref(false);
var showDirection = ref(false);
var webInApp = ref(null as any);

var selectingCategory = ref(false);
var categoryCarousel = ref();
var promotionCarousel = ref();

var enableScrollTopBtn = ref(false);
var baseDescription = ref("");

var scrollPosition = useState(() => { return 0 as any });

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();
var is_followed = ref(false);

var chat_to_shop_drag_button = ref<HTMLElement | null>(null)
var position = reactive({ x: 0, y: 0 });
var initialDragPosition = ref({ x: 0, y: 0 })
const threshold = 10;
var fixX = ref(0);

var showPromotionCondition = ref(false);
var selectedPromotion = ref<any>(null);
var viewTimer: any;
var windowInnerWidth = ref();

watch(search_debounced, (value) => {
	console.log('Debounced value changed:', value)
	// API call or logic here
	searchProduct();
})

onUnmounted(async () => {
	nuxtApp.$unsubscribe(appConst.event_key.shop_updated);
	nuxtApp.$emit('change_header_for_shop_temp_1', false);
	if (shopData.value?.id) {
		let shopRecent = JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) : [];
		let index = shopRecent.findIndex((e: any) => {
			return e.id == shopData.value?.id;
		})
		if (index != -1) {
			shopRecent.splice(index, 1)
		}
		shopRecent = [
			shopData.value,
			...shopRecent,
		].slice(0, 10);

		localStorage.setItem(appConst.storageKey.shopRecent, JSON.stringify(shopRecent));
		clearTimeout(viewTimer);
	}
});

onMounted(async () => {
	// if (route.query?.flush == '1') {
	// 	console.log('shop refresh', dataId)
	// 	refresh();
	// 	// clearNuxtData(`shop_detail_${dataId}`)
	// }

	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartNumber();
	})
	nuxtApp.$listen(appConst.event_key.shop_updated, (e) => {
		console.log("shop update")
		shopData.value = null;
		if (props.mode != 'agent') {
			getDetailShop();
		}
		else {
			agentGetDetailShop()
		}
	})
	await init();

	nuxtApp.$emit('change_header_for_shop_temp_1', {
		_color_1: props?.shopStyle?._color_1,
		_color_2: props?.shopStyle?._color_2, 
		_color_3: props?.shopStyle?._color_3, 
		_color_4: props?.shopStyle?._color_4, 
		_color_5: props?.shopStyle?._color_5, 
	});

	window.addEventListener('resize', () => {
		handleScreenResize()
	})
	handleScreenResize()
	// useServerCache({
	// 	key: `shop_space_${shopDetails.value.id}`,
	// 	duration: 60 * 60, // Cache for 1 hour
	// 	condition: () => !!shopDetails.value.name // Cache only if productData.name is available
	// });
});
onBeforeMount(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	// let userProfileRef = await localStorage.getItem(appConst.storageKey.userInfo);
	// if (userProfileRef) {
	// 	userProfile.value = JSON.parse(userProfileRef as string)
	// }
	// else {
	// 	userProfile.value = null;
	// }
	userProfile.value = await authService.checkAuth();

})

useDraggable(chat_to_shop_drag_button, {
	pointerTypes: ['mouse', 'touch'],
	onStart: ({ x, y }) => {
		initialDragPosition.value.y = position.y;
		chat_to_shop_drag_button.value?.classList.add('none-transition');
	},
	onEnd: ({ x, y }) => {
		position.x = fixX.value;

		const movedY = Math.abs(y - initialDragPosition.value.y)
		if (!y || movedY < threshold) {
			chatToShop()
		}
		chat_to_shop_drag_button.value?.classList.remove('none-transition');
		// handleDragStart(x, y) 
	},
	onMove({ x, y }) {
		position.x = x;
		position.y = y
	},
})

const setChatToShopPositionToBottomRight = () => {
	let container = document.getElementById('shop_content_container_1');
	const containerHeight = container?.getBoundingClientRect().height ?? 0;
	const containerWidth = container?.getBoundingClientRect().width ?? 0;
	position.x = containerWidth - 60 // Adjust based on button width
	position.y = containerHeight - 175// Adjust based on button height
	fixX.value = containerWidth - 60;
}
async function init() {
	if (shopDetails.value) {
		let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
		useSeoMeta({
			ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
			title: () => `${shopDetails.value.name} | Rẻ mà gần`,
			description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
			ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
		});
		useServerSeoMeta({
			ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
			title: () => `${shopDetails.value.name} | Rẻ mà gần`,
			description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
			ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
		})
		shopData.value = await JSON.parse(JSON.stringify(shopDetails.value));
		// await cacheStorage.setItem(`shop_space_${dataId}`, `cache for shop: ${dataId}`)
		refreshing.value = false;
		await getProductSaleOffInShop();
		await getProductFeatureInShop();
		await getProductsWithCategory();

		await checkInteraction()
		increaseView();
		open_time_list.value = formatOpeningHours(JSON.parse(shopData.value?.open_hours));

		if (shopDetails.value?.id == shopData.value?.id) {
			setTimeout(() => {
				document
					.getElementById("shop_content_container_1")?.scrollBy({
						top: scrollPosition.value,
						behavior: 'smooth'
					});
			}, 1000);
		}
		else {
			search_text.value = "";
		}
	}
	else {
		search_text.value = "";
		if (props.mode != 'agent') {
			await getDetailShop();
		}
		else {
			await agentGetDetailShop()
		}
	}
	baseDescription.value =
		`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần.
Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
	getCartNumber();
	// refreshListProduct()
	// 
}
function getDetailShop() {
	refreshing.value = true;
	shopService.detailShop(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
			useServerSeoMeta({
				ogTitle: () => `${shopData.value.name} | Rẻ mà gần`,
				title: () => `${shopData.value.name} | Rẻ mà gần`,
				description: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
				ogDescription: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
				ogUrl: () => domain + route.fullPath,
				ogImage: () => shopData.value.banner ? domainImage + shopData.value.banner.path : baseLogoUrl,
				ogImageUrl: () => shopData.value.banner ? domainImage + shopData.value.banner.path : baseLogoUrl
			});
			useSeoMeta({
				ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
				title: () => `${shopData.value?.name} | Rẻ mà gần`,
				description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogUrl: () => domain + route.fullPath,
				ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
				ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
			});

			// getShopProduct(0, 20);
			refreshing.value = false;
			await getProductSaleOffInShop();
			await getProductFeatureInShop();
			await getProductsWithCategory();
			await checkInteraction()
			increaseView();
			open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
			// setTimeout(() => {
			// 	checkShowFullDescription();
			// }, 300);

			baseDescription.value =
				`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần.
Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
		}
		else {
			refreshing.value = false;
			isErrored.value = true;
		}
	}).catch(err => {
		refreshing.value = false;
		isErrored.value = true;
		console.log(err);
		// setState({
		//     refreshing: false,
		// })
	})
}

function agentGetDetailShop() {
	refreshing.value = true;
	agentService.agentShopDetail(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			// getShopProduct(0, 20);
			await getProductSaleOffInShop();
			await getProductFeatureInShop()
			await getProductsWithCategory();
			await checkInteraction()
			increaseView();
			refreshing.value = false;

			setTimeout(() => {
				// checkShowFullDescription();
				open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
				let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
				useServerSeoMeta({
					ogTitle: () => `${shopData.value.name} | Rẻ mà gần`,
					title: () => `${shopData.value.name} | Rẻ mà gần`,
					description: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
					ogDescription: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
					ogUrl: () => domain + route.fullPath,
					ogImage: () => shopData.value.banner ? domainImage + shopData.value.banner.path : baseLogoUrl,
					ogImageUrl: () => shopData.value.banner ? domainImage + shopData.value.banner.path : baseLogoUrl
				});
				useSeoMeta({
					ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
					title: () => `${shopData.value?.name} | Rẻ mà gần`,
					description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogUrl: () => domain + route.fullPath,
					ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
					ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
				});
			}, 300);
		}
		else {
			toast.error(t('ShopComponent.ban_khong_co_quyen_truy_cap'))
			isErrored.value = true;
			refreshing.value = false;
		}

	}).catch(() => {
		isErrored.value = true;
		refreshing.value = false;
	})
}

function getCartNumber() {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$?.length ? cartData$ : [];
}
// function shareTo(social_name:string){
// 	let share = useSocialShare({
// 		network: 'facebook', // Required!
// 		url: 'https://www.example.com', // Optional, defaults to current page URL if not provided
// 		title: 'My Custom Title', // Optional, see the "Supported Networks" table below
// 		user: 'twitter_user', // Optional, see the "Supported Networks" table below
// 		hashtags: 'list,of,hashtags', // Optional, see the "Supported Networks" table below
// 		image: 'https://www.example.com/path/to/image.jpg', // Optional, see the "Supported Networks" table below
// 	})
// }

// function getShopProduct(offset: any = 0, limit: any = 20) {
// 	loadMore.value = true;
// 	filterProductLoading.value = true;
// 	offset = offset != null ? offset : dataShopProducts.value.length;

// 	limit = limit != null ? limit : 20;

// 	let categories: any[] = filterCategory.value ? [filterCategory.value] : [];

// 	shopService.searchProductsInShop(
// 		search_text.value,
// 		shopData.value.id,
// 		categories,
// 		limit,
// 		offset,
// 		productSortBy.value,
// 		null,
// 		(userProfile.value && userProfile.value.id == shopData.value.user_id) ? true : false
// 	).then(res => {
// 		res.body.data.result.forEach((e: any) => {
// 			e.currentQuantity = getCurrentQuantityInCart(e)
// 		});
// 		dataShopProducts.value = JSON.parse(JSON.stringify(res.body.data.result));
// 		countProducts.value = res.body.data.count;
// 		refreshing.value = false;
// 		loadMore.value = false;
// 		filterProductLoading.value = false;

// 	}).catch(err => {
// 		console.log(err);
// 		filterProductLoading.value = false;
// 		refreshing.value = false;
// 		loadMore.value = false
// 	})
// }
function getProductSaleOffInShop() {
	console.log("get sale off")
	shopService.searchProductsInShopClient(
		search_text.value,
		shopData.value?.id,
		[],
		10,
		null,
		productSortBy.value,
		null,
		(userProfile.value && userProfile.value.id == shopData.value?.user_id) ? true : false,
		true
	).then(res => {
		// res.body?.data?.result.forEach((e: any) => {
		// 	e.currentQuantity = getCurrentQuantityInCart(e)
		// });
		dataShopProductsSaleOff.value = JSON.parse(JSON.stringify(res.body?.data?.result));

	}).catch(err => {
		console.log(err);
	})
}

function getProductFeatureInShop() {
	console.log("get sale off")
	shopService.searchProductsInShopClient(
		"",
		shopData.value?.id,
		[],
		10,
		null,
		productSortBy.value,
		null,
		(userProfile.value && userProfile.value.id == shopData.value?.user_id) ? true : false,
		false,
		true
	).then(res => {
		// res.body?.data?.result.forEach((e: any) => {
		// 	e.currentQuantity = getCurrentQuantityInCart(e)
		// });
		dataShopProductsFeature.value = JSON.parse(JSON.stringify(res.body?.data?.result));

	}).catch(err => {
		console.log(err);
	})
}

function getProductsWithCategory() {
	shopService.getProductsWithCategory(shopData.value?.id).then(res => {
		dataShopCategories.value = res.body?.data?.categories ? JSON.parse(JSON.stringify(res.body?.data?.categories.filter((e: any) => e.products.length > 0))) : [];
		dataShopCategoriesBackup.value = res.body?.data?.categories ? JSON.parse(JSON.stringify(res.body?.data?.categories.filter((e: any) => e.products.length > 0))) : [];
	}).catch(err => {
		dataShopCategories.value = [];
		dataShopCategoriesBackup.value = [];
	})
}

function getCurrentQuantityInCart(itemProduct: any) {
	let index = cartData.value.findIndex(function (e: CartDto) {
		return e.product_id == itemProduct.id
	});
	return index != -1 ? cartData.value[index].quantity : 0
}

function copyToClipboard(text: string) {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(text);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: text
		})
	}
	toast.info(t('ShopComponent.da_sao_chep_lien_ket'), {
		hideProgressBar: true,
		autoClose: 1000
	})
}

function callToShop() {
	if (!validPhone(shopData.value.phone).length) {
		toast.warning(t('ShopComponent.cua_hang_chua_cap_sdt'));
	}
}

// function refreshListProduct() {
// 	let listProduct = JSON.parse(JSON.stringify(dataShopProducts.value));
// 	listProduct.forEach((e: any) => {
// 		e.currentQuantity = getCurrentQuantityInCart(e)
// 	});
// 	dataShopProducts.value = JSON.parse(JSON.stringify(listProduct));
// }

// function getListCategory() {
// 	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
// 		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
// 	})
// }

function checkCategoryFilter(id: any) {
	if (id) {
		return filterCategory.value == id ? true : false
	}
	return false
}

// function listScroll(event: any) {
// 	let el = document?.getElementById('last_of_list')?.getBoundingClientRect().bottom;
// 	if (el && el <= window.innerHeight + 10) { loadMoreProduct(); }
// }

const searchProduct = async () => {
	console.log('search');
	filterProductLoading.value = true;
	await searchProductInCurrentList()
	filterProductLoading.value = false
}

function searchProductInCurrentList() {
	dataShopCategories.value = [];
	var arrTemp = JSON.parse(JSON.stringify(dataShopCategoriesBackup.value));
	var res: any = [];
	if (search_text.value?.length) {
		arrTemp.forEach((element: any) => {
			if (isArray(element.products)) {
				element.products = element.products?.filter((el: any) => {
					return nonAccentVietnamese(showTranslateProductName(el) ?? '').includes(nonAccentVietnamese(search_text.value))
						|| nonAccentVietnamese(showTranslateProductDescription(el) ?? '').includes(nonAccentVietnamese(search_text.value))
				});
			}
			else {
				element.products = [];
			}
			res.push(element)
		});
	}
	else {
		res = arrTemp;
	}
	dataShopCategories.value = JSON.parse(JSON.stringify(res))
	return
}

function share() {
	let messageShare = {
		name: shopData.value.name,
		link: domain + appRoute.DetailShopComponent + "/" + (shopData.value.slug ? shopData.value.slug : shopData.value.id),
		image: shopData.value.banner ? domainImage + shopData.value.banner.path : baseLogoUrl
	}
	nuxtApp.$emit(appConst.event_key.share, messageShare);
}

function getPercentSaleOff(product: any) {
	if (product.price_off && product.price) return -Math.round(((product.price - product.price_off) / product.price) * 100 || 0);
	return 0
}

function changeSlideCategoryIndex(index = 0) {
	categoryCarousel.value.slideTo(index)
}
async function scrollToCategory(category_id: any) {
	let categoryEl = document.getElementById(`category_products_${category_id}`) as Element;
	var block: any = 'center';

	if (categoryEl?.getBoundingClientRect().height > window.innerHeight) {
		block = 'start';
	}
	await categoryEl?.scrollIntoView({
		behavior: 'smooth',
		block: block,
		inline: 'start',
		// inline: 'center'
	});
}
const containerScrolling = (e: any) => {

	showHideHeader(e)

	scrollPosition.value = e.target.scrollTop as number;

	let bannerEl = document.getElementById('shop-container') as HTMLElement;
	if (bannerEl?.getBoundingClientRect()?.top < 0) {
		enableScrollTopBtn.value = true;
	}
	else enableScrollTopBtn.value = false;

	let productListElements = document.querySelectorAll('.category-products-container');
	productListElements.forEach((productList, index) => {
		if (isElementInCenterViewPort(productList)) {
			filterCategory.value = dataShopCategories.value[index].id;
			changeSlideCategoryIndex(index)
		}
	});

	setTimeout(() => {
		var stickyElement = document.getElementById('categories_container') as HTMLElement;

		console.log(stickyElement.getBoundingClientRect().top)

		if (stickyElement.getBoundingClientRect().top <= 55) {
			stickyElement.classList.add('sticky-padding');
		} else {
			stickyElement.classList.remove('sticky-padding');
		}
	}, 500);

}

function scrollToTop() {
	document.getElementById('shop_content_container_1')?.scrollTo({
		top: 0,
		behavior: 'smooth'
	})
}

function isElementInCenterViewPort(element: Element): boolean {
	const viewportHeight = window.innerHeight;
	const elementRect = element.getBoundingClientRect();

	// Element position relative to viewport
	const elementTop = elementRect.top;
	const elementBottom = elementRect.bottom;

	// Calculate viewport center
	const viewportCenter = viewportHeight / 2;

	// Check if element is in center of viewport
	return elementTop <= viewportCenter && elementBottom >= viewportCenter;
}

function formatOpeningHours(hoursObj: any) {
	function formatHours(hours: any) {
		return hours.map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`).join(' & ');
	}
	const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

	const result = [];
	if (hoursObj) {
		let currentGroup: any = [];
		let currentHours = hoursObj[days[0]];

		for (let i = 0; i < days.length; i++) {
			const day = days[i];
			const hours = hoursObj[day];

			if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
				currentGroup.push(t(`DayInWeek.${day}`));
			} else {
				if (currentGroup.length > 1) {
					// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				} else {
					// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				}
				currentGroup = [t(`DayInWeek.${day}`)];
				currentHours = hours;
			}
		}
		// Xử lý nhóm cuối cùng
		if (currentGroup.length > 1) {
			// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		} else {
			// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		}
	}

	return result;

}

function showHideHeader(event: any) {
	nuxtApp.$emit(appConst.event_key.scrolling, event)
}

function followShop() {
	if (userProfile.value?.id) {
		interactionService.createOrDelete({
			interaction_type: appConst.interactionType.follow,
			object_id_from: userProfile.value.id,
			object_type_from: appConst.object_type.user as any,
			object_id_to: shopData.value.id,
			object_type_to: appConst.object_type.shop as any
		}).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				checkInteraction().then(() => {
					if (is_followed.value) {
						shopData.value.follows += 1;
					}
					else {
						shopData.value.follows -= 1;
					}
				});
			}
		})
	}
	else {
		nuxtApp.$emit(appConst.event_key.require_login, {
			redirect_url: route.path,
		})
	}
}

async function checkInteraction() {
	return new Promise((resolve) => {
		interactionService.checkInterctions(shopData.value?.id, InteractionObjectType.follow).then(res => {
			is_followed.value = res?.body?.data;
			resolve(is_followed.value);
		}).catch(() => {
			resolve(is_followed.value);
		});
	})

}

function chatToShop() {
	chatToShopInfo.value = {
		members: [{
			member_id: shopData.value.id,
			member: JSON.parse(JSON.stringify(shopData.value)),
			member_type: member_type.shop
		}],
		name: null,
		type: channel_type.user,
		avatar: shopData.value.logo
	};
	showChatToShop.value = true;
}

function increaseView(obj_id = shopData.value.id ?? shop_id.value) {
	if (obj_id) {
		viewTimer = setTimeout(() => {
			const key = `view_${route.fullPath}`;
			if (interactionService.shouldIncreaseViewCount(key)) {
				console.log('tang luot view')
				interactionService.increaseView(obj_id, increase_view_obj_type.shop)
			}
			else {
				console.log('da tang luot view roi')
			}
		}, 3000);
	}

}

function handleScreenResize() {
	windowInnerWidth.value = window.innerWidth;
	if (windowInnerWidth.value < 1024) {
		nuxtApp.$emit('change_left_logo', {
			key: 'shop_logo',
			path: shopData.value?.logo?.path?.length
				? (domainImage + shopData.value?.logo?.path)
				: '',
			style: shopData.value?.logo?.style,
			title: shopData.value?.name
		})
	}
	else {
		nuxtApp.$emit('change_left_logo', null)
	}
	setChatToShopPositionToBottomRight();
}
</script>
<template>
	<RenderCacheable :cache-key="[locale, dataId].join('--')" class="public-container" id="shop_content_container_1"
		:style="{
			fontFamily: props?.shopStyle?.font,
			'--temp-color-1': props?.shopStyle?._color_1,
			'--temp-color-2': props?.shopStyle?._color_2,
			'--temp-color-3': props?.shopStyle?._color_3,
			'--temp-color-4': props?.shopStyle?._color_4,
			'--temp-color-5': props?.shopStyle?._color_5,
			'--font-resize': `${props?.shopStyle?._font_resize ?? 0}px` ,
			'--back-image': `url(${props.shopStyle?.background && !props.shopStyle?.use_default_backgrond
				? (domainImage + props.shopStyle?.background) 
				: shop_bg
			})`
		}" v-on:scroll="containerScrolling">

		<!-- <div class="scroll-delay-box" :style="{
			'background-image': shopData?.banner?.path
				? `url(${domainImage + shopData.banner.path})`
				: `url(${shop_banner_temp})`,
			transform: `translateY(${scrollPosition * .2}px)`
		}"> -->

		<!-- </div> -->
		<div class="shop-v2-t1-container">
			<v-parallax v-if="shopData?.id" class="banner-delay-box" :src="
				props.shopStyle?.banner && !props.shopStyle?.use_shop_banner
				? (domainImage + props.shopStyle?.banner)
				: shopData?.banner?.path
					? `${domainImage + shopData.banner.path}`
					: `${shop_banner_temp}`"></v-parallax>
			<!-- <HeaderV2Component :header_level_2_id="'search_input_containersearch_input_container'" :last_element_id="null">
			</HeaderV2Component> -->

			<div class="search-input-container" id="search_input_container" v-if=false>
				<!-- <button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="solar:round-alt-arrow-left-linear"></Icon>
				</button> -->
				<div class="search-input-group">
					<button class="search-button">
						<Icon name="solar:rounded-magnifer-outline"></Icon>
					</button>
					<input type="search" name='search-text'
						:placeholder="shopData?.name ?? $t('ShopComponent.tim_san_pham')" autoComplete='off'
						:maxlength="appConst.max_text_short" :value="search_text" v-on:input="($event: any) => {
							search_text = $event.target.value;
							filterProductLoading = true;
							searchProduct()
						}" />
					<button class="clear-button" v-show="search_text?.length > 0" v-on:click="() => {
						search_text = '';
						filterProductLoading = true;
						searchProduct()
					}">
						<Icon name="iconamoon:sign-times" size="25" />
					</button>

				</div>
				<!-- <nuxt-link :to="appRoute.CartComponent" class="cart-button">
					<Icon name=solar:cart-3-bold></Icon>
					<div class="count" v-if="cartData?.length > 0">{{ cartData.length < 10 ? cartData.length : "9+"
							}}</div>
				</nuxt-link> -->
			</div>

			<div class="shop-v2-content-container" id='shop-container' v-if="shopData?.id">
				<div class='v-stack shop-content-container'>
					<!-- <ShopBannerComponent id="shop_banner"
						:imgSrc="(shopData && shopData?.banner) ? (domainImage + shopData?.banner?.path) : null"
						:imgStyle="shopData?.banner?.style" v-on:img_click="() => {
							if (shopData?.banner?.path?.length) {
								let itemImg = {
									path: shopData?.banner?.path,
									title: shopData?.name,
								}
								listObjectViewer = [itemImg];
								indexActive = 0;
								showImageViewerModal = true;
							}
						}"></ShopBannerComponent> -->

					<div class="categories-container" id="categories_container">
						<div class="logo-in-categories-carousel">
							<AvatarComponent class="shop-logo" :imgTitle="shopData?.name"
								:imgStyle="shopData?.logo?.style" :imgSrc="shopData?.logo?.path?.length
									? (domainImage + shopData?.logo?.path)
									: ''" :width="50" :height="50" />
						</div>


						<Swiper class="my-carousel categories-carousel" :modules="[SwiperFreeMode, SwiperNavigation]"
							:navigation="true" :freeMode="true" :centered-slides="true" :centered-slides-bounds="true"
							:slides-per-view="'auto'" :slides-per-group-auto="true" :loop="false" :spaceBetween="30"
							:effect="'creative'" :autoplay="false" key="category-carousel" @init="(e: any) => {
								categoryCarousel = e;
							}">
							<SwiperSlide class="category-item-slide"
								v-for="(itemCategory, indexTab) of dataShopCategories" v-on:click="async () => {
									await scrollToCategory(itemCategory.id)
									// filterCategory = itemCategory.id;
								}" :class="checkCategoryFilter(itemCategory.id) ? 'active' : ''" :value="itemCategory.id"
								:key="itemCategory.id" :id="'tab_' + itemCategory.id">
								<div class="tab-title">
									<span class='name'>
										{{ showTranslateProductName(itemCategory) }}
									</span>
								</div>
							</SwiperSlide>
						</Swiper>
						<div class="search-input-group-in-cs" :class="{
							'hide': !showSearchBar
						}">
							<div class="search-input-group">
								<v-btn class="search-button" variant="text" :loading="filterProductLoading" v-on:click="() => {
									searchProduct()
								}">
									<Icon :name="'solar:rounded-magnifer-outline'"></Icon>
								</v-btn>
								<input type="search" name='search-text'
									:placeholder="shopData?.name ?? $t('ShopComponent.tim_san_pham')" autoComplete='off'
									:maxlength="appConst.max_text_short" :value="search_text" v-on:input="($event: any) => {
										search_text = $event.target.value;
										// searchProduct()
									}" />
								<button class="clear-button" v-show="search_text?.length > 0" v-on:click="() => {
									search_text = '';
									// searchProduct()
								}">
									<Icon name="iconamoon:sign-times" size="25" />
								</button>

							</div>
						</div>


						<div class="search-in-categories-carousel">
							<v-btn class="search-in-cs-button" variant="text" v-on:click="() => {
								showSearchBar = !showSearchBar
							}">
								<Icon
									:name="showSearchBar ? 'icon-park-outline:to-right' : 'solar:rounded-magnifer-outline'">
								</Icon>
							</v-btn>
						</div>
					</div>

					<div class="shop-detail-container">
						<AvatarComponent class="shop-logo" :imgTitle="shopData?.name" :imgStyle="shopData?.logo?.style"
							:imgSrc="shopData?.logo?.path?.length
								? (domainImage + shopData?.logo?.path)
								: ''" :width="100" :height="100" v-on:click="() => {
									// if (shopData?.logo?.path?.length) {
									// 	let itemImg = {
									// 		path: shopData?.logo?.path,
									// 		title: shopData.name,
									// 	}
									// 	listObjectViewer = [itemImg];
									// 	indexActive = 0;
									// 	showImageViewerModal = true;
									// }

								}" />
						<div class="shop-detail-content">
							<div class="name-share">
								<nuxt-link :to="appRoute.ShopInfoComponent.replaceAll(':id', shop_id)" class="name">
									<span>{{ shopData?.name }}</span>
									<!-- <Icon name="solar:alt-arrow-right-linear"></Icon> -->
								</nuxt-link>

							</div>

							<span class="follows">
								<em>{{ shopData?.follows ?? 0 }}</em> {{ $t('ShopComponent.nguoi_theo_doi') }}
							</span>

							<span class="is-open" v-if="shopData?.settings?.general" :class="{
								'closed': !shopData?.settings?.general?.is_open?.value
							}">
								<em v-if="shopData?.settings?.general?.is_open.value">
									{{ $t('ShopComponent.dang_mo_cua') }}
								</em>
								<em v-else class="close">
									<span :class="{
										'has-reason': shopData?.settings?.general?.is_open?.reason && (
											shopData?.settings?.general?.is_open?.reason?.[locale]?.length
											|| shopData?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
										)
									}">{{ $t('ShopComponent.da_dong_cua') }}</span>

									<span class="reason" v-if="shopData?.settings?.general?.is_open?.reason && (
										shopData?.settings?.general?.is_open?.reason?.[locale]?.length
										|| shopData?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
									)">{{
										shopData?.settings?.general?.is_open?.reason?.[locale]
										?? shopData?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]
									}}</span>
								</em>
							</span>
							<span class="is-open" v-else>
								<em>{{ $t('ShopComponent.dang_mo_cua') }}</em>
							</span>

							<div class="actions">
								<!-- <nuxt-link class="action-button call" :to="`tel:${validPhone(shopData?.phone)}`"
									:target="'_blank'" :title="$t('ShopComponent.goi')" v-on:click="() => {
										callToShop()
									}">
									<Icon name="solar:phone-rounded-bold"></Icon>
									{{ $t('ShopComponent.goi') }}
								</nuxt-link> -->
								<v-btn class="action-button direction" variant="text"
									:title="$t('ShopComponent.chi_duong')" v-on:click="() => {
										showDirection = true
									}">
									<Icon name="solar:point-on-map-bold"></Icon>
									{{ $t('ShopComponent.chi_duong') }}
								</v-btn>

								<v-btn class="action-button follow" :variant="is_followed ? 'flat' : 'outlined'"
									:class="{ 'followed': is_followed }" :title="$t('ShopComponent.theo_doi')"
									v-on:click="() => {
										// chatToShop()
										followShop()
									}">
									<Icon :name="!is_followed ? 'solar:add-circle-bold' : 'solar:check-circle-bold'">
									</Icon>
									<span v-if="!is_followed">{{ $t('ShopComponent.theo_doi') }}</span>
									<span v-else>{{ $t('ShopComponent.da_theo_doi') }}</span>

								</v-btn>

								<v-btn class="action-button share" variant="tonal" v-on:click="() => {
									share()
								}">
									<Icon name="solar:share-linear"></Icon>
									<span>{{ $t('ShopComponent.chia_se') }}</span>
								</v-btn>
							</div>


						</div>
					</div>
					<div class="slogan" v-if="shopData.settings?.general?.message?.greeting?.[locale]">
						<Icon name="game-icons:beveled-star"></Icon>
						<span class="text">{{ shopData.settings?.general?.message?.greeting?.[locale] }}</span>
						<Icon name="game-icons:beveled-star"></Icon>
					</div>

					<div class="advanced-detail">
						<div class="shop-detail-open-time" v-if="open_time_list.length > 0">
							<div class="label">
								<span>{{ $t('ShopComponent.gio_mo_cua') }}</span>
								<Icon name="solar:clock-square-linear"></Icon>
							</div>
							<div class="line"></div>
							<div class="list-open-time">
								<div class="item-open-time" v-for="item in open_time_list">
									<span class="days">{{ item.days }}</span>
									<span class="times">{{ item.times }}</span>
								</div>
							</div>
						</div>

						<div class="shop-detail-notification"
							v-if="shopData.settings?.general?.message?.notification?.[locale]">
							<div class="label">
								<Icon name="fxemoji:loudspeaker"></Icon>
								<span>{{ $t('ShopComponent.thong_bao') }}</span>
							</div>
							<div class="line"></div>
							<div class="notification-content"
								v-html="shopData.settings?.general?.message?.notification?.[locale]">
							</div>
						</div>

						<div class="shop-detail-delivery-promotion"
							v-if="shopData.settings?.order?.delivery?.length > 0">
							<div class="label">
								<Icon name="hugeicons:discount-tag-02"></Icon>
								<span>{{ $t('ShopComponent.khuyen_mai') }}</span>
							</div>
							<div class="line"></div>
							<div class="list-delivery-promotion">
								<Swiper class="my-carousel delivery-promotion-carousel" :modules="[SwiperFreeMode]"
									:navigation="false" :freeMode="true" :slides-per-view="'auto'"
									:slides-per-group-auto="true" :loop="false" :spaceBetween="10" :effect="'creative'"
									:autoplay="false" key="category-carousel" @init="(e: any) => {
										promotionCarousel = e;
									}">
									<SwiperSlide class="delivery-promotion-item-slide"
										v-for="(itemPromotion, indexPromotion) in shopData.settings?.order?.delivery?.filter((e: any) => e.enabled)"
										:value="`${itemPromotion.id}_${indexPromotion}`"
										:key="`${itemPromotion.id}_${indexPromotion}`"
										:id="`item_promotion_${itemPromotion.id}_${indexPromotion}`">
										<div class="value-tag">
											<span>-{{ itemPromotion?.type == 'percent' ? `${itemPromotion.value}%` :
												formatNumberToShort(itemPromotion.value) }}</span>
											<img :src="discount_tag" loading="lazy">
										</div>
										<div class="promotion-detail">
											<span class="name">{{
												itemPromotion.name?.[locale]?.length ? itemPromotion.name?.[locale]
													: itemPromotion.name?.vi?.length ? itemPromotion.name.vi
														: checkFirstValueNotEmpty(itemPromotion.name) }}</span>
											<span class="description">{{
												itemPromotion.description?.[locale]?.length ?
													itemPromotion.description?.[locale]
													: itemPromotion.description?.vi?.length ? itemPromotion.description.vi
														: checkFirstValueNotEmpty(itemPromotion.description) }}</span>
											<span class="max-value" v-if="itemPromotion.max_value">{{
												$t('ShopComponent.toi_da') }} <em>{{
													formatCurrency(itemPromotion.max_value
														?? 0) }}</em></span>
											<button class="condition-action" v-on:click="() => {
												selectedPromotion = JSON.parse(JSON.stringify(itemPromotion));
												showPromotionCondition = true;
											}">
												{{ $t('ShopComponent.xem_chi_tiet') }}
											</button>
										</div>

									</SwiperSlide>
								</Swiper>
							</div>
						</div>
					</div>


					<div class="shop-products-container sale-off" v-show="dataShopProductsSaleOff?.length">
						<div class="title-stack uppercase">
							<span>{{ $t('ShopComponent.giam_gia') }}</span>
						</div>
						<div class="products-container">
							<div class="container">
								<Swiper v-if="dataShopProductsSaleOff?.length && !refreshing"
									class="my-carousel stack-carousel product-carousel"
									:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]"
									:slides-per-view="2"
									:breakpoints="{
										576: {
											slidesPerView: 2
										},
										768: {
											slidesPerView: 3
										},
										992: {
											slidesPerView: 4
										},
										1200: {
											slidesPerView: 4
										}
									}"
									:centered-slides="false" :centered-slides-bounds="true"
									:space-between="10" :loop="false" :effect="'creative'" :navigation="true"
									:freeMode=false :autoplay="false" key="sale-off-carousel">
									<SwiperSlide class="product-item-container-grid-carousel"
										v-for="(itemProduct, indexSaleOff) of dataShopProductsSaleOff"
										:key="'sale_off_' + itemProduct.id">
										<ItemProductGridComponent class="swiper-slide w-100"
											:product-data="JSON.parse(JSON.stringify(itemProduct))"
											:shop-data="JSON.parse(JSON.stringify(shopData))"
											:product-index="indexSaleOff" :animation="true" v-on:add_to_cart_click="() => {
												selectedProduct = JSON.parse(JSON.stringify(itemProduct));
												selectedProduct.shop = JSON.parse(JSON.stringify(shopData));
												showSelectedProduct = true;
											}" :view_mode="'grid'">
											<template v-slot:top_left_tag>
												<img :src="hot_sale" class="top-left-tag" />
											</template>
										</ItemProductGridComponent>
									</SwiperSlide>


								</Swiper>
								<div class='v-stack empty-list'
									v-if="(!dataShopProductsSaleOff || !dataShopProductsSaleOff.length) && !refreshing">
									<img loading="lazy" :src='list_empty' :placeholder="list_empty"
										class='empty-list-image' />

									<span>
										{{ $t('ShopComponent.khong_tim_thay_san_pham_nao') }}
									</span>
								</div>
							</div>

						</div>
						<v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained
							content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
							<Icon name="eos-icons:loading"></Icon>
						</v-overlay>
					</div>

					<div class="shop-products-container sale-off" v-show="dataShopProductsFeature?.length">
						<div class="title-stack uppercase">
							<span>{{ $t('ShopComponent.noi_bat') }}</span>
						</div>
						<div class="products-container">
							<div class="container">
								<Swiper v-if="dataShopProductsFeature?.length && !refreshing"
									class="my-carousel stack-carousel product-carousel"
									:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]"
									:slides-per-view="2"
									:breakpoints="{
										576: {
											slidesPerView: 2
										},
										768: {
											slidesPerView: 3
										},
										992: {
											slidesPerView: 4
										},
										1200: {
											slidesPerView: 4
										}
									}" :centered-slides="false" :centered-slides-bounds="true"
									:space-between="10" :loop="false" :effect="'creative'" :navigation="true"
									:freeMode=false :autoplay="false" key="feature-carousel">
									<SwiperSlide class="product-item-container-grid-carousel"
										v-for="(itemProduct, indexSaleOff) of dataShopProductsFeature"
										:key="'feature_' + itemProduct.id">
										<ItemProductGridComponent class="swiper-slide"
											:product-data="JSON.parse(JSON.stringify(itemProduct))"
											:shop-data="JSON.parse(JSON.stringify(shopData))"
											:product-index="indexSaleOff" :animation="true" v-on:add_to_cart_click="() => {
												selectedProduct = JSON.parse(JSON.stringify(itemProduct));
												selectedProduct.shop = JSON.parse(JSON.stringify(shopData));
												showSelectedProduct = true;
											}" :view_mode="'grid'">
											<template v-slot:top_left_tag
												v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
												<img :src="hot_sale" class="top-left-tag" />
											</template>
										</ItemProductGridComponent>
									</SwiperSlide>


								</Swiper>
								<div class='v-stack empty-list'
									v-if="(!dataShopProductsFeature || !dataShopProductsFeature.length) && !refreshing">
									<img loading="lazy" :src='list_empty' :placeholder="list_empty"
										class='empty-list-image' />

									<span>
										{{ $t('ShopComponent.khong_tim_thay_san_pham_nao') }}
									</span>
								</div>
							</div>

						</div>
						<v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained
							content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
							<Icon name="eos-icons:loading"></Icon>
						</v-overlay>
					</div>

					<div class="shop-products-container" v-if="dataShopCategories?.length || refreshing">

						<!-- <div class="line"></div> -->

						<div class="products-container">

							<ItemCategoryProductComponent class="category-products-container"
								v-for="(itemCategory, indexCategory) in dataShopCategories"
								:view_mode="showGrid ? 'grid' : 'list'"
								:category-data="JSON.parse(JSON.stringify(itemCategory))"
								:shop-data="JSON.parse(JSON.stringify(shopData))" :category-index="indexCategory"
								:key="`category_products_${itemCategory.id || 'all'}`"
								:id="`category_products_${itemCategory.id}`" v-on:add_to_cart_click="(itemProduct: any) => {
									selectedProduct = JSON.parse(JSON.stringify(itemProduct));
									selectedProduct.shop = JSON.parse(JSON.stringify(shopData));
									showSelectedProduct = true;
								}">
								<template v-slot:top_header>
									<div class="grid-list-view">
										<!-- <span>
											{{ $t('ShopComponent.che_do_hien_thi') }}
										</span> -->
										<div class="type-view-buttons">
											<button class="type-view-button list" :class="{ 'active': !showGrid }"
												v-on:click="() => { showGrid = false; }">
												<Icon name="solar:server-minimalistic-linear"></Icon>
											</button>
											<button class="type-view-button grid" :class="{ 'active': showGrid }"
												v-on:click="() => { showGrid = true; }">
												<Icon name="solar:widget-linear"></Icon>
											</button>
										</div>
									</div>
								</template>
							</ItemCategoryProductComponent>
						</div>
						<!-- <v-overlay v-model="loadMore" :z-index="100" :absolute="false" contained
							content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
							<Icon name="eos-icons:loading"></Icon>
						</v-overlay> -->
					</div>

					<div class="shop-footer">
						<v-btn class="call-to-shop" varian="elevated">
							<nuxt-link :to="`tel:${validPhone(shopData?.phone)}`" :target="'_blank'"
								:title="$t('ShopComponent.goi_dien_cho_shop')" v-on:click="() => {
									callToShop()
								}">
								<Icon name="solar:phone-calling-bold"></Icon>
								<span>{{ $t('ShopComponent.goi_dien_cho_shop') }}</span>
							</nuxt-link>
						</v-btn>

						<v-btn class="chat-to-shop" variant="elevated" v-on:click="() => {
							chatToShop()
						}">
							<Icon name="solar:chat-round-dots-bold"></Icon>
							<span>{{ $t('ShopComponent.chat_voi_shop') }}</span>
						</v-btn>
					</div>
				</div>

			</div>
			<div class='v-stack loading-skeleton' v-else-if="refreshing">
				<v-skeleton-loader class="banner-skeleton"></v-skeleton-loader>

				<div class='h-stack'>
					<v-skeleton-loader class='avt-skeleton' />
					<div class="v-stack info-placeholder">
						<v-skeleton-loader type="list-item-two-line" class="info-skeleton" />
						<div class="actions-skeleton">
							<v-skeleton-loader class="action-skeleton"></v-skeleton-loader>
							<v-skeleton-loader class="action-skeleton" />
						</div>
					</div>
				</div>


			</div>
			<PageNotFound v-else-if="isErrored"
				:title="$t('ShopComponent.cua_hang_khong_ton_tai_hoac_ngung_hoat_dong')" />

			<div id='last_of_list'></div>
			<!-- <client-only>
				<button ref="chat_to_shop_drag_button" class="chat-to-shop-button"
					:style="{ top: `${position.y}px`, left: `${position.x}px` }">
					<Icon name="solar:chat-round-dots-bold"></Icon>
				</button>
			</client-only> -->

		</div>

		<span class="shop-actions-speed-dial" v-show="shopData?.id">
			<v-speed-dial location="left center" transition="none" contained>
				<template v-slot:activator="{ props: activatorProps }">
					<v-btn v-bind="activatorProps" class="shop-actions-button">
						<Icon name="mdi:phone-message"></Icon>
					</v-btn>
				</template>
				<v-btn class="call-to-shop" :title="$t('ShopComponent.goi_dien_cho_shop')">
					<nuxt-link :to="`tel:${validPhone(shopData?.phone)}`" :target="'_blank'"
						:title="$t('ShopComponent.goi_dien_cho_shop')" v-on:click="() => {
							callToShop()
						}">
						<Icon name="solar:phone-calling-bold"></Icon>
						<!-- <span>{{ $t('ShopComponent.goi_dien_cho_shop') }}</span> -->
					</nuxt-link>
				</v-btn>

				<v-btn class="chat-to-shop" :title="$t('ShopComponent.chat_voi_shop')" v-on:click="() => {
					chatToShop()
				}">
					<Icon name="solar:chat-round-dots-bold"></Icon>
					<!-- <span>{{ $t('ShopComponent.chat_voi_shop') }}</span> -->
				</v-btn>
			</v-speed-dial>
		</span>

		<button class="scroll-top-button">
			<v-btn class="go-up" v-show="enableScrollTopBtn" v-on:click="() => {
				scrollToTop()
			}">
				<Icon name="ic:round-arrow-upward"></Icon>
			</v-btn>
		</button>



		<v-overlay v-model="showDirection" location="bottom" :absolute="true" :z-index="10000" key="show_direction"
			class="shop-direction-v2-overlay-container" persistent content-class='shop-direction-container'
			no-click-animation>
			<DirectionV2Component :latitude_destination="parseFloat(shopData?.latitude)"
				:longitude_destination="parseFloat(shopData?.longitude)" :logo="shopData?.logo" v-on:close="() => {
					showDirection = false;
				}"></DirectionV2Component>
		</v-overlay>
		<AddProductToCartComponent v-if="showSelectedProduct" v-on:close="() => {
			showSelectedProduct = false
		}" :selectedProduct="selectedProduct">
		</AddProductToCartComponent>
		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>
		<v-overlay v-model="showChatToShop" :z-index="100" :absolute="false" :close-on-back="true" contained
			key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
			no-click-animation v-on:click:outside="() => {
				showChatToShop = false;
			}">
			<ChatDetailComponent v-if="showChatToShop" :mode="member_type.user" :receiver_id="shopData?.id"
				:chat_info="chatToShopInfo" :receiver_type="true" v-on:close="() => {
					showChatToShop = false;
				}"></ChatDetailComponent>
		</v-overlay>
		<v-overlay v-model="showPromotionCondition" :z-index="100" :absolute="false" :close-on-back="true"
			key="show_promotion_condition" class="my-overlay-container"
			content-class='promotion-content-modal-container' no-click-animation v-on:click:outside="() => {
				showPromotionCondition = false;
			}">
			<div class="header-promotion">
				<div class="value-tag">
					<span>{{ selectedPromotion?.type == 'percent' ? `${selectedPromotion?.value}%` :
						formatNumberToShort(selectedPromotion?.value ?? 0) }}</span>
					<img :src="discount_tag" loading="lazy">
				</div>
				<div class="promotion-detail">
					<span class="name">{{
						selectedPromotion?.name?.[locale]?.length ? selectedPromotion?.name?.[locale]
							: selectedPromotion?.name?.vi?.length ? selectedPromotion?.name.vi
								: checkFirstValueNotEmpty(selectedPromotion?.name) }}</span>
					<span class="value" v-html="$t('ShopComponent.giam_x_phi_giao_hang', {
						x: ':x'
					}).replaceAll(':x', `<em>${selectedPromotion?.type == 'percent' ? `${selectedPromotion?.value}%` :
						(formatCurrency(selectedPromotion?.value ?? 0))}</em>`)"></span>

				</div>
				<button class="close-button" v-on:click="() => {
					showPromotionCondition = false;
					selectedPromotion = null;

				}">
					<Icon name="lets-icons:close-ring-duotone"></Icon>
				</button>
			</div>
			<div class="promotion-content">
				<div class="promotion-info" v-if="selectedPromotion?.description">
					<label>{{ $t('ShopComponent.chi_tiet') }}</label>
					<div>
						<span class="description">{{
							selectedPromotion?.description?.[locale]?.length ?
								selectedPromotion?.description?.[locale]
								: selectedPromotion?.description?.vi?.length ? selectedPromotion?.description.vi
									: checkFirstValueNotEmpty(selectedPromotion?.description) }}</span>
					</div>

				</div>
				<div class="promotion-info h-stack" v-if="selectedPromotion?.max_value">
					<label>{{ $t('ShopComponent.giam_toi_da') }}:</label>
					<div>
						<span class="value-item">{{ formatCurrency(selectedPromotion?.max_value ?? 0) }}</span>
					</div>
				</div>
				<div class="promotion-condition-list">
					<label>
						<span>{{ $t('ShopComponent.dieu_kien') }} </span>
						<em
							v-if="selectedPromotion?.relationship_condition == condition_relationship.and && selectedPromotion?.conditions?.length > 1">({{
								$t('ShopComponent.thoa_man_tat_ca_dieu_kien') }})</em>
						<em
							v-if="selectedPromotion?.relationship_condition == condition_relationship.or && selectedPromotion?.conditions?.length > 1">({{
								$t('ShopComponent.thoa_man_mot_trong_cac_dieu_kien') }})</em>
					</label>
					<div class="promotion-condition-item" v-if="selectedPromotion?.conditions?.length > 0"
						v-for="(itemCondition, index) in selectedPromotion?.conditions">
						<!-- <div class="relationship" v-if="index > 0">
							{{ $t(`ShopComponent.${selectedPromotion?.relationship_condition?.toLowerCase()}`) }}
						</div> -->
						<Icon name="stash:circle-dot-duotone"></Icon>
						<div class="condition-text">
							<span class="param-item">{{ itemCondition.param == 'price'
								? $t('ShopComponent.gia_tri_don_hang')
								: itemCondition.param == 'delivery_distance'
									? $t('ShopComponent.khoang_cach_giao')
									: itemCondition.param == 'province_id' ? $t('ShopComponent.tinh_thanh_pho') : '' }}
							</span>
							<span class="operator-item">{{ $t(`ShopComponent.${itemCondition.operator}`) }}</span>
							<span class="value-item"
								v-if="itemCondition.param == 'price' || itemCondition.param == 'delivery_distance'">{{
									formatNumber(itemCondition.value) }} {{ itemCondition.unit
								}}</span>
							<span class="value-item" v-if="itemCondition.param == 'province_id'">{{
								itemCondition.raw_value.map((e: any) => e.name).join(', ')
							}}</span>

						</div>

					</div>
					<div class="empty-conditions" v-else>
						{{ $t('ShopComponent.khong_can_dieu_kien') }}
					</div>
				</div>
			</div>

		</v-overlay>
	</RenderCacheable>

</template>

<style lang="scss" src="./ShopV2T1Styles.scss"></style>
