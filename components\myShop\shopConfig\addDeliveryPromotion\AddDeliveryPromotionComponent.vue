<template>
    <VueFinalModal class="my-modal-container" :modal-id="'add_delivery_promotion_modal'" :keep-overlay="true" :overlay-behavior="'persist'"
        :hide-overlay="false" :hide-overlay-on-blur="false" content-class="v-stack form-modal add-delivery-promotion-container"
        :click-to-close="false" :esc-to-close="false" v-on:before-close="() => {
            showAddDeliveryPromotionModal = false
        }" v-model="showAddDeliveryPromotionModal" contentTransition="vfm-slide-down">
        <SubHeaderV2Component :title="$t('AddDeliveryPromotionComponent.them_khuyen_mai_phi_ship')">
            <template v-slot:header_left></template>
        </SubHeaderV2Component>
        <div class="add-delivery-promotion-content-container">
            <VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
                class="add-delivery-promotion-content">
                <div class="form-field-container">
                    <label class="required" for="name_input">{{
                        $t('AddDeliveryPromotionComponent.ten_khuyen_mai') }}</label>
                    <div class="name-language">
                        <button class="lang-button" v-for="item in locales" type="button" :title="`${$t('AddDeliveryPromotionComponent.ten_khuyen_mai')} ${ISO6391.getName(item.code)}: ${delivery_propmotion_data?.name?.[item.code]?.length
                            ? (delivery_propmotion_data?.name?.[item.code]?.length > 10 ? delivery_propmotion_data?.name?.[item.code].slice(0, 10).concat('...') : delivery_propmotion_data?.name?.[item.code])
                            : $t('AddDeliveryPromotionComponent.trong')}`" :class="{
                                'active': selectLanguage == item.code,
                                'filled': delivery_propmotion_data?.name?.[item.code]?.length
                            }" v-on:click="() => {
                                selectLanguage = item.code;
                            }">
                            {{ ISO6391.getNativeName(item.code) }}
                            <Icon name="mdi:edit-circle" class="filled"
                                v-if="delivery_propmotion_data?.name?.[item.code]?.length"></Icon>
                        </button>
                    </div>
                    <Field class="custom-input" :validate-on-input="true" :name="`name_locale`" :id="`name_input`"
                        :placeholder="`${$t('AddDeliveryPromotionComponent.ten_khuyen_mai')} ${ISO6391.getName(selectLanguage)}`"
                        v-bind:model-value="delivery_propmotion_data?.name[selectLanguage]" v-on:update:model-value="($event: any) => {
                            delivery_propmotion_data.name[selectLanguage] = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="name"></ErrorMessage>
                </div>
                <div class="form-field-container">
                    <label class="required" for="value_input">{{ $t('AddDeliveryPromotionComponent.gia_tri') }}</label>
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t('AddDeliveryPromotionComponent.gia_tri')" class="custom-input"
                            :validate-on-input="true" name="value" type="number" id="value_input"
                            v-bind:model-value="delivery_propmotion_data?.value" v-on:update:model-value="($event: any) => {
                                delivery_propmotion_data.value = $event;
                            }"></Field>
                        <div class="type-button-group">
                            <button class="type-promotion-option" v-on:click="() => {
                                delivery_propmotion_data.type = 'percent';
                            }" :class="{ 'active': delivery_propmotion_data.type == 'percent' }">%</button>
                            <button class="type-promotion-option" v-on:click="() => {
                                delivery_propmotion_data.type = 'money';
                                if (delivery_propmotion_data.value && delivery_propmotion_data.value > 100) {
                                    delivery_propmotion_data.value = 100;
                                }
                            }" :class="{ 'active': delivery_propmotion_data.type == 'money' }">VNĐ</button>
                        </div>
                    </div>
                    <span class="price-text"
                        v-if="delivery_propmotion_data.value && delivery_propmotion_data.type == 'money'">{{
                            formatCurrency(delivery_propmotion_data.value) }}</span>
                    <ErrorMessage class="error-message" name="value"></ErrorMessage>
                </div>

                <div class="form-field-container">
                    <label class="optional" for="value_input">{{ $t('AddDeliveryPromotionComponent.gia_tri_toi_da')
                        }}</label>
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t('AddDeliveryPromotionComponent.gia_tri_toi_da')" class="custom-input"
                            :validate-on-input="true" name="max_value" type="number" id="max_value_input"
                            v-bind:model-value="delivery_propmotion_data?.max_value" v-on:update:model-value="($event: any) => {
                                delivery_propmotion_data.max_value = $event;
                            }"></Field>
                    </div>
                    <span class="price-text" v-if="delivery_propmotion_data.max_value">{{
                        formatCurrency(delivery_propmotion_data.max_value) }}</span>
                    <ErrorMessage class="error-message" name="max_value"></ErrorMessage>
                </div>

                <div class="form-field-container">
                    <label class="optional" for="name_input">{{
                        $t('AddDeliveryPromotionComponent.mo_ta_khuyen_mai') }}</label>

                    <Field class="custom-input" :validate-on-input="true" name="description" id="description_input"
                        as="textarea" rows="3" resize="none"
                        :placeholder="`${$t('AddDeliveryPromotionComponent.mo_ta_khuyen_mai')} ${ISO6391.getName(selectLanguage)}`"
                        v-bind:model-value="delivery_propmotion_data?.description[selectLanguage]"
                        v-on:update:model-value="($event: any) => {
                            delivery_propmotion_data.description[selectLanguage] = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="description"></ErrorMessage>
                </div>
                <div class="form-field-container">
                    <div class="h-stack field-header">
                        <label class="optional" for="condition">{{
                            $t('AddDeliveryPromotionComponent.quan_he_giua_cac_dieu_kien') }}</label>
                    </div>
                    <div class="h-stack field-value">
                        <button class="relationship-option" :class="{
                            'active': delivery_propmotion_data.relationship_condition == condition
                        }" v-on:click="() => {
                            delivery_propmotion_data.relationship_condition = condition;
                        }" v-for="condition in condition_relationship">{{
                            $t(`AddDeliveryPromotionComponent.${condition.toLowerCase()}`) }}</button>
                    </div>
                </div>
                <div class="form-field-container">
                    <div class="h-stack field-header">
                        <label class="optional" for="condition">{{
                            $t('AddDeliveryPromotionComponent.dieu_kien') }}</label>
                        <button class="add-condition" v-on:click="() => {
                            delivery_propmotion_data?.conditions?.push({
                                id: delivery_propmotion_data?.conditions.length,
                                param: condition_params[0].param,
                                operator: 'equal_to',
                                value: '',
                                unit: condition_params[0].unit,
                                input_type: condition_params[0].input_type,
                                raw_value: ''
                            });
                        }">
                            <Icon name="iconamoon:sign-plus-circle-light"></Icon> {{
                                $t('AddDeliveryPromotionComponent.them_dieu_kien') }}
                        </button>
                    </div>

                    <div class="list-conditions-container" v-if="delivery_propmotion_data?.conditions?.length">
                        <div class="h-stack condition-item-container"
                            v-for="(condition, index) in delivery_propmotion_data.conditions"
                            :key="`condition_item_${index}`">
                            <div class="relationship-condition-tag" v-if="index > 0">{{
                                $t(`AddDeliveryPromotionComponent.${delivery_propmotion_data.relationship_condition.toLowerCase()}`)
                            }}</div>
                            <v-select class="custom-v-select mt-2 param-select" label="" :item-value="'param'"
                                :placeholder="$t('AddDeliveryPromotionComponent.truong')" v-model="condition.param"
                                v-on:update:modelValue="(e: any) => {
                                    console.log(e);
                                    let indexSelected = condition_params.findIndex(function (el) {
                                        return el.param == e
                                    })
                                    condition.param = e;
                                    condition.unit = condition_params?.[indexSelected != -1 ? indexSelected : 0]?.unit;
                                    condition.input_type = condition_params?.[indexSelected != -1 ? indexSelected : 0]?.input_type;
                                    condition.value = condition_params?.[indexSelected != -1 ? indexSelected : 0]?.input_type == 'string' ? '' : [];
                                    condition.operator = operators[operators.findIndex(function (e) {
                                        return e.type_enable == condition_params?.[indexSelected != -1 ? indexSelected : 0]?.input_type
                                    })].value as any;
                                }" :menu-icon="''" :items="condition_params" variant="plain">
                                <template v-slot:item="{ props, item }">
                                    <v-list-item v-bind="props"
                                        :title="$t(`AddDeliveryPromotionComponent.${item.raw.name}`)"></v-list-item>
                                </template>
                                <template v-slot:selection="{ item }">
                                    <span>{{ $t(`AddDeliveryPromotionComponent.${item.raw.name}`) }}</span>
                                </template>
                                <template v-slot:append-inner></template>
                            </v-select>

                            <v-select class="custom-v-select mt-2 operator-select" label="" :menu-props="{
                                contentClass: 'text-center'
                            }" :placeholder="$t('AddDeliveryPromotionComponent.toan_tu')"
                                v-model="condition.operator" :item-value="'value'" :menu-icon="''" :items="operators"
                                variant="plain">
                                <template v-slot:item="{ props, item }">
                                    <v-list-item v-show="item.raw.type_enable == condition.input_type"
                                        :disabled="item.raw.type_enable != condition.input_type" v-bind="props"
                                        :title="item.raw.symbol"></v-list-item>
                                </template>
                                <template v-slot:selection="{ item }">
                                    <span>{{ item.raw.symbol }}</span>
                                </template>
                                <template v-slot:append-inner></template>
                            </v-select>

                            <div class="field-value-group">
                                <div class="input-group-container type-input" v-if="condition.input_type == 'string'">
                                    <input :placeholder="$t('AddDeliveryPromotionComponent.gia_tri_dieu_kien')"
                                        class="custom-input value-input" name="`condition_${index}`" type="number"
                                        :id="`condition_${index}`" v-currency="appConst.v_currency_option"
                                        v-model="condition.value" />
                                    <span class="unit">{{ condition.unit }}</span>
                                    <span class="price-text" v-if="condition.value && condition.param == 'price'">{{
                                        formatCurrency(condition.value) }}</span>
                                </div>

                                <button class="input-group-container" v-if="condition.input_type == 'select'"
                                    v-on:click="() => {
                                        indexSelectedCondition = index;
                                        showConditionSelectValues = true;
                                    }"
                                    :title="condition.value?.length ? condition.raw_value.map((itemRaw: any) => itemRaw.name).join(', ') : $t('AddDeliveryPromotionComponent.chua_chon')">
                                    {{ condition.value?.length ? condition.raw_value.map((itemRaw: any) =>
                                        itemRaw.name).join(', ') : $t('AddDeliveryPromotionComponent.chua_chon')}}
                                </button>
                            </div>

                            <button class="remove-condition-button" v-on:click="() => {
                                delivery_propmotion_data.conditions?.splice(index, 1);
                            }">
                                <Icon name="clarity:times-line" size="20"></Icon>
                            </button>

                        </div>

                    </div>

                    <div class="list-conditions-container" v-else>
                        <div class="empty-condition">
                            {{ $t('AddDeliveryPromotionComponent.chua_co_dieu_kien') }}
                        </div>
                    </div>
                </div>


                <button hidden ref="submitFormButton" v-on:click="() => {
                    submit()
                }"></button>
            </VeeForm>

        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                {{ $t('AddDeliveryPromotionComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => {
                submitFormButton?.click();
            }">
                <span>{{ $t('AddDeliveryPromotionComponent.tao') }}</span>
            </button>
        </div>

        <CustomSelectComponent v-if="showConditionSelectValues" :_key="'select_province'"
            :select_placeholder="$t('AddDeliveryPromotionComponent.chon_tinh')" :list_item="dataProvince"
            :field_value="'id'" :field_title="'name'" :multiple="true" :searchable="true"
            :title="$t('AddDeliveryPromotionComponent.tinh_thanh_pho')"
            :class="'my-custom-select condition-provice-select'"
            :model_value="delivery_propmotion_data?.conditions?.[indexSelectedCondition].value" v-on:close="() => {
                showConditionSelectValues = false
            }" v-on:model:update="(e: any) => {
                console.log(e);
                delivery_propmotion_data.conditions[indexSelectedCondition].raw_value = dataProvince.filter((item: any) => e.includes(item.id));
                delivery_propmotion_data.conditions[indexSelectedCondition].value = e;
            }">
            <template v-slot:placeholder>
                <div class="h-stack">
                    <span>{{ $t('AddDeliveryPromotionComponent.chon_tinh') }}</span>
                    <Icon name="mdi:chevron-down"></Icon>
                </div>
            </template>
            <template v-slot:title_icon_left>
                <Icon name="solar:hamburger-menu-linear"></Icon>
            </template>
        </CustomSelectComponent>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, nonAccentVietnamese } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { DeliveryFeeConfig, type delivery_fee_condition, type delivery_fee_config, condition_params, operators, condition_relationship } from '../ShopConfigDTO';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    title: null,
    mode: null,
    shop_id: null
});
var nuxtApp = useNuxtApp();
const { t, locale, locales } = useI18n();
const availableLocales = await computed(() => {
    return locales.value.filter(i => i.code)
})

var submitFormButton = ref<HTMLElement | undefined>();
const formSchema = yup.object({
    name: yup.object().test(
        'at-least-one-locale',
        t('AddDeliveryPromotionComponent.vui_long_nhap_ten_khuyen_mai'),
        () => {
            if (
                delivery_propmotion_data.value.name?.vi
                || delivery_propmotion_data.value.name?.en
                || delivery_propmotion_data.value.name?.ko
                || delivery_propmotion_data.value.name?.ru
            ) {
                return true;
            }
            return false;
        }
    ),

    value: yup.number().typeError(t('AddDeliveryPromotionComponent.gia_tri_phai_la_kieu_so')).required(t('AddDeliveryPromotionComponent.vui_long_nhap_gia_tri_khuyen_mai')).test('max-value', t('AddDeliveryPromotionComponent.gia_tri_khuyen_mai_vuot_qua_gioi_han'), (value: any) => {
        const type = delivery_propmotion_data.value.type;
        console.log(value)
        if (type == 'percent') {
            return value <= 100 && value > 0;
        }
        else {
            return value > 0;
        }
    }),
    max_value:
        yup.number().min(0).notRequired().transform((value) => value == Number(value) ? value : null)
            .typeError(t('AddDeliveryPromotionComponent.gia_tri_phai_la_kieu_so'))
            .test('max-value-belong-to-value', t('AddDeliveryPromotionComponent.khong_vuot_qua_gia_tri_khuyen_mai'), (value: any) => {
                const type = delivery_propmotion_data.value.type;
                const currentValue = delivery_propmotion_data.value.value ?? 0;
                if (value && type == 'money') {
                    return value <= currentValue && value > 0;
                }
                else {
                    return true
                }

            }),
    type: yup.mixed().oneOf(['percent', 'money'], t('AddDeliveryPromotionComponent.loai_khuyen_mai_khong_hop_le')),
    relationship_condition: yup.mixed().oneOf([condition_relationship.and, condition_relationship.or], t('AddDeliveryPromotionComponent.quan_he_khong_hop_le')).nullable()
});

var delivery_propmotion_data = ref<delivery_fee_config>(new DeliveryFeeConfig());
var selectLanguage = ref<any>(locale.value);
var dataProvince = ref(appDataStartup.listProvince ?? []);
const { handleSubmit } = useForm({
    validationSchema: formSchema,
});

var showAddDeliveryPromotionModal = ref(false)

var showConditionSelectValues = ref(false);
var indexSelectedCondition = ref<any>(null);
onBeforeUnmount(() => {
})
onBeforeMount(() => {
    nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
        dataProvince.value = JSON.parse(JSON.stringify(appDataStartup.listProvince));
    });
})
onMounted(async () => {
    showAddDeliveryPromotionModal.value = true;
})
onUpdated(() => {
})

function close(value?: any) {
    emit('close', value);
}

async function submit() {

    var valid = await formSchema.isValid(delivery_propmotion_data.value);
    if (valid) {
        emit('submit', JSON.parse(JSON.stringify(delivery_propmotion_data.value)));
    }
    else {
        console.log('form invalid')
    }

}

</script>

<style lang="scss" src="./AddDeliveryPromotionStyles.scss"></style>