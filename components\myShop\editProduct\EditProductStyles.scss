@media screen and (max-width: 500px) {
  .childen-product-info-container {
    padding: 0 !important;
  }
}

.edit-product-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  // max-height: 90vh;
  background: #f1f2f6;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: visible;
  position: relative;

  // & > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  //   position: relative;

  //   & > button {
  //     // position: absolute;
  //     // right: 0;
  //     // top: 5px;
  //     // background-color: white;
  //     border: none;
  //     padding: 5px;
  //     border-radius: 50%;
  //     align-items: center;
  //     justify-content: center;
  //   }
  // }

  & > .edit-product-detail {
    display: flex;
    flex: 1;
    overflow: hidden auto;
    flex-direction: column;
    background: #f1f2f6;

    & > .product-content-preview {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 0;
      background-color: #f6f5fb;
      position: relative;
      // overflow: hidden auto;

      & > .product-profile-picture {
        width: 100dvw;
        max-width: 100%;
        height: 100dvw;
        // min-height: 250px;
        max-height: 300px;
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        & > img {
          object-fit: contain;
          height: inherit;
          max-height: inherit;
          width: inherit;
          max-width: inherit;
        }

        & > .title-header {
          position: absolute;
          left: 0;
          justify-content: space-between;
          top: 15%;
          width: 100%;
          padding: 0 15px;
          border: none;

          & > .header-left {
            flex: none;
            margin-right: 10px;
            > button {
              background-color: rgb(0, 0, 0, 70%);
              width: 30px;
              height: 30px;
              color: white;
              padding: 5px;
            }
          }

          & > .header-right {
            flex: none;
            margin-left: 10px;
            font-size: 1em;
            gap: 15px;
            position: relative;

            & > .cart-in-search {
              animation: none;
              width: 30px;
              height: 30px;
              color: white;
              position: relative;
              padding: 5px;
              background-color: rgb(0, 0, 0, 70%);

              & > em {
                border-radius: 2em;
                color: white;
                background: var(--color-button-error);
                min-width: 15px;
                width: fit-content;
                height: 15px;
                padding: 2px;
                font-size: 0.8em;
                display: flex;
                align-items: center;
                justify-content: center;
                font-style: normal;
                position: absolute;
                bottom: -3px;
                right: -3px;
                line-height: 1;
                font-weight: 500;

                & > span {
                  font-size: 0.8em;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }

            .share-button {
              animation: none;
              width: 30px;
              height: 30px;
              font-size: 20px;
              color: white;
              position: relative;
              padding: 5px;
              background-color: rgb(0, 0, 0, 70%);
            }
          }
        }
      }

      & > .product-detail-stack {
        display: flex;
        padding: 10px 15px;
        flex-direction: column;
        background-color: white;

        & > .label {
          font-size: 16px;
          font-weight: bold;
          color: black;
        }
      }

      & > .product-detail-stack.primary-detail {
        padding: 15px;
        flex-direction: column;

        & > .product-name {
          font-size: 17px;
          font-weight: bold;
          color: #262628;
        }

        & > .rating-sold-amount {
          font-size: 15px;
          display: flex;
          align-items: flex-end;
          color: #7e7d83;
          & > div:first-child {
            padding-left: 0;
            border: none;
          }
          & > div:last-child {
            border: none;
            padding-right: 0;
          }
          & > div {
            padding: 0 10px;
            margin: 10px 0;
            height: 1em;
            display: flex;
            align-items: center;
            border-left: thin solid #7e7d83;
            border-right: thin solid #7e7d83;
          }

          & > .rating {
            display: flex;
            font-weight: bold;
            color: #595959;

            & > svg {
              color: #ffc107;
              font-size: 25px;
              align-self: flex-end;
              margin-bottom: -2px;
              margin-left: -5px;
            }

            & span {
              font-size: 0.7em;
              margin-top: 3px;
            }
          }
        }

        & > .flash-sale {
          display: flex;
          justify-content: space-between;
          text-transform: uppercase;
          color: #7e7d83;

          & > .flash-sale-label {
            & > svg {
              color: #ffc107;
            }
          }

          & > .expire-time {
            & > .count-down {
              font-weight: bold;
              color: #606061;
            }
          }
        }

        & > .product-price-container {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;

          & > .price-content {
            display: flex;
            gap: 15px;
            color: #29292c;

            & > .product-price {
              font-size: 25px;
              font-weight: bold;
              display: flex;
              gap: 15px;
              align-items: center;

              & > em {
                font-size: 13px;
                padding: 0 5px;
                font-style: normal;
                font-weight: 600;
                border-radius: 5px;
                background: #f6f5fb;
                text-decoration: line-through;
              }
            }
          }

          & > .sale-off-badge {
            color: white;
            font-weight: bold;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: auto;
            font-size: 15px;
            width: 50px;
            height: 40px;
            background-image: url("~/assets/image/sale_off_badge_background.png");
            background-size: 100%;
          }
        }

        & > .product-stock {
          font-size: 17px;

          & > .label {
            font-weight: 500;
          }

          & > .content {
            font-weight: 600;
            color: #00823e;
          }
        }

        & > .select-child-product {
          padding: 10px 15px;
          border: thin solid #eeedf2;
          border-radius: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 10px 0 0;
          position: relative;

          & > .selection-show {
            display: flex;
            flex-direction: column;

            & > .label {
              color: #818082;
              font-size: 15px;
              font-weight: 600;
            }

            & > .child-product-name {
              color: #262628;
              font-size: 17px;
              font-weight: bold;
            }
          }

          & > .select-child-product-button {
            color: var(--primary-color-1);
            font-size: 15px;
            font-weight: 600;
          }

          & > .children-products-select-container {
            & .dropdown-content {
              width: 100%;
              max-width: 100% !important;
              left: 0 !important;
              top: 100% !important;

              & .children-products {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
                margin-top: 10px;
                background: white;
                box-shadow: 0 0 20px rgb(0, 0, 0, 10%);
                padding: 10px 15px;
                border-radius: 15px;

                & > .item-child-product {
                  padding: 5px 20px;
                  color: var(--primary-color-1);
                  border: thin solid var(--primary-color-1);
                  border-radius: 2em;
                }
                & > .item-child-product.active {
                  color: white;
                  background: var(--primary-color-1);
                }
              }
            }
          }
        }
      }

      & > .list-notes-container {
        & > .list-notes {
          display: flex;
          flex-direction: column;
          & > .note-item {
            display: flex;
            gap: 5px;
            align-items: flex-start;
            color: #202020;
            font-weight: 500;

            & > svg {
              color: var(--primary-color-1);
              margin-top: 5px;
            }
          }
        }
      }

      & > .stack-space {
        width: 100%;
        height: 10px;
        min-height: 10px;
        background-color: #f6f5fb;
      }

      & > .product-detail-stack.categories-container {
        display: flex;
        gap: 10px;

        & > .stack-content {
          display: flex;
          flex-direction: column;
          gap: 10px;

          & > .stack-content-item {
            display: flex;
            justify-content: space-between;

            & > .label {
              flex: 0.4;
              font-size: 15px;
              color: #8b8b8d;
              font-weight: 500;
              padding-right: 10px;
            }

            & > .content {
              font-weight: 600;
              color: #222222;
              flex: 0.6;
            }
          }

          & > .stack-content-item.category > .content {
            color: var(--primary-color-1);
            font-size: 15px;
          }
        }
      }

      & > .product-detail-stack.description {
        gap: 5px;
        & > .description-container {
          position: relative;
          // margin-top: 10px;
          color: #202020;
          height: fit-content;
          font-size: 17px;
          line-height: 20px;
          overflow: hidden;
          transition: max-height 0.2s ease-in-out;

          & > span {
            height: fit-content;
            min-height: fit-content;
            padding-bottom: 10px;
            white-space: break-spaces;
          }

          & > .show-full-button {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent, white);
            display: flex;
            align-items: flex-end;
            justify-content: center;

            & > button {
              color: var(--primary-color-1);
            }
          }
        }

        & > .description-container.show-full {
          max-height: 100%;
          height: fit-content;

          & > .show-full-button {
            position: relative;
            height: fit-content;
          }
        }
      }

      & > .product-detail-stack.stack-similar {
        font-size: 15px;
        gap: 10px;

        & .product-item-container-grid {
          background: white;
          border-radius: 10px;
          padding: 5px;
          display: flex;
          flex-direction: column;
          margin-bottom: 5px;
          cursor: pointer;
          border: thin solid transparent;
          // width: 100%;
          // gap: 5px;
          // display: flex;
          // margin-bottom: 5px;
          // cursor: pointer;
          // padding: 5px 5px 0;
          // border: thin solid transparent;

          & > img {
            align-self: flex-start;
            width: 100%;
            max-height: 150px;
            height: 150px;
            aspect-ratio: 1;
            object-fit: cover;
            background: var(--color-background-2);
            border-radius: 10px;
          }

          & > .product-detail {
            align-items: flex-start;
            gap: 5px;
            flex: 1;
            padding: 5px;
            text-align: left;

            & > .product-name {
              color: #242328;
              font-weight: bold;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              overflow: hidden;
            }

            & > .h-stack {
              width: 100%;
              margin-top: auto;
              flex-wrap: wrap;
              align-items: flex-end;
              & > .product-price {
                color: #ed1b24;
                font-weight: bold;
                line-height: 1.125;
                font-size: 17px;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;

                & > em.off {
                  font-size: 0.8em;
                  font-style: normal;
                  display: flex;
                  font-weight: 500;
                  color: var(--color-text-note);
                  text-decoration: line-through;
                }
              }

              & > .sale-off-badge {
                color: white;
                font-weight: bold;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: auto;
                font-size: 0.8em;
                width: 40px;
                height: 30px;
                background-image: url("~/assets/image/sale_off_badge_background.png");
                background-size: 100% 100%;
              }
            }
            & .rating-sold-amount {
              font-size: 0.9em;
              display: flex;
              align-items: flex-end;
              color: #7e7d83;
              & > div:first-child {
                padding-left: 0;
              }
              & > div:last-child {
                border: none;
                padding-right: 0;
              }
              & > div {
                padding: 0 10px;
                margin: 10px 0;
                height: 1em;
                display: flex;
                align-items: center;
                border-right: thin solid #7e7d83;
              }

              & > .rating {
                display: flex;
                font-weight: bold;
                color: #595959;

                & > svg {
                  color: #ffc107;
                  font-size: 25px;
                  align-self: flex-end;
                  margin-bottom: -2px;
                  margin-left: -5px;
                }

                & span {
                  font-size: 0.7em;
                  margin-top: 3px;
                }
              }
            }
          }
        }

        & .product-item-container-grid:hover,
        .product-item-container-grid:active {
          background: linear-gradient(#fff7f8, #fff7f8) padding-box,
            linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;

          & > .product-detail {
            border-color: transparent;
          }
        }
      }
    }

    & > .edit-product-content-container {
      // & .product-avt-container {
      //   position: relative;
      //   display: flex;
      //   justify-content: center;
      //   width: 150px;
      //   align-items: center;
      //   margin: 10px auto;

      //   & > img {
      //     border-radius: 10px;
      //     flex: 1 1;
      //     height: 150px;
      //     width: 150px;
      //     object-fit: cover;
      //     // background-color: var(--color-background-2);
      //   }
      // }
      // & .product-avatar-actions {
      //   display: flex;
      //   gap: 15px;
      //   justify-content: center;

      //   & .select-image {
      //     // position: absolute;
      //     // top: 10px;
      //     // right: 10px;
      //     width: 25%;
      //     min-width: fit-content;
      //     white-space: nowrap;
      //     cursor: pointer;

      //     & > div {
      //       position: relative;
      //       width: 100%;
      //       font-size: 1.2em;
      //       background: white;
      //       color: #343434;
      //       height: 100%;
      //       display: flex;
      //       justify-content: center;
      //       font-weight: bold;
      //       border-radius: 7px;
      //       border: thin solid #e7e9ec;
      //       padding: 5px 15px;
      //       cursor: pointer;
      //       font-size: 15px;

      //       & input {
      //         opacity: 0;
      //         width: 100%;
      //         height: 100%;
      //         position: absolute;
      //         top: 0;
      //         left: 0;
      //         cursor: pointer;
      //         z-index: 2;
      //       }

      //       & input::file-selector-button {
      //         cursor: pointer;
      //       }
      //     }
      //   }
      // }
      & .product-image-list {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        justify-content: center;
        background: white;
        padding: 10px;
        margin: 0;
        border-radius: 10px;
        border: 3px dashed #e7e9ec;

        & > .select-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: 2px solid #e7e9ec;
          color: #e7e9ec;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          cursor: pointer;

          & > label {
            width: 100%;
            height: 100%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: -1;
            }
          }
        }
        & .selected-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: thin solid #e7e9ec;
          color: #e7e9ec;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          position: relative;
          cursor: grab;

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: inherit;
          }

          & > .action-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 20px;
            // width: 100%;
            // height: 100%;
            // display: none;
            display: flex;
            color: white;
            justify-content: flex-end;
            align-items: flex-end;
            // padding: 5px;
            gap: 5px;
            background-color: rgb(0, 0, 0, 0.5);
            border-radius: 10px 0;

            & > button {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        & .selected-image.sortable-chosen {
          cursor: grabbing;
        }
        & .drag-drop-container {
          width: 100%;
        }
        & .selected-image:hover {
          & > .action-overlay {
            display: flex;
          }
        }
      }
    }

    & .enable-content {
      justify-content: space-between;
      margin: 0;
      // gap: 10px;

      & > .product-content-edit {
        margin: 0;
        max-height: 50px;
      }
    }

    & .product-content-edit {
      margin: 10px 0;
      font-size: 15px;
      color: #2b2b2b;
      font-weight: 600;

      & > .content-header {
        display: flex;
        gap: 5px 10px;
        flex-wrap: wrap;
        justify-content: space-between;

        & > .add-category {
          display: flex;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: var(--primary-color-1);
        }
      }

      & > .checkbox-input-label {
        gap: 5px;
        display: flex;
        cursor: pointer;
        user-select: none;
        font-weight: 500;
        color: var(--primary-color-1);
        font-size: 20px;

        & span {
          font-size: 17px;
          color: var(--primary-color-1);
        }

        & em {
          color: var(--primary-color-1);
          font-size: 14px;
          line-height: normal;
          align-self: center;
        }
      }

      & .label {
        color: #262a3c;
        font-size: 15px;
      }

      & > .input-custom,
      > .text-area-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
      }
      & > .input-custom:disabled,
      > .text-area-custom:disabled {
        background: #fafafa;
      }
      & > .text-area-custom {
        height: 200px;
        resize: none;
      }
      & > .dropdown-select-container {
        background: white;
        border-radius: 7px;
        margin-top: 5px;
        padding: 10px;
        border: none;
        font-size: 17px;

        & button {
          padding: 0;
        }
      }
      & em {
        color: var(--primary-color-1);
      }

      & .note-message {
        color: var(--color-text-note);
        font-style: italic;
        font-weight: 500;
      }

      & .price-text.notice {
        color: #00823e;
      }
    }
    & .language-options {
      display: flex;
      gap: 5px;
      font-size: 15px;
      justify-content: center;
      width: 100%;
      padding: 10px 0;
      white-space: nowrap;
      flex-wrap: wrap;

      & > .lang-button {
        background-color: #dbdbdb;
        color: #202020;
        padding: 0 10px;
        border-radius: 2em;
      }
      & > .lang-button.active {
        background-color: #202020;
        color: white;
        font-weight: 600;
      }
    }
    & .product-content-edit.edit-enable {
      padding: 5px 0;
      margin: 0;
      gap: 5px;
      max-height: 35px;

      & > .label {
        white-space: nowrap;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      & > .enable-switch {
        & > .react-switch-handle {
          box-shadow: none;
          outline: none;
        }
      }
    }

    & .childen-product-info-container {
      align-items: flex-start;
      gap: 5px;
      margin-top: 20px;
      flex: 1;
      width: 100%;
      padding: 15px;

      & > p {
        color: var(--primary-color-1);
        font-size: 1.3em;
        font-weight: bold;
      }

      & .child-product-item-info {
        border-radius: 10px;
        padding: 5px;
        gap: 10px;
        margin-top: 10px;
        width: 100%;
        align-items: flex-start;
        display: flex;
        justify-content: flex-start;
        border: thin solid #ccc;
        background-color: white;

        & > .avatar {
          border-radius: 10px;
          // width: 25%;
          aspect-ratio: 1;
          width: 100px;
          height: 100px;
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > .child-product-detail {
          gap: 5px;
          align-items: flex-start;
          justify-content: flex-start;

          & > .name {
            font-weight: 600;
            font-size: 0.9em;
          }

          & > .price {
            color: var(--primary-color-1);
            font-size: 0.85em;

            & > em.off {
              color: var(--color-text-note);
              font-size: 0.9em;
              font-style: normal;
              text-decoration: line-through;
            }
          }

          & > .product-stock {
            font-size: 17px;

            & > .label {
              font-weight: 500;
            }

            & > .content {
              font-weight: 600;
              color: #00823e;
            }
          }
        }
      }

      & .child-product-item-edit {
        justify-content: center;
        background-color: white;
        border-radius: 5px;
        padding: 5px;
        gap: 10px;
        width: 100%;
        font-size: 17px;

        & > .edit-content {
          gap: 10px;
          justify-content: center;
          align-items: flex-start;
          padding: 5px;
          background-color: white;
          width: 100%;

          & > .avatar {
            width: 25%;
            min-width: fit-content;
            position: relative;
            gap: 10px;

            & > img {
              border-radius: 10px;
              aspect-ratio: 1;
              width: 100px;
              height: 100px;
              margin: auto;
              background-color: white;
              object-fit: cover;
            }

            & > div.select-image {
              cursor: pointer;

              & > div {
                position: relative;
                width: 100%;
                font-size: 1.2em;
                background: #ccc;
                color: white;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 10px;
                padding: 7px;
                cursor: pointer;

                & input {
                  opacity: 0;
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 0;
                  left: 0;
                  cursor: pointer;
                  z-index: 2;
                }

                & input::file-selector-button {
                  cursor: pointer;
                }
              }
            }
          }

          & > .info {
            gap: 5px;
            flex: 1;
          }

          & .input-custom,
          .text-area-custom {
            background: #f5f6fb;
          }
          & .dropdown-select-container {
            background: #f5f6fb;
          }
        }

        & .form-actions {
          padding: 0;
          background: inherit;
          justify-content: flex-start;
          gap: 10px;
          // padding: 10px 0;

          & > button {
            width: 25%;
            flex: unset;
            min-width: fit-content;
            font-size: 1em;
            padding: 5px;
          }

          & > button.save-button {
            flex: 1;
          }

          & > .delete-button {
            margin-right: auto;
            border: thin solid;
          }
        }
      }
    }
  }

  & .form-actions,
  .form-actions-editing {
    gap: 15px;
    justify-content: space-evenly;
    margin-top: auto;
    padding: 10px;
    position: sticky;
    bottom: 0;
    background: white;

    & > button {
      border-radius: 5px;
      flex: 1;
      padding: 5px 20px;
      border: none;
      font-size: 17px;
      font-weight: bold;
    }

    & > .cancel-button {
      color: #343434;
      border: thin solid #e2e5eb;
      background: #e2e5eb;
    }

    & > .save-button {
      color: white;
      border: thin solid #00823e;
      background: #00823e;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    & > .view-button {
      color: var(--primary-color-1);
      border: thin solid var(--primary-color-1);
      background: white;
      flex: 0;
    }
  }

  .form-actions-editing {
    background: transparent;
  }

  & .edit-product-content-container {
    flex: 1;
    padding: 15px;
  }

  & .dropdown-select-container.category-select .group {
    width: 95%;
    max-height: 500% !important;
    overflow: auto;
    box-shadow: 0 0 5px #ccc;
    border-radius: 10px;
    opacity: 1;
    background: white;
    & ul {
      border-radius: 0;
    }
    & .search-category-empty {
      color: var(--color-text-note);
    }
  }

  & .product-images-carousel {
    width: 100%;
    height: 100%;

    & img {
      min-height: unset;
      max-height: unset;
    }
  }
}
.delete-product-name {
  color: var(--primary-color-1);
}
.delete-product-message {
  font-size: 1.3em;
  // color: var(--primary-color-1);
  text-align: center;
}

.my-modal-content-container.confirm-delete-product {
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  min-height: fit-content !important;
  height: fit-content !important;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.edit-product-modal {
  min-height: 95dvh;
  max-height: 95dvh;
  height: 95dvh;
}
.error-message.hight-light:not(.success) {
  transform-origin: 0 0;
  animation: high-light 0.5s ease-in-out infinite;
}

.category-content-container {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}
.ghost {
  opacity: 0 !important;
}
