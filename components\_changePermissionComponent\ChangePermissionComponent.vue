<template>
	<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'" content-class="v-stack change-permission-container" :click-to-close="true"
		v-model="showChangePermissionModal" contentTransition="vfm-fade" v-on:closed="() => {
			close()
		}">
		<HeaderComponent :title="$t('ChangePermissionComponent.thay_doi_quyen')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close-button" v-on:click="() => {
					close();
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="change-permission-content">
			<span class="label">{{ $t('ChangePermissionComponent.thao_tac_can_thuc_hien') }}:</span>
			<div class="change-step">
				<span class="browser-name">{{ currentBrowser }}:</span>
				<div class="step-breadcrumbs">
					<span v-for="(step, index) in stepToChanges?.steps">
						<span class="step-title"
							v-html="$t(`ChangePermissionComponent.${step.title}`, { domain: ':domain' }).replaceAll(':domain', `<em>${domain}</em>`)"></span>
						<Icon name="lucide:chevron-right" v-if="index < stepToChanges?.steps?.length - 1"></Icon>
					</span>
				</div>
				<div class="quick-link">
					<span>{{ stepToChanges.link }}</span>
					<button
						:class="{'active': copiedLink}" 
						v-on:click="copyToClipboard(stepToChanges.link)">
						<Icon name="line-md:check-all" v-if="copiedLink"> </Icon>
						{{
							copiedLink ? $t('ChangePermissionComponent.da_sao_chep') : $t('ChangePermissionComponent.copy_link')
						}}
					</button>
				</div>
			</div>
		</div>
	</VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
const props = defineProps({

});
const emit = defineEmits(['close']);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var showChangePermissionModal = ref(false);
var webInApp = ref(null as any);
var stepToChanges = ref<any>(null)
var currentBrowser = ref("");

var stepTemp: any = {
	chrome: {
		steps: [
			{
				title: 'chrome.mo_setting',
				href: 'chrome://settings',
				disabled: false
			},
			{
				title: 'chrome.privacy_and_security',
				href: 'chrome://settings/privacy',
				disabled: false
			},
			{
				title: 'chrome.site_settings',
				href: 'chrome://settings/content',
				disabled: false
			},
			{
				title: 'chrome.chon_domain',
				href: `chrome://settings/content/siteDetails?site=https%3A%2F%2F${domain.toString()}%2F`,
				disabled: false
			},
			{
				title: 'chrome.thay_doi_quyen',
				href: null,
				disabled: true
			},
		],
		link: `chrome://settings/content/siteDetails?site=https%3A%2F%2F${domain}%2F`
	},
	firefox: {
		steps: [
			{
				title: 'firefox.mo_setting',
				href: 'about:preferences',
				disabled: false
			},
			{
				title: 'firefox.privacy_and_security',
				href: 'about:preferences#privacy',
				disabled: false
			},
			{
				title: 'firefox.keo_xuong_permission',
				href: null,
				disabled: true
			},
			{
				title: 'firefox.chon_setting_ben_canh_quyen',
				href: null,
				disabled: true
			},
			{
				title: 'firefox.thay_doi_cho_domain',
				href: null,
				disabled: true
			},
		],
		link: `about:preferences#privacy`
	},
	safari: {
		steps: [
			{
				title: 'safari.mo_setting',
				href: null,
				disabled: true
			},
			{
				title: 'safari.chuyen_qua_tab_website',
				href: null,
				disabled: true
			},
			{
				title: 'safari.thay_doi_cho_domain',
				href: null,
				disabled: true
			},
		],
		link: null
	},
	edge: {
		steps: [
			{
				title: 'edge.mo_setting',
				href: 'edge://settings/profiles',
				disabled: false
			},
			{
				title: 'edge.cookies_and_site_permissions',
				href: 'edge://settings/content',
				disabled: false
			},
			{
				title: 'edge.chon_domain',
				href: null,
				disabled: true
			},
			{
				title: 'chrome.thay_doi_quyen',
				href: null,
				disabled: true
			}
		],
		link: 'edge://settings/content'
	},
	unknown: {}
}
var copiedLink = ref(false);
var copiedTimeout:any;
onMounted(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	let browser = getBrowser();
	currentBrowser.value = browser;
	stepToChanges.value = stepTemp[browser.toLowerCase()];
	console.log(currentBrowser.value, stepToChanges.value)
	showChangePermissionModal.value = true;
});

function getBrowser() {
	const userAgent = navigator.userAgent;
	console.log(userAgent);
	if (userAgent.includes("Chrome")) return "Chrome";
	if (userAgent.includes("Firefox")) return "Firefox";
	if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) return "Safari";
	if (userAgent.includes("Edg")) return "Edge";
	return "unknown";
}

function close() {
	showChangePermissionModal.value = false
	emit('close')
}

function copyToClipboard(link: any) {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(link);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: link
		})
	}
	copied()
}
function copied(){
	copiedLink.value = true;
	clearTimeout(copiedTimeout);
	copiedTimeout = setTimeout(() => {
		copiedLink.value = false;
	}, 3000);
}
</script>

<style lang="scss" src="./ChangePermissionStyles.scss"></style>