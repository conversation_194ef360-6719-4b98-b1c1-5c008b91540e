
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class CategoryService extends BaseHTTPService {
    filter(){

    }

    list(offset = 0, limit = 20, all = false){
        let url = appConst.apiURL.categoryList + '?offset=' + offset +'&limit=' + limit + '&all=' + all;
        return this.https('GET', url);
    }

    getCategoryByShopId(shopId:string){
        let url = appConst.apiURL.categoryListByShopId + '/' + shopId;
        return this.https('GET', url);
    }

    createCategory(body:any){
        return this.https('POST', appConst.apiURL.createCategory, body, null, true);
    }

    editCategory(body:any){
        return this.https('POST', appConst.apiURL.editCategory, body, null, true);
    }

    updateIndex(body:any){
        return this.https('POST', appConst.apiURL.updateIndexCategory, body, null, true)
    }

    removeCategory(id:any){
        return this.https('POST', appConst.apiURL.removeCategory, {id: id}, null, true);
    }

    updateListProduct(body:any){
        return this.https('POST', appConst.apiURL.updateListProduct, body, null, true);
    }

    detailCategory(id:string){
        let url = appConst.apiURL.detailCategory + "/" + id;
        return this.https('GET', url);
    }
}