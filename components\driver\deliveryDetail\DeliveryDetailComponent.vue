<template>
  <div class="public-container" id="public-container">
    <div class="v-stack delivery-detail-container">
      <SubHeaderV2Component :title="$t('AppRouteTitle.DeliveryDetailComponent')">
      </SubHeaderV2Component>
      <div class="delivery-detail-content-container">
        <v-overlay v-if="isRefreshing" :z-index="100" :absolute="false" contained content-class='spinner-container'
          persistent scrim="#fff" key="loading" no-click-animation>
          <Icon name="eos-icons:loading"></Icon>
        </v-overlay>
        <div class='v-stack delivery-detail-content' v-if="!isRefreshing && (deliveryData && deliveryData.id)">
          <client-only>
            <div class="map-container" id="delivery_map_container">
              <LMap id="leaflet_map" v-on:ready="(e: any) => {
                leafletMap = e;
                leafletMap.setView([driver_latitude ?? appConst.defaultCoordinate.latitude, driver_longitude ?? appConst.defaultCoordinate.longitude], 17);
                leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                initLeafletMap();
              }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }"
                :world-copy-jump="true" :use-global-leaflet="true">
                <LControlZoom position="bottomright"></LControlZoom>
                <span class="current-location-leaflet" :title="$t('Map.vi_tri_cua_ban')" v-on:click="() => {
                  // gotoCurrentLocationLeaflet();
                  fitToDirection();
                }">
                  <Icon name="line-md:my-location-loop" class="my-location-icon" />
                </span>
                <!-- <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip" data-placement="right"
                  :title="$t('Map.nhan_de_chuyen_loai_map')" v-bind:style="{
                    backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                  }" v-on:click="(event: any) => {
                    if (event.isTrusted) {
                      if (
                        leafletMapTileUrl ==
                        appConst.leafletMapTileUrl.roadmap
                      ) {
                        leafletMapTileUrl =
                          appConst.leafletMapTileUrl.hyprid;
                        mapTypeTitle = $t('Map.ve_tinh');
                        mapType = 'hyprid';
                        buttonMapTileBackgound = map_sateline;
                      } else if (
                        leafletMapTileUrl ==
                        appConst.leafletMapTileUrl.hyprid
                      ) {
                        leafletMapTileUrl =
                          appConst.leafletMapTileUrl.streetmap;
                        mapTypeTitle = $t('Map.co_dien');
                        mapType = 'hyprid';
                        buttonMapTileBackgound = map_streetmap;
                      } else if (
                        leafletMapTileUrl ==
                        appConst.leafletMapTileUrl.streetmap
                      ) {
                        leafletMapTileUrl =
                          appConst.leafletMapTileUrl.roadmap;
                        mapTypeTitle = $t('Map.ve_tinh_va_nhan');
                        mapType = 'roadmap';
                        buttonMapTileBackgound = map_sateline;
                      }
                    } else event.preventDefault();
                  }">
                  <span>{{ mapTypeTitle }}</span>
                </div> -->
                <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                  :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base" name="GoogleMap">
                </LTileLayer>
              </LMap>
            </div>
          </client-only>

          <div class="delivery-detail-info" id="delivery_detail_container">
            <div class="delivery-info">
              <span class="delivery-code"> #{{ deliveryData.short_code }} </span>
              <span class="delivery-status" :class="{
                'pending': deliveryData.status == appConst.delivery_status.pending,
                'confirmed': deliveryData.status == appConst.delivery_status.confirmed,
                'cancel': deliveryData.status == appConst.delivery_status.cancelled,
                'delivered': deliveryData.status == appConst.delivery_status.delivered
              }">{{ $t(`DeliveryDetailComponent.trang_thai_giao_hang_${deliveryData.status}`) }}</span>
              <div class="places-info" :class="{ 'expanded': showFull == true }">
                <div class="place">
                  <img :src="start_location_icon" class="icon-left" />
                  <div class="place-content">
                    <span>{{ deliveryData.name_from }} - {{ deliveryData.phone_from }}
                      <nuxt-link :to="`tel:${validPhone(deliveryData.phone_from)}`" :target="webInApp ? '_blank' : ''"
                        class="call-customer">
                        <Icon name="material-symbols:call"></Icon>
                        {{ $t('DeliveryDetailComponent.goi_dien') }}
                      </nuxt-link>
                    </span>
                    <em>{{ deliveryData.address_from }}</em>
                    
                  </div>

                </div>
                <div class="distance">

                  <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon> <em
                    class="distance">{{
                      deliveryData.distance }} km</em>
                </div>
                <div class="place">
                  <img :src="destination_location_icon" class="icon-left" />
                  <div class="place-content">
                    <span v-on:click="()=>{
                      showOrderReceiverInfoModal = true;
                    }">{{ deliveryData.name_to }} - {{ validPhone(deliveryData.phone_to) }}
                      <nuxt-link :to="`tel:${validPhone(deliveryData.phone_to)}`" :target="webInApp ? '_blank' : ''"
                        v-on:click="($e:Event)=>{
                          $e.stopPropagation()
                        }"
                        class="call-customer">
                        <Icon name="material-symbols:call"></Icon>
                        {{ $t('DeliveryDetailComponent.goi_dien') }}
                      </nuxt-link>
                    </span>
                    <em v-on:click="()=>{
                      showOrderReceiverInfoModal = true;
                    }">{{ deliveryData.address_to }}</em>
                    <button class="open-info" v-on:click="()=>{
                      showOrderReceiverInfoModal = true;
                    }">({{ $t('DeliveryDetailComponent.xem_chi_tiet') }})</button>
                  </div>

                </div>

              </div>
              <div class="primary-info" :class="{ 'expanded': showFull == true }">
                <div class="stack-info auto">
                  <Icon name="ic:baseline-access-time" class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.thoi_gian_lay_hang') }}:</span>
                    <em class="value">{{
                      deliveryData.pickup_time
                        ? moment(deliveryData.pickup_time, 'YYYY-MM-DD HH:mm').format('DD/MM/YYYY HH:mm')
                        : $t('DeliveryDetailComponent.bay_gio')
                    }}</em>
                  </div>
                </div>
                <div class="stack-info auto">
                  <Icon name="streamline:shipping-transfer-truck-time-truck-shipping-delivery-time-waiting-delay"
                    class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.giao_trong') }}:</span>
                    <em class="value">{{ deliveryData.duration.toFixed(0) }} {{ $t('DeliveryDetailComponent.phut')
                      }}</em>
                  </div>
                </div>

                <div class="stack-info auto" v-if="deliveryData.package_info">
                  <Icon name="ph:package-duotone" class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.thong_tin_goi_hang') }}</span>
                    <em class="value">
                      <span v-if="deliveryData.package_info.product_type?.length">{{
                        deliveryData.package_info.product_type }}</span>
                      <span v-if="deliveryData.package_info.weight?.length">{{
                        $t(`DeliveryDetailComponent.${deliveryData.package_info.weight}`)
                        }}</span>
                      <span v-if="deliveryData.package_info.size?.length">{{ deliveryData.package_info.size }}</span>
                    </em>
                  </div>
                </div>
                <div class="stack-info auto" v-if="deliveryData.special_require?.length > 0">
                  <Icon name="pajamas:issue-type-requirements" class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.yeu_cau_dac_biet') }}</span>
                    <em class="value">
                      <span v-for="re in deliveryData.special_require">
                        {{ $t(`DeliveryDetailComponent.${re.key}`) }}
                      </span>
                    </em>
                  </div>
                </div>
                <div class="stack-info full" v-if="false">
                  <Icon name="hugeicons:cash-02" class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.cod') }}</span>
                    <span class="value">{{ formatCurrency(deliveryData.cod_price ||
                      0) }}</span>
                  </div>
                </div>
                <div class="stack-info full" v-if="deliveryData?.notes?.length">
                  <Icon name="ph:note" class="icon-left"></Icon>
                  <div class="stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.ghi_chu_cho_tai_xe') }}</span>
                    <span class="value">{{ deliveryData.notes }}</span>
                  </div>
                </div>
                <div class="stack-info full price">
                  <div class="h-stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.cod') }}</span>
                    <span class="value">{{ formatCurrency(deliveryData.cod_price ||
                      0) }}</span>
                  </div>
                  <div class="h-stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.thu_dau_shop') }}</span>
                    <span class="value">{{ formatCurrency(Math.abs(deliveryData.order?.delivery_discount ?? 0) - deliveryData.cod_price) }}</span>
                  </div>
                  <div class="h-stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.thu_dau_khach') }}</span>
                    <span class="value">{{ formatCurrency(deliveryData.cod_price + deliveryData.order?.delivery_price + (deliveryData.order?.delivery_discount ?? 0)) }}</span>
                  </div>
                  <div class="h-stack-content last">
                    <span class="label">{{ $t('DeliveryDetailComponent.thu_ve_thuc_te') }}</span>
                    <span class="value">{{ formatCurrency(Math.abs(deliveryData.order?.delivery_discount ?? 0) - deliveryData.cod_price + deliveryData.cod_price + deliveryData.order?.delivery_price + (deliveryData.order?.delivery_discount ?? 0)) }}</span>
                  </div>
                  <!-- <div class="h-stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.phi_ship') }}
                      <em>({{ $t(`DeliveryDetailComponent.tra_boi_${deliveryData.payment_method}`) }})</em></span>
                    <span class="value">{{ formatCurrency(deliveryData.total_amount ?? 0) }}</span>
                  </div>
                  <div class="h-stack-content">
                    <span class="label">{{ $t('DeliveryDetailComponent.giam_gia') }}</span>
                    <span class="value">-{{ formatCurrency(deliveryData.discount_amount || 0) }}</span>
                  </div> -->
                  <!-- <div class="h-stack-content last" v-if="deliveryData.status == appConst.delivery_status.picked_up">

                    <span class="label">{{ $t('DeliveryDetailComponent.thu_tu_nguoi_nhan') }}</span>
                    <span class="value">{{ formatCurrency(
                      parseFloat(deliveryData.cod_price ?? 0) +
                      (deliveryData.payment_method == delivery_payment_method_enum.cash_by_recipient ?
                        parseFloat(deliveryData.total_amount ?? 0) : 0)
                    ) }}</span>
                  </div> -->
                  <!-- <div class="h-stack-content last" v-if="deliveryData.status == appConst.delivery_status.confirmed">
                    <span class="label" v-if="
                      (deliveryData.payment_method == delivery_payment_method_enum.cash ? parseFloat(deliveryData.total_amount ?? 0) : 0) >= parseFloat(deliveryData.cod_price ?? 0)
                    ">{{ $t('DeliveryDetailComponent.thu_tu_nguoi_gui') }}</span>
                    <span class="label" v-if="
                      (deliveryData.payment_method == delivery_payment_method_enum.cash ? parseFloat(deliveryData.total_amount ?? 0) : 0) < parseFloat(deliveryData.cod_price ?? 0)
                    ">{{ $t('DeliveryDetailComponent.tra_cho_nguoi_gui') }}</span>
                    <span class="value">{{ formatCurrency(
                      Math.abs((deliveryData.payment_method == delivery_payment_method_enum.cash ?
                        parseFloat(deliveryData.total_amount ?? 0) : 0)
                        - parseFloat(deliveryData.cod_price ?? 0))
                    ) }}</span>
                  </div> -->
                </div>
              </div>
            </div>
            <div class="actions">
              <button class="direction-button" v-if="deliveryData.status == appConst.delivery_status.confirmed"
                v-on:click="goToStartPlace()">
                {{ $t('DeliveryDetailComponent.toi_diem_lay_hang') }}
              </button>
              <button class="direction-button" v-if="deliveryData.status == appConst.delivery_status.picked_up"
                v-on:click="goToDestinationPlace()">
                {{ $t('DeliveryDetailComponent.duong_di') }}
              </button>
              <button class="cancel" v-if="deliveryData.status == appConst.delivery_status.pending" v-on:click="() => {
                updateDeliveryStatus(appConst.delivery_status.cancelled)
              }" :disabled="isUpdating">{{ $t('DeliveryDetailComponent.tu_choi') }}</button>
              <button class="confirm" v-if="deliveryData.status == appConst.delivery_status.pending" v-on:click="() => {
                updateDeliveryStatus(appConst.delivery_status.confirmed)
              }" :disabled="isUpdating">
                {{ $t('DeliveryDetailComponent.nhan_don') }}
                <em>
                  (<vue-countdown v-if="time_remaining > 0" class="time-remaining" :time="time_remaining * 1000"
                    v-slot="{ minutes, seconds }" v-on:end="() => {
                      time_remaining = 0;
                      updateDeliveryStatus(appConst.delivery_status.cancelled)
                    }" v-on:progress="({ totalSeconds }) => { }">
                    {{ moment().minutes(minutes).format('m') }}:{{ moment().seconds(seconds).format('ss') }}
                  </vue-countdown>)
                </em>
              </button>
              <button class="pick-up" v-if="deliveryData.status == appConst.delivery_status.confirmed" v-on:click="() => {
                updateDeliveryStatus(appConst.delivery_status.picked_up)
              }" :disabled="isUpdating">{{ $t('DeliveryDetailComponent.da_lay_hang') }}</button>
              <button class="completed" v-if="deliveryData.status == appConst.delivery_status.picked_up" v-on:click="() => {
                updateDeliveryStatus(appConst.delivery_status.delivered)
              }" :disabled="isUpdating">{{ $t('DeliveryDetailComponent.hoan_thanh') }}</button>
            </div>
            <button class="expanding-button" v-on:click="() => {
              showFull = !showFull
            }">
              <div v-if="!showFull">
                <span>{{ $t('DeliveryDetailComponent.mo_rong') }}</span>
                <Icon name="material-symbols:keyboard-double-arrow-up-rounded"></Icon>
              </div>
              <div v-else>
                <span>{{ $t('DeliveryDetailComponent.thu_gon') }}</span>
                <Icon name="material-symbols:keyboard-double-arrow-down-rounded"></Icon>
              </div>
            </button>
          </div>
        </div>

        <div class='not-existing' v-if="!isRefreshing && !(deliveryData && deliveryData.id)">
          <img loading="lazy" class="empty-image" :src='none_result' :placeholder="none_result" />

          <p class="empty-text">
            {{ $t('DeliveryDetailComponent.don_giao_khong_ton_tai') }}
          </p>
          <button class='accept-button' v-on:click="() => {
            router.push({ path: appRoute.DeliveryHistoryComponent })
          }">
            {{ $t('DeliveryDetailComponent.xem_don_khac') }}
          </button>
        </div>
      </div>
    </div>

    <OrderReceiverInfoComponent v-if="showOrderReceiverInfoModal"
            v-on:close="()=>{
                showOrderReceiverInfoModal = false;
            }"
            :init_data="{
                name: deliveryData.name_to,
                phone: deliveryData.phone_to,
                address: deliveryData.address_to,
                latitude: deliveryData.latitude_to,
                longitude: deliveryData.longitude_to,
                images: isString(deliveryData.order?.extra_data) ? JSON.parse(deliveryData.order?.extra_data)?.location?.images : deliveryData.order?.extra_data?.location?.images,
                note: isString(deliveryData.order?.extra_data) ? JSON.parse(deliveryData.order?.extra_data)?.location?.note : deliveryData.order?.extra_data?.location?.note
            }"
        ></OrderReceiverInfoComponent>
  </div>
</template>

<script lang="ts" setup>
import { DeliveryService } from '~/services/orderService/deliveryService';
import { appConst, domainImage, formatCurrency, formatNumber, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import moment from 'moment';
import { delivery_payment_method_enum } from '~/assets/appDTO';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import none_result from "~/assets/image/not-found-shipper.webp";
import marker_location_icon from "~/assets/image/marker-location.png";
import blue_marker_location_icon from "~/assets/image/blue-dot-location.png";
import driver_marker_location_icon from "~/assets/image/driver-marker.png"
import start_location_icon from "~/assets/image/start-marker-2.png";
import destination_location_icon from "~/assets/image/destination-marker-2.png";

import { toast } from 'vue3-toastify';
import { DriverService } from '~/services/driverService/driverService';
import { HttpStatusCode } from 'axios';
import { UserService } from '~/services/userService/userService';
import { AuthService } from '~/services/authService/authService';
import { MqttService } from '~/services/mqttService/mqttService';
import _ from 'lodash';
import VueCountdown from '@chenfengyuan/vue-countdown';

const { t } = useI18n();
// const emit = defineEmits(['close', 'submit']);
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const device = useDevice();

var profileData = ref(null as any);

var deliveryService = new DeliveryService();
var driverService = new DriverService();
var authService = new AuthService();
var mqttService = new MqttService();

var deliveryId = ref((route.params.delivery_id || null) as any);
var deliveryData = ref();

var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var destinationMarkerLeaflet: L.Marker;
var startMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('Map.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var buttonMapTileBackgound = ref(map_sateline);

let my_latitude = ref(null as any);
let my_longitude = ref(null as any);

let latitude_start = ref(null as any);
let longitude_start = ref(null as any);

let latitude_destination = ref(null as any);
let longitude_destination = ref(null as any);

var isRefreshing = ref(true);
var isUpdating = ref(false);
var webInApp = ref(null as any);
let vehicle = ref('bike');
var showFull = ref(false);
var driver_latitude = ref<any>();
var driver_longitude = ref<any>();
var driver_orient_alpha = ref<any>()

var timeoutDelivery = ref(5 * 60);
var time_remaining = ref(0);

var showOrderReceiverInfoModal = ref(false);
useSeoMeta({
  title: t('AppRouteTitle.DeliveryDetailComponent')
});

var control: any;
var driverWatcherId: any;
var lastPublishMoving = ref<any>(null);

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

watch(
  () => [driver_latitude.value, driver_longitude.value, driver_orient_alpha.value],
  async (newQuery: any, oldQuery: any) => {
    if (localeMarkerLeaflet && newQuery) {
      localeMarkerLeaflet.setLatLng([driver_latitude.value, driver_longitude.value])
    }
    if (deliveryData.value?.status == appConst.delivery_status.confirmed || deliveryData.value?.status == appConst.delivery_status.picked_up) {
      // setControlRouteMachine();
      if (!lastPublishMoving.value || (Date.now() - lastPublishMoving.value > 3000)) {
        lastPublishMoving.value = Date.now();
        if (control) {
          let currentWaypoint = control.getWaypoints();
          currentWaypoint[0].latLng = new nuxtApp.$L.LatLng(driver_latitude.value ?? newQuery?.[0], driver_longitude.value ?? newQuery?.[1]);
          control.setWaypoints(currentWaypoint);
          control.route();
        }
        let messDriverMoving = {
          mess: {
            type: 'driver_moving',
            url: deliveryData.value?.id,
            driver_latitude: driver_latitude.value ?? newQuery?.[0],
            driver_longitude: driver_longitude.value ?? newQuery?.[1],
            driver_orient_alpha: driver_orient_alpha.value ?? newQuery?.[2] ?? 0,
          },
          topic: appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id ?? deliveryData.value?.order_id),
        }
        mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id ?? deliveryData.value?.order_id), JSON.stringify(messDriverMoving))
      }

    }
  },
  { deep: true }
);
watch(() => [user_latitude.value, user_longitude?.value], () => {
  driver_latitude.value = user_latitude?.value;
  driver_longitude.value = user_longitude?.value;
});
onBeforeMount(async () => {
  console.log('before mount')
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})
onMounted(async () => {
  isRefreshing.value = true;
  // await getDriverLocation();
  // await getUserLocation();
  driver_latitude.value = user_latitude?.value;
  driver_longitude.value = user_longitude?.value;

  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

  let profileData$ = await authService.checkAuth();

  profileData.value = profileData$;
  if (profileData.value) {
    await handleGetDetailDelivery();

    let author =  (await authService.checkAuthorize(appConst.role_enum.driver) || await authService.checkAuthorize(appConst.role_enum.admin) || await authService.checkAuthorize(appConst.role_enum.admin2))
    if (author) {
      await watchDeviceOrient();
      if (isiOS()) {
        nuxtApp.$listen(appConst.event_key.device_rotated, (e: any) => {
          if (localeMarkerLeaflet && e !== null) {
            let icon = new nuxtApp.$L.Icon({
              iconUrl: driver_marker_location_icon,
              iconSize: appConst.markerCustom.driverIcon.size,
              className: `${appConst.markerCustom.driverIcon.class} non-transition`,
            })
            localeMarkerLeaflet.setIcon(icon);
            localeMarkerLeaflet.setRotationAngle(e);
            driver_orient_alpha.value = e;
          }
        });
      } else {
        ondeviceorientationabsolute = (e) => {
          if (localeMarkerLeaflet && e.alpha !== null) {
            let icon = new nuxtApp.$L.Icon({
              iconUrl: driver_marker_location_icon,
              iconSize: appConst.markerCustom.driverIcon.size,
              className: `${appConst.markerCustom.driverIcon.class} non-transition`,
            });
            localeMarkerLeaflet.setIcon(icon);
            localeMarkerLeaflet.setRotationAngle(-e.alpha);
            driver_orient_alpha.value = -e.alpha;
          }
        }
      }
      setTimeout(() => {
        showFull.value = true;
      }, 500);

    }
  }
  else {
    nuxtApp.$emit(appConst.event_key.require_login, {
      redirect_url: route.path,
      back_on_close: true
    })
  }

  // nuxtApp.$listen(appConst.event_key.refresh_delivery_detail, async () => {
  //   console.log("refresh")
  //   await handleGetDetailDelivery();
  // })

});

onUnmounted(() => {
  // if ("geolocation" in navigator) {
  //   console.log(driverWatcherId);
  //   navigator.geolocation.clearWatch(driverWatcherId);
  //   driverWatcherId = null;
  // }
  nuxtApp.$unsubscribe(appConst.event_key.user_moving);
})
function isiOS() {
  var Apple = device.isApple;
  var Mac = device.isMacOS;
  var iOS = device.isIos;
  return (Apple || Mac || iOS);
}
function fitToDirection() {
  let bounds = new nuxtApp.$L.LatLngBounds(
    nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
    nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value),
  );
  if (driver_latitude.value && driver_longitude.value) {
    bounds.extend([driver_latitude.value, driver_longitude.value])
  }
  leafletMap.fitBounds(bounds, {
    padding: [50, 50],
  })
}
async function initLeafletMap() {

  // leafletMap.setView([
  //   my_latitude.value,
  //   my_longitude.value
  // ], 17);
  // fitToDirection();

  await addDestinationMarker();
  await addStartMarker();

  setTimeout(async () => {
    await addMyLocationMarker();
    await setControlRouteMachine();
  }, 1000);

}

// async function getUserLocation() {
//   if ("geolocation" in navigator) {
//     navigator.geolocation.getCurrentPosition(
//       (position) => {
//         my_latitude.value = position.coords.latitude;
//         my_longitude.value = position.coords.longitude;
//         // addStartMarker();
//       },
//       (error) => {
//         my_latitude.value = appConst.defaultCoordinate.latitude;
//         my_longitude.value = appConst.defaultCoordinate.longitude;
//         // addStartMarker();
//       }
//     );
//   }
//   else {
//     my_latitude.value = appConst.defaultCoordinate.latitude;
//     my_longitude.value = appConst.defaultCoordinate.longitude;
//   }
//   // }

// }
function addMyLocationMarker() {
  localeMarkerLeaflet = nuxtApp.$L.marker([driver_latitude.value ?? appConst.defaultCoordinate.latitude, driver_longitude.value ?? appConst.defaultCoordinate.longitude], {
    icon: nuxtApp.$L.divIcon({
      html:
        `<div class='user-location'>
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="56" stroke-dashoffset="56" d="M12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4Z"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.5s" values="56;0"/></path><path d="M12 4v0M20 12h0M12 20v0M4 12h0" opacity="0"><set attributeName="opacity" begin="0.9s" to="1"/><animate fill="freeze" attributeName="d" begin="0.9s" dur="0.2s" values="M12 4v0M20 12h0M12 20v0M4 12h0;M12 4v-2M20 12h2M12 20v2M4 12h-2"/><animateTransform attributeName="transform" dur="30s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path></g><circle cx="12" cy="12" r="0" fill="currentColor" fill-opacity="0"><set attributeName="fill-opacity" begin="0.6s" to="1"/><animate fill="freeze" attributeName="r" begin="0.6s" dur="0.2s" values="0;4"/></circle></svg>
    </div>`,
      iconSize: [30, 30],

    }),
    rotationOrigin: appConst.markerCustom.driverIcon.rotatePosition
  });
  localeMarkerLeaflet.addTo(leafletMap);
}
function addStartMarker() {
  startMarkerLeaflet = nuxtApp.$L.marker([latitude_start.value, longitude_start.value], {
    icon: new nuxtApp.$L.Icon({
      iconUrl: start_location_icon,
      iconSize: [30, 45],
      className: appConst.markerCustom.defaultIcon.class,
    }),
  });
  startMarkerLeaflet.addTo(leafletMap);
}
function addDestinationMarker() {
  destinationMarkerLeaflet = nuxtApp.$L.marker([latitude_destination.value, longitude_destination.value], {
    icon: new nuxtApp.$L.Icon({
      iconUrl: destination_location_icon,
      iconSize: [30, 45],
      className: appConst.markerCustom.defaultIcon.class,
    }),
  });
  destinationMarkerLeaflet.addTo(leafletMap);
}


function watchDeviceOrient() {
  if ('requestPermission' in DeviceOrientationEvent) {
    (DeviceOrientationEvent as any).requestPermission().then((response: any) => {
      if (response === "granted") {
        window.addEventListener("deviceorientation", (e: any) => {
          nuxtApp.$emit(appConst.event_key.device_rotated, e.webkitCompassHeading)
        }, true);
      }
    });
  }
}

function handleGetDetailDelivery() {
  return new Promise(async (resolve) => {
    deliveryService.detail(deliveryId.value).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        deliveryData.value = res.body.data;
        deliveryData.value.special_require = deliveryData.value?.special_require?.length ? JSON.parse(deliveryData.value.special_require) : null;
        deliveryData.value.package_info = deliveryData.value?.package_info?.length ? JSON.parse(deliveryData.value.package_info) : null;

        latitude_start.value = deliveryData.value.latitude_from;
        longitude_start.value = deliveryData.value.longitude_from;
        latitude_destination.value = deliveryData.value.latitude_to;
        longitude_destination.value = deliveryData.value.longitude_to;
        timeoutDelivery.value = parseFloat(deliveryData.value?.pending_time ? deliveryData.value?.pending_time?.toString() : 5) * 60;
        checkExpireTime();

        isRefreshing.value = false;
        resolve(res);
      }
      else {
        toast.error(t('DeliveryDetailComponent.don_giao_khong_ton_tai'));
        isRefreshing.value = false;
        resolve(null);
      }

    });

  })
}

function checkExpireTime() {
  let timeTemp = moment().diff(moment(deliveryData.value.updated_at, 'YYYY-MM-DD HH:mm:ss'), 'seconds');
  console.log(timeTemp, timeoutDelivery.value)
  if (timeTemp > timeoutDelivery.value) {
    time_remaining.value = 0;
  }
  else {
    time_remaining.value = timeoutDelivery.value - timeTemp;
  }

}
async function setControlRouteMachine() {
  let start_lat;
  let start_long;
  let end_lat;
  let end_long;
  let plan: any;
  if (deliveryData.value.status == appConst.delivery_status.pending || deliveryData.value.status == appConst.delivery_status.delivered) {
    plan = new nuxtApp.$L.Routing.Plan([
      nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
      nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
    ], {
      createMarker: () => (false),
    })
  }
  else if (deliveryData.value.status == appConst.delivery_status.confirmed) {
    plan = new nuxtApp.$L.Routing.Plan([
      nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
      nuxtApp.$L.latLng(latitude_start.value, longitude_start.value)
    ], {
      createMarker: () => (false),
    })
  }
  else if (deliveryData.value.status == appConst.delivery_status.picked_up) {
    plan = new nuxtApp.$L.Routing.Plan([
      nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
      nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
    ], {
      createMarker: () => (false),
    })
  }
  if (control) {
    control.remove();
    control = null;
    // control.setPlan(plan)
  }
  setTimeout(() => {
    control = new nuxtApp.$L.Routing.Control({
      waypointMode: 'connect',
      router: nuxtApp.$L.Routing.osrmv1({
        serviceUrl: appConst.urlOSRMv1,
        requestParameters: {
          overview: 'full',
          annotations: true,
          steps: true,
          alternatives: 2,
        },
        profile: 'bike',
        useHints: false,
      }),
      fitSelectedRoutes: 'smart',
      plan: plan,
      autoRoute: false,
      routeWhileDragging: false,
      lineOptions: {
        missingRouteTolerance: 10,
        extendToWaypoints: true,
        addWaypoints: false,
        styles: [{
          color: 'var(--primary-color-2)',
          weight: 5,
        }],

      },
      altLineOptions: {
        missingRouteTolerance: 10,
        extendToWaypoints: false,
        addWaypoints: false,
        styles: [{
          opacity: .8,
          color: '#545454',
          weight: 4,
          dashArray: '10, 10'
        }]
      },
      useZoomParameter: true,
      showAlternatives: true,

    });

    control.on('routingerror', () => {
      // noneRoute.value = true;
      control.remove()
    });

    control.on('routesfound', (res: any) => {
      control?.addTo(leafletMap);
    })
    control.route();
  }, 500);

}

function publishDeliveryUpdate(newStatus: any) {
  if (newStatus != appConst.delivery_status.delivered) {
    let messShop = {
      mess: {
        type: 'delivery_update',
        url: deliveryData.value?.id
      },
      topic: appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id),
    }
    mqttService.publish(appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id), JSON.stringify(messShop))

    let messOrder = {
      mess: {
        type: 'delivery_update',
        url: deliveryData.value?.id
      },
      topic: appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id),
    }
    mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id), JSON.stringify(messOrder))
  }
  else {
    let messShop = {
      mess: {
        type: 'delivery_update',
        url: deliveryData.value?.id
      },
      topic: appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id),
    }
    mqttService.publish(appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id), JSON.stringify(messShop))

    let messOrder = {
      mess: {
        type: 'delivery_completed',
        url: deliveryData.value?.id
      },
      topic: appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id),
    }
    mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id), JSON.stringify(messOrder))
  }

}

function updateDeliveryStatus(newStatus: any) {
  isUpdating.value = true;
  driverService.driverAction({
    delivery_id: deliveryData.value.id,
    action: appConst.driver_action.update_delivery_status,
    status: newStatus,
    latitude: driver_latitude.value,
    longitude: driver_longitude.value
  }).then(async res => {
    if (res.status != HttpStatusCode.Ok) {
      toast.error(res.body?.message ?? t('DeliveryDetailComponent.cap_nhat_that_bai'))
    }
    else if (res.status == HttpStatusCode.Ok) {
      if (newStatus == appConst.delivery_status.delivered || newStatus == appConst.delivery_status.failed) {
        driverService.driverAction({
          action: appConst.driver_action.set_online,
          latitude: driver_latitude.value,
          longitude: driver_longitude.value
        })
      }
    }
    await handleGetDetailDelivery().then((res) => {
      setControlRouteMachine()

      publishDeliveryUpdate(newStatus)
    });

    isUpdating.value = false;
  })
}

async function goToStartPlace() {
  // await getUserLocation();
  let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${driver_latitude.value},${driver_longitude.value}&destination=${latitude_start.value},${longitude_start.value}&trabelmode=bicycling`;
  window.open(textToDirections, "_blank")
}
async function goToDestinationPlace() {
  // await getUserLocation();
  let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${driver_latitude.value},${driver_longitude.value}&destination=${latitude_destination.value},${longitude_destination.value}&trabelmode=bicycling`;
  window.open(textToDirections, "_blank")
}

// function getDriverLocation() {
//   if ("geolocation" in navigator) {
//     driverWatcherId = navigator.geolocation.watchPosition(
//       async (position) => {
//         console.log(position);
//         driver_latitude.value = position.coords.latitude;
//         driver_longitude.value = position.coords.longitude;
//         // setLocationLeafletMarker(position.coords.latitude, position.coords.longitude);
//       },
//       async (error) => {
//         console.log(error)
//         driver_latitude.value = appConst.defaultCoordinate.latitude;
//         driver_longitude.value = appConst.defaultCoordinate.longitude;
//       },
//       {
//         enableHighAccuracy: true,
//       }
//     );
//   }
// }

</script>

<style lang="scss" src="./DeliveryDetailStyles.scss"></style>