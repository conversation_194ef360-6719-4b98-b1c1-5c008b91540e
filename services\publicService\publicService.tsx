import axios from "axios";
import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";
import { limit } from "firebase/firestore";

export class PublicService extends BaseHTTPService {
    filterCancelToken: any;
    dashboardCancelToken: any;
    searchCancelToken: any;
    addressSearchCancelToken: any
    
    filter(body: any) {
        if (typeof (this.filterCancelToken) != typeof undefined) {
            this.filterCancelToken.cancel()
        }
        this.filterCancelToken = axios.CancelToken.source();
        return this.https('POST', appConst.apiURL.filter, body, this.filterCancelToken.token);
    }

    dashboard(body:any){
        let payload = {
            section: body.section, //suggest,sale_off,best_around,hot_deal
            latitude_user: body.latitude_user,
            longitude_user: body.longitude_user,
            latitude_s: body.latitude_s ? body.latitude_s : null,
            latitude_b: body.latitude_b ? body.latitude_b : null,
            longitude_s: body.longitude_s ? body.longitude_s : null,
            longitude_b: body.longitude_b ? body.longitude_b : null,
            limit: body.limit ? body.limit : 10,
            offset: body.offset ? body.offset : 0,
            sortBy: body.sortBy ? body.sortBy : null,
            filter_type: body.filter_type ? body.filter_type : null,
            radius: body.radius ? body.radius : null
        }
        // if (typeof (this.dashboardCancelToken) != typeof undefined) {
        //     this.dashboardCancelToken.cancel()
        // }
        this.dashboardCancelToken = axios.CancelToken.source();
        return this.https('POST', appConst.apiURL.dashboard, payload, this.dashboardCancelToken.token);
    }

    
    searchSuggest(search_text: any, offset = 0, limit = 25, latitude_user = 0, longitude_user = 0) {
        if (typeof (this.searchCancelToken) != typeof undefined) {
            this.searchCancelToken.cancel()
        }
        this.searchCancelToken = axios.CancelToken.source();
        let radius = localStorage.getItem(appConst.storageKey.radiusFilter);
        let body = {
            search: search_text,
            limit: limit,
            offset: offset,
            latitude_user: latitude_user,
            longitude_user: longitude_user,
            radius: radius ? JSON.parse(radius) : null,
        }
        return this.https('POST', appConst.apiURL.productSearchSuggest, body, this.searchCancelToken.token);
    }

    reels(latitude_user="", longitude_user="", radius=null, search?: string){
        let body = {
            latitude_user: latitude_user,
            longitude_user: longitude_user,
            radius: radius,
            search: search
        }
        return this.https('POST', appConst.apiURL.reels, body);
    }

    myGeocoderByLatLngToAddress(lat: number, lng: number) {
        let body = {
            latitude: lat,
            longitude: lng,
        }
        return this.https("POST", appConst.apiURL.geocodeLatLngToAddress, body);
    }

    myGeocoderByAddressToLatLng(address:string) {
        if (typeof (this.addressSearchCancelToken) != typeof undefined) {
            this.addressSearchCancelToken.cancel()
        }
        this.addressSearchCancelToken = axios.CancelToken.source();
        let body = {
            search: address
        }
        return this.https("POST", appConst.apiURL.geocodeAddressToLatLng, body, this.addressSearchCancelToken.token);
    }

    getOTP(data:string, type: string, agent = false){
        let body;
        if(type == 'phone')
        {
            body = {
                phone: data,
                agent: agent ? 'zalo' : 'phone',
            }
        }
        else if(type == 'email'){
            body = {
                email: data,
                agent: 'email',
            }
        }
        return this.https("POST", appConst.apiURL.getOTP, body);
    }

    confirmOtp(data:any){
        return this.https("POST", appConst.apiURL.confirmOTP, data);
    }

    detailOrder(orderId:string){
        let url = appConst.apiURL.publicOrderDetail.replaceAll(':id', orderId);
        return this.https("GET", url);
    }

    listVideo(){
        let url = appConst.apiURL.dashboardVideo;
        return this.https("POST", url);
    }

    listReviewVideo(product_id: string){
        let url = `${appConst.apiURL.reviewVideo}?product_id=${product_id}`;
        return this.https("GET", url);
    }

    listShopInteracted(body:any){
        let payload = {
            limit: body.limit ?? 10,
            offset: body.offset ?? 0
        }
        return this.https("POST", appConst.apiURL.listShopInteracted, payload);
    }
}