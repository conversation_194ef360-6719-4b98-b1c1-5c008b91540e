import axios from "axios";
import { AuthService } from "./authService/authService";
// import { refreshToken, requestPermission } from "~/firebase";
import { deleteToken, getToken } from "firebase/messaging";
import { appConst } from "~/assets/AppConst";
// import { getFCMToken } from "~/firebase";
// import { Messaging, getMessaging, getToken } from 'firebase/messaging';
// import { refreshToken, requestPermission } from '../firebase';
// const nuxtApp = useNuxtApp();
export class FCMService {
  nuxtApp = useNuxtApp();
  authService = new AuthService();
  constructor() {}

  async getFcmToken() {
    // requestPermission()
    await getToken(this.nuxtApp.$messaging as any)
      .then(async (currentToken) => {
        console.log(currentToken);
        if (currentToken) {
          let userInfoStr = await localStorage.getItem(
            appConst.storageKey.userInfo
          );
          let userInfo = JSON.parse(userInfoStr as string);

          let localToken = await localStorage.getItem(
            appConst.storageKey.token
          );
          localStorage.setItem(appConst.storageKey.fcmToken, currentToken);
          if (userInfo != null) {
            let bodyFCMToken = {
              token: currentToken,
              token_type: 2,
              user_id: userInfo.id,
            };
            let token = await this.authService.saveFCMToken(bodyFCMToken);
          }
        } else {
        }
      })
      .catch((err) => {
        console.error(err);
      });
  }

  async refreshToken() {
    // refreshToken();
    await deleteToken(this.nuxtApp.$messaging as any);
    // getToken(this.nuxtApp.messaging as any).then(async (newToken) => {
    //   let infor: any;
    //   let userInfoStr = await localStorage.getItem(
    //     appConst.storageKey.userInfo
    //   );
    //   infor = JSON.parse(userInfoStr as string);

    //   localStorage.setItem(appConst.storageKey.fcmToken, newToken);
    //   if (infor != null) {
    //     let bodyFCMToken = {
    //       token: newToken,
    //       token_type: 2,
    //       user_id: infor.id,
    //     };
    //     let token = await this.authService.saveFCMToken(bodyFCMToken);
    //   }
    // });
    this.getFcmToken();
  }

  requestPermission() {
    if ("Notification" in window) {
      Notification.requestPermission().then((permission) => {
        if (permission === "granted") {
          this.getFcmToken();
        } else {
        }
      });
    }
  }

  onMessageReceived(message: any) {
    // PushNotification.getChannels(res=> {
    // }) \
  }
}
