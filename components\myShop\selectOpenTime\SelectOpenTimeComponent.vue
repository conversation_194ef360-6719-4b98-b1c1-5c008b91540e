<template>
    <div v-if="showSelectOpenTimeModal" class="custom-overlay"></div>
    <VueFinalModal class="my-modal-container" :keep-overlay="false" :overlay="false" :hide-overlay="false" :overlay-behavior="'persist'"
        :modal-id="'select_open_time'" :hide-overlay-on-blur="false" overlay-class="select-open-time-overlay"
        content-class="v-stack form-modal select-open-time-container" :click-to-close="false" v-model="showSelectOpenTimeModal"
        contentTransition="vfm-slide-down" v-on:closed="() => {
            close()
        }">
        <HeaderComponent :title="$props.title">
            <template v-slot:header_left></template>
            <template v-slot:header_right>

                <button class="close" v-on:click="() => {
                    close()
                }">
                    <Icon name="clarity:times-line" size="25"></Icon>
                </button>
            </template>
        </HeaderComponent>
        <div class="select-open-time-content-container">
            <div class="item-open-time" :class="{ 'odd': index % 2 == 0 }" v-for="(itemDay, index) in days">
                <span class="day-name">
                    {{ $t(`DayInWeek.${itemDay}`) }}
                </span>
                <span class="open-time-value">{{ dataOpenTime?.[itemDay] ? showOpenTimeInDate(dataOpenTime[itemDay]) : $t('SelectOpenTimeComponent.chua_chon') }}</span>
                <button v-on:click="() => {
                    setEditOpenTime(itemDay)
                }">
                    <Icon name="ic:outline-edit"></Icon>
                </button>
            </div>
            <Swiper class="my-carousel edit-options-carousel" :modules="[SwiperFreeMode]" :freeMode="true"
                :space-between="10" :slides-per-view="'auto'" :loop="false" :effect="'creative'" :autoplay="false"
                key="edit-open-time-options">
                <SwiperSlide class="edit-option-item" v-on:click="async () => {
                    setEditOpenTime()
                }">
                    <span class='name'>
                        {{ $t('SelectOpenTimeComponent.chinh_sua_tat_ca') }}
                    </span>
                </SwiperSlide>
                <SwiperSlide class="edit-option-item" v-on:click="async () => {
                    setEditOpenTime('t2-t6')
                }">
                    <span class='name'>
                        {{ $t('SelectOpenTimeComponent.chinh_sua_t2_t6') }}
                    </span>
                </SwiperSlide>
                <SwiperSlide class="edit-option-item" v-on:click="async () => {
                    setEditOpenTime('t7-cn')
                }">
                    <span class='name'>
                        {{ $t('SelectOpenTimeComponent.chinh_sua_t7_cn') }}
                    </span>
                </SwiperSlide>
            </Swiper>
        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                {{ $t('SelectOpenTimeComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => submit()">
                <span>{{ $t('SelectOpenTimeComponent.xong') }}</span>
            </button>
        </div>
        <EditOpenTimeComponent v-if="showEditOpenTimeModal" :datas="editDatas" v-on:closeEdit="() => {
            showEditOpenTimeModal = false
        }" v-on:submit="async (e: any) => {
            showEditOpenTimeModal = false;
            setOpenTimeData(e)
        }"></EditOpenTimeComponent>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    title: null,
    dataOpenTime: null
})
const nuxtApp = useNuxtApp();
const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
const { t } = useI18n();
var showSelectOpenTimeModal = ref(false);
var dataOpenTime = ref(null as any);
var editDatas = ref({} as any);

var showEditOpenTimeModal = ref(false);

onBeforeUnmount(() => {
})
onMounted(async () => {
    dataOpenTime.value = props.dataOpenTime;
    showSelectOpenTimeModal.value = true;
})
onUpdated(() => {
})

function showOpenTimeInDate(openTimeData: any) {
    let result: any = [];
    if (openTimeData?.length) {
        openTimeData.forEach((element: any) => {
            result.push(`${element[0]} - ${element[1]}`)
        });
        return result.join(", ");
    }
    return t('SelectOpenTimeComponent.chua_chon')
}
function setEditOpenTime(key?: any) {
    let arrEdit = [];
    let arrData = [];
    if (!key) {
        arrEdit = days;
    }
    else if (key == 't2-t6') {
        arrEdit = ["monday", "tuesday", "wednesday", "thursday", "friday"];
    }
    else if (key == 't7-cn') {
        arrEdit = ["saturday", "sunday"];
    }
    else {
        arrEdit = [key];
    }
    arrData = JSON.parse(JSON.stringify(dataOpenTime.value?.[arrEdit[0]] ? dataOpenTime.value?.[arrEdit[0]] : null));
    editDatas.value.days = arrEdit;
    editDatas.value.datas = arrData;
    showEditOpenTimeModal.value = true;
}

function setOpenTimeData({ days = null as any, datas = null as any }) {
    if(!dataOpenTime.value){
        dataOpenTime.value = {}
    }
    days.forEach((day: any) => {
        dataOpenTime.value[day] = datas
    });
}
function close(value?: any) {
    emit('close', value);
}

function submit() {
    let result:any = {}; 
    days.forEach((day:any)=> {
        result[day] = dataOpenTime.value?.[day] ?? []
    })
    emit('submit', JSON.parse(JSON.stringify(result)));
}
</script>

<style lang="scss" src="./SelectOpenTimeStyles.scss"></style>