<template>
    <div class="public-container">
        <div class='agent-order-detail-container'>
            <!-- <div class='title-header'>
            <div class='header-left'>
                <button class="back-button" v-on:click="() => {
                    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
                }">
                    <Icon name="lucide:chevron-left"/>
                </button>
            </div>

            <h3>
                {{ appRouteTitle.AgentOrderDetailComponent }}
            </h3>
            <div class="h-stack header-right">
                <button v-show="order && parseInt(order.status.toString()) == appConst.order_status.waiting.value"
                    v-on:click="async () => {
                        showEditOrderModal = true
                    }">
                    <Icon name="bx:edit-alt" />
                </button>
            </div>


        </div> -->
            <SubHeaderV2Component :title="$t('AppRouteTitle.AgentOrderDetailComponent')">

                <template v-slot:header_right>
                    <button v-show="order && parseInt(order.status.toString()) == appConst.order_status.waiting.value"
                        v-on:click="async () => {
                            showEditOrderModal = true
                        }">
                        <Icon name="bx:edit-alt" />
                    </button>
                </template>
            </SubHeaderV2Component>
            <p class="loading" v-show="isRefreshing">
                {{ $t('AgentOrderDetailComponent.dang_tai') }}
            </p>

            <div class='v-stack' v-if="!isRefreshing && (order && order.id)">
                <div class="main-stack">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="basil:invoice-outline"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.ma_don') }}: {{
                                    order && order.short_code
                                        ? order.short_code
                                        : $t('AgentOrderDetailComponent.chua_co_ma')
                                }}
                            </span>
                        </div>
                        <button class="action copy" v-on:click="() => {
                            toast.success($t('AgentOrderDetailComponent.da_sao_chep_ma_don'));
                            copyToClipboard(order.short_code)
                        }">{{ $t('AgentOrderDetailComponent.sao_chep') }}</button>
                    </div>
                </div>

                <div class="v-stack main-stack">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="solar:pen-new-round-outline"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.trang_thai') }}:
                                {{ $t('AgentOrderDetailComponent.' +
                                    appConst.order_status[
                                        parseInt(order.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key =>
                                                appConst.order_status[key].value == parseInt(order.status)) as string
                                            : "taken"
                                    ].nameKey
                                ) }}
                            </span>
                        </div>
                        <button class="action"
                            v-if="parseInt(order.status.toString()) >= appConst.order_status.confirmed.value"
                            v-on:click="() => {
                                showFullProcess = !showFullProcess
                            }">
                            {{ $t('AgentOrderDetailComponent.mo_rong') }}
                        </button>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.cancel.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.cancel.value">
                            <span class="status-name">{{ $t('AgentOrderDetailComponent.tu_choi_don') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.taken.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.taken.value">
                            <span class="status-name">{{ $t('AgentOrderDetailComponent.giao_hang_thanh_cong') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.confirmed.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.confirmed.value">
                            <span class="status-name">{{ $t('AgentOrderDetailComponent.xac_nhan') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.waiting.value || showFullProcess">
                            <span class="status-name">{{ $t('AgentOrderDetailComponent.dat_hang') }}</span>
                            <span>
                                {{ moment(order.created_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="material-symbols:location-away-outline-rounded"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.nguoi_nhan') }}
                            </span>
                        </div>
                        <div class="h-stack action">
                            <!-- <nuxt-link :to="`https://zalo.me/${order.customer_phone}`" target='_blank' class="call-customer">
                <img loading="lazy" src='~/assets/image/Logo-Zalo-Arc.webp' />
                Zalo
            </nuxt-link> -->
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`" class="call-customer"
                                :target="webInApp ? '_blank' : ''">
                                <Icon name="material-symbols:call"></Icon>
                                {{ $t('AgentOrderDetailComponent.goi_dien') }}
                            </nuxt-link>
                        </div>
                    </div>
                    <div class="h-stack primary-content">
                        <img loading="lazy" class="content-detail user-avatar" :src="order?.customer?.profile_picture
                            ? ((appConst.provider_img_domain.some(e => order?.customer?.profile_picture?.includes(e))) ? order?.customer?.profile_picture : (domainImage + order?.customer?.profile_picture))
                            : non_avatar" :placeholder="non_avatar" alt="" />
                        <div class="content-detail">
                            <div class="customer-name">
                                <span>{{ order.customer_name }}</span>
                            </div>
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`"
                                :target="webInApp ? '_blank' : ''" class="content-detail customer-phone">
                                <span>{{ order.customer_phone }}</span>
                            </nuxt-link>
                            <div class="customer-address">
                                <span>{{ order.address }}</span>
                            </div>
                            <div class="customer-delivery-time" v-if="order?.delivery_time?.length">
                                <span>{{ $t('AgentOrderDetailComponent.muon_nhan_hang_luc') }}: <em>{{
                                    moment(order.delivery_time).format("DD/MM/YYYY - HH:mm") }}</em></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:storefront-outline"></Icon>
                            <span>
                                {{ order.shops.name }}
                                <br> <nuxt-link :to="`tel:${validPhone(order.shops.phone)}`"
                                    :target="webInApp ? '_blank' : ''" class="customer-phone">{{ order.shops.phone
                                    }}</nuxt-link>
                            </span>
                        </div>
                        <div class="v-stack action">
                            <nuxt-link :to="`https://zalo.me/${validPhone(order.shops.phone)}`" target="_blank"
                                class="call-customer">
                                <!-- Icon name="material-symbols:call"></Icon> -->
                                <img loading="lazy" :src='logo_zalo' :placeholder="logo_zalo" />
                                {{ $t('AgentOrderDetailComponent.zalo') }}
                            </nuxt-link>
                            <nuxt-link :to="`tel:${validPhone(order.shops.phone)}`" :target="webInApp ? '_blank' : ''"
                                class="action call-customer">
                                <Icon name="material-symbols:call"></Icon>
                                {{ $t('AgentOrderDetailComponent.goi_dien') }}
                            </nuxt-link>
                        </div>
                    </div>
                    <div class="v-stack order-items-container">
                        <div v-for="(itemOrder) in order.items" class='h-stack item-order-container'
                            :key="itemOrder.id">
                            <div class='v-stack item-order-avatar'>
                                <img loading="lazy"
                                    :src="itemOrder?.profile_picture ? (domainImage + itemOrder.profile_picture) : icon_for_product"
                                    :placeholder="icon_for_product" alt="" />
                            </div>
                            <div class='v-stack item-order-detail'>
                                <span class='item-order-name'>
                                    {{ itemOrder.parent_id ? (showTranslateProductName(itemOrder.parent_product) + " - ") : "" }}{{
                                        showTranslateProductName(itemOrder) }}
                                </span>
                                <div class='h-stack item-order-quantity-price'>
                                    <span>
                                        x{{ parseFloat(itemOrder.pivot.quantity).toString().replaceAll('.', ',') }}
                                    </span>
                                    |
                                    <span class="price">
                                        {{
                                            (itemOrder.pivot.price_off != null && itemOrder.pivot.price_off <
                                                itemOrder.price) ? formatCurrency(parseFloat(itemOrder.pivot.price_off),
                                                    order.shops.currency) : (parseFloat(itemOrder.price) == 0 ||
                                                        itemOrder.price == null) ? $t('AgentOrderDetailComponent.gia_lien_he') :
                                                formatCurrency(parseFloat(itemOrder.price), order.shops.currency) }} <em
                                            class="off"
                                            v-if="(itemOrder.pivot.price_off != null && itemOrder.pivot.price_off < itemOrder.price)">
                                            {{
                                                formatCurrency(itemOrder.price ? parseFloat(itemOrder.price) : 0,
                                                    order.shops.currency)
                                            }}</em>
                                    </span>
                                </div>
                                <span class='item-order-note'>
                                    {{ itemOrder.pivot.notes }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-stack v-stack" v-if="order.notes?.length">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="material-symbols:description-outline"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.ghi_chu_don_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <p class="content-detail">{{ order.notes }}</p>
                    </div>
                </div>

                <div class="main-stack" v-if="false">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="carbon:wallet"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.hinh_thuc_thanh_toan') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ $t('AgentOrderDetailComponent.thanh_toan_khi_nhan_hang') }}</span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class="h-stack first-content">
                        <div class="h-stack title-main-stack">
                            <Icon name="bi:basket2-fill"></Icon>
                            <span>
                                {{ $t('AgentOrderDetailComponent.hinh_thuc_nhan_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ order.delivery_type ? $t('AgentOrderDetailComponent.tu_toi_lay') :
                                $t('AgentOrderDetailComponent.toi_can_giao_tan_noi') }}</span>
                        </div>
                    </div>
                </div>
                <div class="main-stack">
                    <div class="h-stack payment-detail">
                        <span class="label">
                            {{ $t('AgentOrderDetailComponent.tam_tinh') }}
                        </span>
                        <span class="data">
                            {{ formatNumber(order.total_amount || 0) }}
                        </span>
                    </div>
                    <div class="h-stack payment-detail">
                        <span class="label">
                            {{ $t('AgentOrderDetailComponent.giam_gia') }}
                        </span>
                        <span class="data">
                            {{ formatNumber(order.discount_amount || 0) }}
                        </span>
                    </div>
                    <div class="h-stack payment-detail" v-if="parseFloat(order.delivery_price)">
                        <span class="label">
                            {{ $t('AgentOrderDetailComponent.phi_van_chuyen') }}
                        </span>
                        <span class="data">
                            {{ formatNumber(order.delivery_price || 0) }}
                        </span>
                    </div>
                    <div class="h-stack payment-detail total">
                        <span class="label">
                            {{ $t('AgentOrderDetailComponent.thanh_tien') }}
                        </span>
                        <span class="data">
                            {{ formatNumber(order.grand_total || 0) }}
                        </span>
                        <!-- <span class="note" v-if="!parseFloat(order.delivery_price)">{{
                            $t('AgentOrderDetailComponent.chua_bao_gom_phi_van_chuyen') }}</span> -->
                    </div>
                </div>
                <div v-if="parseInt(order.status.toString()) < appConst.order_status.taken.value"
                    class="h-stack main-stack actions-stack">
                    <button class="reject-button" :disabled="isUpdating" v-on:click="() => {
                        showModalConfirmCancelOrder = true
                    }">
                        {{
                            parseInt(order.status.toString()) == appConst.order_status.waiting.value ?
                                $t('AgentOrderDetailComponent.tu_choi')
                                : parseInt(order.status.toString()) == appConst.order_status.confirmed.value ?
                                    $t('AgentOrderDetailComponent.huy_don')
                                    : $t('AgentOrderDetailComponent.huy_don')
                        }}
                    </button>
                    <button class="accept-button" :disabled="isUpdating" v-on:click="() => {
                        showModalConfirmCancelOrder = false;
                        isUpdating = true;
                        if (parseInt(order.status.toString()) <= appConst.order_status.taken.value) {
                            updateStatusOrder(parseInt(order.status.toString()) + 1);
                        }
                    }">
                        {{
                            parseInt(order.status.toString()) == appConst.order_status.waiting.value
                                ? $t('AgentOrderDetailComponent.nhan_don')
                                : parseInt(order.status.toString()) == appConst.order_status.confirmed.value
                                    ? $t('AgentOrderDetailComponent.da_giao')
                                    : $t('AgentOrderDetailComponent.xong')
                        }}
                    </button>
                </div>

                <div class="h-stack main-stack actions-stack"
                    v-if="parseInt(order.status.toString()) >= appConst.order_status.taken.value">
                    <button class="reject-button view-another" v-on:click="() => {
                        updated = true;
                        router.push({
                            path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', order?.shops?.id),
                            hash: `#waiting`
                        })
                    }">
                        {{ $t('AgentOrderDetailComponent.xem_don_khac') }}
                    </button>
                </div>

                <VueFinalModal class="my-modal-container" content-class="confirm-cancel-order-modal" :overlay-behavior="'persist'"
                    v-model="showModalConfirmCancelOrder" v-on:closed="() => {
                        showModalConfirmCancelOrder = false
                    }" contentTransition="vfm-slide-up">
                    <div class="v-stack cancel-order-content">
                        <span class="cancel-order-title">
                            {{ $t('AgentOrderDetailComponent.tu_choi_nhan_don_hang') }}
                        </span>
                        <span class="cancel-order-message">
                            {{ $t('AgentOrderDetailComponent.huy_don_hang') }}
                            <span class="order-code">
                                {{
                                    order && order.short_code
                                        ? order.short_code
                                        : ""
                                }}
                            </span>
                        </span>
                    </div>
                    <div class="h-stack confirm-modal-buttons">
                        <button class="reject-button" :disabled="isUpdating" v-on:click="() => {
                            showModalConfirmCancelOrder = false
                        }">
                            {{ $t('AgentOrderDetailComponent.khong') }}
                        </button>
                        <button class="accept-button" :disabled="isUpdating" v-on:click="() => {
                            updateStatusOrder(appConst.order_status.cancel.value);
                            showModalConfirmCancelOrder = false;
                        }">
                            {{ $t('AgentOrderDetailComponent.co') }}
                        </button>
                    </div>
                </VueFinalModal>

                <VueFinalModal class="my-modal-container" content-class="my-modal-content-container edit-order-modal" :overlay-behavior="'persist'"
                    v-model="showEditOrderModal" v-on:closed="() => {
                        showEditOrderModal = false
                    }" contentTransition="vfm-slide-up">
                    <AgentEditOrderComponent :orderId="order.id" v-on:close="($event: any) => {
                        if ($event == true) {
                            getDetailOrder(orderId)
                        }
                        showEditOrderModal = false
                    }"></AgentEditOrderComponent>
                </VueFinalModal>

            </div>

            <div class="not-existing-order" v-else-if="!isRefreshing">
                <img loading="lazy" class="empty-image" :src="none_result" :placeholder="none_result" />

                <p class="empty-text">
                    {{ $t('AgentOrderDetailComponent.khong_tim_thay_don_hang') }}
                </p>
                <button class="accept-button" v-on:click="() => {
                    router.push({
                        path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', order?.shops?.id),
                        hash: `#waiting`
                    })
                }">
                    {{ $t('AgentOrderDetailComponent.xem_don_khac') }}
                </button>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_product from '~/assets/image/icon-for-product.png';
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import none_result from "~/assets/image/none-result.webp";
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, zaloConfig, domain, showTranslateProductName, showTranslateProductDescription, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { AgentService } from '~/services/agentService/agentService';
import AgentEditOrderComponent from '../agentEditOrder/AgentEditOrderComponent.vue';
import { HttpStatusCode } from 'axios';

const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    shopData: {},
    updated: null
})
const nuxtApp = useNuxtApp();
const { t } = useI18n()
useSeoMeta({
    title: t('AppRouteTitle.AgentOrderDetailComponent')
});

var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var agentService = new AgentService();
var shopService = new ShopService();
var searchOrderTimeout: any;

var loadMoreTimeOut: any;
var searchTimeout: any;

var showModalConfirmCancelOrder = ref(false);
var orderId = ref((route.params.id || null) as any);
var order = ref();
var isRefreshing = ref(true);
var isUpdating = ref(false);
var showEditOrderModal = ref(false);
var updated = ref((route.params && route.params.updated) ? route.params.updated : false);
var webInApp = ref(null as any);

var showFullProcess = ref(false);
onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
    init()
});
async function init() {
    await getDetailOrder(orderId.value);
    updated.value = props ? props.updated : false
}
function getDetailOrder(id: string) {
    isRefreshing.value = true;
    agentService.agentOrderDetail(id).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isRefreshing.value = false;
            order.value = JSON.parse(JSON.stringify(res.body.data))
        }
        else {
            toast.error(res.body?.message ?? t('AgentOrderDetailComponent.don_hang_khong_ton_tai'));
            isRefreshing.value = false;
            // close();
        }
    })
}
function close() {
    router.back()
    // backHandler();
}

function paymentStatusStyle(status: number) {
    switch (status) {
        case appConst.order_status.waiting.value:
            return "cancel";
        case appConst.order_status.confirmed.value:
            return "cancel";
        // case appConst.order_status.ready.value:
        //     return "cancel";
        case appConst.order_status.taken.value:
            return 'taken';
        case appConst.order_status.return.value:
            return 'return';
        case appConst.order_status.cancel.value:
            return "cancel";
        default:
            return 'taken';
    }
}


function copyToClipboard(text: string) {
    if (!webInApp.value) {
        window.navigator.clipboard.writeText(text);
    }
    else {
        nuxtApp.$emit(appConst.event_key.send_request_to_app, {
            action: appConst.webToAppAction.copyToClipboard,
            data: text
        })
    }
}

async function updateStatusOrder(newStatus: number) {

    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.status = newStatus != 3 ? newStatus : 4;
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    })
    agentService.agentUpdateOrder(orderTmp).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            updated.value = true;
            init();
        }
        else if (res.status == HttpStatusCode.Unauthorized) {
            toast.error(t('AgentOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(res.body?.message ?? t('AgentOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('AgentOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}
</script>

<style lang="scss" src="./AgentOrderDetailStyles.scss"></style>