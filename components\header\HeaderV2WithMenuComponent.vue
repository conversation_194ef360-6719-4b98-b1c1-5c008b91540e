<template>
  <div class='title-v2-with-menu-header' 
    id="header_remagan_v2"
    :class="{
			'hide-mobile': checkHideHeaderMobile(),
      'hide-desktop': checkHideHeaderDesktop(),
      'custom-header-for-temp-1': isCustomHeaderTemp1
		}"
    :style="{
      '--temp-color-1': isCustomHeaderTemp1?._color_1,
      '--temp-color-2': isCustomHeaderTemp1?._color_2
    }"
  >
    <div class="header-left">
      <slot name="header_left" v-if="$slots.header_left"></slot>
      <slot v-else>
        <button class="back-button" v-show="route.path != '/' && route.path != appRoute.HomeComponent" v-on:click="() => {
          router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
        }">
          <Icon name="solar:round-alt-arrow-left-linear"></Icon>
        </button>
        <div v-if="currentLogo?.key == 'shop_logo' && checkActive() == appRoute.DetailShopComponent" class="custom-logo">
          <AvatarComponent class="shop-logo" :imgTitle="currentLogo?.title"
									:imgStyle="currentLogo?.style" :imgSrc="currentLogo?.path?.length
										? currentLogo?.path
										: ''" :width="40" :height="40" />
        </div>
        <nuxt-link v-else class="home-logo header-menu-button" :class="{ 'active': checkActive() == appRoute.HomeComponent }" :to="appRoute.HomeComponent" v-on:click="() => {
          if (checkActive() == appRoute.HomeComponent) {
            nuxtApp.$emit(appConst.event_key.refresh_home)
          }
        }">
          <Icon name="solar:home-smile-bold"></Icon>
          {{ $t("SideBarComponent.trang_chu") }}
          <!-- <img class="logo-header" :src="logo_v2" alt="" /> -->
        </nuxt-link>
        <nuxt-link :to="appRoute.AroundComponent" class='header-menu-button' v-if="!checkWindowWidthMax(1024)"
          :class="{ 'active': checkActive() == appRoute.AroundComponent }">

          <!-- <div class="middle-button">
          <div>
            <Icon name="solar:point-on-map-bold" />
          </div>
        </div> -->
          <Icon name="solar:point-on-map-bold" />
          <span>
            {{ $t("FooterV2Component.gan_day") }}
          </span>

        </nuxt-link>
      </slot>

    </div>
    <!-- <h3 v-if="props.title?.length">{{ props.title }}</h3> -->
    <div class="header-middle" v-if="false">
      <nuxt-link :to="appRoute.HomeComponent" class='footer-button'
        :class="{ 'active': checkActive() == appRoute.HomeComponent }" v-on:click="() => {
          if (checkActive() == appRoute.HomeComponent) {
            nuxtApp.$emit(appConst.event_key.refresh_home)
          }
        }">
        <!-- <img loading="lazy" :src="home_icon" :placeholder="home_icon" alt="" /> -->
        <Icon name="solar:home-smile-bold"></Icon>
        <span>
          {{ $t("FooterV2Component.trang_chu") }}
        </span>
      </nuxt-link>

      <nuxt-link :to="appRoute.ChatManageComponent" class='footer-button'
        :class="{ 'active': checkActive() == appRoute.ChatManageComponent }">
        <Icon name="mynaui:chat-dots" />
        <span>
          {{ $t("FooterV2Component.chat") }}
        </span>
        <em v-if="unreadMessageCount && unreadMessageCount > 0">
          {{ unreadMessageCount <= 10 ? unreadMessageCount : "10+" }} </em>
      </nuxt-link>

      <nuxt-link :to="appRoute.AroundComponent" class='footer-button'
        :class="{ 'active': checkActive() == appRoute.AroundComponent }">

        <!-- <div class="middle-button">
          <div>
            <Icon name="solar:point-on-map-bold" />
          </div>
        </div> -->
        <Icon name="gis:pois-o" />
        <span>
          {{ $t("FooterV2Component.gan_day") }}
        </span>

      </nuxt-link>

      <nuxt-link :to="appRoute.CartComponent" class='footer-button'
        :class="{ 'active': checkActive() == appRoute.CartComponent }">
        <Icon name="solar:cart-3-linear"></Icon>
        <span>
          {{ $t("FooterV2Component.gio_hang") }}
        </span>
        <em v-if="cartData && cartData.length">
          {{ cartData.length <= 10 ? cartData.length : "10+" }} </em>

      </nuxt-link>


      <nuxt-link :to="appRoute.ProfileComponent" class='footer-button'
        :class="{ 'active': checkActive() == appRoute.ProfileComponent }">
        <Icon name="solar:user-circle-linear" />
        <span>
          {{ $t("FooterV2Component.toi") }}
        </span>

      </nuxt-link>
    </div>
    <div class="header-right">
      <slot name="header_right" v-if="$slots.header_right"></slot>
      <!-- <slot v-else>
        <nuxt-link
          :to="userInfo?.role_id == appConst.role_enum.agent ? appRoute.ManageShopsComponent : appRoute.MyShopComponent">
          <button class="open-shop-button">
            <Icon name="solar:shop-2-bold"></Icon>
          </button>

        </nuxt-link>
        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained :closeOnContentClick="false"
          v-model="showNotificationModal">
          <template v-slot:activator="{ props }">
            <button class="notification" v-bind="props">
              <v-badge :content="unreadCount" class="notify-badge" :color="'var(--primary-color-2)'">
                <Icon name="ic:sharp-circle-notifications"></Icon>
              </v-badge>
            </button>
          </template>
<div class="notification-dropdown-container" v-if="true">
  <div class="noti-label">{{ $t('NotificationsComponent.thong_bao') }}
    <button class="refresh-noti" :disabled="refreshing || loadingMore" v-on:click="(e) => {
                e.stopPropagation();
                refreshListNotifications();
              }">
      <Icon :name="refreshing ? 'eos-icons:loading' : 'material-symbols:refresh'"></Icon>
    </button>
  </div>
  <NotificationsComponent :notificationData="JSON.parse(JSON.stringify(notificationData))" :count="count"
    :loadingMore="loadingMore" :goUp="goToTopListNoti" v-on:load_more_noti="async () => {
                if (notificationData.length < count) {
                  loadingMore = true;
                  await getMoreNotifications();
                  loadingMore = false;
                }
              }" :unread_count="unreadCount" v-on:unread_change="() => {
                handleUnreadChange();

              }" v-on:noti_click="() => {
                handleCloseNotificationModal(500)
              }"></NotificationsComponent>
</div>
</v-menu>

</slot> -->
      <slot v-else>


        <v-menu class="bootstrap-dropdown-container hide-mobile" location="bottom right" contained :closeOnContentClick="false"
            v-if="userInfo?.id"
            key="notification_dropdown" v-model="showNotificationModal">
          <template v-slot:activator="{ props }">

            <v-btn class="notification menu-button hide-mobile" flat rounded v-bind="props"
              :title="$t('NotificationsComponent.thong_bao')">
              <v-badge v-if="unreadCount" :content="unreadCount < 9 ? unreadCount : '9+'" class="notify-badge"
                :color="'var(--primary-color-2)'">
              </v-badge>
              <Icon name="ic:sharp-circle-notifications"></Icon>
            </v-btn>

          </template>
          <div class="notification-dropdown-container" v-if="true">
            <div class="noti-label">{{ $t('NotificationsComponent.thong_bao') }}
              <button class="refresh-noti" :disabled="refreshing || loadingMore" v-on:click="(e: any) => {
                e.stopPropagation();
                refreshListNotifications();
              }">
                <Icon :name="refreshing ? 'eos-icons:loading' : 'material-symbols:refresh'"></Icon>
              </button>
            </div>
            <NotificationsComponent :notificationData="JSON.parse(JSON.stringify(notificationData))" :count="count"
              :loadingMore="loadingMore" :goUp="goToTopListNoti" v-on:load_more_noti="async () => {
                if (notificationData.length < count) {
                  loadingMore = true;
                  await getMoreNotifications();
                  loadingMore = false;
                }
              }" :unread_count="unreadCount" v-on:unread_change="() => {
                handleUnreadChange();

              }" v-on:noti_click="() => {
                handleCloseNotificationModal(500)
              }"></NotificationsComponent>
          </div>
        </v-menu>
        
        <!-- Language Selector Button -->
        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained :closeOnContentClick="true"
          key="language_selector_dropdown" v-model="showLanguageSelector">
          <template v-slot:activator="{ props }">
            <v-btn class="language-selector menu-button" flat rounded v-bind="props"
              :title="$t('ProfileComponent.ngon_ngu') || 'Language'">
              <Icon name="material-symbols:language"></Icon>
              <!-- <Icon name="bi:translate" size="25px" /> -->
            </v-btn>
          </template>
          <div class="language-dropdown-container">
            <div class="lang-options-container">
              <button v-for="(lang, index) in langOptions" :key="index" 
                :class="{'active-language': locale === lang.code}"
                class="language-option" 
                v-on:click="changeLanguage(lang)">
                {{ ISO6391.getNativeName(lang.code) }}
              </button>
            </div>
          </div>
        </v-menu>

        <v-btn v-if="userInfo?.id" class='menu-button' :variant="'text'" rounded :title="$t('FooterV2Component.chat')">
          <nuxt-link :to="appRoute.ChatManageComponent" class='footer-button'
            :class="{ 'active': checkActive() == appRoute.ChatManageComponent }">
            <v-badge v-if="unreadMessageCount" :content="unreadMessageCount < 10 ? unreadMessageCount : '9+'"
              class="menu-badge" :color="'var(--primary-color-2)'">
            </v-badge>
            <Icon name="mynaui:chat-dots" />
            <!-- <span>
              {{ $t("FooterV2Component.chat") }}
            </span> -->
            <!-- <em v-if="unreadMessageCount && unreadMessageCount > 0">
              {{ unreadMessageCount <= 10 ? unreadMessageCount : "10+" }} </em> -->
          </nuxt-link>
        </v-btn>

        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained :closeOnContentClick="false"
          key="cart_overview_dropdown" v-model="showCartOverviewModal">
          <template v-slot:activator="{ props }">

            <v-btn class='notification cart-button' :variant="'text'" id="cart_btn" v-bind="props"
              :title="$t('FooterV2Component.gio_hang')">
              <v-badge v-if="cartData?.length" :content="cartData?.length < 10 ? cartData?.length : '9+'"
                class="cart-badge" :color="'var(--primary-color-2)'">
              </v-badge>
              <Icon name="solar:cart-3-linear"></Icon>
            </v-btn>

          </template>
          <div class="cart-overview-dropdown-container">
            <CartOverviewComponent v-on:close="() => {
              showCartOverviewModal = false
            }"></CartOverviewComponent>
          </div>
        </v-menu>


        <v-btn class='menu-button hide-mobile' :variant="'text'" rounded :title="$t('FooterV2Component.toi')">
          <nuxt-link :to="appRoute.ProfileComponent" :class="{ 'active': checkActive() == appRoute.ProfileComponent }">
            <img loading="lazy" v-if="userInfo?.id" :src="userInfo && userInfo.profile_picture
              ? ((appConst.provider_img_domain.some(e => userInfo?.profile_picture?.includes(e))) ? userInfo.profile_picture : domainImage + userInfo.profile_picture)
              : non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" />
            <Icon name="solar:user-circle-linear" v-else />
          </nuxt-link>
        </v-btn>

        <v-btn class="menu-side-bar-button" :variant="'text'" v-on:click="() => {
          showSideBar = true;
        }">
          <v-badge v-if="unreadCount || unreadMessageCount"
            :content="(unreadCount + unreadMessageCount) < 10 ? (unreadCount + unreadMessageCount) : '9+'"
            class="menu-badge" :color="'var(--primary-color-2)'">
          </v-badge>
          <Icon name="famicons:menu"></Icon>
        </v-btn>
      </slot>
    </div>

    <SideBarComponent v-if="showSideBar" v-on:close="handleCloseSideBar" :unread-count="unreadCount"
      :unread-message-count="unreadMessageCount"></SideBarComponent>

    <v-overlay v-model="showCartOverviewModal" v-if="checkWindowWidthMax(1024)" location="bottom" :z-index="1101"
      v-on:click:outside="() => {
        showCartOverviewModal = false
      }" key="show_cart_overview_overlay" class="cart-overview-overlay-container" persistent
      content-class='cart-overview-content-container' no-click-animation>
      <CartOverviewComponent v-on:close="() => {
        showCartOverviewModal = false
      }"></CartOverviewComponent>
    </v-overlay>
  </div>
</template>

<style lang="scss" src="./HeaderV2WithMenuStyles.scss"></style>

<script setup lang="ts">
import logo_v2 from "~/assets/imageV2/remagan-logo-v2.png";
import non_avatar from '~/assets/image/non-avatar.jpg';
import ISO6391 from "iso-639-1";
import { appRoute } from '~/assets/appRoute';
import { appConst, domainImage } from "~/assets/AppConst";
import { ChatService } from "~/services/chatService/chatService";
import { AuthService } from "~/services/authService/authService";
import { UserService } from "~/services/userService/userService";
import { HttpStatusCode } from "axios";


const props = defineProps({
  title: null,
  header_level_2_id: null,
  last_element_id: null
})
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const { t, locale, locales, setLocale } = useI18n();

var chatService = new ChatService();
var authService = new AuthService();
var userService = new UserService();

let lastScrollTop = ref(0);
let cartData = ref();

let unreadMessageCount = ref(0);
var userInfo = ref<any>(null);

var notificationData = useState<any>('user_notification', () => { return [] });
var unreadCount = useState('user_notify_unread', () => { return 0 });
var count = ref(0);
var showNotificationModal = ref(false);
var loadingMore = ref(false);
var refreshing = ref(false);
var goToTopListNoti = ref(0);

var showSideBar = ref(false);
var mobileSideBarNoti = useState<any>('mobile_sidebar_notification', () => { return [] });

var canGoBack = ref(false);

var showCartOverviewModal = ref(false);
var windowInnerWidth = ref();

var showLanguageSelector = ref(false);
const availableLocales = computed(() => {
  return locales.value.filter(i => i.code)
})
var langOptions = ref([] as any);

var scrollUp = ref(false);
var scrollTimer = ref<NodeJS.Timeout | null>(null);
var isScrollingDown = ref(false);

watch(() => [router.currentRoute.value.path], (newValue) => {
  canGoBack.value = router.options.history.state.back != null;
  console.log('new route', newValue, router.options.history.state.back);
})

var currentLogo = ref();
var isCustomHeaderTemp1 = ref<any>(false);

onBeforeMount(async () => {
  userInfo.value = await authService.checkAuth();

  nuxtApp.$listen(appConst.event_key.scrolling, (e) => {
    showHideHeader(e);
  })

  nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
    getCart();
  })

  nuxtApp.$listen(appConst.event_key.cart_add_animation, (params: any) => {
    runAnimationAddToCart(params.containerId, params.imgId)
  })

  nuxtApp.$listen(appConst.event_key.login, async () => {
    userInfo.value = await authService.checkAuth();
    setTimeout(() => {
      getUnreadMessageCount();
      getListNotifications();
    }, 500);

  })
  nuxtApp.$listen(appConst.event_key.logout, async () => {
    userInfo.value = await authService.checkAuth();
    setTimeout(() => {
      getUnreadMessageCount();
      getListNotifications();
    }, 500);
  })

  getCart();

  nuxtApp.$listen(appConst.event_key.check_unread_message, getUnreadMessageCount)
  getUnreadMessageCount();

  nuxtApp.$listen(appConst.event_key.check_user_notification, getListNotifications)
  getListNotifications();

  nuxtApp.$listen('change_left_logo', (e)=>{
    console.log(e);
    currentLogo.value = e;
  })

  nuxtApp.$listen('change_header_for_shop_temp_1', (res:any)=>{
    isCustomHeaderTemp1.value = res
  });
})

onMounted(() => {
  window.addEventListener('resize', () => {
    windowInnerWidth.value = window.innerWidth;
  })
  windowInnerWidth.value = window.innerWidth;
  
  langOptions.value = availableLocales.value.map(el => {
    return el
  })
})

onBeforeUnmount(() => {
  // Clean up scroll timer
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }
  
  nuxtApp.$unsubscribe(appConst.event_key.check_unread_message, getUnreadMessageCount);
  nuxtApp.$unsubscribe(appConst.event_key.check_user_notification, getListNotifications);
  nuxtApp.$unsubscribe(appConst.event_key.login);
  nuxtApp.$unsubscribe(appConst.event_key.logout);
  nuxtApp.$unsubscribe(appConst.event_key.cart_add_animation)
})

function changeLanguage(lang:any) {
  setLocale(lang.code)
  if (userInfo.value?.id) {
    userService.switchLanguage(lang.code);
  }
  localStorage.setItem(appConst.storageKey.language, lang.code);
  nuxtApp.$emit(appConst.event_key.send_request_to_app, {
    action: appConst.webToAppAction.setLanguage,
    data: lang.code
  })
  showLanguageSelector.value = false;
}

function showHideHeader(event: any) {
  console.log(event?.target?.scrollTop);
  
  let scrollTop = event?.target?.scrollTop;

  // Clear existing timer
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }

  if (scrollTop > lastScrollTop.value) {
    console.log('Scrolling down');
    
    // Set flag immediately but delay the actual hide
    isScrollingDown.value = true;
    
    // Add delay before hiding header when scrolling down
    scrollTimer.value = setTimeout(() => {
      if (isScrollingDown.value) {
        scrollUp.value = true;
      }
    }, 300); // 300ms delay for scrolling down
  }
  else {
    console.log('Scrolling up');
    scrollTimer.value = setTimeout(() => {

      // Show header immediately when scrolling up
      isScrollingDown.value = false;
      scrollUp.value = false;
    }, 200); // 300ms delay for scrolling down
  }

  lastScrollTop.value = Math.max(scrollTop, 0);
}

const getListNotifications = () => {

  return new Promise((resolve) => {
    if (userInfo.value?.id) {
      userService.listNotify(0, notificationData.value?.length > 20 ? notificationData.value?.length : 20).then((res) => {
        if (res.status == HttpStatusCode.Ok) {
          notificationData.value = JSON.parse(JSON.stringify(res.body.data));
          count.value = res.body.count;
          unreadCount.value = res.body.unread;
          resolve(notificationData.value)
        }
        else {
          notificationData.value = [];
          count.value = 0;
          unreadCount.value = 0
          resolve(null);
        }
      }).catch(() => {
        notificationData.value = [];
        count.value = 0;
        unreadCount.value = 0
        resolve(null);
      })
    }
    else {
      notificationData.value = [];
      count.value = 0;
      unreadCount.value = 0
      resolve(null);
    }

  })
}

async function refreshListNotifications() {
  refreshing.value = true;
  await getListNotifications();
  goToTopListNoti.value = goToTopListNoti.value + 1;
  refreshing.value = false;
}

function getMoreNotifications() {
  return new Promise((resolve) => {
    userService.listNotify(notificationData.value?.length).then((res) => {
      if (res.status == HttpStatusCode.Ok) {
        notificationData.value = [...notificationData.value, ...res.body.data]
        count.value = res.body.count;
        unreadCount.value = res.body.unread;
        resolve(notificationData.value)
      }
    })
  })
}

async function handleUnreadChange() {
  await getListNotifications();
  // if (data.item == 'all') {
  //   getListNotifications()
  // }
  // else {
  //   let indexChange = notificationData.value.findIndex(function (e: any) {
  //     return e.id == data.item?.id
  //   })

  //   if (indexChange != -1) {
  //     notificationData.value[indexChange] = { ...data.item };
  //   }
  //   unreadCount.value = data.unread_count;
  //   count.value = data.count;
  // }
}

function handleCloseNotificationModal(delay: number) {
  setTimeout(() => {
    showNotificationModal.value = false;
  }, delay);
}
function checkActive() {
  if (route.path.includes(appRoute.AroundComponent)) return appRoute.AroundComponent;
  if (route.path.includes(appRoute.HomeComponent)) return appRoute.HomeComponent;
  if (route.path.includes(appRoute.ProfileComponent)) return appRoute.ProfileComponent;
  if (route.path.includes(appRoute.CartComponent)) return appRoute.CartComponent;
  if (route.path.includes(appRoute.SearchComponent)) return appRoute.SearchComponent;
  if (route.path.includes(appRoute.ReelsComponent)) return appRoute.ReelsComponent;
  if (route.path.includes(appRoute.ChatManageComponent)) return appRoute.ChatManageComponent;
  if (route.path.includes(appRoute.DetailShopComponent)) return appRoute.DetailShopComponent;
  return '';
}

function checkHideHeaderMobile() {
	if (route.path.includes(appRoute.AroundComponent)) return true;
	if (route.path.includes(appRoute.ProfileComponent)) return true;
	if (route.path.includes(appRoute.CartComponent)) return true;
	if (route.path.includes(appRoute.ChatManageComponent)) return true;
  // if (route.path.includes(appRoute.HomeComponent)) return true;
	// if (route.path.includes(appRoute.ReelsComponent)) return true;
	return false;
}

function checkHideHeaderDesktop() {
  if(route.path.includes(appRoute.DetailShopComponent)){
    return scrollUp.value
  }
	return false
}

const getUnreadMessageCount = () => {
  return new Promise(async (resolve) => {
    // chatService.listChannel(profileInfo.value?.id, member_type.user).then((listChannel) => {
    // 	if (listChannel.status == HttpStatusCode.Ok) {
    // 		unreadMessageCount.value = listChannel.data.unread_count;
    // 		resolve(listChannel.data);
    // 	}
    // 	else {
    // 		resolve(null);
    // 	}

    // }).catch(() => {
    // 	resolve(null);
    // });
    if (userInfo.value?.id) {
      chatService.countUnreadMessage().then((res) => {
        unreadMessageCount.value = (res.body.data.user ?? 0) + (res.body.data.shop ?? 0) + (res.body.data.agent ?? 0);
        resolve(res);
      }).catch(() => {
        unreadMessageCount.value = 0;
        resolve(null);
      })
    }
    else {
      unreadMessageCount.value = 0;
      resolve(null)
    }
  })
}

function getCart() {
  let cartData$ = JSON.parse(
    localStorage.getItem(appConst.storageKey.cart) as string
  );
  cartData.value = cartData$;
}

function runAnimationAddToCart(containerElId: string, imgElId: string) {
  let valueElement = document.getElementById(containerElId) as HTMLElement;

  let imgEl = document.getElementById(imgElId) as HTMLElement;
  let elClone = imgEl.cloneNode(true) as HTMLElement;
  elClone.style.background = 'white';
  valueElement.appendChild(elClone);

  let begintop = imgEl?.offsetTop;
  let beginLeft = imgEl?.offsetLeft;
  let beginWidth = imgEl?.offsetWidth;

  let destinationElement = document.getElementById('cart_btn');
  let destinationTop = destinationElement?.getBoundingClientRect().top;
  let destinationLeft = destinationElement?.getBoundingClientRect().left;

  elClone.style.position = 'absolute'
  elClone.style.top = begintop?.toString() + "px";
  elClone.style.left = (beginLeft)?.toString() + "px";
  elClone.style.zIndex = '10000';
  elClone.style.transition = 'scale .3s ease-in-out, top 2s ease, left 2s ease'

  elClone.style.borderRadius = '100px'
  setTimeout(() => {
    elClone.style.scale = '.15'
    elClone.style.top = `${destinationTop}px`;
    elClone.style.left = `${destinationLeft}px`;

    setTimeout(() => {
      elClone.remove();
      document.getElementById('cart_btn')?.classList.add('shake');
      setTimeout(() => {
        document.getElementById('cart_btn')?.classList.remove('shake')
      }, 500);
    }, 400)

  }, 200);
}

const handleCloseSideBar = () => {
  setTimeout(() => {
    showSideBar.value = false;
  }, 300);
}

function checkWindowWidthMax(width: any) {
  if (windowInnerWidth.value <= width) return true;
  return false;
}
</script>
