<template>
    <div class="check-quotation-container">
        <SubHeaderV2Component :title="$t('CheckQuotationComponent.kiem_tra_bao_gia')">
            <!-- <template v-slot:header_left>
                <button class="back-button" v-on:click="() => {
                    close();
                }">
                    <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                </button>
            </template> -->
        </SubHeaderV2Component>
        <div class="check-quotation-content-container">
            <v-row class="header-check" justify="center">
                <v-col cols="12" md="4" lg="4" sm="12" class="expiration">
                    <div class="select-expire-date">
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained
                            v-model="menuFrom" :closeOnContentClick="false" key="expire_from">
                            <template v-slot:activator="{ props }">
                                <button class="select-date" v-bind="props">
                                    {{ $t('CheckQuotationComponent.tu') }}: {{ moment(from).isValid() ?
                                        moment(from).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :max="to" show-adjacent-months v-model:modelValue="from"
                                v-on:update:model-value="() => {
                                    // validateFrom();
                                    menuFrom = false;
                                }"></v-date-picker>
                        </v-menu>
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained v-model="menuTo"
                            :closeOnContentClick="false">
                            <template v-slot:activator="{ props }" key="expire_to">
                                <button class="select-date" v-bind="props">
                                    {{ $t('CheckQuotationComponent.den') }}: {{ moment(to).isValid() ?
                                        moment(to).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :min="from" show-adjacent-months v-model:modelValue="to" cl
                                v-on:update:model-value="() => {
                                    // validateTo();
                                    menuTo = false;
                                }"></v-date-picker>
                        </v-menu>
                    </div>
                    <v-btn class="select-from-file-btn" :loading="submitting" :disabled="submitting || !material_ids.length" variant="outlined" v-on:click="() => {
                        checkQuotationSubmit()
                    }">
                        {{ $t('CheckQuotationComponent.xem') }}
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="material" v-show="expandHeader">
                <label for="select_material" class="select-material-label">{{ t('CheckQuotationComponent.nguyen_lieu')
                }}</label>
                <v-autocomplete label="" :filter-mode="'intersection'" class="custom-v-select material-select" clearable
                    chips multiple closable-chips :menu-props="{
                        contentClass: 'text-center'
                    }" id="select_material" v-on:update:model-value="($event) => {
                        material_ids = $event;
                    }" :placeholder="$t('CheckQuotationComponent.chon_nguyen_lieu')" v-model="material_ids"
                    :item-value="'id'" :menu-icon="''" :items="data_material" variant="plain" :custom-filter="(item: any, queryText: any, itemObj: any) => {
                        let name = nonAccentVietnamese(item).toLocaleLowerCase();
                        let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
                        return name.includes(query)
                    }" :item-title="'name'">
                    <template v-slot:chip="{ props, item }">
                        <v-chip v-bind="props" class="chip-material">{{ item.title }}</v-chip>
                    </template>
                    <template v-slot:append-inner></template>
                </v-autocomplete>
            </v-row>
            <v-btn class="expand-header-import" v-on:click="() => {
                collapseHeader()
            }">{{ expandHeader ? $t('CheckQuotationComponent.thu_gon') : $t('CheckQuotationComponent.mo_rong')
                }}</v-btn>
            <v-data-table class="data-check" v-if="material_ids?.length && data_quotations?.length" v-model:page="current_data_page"
                v-model:items-per-page="item_per_page" :headers="[
                    ...Object.keys(data_field_structor).map((key, index) => {
                        return {
                            value: key,
                            title: t(`CheckQuotationComponent.${(data_field_structor as any)?.[key].label_key}`),
                        }
                    }),
                    {
                        value: 'supplier',
                        title: t(`CheckQuotationComponent.nha_cung_cap`),
                    },
                ]" :items="data_quotations">
                <template v-slot:headers="{ columns }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="head-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :class="{
                                    'short': column.value == data_field_structor.stt.label_key,
                                    'auto': column.value == 'action'
                                }">
                                <span class="cursor-pointer">{{ column.title }}</span>
                            </th>
                        </template>
                    </tr>
                </template>
                <template v-slot:item="{ item, columns, index }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="body-col stt" v-if="column.value == data_field_structor.stt?.label_key"
                                :id="`stt_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <span>{{ index + (current_data_page - 1) * item_per_page + 1 }}</span>
                            </th>
                            <th class="body-col" v-else-if="column.value == 'price'"
                                :id="`price_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <p v-for="(itemQuotation, indexQuo) in (item as any).material_quotations"
                                    class="price"
                                    :class="{
                                        'max': checkMaxMin((item as any).material_quotations, indexQuo, true) && (item as any).material_quotations?.length > 1,
                                        'min': checkMaxMin((item as any).material_quotations, indexQuo, false)
                                    }"
                                >
                                    {{formatCurrency(itemQuotation.price)}}
                                    
                                </p>
                            </th>
                            <th class="body-col" v-else-if="column.value == 'supplier'"
                                :id="`supplier_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <nuxt-link 
                                    :to="itemQuotation.quotation?.supplier?.phone?.length 
                                        ? `tel:${itemQuotation.quotation?.supplier?.phone}` 
                                        : '#'" 
                                    v-for="(itemQuotation, indexQuo) in (item as any).material_quotations"
                                    class="supplier"
                                >
                                    <span>{{itemQuotation.quotation?.supplier?.name}}</span>
                                    <!-- <nuxt-link v-if="itemQuotation.quotation?.supplier?.phone?.length" :to="`tel:${itemQuotation.quotation?.supplier?.phone}`">
                                        <Icon name="mdi-light:phone"></Icon>
                                    </nuxt-link> -->
                                </nuxt-link>
                            </th>
                            <th class="body-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :id="`body_long_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`"
                                :class="{
                                    'long-text': ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name?.label_key)
                                        || ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name_en?.label_key)
                                }" v-else>
                                <span>{{ (item as any)[column.key ?? ''] }}</span>
                            </th>

                        </template>
                    </tr>
                </template>
            </v-data-table>
            <div class="none-data" v-else>
                <img :src="none_list_quotations" alt="">
                {{ $t('CheckQuotationComponent.chua_co_du_lieu') }}
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import no_data_found from "~/assets/imageV2/nodata-found.png"
import none_list_quotations from "~/assets/image/list-empty-2.jpg"
import { domainImage, appConst, nonAccentVietnamese, validPhone, formatCurrency } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { HttpStatusCode } from "axios";
import { QuotationService } from "~/services/quotationService/quotationService";
import { ShopService } from "~/services/shopService/shopService";
import { DataFieldStructor, type MaterioDto, type SupplierDto } from "../QuotationDTO";
import { SupplierService } from "~/services/quotationService/supplierService";
import { MaterialService } from "~/services/quotationService/materialService";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var quotationService = new QuotationService();
var supplierService = new SupplierService();
var materialService = new MaterialService();
var shopService = new ShopService();

var name = ref<string>("");
var nameErr = ref(false);
var notes = ref<string>("");
var from = ref<string | null>(null);

var to = ref<string | null>(null);

var data_field_structor = ref<DataFieldStructor>(new DataFieldStructor());

var data_supplier = ref<SupplierDto[]>([]);
var data_material = ref<MaterioDto[]>([]);

var expandHeader = ref(true);

var current_data_page = ref(1);
var item_per_page = ref(10);

var material_ids = ref<any>([]);
var materials_to_request = ref<MaterioDto[]>([]);

var data_quotations = ref<any>();

var menuFrom = ref(false);
var menuTo = ref(false);

var submitting = ref(false);
var showAddSupplierModal = ref(false);

onMounted(async () => {
    window.addEventListener('resize', () => {
        if (window.innerWidth > 961) {
            expandHeader.value = true;
        }
    })
    Object.keys(data_field_structor.value).map(async (key: string) => {
        (data_field_structor.value as any)[key].value = key;
    })
    delete data_field_structor.value.material_id;
    delete data_field_structor.value.price_vat;
    delete data_field_structor.value.notes;

    console.log(data_field_structor.value);
    getListMaterial();
    await getListSupplier();

    useSeoMeta({
        title: t('CheckQuotationComponent.kiem_tra_bao_gia')
    })
})

onBeforeUnmount(() => {
});

function getListSupplier() {
    return new Promise((resolve) => {
        supplierService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_supplier.value = JSON.parse(JSON.stringify(res.body.data))
            }
            resolve(data_supplier.value);
        }).catch(() => {
            toast.error(t('CheckQuotationComponent.lay_danh_sach_nha_cung_cap_that_bai'))
        })
    })

}

function getListMaterial() {
    return new Promise((resolve) => {
        materialService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_material.value = JSON.parse(JSON.stringify(res.body.data));
            }
            resolve(data_material.value);
        })
    })

}

function collapseHeader() {
    if (window.innerWidth > 961) {
        expandHeader.value = true;
    }
    else {
        expandHeader.value = !expandHeader.value
    }
}

function checkQuotationSubmit() {
    submitting.value = true;
    quotationService.quotationFilter(
        material_ids.value,
        moment(from.value).isValid() ? moment(from.value).format("YYYY-MM-DD") : null,
        moment(to.value).isValid() ? moment(to.value).format("YYYY-MM-DD") : null,
    ).then(res=>{
        if(res.status == HttpStatusCode.Ok){
            data_quotations.value = JSON.parse(JSON.stringify(res.body.data))
        }
        else {
            toast.error(t('CheckQuotationComponent.kiem_tra_that_bai'))
        }
        submitting.value = false;
    }).catch(()=>{
        toast.error(t('CheckQuotationComponent.kiem_tra_that_bai'))
        submitting.value = false;
    })
}

function checkMaxMin(listQuote:any, indexCheck: number, is_max=false){
    let listPrice = listQuote.map((e:any)=> {
        return parseFloat(e.price);
    });

    let maxMinPrice = is_max ? Math.max(...listPrice) : Math.min(...listPrice);

    return parseFloat(listQuote[indexCheck].price) == maxMinPrice;
}

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.QuotationComponent)
}
</script>

<style lang="scss" src="./CheckQuotationStyles.scss"></style>
