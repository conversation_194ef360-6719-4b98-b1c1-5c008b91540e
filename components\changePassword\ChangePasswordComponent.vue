<template>
  <div class="public-container">
    <div class="change-password-container">
      <SubHeaderV2Component :title="title">
      </SubHeaderV2Component>
      <div class="change-password-content">
        <img :src="change_password"/>

        <div class='h-stack stack-content'>
          <span class='label'>
            {{ $t('ChangePasswordComponent.ten_dang_nhap') }}:
          </span>
          <input name='email' maxLength=255 class='content-input user-name' :value="profileData?.user_name" readonly />
        </div>
        <div class='v-stack stack-content'>
          <span class='label required'>
            {{ $t('ChangePasswordComponent.mat_khau_moi') }}:
          </span>
          <div class='content-input-group'>
            <input name="password" maxLength=255 class='content-input'
              :placeholder="$t('ChangePasswordComponent.nhap_mat_khau')" :type="newPasswordShow ? 'text' : 'password'"
              v-model="newPassword" :disabled="isSubmiting" v-on:input="($event: any) => {
                newPassword = $event.currentTarget.value;
                passwordValidation();
              }" v-on:blur="() => { passwordValidation(); }" />
            <button v-on:click="() => { newPasswordShow = !newPasswordShow }">
              <Icon name="mage:eye-closed" v-if="!newPasswordShow" />
              <Icon name="mage:eye" v-if="newPasswordShow" />
            </button>
          </div>
          <span class="error-message" :class="{ 'success': newPasswordErr1 == false }">
            <Icon name="ph:dot-outline-fill" v-if="newPasswordErr1"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('ChangePasswordComponent.phai_chua_so_chu_cai_ky_tu_dac_biet') }}&nbsp;
            <v-tooltip location="bottom" open-on-click
              :text="`${$t('ChangePasswordComponent.ky_tu_dac_biet')}: !@#$%^&*()_+-=[]{};:|,.<>?\\`">
              <template v-slot:activator="{ props }">
                <span v-bind="props">
                  <Icon name="bi:question-circle"></Icon>
                </span>
              </template>
            </v-tooltip>
          </span>
          <span class="error-message" :class="{ 'success': newPasswordErr2 == false }">
            <Icon name="ph:dot-outline-fill" v-if="newPasswordErr2"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('ChangePasswordComponent.phai_tu_6_20_ky_tu') }}
          </span>
        </div>
        <div class='v-stack stack-content'>
          <span class='label required'>
            {{ $t('ChangePasswordComponent.xac_nhan_mat_khau') }}
          </span>
          <div class='content-input-group'>
            <input  maxLength=255 class='content-input'
              :placeholder="$t('ChangePasswordComponent.nhap_lai_mat_khau')"
              :type="passwordConfirmationShow ? 'text' : 'password'" :value="passwordConfirmation"
              :disabled="isSubmiting" v-on:input="($event: any) => {
                passwordConfirmation = $event.currentTarget.value;
                confirmPasswordValidation();
              }" v-on:blur="() => { confirmPasswordValidation(); }" />
            <button v-on:click="() => { passwordConfirmationShow = !passwordConfirmationShow }">
              <Icon name="mage:eye-closed" v-if="!passwordConfirmationShow" />
              <Icon name="mage:eye" v-if="passwordConfirmationShow" />
            </button>
          </div>
          <span class='error-message'>{{ passwordConfirmationErr }}</span>
        </div>
        <button class='button-action' :disabled="isSubmiting" v-on:click="() => { submit() }">
          {{ $t('ChangePasswordComponent.luu') }}
        </button>
      </div>
    </div>
  </div>

</template>

<style lang="scss" src="./ChangePasswordStyles.scss"></style>

<script setup lang="ts">
import { appConst, appDataStartup, encryptPassword } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import Vue3Toastify, { toast } from "vue3-toastify";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import { PublicService } from "~/services/publicService/publicService";

import change_password from "~/assets/image/change-password.png"
import { HttpStatusCode } from "axios";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();

let authService = new AuthService();
let userService = new UserService();
let publicService = new PublicService();
let placeService = new PlaceService();
let shopService = new ShopService();
let productService = new ProductService();

let title = ref(t('ChangePasswordComponent.doi_mat_khau'));

let currentPassword = ref("");
let currentPasswordShow = ref(false);
let newPassword = ref("");
let newPasswordShow = ref(false);
let passwordConfirmation = ref("");
let passwordConfirmationShow = ref(false);

var currentPasswordErr = ref("")
var newPasswordErr1 = ref(true);
var newPasswordErr2 = ref(true);
let passwordConfirmationErr = ref("");

let profileData = ref(null as any);

let isSubmiting = ref(false);

onUnmounted(async () => { });
onMounted(async () => {
  useSeoMeta({
    title: title.value,
  });

  let profile$ = JSON.parse(localStorage.getItem(appConst.storageKey.userInfo) as string);
  if (profile$) {
    profileData.value = profile$;
  }
  else {
    router.push(appRoute.LoginComponent);
  }
});

async function submit() {
  if (
    await validateForm() == true
  ) {
    isSubmiting.value = true;
    newPasswordShow.value = false;
    userService.updatePasswordClient(await encryptPassword(newPassword.value)).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        toast.success(t('ChangePasswordComponent.doi_mat_khau_thanh_cong'));
        nuxtApp.$emit(appConst.event_key.user_info_change);
        // isSubmiting.value = false;
        setTimeout(() => {
          router.push(appRoute.ProfileComponent)          
        }, 1500);
      }
      else {
        toast.error(res.body?.message ?? t('ChangePasswordComponent.doi_mat_khau_that_bai'));
        hightlightError();
        isSubmiting.value = false;
        newPasswordShow.value = true;
      }
    })
  }
  else {
    // toast.error("Đăng ký thất bại\nVui lòng kiểm tra lại");
    hightlightError();
    isSubmiting.value = false;
  }
}

function validateForm() {

  passwordValidation();
  confirmPasswordValidation();

  if (
    !newPasswordErr1.value &&
    !newPasswordErr2.value &&
    !passwordConfirmationErr.value?.length
  ) return true;
  return false;
}
function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    // Do stuff here
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}
function passwordValidation() {
  let re = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{1,}$/;
  if (!re.test(newPassword.value)) {
    newPasswordErr1.value = true;
  }
  else {
    newPasswordErr1.value = false;
  }
  if (newPassword.value?.length < 6 || newPassword.value?.length > 20) {
    newPasswordErr2.value = true;
  }
  else {
    newPasswordErr2.value = false;
  }
}

function confirmPasswordValidation() {
  if (!passwordConfirmation.value || !passwordConfirmation.value?.length) {
    passwordConfirmationErr.value = t('ChangePasswordComponent.vui_long_nhap_mat_khau_xac_nhan');
  } else if (passwordConfirmation.value != newPassword.value) {
    passwordConfirmationErr.value = t('ChangePasswordComponent.nhap_lai_mat_khau_khong_khop');
  } else {
    passwordConfirmationErr.value = "";
  }
}

function close() {
  router.options.history.state.back
    ? router.back()
    : router.push(appRoute.HomeComponent);
}
</script>
