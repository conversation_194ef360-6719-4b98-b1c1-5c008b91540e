<template>
	<div class="public-container">
		<div class="chat-manage-container">
			<div class="chat-manage-sticky-header">
				<SubHeaderV2Component class="chat-manage-header" :title="$t('AppRouteTitle.ChatManageComponent')">
					<template v-slot:header_middle>
						<h3>{{ $t('AppRouteTitle.ChatManageComponent') }}</h3>
					</template>
					<template v-slot:header_right>
						<!-- Bạn có thể thêm nội dung cho header_right nếu cần -->
						<button :title="t('ChatManageComponent.chon_cua_hang')" v-if="listShopManaged?.length"
							v-on:click="() => {
								if (listShopManaged?.length > 0) showSelectShopModal = true
							}">
							<Icon name="mdi:shop-settings-outline"></Icon>
						</button>
					</template>
				</SubHeaderV2Component>
				<div class="tab-header">
					<v-tabs v-model="tabIndex" draggable="false" class="manage-tab" color="var(--primary-color-1)">
						<v-tab v-for="(key, indexTab) in Object.keys(tabList)" class="manage-tab-title"
							:class="tabIndex == tabList[key].key ? 'active' : ''" :value="tabList[key].key"
							:key="tabList[key].key" v-on:click="() => {
								tabIndex = tabList[key].key;
								changeTab()
							}">
							<div class="tab-title">
								{{ $t('ChatManageComponent.' + tabList[key].key) }}
								<em v-if="key == 'user' && personalUnreadCount">{{ personalUnreadCount }}</em>
								<em v-if="key == 'shop' && shopUnreadCount">{{ shopUnreadCount }}</em>
							</div>
						</v-tab>
					</v-tabs>
				</div>
				<span v-if="currentShopData?.id"
					v-html="$t('ChatManageComponent.ban_dang_xem_tin_nhan_cua', { shop_name: ':shop_name' }).replaceAll(':shop_name', `<em>${currentShopData.name}</em>`)"></span>
			</div>

			<div class="chat-manage-content-container" v-if="!isRefreshing">
				<div class="list-chat empty" v-if="isErrored">
					<Icon name="weui:error-outlined"></Icon>
					<span>{{ $t('ChatManageComponent.co_loi_xay_ra') }}</span>
					<button v-on:click="() => {
						reloadNuxtApp()
					}">
						{{ $t('ChatManageComponent.tai_lai') }}
					</button>
				</div>
				<NoneMyShopComponent
					v-else-if="noneShop && (object_chat == obj_chat.my_shop || object_chat == obj_chat.agent_shop)"
					:show_header="false" :mode="object_chat == obj_chat.agent_shop ? 'agent' : null"
					:message="object_chat == obj_chat.agent_shop ? $t('ChatManageComponent.cua_hang_khong_ton_tai_hoac_ngung_hoat_dong') : $t('MyShopComponent.ban_chua_co_cua_hang')">
				</NoneMyShopComponent>
				<!-- <div class="list-chat empty"
					v-else-if="noneShop && (object_chat == obj_chat.my_shop || object_chat == obj_chat.agent_shop)">
					<img loading="lazy" :src='none_shop' :placeholder="none_shop"
						:alt="$t('MyShopComponent.chua_dang_ky_cua_hang')" />

					<span>
						{{ $t('MyShopComponent.ban_chua_co_cua_hang') }}
					</span>

					<nuxt-link :to="{
						path: appRoute.RegisterShopComponent
					}">
						<button>
							{{ $t('MyShopComponent.dang_ky_ngay') }}
						</button>
					</nuxt-link>
				</div> -->
				<div class="list-chat" v-else-if="listChannel.length > 0">
					<div v-on:click="() => {
						if (itemChannel.members?.[0]?.member?.id) {
							selectedChatChannel = JSON.parse(JSON.stringify(itemChannel));
							showChatDetail = true;
							router.replace({
								path: route.path,
								query: {
									...route.query,
									channel_id: itemChannel.id
								}
							})
						}
						else {
							toast.warning($t('ChatManageComponent.tai_khoan_khong_ton_tai'))
						}

					}" class="chat-item" :class="{ 'not-read': itemChannel.viewed == false }" v-for="itemChannel of listChannel">
						<div class="avatar-chat" v-if="itemChannel.type == channel_type.user">
							<!-- <img :src="itemChannel.members?.[0]?.member?.profile_picture
								? itemChannel.members?.[0]?.member?.profile_picture
								: non_avatar" class="single"> -->

							<img v-if="itemChannel?.members?.[0]?.member_type == member_type.user" :src="itemChannel?.members?.[0]?.member?.profile_picture
								? ((appConst.provider_img_domain.some(e => itemChannel?.members?.[0]?.member?.profile_picture?.includes(e)))
									? itemChannel?.members?.[0]?.member?.profile_picture
									: (domainImage + itemChannel?.members?.[0]?.member?.profile_picture)
								)
								: non_avatar" class="single" />
							<div class="single" v-if="itemChannel?.members?.[0]?.member_type == member_type.shop">
								<AvatarComponent :imgTitle="itemChannel?.members?.[0]?.member?.name"
									:imgStyle="itemChannel?.members?.[0]?.member?.logo?.style" :imgSrc="itemChannel?.members?.[0]?.member?.logo?.path?.length
										? (domainImage + itemChannel?.members?.[0]?.member?.logo?.path)
										: ''" :width="55" :height="55" />
							</div>
						</div>
						<div v-else class='avatar-chat group'>
							<img v-if="itemChannel.avatar" :src="itemChannel.avatar" class="single" />
							<div v-else class="multiple">
								<img :id="`${itemChannel.id}_${itemChannel.members?.length}_${indexMember}`"
									:hidden="indexMember > 2" v-for="(memberObj, indexMember) in itemChannel.members"
									:src="memberObj?.member?.profile_picture ? memberObj?.member?.profile_picture : non_avatar"
									class="single" :class="{ 'odd': indexMember % 2 != 0 }" :style="{
										zIndex: itemChannel.members?.length - indexMember
									}" />
								<div class="count single" v-show="itemChannel.members!.length > 3">
									{{ itemChannel.members!.length }}
								</div>
							</div>

						</div>
						<div class="content-chat">
							<span class="name">{{ itemChannel.name ?? itemChannel.members?.[0]?.member?.name ??
								$t('ChatManageComponent.tai_khoan_khong_ton_tai') }}</span>
							<span class="last-message"
								v-if="itemChannel.latest_message && itemChannel.latest_message?.status != 3 && itemChannel.latest_message?.status != 2">
								<em v-if="itemChannel.latest_message?.member_id == memberId">{{
									$t('ChatManageComponent.ban') }}:</em>
								<em v-else-if="itemChannel.type == channel_type.group">{{
									itemChannel.members?.[0]?.member?.name
								}}:</em>
								&nbsp;
								<span
									v-if="itemChannel.latest_message?.content?.text?.length && itemChannel.latest_message?.member_id != memberId">
									{{
										itemChannel.latest_message?.content?.translate?.[locale]
										?? itemChannel.latest_message?.content?.text
										?? JSON.parse(itemChannel.latest_message?.content)?.translate[locale]
										?? JSON.parse(itemChannel.latest_message?.content)?.text
									}}
								</span>
								<span
									v-else-if="itemChannel.latest_message?.content?.text?.length && itemChannel.latest_message?.member_id == memberId">
									{{
										itemChannel.latest_message?.content?.text
										?? JSON.parse(itemChannel.latest_message?.content)?.text
									}}
								</span>
								<span v-else-if="itemChannel.latest_message?.content?.image?.length">
									[{{ $t('ChatManageComponent.hinh_anh') }}]
								</span>
								<span v-else-if="itemChannel.latest_message?.content?.link_product?.length">
									[{{ $t('ChatManageComponent.san_pham') }}]
								</span>
								<span v-else-if="itemChannel.latest_message?.content?.link_order?.length">
									[{{ $t('ChatManageComponent.don_hang') }}]
								</span>

							</span>
							<span class="last-message" v-else>
								<span>{{ $t('ChatManageComponent.tin_nhan_da_duoc_thu_hoi') }}</span>
							</span>
						</div>
						<div class="secondary-info">
							<span class="last-message-time" v-if="itemChannel.latest_message">
								{{ showTimeLastMessage(itemChannel.latest_message?.created_at) }}
							</span>
							<button class="actions" v-if="false">
								<Icon name="solar:menu-dots-bold"></Icon>
							</button>
						</div>
					</div>
					<div class="end-list" v-if="listChannel.length == listChannelCount">
						{{ $t('ChatManageComponent.da_hien_thi_het_tin_nhan') }}
					</div>
				</div>
				<div class="list-chat empty" v-else>
					<img :src="list_empty" />
					<span>{{ $t('ChatManageComponent.chua_co_cuoc_tro_chuyen') }}</span>
				</div>


			</div>

			<div class="chat-manage-content-container skeletons" v-else>
				<div class="list-chat">
					<v-skeleton-loader type="list-item-avatar-two-line" :width="'100%'"></v-skeleton-loader>
					<v-skeleton-loader type="list-item-avatar-two-line" :width="'80%'"></v-skeleton-loader>
					<v-skeleton-loader type="list-item-avatar-two-line" :width="'90%'"></v-skeleton-loader>
					<v-skeleton-loader type="list-item-avatar-two-line" :width="'50%'"></v-skeleton-loader>
					<v-skeleton-loader type="list-item-avatar-two-line"></v-skeleton-loader>
				</div>
			</div>


		</div>
		<v-overlay v-model="showChatDetail" :close-on-back="true" :z-index="100" :absolute="false" :contained="true"
			:key="`show_chat_detail_${selectedChatChannel?.id}`" class="chat-detail-overlay-container"
			content-class='chat-detail-modal-container' no-click-animation v-on:click:outside="() => {
				selectedChatChannel = null;
				showChatDetail = false;
				let currentQuery = JSON.parse(JSON.stringify(route.query));
				delete currentQuery['channel_id'];
				router.replace({
					query: currentQuery
				})
			}">
			<ChatDetailComponent v-if="showChatDetail" :receiver_id="selectedChatChannel?.id"
				:id="`chat_detail_${selectedChatChannel?.id}`" :chat_info="selectedChatChannel" :receiver_type="false"
				:mode="object_chat == obj_chat.user
					? member_type.user
					: object_chat == obj_chat.my_shop ? member_type.shop
						: member_type.agent" v-on:readAll="async (e: any) => {
							await handleGetListChatChannel()
						}" v-on:close="() => {
							showChatDetail = false;
							selectedChatChannel = null;
							let currentQuery = route.query;
							delete currentQuery['channel_id'];
							router.replace({
								query: currentQuery
							})
						}"></ChatDetailComponent>
		</v-overlay>

		<CustomSelectComponent v-if="showSelectShopModal" :_key="'select_shop_chat_manage'"
			:list_item="listShopManaged ?? []" :field_value="'id'" :field_title="'name'" :multiple="false"
			:title="$t('ChatManageComponent.chon_cua_hang')" :class="'my-custom-select custom-shop-select'"
			:searchable="false" :model_value="shop_id" v-on:close="() => {
				showSelectShopModal = false
			}" v-on:model:update="(e) => {
				setSelectedShop(e)
			}">
			<template v-slot:placeholder>
				<div class="h-stack">
					<span>{{ $t('RegisterShopComponent.thiet_lap') }}</span>
					<Icon name="mdi:chevron-down"></Icon>
				</div>
			</template>
			<template v-slot:title_icon_left>
				<Icon name="solar:hamburger-menu-linear"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<div class="custom-shop-select-option">
					<AvatarComponent class="select-shop-logo" :imgTitle="item.name" :imgStyle="item.logo?.style"
						:imgSrc="item.logo
							? (domainImage + item.logo.path)
							: item.banner
								? (domainImage + item.banner.path)
								: ''
							" :width="40" :height="40" />
					<span>{{ item.name }}</span>
				</div>

			</template>
		</CustomSelectComponent>
	</div>

</template>

<script lang="ts" setup>

import non_avatar from "~/assets/image/non-avatar.jpg";
import none_shop from "~/assets/image/none-shop.jpg";
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { toast } from "vue3-toastify";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from '~/assets/image/list-empty-2.jpg';
import { useCurrencyInput } from "vue-currency-input";
import moment from "moment";
import { ChatService } from "~/services/chatService/chatService";
import { UserService } from "~/services/userService/userService";
import { HttpStatusCode } from "axios";
import type { ChannelDTO } from "./ChatDTO";
import { channel_type, member_type } from "./ChatDTO"
import { ShopService } from "~/services/shopService/shopService";
import { AgentService } from "~/services/agentService/agentService";

enum obj_chat {
	user = 'user',
	my_shop = 'my_shop',
	agent_shop = 'agent_shop'
}


const nuxtApp = useNuxtApp();
const { locale, t, setLocale } = useI18n();
const router = useRouter();
const route = useRoute();
const emit = defineEmits(['accept', 'close', 'reject']);
const props = defineProps({
	mode: null
})

useSeoMeta({
	title: t('AppRouteTitle.ChatManageComponent')
})

var chatService = new ChatService();
var userService = new UserService();
var shopService = new ShopService();
var agentService = new AgentService();

var tabList = ref({
	user: {
		key: 'ca_nhan',
		title: 'Cá nhân'
	},
	shop: {
		key: 'cua_hang',
		title: 'Cửa hàng'
	}
} as any);
var tabIndex = useState<any>('chat_manage_tab', () => { return null });

var userProfile = ref();
var myShopData = useState<any>('my_shop', () => { });
var listShopManaged = useState<any>('list_shop_managed', () => { });

var shop_id = ref(((route.query && route.query.shop_id) ? route.query.shop_id : "") as any);
var memberId = ref<any>(null);
var isRefreshing = ref<boolean>(true);
var isErrored = ref<boolean>(false);

var listChannel = ref<ChannelDTO[]>([]);
var listChannelCount = ref(0);

var showChatDetail = useState(() => { return false });
var selectedChatChannel = useState<ChannelDTO | null>(() => { return null });
var noneShop = ref(false);

var channel_id = ref<string | null>();
var object_chat = ref<any>('user');
var currentShopData = ref<any>(null);
var showSelectShopModal = ref(false);

var personalUnreadCount = ref(0);
var shopUnreadCount = ref(0);
watch(
	() => route.query,
	async (newQuery, oldQuery) => {
		await handleRouteQuery()
		await initListChannel();
	},
	{ deep: true }
);

onMounted(async () => {
	console.log('chat manage mounted');
	nuxtApp.$listen(appConst.event_key.check_unread_message, handleGetListChatChannel);
	await handleRouteQuery();
	userProfile.value = await getUserInfo();
	myShopData.value = await getMyShopInfo();
	getListShop();
	await initListChannel();

	if (route.query.channel_id) {
		channel_id.value = route.query?.channel_id.toString();
		openFirstChat()
	}
});

onUnmounted(() => {
	console.log('chat manage before mount');
	nuxtApp.$unsubscribe(appConst.event_key.check_unread_message, handleGetListChatChannel)
})

function handleRouteQuery() {
	let routeQuery = route.query;

	let obj = routeQuery.object == obj_chat.my_shop || routeQuery.object == obj_chat.agent_shop ? 'shop' : 'user';
	tabIndex.value = tabList.value[obj].key;

	if (routeQuery.object == obj_chat.agent_shop) {
		object_chat.value = obj_chat.agent_shop;
		shop_id.value = routeQuery.shop_id;
	}
	else if (routeQuery.object == obj_chat.my_shop) {
		object_chat.value = obj_chat.my_shop;
	}
	else {
		object_chat.value = obj_chat.user;
	}
}

const handleGetListChatChannel = async () => {
	await getListChatChannel().then((e: any) => {
		if (e != null) {
			listChannelCount.value = e.count ?? 0;
			listChannel.value = e.result;
		}
		else {
			listChannelCount.value = 0;
			listChannel.value = [];
			isErrored.value = true
		}

		isRefreshing.value = false;
	}).catch(() => {
		isRefreshing.value = false;
	});

	getUnreadMessageCount();
}

async function initListChannel() {
	isRefreshing.value = true;
	if (object_chat.value == obj_chat.user) {
		if (userProfile.value?.id) {
			currentShopData.value = null;
			memberId.value = userProfile.value.id;
			// changeLanguage(e.language?.[0] ?? e.language)
			await handleGetListChatChannel();
		}
		else {
			console.log(route.path);
			nuxtApp.$emit(appConst.event_key.require_login, {
				redirect_url: route.path,
				back_on_close: true
			})
			// router.push({
			// 	path: appRoute.LoginComponent,
			// 	query: {
			// 		redirect: JSON.stringify(route.path)
			// 	}
			// });
		}
	}
	else if (object_chat.value == obj_chat.my_shop) {
		if (myShopData.value?.id) {
			currentShopData.value = myShopData.value;
			memberId.value = myShopData.value.id;
			await handleGetListChatChannel();
			noneShop.value = false;
		}
		else {
			isRefreshing.value = false;

			if (listShopManaged.value?.length) {
				router.replace({
					query: {
						object: obj_chat.agent_shop,
						shop_id: listShopManaged.value?.[0]?.id
					}
				})
			}
			else {
				noneShop.value = true
			}
		}
	}
	else if (object_chat.value == obj_chat.agent_shop) {
		await getShopDetail().then(async (e: any) => {
			if (e?.id) {
				currentShopData.value = e;
				memberId.value = e.id;
				await handleGetListChatChannel()
				noneShop.value = false;
			}
			else {
				isRefreshing.value = false;
				noneShop.value = true
			}
		})
	}
}

function changeLanguage(lang = 'vi') {
	setLocale(lang)
	localStorage.setItem(appConst.storageKey.language, lang);
	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.setLanguage,
		data: lang
	})
}
async function getUserInfo() {
	return new Promise((resolve) => {
		userService.profileInfo().then(res => {
			if (res.status == HttpStatusCode.Ok) {
				resolve(res.data);
			}
			else{
				resolve(null);
			}	
		}).catch(()=>{
			resolve(null);
		})
	})
}

async function getMyShopInfo() {
	return new Promise((resolve) => {
		if (myShopData.value?.id) {
			resolve(myShopData.value);
		}
		else {
			shopService.myShop().then(res => {
				if (res.status == HttpStatusCode.Ok && res?.body?.data) {
					resolve(res.body.data);
				}
				resolve([]);
			})
		}
	})
}

function getShopDetail() {
	return new Promise((resolve) => {
		return agentService.agentShopDetail(shop_id.value).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				resolve(res.body.data);
			}
			resolve([]);
		})
	})
}

function getListChatChannel() {
	return new Promise(async (resolve) => {
		chatService.listChannel(memberId.value, object_chat.value == obj_chat.user ? member_type.user : member_type.shop).then((listChannel) => {
			if (listChannel.status == HttpStatusCode.Ok) {
				resolve(listChannel.data);
			}
			else {
				resolve(null);
			}

		}).catch(() => {
			resolve(null);
		});

	})
}

function showTimeLastMessage(time: any) {
	let now = moment();
	let timeTemp = moment(time, 'YYYY-MM-DD HH:mm:ss');


	let diffInWeek = now.get('d') - timeTemp.get('d');
	let diffInDay = now.diff(timeTemp, 'seconds');

	if (diffInWeek == 1) return `${t('ChatManageComponent.hom_qua')}`;

	if (diffInWeek > 1 && diffInWeek <= 7) return `${diffInWeek} ${t('ChatManageComponent.ngay_truoc')}`;

	if (diffInDay < 60) return `${t('ChatManageComponent.vua_xong')}`;
	if (diffInDay < 60 * 60) return `${Math.round(diffInDay / 60)} ${t('ChatManageComponent.phut_truoc')}`;
	if (diffInDay < 60 * 60 * 24) return `${Math.round(diffInDay / (60 * 60))} ${t('ChatManageComponent.gio_truoc')}`;



	return timeTemp.format('DD/MM/YYYY HH:mm')
}

function openFirstChat() {
	let indexFirstChatChannel = listChannel.value.findIndex(function (e) {
		return e.id == channel_id.value
	});
	if (indexFirstChatChannel != -1) {
		selectedChatChannel.value = listChannel.value[indexFirstChatChannel]
		showChatDetail.value = true;
	}
	else {
		toast.warning(t('ChatManageComponent.tai_khoan_khong_ton_tai'))
	}
}

function changeTab() {
	if (tabIndex.value == 'ca_nhan') {
		router.replace({
			query: {
				object: 'user',
			}
		})
	}
	else if (tabIndex.value == 'cua_hang') {
		let newQuery;
		if (shop_id.value) {
			newQuery = {
				object: obj_chat.agent_shop,
				shop_id: shop_id.value
			}
		}
		else {
			newQuery = {
				object: obj_chat.my_shop,
			}
		}
		router.replace({
			query: newQuery
		})
	}
}

function getListShop() {
	return new Promise((resolve) => {
		if (listShopManaged.value?.length) {
			resolve(listShopManaged.value)
		}
		else {
			agentService.listShopManage('', 100, 0).then(res => {
				if (res.status == HttpStatusCode.Ok) {
					listShopManaged.value = JSON.parse(JSON.stringify(res.body.data));
					if (myShopData.value?.id) {
						listShopManaged.value = [
							myShopData.value,
							...JSON.parse(JSON.stringify(res.body.data))
						];
					}
				}
				else {
					listShopManaged.value = [];
				}
				resolve(listShopManaged.value)
			})
		}
	})


}

function setSelectedShop(shop_id$: any) {
	if (shop_id$ == myShopData.value?.id) {
		shop_id.value = shop_id$
		router.replace({
			query: {
				object: obj_chat.my_shop
			}
		})

	}
	else {
		let indexSelected = listShopManaged.value.findIndex(function (e: any) {
			return e.id == shop_id$;
		});
		if (indexSelected != -1) {
			currentShopData.value = JSON.parse(JSON.stringify(listShopManaged.value[indexSelected]));
			router.replace({
				query: {
					object: obj_chat.agent_shop,
					shop_id: shop_id$
				}
			})
		}
	}
}

const getUnreadMessageCount = () => {
	return new Promise(async (resolve) => {
		chatService.countUnreadMessage().then((res) => {
			personalUnreadCount.value = res.body.data.user ?? 0;
			shopUnreadCount.value = (res.body.data.shop ?? 0) + (res.body.data.agent ?? 0);
			resolve(res);
		}).catch(() => {
			resolve(null);
		})
	})
}
</script>

<style lang="scss" src="./ChatManageStyles.scss"></style>