<template>
    <div class='manage-categories-container' v-if="shopData && shopData.id">
        <div class='v-stack list-category-container' v-if="!refreshing">
            <v-expansion-panels variant="accordion" v-if="listCategory.length">
                <VueDraggableNext handle=".drag-handle" :animation="300" ghost-class="ghost" opacity="1"
                    drag-class="dragging" class="drag-drop-container" :list="listCategory"
                    v-on:change="() => { updateIndexList() }">
                    <div v-for="(itemCategory, index) in listCategory" class="item-category-container"
                        :id="'cate_expand_' + index" :key="'cate_expand_' + index">
                        <button class="drag-handle">
                            <Icon name="ph:arrows-out-cardinal-light"></Icon>
                        </button>

                        <div class="v-stack" v-on:click="() => {
                            let url = $props.mode == 'agent'
                                ? appRoute.AgentProductsCategoryComponent.replaceAll(':id', shopData.slug).replaceAll(':category_id', itemCategory.id)
                                : appRoute.ProductsCategoryComponent.replaceAll(':slug', itemCategory.id);
                            router.push({
                                path: url,
                                state: {
                                    shopData: JSON.parse(JSON.stringify(shopData)),
                                    categoryData: JSON.parse(JSON.stringify(itemCategory))
                                }
                            });
                        }">
                            {{ showTranslateProductName(itemCategory) }}
                            <span>
                                ({{ itemCategory.products.length }} {{ $t('ManageCategoriesComponent.san_pham') }})
                            </span>
                        </div>
                    </div>
                </VueDraggableNext>
            </v-expansion-panels>
            <div class='categories-list-empty' v-else-if="!refreshing">
                <img loading="lazy" :src="list_empty" :placeholder="list_empty"
                    :alt="$t('ManageCategoriesComponent.danh_sach_trong')" />

                <span>
                    {{ $t('ManageCategoriesComponent.danh_sach_trong') }}
                </span>
            </div>
        </div>
        <div class="loading" v-else>
            {{ $t('ManageCategoriesComponent.dang_tai') }}
        </div>
        <button class='add-category-button' :disabled=refreshing v-on:click="() => {
            showCreateCategoryModal = true
        }">
            <Icon name="ic:sharp-add"></Icon>
            {{ $t('ManageCategoriesComponent.them_danh_muc') }}
        </button>
        <VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
            v-model="showCreateCategoryModal" v-on:closed="() => {
                showCreateCategoryModal = false
            }" contentTransition="vfm-fade">
            <CreateCategoryComponent :shopId="shopData.id" :shop_data="JSON.parse(JSON.stringify(shopData))" :dataCategories="listCategory" v-on:close="(obj: any) => {
                showCreateCategoryModal = false;
                if (obj?.id) {
                    getListCategory();
                    let url = $props.mode == 'agent'
                        ? appRoute.AgentProductsCategoryComponent.replaceAll(':id', shopData.slug).replaceAll(':category_id', obj.id)
                        : appRoute.ProductsCategoryComponent.replaceAll(':slug', obj.id);
                    router.push({
                        path: url,
                        state: {
                            shopData: JSON.parse(JSON.stringify(shopData)),
                            categoryData: JSON.parse(JSON.stringify(obj)),
                            isEditing: true,
                            showAddProductForm: true
                        }
                    })
                }
            }" :mode="props.mode" />
        </VueFinalModal>
        <VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
            v-model="showEditCategoryModal" v-on:closed="() => {
                showEditCategoryModal = false
            }" contentTransition="vfm-fade">
            <EditCategoryComponent :categoryData="JSON.parse(JSON.stringify(categoryEditingObj))"
                :shop_data="JSON.parse(JSON.stringify(shopData))"
                :dataCategories="JSON.parse(JSON.stringify(listCategory))" v-on:close="() => {
                    showEditCategoryModal = false;
                    getListCategory();
                }" :mode="props.mode" />
        </VueFinalModal>
        <VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
            v-model="showDeleteCategoryModal" v-on:closed="() => {
                showDeleteCategoryModal = false
            }" contentTransition="vfm-fade">
            <div class='v-stack delete-category-content'>
                <span class='delete-category-title'>
                    {{ $t('ManageCategoriesComponent.xoa_danh_muc') }}
                </span>

                <span class='delete-category-message'>
                    {{ $t('ManageCategoriesComponent.ban_chac_chan_xoa_danh_muc', {
                        categoryName: categoryDeleteObj ?
                    categoryDeleteObj.name : '' }) }}
                </span>
            </div>
            <div class='h-stack confirm-modal-buttons'>
                <button class='reject-button' :disabled=isUpdating v-on:click="() => {
                    showDeleteCategoryModal = false;
                }">
                    {{ $t('ManageCategoriesComponent.khong') }}
                </button>
                <button class='accept-button' :disabled=isUpdating v-on:click="() => {
                    deleteCategory(categoryDeleteObj);
                    showDeleteCategoryModal = false;
                }">
                    {{ $t('ManageCategoriesComponent.co') }}
                </button>
            </div>
        </VueFinalModal>
    </div>
    <div v-if="!(shopData && shopData.id) && !refreshing" class="access-denied">
        <img loading="lazy" :src="none_shop" :placeholder="none_shop"
            :alt="$t('ManageCategoriesComponent.tu_choi_quyen_truy_cap')" />
        <span>
            {{ $t('ManageCategoriesComponent.ban_chua_dang_ky_cua_hang') }}
        </span>
    </div>

</template>
<script lang="ts" setup>


import { VueDraggableNext } from 'vue-draggable-next'
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, showTranslateProductName } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';

import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg"
import { ProductService } from '~/services/productService/productService';
import { ImageService } from '~/services/imageService/imageService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';
const router = useRouter();
const route = useRoute();
const nuxtApp = useNuxtApp();
const {t}=useI18n();
useSeoMeta({
    title: `${t('AppRouteTitle.ManageCategoriesComponent')}`
});
var emit = defineEmits(['close']);

var props = defineProps({
    shopData: null,
    mode: null
})

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var imageService = new ImageService();
var shopService = new ShopService();
var agentService = new AgentService();
var searchProductTimeout: any;
var categoryService = new CategoryService();

var isErrored = ref(false);
var shopData = useState('shop_data', () => { return null as any });
var shop_id = ref((route.params?.id ? route.params.id : null) as any);
var listCategory = useState('shop_categories', () => { return [] as any[] });
var refreshing = ref(false);
var isSaving = ref(false);
var isUpdating = ref(false);
var showCreateCategoryModal = ref(false);
var showEditCategoryModal = ref(false);
var showDeleteCategoryModal = ref(false);
var showProductsModal = ref(false);
var categoryEditingObj = ref(null as any);
var selectedCategoryObj = ref(null as any);
var categoryDeleteObj = ref(null as any);
var isUpdatingIndex = ref(false);

onUnmounted(() => {
    nuxtApp.$unsubscribe('refresh_category_manage')
})

onMounted(() => {
    nuxtApp.$listen('refresh_category_manage', () => {
        getListCategory()
    })
    if (props.shopData?.id) {
        shopData.value = JSON.parse(JSON.stringify(props.shopData));
    }
    if (!shopData?.value?.id) {
        if (props.mode != 'agent') {
            getMyShop();
        }
        else {
            agentGetShopDetail();
        }
    }
    else {
        getListCategory();
    }
})
function getMyShop() {
    refreshing.value = true;
    shopService.myShop().then(async res => {
        if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
            shopData.value = res.body.data;
            getListCategory()
        }
        else if (res.status == 401) {
            // toast.error("Bạn chưa đăng ký cửa hàng!");
            isErrored.value = true;
            refreshing.value = false;
        }
    })
}
function agentGetShopDetail() {
    refreshing.value = true;
    agentService.agentShopDetail(shop_id.value).then(async res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
            shopData.value = res.body.data;
            getListCategory()
        }
        else if (res.status == 401) {
            // toast.error("Bạn chưa đăng ký cửa hàng!");
            isErrored.value = true;
            refreshing.value = false;
        }

    })
}
function getListCategory() {
    categoryService.getCategoryByShopId(shopData.value.id).then(res => {
        listCategory.value = JSON.parse(JSON.stringify(res.body.data))
        refreshing.value = false;
    })
}

function close() {
    router.back();
}

function deleteCategory(itemCategory: any) {
    categoryService.removeCategory(itemCategory.id).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            nuxtApp.$emit('refresh_category_manage')
        }
    })
}

function updateIndexList() {
    isUpdatingIndex.value = true;
    listCategory.value.map((itemCategory, index) => itemCategory.index = index + 1);
    saveIndexUpdate();
}

function saveIndexUpdate() {
    let arr = listCategory.value.map((item: any) => { return { id: item.id, index: item.index } });
    // refreshing.value = true
    categoryService.updateIndex(arr).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            // toast.success("Cập nhật thứ tự thành công");
            // getListCategory();
        }
        else {
            // toast.error("Cập nhật thứ tự thất bại\nVui lòng thử lại sau");
        }
        isUpdatingIndex.value = false;
    })
}
</script>

<style lang="scss" src="./ManageCategoriesStyles.scss"></style>