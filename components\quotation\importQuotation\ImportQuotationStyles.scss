.import-quotation-container {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: hidden auto;
    display: flex;
    justify-content: center;
    flex-direction: column;

    &>.import-quotation-content-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        background: white;
        overflow: hidden;

        &>.header-import {
            margin: 0;
            flex: 0;
            transition: all .3s ease-in-out;

            &>.information,
            >.expiration {
                display: flex;
                flex-direction: column;
                padding: 0 10px;

                & .quotation-detail-input {
                    background: white;
                    border-radius: 7px;
                    margin: 5px 0;
                    padding: 10px;
                    font-size: 15px;
                    height: 45px;
                    font-weight: 600;
                    border: 1px solid rgb(231, 233, 236);
                    outline: none;

                    &:disabled {
                        background: #f5f6fa;
                    }

                }

                &>.select-expire-date {
                    display: flex;
                    gap: 5px;
                    width: 100%;
                    margin: 5px 0;

                    &>button {
                        flex: 1;
                        background: var(--secondary-color-3);
                        height: 45px;
                        border-radius: 7px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        padding: 0 10px;
                    }
                }

                &>.select-from-file-btn {
                    display: flex;
                    gap: 5px;
                    width: 100%;
                    margin: 5px 0;
                    color: var(--primary-color-1);
                    font-weight: 700;
                    height: 45px;
                    margin: 5px 0;
                    box-shadow: none;
                }

            }

            &>.actions {
                display: flex;
                flex-wrap: wrap;
                width: 100%;
                padding: 0 10px;

                &>.supplier-group,
                >.sheet-group {
                    display: flex;
                    height: 45px;
                    width: 100%;
                    flex: auto;
                    margin: 5px 0;

                    &>.supplier-select {
                        border-radius: 7px 0 0 7px;
                        background: var(--secondary-color-1);
                        color: #868686;
                        overflow: hidden;
                        font-weight: 700;
                        font-size: 17px;
                        display: flex;
                        flex-direction: column;

                        & .v-input__control {
                            padding: 5px 10px;
                            height: 45px !important;
                            text-align: center;
                            min-height: unset !important;
                        }

                        & .v-field__input {
                            text-align: center;

                            &>input {
                                align-self: center;
                                height: 45px;
                                text-align: center;
                            }
                        }
                    }

                    &>.add-supplier {
                        border-radius: 0 7px 7px 0;
                        margin-left: 3px;
                        padding: 5px 10px;
                        background: var(--secondary-color-1);
                        color: #868686;
                        font-size: 20px;
                        overflow: hidden;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: auto;
                        min-width: auto;
                        height: 100%;
                        box-shadow: none;
                    }

                    &>.sheet-select {
                        border-radius: 7px;
                        background: var(--secondary-color-2);
                        color: #545454;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;

                        & .v-input__control {
                            padding: 5px 10px;
                        }

                        & .v-select__selection {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: auto;
                            display: block;
                            white-space: nowrap;
                        }

                        & .v-field__field {
                            justify-content: center;
                        }

                        & .v-field__input {
                            justify-content: center;
                        }
                    }
                }

                &>.save-quotation {
                    background: rgb(76, 163, 250);
                    color: white;
                    width: 100%;
                    height: auto;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 20px;
                    margin: 5px 0;
                    border-radius: 7px;
                    font-weight: 700
                }
            }

            & .error {
                border: 2px solid var(--primary-color-2) !important;

            }

            & .error.animation {
                animation: highlightErr .5s ease;
                animation-iteration-count: 2;
            }

            @keyframes highlightErr {
                0% {
                    scale: 1;
                }

                50% {
                    scale: .95;
                }

                100% {
                    scale: 1;
                }
            }
        }

        &>.expand-header-import {
            color: white;
            background: rgb(74, 163, 247);
            margin-bottom: 5px;

            @media screen and (min-width: 960px) {
                display: none;
            }
        }

        &>.data-import {
            height: 100%;
            overflow: auto;

            & .v-table__wrapper {
                overflow: auto;
                scrollbar-width: auto;
                /* Firefox */
                scrollbar-color: auto;

                &::-webkit-scrollbar-thumb {
                    background: auto;
                    border-radius: unset;
                }

                &::-webkit-scrollbar {
                    width: auto;
                    height: auto;
                }
            }



            & thead {
                position: sticky;
                top: 0;
                z-index: 10;
            }

            & .head-col {
                min-width: 200px;
                text-align: center;
                background: var(--secondary-color-1);
                position: relative;

                &.short {
                    min-width: 100px;
                }

                &.auto {
                    min-width: auto;
                }

                @media screen and (max-width: 961px) {
                    min-width: 100px;
                }
            }

            & .head-select-field {
                min-width: max-content;
                background: var(--secondary-color-2);

                & .v-input__control {
                    padding: 5px;
                    // align-items: center;

                    & input {
                        align-self: center;
                        text-align: center;
                    }
                }

                & .v-field__clearable {
                    position: absolute;
                    right: 0;
                    height: 100%;
                }

                & .v-field__input {
                    justify-content: center;
                    padding: 0 10px;
                }
            }

            & .body-col {
                text-align: center;
                padding: 5px;

                &.long-text {
                    min-width: 300px !important;
                }

                & .quotation-detail-input {
                    height: auto;
                    min-height: 45px;
                    width: 100%;
                    padding: 5px;
                    outline: none;
                    background: #f5f6fa;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &>textarea {
                        background-color: transparent !important;
                        resize: none;
                        outline: none;
                        width: 100%;
                        text-align: center;
                    }
                }
            }

            & .body-col.actions {
                text-align: center;
                padding: 5px;
                min-width: auto;
                background: linear-gradient(to left, white 80%, transparent);

                @media screen and (min-width: 961px) {
                    position: sticky;
                    right: 0;
                }

                &>.v-btn-group {
                    border-radius: 5px;

                    &>button.action {
                        align-self: center;
                        padding: 0;
                        font-size: 20px;
                        width: 35px;
                        height: 35px !important;
                        min-width: 30px;

                        &.duplicate {
                            color: #868686;
                        }

                        &.add-new {
                            color: var(--primary-color-1);
                        }

                        &.delete {
                            color: var(--primary-color-2);
                        }
                    }
                }
            }

            & .body-col.material-id {
                min-width: 250px !important;

                & .material-select {
                    background: #f5f6fa;
                }

                & .v-input__control {
                    padding: 3px 10px;
                }

                & .v-field__input {
                    align-items: center;

                    &>input {
                        align-self: center;
                        text-align: center;
                    }
                }
            }
        }

        &>.none-data {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: white;
            overflow: auto;
            font-size: 20px;
        }
    }
}