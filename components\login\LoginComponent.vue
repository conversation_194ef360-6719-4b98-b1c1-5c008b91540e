<template>
	<div class="public-container">
		<div class="stack login-container" id="login_container" v-on:keypress.enter="() => { login() }">
			<!-- <img loading="lazy" :src="icon_square" :placeholder="icon_square" alt="Rẻ mà gần" class='login-icon' />

		<span class='login-slogan'>
			Chào mừng đến với Rẻ mà Gần
		</span>

		<div class='v-stack content-container'>
			<div class='v-stack'>
				<span class='required label'>
					Tên đăng nhập
				</span>
				<input title='Tên đăng nhập' name='user-name' maxLength=255 autoComplete="username" :disabled="isLogining"
					class='input-custom' placeholder="Tên đăng nhập" v-model="userName" v-on:input="($event: any) => {
						userName = $event.currentTarget.value;
						if (!userName || !userName.length)
							userNameErr = 'Vui lòng nhập Tên đăng nhập';
						else userNameErr = '';
					}" v-on:blur="() => {
	if (!userName || !userName.length) {
		userNameErr = 'Vui lòng nhập Tên đăng nhập';
	}
	else { userNameErr = '' }
}" />
				<span class='error-message'>{{ userNameErr }}</span>
			</div>
			<div class='v-stack'>
				<span class='required label'>
					Mật khẩu
				</span>
				<div class='password-input-group'>
					<input title='Mật khẩu' name='password' maxLength=255 autoComplete="password" class='input-custom'
						placeholder="Mật khẩu" :disabled="isLogining" :type="passwordShow ? 'text' : 'password'"
						v-model="password" v-on:input="($event: any) => {
							password = $event.currentTarget.value
							if (!password || !password.length) {
								passwordErr = 'Vui lòng nhập mật khẩ';
							}
							else {
								passwordErr = '';
							}
						}" v-on:blur="() => {
	if (!password || !password.length) {
		passwordErr = 'Vui lòng nhập mật khẩu'
	}
	else {
		passwordErr = ''
	}
}" />
					<button v-on:click="() => { passwordShow = !passwordShow }">
						<Icon name="ion:eye" v-if="!passwordShow" />
						<Icon name="ion:eye-off" v-if="passwordShow" />
					</button>
				</div>

				<span class='error-message'>{{ passwordErr }}</span>
			</div>
			<span class='forget-password-text' v-on:click="() => { }">
				<nuxt-link :to="appRoute.ForgetPasswordComponent">
					Quên mật khẩu?
				</nuxt-link>
				
			</span>

			<button class='button-action' id='login-submit' :loading="isLogining && isLoginingWithUsername"
				:disabled="isLogining || isLoginingWithUsername" v-on:click="() => { login() }">
				Đăng nhập
			</button>

			<button class='button-action' :loading="isLogining && isLoginingWithGoogle"
				v-if="!webInApp"
				:disabled="(isLogining || isLoginingWithGoogle)  || disableLoginWithGoogle"
				v-on:click="() => { loginGoogle() }">
				<img loading="lazy" :src='google_icon' :placeholder="google_icon" />
				Đăng nhập với Google
			</button>
			<button class='button-action' :loading="isLogining && isLoginingWithZalo"
				v-if="!webInApp"
				:disabled="isLogining || isLoginingWithZalo" v-on:click="() => { loginWithZalo() }">
				<img loading="lazy" :src='zalo_icon' :placeholder="zalo_icon" />
				Đăng nhập với zalo
			</button>
		</div>
		<div class='h-stack signup-button-container'>
			<span>Chưa có tài khoản?</span>
			<nuxt-link :to="appRoute.SignupComponent">
				<span class='signup-button'>Đăng ký</span>
			</nuxt-link>
		</div> -->
			<div class="sign-up-btn">
				<!-- <button class="back-home" v-on:click="() => { close(); }">
					<Icon name="lucide:chevron-left" /> {{ $t('LoginComponent.ve_trang_chu') }}
				</button> -->
				<nuxt-link :to="appRoute.SignupComponent">{{ $t('LoginComponent.dang_ky') }}</nuxt-link>
			</div>
			<span class='login-title'>
				{{ $t('LoginComponent.dang_nhap') }}
			</span>
			<span class='login-slogan'>
				{{ $t('LoginComponent.chao_mung_ban_den_voi_re_ma_gan') }}
			</span>
			<div class="login-protocol">
				<button class="login-protocol-btn active"
					v-on:click="() => { login_protocol = login_protocol == 'phone' ? 'email' : 'phone' }">
					{{ login_protocol == 'phone' ? $t('LoginComponent.dang_nhap_bang_email_ten_dang_nhap') :
						$t('LoginComponent.dang_nhap_bang_so_dien_thoai') }}
				</button>
				<!-- <button class="login-protocol-btn" :class="{ 'active': protocol == 'email' }" v-on:click="() => { protocol = 'email' }">Email</button> -->
			</div>

			<div class="content-container" :class="login_protocol">
				<div class='v-stack' v-if="login_protocol == 'phone'">
					<span class='label'>
						{{ $t('LoginComponent.so_dien_thoai') }}
					</span>
					<div class="content-input-group">
						<button class="nation-code">
							+84
						</button>
						<input :title="$t('LoginComponent.so_dien_thoai')" name='phone' type="phone" maxLength=255
							:disabled="isLogining" class='content-input'
							:placeholder="$t('LoginComponent.nhap_so_dien_thoai')" v-model="phone" v-on:input="($event: any) => {
								phone = validPhone($event.currentTarget.value);
								phoneValidation();
							}" v-on:blur="() => {
								phoneValidation();
							}" />
					</div>
					<span class='error-message'>{{ phoneErr }}</span>
				</div>
				<div class='v-stack' v-else>
					<span class='label'>
						{{ $t('LoginComponent.email_ten_dang_nhap') }}
					</span>
					<div class="content-input-group">
						<input :title="$t('LoginComponent.dia_chi_email')" name='email' maxLength=255
							:disabled="isLogining" class='content-input'
							:placeholder="$t('LoginComponent.nhap_email_hoac_ten_dang_nhap')" v-model="email"
							v-on:input="($event: any) => {
								email = nonAccentVietnamese($event.currentTarget.value);
								emailValidation();
							}" v-on:blur="() => {
								emailValidation();
							}" />
					</div>
					<span class='error-message'>{{ emailErr }}</span>
				</div>
				<div class='v-stack'>
					<span class='label'>
						{{ $t('LoginComponent.mat_khau') }}
					</span>
					<div class='content-input-group'>
						<input :title="$t('LoginComponent.mat_khau')" name='password' maxLength=255
							class='content-input' :placeholder="$t('LoginComponent.nhap_mat_khau')"
							:disabled="isLogining" :type="passwordShow ? 'text' : 'password'" v-model="password"
							v-on:input="($event: any) => {
								password = $event.currentTarget.value;
								passwordValidaion();
							}" v-on:blur="() => {
								passwordValidaion();
							}" />
						<button v-on:click="() => { passwordShow = !passwordShow }">
							<Icon name="mage:eye-closed" v-if="!passwordShow" />
							<Icon name="mage:eye" v-if="passwordShow" />
						</button>
					</div>
					<span class='error-message'>{{ passwordErr }}</span>
				</div>
				<span class='forget-password-text' v-on:click="() => { }">
					<nuxt-link :to="appRoute.ForgetPasswordComponent">
						{{ $t('LoginComponent.quen_mat_khau') }}
					</nuxt-link>
				</span>
			</div>
			<button class='button-action' id='login-submit' :loading="isLogining && isLoginingWithUsername"
				:disabled="isLogining || isLoginingWithUsername" v-on:click="() => { login() }">
				{{ $t('LoginComponent.dang_nhap') }}
			</button>
			<div class="others-protocol">
				<span>
					<div class="line"></div> {{ $t('LoginComponent.hoac') }} <div class="line"></div>
				</span>
				<button class='button-action' :loading="isLogining && isLoginingWithGoogle"
					:disabled="(isLogining || isLoginingWithGoogle) || disableLoginWithGoogle"
					v-on:click="() => { loginGoogle() }">
					<img loading="lazy" :src='google_icon' :placeholder="google_icon" />
					{{ $t('LoginComponent.dang_nhap_voi_google') }}
				</button>
				<button class='button-action' :loading="isLogining && isLoginingWithZalo" v-if="!webInApp"
					:disabled="isLogining || isLoginingWithZalo" v-on:click="() => { loginWithZalo() }">
					<img loading="lazy" :src='zalo_icon' :placeholder="zalo_icon" />
					{{ $t('LoginComponent.dang_nhap_voi_zalo') }}
				</button>
				<button class='button-action' :loading="isLogining && isLoginingWithApple" v-if="webInApp && isiOS()"
					:disabled="isLogining || isLoginingWithApple" v-on:click="() => { loginApple() }">
					<img loading="lazy" :src='apple_icon' :placeholder="zalo_icon" />
					{{ $t('LoginComponent.dang_nhap_voi_apple') }}
				</button>
			</div>

			<div class="contact">
				{{ $t('LoginComponent.ban_can_hotro_them') }} <nuxt-link :to="appRoute.AboutComponent">{{
					$t('LoginComponent.lien_he_voi_chung_toi') }}</nuxt-link>
			</div>
			<VueFinalModal class="my-modal-container" content-class="v-stack user-disabled-modal" :overlay-behavior="'persist'"
				v-model="showUserDisabledModal" v-on:closed="() => {
					showUserDisabledModal = false
				}" contentTransition="vfm-slide-up">
				<div>
					<button v-on:click="() => {
						showUserDisabledModal = false
					}">
						<Icon name="material-symbols:cancel-outline"></Icon>
					</button>
					<div class='v-stack'>
						<img loading="lazy" :src="access_denied" :placeholde="access_denied" />
						<p class='message'>
							{{ $t('LoginComponent.tai_khoan_da_bi_khoa') }}
						</p>
						<p class='message'>
							{{ $t('LoginComponent.vui_long_lien_he') }}
							<nuxt-link :to="appRoute.SupportComponent">{{ $t('LoginComponent.ho_tro')
								}}</nuxt-link>
						</p>
					</div>

				</div>
			</VueFinalModal>
		</div>

	</div>

</template>

<script lang="ts" setup>
import { appConst, encryptPassword, zaloConfig, nonAccentVietnamese, validPhone, domain } from '~/assets/AppConst';
import {
	useTokenClient,
	useOneTap,
	useCodeClient,
	useGsiScript,
	decodeCredential,
} from "vue3-google-signin";
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { FCMService } from '~/services/fcmService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { SettingService } from '~/services/settingService/settingService';
import { UserService } from '~/services/userService/userService';
import { toast } from "vue3-toastify";
import access_denied from "~/assets/image/access_denied.png";

import environment from '~/assets/environment/environment';
import axios, { HttpStatusCode } from 'axios';

import icon_square from "~/assets/image/icon_square_circle_300x300.png";
import google_icon from "~/assets/image/google-icon-2.png";
import zalo_icon from "~/assets/image/Logo-Zalo-Arc.webp";
import apple_icon from "~/assets/image/apple-icon.png"
import { VueFinalModal } from 'vue-final-modal';
import { useState } from '#app';

const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n();
const device = useDevice();
var router = useRouter();
var route = useRoute();

var tokenGoogle = ref("" as any);
var disableLoginWithGoogle = ref(false);
var showUserDisabledModal = ref(false);
var authService = new AuthService();
var userService = new UserService();
var fcmService = new FCMService();
var imageService = new ImageService();
var settingService = new SettingService();
var loginWithGoogleOneTap = useOneTap({
	onSuccess: async (response) => {
		isLogining.value = true;
		tokenGoogle.value = response.credential;
		let user = await decodeCredential(tokenGoogle.value);
		let body = {
			id: user.id,
			name: user.family_name + " " + user.given_name,
			email: user.email,
			picture: {
				data: {
					url: user.picture
				}
			}
		}
		let login = await authService.loginWithGoogle(body);
		if (login.status == HttpStatusCode.Ok) {
			toast.success(t('LoginComponent.dang_nhap_thanh_cong'));
			localStorage.setItem(appConst.storageKey.token, login.body["token"]);
			// if (login.body.data.is_register) {
			// 	await insertImagesGoogle(body.picture.data.url);
			// }
			localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(login.body.data));
			// Alert.alert("đăng nhập thành công:");

			await fcmService.getFcmToken();
			let defaultRedirectUrl = authService.getDefaultRedirectUrl(login.body?.data?.role_id, login.body?.data?.shop?.length);
			setTimeout(() => {
				isLogining.value = false;
				nuxtApp.$emit(appConst.event_key.login);
				router.replace(redirect_url.value ?? defaultRedirectUrl)

			}, 1000);

			// props.navigation.popToTop();
		}
		else if (login.status == HttpStatusCode.Forbidden && login.message_code == "C_E_008") {
			showUserDisabledModal.value = true;
		}
		else {
			toast.error(t('LoginComponent.dang_nhap_that_bai'));
			router.push(appRoute.LoginComponent);
		}
		// let user
		// if (tokenGoogle.value) {
		// 	user = await $fetch('/api/google-login', {
		// 		method: "POST",
		// 		body: {
		// 			token: tokenGoogle.value
		// 		}
		// 	});
		// }
		// var client = new OAuth2Client(environment.GoogleIDApp);

		// user = await client.verifyIdToken({
		// 	idToken: token,
		// 	audience: environment.GoogleIDApp
		// })


		// }
		// let a = decodeCredential(token.access_token);

	},
	onError: () => {
		console.log("lỗi login")
		// disableLoginWithGoogle.value = true;
		loginWithGoogleTokenClient.login()
		isLogining.value = false;
	},
	autoSelect: false,
	disableAutomaticPrompt: true,
})
var loginWithGoogleTokenClient = useTokenClient({
	onSuccess: async (response) => {
		isLogining.value = true;
		tokenGoogle.value = response.access_token;
		let url = `https://www.googleapis.com/oauth2/v3/userinfo?access_token=${tokenGoogle.value}`
		axios(url).then(async res => {
			let body = {
				id: res.data.sub,
				name: res.data.family_name + " " + res.data.given_name,
				email: res.data.email,
				picture: {
					data: {
						url: res.data.picture
					}
				}
			}
			let login = await authService.loginWithGoogle(body);
			if (login.status == HttpStatusCode.Ok) {
				toast.success(t('LoginComponent.dang_nhap_thanh_cong'));
				localStorage.setItem(appConst.storageKey.token, login.body["token"]);
				// if (login.body.data.is_register) {
				// 	await insertImagesGoogle(body.picture.data.url);
				// }
				localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(login.body.data));
				// Alert.alert("đăng nhập thành công:");

				await fcmService.getFcmToken();

				let defaultRedirectUrl = authService.getDefaultRedirectUrl(login.body?.data?.role_id, login.body?.data?.shop?.length);
				setTimeout(() => {
					isLogining.value = false;
					nuxtApp.$emit(appConst.event_key.login);
					router.replace(redirect_url.value ?? defaultRedirectUrl);

				}, 1000);

				// props.navigation.popToTop();
			}
			else if (login.status == HttpStatusCode.Forbidden && login.message_code == "C_E_008") {
				showUserDisabledModal.value = true;
			}
			else {
				toast.error(t('LoginComponent.dang_nhap_that_bai'));
				isLogining.value = false;
			}
		})
		// let user = await decodeCredential(tokenGoogle.value);
		// let body = {
		// 	id: user.id,
		// 	name: user.family_name + " " + user.given_name,
		// 	email: user.email,
		// 	picture: {
		// 		data: {
		// 			url: user.picture
		// 		}
		// 	}
		// }
		// let login = await authService.loginWithGoogle(body);
		// if (login.status == HttpStatusCode.Ok) {
		// 	toast.success(t('LoginComponent.dang_nhap_thanh_cong'));
		// 	localStorage.setItem(appConst.storageKey.token, login.body["token"]);
		// 	if (login.body.data.is_register) {
		// 		await insertImagesGoogle(body.picture.data.url);
		// 	}
		// 	localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(login.body.data));
		// 	// Alert.alert("đăng nhập thành công:");

		// 	await fcmService.getFcmToken();
		// 	setTimeout(() => {
		// 		router.push(appRoute.LoginComponent)
		// 	}, 1000);

		// 	// props.navigation.popToTop();
		// }
		// else {
		// 	toast.error(t('LoginComponent.dang_nhap_that_bai'));
		// 	router.push(appRoute.LoginComponent);
		// }
	},
	onError: (err) => {
		disableLoginWithGoogle.value = true;
		isLogining.value = false;
	},
});

var isLogged = ref(false);
var isLogining = ref(false);
var isLoginingWithUsername = ref(false);
var isLoginingWithZalo = ref(false);
var isLoginingWithGoogle = ref(false);
var isLoginingWithApple = ref(false);
// refreshing: true;
var userName = ref((router.options.history.state.userName ? router.options.history.state.userName : "") as string);
var userNameErr = ref("");
var phone = ref((router.options.history.state.phone ? router.options.history.state.phone : "") as string);
var phoneErr = ref("");
var email = ref((router.options.history.state.email ? router.options.history.state.email : "") as string);
var emailErr = ref("");
var password = ref((router.options.history.state.password ? router.options.history.state.password : "") as string);
var passwordErr = ref("");
var passwordShow = ref(router.options.history.state.passwordShow ? router.options.history.state.passwordShow : false);
var webInApp = ref(null as any);

var redirect_url = ref(route.query?.redirect ? JSON.parse(route.query?.redirect as string) : null);

var login_protocol = ref('login_protocol', () => 'phone' as "phone" | "email");

watch(login_protocol, (newValue) => {
	console.log("login_protocol changed to: ", newValue);
	if (process.client) {
		localStorage.setItem('last_login_protocol', newValue);
	}
});

onMounted(async () => {
	// Only access localStorage in browser environment
	
	// Initialize login_protocol from localStorage only on client side

	var savedProtocol = localStorage.getItem('last_login_protocol');
	login_protocol.value = savedProtocol === 'email' ? 'email' : 'phone';

	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	useSeoMeta({
		title: t('AppRouteTitle.LoginComponent')
	});

	let userInfo = await localStorage.getItem(appConst.storageKey.userInfo);
	let defaultRedirectUrl = authService.getDefaultRedirectUrl(userInfo ? JSON.parse(userInfo).role_id : null, userInfo ? JSON.parse(userInfo).shop?.length : null);
	if (userInfo) {
		toast.success(t('LoginComponent.ban_da_dang_nhap'));
		isLogged.value = true;
		router.replace(redirect_url.value ?? defaultRedirectUrl);

	}

	// loginWithGoogleOneTap.login();
})

async function login() {
	isLogining.value = true;
	isLoginingWithUsername.value = true;
	if (login_protocol.value == 'phone') {
		let phoneValidate = phoneValidation();
		if (!phoneValidate) {
			isLogining.value = false;
			isLoginingWithUsername.value = false;
		}
		else {
			userName.value = validPhone(phone.value);
		}
	}
	else if (login_protocol.value == 'email') {
		let emailValidate = emailValidation();
		if (!emailValidate) {
			isLogining.value = false;
			isLoginingWithUsername.value = false;
		}
		else {
			userName.value = email.value;
		}
	}
	// if (!userName.value || !userName.value.length) {
	// 	isLogining.value = false;
	// 	isLoginingWithUsername.value = false;
	// 	userNameErr.value = "Vui lòng nhập Tên đăng nhập"
	// }
	if (!password.value || !password.value?.length) {
		isLogining.value = false;
		isLoginingWithUsername.value = false;
		passwordErr.value = t('LoginComponent.vui_long_nhap_mat_khau');
	}
	if (userName.value && password.value) {
		let encryptedPassword = await encryptPassword(password.value);
		let login = await authService.login(userName.value, encryptedPassword, login_protocol.value);
		if (login.status == HttpStatusCode.Ok) {
			await toast.success(t('LoginComponent.dang_nhap_thanh_cong'));

			isLoginingWithUsername.value = false;
			localStorage.setItem(appConst.storageKey.token, login["token"]);
			let userInfo = await userService.profileInfo();  
			setLocale(userInfo.data.language)
			localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(userInfo.data));
			
			let defaultRedirectUrl = authService.getDefaultRedirectUrl(userInfo.data?.role_id, userInfo.data?.shop?.length);
			// Alert.alert("đăng nhập thành công:");
			setTimeout(() => {
				nuxtApp.$emit(appConst.event_key.login);
				router.replace(redirect_url.value ?? defaultRedirectUrl);
				isLogining.value = false;
			}, 600);

			requestNotification();
			// publish('login');
		}
		else if (login.status == HttpStatusCode.Forbidden && login.message_code == "C_E_008") {
			isLogining.value = false;
			isLoginingWithUsername.value = false;
			showUserDisabledModal.value = true;
		}
		else {
			setTimeout(() => {
				isLogining.value = false;
				isLoginingWithUsername.value = false;
				toast.error(t('LoginComponent.thong_tin_khong_dung'));
			}, 1500);

		}
	}

}

function loginWithZalo() {
	isLoginingWithZalo.value = true;
	let hrefLinkShare = "https://oauth.zaloapp.com/v4/permission?app_id="
		+ zaloConfig.appIDZaloLogin
		+ "&redirect_uri="
		+ encodeURIComponent(`${zaloConfig.callbackUrlZalo}${redirect_url.value ? ('?redirect='+redirect_url.value) : ''}`)
		+ "&state=" + zaloConfig.stateZalo;
	// let hrefLinkShare = "http://localhost:4200/Oauth-zalo";
	var x = (window.outerWidth) / 2 - 350;
	var y = (window.outerHeight) / 2 - 300;
	var responsive = "width=700,height=600,left=" + x + ",top=" + y;

	window.location.replace(hrefLinkShare);
}

async function loginGoogle() {
	if (!webInApp.value) {
		await loginWithGoogleTokenClient.login();
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.loginWithSocial,
			data: appConst.provider_name.google
		})
	}
}

function loginApple() {
	if (webInApp.value) {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.loginWithSocial,
			data: appConst.provider_name.apple
		})
	}
	// else {
	// 	const clientId = environment.AppleClientId; // Your service ID
	// 	const clientSecret = environment.AppleClientSecretKey; // Your service ID
	// 	const redirectUri = "https://remagan.com"+appRoute.LoginComponent; // Your redirect URI
	// 	const scope = 'name email';
	// 	const responseType = 'code id_token';

	// 	const authUrl = `https://appleid.apple.com/auth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=${responseType}&scope=${scope}&response_mode=form_post&client_secret=${clientSecret}`;

	// 	window.location.href = authUrl;
	// }
}

function handlePasswordClick() {
	passwordShow.value = !passwordShow.value
}

async function requestNotification() {
	// await fcmService.requestNotificationPermission();
	await fcmService.getFcmToken();
	// await fcmService.listenerMesaging();
}

async function insertImagesGoogle(image: any) {
	await encodeBase64(image)
}
async function encodeBase64(image: any) {
	await getBase64ImageFromUrl(image);
}

async function getBase64ImageFromUrl(imageUrl: any) {
	return axios.get(imageUrl, {
		responseType: 'blob',
		responseEncoding: "base64",
	}).then(async (response) => {

		var reader = new FileReader();
		reader.onloadend = fileLoadedEvent => {
			const base64Image = fileLoadedEvent.target?.result;
			imageService.insertImagesZalo(base64Image).then(result => {
			}).catch(err => console.error(err));
		};
		reader.readAsDataURL(response.data);
	});
}

function phoneValidation() {
	let re = appConst.validateValue.phone;
	if (!validPhone(phone.value)?.length) {
		phoneErr.value = t("LoginComponent.vui_long_nhap_so_dien_thoai");
		return false;
	}
	if (phone.value[0] != '0' && phone.value[0] != '+') {
		phone.value = "0".concat(phone.value)
	}
	if (!re.test(validPhone(phone.value))) {
		phoneErr.value = t("LoginComponent.so_dien_thoai_khong_dung");
		return false;
	}
	else {
		phoneErr.value = "";
		return true;
	}
}

function emailValidation() {
	let re = appConst.validateValue.email;
	if (!email.value?.length) {
		emailErr.value = t("LoginComponent.vui_long_nhap_email");
		return false;
		// }
		// else if (!re.test(email.value)) {
		// 	emailErr.value = "Email không đúng định dạng";
		// 	return false;
	} else {
		emailErr.value = "";
		return true;
	}
}

function passwordValidaion() {
	if (!password.value || !password.value?.length) {
		passwordErr.value = t('LoginComponent.vui_long_nhap_mat_khau')
	}
	else {
		passwordErr.value = ''
	}
}
function isiOS() {
	var Apple = device.isApple;
	var Mac = device.isMacOS;
	var iOS = device.isIos;
	return (Apple || Mac || iOS);
}
function close() {
	router.push(appRoute.HomeComponent);
	// router.replace(redirect_url.value);
}
</script>

<style lang="scss" src="./LoginStyles.scss"></style>