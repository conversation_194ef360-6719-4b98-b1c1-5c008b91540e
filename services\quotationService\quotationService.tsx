
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class QuotationService extends BaseHTTPService {
  readFile(file: File, row_column?: number, row_start_data?: number, column_start?: string, column_end?: string) {
    let formData = new FormData();
    formData.append("file", file, file.name);
    if(row_column) formData.append("row_column", row_column.toString());
    if(row_start_data) formData.append("row_start_data", row_start_data.toString());
    if(column_start) formData.append("column_start", column_start);
    if(column_end) formData.append("column_end", column_end);
    let url = appConst.apiURL.quotationReadFile
    // let url = "http://clomart.xampps/api/v1/quotation/read_excel";
    return this.https('POST', url, formData);
  }

  add(supplier_id = null, name = "", from = "", to ="", notes = "", file = "", material_quotations:any = []){
    let body = {
      supplier_id: supplier_id,
      name: name,
      from: from,
      to: to,
      notes: notes,
      file: file,
      material_quotations: material_quotations
    }
    let url = appConst.apiURL.quotationAdd;
    // let url = "http://clomart.xampps/api/v1/quotation/add";
    return this.https("POST", url, body);
  }

  update(id = null, status = 1, supplier_id = null, name = "", from = "", to ="", notes = "", material_quotations = []){
    let body = {
      id: id,
      status: status,
      supplier_id: supplier_id,
      name: name,
      from: from,
      to: to,
      notes: notes,
      material_quotations: material_quotations
    }
    let url = appConst.apiURL.quotationUpdate;
    // let url = "http://clomart.xampps/api/v1/quotation/add";
    return this.https("POST", url, body);
  }

  request(supplier_ids:any = [], name = "", from:any = "", to:any = "", notes = "", file = "", material_quotations:any = []){
    let body = {
      supplier_ids: supplier_ids,
      name: name,
      from: from,
      to: to,
      notes: notes,
      file: file,
      material_quotations: material_quotations
    }
    let url = appConst.apiURL.quotationRequest;
    // let url = "http://clomart.xampps/api/v1/quotation/add";
    return this.https("POST", url, body);
  }

  list(shop_id:any='', from='', to='', limit='', offset=''){
    let url = appConst.apiURL.quotationList + "?"
      + "shop_id=" + shop_id 
      + "&from=" + from 
      + "&to=" + to 
      + "&limit=" + limit 
      + "&offset=" + offset  ;
    return this.https("GET", url);
  }
  detail(id:string){
    let url = appConst.apiURL.quotationDetail + "?quotation_id=" + id;
    return this.https("GET", url);
  }

  quotationFilter(material_ids = [], from:string|null='', to:string|null='', order='price', sort='asc'){
    let body = {
      material_ids : material_ids,
      from: from,
      to: to,
      order: order,
      sort: sort
    }
    let url = appConst.apiURL.quotationFilter;
    return this.https("POST", url, body);
  }

  saveImportHistory(material_quotations = []){
    let body = {
      material_quotations: material_quotations
    }

    let url = appConst.apiURL.materialSaveImportHistory;
    return this.https("POST", url, body);
  }
}