<template>
	<div class="public-container">
		<div class='order-container'>
			<client-only hidden>
				<LMap id="temp_leaflet_map" v-on:ready="(e: any) => {
					tempLeafletMap = e;
				}"></LMap>
			</client-only>

			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ appRouteTitle.OrderComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<HeaderComponent :title="$t('AppRouteTitle.OrderComponent')">
			</HeaderComponent>
			<div class='v-stack order-content-container'
				v-if="!refreshing && (selectedItemsCart && selectedItemsCart.length)">

				<div class="receiver-info" v-on:click="() => {
					if (userInfo) {
						showSelectUser = true;
						isEditingCustomerInfo = false;
					}
					else {
						isEditingCustomerInfo = true;
						showSelectUser = false;
					}

				}">
					<Icon name="material-symbols:person-pin-circle-rounded"></Icon>
					<div class="user-info">
						<span class="name-phone">
							<span :class="{ 'error': !(orderData.customer_name && orderData.customer_name.length) }">
								{{ orderData.customer_name && orderData.customer_name.length
									? orderData.customer_name
									: $t('OrderComponent.chua_co_ten')
								}}
							</span>

							{{ " | " }}

							<span
								:class="{ 'error': !(validPhone(orderData.customer_phone) && validPhone(orderData.customer_phone).length) }">
								{{ orderData.customer_phone && orderData.customer_phone.length
									? orderData.customer_phone
									: $t('OrderComponent.chua_co_sdt')
								}}
							</span>
						</span>

						<span class="address" :class="{ 'error': !(orderData.address && orderData.address.length) }">
							{{ orderData.address && orderData.address.length
								? orderData.address
								: $t('OrderComponent.chua_co_dia_chi')
							}}
						</span>
					</div>
					<button class="select-receiver">
						<Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
					</button>
				</div>
				<div class="receiver-info" v-on:click="() => {
					showDateTimePickerModal = true;

				}">
					<Icon name="material-symbols:nest-clock-farsight-analog-rounded"></Icon>
					<div class="user-info">
						<span class="name-phone">
							<span>
								{{ $t('OrderComponent.thoi_gian_nhan_hang') }}
							</span>
						</span>

						<span class="address">
							{{ orderData.delivery_time?.length
								? orderData.delivery_time
								: $t('OrderComponent.bay_gio')
							}}
						</span>
					</div>
					<button class="select-receiver">
						<Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
					</button>
				</div>
				<div class="cart-selected-items">
					<div class="cart-item-container" v-for=" (itemCart, index) in selectedItemsCart">
						<img loading="lazy" :src="(itemCart && itemCart.product?.profile_picture)
							? (domainImage + itemCart.product?.profile_picture)
							: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemCart.product)" />
						<div class='v-stack item-cart-detail'>
							<div class='h-stack item-cart-product-name'
								:title="showTranslateProductName(itemCart.product)">
								<span>
									{{ showTranslateProductName(itemCart.product) }}
								</span>
							</div>
							<span class='h-stack product-price'>
								x{{ itemCart.quantity.toString().replaceAll('.', ',') }} &nbsp;|&nbsp;
								<!-- {{
								(itemCart.product.price_off != null && itemCart.product.price_off < itemCart.product.price)
									? formatCurrency(parseFloat(itemCart.product.price_off),
										itemCart.product.shop?.currency) : (parseFloat(itemCart.product.price) == 0 ||
											parseFloat(itemCart.product.price) == null) ? $t('OrderComponent.gia_lien_he') :
										formatCurrency(parseFloat(itemCart.product.price), itemCart.product.shop?.currency) }}
								<em class="off"
								v-if="(itemCart.product.price_off != null && itemCart.product.price_off < itemCart.product.price)">
								{{
									(parseFloat(itemCart.product.price) == 0 || parseFloat(itemCart.product.price) == null)
										? $t('OrderComponent.gia_lien_he')
										: formatCurrency(parseFloat(itemCart.product.price), itemCart.product.shop?.currency)
								}}
								</em> -->
								{{
									(itemCart.product.price_off != null && itemCart.product.price_off <
										itemCart.product.price) ? formatCurrency(parseFloat(itemCart.product.price_off),
											itemCart.product.shop?.currency) : (parseFloat(itemCart.product.price) == 0 ||
												itemCart.product.price == null) ? $t('OrderComponent.gia_lien_he') :
										formatCurrency(parseFloat(itemCart.product.price), itemCart.product.shop?.currency)
								}} <em class="off"
									v-if="(itemCart.product.price_off != null && itemCart.product.price_off < itemCart.product.price)">
									{{
										formatCurrency(itemCart.product.price ? parseFloat(itemCart.product.price) : 0,
											itemCart.product.shop?.currency)
									}}</em>

							</span>
							<span class="product-notes">
								{{ itemCart.notes }}
							</span>
						</div>
					</div>
				</div>
				<div class="payment-method">
					<Icon class="icon-label" name="carbon:wallet"></Icon>
					<div class="content-container">
						<span class="label">
							{{ $t('OrderComponent.hinh_thuc_thanh_toan') }}
						</span>
						<span class="content">
							{{ $t('OrderComponent.thanh_toan_khi_nhan_hang') }}
						</span>
					</div>
				</div>

				<div class="payment-method">
					<Icon class="icon-label" name="carbon:delivery-parcel"></Icon>
					<div class="content-container">
						<span class="label">
							{{ $t('OrderComponent.phuong_thuc_nhan_hang') }}
						</span>
						<span class="h-stack content">
							<div class="content-item" :class="{ 'active': !deliveryType }" v-on:click="() => {
								deliveryType = false;
								checkDeliveryPrice(driving_distance);
							}">
								<Icon name="ic:outline-radio-button-checked" v-if="!deliveryType"></Icon>
								<Icon name="ic:outline-radio-button-unchecked" v-else></Icon>
								<span>{{ $t('OrderComponent.giao_tan_noi') }}</span>
							</div>
							<div class="content-item" :class="{ 'active': deliveryType }" v-on:click="() => {
								deliveryType = true;
								deliveryPrice = 0;
							}">
								<Icon name="ic:outline-radio-button-checked" v-if="deliveryType"></Icon>
								<Icon name="ic:outline-radio-button-unchecked" v-else></Icon>
								<span>{{ $t('OrderComponent.tu_lay_hang') }}</span>
							</div>

						</span>
						<span class="delivery-price" v-if="deliveryPrice != null">
							{{ $t('OrderComponent.phi_van_chuyen') }}: {{ formatCurrency(deliveryPrice || 0) }}
						</span>
					</div>
				</div>

				<div class='note-container'>
					<Icon class="icon-label" name="uil:comment-alt-notes"></Icon>
					<div class="content-container">
						<span class='title'>
							{{ $t('OrderComponent.ghi_chu_cho_cua_hang') }}
							<em class="text-length">({{ orderData.notes?.length ?? 0 }}/{{ appConst.max_text_short }})</em>
						</span>
						<div class='h-stack note-order-input-container'>
							<textarea :placeholder="$t('OrderComponent.ghi_chu_cho_cua_hang_placeholder')"
							:maxlength="appConst.max_text_long"
								v-model="orderData.notes" name="note-cart" id="note-cart" rows={5}
								class='note-order-input'></textarea>
						</div>
					</div>


				</div>

				<div class="payment-info-container">
					<div class="payment-info-content">
						<span class="title">
							{{ $t('OrderComponent.tam_tinh') }}
						</span>
						<span class="value">
							{{
								getCartTotalPrice() ?
									formatCurrency(getCartTotalPrice() || 0, selectedItemsCart[0].shop.currency)
									: $t('OrderComponent.gia_lien_he')
							}}
						</span>
					</div>
					<div class="payment-info-content off">
						<span class="title">
							{{ $t('OrderComponent.giam_gia') }}
						</span>
						<span class="value">
							{{ formatCurrency(((getCartTotalPriceOff() || 0) - (getCartTotalPrice() || 0)) || 0,
								selectedItemsCart[0].shop.currency) }}
						</span>
					</div>
					<div class="payment-info-content off">
						<span class="title">
							{{ $t('OrderComponent.phi_van_chuyen') }}
							<span v-if="driving_distance" class="distance">
								({{ driving_distance }} km)
							</span>

						</span>
						<span class="value">
							{{ deliveryPrice != null ? formatCurrency(deliveryPrice || 0) :
								$t('OrderComponent.doi_bao_gia') }}
						</span>
					</div>
					<div class="payment-info-content total-left">
						<span class="title">
							{{ $t('OrderComponent.thanh_tien') }}
						</span>
						<span class="value">
							{{
								getCartTotalPriceOff() ?
									formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice || 0).toString()),
										selectedItemsCart[0].shop.currency)
									: $t('OrderComponent.gia_lien_he')
							}}
							<!-- <span class="note" v-if="!driving_distance">
								{{ $t('OrderComponent.chua_bao_gom_phi_van_chuyen') }}
							</span> -->
						</span>
					</div>

				</div>
				<div class="order-footer">
					<div class="order-footer-content">
						<div class="total">
							<span>{{ $t('OrderComponent.tong_tien') }}</span>
							<span class="price">{{
								getCartTotalPriceOff() ?
									formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice ||
										0).toString()),
										selectedItemsCart[0].shop.currency)
									: $t('OrderComponent.lien_he_de_biet_gia')
							}}</span>
						</div>
						<button :disabled=isSubmiting v-on:click="() => {
							submitOrder()
						}">
							<Icon name="eos-icons:loading" size="20" v-if="isSubmiting" />{{
								$t('OrderComponent.dat_hang')
							}}
						</button>
					</div>

				</div>
			</div>
			<div class="v-stack empty-cart" v-if="!refreshing && (!selectedItemsCart || !selectedItemsCart.length)">
				<img loading="lazy" :src='none_cart' :placeholder="none_cart" class="empty-cart-avt" />

				<span class="text">
					{{ $t('OrderComponent.gio_hang_trong') }}
				</span>
				<button class="action" v-on:click="() => {
					router.push({
						path: appRoute.AroundComponent,
						query: {
							isShowList: 'true'
						}
					})
				}">
					{{ $t('OrderComponent.hay_them_san_pham_vao_gio_hang') }}
				</button>

			</div>
			<VueFinalModal class="my-modal-container" :click-to-close="false" :overlay-behavior="'persist'"
				content-class="my-modal-content-container edit-customer-info-modal" v-model="isEditingCustomerInfo"
				v-on:closed="() => {
					isEditingCustomerInfo = false;
					if (latitude && longitude) {
						getDistance()
					}
				}" contentTransition="vfm-slide-up">
				<div class='v-stack edit-customer-info-container'>
					<span class='title'>
						{{ $t('OrderComponent.thong_tin_nguoi_nhan') }}
					</span>

					<div class='v-stack' v-if="false">
						<USelectMenu class="dropdown-select-container" variant="none" :options="dataSavedUsers"
							placeholder="{{ $t('OrderComponent.thong_tin_da_luu') }}" v-on:change="(newValue: any) => {
								name = newValue.name;
								phone = newValue.phone;
								address = newValue.address;
							}" option-attribute="customer_name">
							<template #label>
								<span class="truncate">{{
									dataSavedUsers[
										dataSavedUsers.findIndex((e: any) => {
											return e.name == orderData.customer_name
										})
									]?.name || $t('OrderComponent.thong_tin_da_luu') }}</span>
							</template>
							<template #option="{ option: userOption }">
								<span class="truncate">{{ userOption.name }}</span>
							</template>
							<template #empty>
								{{ $t('OrderComponent.chua_luu_thong_tin') }}
							</template>
						</USelectMenu>
					</div>
					<button class="select-user-saved" v-if="userInfo" v-on:click="() => {
						showSelectUser = true;
						isEditingCustomerInfo = false;
					}">
						{{ $t('OrderComponent.chon_tu_so_dia_chi') }}
						<!-- <Icon name="material-symbols:person-pin-circle-outline"></Icon> -->
					</button>
					<div class='v-stack'>
						<span class='label required'>
							{{ $t('OrderComponent.ten') }}
						</span>
						<input :title="$t(' OrderComponent.ten')" name='customer-name' maxLength=255 autoComplete="off"
							class='input-order' :placeholder="$t('OrderComponent.ten_placeholder')"
							:value="name || null" v-on:input="($event: any) => {
								name = $event.target.value;
								nameValidation()
							}" v-on:blur="() => {
								nameValidation()
							}" />
						<span class='error-message'>{{ nameErr }}</span>
					</div>
					<div class='v-stack'>
						<span class='label required'>
							{{ $t('OrderComponent.so_dien_thoai') }}
						</span>
						<input :title="$t(' OrderComponent.so_dien_thoai')" name='customer-phone' maxLength=255
							type="phone" autoComplete="off" class='input-order'
							:placeholder="$t('OrderComponent.so_dien_thoai_placeholder')" :value="phone || null"
							v-on:input="($event: any) => {
								phone = validPhone($event.target.value);
								phoneValidation();
							}" v-on:blur="() => {
								phoneValidation();
							}" />
						<span class='error-message'>{{ phoneErr }}</span>
					</div>
					<div class='v-stack'>
						<span class='label required'>
							{{ $t('OrderComponent.dia_chi') }}
						</span>
						<input :title="$t('OrderComponent.dia_chi')" name='customer-address' maxLength=255
							autoComplete="off" class='input-order'
							:placeholder="$t('OrderComponent.dia_chi_placeholder')" :value="address || null" v-on:input="($event: any) => {
								address = $event.target.value;
								addressValidation()
							}" v-on:blur="() => {
								addressValidation()
							}" />
						<span class='error-message'>{{ addressErr }}</span>
					</div>
					<div class="map-container">
						<client-only>
							<LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
								leafletMap = e;
								leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

								initLeafletMap();
							}" :max-zoom="appConst.leafletMapTileOption.maxZoom" :ondragend="async (bounds: any) => {
								console.log('center change');
								latitude = leafletMap.getCenter().lat;
								longitude = leafletMap.getCenter().lng;
								await getUserAddress();

							}" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true" :use-global-leaflet="true">
								<LControlZoom position="bottomright"></LControlZoom>
								<span class="current-location-leaflet" title="{{ $t('OrderComponent.vi_tri_cua_ban') }}"
									v-on:click="() => {
										gotoCurrentLocationLeaflet();
									}
										">
									<Icon name="line-md:my-location-loop" class="my-location-icon" />
								</span>
								<div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
									data-placement="right" title="{{ $t('OrderComponent.nhan_de_chuyen_loai_map') }}"
									v-bind:style="{
										backgroundImage: `url(` + buttonMapTileBackgound + `)`,
									}" v-on:click="(event: any) => {
										if (event.isTrusted) {
											if (
												leafletMapTileUrl ==
												appConst.leafletMapTileUrl.roadmap
											) {
												leafletMapTileUrl =
													appConst.leafletMapTileUrl.hyprid;
												mapTypeTitle = $t('OrderComponent.ve_tinh');
												mapType = 'hyprid';
												buttonMapTileBackgound = map_sateline;
											} else if (
												leafletMapTileUrl ==
												appConst.leafletMapTileUrl.hyprid
											) {
												leafletMapTileUrl =
													appConst.leafletMapTileUrl.streetmap;
												mapTypeTitle = $t('OrderComponent.co_dien');
												mapType = 'hyprid';
												buttonMapTileBackgound = map_streetmap;
											} else if (
												leafletMapTileUrl ==
												appConst.leafletMapTileUrl.streetmap
											) {
												leafletMapTileUrl =
													appConst.leafletMapTileUrl.roadmap;
												mapTypeTitle = $t('OrderComponent.ve_tinh_nhan');
												mapType = 'roadmap';
												buttonMapTileBackgound = map_sateline;
											}
										} else event.preventDefault();
									}
										">
									<span>{{ mapTypeTitle }}</span>
								</div>
								<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
									:max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
									name="GoogleMap">
								</LTileLayer>
								<div class="marker-location">
									<img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon"
										alt="" />
								</div>
							</LMap>
						</client-only>
					</div>
					<div class='h-stack save-info-container' v-if="false">
						<label :disabled="isSubmiting">
							<input type="radio" class='save-user-info-icon' :disabled="isSubmiting"
								:checked=saveUserInfo v-on:click="() => { saveUserInfo = !saveUserInfo }"
								:value=saveUserInfo />
							{{ " " }}{{ $t('OrderComponent.luu_thong_tin') }}
						</label>
					</div>

					<div class='h-stack edit-customer-info-actions'>
						<button class='reject-button' :disabled=isSavingUserInfo v-on:click="() => {
							name = orderData.customer_name;
							phone = validPhone(orderData.customer_phone);
							address = orderData.address;
							latitude = orderData.latitude;
							longitude = orderData.longitude;
							isEditingCustomerInfo = false;
							phoneValidation();

						}">
							{{ $t('OrderComponent.huy') }}
						</button>
						<button class='accept-button' :disabled="isSavingUserInfo == true
							|| !name?.length
							|| !phone?.length
							|| !address?.length
							|| nameErr.length > 0
							|| phoneErr.length > 0
							" v-on:click="() => {
								updateUserInfo()
							}">
							{{ $t('OrderComponent.xac_nhan') }}
						</button>
					</div>

					<!-- <v-overlay v-model="showSelectUser" :z-index="1000" :absolute="false" :width="'100%'"
						:height="'100%'" persistent no-click-animation contained v-on:click:outside="() => {
							// showSelectUser = false;
						}
							">
						<div class="list-saved-user-container">
							<div class="close" v-on:click="() => {
								showSelectUser = false;
							}">
								<Icon name="clarity:times-line" size="25"></Icon>
							</div>
							<span class="title">
								{{ $t('OrderComponent.chon_thong_tin_nguoi_nhan') }}
							</span>
							<div class="saved-user-list" v-if="dataSavedUsers?.length > 0">
								<div v-for="(itemUser, index) in dataSavedUsers" class="item-saved-user" v-on:click="async () => {
									latitude = itemUser.latitude;
									longitude = itemUser.longitude;
									name = itemUser.name;
									phone = itemUser.phone;
									address = itemUser.address;
									province_id = itemUser.province_id;
									district_id = itemUser.district_id;
									ward_id = itemUser.ward_id;
									await leafletMap.setView([latitude, longitude], leafletMap.getZoom());
									showSelectUser = false;
								}">
									<div class="name-phone">
										<span class="name">
											{{ itemUser.name }}
										</span>
										<span class="phone">
											{{ itemUser.phone }}
										</span>
									</div>
									<div class="address">
										<span class="address-type" :class="{
											'home': itemUser.title == t('SavedAddressComponent.nha_rieng'),
											'work': itemUser.title == t('SavedAddressComponent.cong_ty'),
											'others': itemUser.title != t('SavedAddressComponent.nha_rieng') && itemUser.title != $t('SavedAddressComponent.cong_ty'),
										}">{{ itemUser.title }}</span>
										{{ itemUser.address }}
									</div>
									<span class="selected" v-if="checkSelectedUserInfo(itemUser)">
										<Icon name="mingcute:flag-3-fill"></Icon>
										{{ $t('OrderComponent.dang_chon') }}
									</span>
								</div>
							</div>
							<div class="none-user-list" v-else>
								<img loading="lazy" :src="none_save_user" :placeholder="none_save_user" alt="" />
								<span>
									{{ $t('OrderComponent.chua_co_thong_tin_nao_duoc_luu') }}
								</span>
							</div>
						</div>
					</v-overlay> -->
				</div>


			</VueFinalModal>
			<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
				content-class="my-modal-content-container saved-address-modal-container" :click-to-close="false"
				v-model="showSelectUser" v-on:closed="() => {
					showSelectUser = false
				}" contentTransition="vfm-slide-up">
				<SavedAddressComponent :mode="'order'" :selectedObject="{
					name: orderData.customer_name,
					phone: orderData.customer_phone,
					address: orderData.address,
					latitude: orderData.latitude,
					longitude: orderData.longitude,
				}" v-on:close="() => {
					showSelectUser = false;
				}" v-on:enterTempAddres="() => {
					showSelectUser = false;
					isEditingCustomerInfo = true;
				}" v-on:selectedUser="(selectedObj: any) => {
					showSelectUser = false;
					setDataOnSelected(selectedObj);
				}"></SavedAddressComponent>
			</VueFinalModal>
			<VueFinalModal class="my-modal-container" content-class="complete-order-container" :overlay-behavior="'persist'" :click-to-close="false"
				v-model="orderComplete" v-on:closed="() => {
					orderComplete = false
				}" contentTransition="vfm-slide-up">
				<div class='v-stack complete-order-container'>
					<img loading="lazy" :src='order_complete' :placeholder="order_complete"
						:alt="$t('OrderComponent.don_hang_da_duoc_gui')" class='complete-order-image' />
					<span class='complete-order-message'>
						{{ $t('OrderComponent.don_cua_ban_da_duoc_gui') }}
					</span>
					<button class='complete-order-action' v-on:click="() => {
						router.push(appRoute.HomeComponent)
					}">
						{{ $t('OrderComponent.dat_don_khac') }}
					</button>
				</div>

			</VueFinalModal>
		</div>
		<DateTimePickerComponent v-if="showDateTimePickerModal" :title="t('OrderComponent.thoi_gian_nhan_hang')"
			:startDate="moment()" :endDate="moment().add(30, 'days')"
			:startTime="moment().hour(7).minutes(0).seconds(0).milliseconds(0)"
			:endTime="moment().hour(22).minutes(31).seconds(0).milliseconds(0)"
			:initialDate="orderData.delivery_time ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY') : null"
			:initialTime="orderData.delivery_time ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('HH:mm') : null"
			:stepMinute="5" :firstStepMinute="60" v-on:close="() => {
				showDateTimePickerModal = false
			}" v-on:submit="(e: any) => {
				showDateTimePickerModal = false
				orderData.delivery_time = e;
				checkDeliveryPrice(driving_distance)
			}"></DateTimePickerComponent>
	</div>

</template>

<script lang="ts" setup>

import type { Icon } from 'leaflet';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, showTranslateProductName, showTranslateProductDescription, validPhone } from "~/assets/AppConst";
import type { CartDto, OrderDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { OrderService } from '~/services/orderService/orderService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";
import moment from 'moment'
import { PlaceService } from '~/services/placeService/placeService';
import icon_for_product from '~/assets/image/icon-for-product.png';
import none_save_user from "~/assets/image_08_05_2024/none-save-user.jpg";
import order_complete from "~/assets/image/order-complete.png"
import { DeliveryService } from '~/services/orderService/deliveryService';
import { UserAddressService } from '~/services/userAddressService/userAddressService';
import SavedAddressComponent from '../savedAddress/SavedAddressComponent.vue';
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

const nuxtApp = useNuxtApp();

var router = useRouter();
var route = useRoute();
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.OrderComponent')
})
var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var categoryService = new CategoryService();
var imageService = new ImageService();
var orderService = new OrderService();
var placeService = new PlaceService();
var deliveryService = new DeliveryService();
var userAddressService = new UserAddressService();

var webInApp = ref(null as any);

var leafletMap: L.Map;
var tempLeafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('OrderComponent.ve_tinh_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var latitude = ref(null as any);
var longitude = ref(null as any);
var disabledDragTab = ref(false);
var buttonMapTileBackgound = ref(map_sateline);

var cartData = ref([] as CartDto[]);

var userInfo = ref(null as any);
var orderData = ref({} as OrderDto);
var deliveryType = ref(false);
var deliveryPrice = ref(0);
var paymentMethod = ref(1);
var name = ref("");
var nameErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var address = ref("");
var addressErr = ref("");
var province_id = ref(null as any);
var district_id = ref(null as any);
var ward_id = ref(null as any);
var isSubmiting = ref(false);
var orderComplete = ref(false);
var dataSavedUsers = ref([] as any);
var refreshing = ref(true);
var editing = ref(false);
var isEditingCustomerInfo = ref(false);
var isSavingUserInfo = ref(false);
var saveUserInfo = ref(false);
var showSelectUser = ref(false);
var showDateTimePickerModal = ref(false);

var searchProductTimeout: any;
var searchAddressTimeout: any;

var driving_distance = ref(0);
var selectedItemsCart = ref(router.options.history.state.selectedItemsCart ? JSON.parse(router.options.history.state.selectedItemsCart as string) : []);
var control: any;

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
// watch(() => [user_latitude.value, user_longitude?.value], () => {
// 	latitude.value = user_latitude?.value;
// 	longitude.value = user_longitude?.value;
// });
onUnmounted(() => {
	nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})

onBeforeMount(async () => {

	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	refreshing.value = true;

	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		console.log('moving', coor);
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;
	});
	// let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

	// dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];

})
onMounted(async () => {

	let cartDataCurrent = await localStorage.getItem(appConst.storageKey.cart);
	cartData.value = cartDataCurrent ? JSON.parse(cartDataCurrent as string) : [];

	selectedItemsCart.value.forEach((element: any) => {
		let indexInCart = cartData.value.findIndex(function (e) {
			return e.product_id == element.product_id;
		})
		if (indexInCart != -1) element = JSON.parse(JSON.stringify(cartData.value[indexInCart]));
	});

	selectedItemsCart.value = selectedItemsCart.value?.length ? selectedItemsCart.value : JSON.parse(cartDataCurrent as string);

	let userInfoCurrent = await localStorage.getItem(appConst.storageKey.userInfo);
	userInfo.value = JSON.parse(userInfoCurrent as string);
	if (userInfo.value) {
		orderData.value = {
			...orderData.value,
			customer_id: userInfo.value ? userInfo.value.id : null,
		};
		setDataOnSelected(userInfo.value);
	}
	await getMyListAddress();

	if (orderData.value.latitude && orderData.value.longitude && !orderData.value.address?.length) {
		// isEditingCustomerInfo.value = true;
		getCurrentLocation();
	}

	// setFirstDeliveryTime();

	refreshing.value = false;

	setTimeout(() => {
		nameValidation();
		phoneValidation();
		addressValidation();
	}, 500);

})
onBeforeRouteUpdate(() => {

})
async function getMyListAddress() {
	let res = await userAddressService.myListAddress();
	if (res.status == HttpStatusCode.Ok) {
		dataSavedUsers.value = res.body.data;
		let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
			return e.is_default == true;
		})

		if (indexDefault != -1) {
			setDataOnSelected(dataSavedUsers.value[indexDefault]);
		}
		else {
			setDataOnSelected(dataSavedUsers.value[0]);

		}
		setTimeout(() => {
			getDistance();
		}, 1000);
	}
	else {
		dataSavedUsers.value = [];
	}
}
function getCartTotalPrice() {
	if (!selectedItemsCart.value.length) return 0;
	let total = 0;
	let indexPriceNull = selectedItemsCart.value.findIndex((e: any) => {
		return (e.product.price == 0 || e.product.price == null)
	})
	if (indexPriceNull != -1) return null;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (parseFloat(current.product.price.toString()) ? parseFloat(current.product.price.toString()) * current.quantity : 0), 0
		)
}
function getCartTotalPriceOff() {
	if (!selectedItemsCart.value.length) return 0;
	let total = 0;
	let indexPriceNull = selectedItemsCart.value.findIndex((e: any) => {
		return (e.product.price_off != null && e.product.price_off < e.product.price) ? (e.product.price_off == 0 || e.product.price_off == null) : (e.product.price == 0 || e.product.price == null)
	})
	if (indexPriceNull != -1) return null;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (
				(current.product.price_off)
					? parseFloat(current.product.price_off.toString()) * current.quantity
					: parseFloat(current.product.price.toString()) * current.quantity
			), 0)
}

function getCartTotalProduct() {
	if (!selectedItemsCart.value.length) return 0;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (current.quantity ? current.quantity : 0), 0
		)
}

function nameValidation() {
	if (!name.value || !name.value.length) {
		nameErr.value = t('OrderComponent.vui_long_nhap_ten_nguoi_nhan')
	}
	else {
		nameErr.value = '';
	}
}

function phoneValidation() {
	let re = appConst.validateValue.phone;
	if (!validPhone(phone.value) || !validPhone(phone.value).length) {
		phoneErr.value = t('OrderComponent.vui_long_nhap_sdt')
		return;
	}
	if (!re.test(validPhone(phone.value))) {
		phoneErr.value = t('OrderComponent.sdt_khong_dung');
		return;
	}
	else {
		phoneErr.value = '';
	}
}

function addressValidation() {
	if (address.value?.length) {
		addressErr.value = "";
	}
	else addressErr.value = t('OrderComponent.vui_long_nhap_dia_chi');
}

async function submitOrder() {
	if (!latitude.value || !longitude.value!!) {
		isEditingCustomerInfo.value = true;
		return;
	}
	// if (!orderData.value.delivery_time?.length) {
	// 	showDateTimePickerModal.value = true;
	// 	return;
	// }
	isSubmiting.value = true;
	orderData.value = {
		...orderData.value,
		price: getCartTotalPrice(),
		price_off: getCartTotalPriceOff(),
		delivery_type: deliveryType.value,
		delivery_price: deliveryPrice.value || 0,
		delivery_time: orderData.value?.delivery_time?.length ? moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").format("YYYY-MM-DD HH:mm") : null,
		// 	delivery_price: 0,
		payment_method: paymentMethod.value,
		items: selectedItemsCart.value,
		shop_id: selectedItemsCart.value[0].shop_id,
		delivery_distance: driving_distance.value,
		province_id: province_id.value,
		district_id: district_id.value,
		ward_id: ward_id.value,
		notes: orderData.value.notes ? orderData.value.notes : ''
		// delivery: {
		// 	latitude_from: selectedItemsCart.value[0]?.shop?.latitude,
		// 	longitude_from: selectedItemsCart.value[0]?.shop?.longitude,
		// 	latitude_to: latitude.value,
		// 	longitude_to: longitude.value,
		// 	distance: driving_distance.value,
		// 	driver_id: null,
		// }
	}
	orderService.create(orderData.value).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			// localStorage.removeItem(appConst.storageKey.cart);
			clearNuxtState('cart_selected');
			selectedItemsCart.value.forEach((e: any) => {
				let indexInCart = cartData.value.findIndex(function (eCart) {
					return eCart.product_id == e.product_id
				})

				if (indexInCart != -1) {
					cartData.value.splice(indexInCart, 1);
				}
			});

			// router.options.history.state.selectedItemsCart = null;
			// router.currentRoute.value.params.selectedItemsCart = [];
			orderComplete.value = true;
			localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));
			selectedItemsCart.value = [];
			isSubmiting.value = false;
			router.push({
				path: appRoute.OrderComponent,
				force: true,
				state: {
					selectedItemsCart: null
				},
				replace: true
			})

			nuxtApp.$emit('cart_change');
		}
		else {
			isSubmiting.value = false;
			toast.error(t('OrderComponent.dat_hang_that_bai'), {
				autoClose: 1000
			});
		}
	}).catch(err => {
		console.log(err);
		toast.error(t('OrderComponent.dat_hang_that_bai'), {
			autoClose: 1000,
		});
		isSubmiting.value = false;
	})
}

function updateUserInfo() {
	if (saveUserInfo.value) {
		let arr = JSON.parse(JSON.stringify(dataSavedUsers.value));
		let index = arr.findIndex((e: any) => {
			return e.name == name.value
				&& e.phone == phone.value
				&& e.address == address.value
				&& e.latitude == latitude.value
				&& e.longitude == longitude.value
		})

		if (index == -1 && userInfo?.value?.id) {
			let info = {
				id: null,
				name: name.value,
				address: address.value,
				phone: validPhone(phone.value),
				latitude: latitude.value,
				longitude: longitude.value,
				province_id: province_id.value,
				district_id: district_id.value,
				ward_id: ward_id.value,
				title: "new",
				is_default: false
			}
			// arr.push({
			// 	customer_name: name.value,
			// 	customer_phone: phone.value,
			// 	address: address.value,
			// 	latitude: latitude.value,
			// 	longitude: longitude.value,

			// });
			// localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(arr));
			// dataSavedUsers.value = arr;
			userAddressService.create(info).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					getMyListAddress();
				}
				else {
					toast.error(t('SavedAddressComponent.luu_dia_chi_that_bai'))
				}
			})
		}

	}
	orderData.value = {
		...orderData.value,
		customer_name: name.value,
		customer_phone: validPhone(phone.value),
		address: address.value,
		latitude: latitude.value,
		longitude: longitude.value,
	};
	isEditingCustomerInfo.value = false;
}
async function getDistance() {
	if (control) {
		control.remove();
	}

	control = nuxtApp.$L.Routing.control({
		waypointMode: 'connect',
		router: nuxtApp.$L.Routing.osrmv1({
			serviceUrl: appConst.urlOSRMv1,
			requestParameters: {
				overview: 'full',
				annotations: true,
				steps: true,
				alternatives: 2,

			},
		}),
		plan: new nuxtApp.$L.Routing.Plan([
			nuxtApp.$L.latLng(selectedItemsCart.value[0]?.shop?.latitude, selectedItemsCart.value[0]?.shop?.longitude),
			nuxtApp.$L.latLng(latitude.value, longitude.value)
		], {
			createMarker: () => (false),
		}),
	})
	if (tempLeafletMap) {
		control.addTo(tempLeafletMap).on('routesfound', (e: any) => {
			driving_distance.value = parseFloat((e.routes[0].summary.totalDistance / 1000).toFixed(1));
			checkDeliveryPrice(driving_distance.value);
		});
	}

}
function checkDeliveryPrice(distance = 0) {
	if (!deliveryType.value) {
		let duration =
			(!orderData.value.delivery_time || moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes') < 30)
				? 30
				: moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes');
		deliveryService.checkDeliveryPrice(distance, duration).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				deliveryPrice.value = res.body.data
			}
		})
	}
	else {
		deliveryPrice.value = 0
	}

}

async function initLeafletMap() {
	// markersCluster = new nuxtApp.$L.MarkerClusterGroup({
	// 	maxClusterRadius: 5,
	// 	iconCreateFunction: (cluster) => createClusterElement(cluster),
	// }).addTo(leafletMap);
	console.log("init map")
	await setCurrentLocationLeaflet();
	(leafletMap as any)["gestureHandling"].enable();
}
async function setCurrentLocationLeaflet() {
	if (latitude.value && longitude.value) {
		console.log("có lat lng");
		leafletMap.setView([latitude.value, longitude.value], 17);
		// setLocationLeafletMarker(latitude.value, longitude.value);
	} else {

		// if ("geolocation" in navigator) {
		// 	navigator.geolocation.getCurrentPosition(
		// 		(position) => {

		// 			latitude.value = position.coords.latitude;
		// 			longitude.value = position.coords.longitude;
		// 			leafletMap.setView(
		// 				[position.coords.latitude, position.coords.longitude],
		// 				17
		// 			);
		// 			// getUserAddress()
		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
		// 		},
		// 		(error) => {
		// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
		// 			// 	autoClose: 1000,
		// 			// 	hideProgressBar: true,
		// 			// });
		// 			latitude.value = appConst.defaultCoordinate.latitude;
		// 			longitude.value = appConst.defaultCoordinate.longitude;
		// 			leafletMap.setView([latitude.value, longitude.value], 17);
		// 			// getUserAddress()
		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
		// 		},
		// 		{
		// 			enableHighAccuracy: false, // Use less accurate but faster methods
		// 			timeout: 5000, // Set a timeout (in milliseconds)

		// 		}
		// 	);
		// }
		latitude.value = user_latitude.value;
		longitude.value = user_longitude.value;
		leafletMap.setView(
			[user_latitude.value, user_longitude.value],
			17
		);
	}
}
function getCurrentLocation() {
	// if ("geolocation" in navigator) {
	// 	navigator.geolocation.getCurrentPosition(
	// 		(position) => {

	// 			latitude.value = position.coords.latitude;
	// 			longitude.value = position.coords.longitude;
	// 			getDistance()
	// 		},
	// 		(error) => {
	// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
	// 			// 	autoClose: 1000,
	// 			// 	hideProgressBar: true,
	// 			// });
	// 			// latitude.value = appConst.defaultCoordinate.latitude;
	// 			// longitude.value = appConst.defaultCoordinate.longitude;
	// 			// getDistance()
	// 		},
	// 		{
	// 			enableHighAccuracy: false, // Use less accurate but faster methods
	// 			timeout: 5000, // Set a timeout (in milliseconds)

	// 		}
	// 	);
	// }
	latitude.value = user_latitude.value;
	longitude.value = user_longitude.value;
	getDistance()
}
function getUserAddress() {
	clearTimeout(searchAddressTimeout);
	searchAddressTimeout = setTimeout(() => {
		placeService
			.myGeocoderByLatLngToAddress(latitude.value, longitude.value)
			.then((res: any) => {
				if (res.body.data && res.body.data.length) {
					address.value = res.body.data[0].address
						? res.body.data[0].address
						: "";
					province_id.value = res.body.data[0].province_id ? res.body.data[0].province_id : null;
					district_id.value = res.body.data[0].district_id ? res.body.data[0].district_id : null;
					ward_id.value = res.body.data[0].ward_id ? res.body.data[0].ward_id : null;
					addressValidation()
				}
			});
	}, 500);

}

async function gotoCurrentLocationLeaflet(event?: Event) {
	if (!event || event.isTrusted == true) {

		// if ("geolocation" in navigator) {
		// 	navigator.geolocation.getCurrentPosition(
		// 		(position) => {
		// 			leafletMap.flyTo(
		// 				[position.coords.latitude, position.coords.longitude],
		// 				17
		// 			);
		// 			latitude.value = position.coords.latitude;
		// 			longitude.value = position.coords.longitude;
		// 			getUserAddress()
		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
		// 		},
		// 		(error) => {
		// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
		// 			// 	autoClose: 1000,
		// 			// 	hideProgressBar: true,
		// 			// });
		// 			latitude.value = appConst.defaultCoordinate.latitude;
		// 			longitude.value = appConst.defaultCoordinate.longitude;
		// 			leafletMap.flyTo([latitude.value, longitude.value], 17);
		// 			getUserAddress()
		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
		// 		},
		// 		{
		// 			enableHighAccuracy: false, // Use less accurate but faster methods
		// 			timeout: 5000, // Set a timeout (in milliseconds)

		// 		}
		// 	);
		// }

		leafletMap.flyTo(
			[user_latitude.value, user_longitude.value],
			17
		);
		latitude.value = user_latitude.value;
		longitude.value = user_longitude.value;
		getUserAddress()

	}
}

function checkSelectedUserInfo(userInfo: any) {
	return userInfo.name == name.value
		&& userInfo.phone == phone.value
		&& userInfo.address == address.value
		&& userInfo.latitude == latitude.value
		&& userInfo.longitude == longitude.value
}

function setFirstDeliveryTime() {
	let currentDate = moment();

	if (moment().minutes() > 30) {
		currentDate.add(1, 'hours').minutes(0).seconds(0);
	}
	else {
		currentDate.minutes(30).seconds(0);
	}

	orderData.value.delivery_time = currentDate.format("DD/MM/YYYY HH:mm")
}

function setDataOnSelected(selectedObj: any) {
	orderData.value = {
		...orderData.value,
		customer_name: selectedObj ? selectedObj.name : null,
		customer_phone: selectedObj ? selectedObj.phone : null,
		address: selectedObj ? selectedObj.address : null,
		latitude: selectedObj ? selectedObj.latitude : null,
		longitude: selectedObj ? selectedObj.longitude : null,
		province_id: selectedObj ? selectedObj.province_id : null,
		district_id: selectedObj ? selectedObj.district_id : null,
		ward_id: selectedObj ? selectedObj.ward_id : null
	};
	name.value = selectedObj ? selectedObj.name : null;
	phone.value = selectedObj ? selectedObj.phone : null;
	address.value = selectedObj ? selectedObj.address : null;
	latitude.value = selectedObj ? selectedObj.latitude : null;
	longitude.value = selectedObj ? selectedObj.longitude : null;
	province_id.value = selectedObj ? selectedObj.province_id : null;
	district_id.value = selectedObj ? selectedObj.district_id : null;
	ward_id.value = selectedObj ? selectedObj.ward_id : null;
	getDistance();
}
</script>

<style lang="scss" src="./OrderStyles.scss"></style>
