@media screen and (max-width: 765px) {
  // .shop-direction-container {
  //   height: 75% !important;
  // }
}

.shop-direction-overlay-container {
  overflow: hidden;
  z-index: 1001 !important;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .shop-direction-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    max-width: var(--max-width-content-view-1024);
    width: 100%;
    height: fit-content;
    min-height: fit-content;
    border-radius: 50px 50px 0 0;
    animation: slide-up 0.5s ease;
  }
}

.direction-container{
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 1rem;

  & > .title-header-direction{
    display: flex;
    position: relative;
    justify-content: center;
    font-weight: bold;
    border: none;
    padding: 15px;
    box-shadow: 0 10px 10px white;
    z-index: 100;
    
    & > button{
      font-size: 30px;
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      animation: none;
    }

    & > .overlay-bottom{
      position: absolute;
      top: 100%;
      left: 0;
      width: 100%;
      height: 60%;
      background: linear-gradient(to bottom, white, transparent);
    }
  }

  & > .map-container{
    width: 100%;
    flex: 1;
    height: 400px;
    min-height: 400px;
    max-height: 70dvh;
    display: flex;
    position: relative;
  }

  #leaflet_map {
    // flex: 1;
    // align-self: unsafe;
    // height: 100%;
    // // min-height: 450px;
    // outline: none;
    z-index: 1;
    font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system, Tahoma,
      "Segoe UI";
    position: absolute;
  }
  .leaflet-routing-container{
    display: none;
  }

  & > .direction-info-container{
    position: relative;
    border-radius: 50px 50px 0 0;
    
    & > .direction-info{
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      padding: 15px 0;
      width: 100%;
      border-radius: 50px 50px 0 0;
      position: relative;
      background: white;
      font-size: 17px;
      

      & > .label{
        color: #828282;
        font-weight: 500;
        font-style: italic;
      }
  
      & > .direction-detail{
        display: flex;
        justify-content: space-evenly;
        width: 100%;
        margin-top: 10px;
  
        & > .detail-item{
          display: flex;
          gap: 15px;
          align-items: center;
          justify-content: center;
  
          & > svg {
            color: var(--primary-color-2);
            font-size: 25px;
          }
  
          & > .content{
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
  
            & > span {
              font-weight: bold;
            }
  
            & > span + span {
              font-weight: 500;
              color: #828282;
            }
          }
        }
      }
      & > .direction-by-option{
        display: flex;
        justify-content: center;
        
        font-size: 25px;
        margin-top: 10px;

        & > button{
          color: #020202;
          border: thin solid transparent;
          width: 45px;
          height: 45px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 15px;
        }

        & > button.active{
          color: var(--primary-color-2);
          border-color: var(--primary-color-2);
        }
      }
      
      & > .note{
        font-size: 12px;
        margin-top: 5px;
        font-style: italic;
        color: #828282;
      }
    }

    & > .go-to-direction{
      position: absolute;
      top: -50px;
      z-index: 1;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      background: linear-gradient(to right, var(--primary-color-2), #f86f64);
      color: white;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      cursor: pointer;
      user-select: none;

      & > div{
        height: 50px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 5px;
        justify-content: center;
        text-transform: uppercase;
        
        & > svg{
          font-size: 25px;
        }
      }

      &:disabled{
        opacity: 1;
        filter: brightness(1.4);
        animation: none;
        cursor: default;
      }
    }
  }

  .leaflet-bottom{
    bottom: 50px;
  }

  .current-location-leaflet{
    bottom: 175px;
    right: 10px;
  }
}

.user-location{
  width: 100%;
  height: 100%;
  color: var(--primary-color-2);
}

.shop-logo {
  width: 40px !important;
  height: 40px !important;
  aspect-ratio: 1/1;
  // height: 100% !important;
  object-fit: cover;
  border-radius: 2em;
  // outline: 2px solid var(--primary-color-1);
  box-shadow: 0 0 0 2px var(--primary-color-1);
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;

  & > .logo-origin-container {
    transform: scale(calc(40 / var(--scale-origin)));
  }

  & > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}