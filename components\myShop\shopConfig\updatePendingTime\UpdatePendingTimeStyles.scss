.update-pending-time-container {
  max-height: 95dvh;
  height: fit-content;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  overflow: hidden;
  background-color: white;
  position: relative;

  & h3{
    text-transform: none;
  }

  &>.update-pending-time-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-bottom: 10px;
    padding-bottom: 10px;
    overflow: auto;

    &>.add-delivery-promotion-content {
      display: flex;
      flex-direction: column;
      padding: 10px;
      gap: 10px;
    }

    & .form-field-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
      padding: 10px;

      &>.input-group-container {
        flex: 1;
        align-items: center;

        & .custom-input{
          flex: 1;
          border-radius: 5px 0 0 5px;
          border-right: none;
          text-align: center;
        }

        & .time-unit-select{
          flex: 1;
          background: #f5f6fa;
          margin-top: 0 !important;
          border-radius: 0 5px 5px 0;

          & .v-field__input{
            justify-content: center;
          }
          & .v-input__control{
            padding: 5px;

            & .v-field__field{
              align-items: center;

              & input{
                align-self: center;
                margin: auto;
                text-align: center;
              }
            }
          }
        }
      }

    }

    & .custom-input,
    .text-area-custom {
      background: white;
      border-radius: 7px;
      margin: 5px 0;
      padding: 10px;
      font-size: 15px;
      font-weight: 600;
      border: 1px solid rgb(231, 233, 236);
      outline: none;
      flex: 1;

      &:disabled {
        background: #f5f6fa;
      }

    }
  }

  &>.action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;
    user-select: none;

    &>button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }

    &>.cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }

    &>.save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    &>.cancel-button:disabled {
      // color: var(--color-text-note);
      // border-color: var(--color-text-note);
      opacity: 0.5;
    }

    &>.save-button:disabled {
      // background: #ccc;
      // color: var(--primary-color-1);
      // border-color: #ccc;
      opacity: 0.5;
    }
  }
}