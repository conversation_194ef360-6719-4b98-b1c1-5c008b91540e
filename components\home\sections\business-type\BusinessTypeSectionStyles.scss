.home-v2-container .content-list.business-type-list {
  min-height: auto;
  // background: transparent;
  

  &>.stack-content-business-type {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
    

    & > .business-type-carousel{
      height: auto;
      margin: auto;
      max-width: max-content;

      & > .swiper-wrapper{
        height: auto;
      }

      & .business-type-slide{
        height: auto;
        width: 150px;
        border: none;
        border: thin solid transparent !important;

        & > .business-type-item{
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          gap: 3px;
          color: #545454;
          text-align: center;
          padding: 5px 0;
          

          & > .business-type-logo{
            width: 100px;
            height: 75px;
            padding: 5px;
            border-radius: 50%;

            & img{
              width: 100%;
              height: 100%;
              min-height: unset;
              border-radius: 10px;
              object-fit: cover;
            }
            & svg{
              width: 100%;
              height: 100%;
              color: #898989;
            }
          }
        }
      }
    }
  }
}