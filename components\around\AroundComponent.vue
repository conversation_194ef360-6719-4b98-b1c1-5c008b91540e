<template>
    <div class="public-container" v-on:scroll="(e: any) => { listScroll(e); }">
        <div class="around-container">
            <div class="around-content-container" :class="{ 'show-search': searchFocus }">
                <div class="location-and-search scale-down">
                    <div class="background-image"></div>
                    <div class="h-stack location-text">
                        <!-- <span>{{ $t('AroundComponent.giao_hang_den') }}</span>
    <div class="address">
        <span class="location-value" :title="address.length ? address : $t('AroundComponent.khong_co_dia_chi')">
            {{ address.length ? address : $t('AroundComponent.khong_co_dia_chi') }}
        </span>
    </div> -->
                        <img loading="lazy" :src="logo" :placeholder="logo" alt="logo" />
                    </div>
                    <div class="h-stack search-bar-container" :class="{ 'search-focus': searchFocus }">
                        <button v-show="searchFocus || (filterData.search && filterData.search.length)" v-on:click="() => {
                            if (searchFocus) {
                                searchFocus = false;
                                filterData = {
                                    ...filterData,
                                    search: filterData.search_text,
                                };
                            }
                            else if (filterData.search && filterData.search.length) {
                                filterData.search = '';
                                filter();
                            }

                            // if (!filterData.search && !filterData.search.length) {
                            // 	filter();
                            // }

                        }">
                            <Icon name="bi:chevron-left" size="25"></Icon>
                        </button>
                        <div class="h-stack search-container">
                            <button class="search-button" :disabled="refreshing || searchProductLoading" v-on:click="() => {
                                searchFocus = false;
                                filter();
                                blurSearch();
                            }">
                                <Icon name="iconoir:search" size="25" v-show="!(refreshing || searchProductLoading)" />
                                <Icon name="eos-icons:loading" size="25" v-show="refreshing || searchProductLoading" />
                            </button>

                            <input type="search" name="search-text" :placeholder="$t('AroundComponent.tim_kiem')"
                                :maxlength="appConst.max_text_short"
                                class="search-input-container" v-on:click="() => {
                                    searchFocus = true;
                                }" :value="filterData.search" v-on:input="(e: any) => {
        filterData = {
            ...filterData,
            search: e.target.value
        };
        getSearchProductResult();
    }" autoComplete="off" v-on:keypress.enter="() => {
        searchFocus = false;
        filterData = {
            ...filterData,
            search_text: filterData.search,
        };
        filter();
        blurSearch();
    }" id="search_input" x-webkit-speech />
                            <button class="clear-button" v-show="filterData.search && filterData.search.length"
                                v-on:click="() => {
                                    filterData = {
                                        ...filterData,
                                        search: ''
                                    }
                                    searchProductSuggest = [];
                                    focusSearch()
                                    // filter()
                                }">
                                <Icon name="iconamoon:sign-times" size="25" />
                            </button>
                            <!-- <button class="voice" v-on:click="()=>{
            recording()
        }">
            <Icon name="carbon:microphone-filled"></Icon>
        </button> -->
                        </div>
                        <button
                            :title="isShowList ? $t('AroundComponent.hien_thi_ban_do') : $t('AroundComponent.hien_thi_danh_sach')"
                            class="cart-in-search" v-show="!searchFocus" v-on:click="() => {
                                showList();
                            }">
                            <Icon name="iconoir:map" size="25" v-if="isShowList"
                                :title="$t('AroundComponent.hien_thi_ban_do')" />
                            <Icon name="material-symbols:lists" size="25" v-if="!isShowList"
                                :title="$t('AroundComponent.hien_thi_danh_sach')">
                            </Icon>
                        </button>
                    </div>
                </div>

                <div class="filter-result-container" :class="{ 'show-map': !isShowList }" id="map_container">
                    <client-only>
                        <LMap id="leaflet_map" :draggable="!showSelectedShop" v-on:ready="(e: any) => {
                            leafletMap = e;
                            leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                            initLeafletMap();
                        }" v-on:update:bounds="(bounds: any) => {
        mapBoundChanges(
            bounds.getSouth(),
            bounds.getWest(),
            bounds.getNorth(),
            bounds.getEast()
        );
    }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }" :world-copy-jump="true"
                            :use-global-leaflet="true">
                            <LControlZoom position="bottomright"></LControlZoom>
                            <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                :use-global-leaflet="true" :max-zoom="appConst.leafletMapTileOption.maxZoom"
                                :min-zoom="5" layer-type="base" :name="$t('AroundComponent.google_map')" />

                            <span class="current-location-leaflet" :title="$t('AroundComponent.vi_tri_cua_ban')"
                                v-on:click="(e: any) => {
                                    gotoCurrentLocationLeaflet();
                                }">
                                <Icon name="line-md:my-location-loop" class="my-location-icon" />
                            </span>
                            <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
                                data-placement="right" :title="$t('AroundComponent.nhan_de_chuyen_loai_map')"
                                v-bind:style="{
                                    backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                                }" v-on:click="(event: any) => {
        if (event.isTrusted) {
            if (leafletMapTileUrl == appConst.leafletMapTileUrl.roadmap) {
                leafletMapTileUrl = appConst.leafletMapTileUrl.hyprid;
                mapTypeTitle = $t('AroundComponent.ve_tinh');
                mapType = 'hyprid';
                buttonMapTileBackgound = map_sateline;
            } else if (
                leafletMapTileUrl == appConst.leafletMapTileUrl.hyprid
            ) {
                leafletMapTileUrl = appConst.leafletMapTileUrl.streetmap;
                mapTypeTitle = $t('AroundComponent.co_dien');
                mapType = 'hyprid';
                buttonMapTileBackgound = map_streetmap;
            } else if (
                leafletMapTileUrl == appConst.leafletMapTileUrl.streetmap
            ) {
                leafletMapTileUrl = appConst.leafletMapTileUrl.roadmap;
                mapTypeTitle = $t('AroundComponent.ve_tinh_va_nhan');
                mapType = 'roadmap';
                buttonMapTileBackgound = map_sateline;
            }
            // setAreaBoundLeaflet(currentLocaleObject);
        } else event.preventDefault();
    }">
                                <span>{{ mapTypeTitle }}</span>
                            </div>
                        </LMap>

                        <v-overlay v-model="showSelectedShop" :z-index="2" location="bottom" :width="'100%'"
                            content-class='shop-selected-container' persistent contained v-on:click:outside="() => {
                                showSelectedShop = false;
                            }">
                            <div class="shop-selected-content">
                                <div class="close" v-on:click="() => {
                                    showSelectedShop = false;
                                }">
                                    <Icon name="clarity:times-line" size="25"></Icon>
                                </div>
                                <!-- <nuxt-link :title="selectedShop.name"
            :to="appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id)"
            class="shop-seleted-logo">
            <img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt="shop logo"
                title="lỗi ảnh" v-if="!(selectedShop.logo || selectedShop.banner)" />
            <div class="logo-origin-container"
                :class="{ 'none-style': !(selectedShop.logo?.style?.length || selectedShop.banner?.style?.length) }"
                v-else>
                <img :src="selectedShop.logo ? (domainImage + selectedShop.logo.path) :
                    selectedShop.banner ? (domainImage + selectedShop.banner.path) : shop_logo"
                    :placeholder="shop_logo" v-on:click="() => {
                        router.push(appRoute.DetailShopComponent + '/' + selectedShop.slug)
                    }" :style="{
                    transform: (selectedShop.logo && selectedShop.logo.style) ? selectedShop.logo.style
                        : (selectedShop.banner && selectedShop.banner.style) ? selectedShop.banner.style
                            : 'none'
                }" alt="shop logo" />
            </div>
        </nuxt-link> -->
                                <AvatarComponent class="shop-seleted-logo" :imgTitle="selectedShop.name"
                                    :imgStyle="selectedShop.logo?.style" :imgSrc="selectedShop.logo
                                        ? (domainImage + selectedShop.logo.path)
                                        : selectedShop.banner
                                            ? (domainImage + selectedShop.banner.path)
                                            : ''
                                        " :width="100" :height="100" v-on:img_click="() => {
            router.push(appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id))
        }" />

                                <nuxt-link :title="selectedShop.name"
                                    :to="appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id)"
                                    class="name">
                                    <span>
                                        {{ selectedShop.name ? selectedShop.name : "" }}
                                    </span>
                                </nuxt-link>
                                <nuxt-link :title="selectedShop.address"
                                    :to="appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id)"
                                    class="text-overflow address" v-if="selectedShop.address">
                                    <span>
                                        {{
                                            selectedShop.address
                                                ? selectedShop.address
                                        : $t('AroundComponent.chua_cap_dia_chi')
                                        }}
                                    </span>
                                </nuxt-link>
                                <div class="text-overflow distance">
                                    <strong>
                                        <span>
                                            {{ parseFloat(selectedShop.distance) > 1000 ?
                                                (parseFloat(selectedShop.distance) / 1000).toFixed(1)
                                                :
                                                parseFloat(selectedShop.distance).toFixed(0) }}
                                            <em>{{ parseFloat(selectedShop.distance) > 1000 ? ' km' : ' m' }}</em>
                                        </span>
                                    </strong>
                                </div>
                                <div class="products" v-if="selectedShop.products && selectedShop.products.length">
                                    <Swiper class="my-carousel stack-carousel suggest-carousel"
                                        :modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :freeMode="true"
                                        :slides-per-view="2" :loop="true" :effect="'creative'" :navigation="true"
                                        :spaceBetween="10" :autoplay="false" key="suggest-carousel">
                                        <SwiperSlide class="item-stack-slide" v-for="item of selectedShop.products"
                                            :key="'suggest_' + item.id">
                                            <nuxt-link
                                                :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                                class="item-stack">
                                                <span class="distance">
                                                    {{ parseFloat(selectedShop.distance) > 1000 ?
                                                        (parseFloat(selectedShop.distance) / 1000).toFixed(1)
                                                        :
                                                    parseFloat(selectedShop.distance).toFixed(0) }}
                                                    <em>{{ parseFloat(selectedShop.distance) > 1000 ? ' km' : ' m' }}</em>
                                                </span>
                                                <img loading="lazy"
                                                    :src="item?.profile_picture?.length ? (domainImage + item?.profile_picture) : icon_for_product"
                                                    :placeholder="icon_for_product"
                                                    :alt="showTranslateProductName(item)"
                                                    :title="showTranslateProductName(item)" />
                                                <div class="item-stack-content">
                                                    <nuxt-link
                                                        :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                                        :title="showTranslateProductName(item)">
                                                        <span class="name">{{ showTranslateProductName(item) }}</span>
                                                    </nuxt-link>
                                                    <div class="h-stack price-container">
                                                        <span class="price">
                                                            {{
                                                                (item.price_off != null && item.price_off < item.price) ?
                                                                    formatCurrency(parseFloat(item.price_off), item.shop ?
                                                                        item.shop.currency : item.currency) :
                                                                    (parseFloat(item.price) == 0 || item.price ==null) ?
                                                                $t('AroundComponent.gia_lien_he') :
                                                                formatCurrency(parseFloat(item.price), item.shop ?
                                                                item.shop.currency : selectedShop.currency) }} </span>
                                                                <span class="origin-price"
                                                                    v-if="(item.price_off != null && item.price_off < item.price)">
                                                                    {{
                                                                        (parseFloat(item.price) == 0 ||
                                                                            parseFloat(item.price)
                                                                            == null)
                                                                    ? $t('AroundComponent.gia_lien_he')
                                                                    : formatCurrency(parseFloat(item.price), item.shop ?
                                                                    item.shop.currency : selectedShop.currency)
                                                                    }}
                                                                </span>
                                                    </div>
                                                </div>
                                            </nuxt-link>
                                        </SwiperSlide>
                                    </Swiper>
                                </div>
                            </div>
                        </v-overlay>

                    </client-only>
                    <div class="search-result" id="search-result" :class="{ 'show': searchFocus }" v-on:scroll="(e: any) => {
                        listSuggestScroll(e);
                    }">
                        <div class="history-search" v-if="searchTextSuggest && searchTextSuggest.length">
                            <span>{{ $t('AroundComponent.goi_y') }}:</span>

                            <div class="list-history-search">
                                <!-- <button v-for="(itemSearch, indexTab) in searchHistory" v-on:click="() => {
                filterData = {
                    ...filterData,
                    search: itemSearch
                };
                getSearchProductResult();
            }">
                {{ itemSearch }}
            </button> -->
                                <button v-for="(itemSearch, indexTab) in searchTextSuggest" v-on:click="() => {
                                    filterData = {
                                        ...filterData,
                                        search: itemSearch,
                                        search_text: itemSearch
                                    };
                                    filter();
                                    blurSearch();
                                    // getSearchProductResult();
                                }">
                                    {{ itemSearch }}
                                </button>
                            </div>
                        </div>
                        <div class="categories-and-list-suggest">
                            <div class="categories" v-if="false">
                                <button class="item-category" v-for="itemCategory in dataCategory"
                                    :class="{ 'active': checkCategoryFilter(itemCategory.id) }" v-on:click="async ($event: any) => {
                                        setCategoryFilter(itemCategory.id);
                                        // filter();
                                    }">
                                    <img loading="lazy" :src="domainImage + itemCategory.profile_picture" alt=""
                                        v-if="itemCategory.profile_picture" />
                                    <Icon name="carbon:data-categorical" v-else></Icon>
                                    <span>{{ itemCategory.name }}</span>
                                </button>
                            </div>
                            <div class="list-search-suggest-container">
                                <div class="list-search-suggest">
                                    <span>{{ $t('AroundComponent.xem_gan_day') }}:</span>
                                    <div class="search-placeholder" v-if="(!productRecent || !productRecent.length)">
                                        <img loading="lazy" :src="none_result" :placeholder="none_result" />
                                    </div>
                                    <nuxt-link
                                        :to="appRoute.ProductComponent + '/' + (itemRecent.slug?.length ? itemRecent.slug : itemRecent.id)"
                                        v-if="productRecent && productRecent.length" v-for="itemRecent of productRecent"
                                        class="search-result-item-container"
                                        :title="showTranslateProductName(itemRecent)">
                                        <img loading="lazy"
                                            :src="itemRecent?.profile_picture?.length ? (domainImage + itemRecent?.profile_picture) : icon_for_product"
                                            :placeholder="icon_for_product"
                                            :alt="showTranslateProductName(itemRecent)" />

                                        <div class="items-suggest-content">
                                            <span class="name">{{ showTranslateProductName(itemRecent) }}</span>
                                            <span class="shop-name" v-show="itemRecent.shop">{{ itemRecent.shop?.name
                                                }}</span>
                                            <span class="price">
                                                {{
                                                    (itemRecent.price_off != null && itemRecent.price_off <
                                                        itemRecent.price) ? formatCurrency(parseFloat(itemRecent.price_off),
                                                            itemRecent.shop ? itemRecent.shop.currency : itemRecent.currency) :
                                                        (itemRecent.price == 0 || itemRecent.price == null) ?
                                                            $t('AroundComponent.gia_lien_he') :
                                                    formatCurrency(parseFloat(itemRecent.price), itemRecent.shop ?
                                                    itemRecent.shop.currency : itemRecent.currency) }} </span>
                                                    <span class="origin-price"
                                                        v-if="(itemRecent.price_off != null && itemRecent.price_off < itemRecent.price)">
                                                        {{
                                                            (itemRecent.price == 0 || itemRecent.price == null)
                                                                ? $t('AroundComponent.gia_lien_he')
                                                                : formatCurrency(parseFloat(itemRecent.price), itemRecent.shop ?
                                                        itemRecent.shop.currency : itemRecent.currency)
                                                        }}
                                                    </span>
                                                    <button class="add-to-cart">
                                                        {{ $t('AroundComponent.them_vao_gio') }}
                                                    </button>
                                        </div>
                                    </nuxt-link>

                                    <div class="loading-more" v-if="searchProductLoadingMore == true">
                                        {{ $t('AroundComponent.loading') }}
                                    </div>
                                    <div id="last_of_list_suggest"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="list-result" :class="{ 'show': isShowList }" id="list-result">
                        <div class="empty-search"
                            v-if="!(isFirstLoad || refreshing || (filterResultList && filterResultList.length))">
                            <img loading="lazy" class="empty-image" :src="none_result_1" :placeholder="none_result_1" />
                            <p class="empty-text">{{ $t('AroundComponent.khong_tim_thay') }}</p>
                            <span class="go-map-and-zoom-out" v-on:click="() => {
                                showMap()
                            }">{{ $t('AroundComponent.chuyen_qua_ban_do') }}</span>
                        </div>
                        <div v-if="filterResultList && filterResultList.length" v-for="itemResult of filterResultList"
                            :key="itemResult.id" class="v-stack result-item-container" :title="itemResult.name">
                            <button class="h-stack shop-content">
                                <nuxt-link class="h-stack" :to="appRoute.DetailShopComponent + '/' + itemResult.slug">
                                    <AvatarComponent :imgTitle="itemResult.name" :imgStyle="itemResult.logo?.style"
                                        :imgSrc="itemResult.logo
                                            ? (domainImage + itemResult.logo.path)
                                            : itemResult.banner
                                                ? (domainImage + itemResult.banner.path)
                                                : ''
                                            " :width="100" :height="100" v-on:img_click="() => {
            router.push(appRoute.DetailShopComponent + '/' + itemResult.slug)
        }" />

                                    <div class="shop-info">
                                        <p class="shop-name">{{ itemResult.name }}</p>
                                        <span class="shop-address">
                                            {{
                                                itemResult.address ? itemResult.address :
                                            $t('AroundComponent.chua_cap_dia_chi')
                                            }}
                                        </span>
                                        <span class="shop-distance">
                                            {{ $t('AroundComponent.khoang_cach') }}: {{ parseFloat(itemResult.distance) > 1000 ?
                                                (parseFloat(itemResult.distance) / 1000).toFixed(1) :
                                            (parseFloat(itemResult.distance)).toFixed(0) }}
                                            <em>{{ parseFloat(itemResult.distance) > 1000 ? ' km' : ' m' }}</em>

                                            <nuxt-link>
                                                <button>{{ $t('AroundComponent.xem_shop') }}</button>
                                            </nuxt-link>
                                        </span>
                                    </div>
                                </nuxt-link>
                            </button>
                            <div class="v-stack shop-products-container"
                                v-if="itemResult.products && itemResult.products.length"
                                v-for="itemProduct of itemResult.products.slice(0, 2)">
                                <nuxt-link
                                    :to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
                                    :title="showTranslateProductName(itemProduct)">
                                    <div class="h-stack product-detail" :id="itemProduct.id">
                                        <nuxt-link
                                            :to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
                                            :title="showTranslateProductName(itemProduct)">
                                            <img loading="lazy" :src="itemProduct && itemProduct.profile_picture
                                                ? domainImage + itemProduct.profile_picture
                                                : icon_for_product
                                                " :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)"
                                                :title="showTranslateProductName(itemProduct)" />
                                            <div class="v-stack product-info">
                                                <p class="product-name">{{ showTranslateProductName(itemProduct) }}</p>
                                                <div class="product-price-add-to-cart">
                                                    <div class="v-stack">
                                                        <p class="product-price">
                                                            {{
                                                                (itemProduct.price_off != null && itemProduct.price_off <
                                                                    itemProduct.price) ?
                                                                    formatCurrency(parseFloat(itemProduct.price_off),
                                                                        itemProduct.shop ? itemProduct.shop.currency :
                                                                            itemResult.currency) : (parseFloat(itemProduct.price) == 0
                                                                || itemProduct.price==null) ?
                                                                $t('AroundComponent.gia_lien_he') :
                                                                formatCurrency(parseFloat(itemProduct.price),
                                                                itemProduct.shop ? itemProduct.shop.currency :
                                                                itemResult.currency) }} </p>
                                                                <p class="product-origin-price"
                                                                    v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
                                                                    {{ (parseFloat(itemProduct.price) == 0 ||
                                                                        itemProduct.price == null)
                                                                        ? $t('AroundComponent.gia_lien_he')
                                                                    : formatCurrency(parseFloat(itemProduct.price),
                                                                    itemProduct.shop ? itemProduct.shop.currency :
                                                                    itemResult.currency) }}
                                                                </p>
                                                    </div>
                                                </div>
                                                <button class="add-to-cart" v-on:click="() => {
                                                    selectedProduct = JSON.parse(JSON.stringify(itemProduct));
                                                    selectedProduct.shop = JSON.parse(JSON.stringify(itemResult));
                                                    // await checkSelectedProductQuantity()
                                                    showSelectedProduct = true;
                                                }" v-on:click.stop="(e) => {
        e.preventDefault()
    }">
                                                    {{ $t('AroundComponent.them_vao_gio') }}
                                                </button>
                                            </div>
                                        </nuxt-link>
                                    </div>
                                </nuxt-link>
                            </div>
                        </div>
                        <div id="last_of_list"></div>
                        <div class="loading-more" v-if="loadingMore == true">
                            {{ $t('AroundComponent.loading') }}
                        </div>
                        <span class="showed-all"
                            v-if="filterResultList && filterResultList.length && (filterResultList.length >= filterResultListCount)">
                            {{ $t('AroundComponent.da_hien_thi_toan_bo') }}
                        </span>
                        <v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained
                            content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
                            <Icon name="eos-icons:loading"></Icon>
                        </v-overlay>
                    </div>

                </div>
            </div>

        </div>
        <AddProductToCartComponent v-if="showSelectedProduct" v-on:close="() => {
            showSelectedProduct = false
        }" :selectedProduct="selectedProduct">
        </AddProductToCartComponent>
        <div class="auto-click" id="auto-click" v-on:click="(e) => {
            // watchDeviceOrient()
        }"></div>
    </div>

</template>
<style lang="scss" src="./AroundStyles.scss"></style>
<script lang="ts" setup>
import logo from "~/assets/image_13_3_2024/logo.png"
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import shop_banner from "~/assets/image/remagan-banner-19_1.png";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import icon_for_product from "~/assets/image/icon-for-product.png";
import marker_location_icon from "~/assets/image/marker-location.png";
import blue_marker_location_icon from "~/assets/image/blue-dot-location.png";
import none_result from "~/assets/image/none-result-2.webp";
import none_result_1 from "~/assets/image/none-result.webp";

import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { appRoute } from '~/assets/appRoute';
import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName, showTranslateProductDescription } from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";
import { PlaceService } from "~/services/placeService/placeService";
import { toast } from "vue3-toastify";
import type { MarkerClusterGroup } from "leaflet";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "../confirm18Age/Confirm18AgeComponent.vue";
import type { CartDto } from "~/assets/appDTO";
import { documentId } from "firebase/firestore";
import AddProductToCartComponent from "../cart/addProductToCart/AddProductToCartComponent.vue";
import AvatarComponent from "../avatar/AvatarComponent.vue";
import { HttpStatusCode } from "axios";
// import L from "leaflet";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const device = useDevice();

useHead({
    title: t('AppRouteTitle.AroundComponent'),
    meta: [
        {
            name: "title",
            content: t('AppRouteTitle.AroundComponent'),
        },
    ],
});
useSeoMeta({
    title: t('AppRouteTitle.AroundComponent'),
    ogTitle: t('AppRouteTitle.AroundComponent'),
    description: t('AppRouteTitle.AroundComponent'),
    ogDescription: t('AppRouteTitle.AroundComponent'),
    ogImage: baseLogoUrl,
    ogImageHeight: 400,
    ogImageWidth: 720,
    ogUrl: "https://remagan.com",
    ogType: "website",
});

var dataCategory = ref(appDataStartup.listCategory) as any;

var address = ref("");
var isFirstLoad = ref(true);
var refreshing = ref(false);
var loadingMore = ref(false);
var loadMoreTimeout: any;
var selectedShop = ref(null as any);
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref(false);
var searchSuggestAll = ref(false);
var searchFocus = ref(route.query?.searchFocus == 'true' ? true : false);
var isShowList = ref(route.query?.isShowList == 'false' ? false : true)
var productRecent = ref([] as any[]);
var searchTextSuggest = ref([] as any[]);
var cartData = ref();
var markerLeaflets = ref([] as any);
var filterResultMap = ref([] as any[]);
var filterResultList = ref([] as any[]);
var filterResultListCount = ref(0);
var filterData = ref({
    search: "",
    search_text: "",
    latitude_user: 0,
    longitude_user: 0,
    latitude_s: "",
    latitude_b: "",
    longitude_s: "",
    longitude_b: "",
    limit: 20,
    offset: 0,
    category_ids: [] as any,
    sortBy: 1,
    filter_type: 1,
    map_data: true
});
var isSearchNew = ref(false);
var buttonMapTileBackgound = ref(map_sateline);
var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: MarkerClusterGroup;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('AroundComponent.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var latitude = ref();
var longitude = ref();

var searchProductTimeout: any;
var searchProductSuggest = ref();
var searchProductSuggestCount = ref();

var publicService = new PublicService();
var placeService = new PlaceService();

var showSelectedShop = ref(false);

var selectedProduct = ref(null as any);
var showSelectedProduct = ref(false);
var webInApp = ref(null as any);

var rotate = ref(0 as any);
onUnmounted(() => {
    // filterData.value = {
    //     ...filterData.value,
    //     search: filterData.value.search_text
    // }
    let storageState = {
        filterData: filterData.value,
        isShowList: isShowList.value,
        searchProductSuggest: searchProductSuggest.value,
        searchProductSuggestCount: searchProductSuggestCount.value,
        searchTextSuggest: searchTextSuggest.value,
        selectedShop: selectedShop.value,
        showSelectedShop: showSelectedShop.value,
        // filterResultMap: filterResultMap.value,
        // filterResultList: filterResultList.value
    }
    sessionStorage.setItem(appConst.storageKey.stateRestore.AroundComponent, JSON.stringify(storageState));
})
onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    productRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : []
    window.addEventListener('beforeunload', async () => {
        refresh()
    });
    nuxtApp.$listen('refresh_around', async () => {
        refresh()
    });
    let queryFilter = route.query?.filter ? route.query.filter : null;
    let queryIsShowList = route.query?.isShowList ? route.query?.isShowList : null;

    let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.AroundComponent) as string);
    if (queryFilter) {
        filterData.value = JSON.parse(route.query.filter as string);
        router.replace({
            query: {
                ...route.query,
                filter: JSON.stringify(filterData.value)
            }
        })
    }

    if (queryIsShowList) {
        isShowList.value = queryIsShowList == 'true' ? true : false;
        router.replace({
            query: {
                ...route.query,
                isShowList: JSON.stringify(isShowList.value)
            }
        })
    }

    if (!queryFilter && store) {
        filterData.value = store.filterData;
        router.replace({
            query: {
                ...route.query,
                filter: JSON.stringify(filterData.value),
            }
        })
        searchProductSuggest.value = store.searchProductSuggest;
        searchProductSuggestCount.value = store.searchProductSuggestCount;
        searchTextSuggest.value = store.searchTextSuggest;
        // filterResultMap.value = store.filterResultMap;
        // filterResultList.value = store.filterResultList;
    }
    if (store) {
        selectedShop.value = store.selectedShop;
        showSelectedShop.value = store.showSelectedShop;
    }

    // --------------------- sẽ mở lại sau -----------------------
    // if (isiOS()) {
    //     nuxtApp.$listen(appConst.event_key.device_rotated, (e: any) => {
    //         rotate.value = e;
    //         if (localeMarkerLeaflet && e !== null) {
    //             let icon = new nuxtApp.$L.Icon({
    //                 iconUrl: blue_marker_location_icon,
    //                 iconSize: appConst.markerCustom.rotatedIcon.size,
    //                 className: appConst.markerCustom.rotatedIcon.class,
    //             })
    //             localeMarkerLeaflet.setIcon(icon);
    //             localeMarkerLeaflet.setRotationAngle(rotate.value);
    //         }
    //     });
    // } else {
    //     ondeviceorientationabsolute = (e) => {
    //         console.log("device orientation: ", e);
    //         rotate.value = e.alpha;
    //         if (localeMarkerLeaflet && e.alpha !== null) {
    //             let icon = new nuxtApp.$L.Icon({
    //                 iconUrl: blue_marker_location_icon,
    //                 iconSize: appConst.markerCustom.rotatedIcon.size,
    //                 className: appConst.markerCustom.rotatedIcon.class,
    //             })
    //             localeMarkerLeaflet.setIcon(icon);
    //             localeMarkerLeaflet.setRotationAngle(-rotate.value);
    //         }
    //     }
    // }

    nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
        dataCategory.value = appDataStartup.listCategory;
    });
    nuxtApp.$listen(appConst.event_key.search_focus, () => {
        focusSearch()
    });

    nuxtApp.$listen(appConst.event_key.home_click, () => {
        isShowList.value = !isShowList.value;
        router.replace({
            query: {
                ...route.query,
                isShowList: JSON.stringify(isShowList.value)

            }
        })
    });
    let cartData$ = JSON.parse(
        localStorage.getItem(appConst.storageKey.cart) as string
    );
    cartData.value = cartData$;

    // let userLocation = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);
    // // if (nuxtApp.$messaging) {
    // // 	let token = await getToken(nuxtApp.$messaging);
    // // 	console.log(token);
    // // }
    // if (userLocation && userLocation.latitude_user && userLocation.longitude_user) {
    //     filterData.value = {
    //         ...filterData.value,
    //         latitude_user: userLocation.latitude_user,
    //         longitude_user: userLocation.longitude_user
    //     }
    // }
})


function refresh() {
    sessionStorage.removeItem(appConst.storageKey.stateRestore.AroundComponent);
}
function isiOS() {
    var Apple = device.isApple;
    var Mac = device.isMacOS;
    var iOS = device.isIos;
    return (Apple || Mac || iOS);
}
function filter(offset = 0, limit = 20) {
    refreshing.value = true;
    filterData.value.map_data = false;
    filterData.value.filter_type = 1;
    filterData.value.sortBy = 6;
    router.replace({
        query: {
            ...route.query,
            filter: JSON.stringify(filterData.value)
        }
    })
    publicService
        .filter(filterData.value)
        .then((res) => {
            if (res.status && res.status == HttpStatusCode.Ok) {
                filterResultMap.value = res.body.data.resultMap;
                filterResultList.value = res.body.data.resultList;
                filterResultListCount.value = res.body.data.count;
                refreshing.value = false;
                if (leafletMap) showProductsOnMap();
            }
        })
        .catch((err) => {
            console.error("lỗi list: ", err);
            refreshing.value = false;
        });
}
async function initLeafletMap() {
    markersCluster = new nuxtApp.$L.MarkerClusterGroup({
        maxClusterRadius: 30,
        disableClusteringAtZoom: 20,
        iconCreateFunction: (cluster) => createClusterElement(cluster),
    });
    leafletMap.addLayer(markersCluster)

    setCurrentLocationLeaflet();
}

function createClusterElement(cluster: any) {
    return nuxtApp.$L.divIcon({
        html: '<div class="my-cluster-icon red-shadow-icon">'
            // + '<span>' 
            + cluster.getChildCount()
            // + '</span>'
            + '</div>'
    });
}
async function mapBoundChanges(lat_s: any, lng_s: any, lat_b: any, lng_b: any) {
    let filterParams = JSON.parse(JSON.stringify(filterData.value));
    filterParams = {
        ...filterParams,
        latitude_s: lat_s,
        latitude_b: lat_b,
        longitude_s: lng_s,
        longitude_b: lng_b,
    };

    filterData.value = JSON.parse(JSON.stringify(filterParams));
    if (isFirstLoad.value == true) {
        if (!filterResultList.value || !filterResultList.value.length || !filterResultMap.value || !filterResultMap.value.length) {
            filter();
        }
        else {
            showProductsOnMap();
        }
    }
    else {
        filter();
    }
    isFirstLoad.value = false;
}
function resetMarkersLeaflet() {
    if (markerLeaflets.value.length) {
        // markerLeaflets.value.forEach((element: any, index: any) => {
        //     element.removeFrom(leafletMap);
        // });
        markerLeaflets.value = [];
        markersCluster.clearLayers();
    }

    // this.leafletMap.eachLayer(layer=>{
    //   if(layer["options"].className == "tooltip-leaflet-own"){
    //     layer.removeFrom(this.leafletMap);
    //   };
    // });
}
async function showProductsOnMap() {
    let listPropertyOnMap = JSON.parse(JSON.stringify(filterResultMap.value));

    await resetMarkersLeaflet();
    listPropertyOnMap.forEach(async (element: any, index: number) => {

        let markerMap = nuxtApp.$L.marker([element.latitude, element.longitude], {
            icon: nuxtApp.$L.divIcon({
                className: "img-icon",
                html: `
                <div class='shop-logo'>
                    ${element.logo
                        ? `<div class='logo-origin-container'>
                            <img 
                                src='${element.logo ? (domainImage + element.logo.path) : element.banner ? (domainImage + element.banner.path) : shop_logo}'
                                style='
                                    transform: ${element.logo?.style?.length ? element.logo.style : element.banner?.style?.length ? element.banner.style : 'scale(1) translate(0)'};
                                    ${element.logo?.style?.length ? '' : 'height: 100%'}
                                '
                            />
                        </div>`
                        : `<img
                            [hidden]=${!!element.logo}
                            src='${shop_logo}'
                            />`
                    } 
                    
                    
                </div>
                
                <div class='after'></div>
                `,
                iconUrl: (element.banner ? domainImage + element.banner : shop_logo),
                // iconAnchor: [7, 7],
                iconSize: [40, 50],
            }),
        });


        let markerMapZoom13 = nuxtApp.$L.marker([element.latitude, element.longitude], {
            icon: nuxtApp.$L.divIcon({
                className: "red-shadow-icon",
                iconUrl: (element.banner ? domainImage + element.banner : shop_banner),
                iconAnchor: [7, 7],
                iconSize: [15, 15],
            }),
        });

        if (leafletMap.getZoom() >= 13) {
            markerLeaflets.value.push(markerMap);
            if (leafletMap.getZoom() >= 15) {
                markerMap.bindTooltip(element.name, {
                    direction: 'bottom',
                    permanent: true,
                    interactive: true,
                    className: "tooltip-leaflet-own",
                });
            }
        }
        else {
            markerLeaflets.value.push(markerMapZoom13);
        }

        let iconHoverLeaflet = nuxtApp.$L.divIcon({
            className: "violet-shadow-icon",
            iconAnchor: [7, 7],
            iconSize: [14, 14],
        });




        markerLeaflets.value[index].addEventListener("mouseover", () => {
            if (leafletMap.getZoom() < 15) {
                markerLeaflets.value[index].bindTooltip(listPropertyOnMap[index].name, {
                    direction: 'bottom',
                    permanent: true,
                    interactive: true,
                    className: "tooltip-leaflet-own",
                });
            }
        });

        markerLeaflets.value[index].addEventListener("mouseout", () => {
            // markerLeaflets.value[index].setIcon(
            // nuxtApp.$L.divIcon({
            //   className: "red-shadow-icon",
            //   iconAnchor: [7, 7],
            //   iconSize: [14, 14],
            // })
            // );
            if (leafletMap.getZoom() < 15) {
                markerLeaflets.value[index].closeTooltip();
            }

        });

        markerLeaflets.value[index].addEventListener("click", async () => {
            // if (window.innerWidth > 480) {
            //     router.push(appRoute.DetailShopComponent + "/" + element.slug);
            // } else {

            // }

            selectedShop.value = listPropertyOnMap[index];
            showSelectedShop.value = true;
        });


        // leafletMap.addLayer(markerLeaflets.value[index]);
        // markersCluster.addLayer(markerLeaflets.value[index]);
    });
    markersCluster.addLayers(markerLeaflets.value);
    //   markerLeaflets.value.forEach((mark:any)=>{
    // 	mark.openPopup();
    //   })

}
function getCurrentLocationOfUser() {
    if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                filterData.value = {
                    ...filterData.value,
                    latitude_user: position.coords.latitude,
                    longitude_user: position.coords.longitude
                }
                // initDashboard()
            },
            (error) => {
                filterData.value = {
                    ...filterData.value,
                    latitude_user: appConst.defaultCoordinate.latitude,
                    longitude_user: appConst.defaultCoordinate.longitude
                }
                // initDashboard()
            }
        );
    }
}
async function setCurrentLocationLeaflet() {
    // await getCurrentLocationOfUser();
    if (
        filterData.value.latitude_s &&
        filterData.value.latitude_b &&
        filterData.value.longitude_s &&
        filterData.value.longitude_b
    ) {
        let bound = new nuxtApp.$L.LatLngBounds(
            [
                parseFloat(filterData.value.latitude_b),
                parseFloat(filterData.value.longitude_b),
            ],
            [
                parseFloat(filterData.value.latitude_s),
                parseFloat(filterData.value.longitude_s),
            ]
        );
        leafletMap.fitBounds(bound);
        setLocationLeafletMarker(filterData.value.latitude_user, filterData.value.longitude_user);
    }
    else if (filterData.value.latitude_user && filterData.value.longitude_user) {
        leafletMap.setView([filterData.value.latitude_user, filterData.value.longitude_user], 13);
        setLocationLeafletMarker(filterData.value.latitude_user, filterData.value.longitude_user);
    } else if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(
            async (position) => {
                filterData.value.latitude_user = position.coords.latitude;
                filterData.value.longitude_user = position.coords.longitude;

                latitude.value = position.coords.latitude;
                longitude.value = position.coords.longitude;
                if (
                    filterData.value.latitude_s &&
                    filterData.value.latitude_b &&
                    filterData.value.longitude_s &&
                    filterData.value.longitude_b
                ) {
                    let bound = new nuxtApp.$L.LatLngBounds(
                        [
                            parseFloat(filterData.value.latitude_b),
                            parseFloat(filterData.value.longitude_b),
                        ],
                        [
                            parseFloat(filterData.value.latitude_s),
                            parseFloat(filterData.value.longitude_s),
                        ]
                    );

                    leafletMap.fitBounds(bound);
                } else {
                    leafletMap.setView([filterData.value.latitude_user, filterData.value.longitude_user], 13);
                }

                await setLocationLeafletMarker(filterData.value.latitude_user, filterData.value.longitude_user);
            },
            async (error) => {
                filterData.value.latitude_user = appConst.defaultCoordinate.latitude;
                filterData.value.longitude_user = appConst.defaultCoordinate.longitude;
                latitude.value = appConst.defaultCoordinate.latitude;
                longitude.value = appConst.defaultCoordinate.longitude;

                if (
                    filterData.value.latitude_s &&
                    filterData.value.latitude_b &&
                    filterData.value.longitude_s &&
                    filterData.value.longitude_b
                ) {
                    let bound = new nuxtApp.$L.LatLngBounds(
                        [
                            parseFloat(filterData.value.latitude_b),
                            parseFloat(filterData.value.longitude_b),
                        ],
                        [
                            parseFloat(filterData.value.latitude_s),
                            parseFloat(filterData.value.longitude_s),
                        ]
                    );
                    leafletMap.fitBounds(bound);
                } else {
                    leafletMap.setView([latitude.value, longitude.value], 13);
                }
                await setLocationLeafletMarker(latitude.value, longitude.value);
            }
        );
    }
}

function getSearchProductResult() {
    searchProductLoading.value = true;
    clearTimeout(searchProductTimeout);
    // if (filterData.value.search && filterData.value.search.length) {
    searchProductTimeout = setTimeout(() => {
        publicService
            .searchSuggest(filterData.value.search, undefined, undefined, filterData.value.latitude_user, filterData.value.longitude_user)
            .then((res) => {

                if (res.status == HttpStatusCode.Ok) {
                    searchProductSuggest.value = res.body.data.products;
                    searchProductSuggestCount.value = res.body.data.count;
                    searchTextSuggest.value = res.body.data.strings;
                    searchProductLoading.value = false;
                    document.getElementById("search-result")?.scrollTo({ top: 0 });
                    searchSuggestAll.value = false;
                }
            })
            .catch(() => {
                searchProductLoading.value = false;
            });
    }, 1000);
    // } else {
    // 	searchProductLoading.value = false;
    // }
}

function loadMoreProductSearching() {
    if (
        searchProductSuggest.value.length < searchProductSuggestCount.value &&
        !searchSuggestAll.value && !searchProductLoadingMore.value
    ) {
        searchProductLoadingMore.value = true;
        clearTimeout(searchProductTimeout);
        searchProductTimeout = setTimeout(() => {
            publicService
                .searchSuggest(
                    filterData.value.search,
                    searchProductSuggest.value.length,
                    25,
                    filterData.value.latitude_user, filterData.value.longitude_user
                )
                .then((res) => {
                    if (res.status == HttpStatusCode.Ok) {
                        if (res.body.data && res.body.data.result.length > 0) {
                            searchProductSuggest.value = [
                                ...searchProductSuggest.value,
                                ...res.body.data.products,
                            ];
                        } else {
                            searchSuggestAll.value = true;
                        }
                        searchProductLoadingMore.value = false;
                    }
                })
                .catch(() => {
                    searchProductLoadingMore.value = false;
                });
        }, 1000);
    }
}

function setLocationLeafletMarker(lat: number, lng: number) {
    if (localeMarkerLeaflet) {
        localeMarkerLeaflet.remove();
    }

    localeMarkerLeaflet = nuxtApp.$L.marker([lat, lng], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: marker_location_icon,
            iconSize: appConst.markerCustom.defaultIcon.size,
            className: appConst.markerCustom.defaultIcon.class,
        }),
        rotationOrigin: 'center 75%'
    });
    localeMarkerLeaflet.addTo(leafletMap);
    latitude.value = lat;
    longitude.value = lng;
    filterData.value.latitude_user = lat;
    filterData.value.longitude_user = lng;
    getUserAddress();

    localeMarkerLeaflet.addEventListener("click", (event: L.LeafletEvent) => {
        //   isShowCoordinateLocation = true;
        //   document.getElementById("leaflet-detail-auto-click").click();
    });
}
async function gotoCurrentLocationLeaflet(event?: Event) {
    if (!event || event.isTrusted == true) {
        // if (webInApp.value) {
        //     await nuxtApp.$emit(appConst.event_key.send_request_to_app, {
        //         action: appConst.webToAppAction.requestUserLocation,
        //     })
        //     let userLocation = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);

        //     // if (nuxtApp.$messaging) {
        //     // 	let token = await getToken(nuxtApp.$messaging);
        //     // 	console.log(token);
        //     // }
        //     if (userLocation && userLocation.latitude_user && userLocation.longitude_user) {
        //         filterData.value = {
        //             ...filterData.value,
        //             latitude_user: userLocation.latitude_user,
        //             longitude_user: userLocation.longitude_user
        //         }
        //         leafletMap.flyTo(
        //             [userLocation.latitude_user, userLocation.longitude_user],
        //             17
        //         );
        //         setLocationLeafletMarker(userLocation.latitude_user, userLocation.longitude_user);
        //     }
        // }

        // else {
        if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    leafletMap.flyTo(
                        [position.coords.latitude, position.coords.longitude],
                        17
                    );
                    latitude.value = position.coords.latitude;
                    longitude.value = position.coords.longitude;
                    localeMarkerLeaflet.setLatLng([position.coords.latitude, position.coords.longitude]);

                    // setLocationLeafletMarker(position.coords.latitude, position.coords.longitude);
                },
                (error) => {
                    toast.warning(t('AroundComponent.chua_cung_cap_vi_tri'), {
                        autoClose: 1000,
                        hideProgressBar: true,
                    });
                    latitude.value = appConst.defaultCoordinate.latitude;
                    longitude.value = appConst.defaultCoordinate.longitude;
                    leafletMap.flyTo([latitude.value, longitude.value], 17);

                    // setLocationLeafletMarker(latitude.value, longitude.value);
                    setLocationLeafletMarker(latitude.value, longitude.value);
                }
            );
        }
        // }

    }
}

function getUserAddress() {
    setProvinceDistrictByLocation();
}
async function setProvinceDistrictByLocation() {
    placeService
        .myGeocoderByLatLngToAddress(latitude.value, longitude.value)
        .then((res: any) => {
            if (res.body.data && res.body.data.length) {
                address.value = res.body.data[0].address
                    ? res.body.data[0].address
                    : "";
                // address = res.body.body.data[0].address ? res.body.body.data[0].address : "";
                // province_id = res.body.body.data[0].province_id ? res.body.body.data[0].province_id : null;
                // district_id = res.body.body.data[0].district_id ? res.body.body.data[0].district_id : null;
                // ward_id = res.body.body.data[0].ward_id ? res.body.body.data[0].ward_id : null;
            }
        });
}
function checkCategoryFilter(id: any) {
    if (
        filterData.value.category_ids &&
        filterData.value.category_ids.indexOf(id) != -1
    )
        return true;
    return false;
}
function setCategoryFilter(id: any) {
    let arr: any = filterData.value.category_ids || [];
    let index = arr.indexOf(id);
    if (index == -1) {
        arr.push(id);
    } else {
        arr.splice(index, 1);
    }
    filterData.value = {
        ...filterData.value,
        category_ids: arr,
    };
}
function listScroll(event: any) {
    let el = document
        .getElementById("last_of_list")
        ?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 10) {
        loadMoreFilterResult()
    }
}
function loadMoreFilterResult() {
    if (filterResultList.value.length < filterResultMap.value.length) {
        loadingMore.value = true;
        clearTimeout(loadMoreTimeout)
        loadMoreTimeout = setTimeout(() => {

            let listOnMap = JSON.parse(JSON.stringify(filterResultMap.value));
            filterResultList.value = [
                ...filterResultList.value,
                ...filterResultMap.value.slice(filterResultList.value.length, filterResultList.value.length + 20)
            ]
            loadingMore.value = false;
        }, 1000);
    }

}
function listSuggestScroll(event: any) {
    let el = document
        .getElementById("last_of_list_suggest")
        ?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 10) {
        if (searchProductSuggest.value?.length < searchProductSuggestCount.value) {
            loadMoreProductSearching();
        }
    }
}
function focusSearch() {
    document.getElementById('search_input')?.focus();
    searchFocus.value = true;
}
function blurSearch() {
    document.getElementById('search_input')?.blur();
    searchFocus.value = false;
}

function showList() {
    nuxtApp.$emit(appConst.event_key.home_click)
}
function showMap() {
    isShowList.value = false;
    leafletMap.setZoom(leafletMap.getZoom() - 1);
}
function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
function getPercentSaleOff(product: any) {
    if (product.price_off && product.price) return -Math.round(((product.price - product.price_off) / product.price) * 100 || 0);
    return 0
}
</script>