<script setup lang="ts">

import { appConst, appDataStartup, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, showTranslateProductName, showTranslateProductDescription } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { increase_view_obj_type, InteractionObjectType, type CartDto } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';
import { ProductService } from '~/services/productService/productService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import non_avatar from '~/assets/image/non-avatar.jpg';
import { toast } from "vue3-toastify";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "../confirm18Age/Confirm18AgeComponent.vue";
import VueCountdown from '@chenfengyuan/vue-countdown';
import moment from "moment";
import { ShopService } from "~/services/shopService/shopService";
import { RatingService } from "~/services/ratingService/ratingService";
import environment from "~/assets/environment/environment";
import { HttpStatusCode } from "axios";
import { InteractionService } from "~/services/interactionService/interactionService";
import { PublicService } from "~/services/publicService/publicService";
import { channel_type, member_type, type ChannelDTO } from "../chatManage/ChatDTO";

const AddProductToCartComponent = defineAsyncComponent(() => import('~/components/cart/addProductToCart/AddProductToCartComponent.vue'));
const AddCommentComponent = defineAsyncComponent(() => import('~/components/addComment/AddCommentComponent.vue'));
const ImageViewerComponent = defineAsyncComponent(() => import('~/components/imageViewer/ImageViewerComponent.vue'));
const ChatDetailComponent = defineAsyncComponent(() => import('~/components/chatManage/chatDetail/ChatDetailComponent.vue'));


const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const dataId = route.params.slug;
const { t, locale } = useI18n();

var productDetails: any;
var productData = ref();
// const { data, refresh, execute, clear } = await useLazyFetch(appConst.apiURL.publicProductDetail + dataId, {
// 	key: `product_detail_${dataId}`,
// 	headers: {
// 		Origin: `https://${environment.domain}`
// 	},
// 	immediate: true,
// 	cache: 'reload',
// 	// getCachedData: (key) => {
// 	// 	// Check if the data is already cached in the Nuxt payload
// 	// 	if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
// 	// 		console.log("nuxt payload", nuxtApp.payload.data[key].body.data);

// 	// 		return nuxtApp.payload.data[key]

// 	// 	}

// 	// 	// Check if the data is already cached in the static data
// 	// 	if (nuxtApp.static.data[key]) {
// 	// 		console.log("static data", nuxtApp.payload.data[key]);
// 	// 		// return nuxtApp.static.data[key]
// 	// 	}

// 	// 	return null
// 	// }
// });
// let productGet: any = data.value;

// const { data } = useFetch(appConst.apiURL.publicProductDetail + dataId);

const { data, refresh, execute } = await useLazyFetch(appConst.apiURL.publicProductDetail + dataId, {
	key: `product_detail_${dataId}`,
	// ttl: 600,
	headers: {
		Origin: `https://${environment.domain}`
	},
	cache: 'default',
	getCachedData: (key) => { // Changed to async
		// Check if the data is already cached in the Nuxt payload
		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
			if (route.query?.flush == '1') {
				return null;
			} else {
				return nuxtApp.payload.data[key];
			}
		}
		// Check if the data is already cached in the static data
		if (nuxtApp.static.data[key]) {
			if (route.query?.flush == '1') {
				return null;
			}
			return nuxtApp.static.data[key];
		}
		return null;
	}
});
let productGet: any = data.value;

if (productGet?.status == HttpStatusCode.Ok) {
	productDetails = await JSON.parse(JSON.stringify(productGet?.body.data));
	if (productDetails?.enable) {
		productData.value = productDetails;
		let metaName = await showTranslateProductName(productData.value);
		let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
		useServerSeoMeta({
			title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
			ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
		})
	}
}

// const newData = await fetch(appConst.apiURL.publicProductDetail + dataId);
// if(newData){
// 	refresh();	
// }
var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var ratingService = new RatingService();
var interactionService = new InteractionService();
var publicService = new PublicService();

var userProfile = ref(null as any);
var refreshing = ref(true);

var productID = ref((route.params?.slug ? route.params.slug : null) as string);
var selectedChildProduct = ref(null as any);
var shopData = ref();
var quantityRef = ref(0);
var cartData = ref([] as any[]);
var loadMore = ref(false);
var isErrored = ref(false);
var isAuth = ref(false);
var isUpdating = ref(false);
// var showModalConfirmResetCart = ref(false);
// var showModalConfirm18Age = ref(false);
var isShowFullNote = ref(false);
var showFullButton = ref(false);
var showAddToCartModal = ref(false);
var showAddCommentModal = ref(false);
var showUpdateCommentModal = ref(false);

var loadMoreTimeOut: any;
var webInApp = ref(null as any);

var notesToCartRef = ref("");
var showNoteInCart = ref(false);
var similarProducts = ref([] as any);
var slidersPerViewSimilar = ref(2);

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);
// var expireTime = ref(moment('2024-05-24 9:00:00'));
// var currentTime = ref(moment());

var is_liked = ref(false);

var search_text = ref("");
var sectionCarousel = ref();
var sectionViewing = ref(null as any);
var listVideo = ref([] as any);

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();
var firstMessage = ref();
var listQuickChat = [
	"Mua nhiều có được giảm không?",
	"Hàng có sẵn không shop?",
	"Có giá sỉ không shop?",
	"Tư vấn giúp tôi",
	"Phí ship tính sao?",
];
var filterData = ref({
	search: "",
	search_text: "",
	latitude_user: 0,
	longitude_user: 0,
	latitude_s: "",
	latitude_b: "",
	longitude_s: "",
	longitude_b: "",
	limit: 20,
	offset: 0,
	category_ids: [] as any,
	sortBy: 1,
	filter_type: 1,
});

var myRating = ref<any>(null);
var viewTimer: any;
onActivated(async () => {

	// await getDetailProduct().then(async () => {
	// 	let cartDataLocal = await localStorage.getItem(appConst.storageKey.cart);
	// 	cartData.value = cartDataLocal ? JSON.parse(cartDataLocal as string) : []
	// 	let index = await cartData.value.findIndex((res: any) => {
	// 		return res.product_id == productData.value.id
	// 	});
	// 	quantity = index != -1 ? cartData.value[index].quantity : 0
	// });
})
onBeforeMount(async () => {

	// if (route.query?.flush == '1') {
	// 	console.log('refresh', dataId, route.path)

	// 	clearNuxtData(`product_detail_${dataId}`);
	// 	clearNuxtData(route.path)

	// }
})
onMounted(async () => {
	// if (route.query?.flush == '1') {
	// 	console.log('refresh', dataId)
	// 	refresh()
	// 	// clearNuxtData(`product_detail_${dataId}`)
	// }
	refreshing.value = true;
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartNumber();
	})
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	userProfile.value = await authService.checkAuth();

	if (productData.value) {
		let metaName = await showTranslateProductName(productData.value);
		let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
		useSeoMeta({
			title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
			ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
		})
		useServerSeoMeta({
			title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
			ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
		})
		if (productData.value.children_products.length) {
			productData.value.children_products.forEach((item: any) => {
				item.shop = JSON.parse(JSON.stringify(productData.value.shop));

			})

		}
		productData.value.images = productData.value?.images.sort((a: any, b: any) => a.index - b.index);
		refreshing.value = false;
		getCartNumber();
		checkNoteShowFullable();
		checkInteraction()
		getSimilarProducts();
		getProductRating();
		getListReview();
		getMyRatingToProduct();
		increaseView();
		// useServerCache({
		//     key: `product_detail_${productData.value.id}`,
		//     duration: 60 * 60, // Cache for 1 hour
		//     condition: () => !!productData.value.name // Cache only if productData.name is available
		// });

	}
	else {
		await getDetailProduct().then(async (res) => {
			refreshing.value = false;
			if (res) {
				if (productData.value.children_products.length) {
					productData.value.children_products.forEach((item: any) => {
						item.shop = JSON.parse(JSON.stringify(productData.value.shop))
					})
				}
				getCartNumber();
				checkNoteShowFullable();
				checkInteraction()
				getSimilarProducts();
				getProductRating();
				getListReview()
				getMyRatingToProduct();
				increaseView()
				productData.value.images = productData.value?.images.sort((a: any, b: any) => a.index - b.index);
			}

		});
	}
});

onUnmounted(async () => {
	// execute();
	// refresh();
	if (productData.value?.id) {
		let productRecent = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : [];
		let index = productRecent.findIndex((e: any) => {
			return e.id == productData.value?.id;
		})
		if (index != -1) {
			productRecent.splice(index, 1)
		}
		productRecent = [
			productData.value,
			...productRecent,
		].slice(0, 10);

		localStorage.setItem(appConst.storageKey.productRecent, JSON.stringify(productRecent));
		clearTimeout(viewTimer);
	}
});

async function getDetailProduct() {
	console.log("lấy thông tin")
	refreshing.value = true;
	return new Promise(async (resolve) => {
		await productService.clientDetailProduct(productID.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				let product$ = JSON.parse(JSON.stringify(res.body.data));
				// console.log(product$);
				if (product$.enable) {
					productData.value = product$;
					refreshing.value = false
					let metaName = await showTranslateProductName(productData.value);
					let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
					useServerSeoMeta({
						title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogUrl: () => domain + route.fullPath,
						ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
						ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
					});
					useSeoMeta({
						title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogUrl: () => domain + route.fullPath,
						ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
						ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
					});
					resolve(productData.value)
				}
				else {
					productData.value = null;
					refreshing.value = false
					isErrored.value = true;
					resolve(null)
				}
			}
			else {
				productData.value = null;
				refreshing.value = false
				isErrored.value = true;
				resolve(null)
			}
		}).catch(err => {
			productData.value = null;
			isErrored.value = true;
			refreshing.value = false
			resolve(null)
		})
	})
}
async function getCartNumber() {
	let cartDataLocal = await localStorage.getItem(appConst.storageKey.cart);

	cartData.value = cartDataLocal ? JSON.parse(cartDataLocal) : [];
	let index = await cartData.value.findIndex((res: any) => {
		return res.product_id == productData.value.id
	});
	quantityRef.value = index != -1 ? cartData.value[index].quantity : 0;
}

function increaseView(obj_id = productData.value.id ?? productID.value) {
	if (obj_id) {
		viewTimer = setTimeout(() => {
			const key = `view_${route.fullPath}`;
			if (interactionService.shouldIncreaseViewCount(key)) {
				console.log('tang luot view')
				// Call your API to increase the view count
				interactionService.increaseView(obj_id, increase_view_obj_type.product)
			}
			else {
				console.log('da tang luot view roi')
			}
		}, 3000);
	}

}

function getSimilarProducts() {
	return productService.relatedProducts(productData.value.id).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			similarProducts.value = JSON.parse(JSON.stringify(res.body.data.result));
		}
		else {
			similarProducts.value = [];
		}
	}).catch((err => {
		similarProducts.value = [];
	}));
}

function getPercentSaleOff(product: any) {
	if (product.price_off && product.price) return Math.ceil(((product.price - product.price_off) / product.price) * 100 || 0);
	return 0
}

function checkNoteShowFullable() {
	setTimeout(() => {
		let heighContent = document?.getElementById("product_note_content")?.getBoundingClientRect()?.height || 0;
		let heightParent = document?.getElementById("product_note_container")?.getBoundingClientRect()?.height || 0;
		if (heighContent > 100 && heighContent >= heightParent - 25) {
			showFullButton.value = true;
		}
		else showFullButton.value = false;
	}, 1000);

}

function getSlideHeight(id: any) {
	return document.getElementById(id)?.getBoundingClientRect().width;
}

async function getProductRating() {
	await ratingService.calcObjectRating(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.ratings = res.body.data
		}

	})
	await ratingService.listRatingByObject(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.comment_count = res.body.data.count;
			productData.value.comments = res.body.data.result.filter((e: any) => { return e.users?.id != userProfile.value?.id });
		}

	})
}

async function getMyRatingToProduct() {
	await ratingService.myRatingToObjectId(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			myRating.value = res.body
		}

	})
}
async function loadmoreRating() {
	await ratingService.listRatingByObject(productData.value.id, productData.value.comments.length, 20).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.comments = [...productData.value.comments, ...res.body.data.result.filter((e: any) => { return e.users?.id != userProfile.value?.id })];
		}

	})
}

async function scrollToSection(section_id: any) {
	let categoryEl = document.getElementById(section_id) as Element;
	var block: any = 'center';

	if (categoryEl?.getBoundingClientRect().height > window.innerHeight) {
		block = 'start';
	}
	await categoryEl?.scrollIntoView({
		behavior: 'smooth',
		block: block,
		inline: 'start',
		// inline: 'center'
	});
}
function checkSectionViewing(id: any) {
	if (id) {
		return sectionViewing.value == id ? true : false
	}
	return false
}
function likeProduct() {
	if (userProfile.value?.id) {
		interactionService.createOrDelete({
			interaction_type: InteractionObjectType.like,
			object_id_from: userProfile.value.id,
			object_type_from: appConst.object_type.user as any,
			object_id_to: productData.value?.id,
			object_type_to: appConst.object_type.product as any
		}).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				checkInteraction();
			}
		})
	}
	else {
		nuxtApp.$emit(appConst.event_key.require_login, {
			redirect_url: route.fullPath,
			back_on_close: false
		})
	}

}
async function checkInteraction() {
	interactionService.checkInterctions(productData.value?.id, InteractionObjectType.like).then(res => is_liked.value = res.body.data);
}

function getListReview() {
	publicService.listReviewVideo(productData.value.id).then((res: any) => {
		if (res.status == HttpStatusCode.Ok) {
			listVideo.value = res.body.data ? JSON.parse(JSON.stringify(res.body.data)) : []
		}
	});
}

function showView(amount: number) {
	if (amount > Math.pow(1000, 3)) return `${(amount / Math.pow(1000, 3)).toFixed(amount % Math.pow(1000, 3) != 0 ? 1 : 0)} B`;
	if (amount > Math.pow(1000, 2)) return `${(amount / Math.pow(1000, 2)).toFixed(amount % Math.pow(1000, 2) != 0 ? 1 : 0)} M`;
	if (amount > Math.pow(1000, 1)) return `${(amount / Math.pow(1000, 1)).toFixed(amount % Math.pow(1000, 1) != 0 ? 1 : 0)} K`;
	return amount
}

function share() {
	let titleShop = productData.value.shop && productData.value.shop.id
		? productData.value.shop.name + " - "
		: "";
	let messageShare = {
		name: titleShop + showTranslateProductName(productData.value) + "\n",
		link: domain + appRoute.ProductComponent + "/" + (productData.value.slug?.length ? productData.value.slug : productData.value.id),
		image: productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
	}
	nuxtApp.$emit(appConst.event_key.share, messageShare);
}

function containerScrolling() {
	let sectionsElements = document.querySelectorAll('.tab-section');
	sectionsElements.forEach((section, index) => {

		if (isElementInCenterViewPort(section)) {
			changeSlideCategoryIndex(index, section.id)
		}
	});
}
function checkShowTabSection() {
	let summary_section_container = document.getElementById('summary_section') as HTMLElement;
	if (summary_section_container?.getBoundingClientRect()?.top <= -55) {
		document.getElementById('sticky_header')?.classList.add('sticky-55');
	}
	else {
		document.getElementById('sticky_header')?.classList.remove('sticky-55');
	}
}
function changeSlideCategoryIndex(index = 0, elId: string) {
	sectionCarousel.value.slideTo(index)
	sectionViewing.value = elId;
}

function isElementInCenterViewPort(element: Element): boolean {
	const viewportHeight = window.innerHeight;
	const elementRect = element.getBoundingClientRect();

	// Element position relative to viewport
	const elementTop = elementRect.top;
	const elementBottom = elementRect.bottom;

	// Calculate viewport center
	const viewportCenter = viewportHeight / 2;
	// Check if element is in center of viewport
	return elementTop <= viewportCenter && elementBottom >= viewportCenter;
}
function chatToShop(firstMess?: string) {
	firstMessage.value = firstMess ?? null
	if (userProfile.value?.id) {
		chatToShopInfo.value = {
			members: [{
				member_id: productData.value?.shop?.id,
				member: JSON.parse(JSON.stringify(productData.value?.shop)),
				member_type: member_type.shop
			}],
			name: null,
			type: channel_type.user,
			avatar: productData.value?.shop?.logo
		};
		showChatToShop.value = true;
	}
	else {
		nuxtApp.$emit(appConst.event_key.require_login, {
			redirect_url: route.fullPath,
			back_on_close: false
		})
	}

}

function pushToAround(search_text: string) {
	nuxtApp.$emit('refresh_around');

	setTimeout(() => {
		filterData.value = {
			...filterData.value,
			search: search_text,
			search_text: search_text
		};

		// getSearchProductResult();
		router.push({
			path: appRoute.AroundComponent,
			query: {
				filter: JSON.stringify(filterData.value)
			}
		})
	}

		, 300);

}
</script>

<template>
	<RenderCacheable :cache-key="[locale, dataId].join('--')" class="public-container" v-on:scroll="(e: any) => {
		containerScrolling();
		checkShowTabSection()
	}">

		<div class='v-stack product-v2-container'>
			<!-- <HeaderV2Component :header_level_2_id="'sticky_header'" :last_element_id="null">
			</HeaderV2Component> -->
			<div class="sticky-header" id="sticky_header">
				<div class="search-input-container" id="search_input_container">
					<!-- <button class="back-button" v-on:click="() => {
						router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
					}">
						<Icon name="solar:round-alt-arrow-left-linear"></Icon>
					</button> -->
					<div class="search-input-group">
						<button class="search-button" v-on:click="() => {
							pushToAround(search_text)
						}">
							<Icon name="solar:rounded-magnifer-outline"></Icon>
						</button>
						<input type="search" name='search-text' :maxlength="appConst.max_text_short"
							:placeholder="productData?.name ?? $t('ProductV2Component.tim_san_pham_khac')"
							autoComplete='off' :value="search_text" v-on:input="($event: any) => {
								search_text = $event.target.value;
								// searchProduct()
							}" v-on:keypress.enter="($event: any) => {
								pushToAround(search_text)
								// searchProduct()
							}" />
						<button class="clear-button" v-show="search_text?.length > 0" v-on:click="() => {
							search_text = '';
							// searchProduct()
						}">
							<Icon name="iconamoon:sign-times" size="25" />
						</button>

					</div>
					<button class="share-button" v-on:click="() => { share() }">
						<Icon name="solar:share-linear"></Icon>
					</button>
					<!-- <nuxt-link :to="appRoute.CartComponent" class="cart-button">
						<Icon name=solar:cart-3-bold></Icon>
						<div class="count" v-if="cartData?.length > 0">{{ cartData.length < 10 ? cartData.length : "9+"
								}}</div>
					</nuxt-link> -->
				</div>
				<Swiper class="my-carousel sections-tab-carousel" :modules="[SwiperFreeMode, SwiperNavigation]"
					:navigation="true" :freeMode="true" :slides-per-view="'auto'" :slides-per-group-auto="true"
					:loop="false" :effect="'creative'" :autoplay="false" key="sections-carousel" @init="(e: any) => {
						sectionCarousel = e;
					}">
					<SwiperSlide class="section-tab-item-slide" v-on:click="async () => {
						await scrollToSection('summary_section')
						// filterCategory = itemCategory.id;
					}" :class="checkSectionViewing('summary_section') ? 'active' : ''" :value="'summary_section'"
						:key="'summary_section'" :id="'tab_summary_section'">
						<div class="tab-title">
							<span class='name'>
								{{ $t('ProductV2Component.tong_quan') }}
							</span>
						</div>
					</SwiperSlide>
					<SwiperSlide class="section-tab-item-slide" v-on:click="async () => {
						await scrollToSection('rating_section')
						// filterCategory = itemCategory.id;
					}" :class="checkSectionViewing('rating_section') ? 'active' : ''" :value="'rating_section'"
						:key="'rating_section'" :id="'tab_rating_section'">
						<div class="tab-title">
							<span class='name'>
								{{ $t('ProductV2Component.danh_gia') }}
							</span>
						</div>
					</SwiperSlide>
					<SwiperSlide class="section-tab-item-slide" v-on:click="async () => {
						await scrollToSection('review_section')
						// filterCategory = itemCategory.id;
					}" :class="checkSectionViewing('review_section') ? 'active' : ''" :value="'review_section'"
						:key="'review_section'" :id="'tab_review_section'">
						<div class="tab-title">
							<span class='name'>
								{{ $t('ProductV2Component.review') }}
							</span>
						</div>
					</SwiperSlide>
					<SwiperSlide class="section-tab-item-slide" v-on:click="async () => {
						await scrollToSection('related_section')
						// filterCategory = itemCategory.id;
					}" :class="checkSectionViewing('related_section') ? 'active' : ''" :value="'related_section'"
						:key="'related_section'" :id="'tab_related_section'">
						<div class="tab-title">
							<span class='name'>
								{{ $t('ProductV2Component.de_xuat') }}
							</span>
						</div>
					</SwiperSlide>
				</Swiper>
			</div>

			<div class='v-stack loading-skeleton' v-if="!productData?.name && refreshing">
				<v-skeleton-loader height=300></v-skeleton-loader>
				<v-skeleton-loader type="article"></v-skeleton-loader>
			</div>
			<div class="product-v2-content-container" v-else-if="productData?.name">
				<div class="product-v2-profile-picture tab-section" id="summary_section">
					<Swiper class="my-carousel stack-carousel product-images-carousel"
						v-if="productData?.images?.length" :modules="[SwiperAutoplay, SwiperNavigation]"
						:slides-per-view="1" :effect="'creative'" :navigation="true" :spaceBetween="10"
						v-on:slideChange="(e) => {
							indexActive = e.activeIndex;
						}" :autoplay="false" key="suggest-carousel">
						<SwiperSlide class="item-stack-slide" v-for="(item, indexImg) of productData.images"
							:key="'suggest_' + item.id">
							<img loading="lazy" class="item-stack" v-on:click="() => {
								listObjectViewer = productData.images;
								indexActive = indexImg;
								showImageViewerModal = true;
							}" v-on:error="(event: any) => {
								event.target.src = icon_for_broken_image;
							}" :src="item?.path ? (domainImage + item?.path) : icon_for_product" :placeholder="icon_for_product"
								:alt="showTranslateProductName(productData)"
								:title="showTranslateProductName(productData)" />
						</SwiperSlide>
						<div class="image-index-showing">
							{{ indexActive + 1 }}/{{ productData?.images?.length }}
						</div>
					</Swiper>
					<img v-else v-on:click="() => {
						let itemImg = {
							path: productData.profile_picture,
							title: showTranslateProductName(productData),
						}
						listObjectViewer = [itemImg];
						indexActive = 0;
						showImageViewerModal = true;
					}" v-on:error="(event: any) => {
						event.target.src = icon_for_broken_image;
					}" :src="productData?.profile_picture?.length ? (domainImage + productData.profile_picture) : icon_for_product"
						:placeholder="icon_for_product"
						:alt="productData ? showTranslateProductName(productData) : $t('ProductV2Component.khong_co_avatar')" />
					<div class="icon-hang-tuyen" v-if="productData.is_suggest">
						<span class="icon-1">hàng</span>
						<span class="icon-2">tuyển</span>
					</div>

				</div>
				<div class="product-v2-flash-sale"
					v-if="(productData.price_off != null && productData.price_off < productData.price) && false">
					<div class="flash-sale-label">
						<Icon name="streamline:flash-1-solid"></Icon>
						<span>Flash Sale</span>
					</div>
					<div class="flash-sale-time-remaining">
						<client-only>
							<vue-countdown class="cool-down" :time="(moment().endOf('day').diff(moment()))"
								v-slot="{ hours, minutes, seconds }">
								{{ $t('ProductV2Component.ket_thuc_sau') }}:
								<span>
									<em>{{ Math.floor(hours / 10) }}</em>
									<em>{{ hours % 10 }}</em>
								</span>:<span>
									<em>{{ Math.floor(minutes / 10) }}</em>
									<em>{{ minutes % 10 }}</em>
								</span>:<span>
									<em>{{ Math.floor(seconds / 10) }}</em>
									<em>{{ seconds % 10 }}</em>
								</span>
							</vue-countdown>
						</client-only>
					</div>
				</div>
				<div class="product-v2-primary-info">
					<div class="price-info">
						<span class='product-price'>
							{{
								(productData.price_off != null && productData.price_off < productData.price) ?
									formatCurrency(parseFloat(productData.price_off), productData.shop?.currency) :
									(parseFloat(productData.price) == 0 || productData.price == null) ?
										$t('ProductV2Component.gia_lien_he') : formatCurrency(parseFloat(productData.price),
											productData.shop?.currency) }} <em class="off"
								v-if="(productData.price_off != null && productData.price_off < productData.price)">{{
									(parseFloat(productData.price) == 0 || productData.price == null)
										? $t('ProductV2Component.gia_lien_he')
										: formatCurrency(parseFloat(productData.price), productData.shop?.currency)
								}}</em>
						</span>
						<div class="percent-tag"
							v-if="(productData.price_off != null && productData.price_off < productData.price)">
							{{ $t('ProductV2Component.tiet_kiem_toi', { percent: getPercentSaleOff(productData) }) }}
						</div>
					</div>
					<span class="product-name">
						<!-- {{ productData?.name }} -->
						{{ showTranslateProductName(productData) }}
					</span>
					<div class="rating-sold-amount">
						<div class="rating" v-if="productData.ratings">
							<Icon name="solar:star-bold"></Icon> {{ productData.ratings?.toFixed(1) || 0 }}<span>/{{
								appConst.defaultMaxRate }}</span>
						</div>

						<div class="rate-amount" v-if="productData.comment_count">
							({{ productData.comment_count || 0 }})
						</div>

						<div class="sold-amount" v-if="productData.sold_counter">
							{{ $t('ProductV2Component.da_ban', { count: productData.sold_counter || 0 }) }}
						</div>

						<button class="heart-button" :class="{ 'liked': is_liked }"
							:title="is_liked ? $t('ProductV2Component.da_thich') : $t('ProductV2Component.them_vao_yeu_thich')"
							v-on:click="() => {
								likeProduct()
							}">
							<Icon name="solar:heart-bold" v-if="is_liked"></Icon>
							<Icon name="solar:heart-outline" v-else></Icon>
						</button>
					</div>

					<nuxt-link v-if="productData.is_suggest" :to="appRoute.HomeComponent" v-on:click.native="() => {
						// router.push({
						// 	path: appRoute.HomeComponent,
						// 	query: {
						// 		section: 'hang-tuyen'
						// 	}
						// })
					}" class="hang-tuyen-button">
						<Icon name="solar:crown-star-bold" class="left-icon"></Icon>
						<span>{{ $t('ProductV2Component.hang_tuyen') }}: <span>{{
							$t('ProductV2Component.giai_thich_hang_tuyen') }}</span></span>
						<!-- <Icon name="solar:alt-arrow-right-linear" class="right-icon"></Icon> -->
					</nuxt-link>

					<div class="product-v2-short-descriptions"
						v-if="showTranslateProductDescription(productData)?.length > 0">
						<span class='descriptions-section-label'>
							{{ $t('ProductV2Component.mo_ta_san_pham') }}
						</span>
						<div class="short-description-container">
							<span>
								<!-- {{ productData.notes }} -->
								{{ showTranslateProductDescription(productData) }}
							</span>
						</div>
					</div>
					<div class="product-v2-short-descriptions" v-else>
						<span class='descriptions-section-label'>
							{{ $t('ProductV2Component.mo_ta_san_pham') }}
						</span>
						<div class="short-description-container">
							<span>
								<!-- hiển thị name khi không có notes -->
								{{ showTranslateProductName(productData) }}
							</span>
						</div>
					</div>
				</div>



				<div class="product-v2-rating tab-section" id="rating_section">
					<div class="stack-title">
						<span class="label">{{ $t('ProductV2Component.danh_gia_cua_khach_hang') }}</span>
						<button class="add-comment" v-on:click="showAddCommentModal = true" v-if="!myRating?.id">
							{{ $t('ProductV2Component.them_danh_gia') }}
							<Icon name="pajamas:duo-chat"></Icon>
						</button>
					</div>

					<div class="rating-overview">
						<div class="rating" v-if="productData.ratings">
							<NuxtRating v-if="productData.ratings" active-color="#ffc107" :border-color="'transparent'"
								:roundedCorners="true" :rating-spacing="5" :rating-step=".5" :rating-size="10"
								:rating-value="productData.ratings || 0" />
							<span>{{ productData.ratings?.toFixed(1) || 0 }}/{{
								appConst.defaultMaxRate }}</span>
						</div>
						<div class="comment" v-if="productData.comment_count">
							({{ productData.comment_count }})
						</div>
					</div>

					<div class="my-comment comment-container" v-if="myRating?.id">
						<div class="comment-header">
							<label>{{ $t('ProductV2Component.danh_gia_cua_ban') }}</label>
							<button class="update-comment" v-on:click="showUpdateCommentModal = true">{{
								$t('ProductV2Component.cap_nhat') }}</button>
						</div>

						<div class="comment-item-container">
							<div class="user-info">
								<img loading="lazy" :src="myRating.users?.profile_picture
									? ((appConst.provider_img_domain.some(e => myRating.users?.profile_picture.includes(e)))
										? myRating.users?.profile_picture
										: (domainImage + myRating.users?.profile_picture)
									)
									: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:error="(event: any) => {
										event.target.src = non_avatar;
									}" />
								<div class="name-rate">
									<span class="name">
										{{ myRating.users.name }}
									</span>
									<NuxtRating v-if="myRating.rating" active-color="#ffc107"
										:border-color="'transparent'" :rounded-corners="true" :rating-spacing="5"
										:rating-step=".5" :rating-size="10" :rating-value="myRating.rating || 0"
										class="rating" />
								</div>
							</div>

							<div class="comment-detail" v-if="myRating.review?.length">
								{{ myRating.review }}
							</div>
							<div class="comment-images" v-if="myRating.images?.length">
								<div class="comment-image-item" v-for="(itemImg, index) in myRating.images" v-on:click="() => {
									listObjectViewer = JSON.parse(JSON.stringify(myRating.images));
									indexActive = index;
									showImageViewerModal = true;
								}">
									<img :src="itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image"
										v-on:error="(event: any) => {
											event.target.title = t('ProductComponent.loi_anh');
											event.target.src = icon_for_broken_image;
										}" v-on:click="(event: any) => {
											event.target.title = '';
											event.target.src = itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image
										}" loading="lazy" />
								</div>
							</div>
							<div class="comment-time">
								<span>
									{{ moment(myRating.created_at, 'YYYY-MM-DD HH:mm:ss').format('DD-MM-YYYY HH:mm')
									}}</span>
								<button v-if="myRating.shop_reply" class="open-shop-reply" v-on:click="() => {
									myRating.open_shop_reply = true
								}">
									{{ $t('ProductV2Component.phan_hoi_tu_cua_hang') }}
									<Icon name="solar:alt-arrow-down-linear"></Icon>
								</button>
							</div>
							<div class="shop-reply" v-if="(myRating.shop_reply && myRating.open_shop_reply)">
								<span class="label">{{ $t('ProductV2Component.phan_hoi_tu_cua_hang') }}</span>
								<span class="content">{{ myRating.shop_reply }}</span>
							</div>
						</div>
					</div>

					<div class="comment-container" id="product_comment">
						<div class="comment-item-container" v-for="itemComment in productData.comments">
							<div class="v-stack"></div>
							<div class="user-info" v-if="itemComment.users?.id">
								<img loading="lazy" :src="itemComment.users?.profile_picture
									? ((appConst.provider_img_domain.some(e => itemComment.users?.profile_picture.includes(e)))
										? itemComment.users?.profile_picture
										: (domainImage + itemComment.users?.profile_picture)
									)
									: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:error="(event: any) => {
										event.target.src = non_avatar;
									}" />
								<div class="name-rate">
									<span class="name">
										{{ itemComment.users.name }}
									</span>
									<NuxtRating v-if="itemComment.rating" active-color="#ffc107"
										:border-color="'transparent'" :rounded-corners="true" :rating-spacing="5"
										:rating-step=".5" :rating-size="10" :rating-value="itemComment.rating || 0"
										class="rating" />
								</div>
							</div>

							<div class="comment-detail" v-if="itemComment.review?.length">
								{{ itemComment.review }}
							</div>
							<div class="comment-images" v-if="itemComment.images?.length">
								<div class="comment-image-item" v-for="(itemImg, index) in itemComment.images"
									v-on:click="() => {
										listObjectViewer = JSON.parse(JSON.stringify(itemComment.images));
										indexActive = index;
										showImageViewerModal = true;
									}">
									<img :src="itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image"
										v-on:error="(event: any) => {
											event.target.title = t('ProductComponent.loi_anh');
											event.target.src = icon_for_broken_image;
										}" v-on:click="(event: any) => {
											event.target.title = '';
											event.target.src = itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image
										}" loading="lazy" />
								</div>
							</div>
							<div class="comment-time">
								<span>
									{{ moment(itemComment.created_at, 'YYYY-MM-DD HH:mm:ss').format('DD-MM-YYYY HH:mm')
									}}</span>
								<button v-if="itemComment.shop_reply" class="open-shop-reply" v-on:click="() => {
									itemComment.open_shop_reply = true
								}">
									{{ $t('ProductV2Component.phan_hoi_tu_cua_hang') }}
									<Icon name="solar:alt-arrow-down-linear"></Icon>
								</button>
							</div>
							<div class="shop-reply" v-if="(itemComment.shop_reply && itemComment.open_shop_reply)">
								<span class="label">{{ $t('ProductV2Component.phan_hoi_tu_cua_hang') }}</span>
								<span class="content">{{ itemComment.shop_reply }}</span>
							</div>
						</div>
					</div>
					<button class="rate-loadmore"
						v-if="(productData?.comments?.length + (myRating?.id ? 1 : 0)) < productData.comment_count"
						v-on:click="loadmoreRating()">
						{{ t('ProductV2Component.hien_thi_them_danh_gia') }}
						<Icon name="solar:alt-arrow-down-linear"></Icon>
					</button>
					<div class="show-all-rating" v-else-if="productData.comment_count > 0">
						{{ t('ProductV2Component.hien_thi_tat_ca') }}
					</div>
					<div class="show-all-rating" v-else>
						{{ t('ProductV2Component.chua_co_danh_gia') }}
					</div>
				</div>

				<div class="product-v2-review tab-section" id="review_section">
					<div class="stack-title">
						<Icon name="pajamas:duo-chat" class="left-icon"></Icon>
						<span class="label">{{ $t('ProductV2Component.video_review_san_pham') }}</span>
					</div>

					<Swiper v-if="listVideo?.length" class="my-carousel stack-carousel product-review-carousel"
						:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
						:slides-per-group-auto="true" :simulateTouch="true" :loop="false" :effect="'creative'"
						:navigation="true" :freeMode=false :autoplay="false" :spaceBetween="10"
						key="shop-around-carousel">
						<SwiperSlide class="item-stack-slide product-review-stack" v-for="item of listVideo"
							:key="'product_review_' + item.id">
							<nuxt-link :dragable="false" :to="'#'" class="item-stack" :title="item.file_name">
								<div class="item-stack-header">
									<span>
										<Icon name="solar:eye-scan-bold"></Icon>
										{{ showView(item.view_total || 0) }}
									</span>
								</div>

								<div class='primary-content' v-on:click="(e: any) => {
									e.stopPropagation();
									e.preventDefault();
								}">
									<client-only v-if="item.file_type == 'video'">
										<VideoHLSPlayerComponent :src="item.file_path" :video_id="item.id" :width="150"
											:height="300"
											:thumbnail="item.thumbnail ? (domainImage + item.thumbnail?.path) : null"
											:controls="false" v-on:clickOnPlaying="(e) => {
												// router.push(appRoute.DetailShopComponent + '/' + item.slug)
											}">
										</VideoHLSPlayerComponent>
									</client-only>
									<img v-else :src="item.file_path" />
								</div>
								<div class="item-stack-footer">
									<span class="video-name">{{ item.file_name }}</span>
									<!-- <span class="shop-address">{{ item.address }}</span> -->
								</div>
							</nuxt-link>
						</SwiperSlide>
					</Swiper>
					<div class="show-all-rating" v-else>
						{{ t('ProductV2Component.chua_co_review') }}
					</div>
				</div>
				<div class="product-v2-descriptions" v-if="showTranslateProductDescription(productData)?.length > 0">
					<span class='descriptions-section-label'>
						{{ $t('ProductV2Component.chi_tiet_san_pham') }}
					</span>
					<div class="description-container" id="product_note_container"
						:class="{ 'show-full': isShowFullNote }">
						<span id="product_note_content">
							<!-- {{ productData.notes }} -->
							{{ showTranslateProductDescription(productData) }}
						</span>

					</div>
					<div class="show-full-button" v-if="showFullButton">
						<button v-on:click="() => {
							isShowFullNote = !isShowFullNote;
							checkNoteShowFullable();
						}">{{ isShowFullNote ? $t('ProductV2Component.thu_gon') : $t('ProductV2Component.mo_rong') }}</button>
					</div>
				</div>
				<div class="product-v2-descriptions" v-else>
					<span class='descriptions-section-label'>
						{{ $t('ProductV2Component.chi_tiet_san_pham') }}
					</span>
					<div class="description-container show-full" id="product_note_container">
						<span id="product_note_content">
							<!-- hiển thị name khi không có notes -->
							{{ showTranslateProductName(productData) }}
						</span>

					</div>
				</div>

				<div class="product-v2-shop" v-if="productData?.shop?.id">
					<div class="shop-info-content">
						<AvatarComponent class="product-v2-shop-logo" :imgTitle="productData.shop?.name"
							:imgStyle="productData.shop?.logo?.style" :imgSrc="productData.shop?.logo
								? (domainImage + productData.shop?.logo.path)
								: productData.shop?.banner
									? (domainImage + productData.shop?.banner.path)
									: ''
								" :width="70" :height="70" />
						<nuxt-link
							:to="appRoute.DetailShopComponent + '/' + (productData.shop?.slug || productData.shop?.id)"
							class="shop-info">
							<span class="name">{{ productData.shop?.name }}</span>
							<span class="follows">
								<em>{{ showView(productData.shop?.follows ?? 0) }}</em> {{
									$t('ProductV2Component.nguoi_theo_doi') }}
							</span>
							<div class="rating-actions">
								<div class="rating" v-if="productData?.shop?.ratings">
									<Icon name="solar:star-bold"></Icon>
									{{ productData?.shop?.ratings?.toFixed(1) || 0 }}
									<span>/{{ appConst.defaultMaxRate }}</span>
								</div>
								<button class="go-to-shop-button">
									<Icon name="solar:shop-2-bold"></Icon>
									{{ $t('ProductV2Component.ghe_shop') }}
								</button>
							</div>
						</nuxt-link>
					</div>

					<div class="quick-chat-to-shop">
						<span class="quick-chat-item" v-on:click="() => {
							chatToShop(itemQuickChat)
						}" v-for="itemQuickChat in listQuickChat">
							{{ itemQuickChat }}
						</span>
					</div>
				</div>

				<div class="product-v2-related tab-section" v-if="similarProducts?.length" id="related_section">
					<span class="label">{{ $t('ProductV2Component.co_the_ban_cung_thich') }}</span>
					<div class="list-related-products" v-if="similarProducts?.length">
						<div class="product-item-container-grid" v-for="(itemProduct, index) of similarProducts"
							:key="'similar_product_' + itemProduct.id + '_' + index"
							:id="'similar_product_' + itemProduct.id + '_' + index">
							<nuxt-link
								:to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
								class="product-item">
								<img loading="lazy" v-bind:style="{
									height: getSlideHeight('similar_product_' + itemProduct.id) + 'px',
									maxHeight: getSlideHeight('similar_product_' + itemProduct.id) + 'px'
								}" v-on:click="() => {
									router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
								}" :src="(itemProduct && itemProduct.profile_picture)
									? (domainImage + itemProduct.profile_picture)
									: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" v-on:error="(event: any) => {
										event.target.src = icon_for_product;
									}" />

								<div class="product-item-content">
									<span class="name">{{ showTranslateProductName(itemProduct) }}</span>
									<span class="sold-like-amount">
										<span v-if="itemProduct.sold_count">
											{{ $t('AroundComponent.luot_ban', {
												count: itemProduct.sold_count ??
													0
											}) }}
										</span>
										<span v-if="itemProduct.sold_count && itemProduct.likes">
											&nbsp;|&nbsp;
										</span>
										<span v-if="itemProduct.likes">
											{{ $t('AroundComponent.luot_thich', {
												count: itemProduct.likes ??
													0
											}) }}
										</span>

									</span>
									<div class="h-stack">
										<span class="price">
											<em class="off"
												v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">{{
													(parseFloat(itemProduct.price) == 0 || itemProduct.price ==
														null)
														? $t('ShopComponent.gia_lien_he')
														: formatCurrency(parseFloat(itemProduct.price), itemProduct.shop
															?
															itemProduct.shop.currency
															: itemProduct.currency)
												}}</em>
											{{
												(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)
													? formatCurrency(parseFloat(itemProduct.price_off), itemProduct.shop ?
														itemProduct.shop.currency : itemProduct.currency) :
													(parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
														$t('ShopComponent.gia_lien_he') :
														formatCurrency(parseFloat(itemProduct.price), itemProduct.shop ?
															itemProduct.shop.currency : itemProduct.currency) }} </span>

									</div>
								</div>
							</nuxt-link>
						</div>
					</div>
					<div class="none-list-related-products" v-else>
						<span>{{ $t('ProductV2Component.chua_co_san_pham_de_xuat') }}</span>
					</div>
				</div>

				<div class="product-v2-footer">
					<nuxt-link
						:to="appRoute.DetailShopComponent + '/' + (productData.shop?.slug || productData.shop?.id)"
						class="footer-button">
						<Icon name="solar:shop-2-linear"></Icon>
						<span>
							{{ $t('ProductV2Component.di_den_cua_hang') }}
						</span>

					</nuxt-link>
					<button class="footer-button" v-on:click="() => {
						chatToShop()
					}">
						<Icon name="solar:dialog-linear"></Icon>
						<span>
							{{ $t('ProductV2Component.chat_ngay') }}
							<em>{{ $t('ProductV2Component.dich_thuat_thong_minh') }}</em>
						</span>

					</button>
					<button class="footer-button add-to-cart" v-on:click="async () => {
						showAddToCartModal = true;
					}" v-on:click.stop="(e) => { e.preventDefault() }">
						<Icon name="solar:cart-plus-linear"></Icon>
						<span>
							{{ $t('ProductV2Component.them_vao_gio_hang') }}
						</span>
					</button>
				</div>
			</div>
			<div class="product-v2-content-container" v-else>
				<Product404Component />
			</div>
		</div>



		<AddProductToCartComponent v-if="showAddToCartModal" v-on:close="() => {
			showAddToCartModal = false
		}" :selectedProduct="selectedChildProduct?.id ? selectedChildProduct : productData" :noteForAddItem="notesToCartRef">
		</AddProductToCartComponent>
		<AddCommentComponent v-if="showAddCommentModal" :showModal="showAddCommentModal"
			:object="selectedChildProduct?.id ? selectedChildProduct : productData"
			:object_type="appConst.object_type.product" v-on:close="(e: any) => {
				if (e == true) {
					getProductRating();
					getMyRatingToProduct();
				}
				showAddCommentModal = false
			}" v-on:submit="() => {

			}">

		</AddCommentComponent>

		<AddCommentComponent v-if="showUpdateCommentModal" :showModal="showUpdateCommentModal"
			:object="selectedChildProduct?.id ? selectedChildProduct : productData"
			:initData="JSON.parse(JSON.stringify(myRating))" :object_type="appConst.object_type.product" v-on:close="(e: any) => {
				if (e == true) {
					getProductRating();
					getMyRatingToProduct();
				}
				showUpdateCommentModal = false
			}" v-on:submit="() => {

			}">

		</AddCommentComponent>

		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>
		<v-overlay v-model="showChatToShop" :z-index="100" :absolute="false" contained :close-on-back="true"
			key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
			no-click-animation v-on:click:outside="() => {
				showChatToShop = false;
			}">
			<ChatDetailComponent v-if="showChatToShop" :receiver_id="productData?.shop?.id" :chat_info="chatToShopInfo"
				:firstMessage="firstMessage" :receiver_type="true" :product_link="productData?.slug ?? productData?.id"
				:product_detail="productData" :mode="member_type.user" v-on:close="() => {
					showChatToShop = false;
				}"></ChatDetailComponent>
		</v-overlay>
	</RenderCacheable>

</template>
<style lang="scss" src="./ProductV2Styles.scss"></style>