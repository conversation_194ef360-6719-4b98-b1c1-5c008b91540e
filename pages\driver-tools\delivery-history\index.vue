<template>
  <DeliveryHistoryComponent v-if="is_authorized == true"></DeliveryHistoryComponent>
  <UnauthorizedComponent v-else-if="is_authorized == false"></UnauthorizedComponent>
</template>
<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import { AuthService } from '~/services/authService/authService';

const nuxtApp = useNuxtApp();
const { t } = useI18n();
var authService = new AuthService();

var is_authorized = ref<any>(null);


onBeforeMount(async () => {
  nuxtApp.$emit(appConst.event_key.show_footer, false);
  let author = (await authService.checkAuthorize(appConst.role_enum.driver) || await authService.checkAuthorize(appConst.role_enum.admin) || await authService.checkAuthorize(appConst.role_enum.admin2));
  is_authorized.value = author
  
});

onMounted(() => {
  useSeoMeta({
    title: t('AppRouteTitle.DeliveryHistoryComponent')
  });
  document.title = t('AppRouteTitle.DeliveryHistoryComponent')
})
</script>