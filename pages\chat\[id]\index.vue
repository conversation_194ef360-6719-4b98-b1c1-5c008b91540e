<template>
    <div class="public-container chat-page-container">
        <ChatDetailComponent 
            :receiver_id="route.params.id" 
            :receiver_type="receiver_type" :chat_info="chat_info"
            :mode="member_type.user" :firstMessage="firstMessage" :product_link="product_link" :product_detail="product_detail" 
            v-on:close="()=>{
                router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
            }"
        />
    </div>

</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import { member_type } from '~/components/chatManage/ChatDTO';
const nuxtApp = useNuxtApp();
const route = useRoute();
const router = useRouter();

var receiver_id = ref(route.params.channel_id);
var receiver_type = ref<any>(router.options.history.state.receiver_type ? router.options.history.state.receiver_type : "");
var chat_info = ref<any>(router.options.history.state.chat_info ? router.options.history.state.chat_info : "");
var mode = ref<any>(router.options.history.state.mode ? router.options.history.state.mode : "");
var firstMessage = ref<any>(router.options.history.state.firstMessage ? router.options.history.state.firstMessage : "");
var product_link = ref<any>(router.options.history.state.product_link ? router.options.history.state.product_link : "");
var product_detail = ref<any>(router.options.history.state.product_detail ? router.options.history.state.product_detail : "");
onBeforeMount(() => {
    nuxtApp.$emit(appConst.event_key.show_footer, true)
})
</script>