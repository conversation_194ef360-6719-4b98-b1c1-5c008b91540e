<template>
  <div class="cart-overview-container">
    <div class="cart-overview-header">
      <h3>{{ $t('CartOverviewComponent.gio_hang') }}</h3>
      <v-btn class="close-cart-overview" variant="text" @click="emit('close')">
        <Icon name="clarity:times-line" size="25"></Icon>
      </v-btn>
    </div>
    <div class="cart-overview-content" v-if="(cartData && cartData.length)">
      <div class="cart-overview-item" v-for="(itemCart, index) in cartData">
        <img loading="lazy" :src="(itemCart && itemCart.product?.profile_picture)
          ? (domainImage + itemCart.product?.profile_picture)
          : icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemCart.product)" />
        <div class='v-stack item-cart-detail'>
          <div class='h-stack item-cart-product-name' :title="showTranslateProductName(itemCart.product)">
            <span>
              {{ showTranslateProductName(itemCart.product) }}
            </span>
          </div>
          <div class="price-quantity">
            <span class='product-price'>
              {{
                (itemCart.product?.price_off != null && itemCart.product?.price_off < itemCart.product?.price) ?
                  formatCurrency(parseFloat(itemCart.product?.price_off), itemCart.product?.shop?.currency) :
                  (parseFloat(itemCart.product?.price) == 0 || itemCart.product.price == null) ?
                    $t('CartOverviewComponent.gia_lien_he') : formatCurrency(parseFloat(itemCart.product?.price),
                      itemCart.product.shop?.currency) }} <em class="off"
                :class="{ 'show': (itemCart.product?.price_off != null && itemCart.product?.price_off < itemCart.product?.price) }">
                {{
                  formatCurrency(itemCart.product?.price ? parseFloat(itemCart.product?.price) :
                    0,
                    itemCart.product?.shop?.currency)
                }}
                </em>
            </span>
            <div class='h-stack item-cart-quantity'>
              x{{ itemCart.quantity }}
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="cart-overview-content" v-if="!(cartData && cartData.length)">
      <p class="empty-cart">{{ $t('CartOverviewComponent.gio_hang_trong') }}</p>
    </div>
    <div class="cart-overview-footer" v-if="(cartData && cartData.length)" v-on:click="() => {
      emit('close')
    }">
      <div class="cart-subtotal">
        <span>{{ $t('CartOverviewComponent.tam_tinh') }} <em>({{ $t('CartOverviewComponent.mat_hang', {
          count: cartData?.length
        }) }})</em></span>
        <span class="price" v-if="cartData.length">{{
          getSubtotalItems() ? formatCurrency(getSubtotalItems(), cartData[0].product?.shop?.currency) :
            $t('CartOverviewComponent.gia_lien_he')
        }}</span>
      </div>
      <nuxt-link :to="appRoute.CartComponent">
        <v-btn class="cart-overview-button" variant="tonal" color="var(--primary-color-1)">
          <span>{{ $t('CartOverviewComponent.xem_gio_hang') }}</span>
        </v-btn>
      </nuxt-link>



      <nuxt-link :to="appRoute.OrderComponent">
        <v-btn class="cart-overview-button" type="link" variant="outlined" color="var(--primary-color-2)">
          <span>{{ $t('CartOverviewComponent.dat_hang_ngay') }}</span>
        </v-btn>

      </nuxt-link>
    </div>
  </div>
</template>

<style lang="scss" src="./CartOverviewStyles.scss"></style>

<script setup lang="ts">
import icon_for_product from '../../assets/image/icon-for-product.png';
import { HttpStatusCode } from 'axios';
import { appConst, domainImage, formatCurrency, showTranslateProductName } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { UserService } from '~/services/userService/userService';

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const props = defineProps({
  cartData: null,
});
const emit = defineEmits(['close']);

var userService = new UserService();
var authService = new AuthService();

var cartData = ref();
onBeforeMount(() => { })

onMounted(async () => {
  getCart();
})
onUpdated(() => { })

function getCart() {
  let cartData$ = JSON.parse(
    localStorage.getItem(appConst.storageKey.cart) as string
  );
  cartData.value = cartData$;
}

function getSubtotalItems() {
  if (!cartData.value.length) return 0;
  let indexPriceNull = cartData.value.findIndex(function (e: any) {
    return (e.price == 0 || e.price == null)
  })
  if (indexPriceNull != -1) return null;
  return cartData.value
    .reduce((total: number, current: any) =>
      total + (current.price ? parseFloat(current.price.toString()) : 0), 0
    )
}

</script>