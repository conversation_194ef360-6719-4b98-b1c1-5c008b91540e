.skeleton-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1;
  background-color: white;
  position: relative;
  padding: 0;
  font-size: 1.2em;
  // overflow: auto;

  // & > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }
}

.register-shop-button-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  align-items: center;
  justify-content: center;
  gap: 15px;

  // & > .title-header {
  //   width: 100%;
  //   // font-size: 1.6em;
  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .register-shop-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 10px;

    & > img {
      width: 300px;
      height: 300px;
      align-items: center;
      object-fit: contain;
      position: relative;
    }

    & span {
      font-size: 1.5em;
      font-weight: 500;
    }

    & button {
      background-color: var(--primary-color-1);
      border-radius: 10px;
      padding: 5px 20px;
      color: white;
      font-size: 1.5em;
      border: none;
    }
  }
}

.my-shop-container {
  display: flex;
  flex-direction: column;
  flex: 1 1;
  background-color: var(--color-background-2);
  position: relative;
  padding: 0;
  font-size: 17px;
  // overflow: auto;
  font-weight: bold;
  max-width: var(--max-width-content-view-1024) !important;

  // & > .title-header {

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .my-shop-detail-container {
    display: flex;
    flex: 1;
    padding-bottom: 1em;
    flex-direction: column;
    // overflow: auto;
    // background-image: url("~/assets/image_08_04_2024/my-shop-background.jpg");
    background-size: 100%;
    background-color: #f4f4f4;

    & .blocked-shop{
      color: white;
      text-align: center;
      font-style: italic;
      margin-top: 10px;
      font-size: 25px;
      font-weight: 800;
      padding: 0 50px;
      background: linear-gradient(to right, transparent 5px, var(--primary-color-2) , transparent calc(100% - 5px));
    }

    & .waiting-confirm-shop{
      color: white;
      text-align: center;
      font-style: italic;
      margin-top: 10px;
      font-size: 20px;
      font-weight: 800;
      padding: 0 50px;
      background: linear-gradient(to right, transparent 2px, #0073c5 50%, transparent calc(100% - 2px));
    }
    // & > img {
    //   width: 100%;
    //   height: 150px;
    //   align-items: center;
    //   object-fit: cover;
    //   position: relative;
    // }

    // & > .my-shop-detail-content {
    //   gap: 10px;
    //   padding: 0 10px;

    //   & > .primary-detail {
    //     justify-content: space-between;
    //     flex: 1;
    //     gap: 10px;

    //     & .shop-name {
    //       font-weight: bold;
    //     }

    //     & .shop-slug {
    //       color: var(--primary-color-1);
    //       font-size: 0.85em;
    //     }

    //     & button {
    //       border: none;
    //       padding: 0;
    //       background: white;
    //       border-radius: 50%;
    //       margin-left: auto;
    //       width: 40px;
    //       height: 40px;
    //       font-size: 1.1em;
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       box-shadow: 0 0 5px #ccc;
    //     }
    //   }

    //   & > .other-detail {
    //     & > :first-child {
    //       border-top-left-radius: 10px;
    //       border-top-right-radius: 10px;
    //     }
    //     & > :last-child {
    //       border-bottom-left-radius: 10px;
    //       border-bottom-right-radius: 10px;
    //     }
    //     & > a {
    //       width: 100%;
    //     }
    //     & > a > button {
    //       padding: 5px 5%;
    //       background: white;
    //       border: none;
    //       display: flex;
    //       width: 100%;
    //       border-radius: inherit;
    //       /* justify-content: space-between; */
    //       align-items: center;

    //       & > .icon {
    //         font-size: 1.3em;
    //         justify-content: center;
    //         color: var(--primary-color-1);
    //       }
    //       & > span {
    //         color: var(--primary-color-1);
    //         font-weight: 600;
    //         align-items: center;
    //         margin-left: 1em;
    //         margin-right: auto;
    //       }
    //       & > .icon-right {
    //         font-size: 1.3em;
    //         justify-content: center;
    //       }
    //     }

    //     & > .order-manage-options {
    //       gap: 10px;
    //       padding: 10px;
    //       justify-content: space-between;
    //       background-color: white;

    //       & > button {
    //         min-width: 30%;
    //         width: 100px;
    //         min-height: 100px;
    //         aspect-ratio: 1;
    //         position: relative;
    //         background-color: var(--color-background-2);
    //         display: flex;
    //         flex-direction: column;
    //         justify-content: center;
    //         align-items: center;
    //         border: none;

    //         & > img {
    //           width: 50%;
    //           aspect-ratio: 1;
    //         }
    //         & > span {
    //           font-size: 0.8em;
    //         }
    //         & > em {
    //           border-radius: 2em;
    //           color: white;
    //           background: var(--color-button-error);
    //           min-width: 25px;
    //           height: 25px;
    //           font-size: .8em;
    //           padding: 5px;
    //           display: flex;
    //           align-items: center;
    //           justify-content: center;
    //           font-style: normal;
    //           position: absolute;
    //           top: 5px;
    //           right: 5px;
    //           line-height: 1;
    //           font-weight: 500;

    //           & > span {
    //             font-size: 0.8em;
    //             display: flex;
    //             align-items: center;
    //             justify-content: center;
    //           }
    //         }
    //       }
    //     }

    //     & > .product-manage-options {
    //       padding: 0 30px;
    //       background-color: white;

    //       & .total-product-button {
    //         display: flex;
    //         flex-direction: column;
    //         justify-content: center;
    //         align-items: center;
    //         width: 100%;
    //         background: inherit;
    //         border: none;
    //         border-bottom: thin solid #ccc;
    //         padding: 5px;

    //         & > span {
    //           font-size: 1.5em;
    //           font-weight: 600;
    //         }

    //         & > span + span {
    //           font-size: 0.85em;
    //         }
    //       }

    //       & .product-action {
    //         width: 100%;
    //         background: white;
    //         border: none;
    //         border-bottom: thin solid #ccc;
    //         height: 50px;
    //         gap: 10px;
    //         display: flex;
    //         align-items: center;
    //         justify-content: flex-start;
    //         color: var(--primary-color-1);
    //         font-size: 1em;
    //         font-weight: 600;
    //       }

    //       & .product-action.last {
    //         border-bottom: none;
    //       }
    //     }
    //   }
    // }

    & .primary-shop-info {
      padding: 100px 15px 0;
      margin-bottom: 75px;
      & > .primary-info-content.skeleton {
        background-image: none;
      }
      & > .primary-info-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 15px;
        // background: white;
        background-image: url("~/assets/image_08_04_2024/my-shop-info-background.png");
        background-size: 100%;
        background-color: white;
        box-shadow: 0 0 5px rgb(0, 0, 0, 20%);
        margin-bottom: -50px;

        & > .shop-info-buttons {
          display: flex;
          justify-content: space-between;
          height: 50px;
          width: 100%;
          margin-top: 10px;

          > .button-skeleton {
            width: 55%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-size: 0.75em;
            font-weight: bold;
            padding: 0 !important;
          }

          & > button {
            width: 50%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-size: 0.75em;
            font-weight: bold;
          }

          & > .profile-info-button {
            background-color: #efefef;
            color: #595e63;
            border-radius: 0 0 0 15px;
            clip-path: polygon(0 0, calc(100% - 35px) 0, 100% 100%, 0% 100%);
            margin-right: -13px;
            padding-right: 7%;
          }

          & > .others-shop-button {
            background-color: #efefef;
            color: #595e63;
            border-radius: 0 0 15px 0;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 35px 100%);
            margin-left: -13px;
            padding-left: 7%;
            padding-right: 5px;
            justify-content: center;
            overflow: hidden;
            text-overflow: ellipsis;
            gap: 10px;
            flex: 1;

            & > svg {
              width: 25px;
              min-width: 25px;
            }
            & > svg.right {
              width: 20px;
              min-width: 20px;
            }
          }
        }

        & .shop-banner {
          margin: 10px;
          width: 90px;
          min-width: 90px;
          height: 90px;
          box-shadow: 0 0 0 3px white;
          margin-top: -35px;
          border-radius: 50%;
          cursor: pointer;
        }

        & .none-avatar {
          /* flex: 1; */
          margin: 10px;
          justify-content: center;
          width: 75px;
          height: 75px;
          object-fit: contain;
          border-radius: 50%;
          cursor: pointer;
        }

        & .name {
          font-weight: bold;
          font-size: 1.3em;
          color: black;
          padding: 0 5px;
          white-space: balance;
          text-align: center;
          width: 100%;
          flex: 1;
        }

        & .shop-detail {
          color: #818086;
          font-size: 0.8em;
          align-items: center;
          padding: 0px 15px;
          width: 100%;
          justify-content: center;
          white-space: nowrap;
          text-align: center;
          font-weight: 600;

          & > a {
            padding: 0 5px;
            color: black;
            width: 100%;
            white-space: break-spaces;
          }
          & em {
            font-style: normal;
            color: #595e63;
          }
        }
      }
    }

    & .other-detail {
      padding: 15px;
      filter: drop-shadow(0 0 2px rgb(0, 0, 0, 10%));

      & > :first-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
      & > :last-child {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }
      & > a {
        width: 100%;
      }
      & .other-detail-button {
        padding: 10px 15px 0;
        background: white;
        border: none;
        display: flex;
        width: 100%;
        border-radius: inherit;
        align-items: center;

        & > .icon {
          font-size: 1.3em;
          justify-content: center;
          color: #818086;
        }
        & > span {
          font-weight: bold;
          align-items: center;
          margin-right: auto;
        }
        & > .icon-right {
          font-size: 1.3em;
          justify-content: center;
        }
      }
      & > .other-detail-options {
        gap: 10px;
        padding: 10px;
        justify-content: center;
        align-items: flex-start;
        background-color: white;

        & > button {
          flex: 1;
          max-width: 150px;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border: none;
          gap: 5px;

          & > .button-icon {
            background-color: #fff6f8;
            width: 60%;
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color-1);
            border-radius: 10px;
            font-size: 25px;
            position: relative;

            & > em {
              border-radius: 2em;
              color: white;
              background: var(--primary-color-2);
              min-width: 25px;
              height: 25px;
              font-size: 13px;
              padding: 5px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-style: normal;
              position: absolute;
              top: -10px;
              right: -10px;
              line-height: 1;
              font-weight: 700;

              & > span {
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
          & > span {
            font-size: 15px;
            font-weight: 600;
          }
        }

        & > .other-option {
          flex: 1;
          position: relative;
          display: flex;
          align-items: center;
          border: none;
          gap: 5px;
          font-weight: 600;
          padding: 0 20px;
          margin: 10px 0;
          width: 100%;
          justify-content: space-between;
          flex-direction: row;
          font-size: 15px;

          & > .icon-right {
            font-size: 15px;
            justify-content: center;
            color: #818086;
          }
        }
      }
    }

    & .product-actions {
      justify-content: space-between;
      gap: 15px;
      padding: 0 15px;
      filter: drop-shadow(0 0 2px rgb(0, 0, 0, 10%));

      & > button {
        border-radius: 5px;
        flex: 1;
        padding: 10px 5px;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;

        & > svg {
          width: 20px;
          min-width: 20px;
        }
        & > span {
          font-size: 0.7em;
          font-weight: 600;
          white-space: balance;
        }
      }

      & > button.add-product {
        color: white;
        background-color: var(--primary-color-1);
      }
    }
  }
}

.add-product-option {
  color: var(--primary-color-1) !important;
  text-align: center !important;
  background: transparent !important;
  font-size: 1em;
  position: relative;
}

.no-product-modal {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  overflow: hidden;
  padding: 10px;
  // min-height: 40dvh;
  border-radius: 10px;
  background-color: white;
  .no-product-container {
    & > .message {
      padding: 15px 0 0;
      font-size: 20px;
      color: #545454;
      font-weight: 600;
      text-align: center;
    }

    & > .create-product-options {
      display: flex;
      justify-content: space-evenly;
      padding: 10px 0;

      & > a {
        width: 100px;
        height: 100px;
        border-radius: 15px;
        background-color: color-mix(
          in srgb,
          var(--primary-color-1) 10%,
          transparent
        );
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--primary-color-1);
        text-align: center;
        font-weight: 700;
        font-size: 15px;
      }
      & > a:hover {
        background-color: var(--primary-color-1);
        color: white;
      }
    }
    & > button {
      width: 200px;
      margin: auto;
      max-width: 100%;
      padding: 5px 15px;
      border-radius: 7px;
      color: var(--primary-color-2);
      background-color: color-mix(
        in srgb,
        var(--primary-color-2) 10%,
        transparent
      );
      border: thin solid;
    }
  }
}
