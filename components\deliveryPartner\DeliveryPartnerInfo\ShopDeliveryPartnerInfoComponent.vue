<template>
	<VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false" :overlay-behavior="'persist'"
		content-class="v-stack form-modal shop-delivery-partner-info-container" v-model="showShopDeliveryPartnerInfoModal"
		v-on:closed="() => {
			showShopDeliveryPartnerInfoModal = false
		}" contentTransition="vfm-fade">
		<HeaderComponent
			:title="shop_deli_part_connect_data?.id ? `${$t('ShopDeliveryPartnerInfoComponent.ket_noi_don_vi_van_chuyen')}` : $t('ShopDeliveryPartnerInfoComponent.ket_noi_don_vi_van_chuyen')">
			<template v-slot:header_left></template>
		</HeaderComponent>

		<h4 class="partner-info">{{ $t('ShopDeliveryPartnerInfoComponent.ket_noi_voi') }}:
			<img :src="props.delivery_partner_info.information.logo" alt=""
				v-if="props.delivery_partner_info.information.logo">
			<span v-else>{{ props.delivery_partner_info.name }}</span>
		</h4>

		<VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
			class="shop-delivery-partner-info-content">
			<div class="form-field-container">
				<label class="required" for="name">{{
					$t('ShopDeliveryPartnerInfoComponent.ten_dia_diem_nhan_hang') }}</label>
				<Field class="custom-input" :disabled="!is_editing" :validate-on-input="true" name="name"
					:placeholder="$t('ShopDeliveryPartnerInfoComponent.ten_dia_diem_nhan_hang')"
					v-bind:model-value="shop_deli_part_connect_data?.name" v-on:update:model-value="($event) => {
						shop_deli_part_connect_data.name = $event;
					}"></Field>
				<ErrorMessage class="error-message" name="name"></ErrorMessage>
			</div>
			<div class="form-field-container">
				<label class="required" for="name">{{ $t('ShopDeliveryPartnerInfoComponent.dia_chi') }}</label>
				<Field :placeholder="$t('ShopDeliveryPartnerInfoComponent.dia_chi')" :disabled="!is_editing"
					class="custom-input" :validate-on-input="true" name="address" type="text"
					v-bind:model-value="shop_deli_part_connect_data?.address" v-on:update:model-value="($event) => {
						shop_deli_part_connect_data.address = $event;
					}"></Field>
				<ErrorMessage class="error-message" name="address"></ErrorMessage>
			</div>
			<div class="form-field-container">
				<label class="required" for="name">{{ $t('ShopDeliveryPartnerInfoComponent.so_dien_thoai') }}</label>
				<Field :placeholder="$t('ShopDeliveryPartnerInfoComponent.so_dien_thoai')" :disabled="!is_editing"
					class="custom-input" :validate-on-input="true" name="phone" type="text" rules="phone"
					v-bind:model-value="shop_deli_part_connect_data?.phone" v-on:update:model-value="($event) => {
						shop_deli_part_connect_data.phone = $event;
					}"></Field>
				<ErrorMessage class="error-message" name="phone"></ErrorMessage>
			</div>
			<div class="form-field-container">
				<label class="required" for="name">{{ $t('ShopDeliveryPartnerInfoComponent.email') }}</label>
				<Field :placeholder="$t('ShopDeliveryPartnerInfoComponent.email')" :disabled="!is_editing"
					class="custom-input" :validate-on-input="true" name="email" type="email"
					v-bind:model-value="shop_deli_part_connect_data?.email" v-on:update:model-value="($event) => {
						shop_deli_part_connect_data.email = $event;
					}"></Field>
				<ErrorMessage class="error-message" name="email"></ErrorMessage>
			</div>
			<button hidden ref="submitFormButton" v-on:click="() => {
				submit()
			}"></button>
		</VeeForm>

		<div class="shop-delivery-partner-info-footer">
			<button class='reject-button' v-on:click="() => {
				close()
			}">
				{{ $t('ShopDeliveryPartnerInfoComponent.dong') }}
			</button>
			<button class='accept-button' :disabled="is_submitting || !formSchema.isValid" v-if="is_editing" v-on:click="() => {
				submitFormButton?.click();
				// submit(shop_deli_part_connect_data)
			}">
				{{ !shop_deli_part_detail?.id ? $t('ShopDeliveryPartnerInfoComponent.ket_noi') :
					$t('ShopDeliveryPartnerInfoComponent.luu') }}
			</button>
			<button class='accept-button' v-else v-on:click="() => {
				is_editing = true;
				// submit()
			}">
				{{ $t('ShopDeliveryPartnerInfoComponent.chinh_sua') }}
			</button>
		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency, validPhone } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { AuthService } from "~/services/authService/authService";
import { toast } from "vue3-toastify";

import delete_account from "~/assets/image/delete_account.jpg";
import { HttpStatusCode } from "axios";
import { DeliveryPartnerService } from "~/services/deliveryPartnerService/deliveryPartnerService";
import { ShopService } from "~/services/shopService/shopService";
import { AgentService } from "~/services/agentService/agentService";
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';

const { t } = useI18n();
const nuxtApp = useNuxtApp();
const props = defineProps({
	mode: null,
	delivery_partner_info: null,
	shop_id: null,
	shop_data:null,
	is_reconnect: null
});
const emit = defineEmits(['close']);

yup.addMethod(yup.string, 'phoneValidate', function () {
	return this.test(
		'phone-validate',
		t('ShopDeliveryPartnerInfoComponent.so_dien_thoai_khong_dung'),
		(value: any) => {
			if (!value) return true;
			const re = appConst.validateValue.phone;
			if (!re.test(validPhone(value)))
				return false;
			return true
		}
	)
})

const formSchema = yup.object({
	name: yup.string().required(t('ShopDeliveryPartnerInfoComponent.vui_long_nhap_ten_dia_diem_nhan_hang')),
	address: yup.string().required(t('ShopDeliveryPartnerInfoComponent.vui_long_nhap_dia_chi_nhan_hang')).max(5000),
	email: yup.string().email(t('ShopDeliveryPartnerInfoComponent.email_khong_dung_dinh_dang')).required(t('ShopDeliveryPartnerInfoComponent.vui_long_nhap_email')),
	phone: yup.string().required(t('ShopDeliveryPartnerInfoComponent.vui_long_nhap_so_dien_thoai')).test('is-phone', t('ShopDeliveryPartnerInfoComponent.so_dien_thoai_khong_dung'), (value: any) => {
		const re = appConst.validateValue.phone;
		return re.test(validPhone(value));
	})
});
const { handleSubmit } = useForm({
	validationSchema: formSchema,
});

var router = useRouter();
var route = useRoute();

var authService = new AuthService();
var shopService = new ShopService();
var agentService = new AgentService();
var deliverPartnerService = new DeliveryPartnerService();

var submitFormButton = ref<HTMLElement | undefined>();
var shop_id = ref<any>(props.shop_id ?? route.query?.shop_id ?? null);
var shop_deli_part_connect_data = ref<{
	name: string,
	address: string,
	phone: string,
	email: string
}>() as any;
var shop_deli_part_detail = ref<any>(null);
var shopData = ref<any>();

var showShopDeliveryPartnerInfoModal = ref(false);
var is_editing = ref(false);
var is_submitting = ref(false);

onMounted(() => {
	getShopInfo().then(() => {
		getShopDeliveryPartnerInfo().then((res: any) => {
			if (res.status == HttpStatusCode.Ok && res.body?.data != null) {
				shop_deli_part_detail.value = res.body.data;
				shop_deli_part_connect_data.value = res.body.data.connect_data ?? {};
				is_editing.value = props.is_reconnect ? true : false;
			}
			else if (res.status == HttpStatusCode.NotFound) {
				shop_deli_part_detail.value = null;
				shop_deli_part_connect_data.value = {
					name: shopData.value?.name ?? "",
					address: shopData.value?.address ?? "",
					email: shopData.value?.email ?? "",
					phone: shopData.value?.phone ?? "",
				}
				is_editing.value = true;
			}
			else {
				shop_deli_part_detail.value = null;
				shop_deli_part_connect_data.value = {
					name: shopData.value?.name ?? "",
					address: shopData.value?.address ?? "",
					email: shopData.value?.email ?? "",
					phone: shopData.value?.phone ?? "",
				}
				is_editing.value = true;
			}
			showShopDeliveryPartnerInfoModal.value = true;
		}).catch(() => {
			// close();
			toast.error(t('ShopDeliveryPartnerInfoComponent.co_loi_xay_ra'))
		});
	});

})

function close(updated = false) {
	emit('close', updated);
}

async function submit() {
	is_submitting.value = true;
	if (await formSchema.isValid(shop_deli_part_connect_data.value)) {
		let data$ = {
			shop_id: props.shop_id ?? shop_id.value,
			partner: props.delivery_partner_info?.name.toLowerCase().includes('remagan') ? 'remagan' : props.delivery_partner_info?.name.toLowerCase(),
			is_enabled: props.is_reconnect ? true : shop_deli_part_detail.value?.is_enabled,
			delivery_partner_id: props.delivery_partner_info?.id,
			connect_data: shop_deli_part_connect_data.value,
		}
		if (shop_deli_part_detail.value?.id) {
			deliverPartnerService.updateDeliveryPartnerInfo(data$).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					is_submitting.value = false;
					close(true);
				}
				else if (res.status == HttpStatusCode.Unauthorized) {
					toast.error(t('ShopDeliveryPartnerInfoComponent.ban_khong_co_quyen'));
					is_submitting.value = false;
				}
				else {
					toast.error(res.body?.message ?? t('ShopDeliveryPartnerInfoComponent.ket_noi_that_bai'));
					is_submitting.value = false;
				}
			}).catch((err) => {
				toast.error(t('ShopDeliveryPartnerInfoComponent.ket_noi_that_bai'));
				is_submitting.value = false;
			})
		}
		else {
			deliverPartnerService.addDeliveryPartnerToShop(data$).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					is_submitting.value = false;
					close(true);
				}
				else if (res.status == HttpStatusCode.Unauthorized) {
					toast.error(t('ShopDeliveryPartnerInfoComponent.ban_khong_co_quyen'));
					is_submitting.value = false;
				}
				else {
					toast.error(res.body?.message ?? t('ShopDeliveryPartnerInfoComponent.ket_noi_that_bai'));
					is_submitting.value = false;
				}
			}).catch((err) => {
				toast.error(t('ShopDeliveryPartnerInfoComponent.ket_noi_that_bai'));
				is_submitting.value = false;
			})
		}

	}
	else {
		is_submitting.value = false;
	}

}
function getShopInfo() {
	return new Promise((resolve) => {
		if(props.shop_data){
			resolve(props.shop_data);
			return;
		}
		else if (props.mode != 'agent') {
			getMyShop().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
		else {
			getShopDetail().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
	})
}

function getMyShop() {
	return new Promise((resolve) => {
		shopService.myShop().then(async res => {
			if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
				shopData.value = res?.body?.data;
			}
			else {
				shopData.value = null;
			}
			resolve(shopData.value);
		}).catch(() => {

			resolve(null);
		})
	})

}
function getShopDetail() {
	return new Promise((resolve) => {
		agentService.agentShopDetail(shop_id.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				shopData.value = res.body.data;
				useSeoMeta({
					title: `${t('AppRouteTitle.ManageDeliveryPartnerComponent')} - ${shopData.value?.name}`
				})
				resolve(shopData.value);
			}
			else if (res.status == HttpStatusCode.Unauthorized) {
				router.push({
					path: appRoute.LoginComponent,
					query: {
						redirect: JSON.stringify(route.fullPath)
					}
				})
				resolve(null);
			}
			else {
				toast.error(res.body?.message ?? t('ManageDeliveryPartnerComponent.khong_quyen_quan_ly'))
				// setTimeout(() => {
				// 	router.back();	
				// }, 2000);
				resolve(null);
			}
		}).catch(e => {
			toast.error(t('ManageDeliveryPartnerComponent.co_loi_xay_ra'))
			resolve(null);
			// setTimeout(() => {
			// 		router.back();	
			// 	}, 2000);
		})
	})
}

function getShopDeliveryPartnerInfo() {
	return new Promise((resolve) => {
		deliverPartnerService.getShopDeliveryPartnerInfo(props.shop_id, props.delivery_partner_info?.id).then((res) => {
			resolve(res);
		})
	})

}
</script>

<style lang="scss" src="./ShopDeliveryPartnerInfoStyles.scss"></style>