<script setup lang="ts">
import { appConst, domainImage } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import banner from '~/assets/image/remagan-banner-19_1.png';
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_shop from "~/assets/image/none-shop.jpg";
import { VueFinalModal } from 'vue-final-modal';
import CreatePrivateProductComponent from './createPrivateProduct/CreatePrivateProductComponent.vue';
import AddProductsFromSystemComponent from './addProductsFromSystem/AddProductsFromSystemComponent.vue';
import moment from 'moment';
import { HttpStatusCode } from 'axios';
import { AgentService } from '~/services/agentService/agentService';
import { shop_status } from './MyShopDTO';

const router = useRouter();
const route = useRoute();
const { t } = useI18n()

var authService = new AuthService();
var userService = new UserService();
var shopService = new ShopService();
var orderService = new OrderService();
var agentService = new AgentService();

var myShopData = useState<any>('my_shop', ()=>{});
var listShopManaged = useState<any>('list_shop_managed', () => {
	return [];
})
var isFocus = ref(false);
var countOrderByStatus = ref([] as any[]);
var profileData = ref();
var refreshing = ref(true);
var showCreateProductModal = ref(false);
var reShowCreateProductModal = useState(() => true);

var showSelectShopModal = ref(false);
var shop_id = ref<any>(null)
useSeoMeta({
	title: t('AppRouteTitle.MyShopComponent')
})
onUnmounted(async () => { });
onMounted(async () => {
	getProfileData()
	getMyShop();
	if(!listShopManaged.value?.length){
		getAgentShop();
	}
	
});
onBeforeMount(async () => {

})
async function getProfileData(){
	profileData.value = await authService.checkAuth();
}
function getMyShop() {
	refreshing.value = true;
	shopService.myShop().then(async res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
			refreshing.value = false;
			myShopData.value = res?.body?.data;
			if (myShopData.value && myShopData.value?.id) {
				shop_id.value = myShopData.value?.id;
				getMyShopCountOrderByStatus();
				if (reShowCreateProductModal.value) {
					showCreateProductModal.value = !myShopData.value?.products?.length
				}
			}
		}
		else {
			refreshing.value = false;
			myShopData.value = null
		}

	})
}

function getAgentShop() {
	agentService.listShopManage('', 100, 0).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listShopManaged.value = JSON.parse(JSON.stringify(res.body.data));
			if (myShopData.value?.id) {
				listShopManaged.value = [
					myShopData.value,
					...JSON.parse(JSON.stringify(res.body.data))
				];
			}

		}
		else {
			listShopManaged.value = [];
		}
	})
}

function getMyShopOrder() {
	orderService.orderByShopId(myShopData.value?.id, 0, 20, null).then(res => {
		if (res.status && res.status == HttpStatusCode.Ok) {
			let count = res.status && res.status == HttpStatusCode.Ok ? res.body.data.count : 0;
			myShopData.value = {
				...myShopData.value,
				orderCount: count
			}
		}

	})
}

function getMyShopCountOrderByStatus() {
	orderService.countOrderByStatus(myShopData.value?.id).then(async res => {
		countOrderByStatus.value = res.body.data
	})
}
function getCountOrderByStatus(status: number) {
	if (status == appConst.order_status.taken.value) {
		orderService.orderByShopId(
			myShopData.value?.id,
			0,
			5,
			appConst.order_status.taken.value,
			null,
			moment().format('YYYY-MM-DD'),
			moment().format('YYYY-MM-DD')
		).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				let count = res?.body?.data?.count;
				if (count < 1000) return count;
				if (count >= 1000) return '999+';
				return count;
			}
			return 0;
		});

	}
	else {
		let index = countOrderByStatus.value.findIndex(function (e: any) {
			return e.status == status
		})

		if (index != -1) {
			if (countOrderByStatus.value[index].count < 1000) return countOrderByStatus.value[index].count;
			if (countOrderByStatus.value[index].count >= 1000) return '999+';
		}
		return 0;
	}
	return 0;
}

function setSelectedShop(shop_id$: any) {
	if (shop_id$ != myShopData.value?.id) {
		let indexSelected = listShopManaged.value.findIndex(function (e: any) {
			return e.id == shop_id$;
		});
		if (indexSelected != -1) {
			router.push({
				path: appRoute.AgentShopDetailDashboardComponent.replaceAll(':id', listShopManaged.value?.[indexSelected]?.slug ?? listShopManaged.value?.[indexSelected]?.id)
			})
		}

	}
}
</script>
<template>
	<div class="public-container">
		<div class="my-shop-container" v-if="refreshing">
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ appRouteTitle.MyShopComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.MyShopComponent')">
				
			</SubHeaderV2Component>
			<div class='my-shop-detail-container'>
				<div class="primary-shop-info">
					<div class="primary-info-content skeleton">
						<v-skeleton-loader class="shop-banner"></v-skeleton-loader>

						<div class="shop-info-buttons">
							<v-skeleton-loader class="button-skeleton profile-info-button"></v-skeleton-loader>
							<v-skeleton-loader class="button-skeleton others-shop-button"></v-skeleton-loader>
						</div>
					</div>
				</div>
			</div>
			<!-- <v-skeleton-loader width="100%" height="150px"></v-skeleton-loader>
		<div class="h-stack" style="padding:10px; justify-content: space-between; width: 100%;">
			<div class='v-stack' style="width: 65%; height: 50px; gap: 5px;">
				<v-skeleton-loader width="50%" height="20px"></v-skeleton-loader>
				<v-skeleton-loader width="75%" height="20px"></v-skeleton-loader>
			</div>
			<v-skeleton-loader width="40px" height="40px" style="border-radius: 50%;"></v-skeleton-loader>
		</div>
		<div class="v-stack" style="margin-top: 10px; padding: 0 10px">
			<v-skeleton-loader width="100%" height="2em" style="margin-top: 10px;"></v-skeleton-loader>
			<v-skeleton-loader width="100%" height="2em" style="margin-top: 10px;"></v-skeleton-loader>
			<v-skeleton-loader width="100%" height="2em" style="margin-top: 10px;"></v-skeleton-loader>
			<v-skeleton-loader width="100%" height="2em" style="margin-top: 10px;"></v-skeleton-loader>
			<v-skeleton-loader width="100%" height="2em" style="margin-top: 10px;"></v-skeleton-loader>
		</div> -->
		</div>
		<div class='my-shop-container' v-else-if="myShopData && myShopData !== null">
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ appRouteTitle.MyShopComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<!-- <HeaderComponent :title="$t('AppRouteTitle.MyShopComponent')">
				<template v-slot:header_right>

				</template>
			</HeaderComponent> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.MyShopComponent')">
				<template v-slot:header_right>
					<button :title="t('ChatManageComponent.chon_cua_hang')" v-if="listShopManaged?.length" v-on:click="() => {
						if (listShopManaged?.length > 0) showSelectShopModal = true
					}">
						<Icon name="mdi:shop-settings-outline"></Icon>
					</button>
				</template>
			</SubHeaderV2Component>
			<div class='my-shop-detail-container'>

				<div class="primary-shop-info">
					<div class="primary-info-content">
						<!-- <div class="shop-banner">
			<img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt="" v-if="!myShopData.logo && !myShopData.banner"/>
			<div class="logo-origin-container" :class="{'none-style' : !(myShopData.logo?.style?.length)}" v-else>
				<img
				:src=" myShopData.logo ? (domainImage + myShopData.logo.path) : shop_logo"
				:placeholder="shop_logo"
				v-on:click="()=>{
					router.push(appRoute.DetailShopComponent + '/' + myShopData.slug)
				}"
				:style="{
					transform: (myShopData.logo && myShopData.logo.style) ? myShopData.logo.style : 'none'
				}"
				alt="shop logo" />
			</div>
		</div> -->
						<AvatarComponent class="shop-banner" :imgTitle="myShopData?.name"
							:imgStyle="myShopData?.logo?.style" :imgSrc="myShopData?.logo
								? (domainImage + myShopData?.logo?.path)
								: myShopData?.banner
									? (domainImage + myShopData?.banner?.path)
									: ''
								" :width="90" :height="90" v-on:img_click="() => {
									router.push(appRoute.DetailShopComponent + '/' + myShopData?.slug)
								}" />

						<span class="name">
							{{ myShopData ? myShopData?.name : $t('MyShopComponent.chua_co_ten') }}
						</span>
						<div class="v-stack shop-detail">
							<nuxt-link :to="{ path: appRoute.DetailShopComponent + '/' + myShopData?.slug }"
								class="first">
								<span>
									{{ myShopData?.address }}
								</span>
							</nuxt-link>
							<nuxt-link class="last">
								<span>
									{{
										myShopData?.phone?.length ? myShopData?.phone : $t('MyShopComponent.chua_cap_sdt')
									}}
								</span>
							</nuxt-link>
						</div>
						<span class="waiting-confirm-shop" v-if="myShopData.status == shop_status.in_process">{{ $t('MyShopComponent.cua_hang_dang_cho_duyet')
							}}</span>
						<span class="blocked-shop" v-else-if="!myShopData.enable">{{ $t('MyShopComponent.cua_hang_bi_khoa')
							}}</span>
						<!-- <nuxt-link :to="{ path: appRoute.ShopProductsComponent }">
			<span class="shop-detail">
				{{
					myShopData.products.length
				}} <em> {{ $t('MyShopComponent.san_pham') }} </em>
			</span>
		</nuxt-link> -->
						<div class="shop-info-buttons">
							<button class="profile-info-button" v-on:click="() => {
								router.push(appRoute.ProfileComponent)
							}">
								<Icon name="ph:user-circle-light" size="30px"></Icon> {{
									$t('MyShopComponent.trang_ca_nhan')
								}}
							</button>
							<button class="others-shop-button" v-on:click="() => {
								router.push(appRoute.DetailShopComponent + '/' + myShopData?.slug)
							}">
								<Icon name="emojione:department-store"></Icon>
								<span>{{ $t('MyShopComponent.che_do_khach') }}</span>
								<!-- <Icon name="tabler:switch-vertical" class="right" /> -->
							</button>
						</div>
					</div>
				</div>

				<div class='v-stack other-detail shop-orders'>
					<nuxt-link :to="{
						path: appRoute.ManageOrdersComponent
					}">
						<div class='other-detail-button'>
							<span>
								{{ $t('MyShopComponent.don_hang_cua_shop') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</div>
					</nuxt-link>
					<div class='h-stack other-detail-options'>
						<button v-on:click="() => {
							router.push({
								path: appRoute.ManageOrdersComponent,
								hash: '#waiting',
								state: {
									tabIndex: 'waiting'
								}
							})
						}">
							<div class="button-icon">
								<Icon name="material-symbols:add-task-rounded"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.waiting.value)">
									{{ getCountOrderByStatus(appConst.order_status.waiting.value) }}
								</em>
							</div>
							<span>
								{{ $t('MyShopComponent.cho_xac_nhan') }}
							</span>
						</button>
						<button v-on:click="() => {
							router.push({
								path: appRoute.ManageOrdersComponent,
								state: {
									tabIndex: 'confirmed'
								},
								hash: '#confirmed'
							})
						}">
							<div class="button-icon">
								<Icon name="material-symbols:box-outline"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.confirmed.value)">
									{{ getCountOrderByStatus(appConst.order_status.confirmed.value) }}
								</em>
							</div>
							<span>
								{{ $t('MyShopComponent.dang_xu_ly') }}
							</span>
						</button>
						<!-- <button v-on:click="() => {
			router.push({
				path: appRoute.ManageOrdersComponent,
				hash: '#confirmed',
				state: {
					tabIndex: 'confirmed'
				}
			})
		}">
			<div class="button-icon">
				<Icon name="mdi:truck-fast-outline"></Icon>
				<em>
					{{ getCountOrderByStatus(appConst.order_status.confirmed.value) }}
				</em>
			</div>
			<span>
				{{ $t('MyShopComponent.dang_van_chuyen') }}
			</span>
		</button> -->
						<button v-on:click="() => {
							router.push({
								path: appRoute.ManageOrdersComponent,
								hash: '#taken',
								state: {
									tabIndex: 'taken'
								}
							})
						}">
							<div class="button-icon">
								<Icon name="fluent:box-checkmark-24-regular"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.taken.value)">
									{{ getCountOrderByStatus(appConst.order_status.taken.value) }}
								</em>
							</div>
							<span>
								{{ $t('MyShopComponent.da_giao_hom_nay') }}
							</span>
						</button>
					</div>
				</div>
				<div class='h-stack product-actions' v-if="false">
					<v-menu class="bootstrap-dropdown-container" location="bottom left">
						<template v-slot:activator="{ props }">
							<button class="add-product" v-bind="props">
								<Icon name="clarity:plus-circle-solid"></Icon>
								<span>
									{{ $t('MyShopComponent.dang_san_pham_moi') }}
								</span>
							</button>
						</template>

						<v-list>
							<v-list-item key="add_neww" class="add-product-option" v-on:click="() => {
								// showCreatePrivateProductModal = true 
								router.push({
									path: appRoute.CreatePrivateProductComponent,
								})
							}">
								<v-list-item-title>{{ $t('MyShopComponent.dang_moi') }}</v-list-item-title>
							</v-list-item>
							<v-list-item key="add-from-system" class="add-product-option" v-on:click="() => {
								// showAddProductsFromSystemModal = true
								router.push({
									path: appRoute.AddProductsFromSystemComponent,
								})
							}">
								<v-list-item-title>{{ $t('MyShopComponent.them_tu_he_thong') }}</v-list-item-title>
							</v-list-item>
						</v-list>
					</v-menu>
					<button v-on:click="() => {
						router.push({
							path: appRoute.MyShopManageComponent,
							state: {
								tabIndex: 'products'
							}
						})
					}">
						<Icon name="material-symbols:checklist"></Icon>
						<span>
							{{ $t('MyShopComponent.toan_bo_san_pham') }}
						</span>
					</button>
				</div>

				<div class='v-stack other-detail my-orders'>
					<nuxt-link>
						<div class='other-detail-button'>
							<span>
								{{ $t('MyShopComponent.san_pham') }}
							</span>
							<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
						</div>
					</nuxt-link>
					<div class='v-stack other-detail-options'>
						<nuxt-link :to="appRoute.MyShopManageComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.quan_ly_san_pham') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
					</div>
				</div>

				<div class='v-stack other-detail my-orders'>
					<nuxt-link>
						<div class='other-detail-button'>
							<span>
								{{ $t('MyShopComponent.cua_hang') }}
							</span>
							<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
						</div>
					</nuxt-link>
					<div class='v-stack other-detail-options'>
						<nuxt-link :to="appRoute.DetailShopComponent + '/' + myShopData?.slug" class="other-option">
							<span>
								{{ $t('MyShopComponent.thong_tin_shop') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.EditShopInfoComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.sua_thong_tin') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.MyShopChatManageComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.tin_nhan') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.StockManagementComponent" class="other-option">
							<span>
								{{ $t('StockManagement.quan_ly_kho') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.MyShopManageDeliverPartnerComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.don_vi_van_chuyen') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.MyShopConfigComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.cau_hinh') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>

						<nuxt-link :to="appRoute.QuotationComponent" class="other-option">
							<span>
								{{ $t('MyShopComponent.bao_gia') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
					</div>
				</div>

			</div>

			<VueFinalModal class="my-modal-container" content-class="v-stack no-product-modal" :overlay-behavior="'persist'"
				v-model="showCreateProductModal" v-on:closed="() => {
					showCreateProductModal = false;
					reShowCreateProductModal = false
				}" contentTransition="vfm-slide-up">
				<div class='v-stack no-product-container'>
					<p class='message'>
						{{ $t('MyShopComponent.cua_hang_chua_co_san_pham') }}
					</p>

					<div class="create-product-options">
						<nuxt-link :to="appRoute.AddProductsFromSystemComponent">
							<span>{{ $t('MyShopComponent.them_tu_he_thong') }}</span>
						</nuxt-link>
						<nuxt-link :to="appRoute.CreatePrivateProductComponent">
							<span>{{ $t('MyShopComponent.tao_moi') }}</span>
						</nuxt-link>
					</div>
					<button v-on:click="() => {
						showCreateProductModal = false
					}">
						<span>{{ $t('MyShopComponent.de_sau') }}</span>
					</button>
				</div>
			</VueFinalModal>
		</div>
		<NoneMyShopComponent v-else :show_header="true" :title="$t('MyShopComponent.ban_chua_co_cua_hang')"></NoneMyShopComponent>
		<CustomSelectComponent v-if="showSelectShopModal" :_key="'select_shop_chat_manage'"
			:list_item="listShopManaged ?? []" :field_value="'id'" :field_title="'name'" :multiple="false"
			:title="$t('ChatManageComponent.chon_cua_hang')" :class="'my-custom-select custom-shop-select'"
			:searchable="false" :model_value="shop_id" v-on:close="() => {
				showSelectShopModal = false
			}" v-on:model:update="(e) => {
				setSelectedShop(e)
			}">
			<template v-slot:placeholder>
				<div class="h-stack">
					<span>{{ $t('RegisterShopComponent.thiet_lap') }}</span>
					<Icon name="mdi:chevron-down"></Icon>
				</div>
			</template>
			<template v-slot:title_icon_left>
				<Icon name="solar:hamburger-menu-linear"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<div class="custom-shop-select-option">
					<AvatarComponent class="select-shop-logo" :imgTitle="item.name" :imgStyle="item.logo?.style"
						:imgSrc="item.logo
							? (domainImage + item.logo.path)
							: item.banner
								? (domainImage + item.banner.path)
								: ''
							" :width="40" :height="40" />
					<span>{{ item.name }}
						<em v-if="item.user_id == profileData?.id" class="owner">{{ $t('CustomSelectComponent.ban_la_chu_shop') }}</em>
					</span>
				</div>

			</template>
		</CustomSelectComponent>
	</div>

</template>

<style lang="scss" src="./MyShopStyles.scss"></style>