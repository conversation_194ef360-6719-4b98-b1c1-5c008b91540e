@media screen and (max-width: 720px) {
  .product-item-container-grid > img {
    height: auto;
  }

  .comment-container .comment-image-item{
    width: calc(25% - 5px) !important;
    min-width: unset !important;
    min-height: unset !important;
  }
}

.loading-skeleton,
.product-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  background-color: #f6f5fb;
  position: relative;
  // font-size: calc(var(--font-size) * 1.3);
  overflow: hidden auto;
  padding-bottom: 50px;

  // & img {
  //   height: 300px;
  //   min-height: 300px;
  //   width: 100%;
  //   margin: 0 auto;
  //   object-fit: contain;
  // }

  // & .product-detail {
  //   padding: 0 15px;
  //   flex: 1;
  //   overflow-y: auto;
  //   overflow-x: hidden;
  // }

  // & .product-detail > .product-shop-detail {
  //   gap: 5px;
  //   color: var(--color-button-black);
  //   font-size: 0.9em;
  //   cursor: pointer;
  //   align-items: baseline;
  //   padding: 5px 0;
  // }

  // & .product-detail > .product-shop-detail span {
  //   color: var(--color-button-black);
  // }

  // & .product-detail > .product-type-detail {
  //   gap: 5px;
  //   color: var(--primary-color-1);
  //   font-weight: 500;
  //   flex-wrap: wrap;
  //   font-size: 0.9em;

  //   & > span {
  //     white-space: nowrap;
  //   }
  // }

  // & .product-detail > .product-name {
  //   font-size: 1em;
  //   font-weight: 500;
  //   color: var(--color-button-special);
  // }

  // & .product-detail > .product-notes {
  //   font-size: 0.9em;
  //   font-weight: 400;
  //   font-style: italic;
  //   color: var(--color-text-note);
  // }

  // & .product-detail > .children-products {
  //   display: flex;
  //   gap: 10px;
  //   flex-wrap: wrap;
  //   margin-top: 10px;

  //   & > .item-child-product {
  //     padding: 5px 20px;
  //     color: var(--color-button-black);
  //     border: 2px solid var(--primary-color-1);
  //     border-radius: 2em;
  //   }
  //   & > .item-child-product.active {
  //     color: white;
  //     background: var(--primary-color-1);
  //   }
  // }

  // & .product-detail .product-price {
  //   font-weight: 600;
  //   color: #e18a06;
  //   font-size: 0.9em;

  //   & > .off {
  //     color: var(--color-text-note);
  //     font-style: normal;
  //     text-decoration: line-through;
  //   }
  // }

  // & .price-and-quantity {
  //   justify-content: space-between;
  //   margin-top: 5px;
  // }

  // & .quantity-button {
  //   background: var(--primary-color-1);
  //   color: white;
  //   border: none;
  //   border-radius: 50%;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   width: 35px;
  //   height: 35px;
  //   padding: 0;
  //   font-size: 1.3em;
  // }

  // & .quantity-button:disabled {
  //   background-color: var(--color-text-note);
  // }

  // & .price-input {
  //   border: thin solid var(--color-text-note);
  //   border-width: 0 0 1px 0;
  //   width: 50px;
  //   text-align: center;
  //   font-weight: 600;
  //   margin: 0 5px;
  //   outline: none;
  //   appearance: none !important;
  //   background-color: white;
  //   color: #2f3640;
  // }

  & > .product-profile-picture {
    width: 100dvw;
    max-width: 100%;
    height: 100dvw;
    // min-height: 250px;
    max-height: 480px;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    & > img {
      object-fit: contain;
      height: 100%;
      max-height: inherit;
      width: inherit;
      max-width: inherit;
    }

    

    & .product-images-carousel{
      width: 100%;
      height: 100%;
  
      & img{
        min-height: unset;
        max-height: unset;
      }
    }
  }
  & > .title-header {
    position: absolute;
    left: 0;
    justify-content: space-between;
    top: 75px;
    width: 100%;
    background: transparent;
    padding: 0 15px;
    border: none;

    & > .header-left {
      flex: none;
      margin-right: 10px;
      > button {
        background-color: rgb(0, 0, 0, 70%);
        width: 30px;
        height: 30px;
        color: white;
        padding: 5px;
      }
    }

    & > .header-right {
      flex: none;
      margin-left: 10px;
      font-size: 1em;
      gap: 15px;
      position: relative;

      & > .cart-in-search {
        animation: none;
        width: 30px;
        height: 30px;
        color: white;
        position: relative;
        padding: 5px;
        background-color: rgb(0, 0, 0, 70%);

        & > em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 15px;
          width: fit-content;
          height: 15px;
          padding: 2px;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          position: absolute;
          bottom: -3px;
          right: -3px;
          line-height: 1;
          font-weight: 500;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .share-button {
        animation: none;
        width: 30px;
        height: 30px;
        font-size: 20px;
        color: white;
        position: relative;
        padding: 5px;
        background-color: rgb(0, 0, 0, 70%);
      }
    }
  }
  & > .product-detail-stack {
    display: flex;
    padding: 10px 15px;
    flex-direction: column;
    background-color: white;

    & > .stack-title {
      display: flex;
      justify-content: space-between;
    }

    & .label {
      font-size: 16px;
      font-weight: bold;
      color: black;
    }

    & > .rating-overview {
      display: flex;
      align-items: center;
      padding: 10px 0;

      & > .comment {
        margin-left: 15px;
        padding-left: 15px;
        border-left: thin solid #7e7d83;
        line-height: 1;
      }
    }
    & .rating {
      display: flex;
      font-weight: 500;
      color: #28262c;
      font-size: 25px;

      & > svg {
        color: #ffc107;
        font-size: 35px;
        align-self: center;
        margin-top: -1%;
      }
    }

    & > .comment-container {
      display: flex;
      flex-direction: column;

      & > .comment-item-container:first-child {
        border-top: thin solid #ececec;
      }
      & > .comment-item-container {
        display: flex;
        flex-direction: column;
        padding: 5px 0 15px 0;
        border-bottom: thin solid #ececec;
        // gap: 5px;
        & > .rate {
          display: flex;
          gap: 10px;
          align-items: baseline;
          font-weight: 600;
          color: black;
          font-size: 20px;
        }

        & > .comment-detail {
          color: #1a1a1a;
          font-weight: 500;
          line-height: normal;
          white-space: break-spaces;
        }

        & > .comment-images {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          margin: 5px 0;

          & > .comment-image-item {
            width: calc(20% - 5px);
            aspect-ratio: 1;
            min-width: 100px;
            min-height: 100px;
            border-radius: 10px;
            border: thin solid #e7e9ec;
            color: #e7e9ec;
            font-size: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
            position: relative;
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: inherit;
            }
          }
        }

        & > .user-info {
          display: flex;
          gap: 10px;
          align-items: center;
          color: #7e7d83;

          & img {
            width: 25px;
            height: 25px;
            object-fit: cover;
            border-radius: 50%;
          }
        }
      }
    }

    & .add-comment {
      color: var(--primary-color-1);
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    & .rate-loadmore{
      margin: 10px 0 0;
      font-style: italic;
      color: var(--primary-color-1);
      text-align: center;
    }
    & .show-all-rating{
      margin: 10px 0 0;
      font-style: italic;
      color: var(--color-text-note);
      text-align: center;
    }
  }

  & > .product-detail-stack.primary-detail {
    padding: 15px;
    flex-direction: column;

    & > .product-name {
      font-size: 17px;
      font-weight: bold;
      color: #262628;
    }

    & > .rating-sold-amount {
      font-size: 15px;
      display: flex;
      align-items: flex-end;
      color: #7e7d83;
      & > div:first-child {
        padding-left: 0;
        border-left: none;
      }
      & > div:last-child {
        border-right: none;
        padding-right: 0;
      }
      & > div {
        padding: 0 10px;
        margin: 10px 0;
        height: 1em;
        display: flex;
        align-items: center;
        border-left: thin solid #7e7d83;
      }

      & > .rating {
        display: flex;
        font-weight: bold;
        color: #595959;

        & > svg {
          color: #ffc107;
          font-size: 25px;
          align-self: flex-end;
          margin-bottom: -2px;
          margin-left: -5px;
        }

        & span {
          font-size: 0.7em;
          margin-top: 3px;
        }
      }
    }

    & > .flash-sale {
      display: flex;
      justify-content: space-between;
      text-transform: uppercase;
      color: #7e7d83;

      & > .flash-sale-label {
        & > svg {
          color: #ffc107;
        }
      }

      & > .expire-time {
        & > .count-down {
          font-weight: bold;
          color: #606061;
        }
      }
    }

    & > .product-price-container {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      & > .price-content {
        display: flex;
        gap: 15px;
        color: #29292c;

        & > .product-price {
          font-size: 25px;
          font-weight: bold;
          display: flex;
          gap: 15px;
          align-items: center;

          & > em {
            font-size: 13px;
            padding: 0 5px;
            font-style: normal;
            font-weight: 600;
            border-radius: 5px;
            background: #f6f5fb;
            text-decoration: line-through;
          }
        }
      }

      & > .sale-off-badge {
        color: white;
        font-weight: bold;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: auto;
        font-size: 15px;
        width: 50px;
        height: 40px;
        background-image: url("~/assets/image/sale_off_badge_background.png");
        background-size: 100%;
      }
    }

    & > .select-child-product {
      padding: 10px 15px;
      border: thin solid #eeedf2;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0 0;
      position: relative;

      & > .selection-show {
        display: flex;
        flex-direction: column;

        & > .label {
          color: #818082;
          font-size: 15px;
          font-weight: 600;
        }

        & > .child-product-name {
          color: #262628;
          font-size: 17px;
          font-weight: bold;
        }
      }

      & > .select-child-product-button {
        color: var(--primary-color-1);
        font-size: 15px;
        font-weight: 600;
      }

      & > .children-products-select-container {
        & .dropdown-content {
          width: 100%;
          max-width: 100% !important;
          left: 0 !important;
          top: 100% !important;

          & .children-products {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
            background: white;
            box-shadow: 0 0 20px rgb(0, 0, 0, 10%);
            padding: 10px 15px;
            border-radius: 15px;

            & > .item-child-product {
              padding: 5px 20px;
              color: var(--primary-color-1);
              border: thin solid var(--primary-color-1);
              border-radius: 2em;
            }
            & > .item-child-product.active {
              color: white;
              background: var(--primary-color-1);
            }
          }
        }
      }
    }
  }

  & > .list-notes-container {
    & > .list-notes {
      display: flex;
      flex-direction: column;
      & > .note-item {
        display: flex;
        gap: 5px;
        align-items: flex-start;
        color: #202020;
        font-weight: 500;

        & > svg {
          color: var(--primary-color-1);
          margin-top: 5px;
        }
      }
    }
  }

  & > .stack-space {
    width: 100%;
    height: 10px;
    min-height: 10px;
    background-color: #f6f5fb;
  }

  & > .product-detail-stack.add-to-cart-stack {
    display: flex;
    flex-direction: row;
    gap: 15px;
    position: sticky;
    bottom: 0;
    left: 0;
    flex-wrap: wrap;
    background: white;

    & > button {
      padding: 0 10px;
      border-radius: 10px;
      height: 40px;
      font-weight: 600;
      width: fit-content;
      border: thin solid #7e7d83;
    }

    & > button.message {
      color: #7e7d83;
      width: 40px;
      font-size: 27px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
    }

    & > button.add-to-cart {
      background: white;
      border: thin solid #ed1b24;
      color: #ed1b24;
      border-radius: 2em;
      font-size: 15px;
      padding: 5px 15px;
      font-weight: bold;
      margin: auto;
      width: 55%;
    }

    & button.quick-add-to-cart {
      background-color: #e00e72;
      color: white;
      border-color: #e00e72;
      flex: 1;
    }

    & > .note-input {
      width: 100%;
      background: #f5f6fb;
      color: #202020;
      padding: 10px;
      outline: none;
      border-radius: 5px;
    }
  }

  & > .product-detail-stack.vouchers {
    gap: 5px;

    & > .list-voucher {
      display: flex;
      flex-wrap: wrap;
      gap: 10px 15px;

      & > button {
        color: #e00e72;
        padding: 5px 10px;
        border: thin solid #efedf2;
        border-radius: 10px;
      }
    }
  }

  & > .product-detail-stack.shop-info-container {
    & > .shop-info-content {
      display: flex;
      gap: 5px;

      & > .shop-name-address {
        display: flex;
        flex-direction: column;

        & > .name {
          color: rgb(40, 40, 42);
          font-weight: bold;
          font-size: 15px;
        }

        & > .address {
          font-size: 13.5px;
          font-weight: 600;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      & > a > button {
        padding: 0px 10px;
        color: #28282a;
        border: thin solid #7d7c82;
        border-radius: 5px;
        white-space: nowrap;
        height: fit-content;
      }
    }

    & > .shop-preview-content {
      display: flex;
      justify-content: space-evenly;
      padding: 10px 0;
      align-items: flex-start;
      background: #f5f6fb;
      border-radius: 10px;
      margin-top: 5px;

      & > div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;

        & > .label {
          font-size: 15px;
          color: #838287;
          text-align: center;
          font-weight: 500;
        }
        & > .value {
          text-align: center;
          font-size: 17px;
          color: #202020;
          font-weight: bold;

          & > em {
            font-style: normal;
            font-size: 13px;
          }
        }
      }
    }
  }

  & > .product-detail-stack.categories-container {
    display: flex;
    gap: 10px;

    & > .stack-content {
      display: flex;
      flex-direction: column;
      gap: 10px;

      & > .stack-content-item {
        display: flex;
        justify-content: space-between;

        & > .label {
          flex: 0.4;
          font-size: 15px;
          color: #8b8b8d;
          font-weight: 500;
          padding-right: 10px;
        }

        & > .content {
          font-weight: 600;
          color: #222222;
          flex: 0.6;
        }
      }

      & > .stack-content-item.category > .content {
        color: var(--primary-color-1);
      }
    }
  }

  & > .product-detail-stack.description {
    gap: 5px;
    & > .description-container {
      position: relative;
      // margin-top: 10px;
      color: #202020;
      height: fit-content;
      font-size: 17px;
      line-height: 20px;
      max-height: 200px;
      overflow: hidden;
      transition: max-height 0.2s ease-in-out;

      & > span {
        height: fit-content;
        min-height: fit-content;
        padding-bottom: 10px;
        white-space: break-spaces;
      }

      & > .show-full-button {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, transparent, white);
        display: flex;
        align-items: flex-end;
        justify-content: center;

        & > button {
          color: var(--primary-color-1);
        }
      }
    }

    & > .description-container.show-full {
      max-height: 100%;
      height: fit-content;

      & > .show-full-button {
        position: relative;
        height: fit-content;
      }
    }
  }

  & > .product-detail-stack.stack-similar {
    font-size: 15px;
    gap: 10px;

    & .product-item-container-grid {
      background: white;
      border-radius: 10px;
      padding: 5px;
      display: flex;
      flex-direction: column;
      margin-bottom: 5px;
      cursor: pointer;
      border: thin solid transparent;
      // width: 100%;
      // gap: 5px;
      // display: flex;
      // margin-bottom: 5px;
      // cursor: pointer;
      // padding: 5px 5px 0;
      // border: thin solid transparent;

      & > img {
        align-self: flex-start;
        width: 100%;
        max-height: 150px;
        height: 150px;
        aspect-ratio: 1;
        object-fit: cover;
        background: var(--color-background-2);
        border-radius: 10px;
      }

      & > .product-detail {
        align-items: flex-start;
        gap: 5px;
        flex: 1;
        padding: 5px;
        text-align: left;

        & > .product-name {
          color: #242328;
          font-weight: bold;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          overflow: hidden;
        }

        & > .h-stack {
          width: 100%;
          margin-top: auto;
          flex-wrap: wrap;
          align-items: flex-end;
          & > .product-price {
            color: #ed1b24;
            font-weight: bold;
            line-height: 1.125;
            font-size: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;

            & > em.off {
              font-size: 0.8em;
              font-style: normal;
              display: flex;
              font-weight: 500;
              color: var(--color-text-note);
              text-decoration: line-through;
            }
          }

          & > .sale-off-badge {
            color: white;
            font-weight: bold;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: auto;
            font-size: 0.8em;
            width: 40px;
            height: 30px;
            background-image: url("~/assets/image/sale_off_badge_background.png");
            background-size: 100% 100%;
          }
        }
        & .rating-sold-amount {
          font-size: 0.9em;
          display: flex;
          align-items: flex-end;
          color: #7e7d83;
          & > div:first-child {
            padding-left: 0;
          }
          & > div:last-child {
            border: none;
            padding-right: 0;
          }
          & > div {
            padding: 0 10px;
            margin: 10px 0;
            height: 1em;
            display: flex;
            align-items: center;
            border-right: thin solid #7e7d83;
          }

          & > .rating {
            display: flex;
            font-weight: bold;
            color: #595959;

            & > svg {
              color: #ffc107;
              font-size: 25px;
              align-self: flex-end;
              margin-bottom: -2px;
              margin-left: -5px;
            }

            & span {
              font-size: 0.7em;
              margin-top: 3px;
            }
          }
        }
      }
    }

    & .product-item-container-grid:hover,
    .product-item-container-grid:active {
      background: linear-gradient(#fff7f8, #fff7f8) padding-box,
        linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;

      & > .product-detail {
        border-color: transparent;
      }
    }
  }
}
.share-dropdown-content > div {
  background: rgb(0, 0, 0, 80%) !important;
  border-radius: 15px !important;
}
.share-dropdown-item {
  color: white !important;
  text-align: center !important;
  background: transparent !important;
  font-size: 1em;
  position: relative;

  & .custom-share-button {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  & .custom-share-button:hover {
    opacity: calc(var(--v-hover-opacity) * var(--v-theme-overlay-multiplier));
  }
}
.zalo-share-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  opacity: 0;
}

.related-product-carousel .swiper-button-prev,
.related-product-carousel > .swiper-button-next {
  top: 0%;
}
