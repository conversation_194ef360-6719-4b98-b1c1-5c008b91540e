<script setup lang="ts">
import { channel_type, member_type, type ChannelDTO } from "~/components/chatManage/ChatDTO";
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber, nonAccentVietnamese, showTranslateProductName, showTranslateProductDescription, validPhone } from '~/assets/AppConst';
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import banner_temp from '~/assets/image/remagan-banner-19_1.png';
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import non_avatar from '~/assets/image/non-avatar.jpg';
import list_empty from "~/assets/image/list-empty-2.jpg"
import icon_for_product from '~/assets/image/icon-for-product.png'
import { VueFinalModal } from 'vue-final-modal';
import EditShopInfoComponent from '../myShop/editShopInfoComponent/EditShopInfoComponent.vue';
import ResetCartComponent from '../resetCart/ResetCartComponent.vue';
import Confirm18AgeComponent from '../confirm18Age/Confirm18AgeComponent.vue';
import EditProductComponent from '../myShop/editProduct/EditProductComponent.vue';
import DirectionComponent from './direction/DirectionComponent.vue';
import { AgentService } from '~/services/agentService/agentService';
import environment from '~/assets/environment/environment';
import { settings } from 'firebase/analytics';

const nuxtApp = useNuxtApp();
const { t } = useI18n()
var props = defineProps({
	mode: null
})
var router = useRouter();
var route = useRoute();
const dataId = props.mode != 'agent' ? route.params.slug : route.params.id;
// var shopGet = await fetch(appConst.apiURL.detailShop + '/' + dataId);
var shopDetails = ref(null as any);
var shopGet: any = await useLazyFetch(appConst.apiURL.detailShop + '/' + dataId, {
	key: `shop_detail_${dataId}`,
	headers: {
		Origin: `https://${environment.domain}`
	},
	getCachedData: (key) => {
		// Check if the data is already cached in the Nuxt payload
		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
			return nuxtApp.payload.data[key]
		}

		// Check if the data is already cached in the static data
		if (nuxtApp.static.data[key]) {
			return nuxtApp.static.data[key]
		}

		return null
	}
});
if (shopGet?.data?.value?.status == 200) {
	shopDetails.value = await JSON.parse(JSON.stringify(shopGet.data.value.body.data));
	// shopData.value = shopDetails;
	// dataShopProducts.value = shopDetails.products;
	let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
	useServerSeoMeta({
		ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
		title: () => `${shopDetails.value.name} | Rẻ mà gần`,
		description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogUrl: () => domain + route.fullPath,
		ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
		ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
	});
}

var shopData = useState(() => { return null as any });
var dataShopProducts = ref([] as any);
var dataShopProductsSaleOff = ref([] as any[]);

var showSelectedProduct = ref(false);


var authService = new AuthService();
var userService = new UserService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var bannerElement: any;

var dataFilterSort = [
	{
		title: "Giá từ thấp đến cao",
		value: 1
	},
	{
		title: "Giá từ cao đến thấp",
		value: 2
	},
	{
		title: "Mới nhất",
		value: 3
	},
	{
		title: "Xếp theo tên a-z",
		value: 4
	},
	{
		title: "Xếp theo tên z-a",
		value: 5
	},
	{
		title: "Đặc trưng",
		value: 6
	}
];
var loadMoreTimeOut: any;
var searchProductTimeout: any;

var userProfile = ref(null as any);
var refreshing = ref(true);

var countProducts = ref(0);
var shop_id = ref((route.params && props.mode != 'agent' ? (route.params.slug ? route.params.slug : null) : (route.params.id ? route.params.id : null)) as any);
var showMode = ref(1);
var productSortBy = ref(6);
var cartData = ref([] as CartDto[]);
var loadMore = ref(false);
var filterProductLoading = ref(false);
var selectedProduct = ref(null as any);
var showEditProductModal = ref(false);
var showEditShopInfoModal = ref(false);
var dataShopCategories = useState(`data_shop_categories_${shop_id.value}`, () => { return [] as any[] });
var dataShopCategoriesBackup = useState(`data_shop_categories_backup_${shop_id.value}`, () => { return [] as any[] });
var search_text = useState(`search_text_${shop_id.value}`, () => { return "" });
var filterCategory = ref(null);

var showDescription = ref(true);
// var showFullDescription = ref(false);
// var checkShowFullable = ref(false);
var showDirection = ref(false);
var webInApp = ref(null as any);

var selectingCategory = ref(false);
var categoryCarousel = ref();

var enableScrollTopBtn = ref(false);
var baseDescription = ref("");

var scrollPosition = useState(() => { return 0 as any });

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);
var open_time_list = ref([] as any);

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();
onUnmounted(async () => {
	nuxtApp.$unsubscribe(appConst.event_key.shop_updated)
});

onMounted(async () => {

	refreshing.value = true;
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartNumber();
	})
	nuxtApp.$listen(appConst.event_key.shop_updated, (e) => {
		console.log("shop update")
		shopData.value = null;
		if (props.mode != 'agent') {
			getDetailShop();
		}
		else {
			agentGetDetailShop()
		}
	})
	init();
});
onBeforeMount(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	let userProfileRef = await localStorage.getItem(appConst.storageKey.userInfo);
	if (userProfileRef) {
		userProfile.value = JSON.parse(userProfileRef as string)
	}
	else {
		userProfile.value = null;
	}


})

async function init() {
	if (shopDetails.value && shopData.value) {

		if (shopDetails.value?.id == shopData.value?.id) {
			setTimeout(() => {
				document
					.getElementById("shop_content_container")?.scrollBy({
						top: scrollPosition.value
					});
			}, 300);
		}
		else {
			search_text.value = "";
		}
		let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
		useServerSeoMeta({
			ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
			title: () => `${shopDetails.value.name} | Rẻ mà gần`,
			description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
			ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
		});
		shopData.value = await JSON.parse(JSON.stringify(shopDetails.value));
		await getProductSaleOffInShop();
		await getProductsWithCategory();
		open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
		refreshing.value = false;
		// setTimeout(() => {
		// 	checkShowFullDescription();
		// }, 300);
	}
	else {
		search_text.value = "";
		if (props.mode != 'agent') {
			await getDetailShop();
		}
		else {
			await agentGetDetailShop()
		}
	}
	baseDescription.value =
		`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần.
Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
	getCartNumber();
	// refreshListProduct()
	// 
	loadScript();
}
function getDetailShop() {
	refreshing.value = true;
	shopService.detailShop(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
			useSeoMeta({
				ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
				title: () => `${shopData.value?.name} | Rẻ mà gần`,
				description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogUrl: () => domain + route.fullPath,
				ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
				ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
			});

			// getShopProduct(0, 20);
			getProductSaleOffInShop();
			// getListCategory();
			getProductsWithCategory();
			open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
			refreshing.value = false;
			// setTimeout(() => {
			// 	checkShowFullDescription();
			// }, 300);

			baseDescription.value =
				`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần.
Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
		}
		else {
			refreshing.value = false;
		}
	}).catch(err => {
		refreshing.value = false;
		console.log(err);
		// setState({
		//     refreshing: false,
		// })
	})
}

function agentGetDetailShop() {
	refreshing.value = true;
	agentService.agentShopDetail(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			// getShopProduct(0, 20);
			getProductSaleOffInShop();
			// getListCategory();
			getProductsWithCategory();

			refreshing.value = false;

			setTimeout(() => {
				// checkShowFullDescription();
				open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
				let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
				useSeoMeta({
					ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
					title: () => `${shopData.value?.name} | Rẻ mà gần`,
					description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogUrl: () => domain + route.fullPath,
					ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
					ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
				});
			}, 300);
		}
		else {
			toast.error(t('ShopComponent.ban_khong_co_quyen_truy_cap'))
			refreshing.value = false;
		}

	})
}

function getCartNumber() {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$?.length ? cartData$ : [];
}
// function shareTo(social_name:string){
// 	let share = useSocialShare({
// 		network: 'facebook', // Required!
// 		url: 'https://www.example.com', // Optional, defaults to current page URL if not provided
// 		title: 'My Custom Title', // Optional, see the "Supported Networks" table below
// 		user: 'twitter_user', // Optional, see the "Supported Networks" table below
// 		hashtags: 'list,of,hashtags', // Optional, see the "Supported Networks" table below
// 		image: 'https://www.example.com/path/to/image.jpg', // Optional, see the "Supported Networks" table below
// 	})
// }

// function getShopProduct(offset: any = 0, limit: any = 20) {
// 	loadMore.value = true;
// 	filterProductLoading.value = true;
// 	offset = offset != null ? offset : dataShopProducts.value.length;

// 	limit = limit != null ? limit : 20;

// 	let categories: any[] = filterCategory.value ? [filterCategory.value] : [];

// 	shopService.searchProductsInShop(
// 		search_text.value,
// 		shopData.value.id,
// 		categories,
// 		limit,
// 		offset,
// 		productSortBy.value,
// 		null,
// 		(userProfile.value && userProfile.value.id == shopData.value.user_id) ? true : false
// 	).then(res => {
// 		res.body.data.result.forEach((e: any) => {
// 			e.currentQuantity = getCurrentQuantityInCart(e)
// 		});
// 		dataShopProducts.value = JSON.parse(JSON.stringify(res.body.data.result));
// 		countProducts.value = res.body.data.count;
// 		refreshing.value = false;
// 		loadMore.value = false;
// 		filterProductLoading.value = false;

// 	}).catch(err => {
// 		console.log(err);
// 		filterProductLoading.value = false;
// 		refreshing.value = false;
// 		loadMore.value = false
// 	})
// }
function getProductSaleOffInShop() {
	console.log("get sale off")
	shopService.searchProductsInShopClient(
		search_text.value,
		shopData.value?.id,
		[],
		10,
		null,
		productSortBy.value,
		null,
		(userProfile.value && userProfile.value.id == shopData.value?.user_id) ? true : false,
		true
	).then(res => {
		res.body?.data?.result.forEach((e: any) => {
			e.currentQuantity = getCurrentQuantityInCart(e)
		});
		dataShopProductsSaleOff.value = JSON.parse(JSON.stringify(res.body?.data?.result));

	}).catch(err => {
		console.log(err);
	})
}

function getProductsWithCategory() {
	shopService.getProductsWithCategory(shopData.value.id).then(res => {
		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data.categories));
		dataShopCategoriesBackup.value = JSON.parse(JSON.stringify(res.body.data.categories));
	}).catch(err => {
		dataShopCategories.value = [];
		dataShopCategoriesBackup.value = [];
	})
}

// function loadMoreProduct() {
// 	clearTimeout(loadMoreTimeOut);
// 	loadMore.value = true;

// 	if (dataShopProducts.value.length < countProducts.value) {
// 		loadMoreTimeOut = setTimeout(() => {
// 			let categories = filterCategory.value ? [filterCategory.value] : [];
// 			shopService.searchProductsInShop(
// 				search_text.value,
// 				shopData.value.id,
// 				categories,
// 				20,
// 				dataShopProducts.value.length,
// 				productSortBy.value,
// 				null,
// 				(userProfile.value && userProfile.value.id == shopData.value.user_id) ? true : false
// 			).then(res => {
// 				let list = [...dataShopProducts.value, ...res.body.data.result];
// 				list.forEach(e => {
// 					e.currentQuantity = getCurrentQuantityInCart(e)
// 				});
// 				dataShopProducts.value = list;
// 				loadMore.value = false;
// 			}).catch(err => {
// 				console.log(err);
// 				loadMore.value = false
// 			})
// 		}, 500);

// 	}
// 	else {
// 		loadMore.value = false
// 	}

// }

function getCurrentQuantityInCart(itemProduct: any) {
	let index = cartData.value.findIndex(function (e: CartDto) {
		return e.product_id == itemProduct.id
	});
	return index != -1 ? cartData.value[index].quantity : 0
}

function copyToClipboard(text: string) {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(text);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: text
		})
	}
	toast.info(t('ShopComponent.da_sao_chep_lien_ket'), {
		hideProgressBar: true,
		autoClose: 1000
	})
}

function callToShop() {
	if (!validPhone(shopData.value.phone).length) {
		toast.warning(t('ShopComponent.cua_hang_chua_cap_sdt'));
	}
}

// function refreshListProduct() {
// 	let listProduct = JSON.parse(JSON.stringify(dataShopProducts.value));
// 	listProduct.forEach((e: any) => {
// 		e.currentQuantity = getCurrentQuantityInCart(e)
// 	});
// 	dataShopProducts.value = JSON.parse(JSON.stringify(listProduct));
// }

// function getListCategory() {
// 	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
// 		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
// 	})
// }

function checkCategoryFilter(id: any) {
	if (id) {
		return filterCategory.value == id ? true : false
	}
	return false
}

// function listScroll(event: any) {
// 	let el = document?.getElementById('last_of_list')?.getBoundingClientRect().bottom;
// 	if (el && el <= window.innerHeight + 10) { loadMoreProduct(); }
// }

function searchProduct() {
	clearTimeout(searchProductTimeout);

	searchProductTimeout = setTimeout(async () => {
		// getShopProduct(0, 20);
		await searchProductInCurrentList()
	}, 1000)
}

function searchProductInCurrentList() {
	dataShopCategories.value = [];
	var arrTemp = JSON.parse(JSON.stringify(dataShopCategoriesBackup.value));
	var res: any = [];
	if (search_text.value?.length) {
		arrTemp.forEach((element: any) => {
			if (isArray(element.products)) {
				element.products = element.products?.filter((el: any) => {
					return nonAccentVietnamese(showTranslateProductName(el) ?? '').includes(nonAccentVietnamese(search_text.value))
						|| nonAccentVietnamese(showTranslateProductDescription(el) ?? '').includes(nonAccentVietnamese(search_text.value))
				});
			}
			else {
				element.products = [];
			}
			res.push(element)
		});
	}
	else {
		res = arrTemp;
	}
	dataShopCategories.value = JSON.parse(JSON.stringify(res))
	return
}

function loadScript() {
	const script = document?.createElement('script');
	script.type = 'text/javascript';
	script.src = 'https://sp.zalo.me/plugins/sdk.js';
	script.async = false;
	script.defer = true;
	script.id = "zalo_share";
	document?.body.appendChild(script);
}
function shareToZalo() {
	document?.getElementById('share_zalo_button')?.click();
}
function shareToApp() {

	let messageShare = {
		message: shopData.value.name,
		url: domain + appRoute.DetailShopComponent + "/" + (shopData.value.slug ? shopData.value.slug : shopData.value.id),
		filename: shopData.value.banner ? domainImage + shopData.value.banner : baseLogoUrl
	}

	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.share,
		data: messageShare
	})
}
function shareToFacebook() {
	let url = domain + appRoute.DetailShopComponent + "/" + (shopData.value.slug ? shopData.value.slug : shopData.value.id);
	let hrefLinkShare = "https://www.facebook.com/sharer/sharer.php?u=" + url;
	var x = (window.outerWidth) / 2 - 350;
	var y = (window.outerHeight) / 2 - 300;
	var responsive = "width=700,height=600,left=" + x + ",top=" + y
	window.open(hrefLinkShare, 'name', responsive)
}

// function checkShowFullDescription() {
// 	setTimeout(() => {
// 		let heighContent = document?.getElementById("description_content")?.getBoundingClientRect()?.height as number;
// 		let heightParrent = document?.getElementById("description_container")?.getBoundingClientRect()?.height as number;

// 		if (heighContent > heightParrent) {
// 			checkShowFullable.value = true
// 		}
// 		else {
// 			checkShowFullable.value = false
// 		}
// 	}, 500);
// }

function getPercentSaleOff(product: any) {
	if (product.price_off && product.price) return -Math.round(((product.price - product.price_off) / product.price) * 100 || 0);
	return 0
}

function changeSlideCategoryIndex(index = 0) {
	categoryCarousel.value.slideTo(index)
}
async function scrollToCategory(category_id: any) {
	let categoryEl = document.getElementById(`category_products_${category_id}`) as Element;
	var block: any = 'center';
	if (categoryEl?.getBoundingClientRect().height > window.innerHeight) {
		block = 'start';
	}
	await categoryEl?.scrollIntoView({
		behavior: 'smooth',
		block: block,
		inline: 'nearest',
		// inline: 'center'
	});
}
function containerScrolling() {
	let ep = document
		.getElementById("shop_content_container")
		?.scrollTop;
	scrollPosition.value = ep as number;
	let containerEl = document.getElementById('shop_banner') as HTMLElement;
	if (containerEl.getBoundingClientRect().top < 0) {
		enableScrollTopBtn.value = true;
	}
	else enableScrollTopBtn.value = false;
	let productListElements = document.querySelectorAll('.category-products-container');
	productListElements.forEach((productList, index) => {
		if (isElementInCenterViewPort(productList)) {
			filterCategory.value = dataShopCategories.value[index].id;
			changeSlideCategoryIndex(index)
		}
	});

	var stickyElement = document.getElementById('categories_container') as HTMLElement;

	if (stickyElement.getBoundingClientRect().top === 0) {
		stickyElement.classList.add('sticky-padding');
	} else {
		stickyElement.classList.remove('sticky-padding');
	}
}
function isElementInCenterViewPort(element: Element): boolean {
	const viewportHeight = window.innerHeight;
	const elementRect = element.getBoundingClientRect();

	// Element position relative to viewport
	const elementTop = elementRect.top;
	const elementBottom = elementRect.bottom;

	// Calculate viewport center
	const viewportCenter = viewportHeight / 2;

	// Check if element is in center of viewport
	return elementTop <= viewportCenter && elementBottom >= viewportCenter;
}
function scrollToTop() {
	document.getElementById('shop_content_container')?.scrollTo({
		top: 0,
		behavior: 'smooth'
	})
}

function formatOpeningHours(hoursObj: any) {
	function formatHours(hours: any) {
		return hours.map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`).join(' & ');
	}
	const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

	const result = [];
	if (hoursObj) {
		let currentGroup: any = [];
		let currentHours = hoursObj[days[0]];

		for (let i = 0; i < days.length; i++) {
			const day = days[i];
			const hours = hoursObj[day];

			if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
				currentGroup.push(t(`DayInWeek.${day}`));
			} else {
				if (currentGroup.length > 1) {
					// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				} else {
					// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				}
				currentGroup = [t(`DayInWeek.${day}`)];
				currentHours = hours;
			}
		}
		// Xử lý nhóm cuối cùng
		if (currentGroup.length > 1) {
			// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		} else {
			// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		}
	}

	return result;

}

function chatToShop() {
	chatToShopInfo.value = {
		members: [{
			...JSON.parse(JSON.stringify(shopData.value)),
			member_type: member_type.shop
		}],
		name: null,
		type: channel_type.user,
		avatar: shopData.value.logo
	};
	showChatToShop.value = true;
}
</script>
<template>
	<div class="public-container" id="shop_content_container" v-on:scroll="() => {
		containerScrolling()
	}">
		<div class="title-header shop-title-header h-fit" v-if="shopData && shopData.id && !refreshing">
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<div class="search-input-container">
				<Icon name="bx:search"></Icon>
				<input type="search" name='search-text' :placeholder="$t('ShopComponent.tim_san_pham')"
				:maxlength="appConst.max_text_short"
					autoComplete='off' :value="search_text" v-on:input="($event: any) => {
						search_text = $event.target.value;
						filterProductLoading = true;
						searchProduct()
					}" />
			</div>

			<div class="header-right">
				<button class="cart-in-search" v-on:click="() => {
					router.push(appRoute.CartComponent);
				}">
					<Icon name="bi:basket2-fill" size="25"></Icon>
					<em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length : "10+" }} </em>
				</button>
			</div>
		</div>
		<div class='v-stack loading-skeleton' v-if="refreshing">
			<v-skeleton-loader height='250px'></v-skeleton-loader>

			<div class='h-stack'>
				<v-skeleton-loader style="border-radius: 15;" width='125px' height='125px' class='avt-skeleton' />
				<div class='v-stack'>
					<v-skeleton-loader width='40%' height='20px' />
					<v-skeleton-loader width='100%' height='20px' borderRadius='15px' />

					<v-skeleton-loader width='100%' height='20px' borderRadius='15px' />
				</div>
			</div>


		</div>
		<div v-else-if="shopData && shopData.id && !refreshing" class="shop-container" id='shop-container'>


			<div class='v-stack shop-content-container'>

				<div class="shop-banner" id="shop_banner">

					<img loading="lazy" :src="banner_temp" :placeholder="banner_temp" alt=""
						v-if="!(shopData && shopData.banner)" />

					<div class="banner-origin-container" :class="{ 'none-style': !(shopData.banner?.style?.length) }"
						v-else v-on:click="() => {
							if (shopData?.banner?.path?.length) {
								let itemImg = {
									path: shopData?.banner?.path,
									title: shopData.name,
								}
								listObjectViewer = [itemImg];
								indexActive = 0;
								showImageViewerModal = true;
							}

						}">
						<img :src="(shopData && shopData.banner) ? (domainImage + shopData.banner.path) : banner_temp"
							:placeholder="banner_temp" :style="{
								transform: (shopData.banner && shopData.banner.style) ? shopData.banner.style : 'none'
							}" />
					</div>


				</div>

				<div class="shop-detail-container">
					<!-- <div class="shop-logo">
        <img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt="" v-if="!shopData.logo" />
        <div class="logo-origin-container" :class="{ 'none-style': !(shopData.logo?.style?.length) }"
            v-else>
            <img :src="domainImage + shopData.logo.path" :style="{
                transform: (shopData.logo && shopData.logo.style) ? shopData.logo.style : 'none'
            }" alt="" />
        </div>
    </div> -->
					<AvatarComponent class="shop-logo" :imgTitle="shopData?.name" :imgStyle="shopData?.logo?.style"
						:imgSrc="shopData?.logo?.path?.length
							? (domainImage + shopData?.logo?.path)
							: ''" :width="125" :height="125" v-on:click="() => {
								// if (shopData?.logo?.path?.length) {
								// 	let itemImg = {
								// 		path: shopData?.logo?.path,
								// 		title: shopData.name,
								// 	}
								// 	listObjectViewer = [itemImg];
								// 	indexActive = 0;
								// 	showImageViewerModal = true;
								// }

							}" />
					<div class="shop-detail-content">
						<span class="name">
							{{ shopData.name }}
						</span>
						<span class="business-types">
							{{ shopData.business_types?.name }}
						</span>
						<!-- <span class="open-time" v-if="shopData?.open_hours">
							<em>{{ $t('ShopComponent.gio_mo_cua') }}:</em>
							{{ formatOpeningHours(JSON.parse(shopData?.open_hours)) }}
						</span> -->
						<span class='address'>
							{{ shopData.address }}
						</span>

						<div class="actions">
							<button :title="$t('ShopComponent.chi_duong')" v-on:click="() => {
								showDirection = true
							}">
								<Icon name="ci:navigation"></Icon>
							</button>
							<button :title="$t('ShopComponent.sua_thong_tin')"
								v-if="userProfile && userProfile.id == shopData.user_id" v-on:click="() => {
									// showEditShopInfoModal = true;
									if (props.mode != 'agent') {
										router.push(appRoute.EditShopInfoComponent)
									}
									else {
										router.push(appRoute.AgentEditShopInfoComponent.replaceAll(':id', shop_id))
									}

								}">
								<Icon name="bx:edit-alt" class='share'></Icon>
							</button>
							<nuxt-link :to="`tel:${validPhone(shopData.phone)}`" :target="'_blank'" v-else
								:title="$t('ShopComponent.go_call_cua_hang')" v-on:click="() => {
									callToShop()
								}">
								<Icon name="material-symbols:call-sharp"></Icon>
							</nuxt-link>
							<button :title="$t('ShopComponent.nhan_tin')" v-if="userProfile && userProfile.id != shopData.user_id" v-on:click="() => {
								chatToShop()
							}">
								<Icon name="mdi:message-processing-outline"></Icon>
							</button>
							<!-- <button :title="$t('ShopComponent.mo_ta')" :disabled="!shopData.description?.length" v-on:click="() => {
                showDescription = !showDescription;
            }">
                <Icon name="iconamoon:information-circle"></Icon>
            </button> -->
							<div class="zalo-share-button" id="share_zalo_button"
								:data-href="domain + appRoute.DetailShopComponent + '/' + (shopData.slug ? shopData.slug : shopData.id)"
								:data-oaid="zaloConfig.appIDZaloLogin" :data-customize='true'>
								{{ $t('ShopComponent.chia_se_qua_zalo') }}
							</div>
							<v-menu class="bootstrap-dropdown-container" location="bottom right"
								content-class="shop-share-dropdown-content">
								<template v-slot:activator="{ props }">
									<button :title="$t('ShopComponent.chia_se')" class="share" dark v-bind="props">
										<Icon name="ion:ios-more" />
									</button>
								</template>
								<v-list>
									<v-list-item key="copy_url" class="shop-share-dropdown-item" v-on:click="() => {
										router.push(appRoute.ElegantMenuShopComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id))
										// copyToClipboard(domain + appRoute.DetailShopComponent + '/' + shopData.slug);
									}">
										<v-list-item-title>{{ $t('ShopComponent.xem_menu') }}</v-list-item-title>
									</v-list-item>
									<v-list-item key="copy_url" class="shop-share-dropdown-item" v-on:click="() => {
										copyToClipboard(domain + appRoute.DetailShopComponent + '/' + shopData.slug);
									}">
										<v-list-item-title>{{ $t('ShopComponent.sao_chep_dia_chi')
											}}</v-list-item-title>
									</v-list-item>
									<v-list-item key="share_to_facebook" class="shop-share-dropdown-item" v-on:click="() => {
										shareToFacebook()
									}">
										<v-list-item-title>{{ $t('ShopComponent.chia_se_qua_facebook')
											}}</v-list-item-title>
									</v-list-item>
									<v-list-item key="share_to_zalo" class="shop-share-dropdown-item" v-if="!webInApp"
										v-on:click="() => {
											shareToZalo();
										}">
										<v-list-item-title>{{ $t('ShopComponent.chia_se_qua_zalo')
											}}</v-list-item-title>
									</v-list-item>

									<v-list-item key="share_to_app" class="shop-share-dropdown-item" v-if="webInApp"
										v-on:click="() => {
											shareToApp();
										}">
										<v-list-item-title>{{ $t('ShopComponent.khac') }}</v-list-item-title>
									</v-list-item>
								</v-list>
							</v-menu>
						</div>
					</div>
				</div>

				<div class="shop-detail-open-time" v-if="open_time_list.length > 0">
					<div class="shop-detail-open-time-content">
						<span class="label">{{ $t('ShopComponent.gio_mo_cua') }}</span>
						<div class="list-open-time">
							<div class="item-open-time" v-for="item in open_time_list">
								<span class="days">{{ item.days }}</span>
								<span class="times">{{ item.times }}</span>
							</div>
						</div>
					</div>

				</div>

				<div class="line"></div>
				<div class="shop-detail-description" v-show="showDescription">
					<span class='description'>
						<span id="description_container">
							<span id="description_content">
								{{ shopData.description?.length ? shopData.description : baseDescription }}
							</span>
						</span>
					</span>
				</div>

				<div class="shop-products-container" v-if="dataShopProductsSaleOff?.length || refreshing">
					<div class="title-stack">
						<span>{{ $t('ShopComponent.dang_giam_gia') }}</span>
					</div>
					<div class="products-container grid">
						<nuxt-link
							:to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
							class="product-item-container-grid" :key="'list_' + itemProduct.id"
							v-for="(itemProduct, indexSelected) in dataShopProductsSaleOff"
							v-if="dataShopProductsSaleOff.length">
							<img loading="lazy" v-on:click="() => {
								router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
							}" :src="(itemProduct && itemProduct.profile_picture)
								? (domainImage + itemProduct.profile_picture)
								: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" />
							<div class='v-stack product-detail'>
								<span class='product-name' v-on:click="() => {
									// this.addProductToCart(itemProduct)
									router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
								}">
									<!-- {{ itemProduct.name }} -->
									{{ showTranslateProductName(itemProduct) }}
								</span>

								<div class="h-stack">
									<span class='product-price' v-on:click="() => {
										// this.addProductToCart(itemProduct)
										router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
									}">
										{{
											(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
												formatCurrency(parseFloat(itemProduct.price_off), shopData.currency) :
												(parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
													$t('ShopComponent.gia_lien_he') :
													formatCurrency(parseFloat(itemProduct.price), shopData.currency) }} <em
											class="off"
											v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
											{{
												formatCurrency(itemProduct.price ? parseFloat(itemProduct.price) : 0,
													shopData.currency)
											}}</em>
									</span>
									<div class="sale-off-badge">
										{{ getPercentSaleOff(itemProduct) }}%
									</div>
								</div>
								<button class='edit-button'
									v-if="userProfile && ((props.mode != 'agent ' && shopData.user_id == userProfile.id) || (props.mode == 'agent ' && shopData.agent_id == userProfile.id))"
									v-on:click.stop="(e) => {
										e.preventDefault()
									}" v-on:click="() => {
										// selectedProduct = JSON.parse(JSON.stringify(itemProduct));
										// showEditProductModal = true;
										if (props.mode != 'agent') {
											router.push(appRoute.EditProductComponent.replaceAll(':id', itemProduct.id))
										}
										else {
											router.push(appRoute.AgentShopEditProductComponent.replaceAll(':id', shop_id).replaceAll(':product_id', itemProduct.id))
										}
									}">
									{{ $t('ShopComponent.chinh_sua') }}
								</button>
								<button v-else class='add-to-cart-btn' v-on:click="async () => {
									selectedProduct = JSON.parse(JSON.stringify(itemProduct));
									selectedProduct.shop = JSON.parse(JSON.stringify(shopData));
									showSelectedProduct = true;
								}" v-on:click.stop="(e) => { e.preventDefault() }">
									{{ $t('ShopComponent.them_vao_gio') }}
								</button>

							</div>


						</nuxt-link>
						<div class='v-stack empty-list'
							v-if="(!dataShopProductsSaleOff || !dataShopProductsSaleOff.length) && !refreshing">
							<img loading="lazy" :src='list_empty' :placeholder="list_empty" class='empty-list-image' />

							<span>
								{{ $t('ShopComponent.khong_tim_thay_san_pham_nao') }}
							</span>
						</div>
					</div>
					<v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained
						content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
						<Icon name="eos-icons:loading"></Icon>
					</v-overlay>
				</div>


				<div class="shop-products-container">
					<div class="title-stack">
						<span>{{ $t('ShopComponent.tat_ca_san_pham') }}</span>
						<!-- <nuxt-link :to="appRoute.ElegantMenuShopComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)">{{ $t('ShopComponent.menu_sieu_cap') }}</nuxt-link> -->
						<!-- <v-menu class="bootstrap-dropdown-container" location="bottom right" v-if="false">
            <template v-slot:activator="{ props }" v>
                <button class="button-mode button-fitler-sort" dark v-bind="props">
                    {{ dataFilterSort.filter(e => e.value == productSortBy)[0].title }}
                    <Icon name="material-symbols:arrow-drop-down-rounded" />
                </button>
            </template>
            <v-list>
                <v-list-item v-for="(item, index) in dataFilterSort" :key="'sort_' + item.value"
                    class="filter-sort-dropdown-item" :active="productSortBy == item.value"
                    activeClass="active" v-on:click="() => {
                        productSortBy = item.value;
                        getShopProduct(0, 20)
                    }">
                    <v-list-item-title>{{ item.title }}</v-list-item-title>
                </v-list-item>
            </v-list>
        </v-menu> -->
					</div>

					<div class="categories-container" id="categories_container">
						<Swiper class="my-carousel" :modules="[SwiperFreeMode]" :freeMode="true" :centeredSlides="true"
							:centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false" :spaceBetween="10"
							:effect="'creative'" :autoplay="false" key="category-carousel" @init="(e: any) => {
								categoryCarousel = e;
							}">
							<SwiperSlide class="category-item-slide"
								v-for="(itemCategory, indexTab) of dataShopCategories" v-on:click="async () => {
									// let selected = checkCategoryFilter(itemCategory.id);
									// if (selected) {
									//  filterCategory = null;
									// }
									// else {
									//  filterCategory = itemCategory.id
									// }

									// await getShopProduct(0, 20);

									await scrollToCategory(itemCategory.id)
									// filterCategory = itemCategory.id;
								}" :class="checkCategoryFilter(itemCategory.id) ? 'active' : ''" :value="itemCategory.id"
								:key="itemCategory.id" :id="'tab_' + itemCategory.id">
								<div class="tab-title">
									<span class='name'>
										{{ showTranslateProductName(itemCategory) }}
									</span>
								</div>
							</SwiperSlide>
						</Swiper>
					</div>

					<div class="products-container">
						<div class="category-products-container"
							v-for="(itemCategory, indexCategory) in dataShopCategories"
							:key="`category_products_${itemCategory.id || 'all'}`"
							:id="`category_products_${itemCategory.id}`">
							<div class="category-title" :id="`category_products_title_${itemCategory.id}`">
								{{ itemCategory.name }} ({{ itemCategory.products.length }})
							</div>
							<div class="category-products-list"
								:key="`category_${itemCategory.id || 'all'}_product_item_${itemProduct.id}`"
								v-for="(itemProduct, indexSelected) in itemCategory.products">
								<nuxt-link
									:to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
									class="product-item-container" :key="'list_' + itemProduct.id">
									<img loading="lazy" v-on:click="() => {
										router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
									}" :src="(itemProduct && itemProduct.profile_picture)
										? (domainImage + itemProduct.profile_picture)
										: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" />
									<div class='v-stack product-detail'>
										<span class='product-name' v-on:click="() => {
											// this.addProductToCart(itemProduct)
											router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
										}">
											<!-- {{ itemProduct.name }} -->
											{{ showTranslateProductName(itemProduct) }}
										</span>

										<div class="h-stack">
											<span class='product-price' v-on:click="() => {
												// this.addProductToCart(itemProduct)
												router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
											}">
												{{
													(itemProduct.price_off != null && itemProduct.price_off <
														itemProduct.price) ?
														formatCurrency(parseFloat(itemProduct.price_off), shopData.currency)
														: (parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
															$t('ShopComponent.gia_lien_he') :
															formatCurrency(parseFloat(itemProduct.price), shopData.currency) }}
													<em class="off"
													v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
													{{
														formatCurrency(itemProduct.price ? parseFloat(itemProduct.price) :
															0,
															shopData.currency)
													}}</em>
											</span>
											<button class='edit-button'
												v-if="userProfile && (shopData.user_id == userProfile.id)"
												v-on:click.stop="(e) => {
													e.preventDefault()
												}" v-on:click="() => {
													// selectedProduct = JSON.parse(JSON.stringify(itemProduct));
													// showEditProductModal = true;
													if (props.mode != 'agent') {
														router.push(appRoute.EditProductComponent.replaceAll(':id', itemProduct.id))
													}
													else {
														router.push(appRoute.AgentShopEditProductComponent.replaceAll(':id', shop_id).replaceAll(':product_id', itemProduct.id))
													}
												}">
												{{ $t('ShopComponent.chinh_sua') }}
											</button>
											<button v-else class='add-to-cart-btn' v-on:click="async () => {
												selectedProduct = JSON.parse(JSON.stringify(itemProduct));
												selectedProduct.shop = JSON.parse(JSON.stringify(shopData));
												showSelectedProduct = true;
											}" v-on:click.stop="(e) => { e.preventDefault() }">
												{{ $t('ShopComponent.them_vao_gio') }}
											</button>
										</div>


									</div>


								</nuxt-link>
							</div>
						</div>

						<!-- <div class='v-stack empty-list'
            v-if="(!dataShopProducts || !dataShopProducts.length) && !refreshing">
            <img loading="lazy" :src='list_empty' :placeholder="list_empty" class='empty-list-image' />

            <span>
                {{ $t('ShopComponent.khong_tim_thay_san_pham_nao') }}
            </span>
        </div> -->
						<div id='last_of_list'></div>
					</div>
					<v-overlay v-model="loadMore" :z-index="100" :absolute="false" contained
						content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
						<Icon name="eos-icons:loading"></Icon>
					</v-overlay>
				</div>



			</div>

			<span class='loading-more' v-show="loadMore">
				<Icon name="eos-icons:three-dots-loading"></Icon>
			</span>

			<!-- <VueFinalModal class="my-modal-container" :click-to-close="false"
			content-class="my-modal-content-container v-stack edit-shop-info-modal" v-model="showEditShopInfoModal"
			v-on:closed="() => {
				showEditShopInfoModal = false
			}" contentTransition="vfm-slide-up">

			<EditShopInfoComponent :shopData="shopData" :profileData="shopData.owner" v-on:close="async (e) => {
				if (e) {
					showFullDescription = false;
					await getDetailShop();

				}
				showEditShopInfoModal = false;
			}"></EditShopInfoComponent>
		</VueFinalModal> -->
			<!-- <VueFinalModal class="my-modal-container"
				content-class="my-modal-content-container v-stack edit-product-modal" v-model="showEditProductModal"
				v-on:closed="() => {
					showEditProductModal = false
				}" contentTransition="vfm-slide-up">
				<EditProductComponent :productData="JSON.parse(JSON.stringify(selectedProduct))" v-on:close="(event: any) => {
					showEditProductModal = false
					if (event) {
						// getShopProduct();
						getProductSaleOffInShop();
					}

				}"></EditProductComponent>
			</VueFinalModal> -->
		</div>
		<div v-else-if="!refreshing">
			<PageNotFound />
		</div>

		<div class="footer-absolute h-fit">
			<button class="go-up" v-if="enableScrollTopBtn" v-on:click="() => {
				scrollToTop()
			}">
				<Icon name="ic:round-arrow-upward"></Icon>
			</button>
		</div>
		<v-overlay v-model="showDirection" location="bottom" contained :absolute="true" :z-index="10000"
			key="show_direction" class="shop-direction-overlay-container" persistent
			content-class='shop-direction-container' no-click-animation>
			<DirectionComponent :latitude_destination="parseFloat(shopData.latitude)"
				:longitude_destination="parseFloat(shopData.longitude)" :logo="shopData.logo" v-on:close="() => {
					showDirection = false;
				}"></DirectionComponent>
		</v-overlay>
		<AddProductToCartComponent v-if="showSelectedProduct" v-on:close="() => {
			showSelectedProduct = false
		}" :selectedProduct="selectedProduct">
		</AddProductToCartComponent>
		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>
		<v-overlay v-model="showChatToShop" :close-on-back="true" :z-index="100" :absolute="true" contained key="show_chat_detail"
			class="chat-detail-overlay-container" content-class='chat-detail-modal-container' no-click-animation
			v-on:click:outside="() => {
				showChatToShop = false;
			}">
			<ChatDetailComponent v-if="showChatToShop" 
				:receiver_id="shopData.id" 
				:chat_info="chatToShopInfo"
				:receiver_type="true" 
				:mode="member_type.user"
				v-on:close="() => {
					showChatToShop = false;
				}"></ChatDetailComponent>
		</v-overlay>
	</div>

</template>

<style lang="scss" src="./ShopStyles.scss" scoped></style>
