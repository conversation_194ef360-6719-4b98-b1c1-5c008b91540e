.chat-manage-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f5f6fa;
  position: relative;
  font-size: 17px;
  overflow: hidden;
  z-index: 1;
  width: 100%;
  max-width: var(--max-width-content-view-1024) !important;
  margin: auto;

  & .manage-tab {
    text-transform: none;
    font-weight: 600;
    font-size: 1em;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    background: white;
    height: fit-content;

    & .manage-tab-title {
      flex: 1;
      text-transform: none;
      // border-bottom: 2px solid transparent;
      color: #79787d;
      font-weight: 600;
      gap: 5px;
      font-size: 15px;
      height: 35px !important;
      background-color: white;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        & em {
          border-radius: 2em;
          color: white;
          background: var(--primary-color-2);
          min-width: 20px;
          height: 20px;
          font-size: 13px;
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 600;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    & .manage-tab-title.active {
      font-weight: 600;
      // border-bottom: 2px solid var(--primary-color-1);
    }
  }

  & > .chat-manage-sticky-header{
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;

    & > span{
      font-size: 13px;
      color: #545454;
      font-weight: 400;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 5px;

      & > em{
        font-weight: 600;
        color: var(--primary-color-2);
      }
    }
  }

  & .chat-manage-header {
    font-size: 15px;

    & h3 {
      text-transform: uppercase;
    }
  }

  & > .chat-manage-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: #f4f4f4;
    position: relative;
    font-size: 17px;
    padding-bottom: 30px;

    & > .list-chat {
      display: flex;
      flex-direction: column;

      & > .chat-item {
        display: flex;
        flex: 1;
        cursor: pointer;
        color: #363636;
        background-color: white;
        border-bottom: thin solid #ebebeb;

        & > .avatar-chat {
          width: 75px;
          height: 75px;
          padding: 10px;
          border-radius: 5px;

          & .single {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 5px;

            & > .avatar-container {
              border-radius: 5px;
            }
            // box-shadow: 0 0 5px #ddd;
          }
        }
        & > .avatar-chat.group {
          display: flex;
          flex-wrap: wrap;
          gap: 1px;

          & > .multiple {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;

            & > .single {
              width: calc(100% / 2);
              height: calc(100% / 2);
              margin-left: 5px;
            }
            & > .single.odd {
              margin-left: -5px;
            }
            & > .single:not(.odd):not(:first-child) {
              margin-top: -5px;
            }

            & > .count {
              background: #ddd;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 15px;
              font-weight: 700;
              margin: -5px 0 0 -5px;
              z-index: 100;
              color: #8f8f8f;
            }
          }
        }

        & > .content-chat {
          padding: 10px 0;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          flex: 1;
          overflow: hidden;
          user-select: none;

          & > .name {
            font-size: 15px;
            font-weight: 700;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
            overflow: hidden;
            color: #545454;
          }

          & > .last-message {
            font-size: 13px;
            color: #545454;
            font-weight: 400;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
            overflow: hidden;
            display: flex;

            & > em {
              overflow: hidden;
              text-overflow: ellipsis;
              width: fit-content;
              max-width: 50%;
              display: block;
              font-weight: 700;
              flex: none;
            }

            & > span {
              // max-width: calc(100% - 110px);
              display: block;
              overflow: hidden;
              text-overflow: ellipsis;

              & > svg {
                height: 20px;
              }
            }
            & > span.advanced-message {
              display: flex;
              align-items: center;

              & > svg {
                min-width: 20px;
                width: 20px;
              }

              & > span {
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }

        & > .secondary-info {
          display: flex;
          flex-direction: column;
          padding: 10px;
          font-size: 12px;
          color: #545454;
          font-weight: 600;

          & > .last-message-time {
            display: block;
            margin-top: 7px;
          }

          & > .actions {
            display: none;
            width: 30px;
            height: 30px;
            border-radius: 5px;
            justify-content: center;
            align-items: center;
            background: #f5f6fa;
            margin-left: auto;
            font-size: 20px;
            color: #363636;
          }
        }
      }

      & > .chat-item.not-read {
        background: var(--secondary-color-1);
        & .name {
          font-weight: 700;
          color: #363636;
        }
        & .last-message {
          font-weight: 700;
          color: #363636;
        }

        & .secondary-info {
          color: #363636;
          font-weight: 700;
        }
      }

      & > .chat-item:hover {
        & .actions {
          display: flex;
        }
      }

      & > .end-list {
        font-size: 14px;
        text-align: center;
        font-weight: 400;
        color: #a8a7a7;
        margin: 10px 0;
      }
    }

    & > .list-chat.empty {
      align-items: center;
      justify-content: center;
      flex: 1;

      & > img {
        width: 200px;
        object-fit: contain;
      }
      & > svg {
        width: 100px;
        color: var(--primary-color-2);
      }
      & > span {
        text-align: center;
        // margin: auto;
        font-size: 20px;
        font-weight: 700;
        color: var(--primary-color-2);
      }

      & > button,
      a {
        color: #363636;
        font-weight: 700;
        font-size: 20px;
      }
    }
  }

  & > .chat-manage-content-container.skeletons {
    background: white;
  }
}


