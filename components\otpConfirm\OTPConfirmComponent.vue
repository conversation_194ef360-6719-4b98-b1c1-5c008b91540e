<template>
	<div class='v-stack otp-confirm-container'>
		<span class="title">
			{{ $t('OTPConfirmComponent.ma_otp_da_gui_den') }} {{ props.type }} <span>{{ getTextHiding(dataCheckOTP)
				}}</span>
		</span>
		<v-otp-input v-model="otp" class="otp-input"></v-otp-input>
		<div class="re-send">
			{{ $t('OTPConfirmComponent.khong_nhan_duoc_ma') }}&nbsp;
			<button v-if="time_remaining <= 0" v-on:click="() => {
				getOTP();
			}">{{ $t('OTPConfirmComponent.gui_lai') }}</button>
			<button v-if="time_remaining > 0" disabled>
				{{ $t('OTPConfirmComponent.gui_lai_sau') }}
				<vue-countdown :time="time_remaining * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
					console.log('end');
					time_remaining = 0;
				}" v-on:progress="({ totalSeconds }) => {
						// time_remaining = totalSeconds;
					}">
					{{ minutes ? minutes + $t('OTPConfirmComponent.phut') : '' }} {{ seconds }} {{
						$t('OTPConfirmComponent.giay') }}
				</vue-countdown>
			</button>
		</div>

		<div class='h-stack form-actions'>
			<button class='cancel-button' :disabled="isChecking" v-on:click="() => {
				close(false)
			}">
				{{ $t('OTPConfirmComponent.dong') }}
			</button>
			<button class='save-button' :disabled="isChecking || !otp?.length" v-on:click="() => {
				// close(true);
				submit()
			}">
				<span v-if="!isChecking">{{ $t('OTPConfirmComponent.xac_thuc') }}</span>
				<Icon name="eos-icons:loading" size="20" v-else />
			</button>
		</div>
	</div>

</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import type { CartDto } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';
import { ProductService } from '~/services/productService/productService';
import { UserService } from '~/services/userService/userService';
import { connectFirestoreEmulator } from "firebase/firestore";
import { toast } from "vue3-toastify";
import { PublicService } from "~/services/publicService/publicService";
import VueCountdown from '@chenfengyuan/vue-countdown';
import { HttpStatusCode } from "axios";
const router = useRouter();
const props = defineProps({
	data: null,
	type: null
});
const { t } = useI18n();
let publicService = new PublicService();

var emit = defineEmits(['close'])

var otp = ref("");
var isChecking = ref(false);
var dataCheckOTP = ref(props.data ? props.data : "");
var time_remaining = useState('otp_cooldown', () => { return 0 });
useSeoMeta({
	title: t('AppRouteTitle.OTPConfirmComponent'),
});

onMounted(() => {
	getOTP();
})

function getOTP() {
	publicService.getOTP(dataCheckOTP.value, props.type).then(res => {
		time_remaining.value = res.otp_cooldown;
	})
}

function getTextHiding(data: string) {
	let index$ = data.indexOf('@');
	return data.substring(0, 3) + "****" + data.substring(index$ != -1 ? index$ : 7);
}

function close(checked = false) {
	// props.close();
	emit('close', checked);
}

async function submit() {
	isChecking.value = true;
	let data$;
	if (props.type == 'phone') {
		data$ = {
			phone: dataCheckOTP.value,
			otp: otp.value
		}
	}
	else if (props.type == 'email') {
		data$ = {
			email: dataCheckOTP.value,
			otp: otp.value
		}
	}

	let confirm = await publicService.confirmOtp(data$);
	if (confirm.status == HttpStatusCode.Ok) {
		if (confirm.body.data == true) {
			close(true);
		}
		else {
			toast.error(t('OTPConfirmComponent.ma_xac_thuc_sai'))
		}
	}
	else {
		toast.error(t('OTPConfirmComponent.ma_xac_thuc_sai'))
	}
	isChecking.value = false;
}
</script>
<style lang="scss" src="./OTPConfirmStyles.scss"></style>