export default defineNuxtPlugin(() => {
  if (process.client) {
    console.log('Network error plugin initialized');
    const { show, hide } = useNetworkError()

    // Listen for browser offline/online events
    window.addEventListener('offline', () => {
      console.log('<PERSON><PERSON><PERSON> went offline - showing network error modal');
      show()
    })

    window.addEventListener('online', () => {
      console.log('<PERSON><PERSON><PERSON> came online - hiding network error modal');
      hide()
    })

    // Check initial network status
    if (!navigator.onLine) {
      console.log('Browser is offline on load - showing network error modal');
      show()
    }

    // Add global test function
    (window as any).testNetworkErrorGlobal = () => {
      console.log('Global test function called');
      show();
    };
  }
})
