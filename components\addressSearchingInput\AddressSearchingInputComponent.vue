<template>
  <div class="search-address-container">
    <input :title="address?.length ? address : $t('AddressSearchingInputComponent.dia_chi')" name="search-address"
    :maxlength="appConst.max_text_short"
      class="input-search-address" :placeholder="$t('AddressSearchingInputComponent.ten_duong_toa_nha_so_nha')"
      autocomplete="off" :value="address || ''" v-on:focus="() => {
        isFocusAddress = true;
      }" v-on:input="($event: any) => {
        changeAddress($event.currentTarget.value);
      }" v-on:keypress.enter="() => {
        if (listAddressSuggest.length) {
          isFocusAddress = true;
        }
        else {
          changeAddress(address);
        }
      }" v-on:blur="() => {
        hideAddressSuggest()
      }" />
    <span class="icon-right" v-show="addressSearching">
      <Icon name="eos-icons:loading"></Icon>
    </span>
    <span class="icon-right" v-show="listAddressSuggest.length" v-on:click="() => {
      isFocusAddress = !isFocusAddress
    }">
      <Icon name="material-symbols:keyboard-arrow-up" v-if="isFocusAddress"></Icon>
      <Icon name="material-symbols:keyboard-arrow-down" v-else></Icon>
    </span>
    <v-list v-show="isFocusAddress && listAddressSuggest.length" class="address-suggest-list-container">
      <v-list-item key="add_neww" class="address-suggest-item" v-for="(itemSuggest) in listAddressSuggest" v-on:click="() => {
        selectAddressSuggest(itemSuggest)
      }">
        <span>{{ itemSuggest?.address }}</span>
      </v-list-item>
      <span class="none-address-searching" v-if="!listAddressSuggest.length && !addressSearching">
        {{ $t('AddressSearchingInputComponent.khong_tim_thay_dia_chi_phu_hop') }}
      </span>

    </v-list>

  </div>
</template>

<script lang="ts" setup>
import { HttpStatusCode } from 'axios';
import { appConst } from '~/assets/AppConst';
import { PublicService } from '~/services/publicService/publicService';

const props = defineProps({
  address: null
})

const emit = defineEmits([
  'input',
  'select'
])

var publicService = new PublicService();

var address = ref(props.address?.length ? props.address : "");
var listAddressSuggest = ref([] as any)
var addressSuggestTimeout: any;
var isFocusAddress = ref(false);
var addressSearching = ref(false);

// onMounted(()=>{
//   address.value = props.address?.length ? props.address : "";
// })

onUpdated(() => {
  address.value = props.address?.length ? props.address : address.value;
})

function getAddressSuggest() {
  addressSearching.value = true;
  clearTimeout(addressSuggestTimeout);
  addressSuggestTimeout = setTimeout(() => {
    publicService.myGeocoderByAddressToLatLng(address.value).then(res => {
      if (res.status != null) {
        if (res.status == HttpStatusCode.Ok) {
          listAddressSuggest.value = JSON.parse(JSON.stringify(res.body.data))
        }
        else listAddressSuggest.value = [];
        addressSearching.value = false;
      }

    }).catch((err) => {
      listAddressSuggest.value = [];
      addressSearching.value = false;
    })
  }, 500);
}
function changeAddress(address_text: string) {
  address.value = address_text;
  getAddressSuggest();
  emit('input', address.value);
}
function selectAddressSuggest(addressSuggest: any) {
  address.value = addressSuggest.address;
  emit('select', JSON.parse(JSON.stringify(addressSuggest)));
  hideAddressSuggest();
}

function hideAddressSuggest() {
  setTimeout(() => {
    isFocusAddress.value = false;
  }, 300);
}
</script>
<style lang="scss" src="./AddressSearchingInputStyles.scss"></style>
