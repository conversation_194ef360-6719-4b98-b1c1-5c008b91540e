<template>
    <div class="reply-quotation-container">
        <SubHeaderV2Component :title="dataQuotation?.name ?? $t('ReplyQuotationComponent.phan_hoi_bao_gia')">
            <!-- <template v-slot:header_left>
                <button class="back-button" v-on:click="() => {
                    close();
                }">
                    <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                </button>
            </template> -->
            <template v-slot:header_right>
                <v-btn class="view-detail" v-on:click="() => {
                    show_detail = true
                }">
                    <Icon name="proicons:info"></Icon>
                </v-btn>

                <v-btn class="view-detail save-quotation" 
                    v-if="!deepCompareJsons(dataQuotation, dataQuotationBackup)" v-on:click="() => {
                        saveQuotation();
                    }"
                    :disabled="isSaving"
                    :loading="isSaving"
                >
                    <Icon name="fluent:save-arrow-right-20-regular"></Icon>
                </v-btn>
            </template>
        </SubHeaderV2Component>
        <div class="reply-quotation-content-container">

            <v-data-table class="data-detail" v-if="dataQuotation?.id" v-model:page="current_data_page"
                v-model:items-per-page="item_per_page" :headers="[
                    ...Object.keys(data_field_structor).map((key, index) => {
                        return {
                            value: key,
                            title: t(`ReplyQuotationComponent.${(data_field_structor as any)?.[key].label_key}`),
                        }
                    })
                ]" :items="dataQuotation.material_quotations">
                <template v-slot:headers="{ columns }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="head-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :class="{
                                    'short': column.value == data_field_structor.stt.label_key,
                                    'auto': column.value == 'action'
                                }">
                                <span class="cursor-pointer">{{ column.title }}</span>
                            </th>
                        </template>
                    </tr>
                </template>
                <template v-slot:item="{ item, columns, index }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="body-col stt" v-if="column.value == data_field_structor.stt?.label_key"
                                :id="`stt_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <span>{{ index + (current_data_page - 1) * item_per_page + 1 }}</span>
                            </th>
                            <th class="body-col notes" v-else-if="column.value == 'notes'">
                                <UTextarea autoresize resize :maxrows="5" :rows="1" type="text"
                                    :placeholder="t('ReplyQuotationComponent.ghi_chu_cho_nguyen_lieu')"
                                    class="quotation-detail-input" v-on:input="($event: any) => {
                                        (item as any)[column.key ?? ''] = $event.target.value;
                                    }" :value="(item as any)?.[column.key ?? '']" />
                            </th>
                            <th class="body-col notes" v-else-if="column.value == 'price'">
                                <input v-maska data-maska-number-fraction="2" data-maska-reverse="true"
                                    inputmode="numeric" :placeholder="$t('ReplyQuotationComponent.gia')"
                                    :value="(item as any)[column.key ?? '']" class="quotation-detail-input" v-on:input="($event: any) => {
                                        // let tempNum = Number($event.target.value.replaceAll(',', ''));
                                        // if (tempNum <= 1000000000000) {
                                        //     (item as any)[column.key ?? ''] = tempNum;
                                        // }
                                    }" v-on:change="($event: any) => {
                                        let tempNum = Number($event.target.value.replaceAll(',', ''));
                                        if (tempNum <= 1000000000000) {
                                            (item as any)[column.key ?? ''] = tempNum;
                                        }
                                        else{
                                            toast.error($t('ReplyQuotationComponent.toi_da_la_x_ty',{x: formatNumber(1_000)}))
                                        }
                                        $event.target.value = Intl.NumberFormat('en-ES', {
                                            minimumFractionDigits: 0,
                                            maximumFractionDigits: 2,
                                        }).format((item as any)[column.key ?? '']);
                                    }">
                            </th>
                            <th class="body-col notes" v-else-if="column.value == 'price_vat'">
                                <input v-maska data-maska-number-fraction="2" data-maska-reverse="true"
                                    inputmode="numeric" :placeholder="$t('ReplyQuotationComponent.gia_vat')"
                                    :value="(item as any)[column.key ?? '']" class="quotation-detail-input" v-on:input="($event: any) => {
                                        // let tempNum = Number($event.target.value.replaceAll(',', ''));
                                        // if (tempNum <= 1000000000000) {
                                        //     (item as any)[column.key ?? ''] = tempNum;
                                        // }
                                    }" v-on:change="($event: any) => {
                                        let tempNum = Number($event.target.value.replaceAll(',', ''));
                                        if (tempNum <= 1000000000000) {
                                            (item as any)[column.key ?? ''] = tempNum;
                                        }
                                        else{
                                            toast.error($t('ReplyQuotationComponent.toi_da_la_x_ty',{x: formatNumber(1_000)}))
                                        }
                                        $event.target.value = Intl.NumberFormat('en-ES', {
                                            minimumFractionDigits: 0,
                                            maximumFractionDigits: 2,
                                        }).format((item as any)[column.key ?? '']);
                                    }">
                            </th>
                            <th class="body-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :id="`body_long_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`"
                                :class="{
                                    'long-text': ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name?.label_key)
                                        || ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name_en?.label_key)
                                }" v-else>
                                <span>{{ (item as any)[column.key ?? ''] }}</span>
                            </th>

                        </template>
                    </tr>
                </template>
            </v-data-table>
            <div class="none-data" v-else>
                <img :src="none_list_quotations" alt="">
                {{ $t('ReplyQuotationComponent.chua_co_du_lieu') }}
            </div>

            <v-overlay location="right" :absolute="true" :contained="true" :z-index="1001" v-model="show_detail"
                content-class='primary-reply-quotation-container'>
                <div class="header-detail">
                    <div class="information">
                        <span class="quotation-name">{{ dataQuotation?.name }}</span>
                    </div>
                    <v-btn class="close-btn" v-on:click="() => {
                        show_detail = false
                    }">
                        <Icon name="iconamoon:close-circle-1-light"></Icon>
                    </v-btn>
                </div>
                <div class="body-detail">

                    <div class="data-detail" v-if="dataQuotation?.shop?.id">
                        <label> {{ $t('ReplyQuotationComponent.cua_hang_yeu_cau') }}: </label>
                        <div class="quotation-info">
                            <span id="shop_name">{{ dataQuotation?.shop?.name }} - <nuxt-link id="shop_phone"
                                    :to="`tel:${dataQuotation?.shop?.phone}`"> {{ dataQuotation?.shop?.phone }}
                                </nuxt-link></span>
                            <span id="shop_address">{{ dataQuotation?.shop?.address }}</span>
                        </div>
                    </div>
                    <div class="data-detail" v-if="dataQuotation?.supplier?.id">
                        <label> {{ $t('ReplyQuotationComponent.nha_cung_cap') }}: </label>
                        <div class="quotation-info">
                            <span id="supplier_name">{{ dataQuotation?.supplier?.name }} - <nuxt-link
                                    id="supplier_phone" :to="`tel:${dataQuotation?.supplier?.phone}`"> {{
                                        dataQuotation?.supplier?.phone }}
                                </nuxt-link></span>
                            <span id="supplier_address">{{ dataQuotation?.supplier?.address }}</span>
                        </div>
                    </div>
                    <div class="data-detail">
                        <label> {{ $t('ReplyQuotationComponent.tu') }}: </label>
                        <span class="quotation-info"> {{ moment(dataQuotation?.from).isValid() ?
                            moment(dataQuotation?.from).format("DD/MM/YYYY") : "--/--/----" }}</span>
                    </div>
                    <div class="data-detail">
                        <label> {{ $t('ReplyQuotationComponent.den') }}: </label>
                        <span class="quotation-info"> {{ moment(dataQuotation?.to).isValid() ?
                            moment(dataQuotation?.to).format("DD/MM/YYYY") : "--/--/----" }}
                        </span>
                    </div>
                    <div class="data-detail">
                        <label> {{ $t('ReplyQuotationComponent.trang_thai') }}: </label>
                        <span class="quotation-info">
                            <v-chip class="status-chip"
                                :color="dataQuotation.status == quotation_status.saved ? 'green' : dataQuotation.status == quotation_status.responsed ? 'warning' : 'default'">
                                {{ $t(`ReplyQuotationComponent.trang_thai_${dataQuotation.status}`) }}
                            </v-chip>
                        </span>
                    </div>
                    <div class="data-detail">
                        <label> {{ $t('ReplyQuotationComponent.tao_yeu_cau_luc') }}: </label>
                        <span class="quotation-info">{{ moment(dataQuotation.created_at).format('DD/MM/YYYY HH:mm')
                        }}</span>
                    </div>
                    <div class="data-detail"
                        v-if="moment(dataQuotation?.updated_at).diff(moment(dataQuotation.created_at)) > 0">
                        <label> {{ $t('ReplyQuotationComponent.cap_nhat_gan_nhat') }}: </label>
                        <span class="quotation-info">{{ moment(dataQuotation.updated_at).format('DD/MM/YYYY HH:mm')
                        }}</span>
                    </div>
                    <div class="data-detail" v-if="dataQuotation?.notes?.length">
                        <label> {{ $t('ReplyQuotationComponent.ghi_chu') }}: </label>
                        <span class="quotation-info">{{ dataQuotation?.notes }}</span>
                    </div>
                </div>
            </v-overlay>

        </div>
    </div>
</template>

<script lang="ts" setup>
import { UseClipboard } from '@vueuse/components'
import no_data_found from "~/assets/imageV2/nodata-found.png"
import none_list_quotations from "~/assets/image/list-empty-2.jpg"
import { domainImage, appConst, nonAccentVietnamese, validPhone, domain, parseDecimal2Digits, formatNumber, formatCurrency, deepCompareJsons } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { useClipboard } from '@vueuse/core'
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { HttpStatusCode } from "axios";
import { QuotationService } from "~/services/quotationService/quotationService";
import { ShopService } from "~/services/shopService/shopService";
import { DataFieldStructor, quotation_status, type MaterioDto, type SupplierDto } from "../QuotationDTO";
import { SupplierService } from "~/services/quotationService/supplierService";
import { MaterialService } from "~/services/quotationService/materialService";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var quotationService = new QuotationService();
var supplierService = new SupplierService();
var materialService = new MaterialService();
var shopService = new ShopService();

var data_field_structor = ref<DataFieldStructor>(new DataFieldStructor());

var quotation_id = ref<string>((route.params?.quotation_id as any) ?? '');
var dataQuotation = ref<any>(null);
var dataQuotationBackup = ref<any>(null);

var current_data_page = ref(1);
var item_per_page = ref(10);

var show_detail = ref(false)
var loading = ref(false);
var webInApp = ref<any>(null);

var isSaving = ref(false);

onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    Object.keys(data_field_structor.value).map(async (key: string) => {
        (data_field_structor.value as any)[key].value = key;
    });
    delete data_field_structor.value.material_id;
    await getQuotationDetail();

    useSeoMeta({
        title: `${t('ReplyQuotationComponent.phan_hoi_bao_gia')}${dataQuotation.value?.name ? (': ' + dataQuotation.value?.name) : ''}`
    })
})

onBeforeUnmount(() => {
});

function getQuotationDetail() {
    return new Promise((resolve) => {
        loading.value = true;
        quotationService.detail(quotation_id.value).then(async res => {

            if (res.status == 200) {
                res.body.data.material_quotations = res.body.data.material_quotations.map((item: any) => {
                    return {
                        ...item,
                        name: item.material?.name,
                        name_en: item.material?.name_en,
                        unit: item.material?.unit,
                        packing: item.material?.packing,
                        origin: item.material?.origin,
                        brand: item.material?.origin,
                        barcode: item.material?.barcode,
                    }
                })
                dataQuotation.value = JSON.parse(JSON.stringify(res.body.data));
                dataQuotationBackup.value = JSON.parse(JSON.stringify(res.body.data));
                loading.value = false;

                resolve(dataQuotation.value);
            }
            else {
                toast.warning(t('DetailQuotationComponent.bao_gia_khong_ton_tai'))
                loading.value = false;
                resolve(null);
            }
        }).catch(() => {
            toast.error(t('DetailQuotationComponent.co_loi_xay_ra'))
            loading.value = false;
            resolve(null);
        })
    })
}

function saveQuotation(){
    return new Promise((resolve)=>{
        isSaving.value = true;
        quotationService.update(
                dataQuotation.value.id,
                appConst.quotation_status.reply.value,
                dataQuotation.value.supplier_id,
                dataQuotation.value.name,
                dataQuotation.value.from,
                dataQuotation.value.to,
                dataQuotation.value.notes,
                dataQuotation.value.material_quotations,
            ).then(res => {
                if (res.status == HttpStatusCode.Ok) {
                    toast.success(t('ReplyQuotationComponent.gui_bao_gia_thanh_cong'));
                    setTimeout(() => {
                        close();
                    }, 1500);
                    resolve(true);
                }
                else {
                    toast.error(t('ReplyQuotationComponent.gui_bao_gia_that_bai'))
                    resolve(false);
                }
                isSaving.value = false;
                
            }).catch(err => {
                toast.error(t('ReplyQuotationComponent.gui_bao_gia_that_bai'))
                isSaving.value = false;
                resolve(false);
            })
    })
    
}

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.QuotationComponent)
}
</script>

<style lang="scss" src="./ReplyQuotationStyles.scss"></style>
