<template>
    <div class="v-stack result-item-container " :title="props.itemData?.name" ref="targetItemResult"
        :id="`filter_item_${props.itemIndex}`">

        <div class="shop-content" v-if="isVisible || true">
            <nuxt-link class="shop-content-container product-content" :prefetch="false"
                :to="appRoute.DetailShopComponent + '/' + props.itemData?.slug">
                <AvatarComponent class="shop-filter-logo" :imgTitle="props.itemData?.name"
                    :imgStyle="props.itemData?.logo?.style" :imgSrc="props.itemData?.logo
                        ? (domainImage + props.itemData?.logo.path)
                        : props.itemData?.banner
                            ? (domainImage + props.itemData?.banner.path)
                            : ''
                        " :width="100" :height="100" v-on:img_click="() => {
                            router.push(appRoute.DetailShopComponent + '/' + (props.itemData?.slug ? props.itemData?.slug : props.itemData?.id))
                        }" />
                <div class="shop-detail">
                    <div class="name-distance">
                        <p class="shop-name">{{ props.itemData?.name }}</p>
                    </div>
                    <span class="shop-address">
                        <Icon name="solar:map-point-linear"></Icon>
                        <span>
                            {{
                                props.itemData?.address ? props.itemData?.address :
                                    $t('AroundComponent.chua_cap_dia_chi')
                            }}
                        </span>

                    </span>
                    <div class="business-type-rating">
                        <span class="business-name" v-if="props.itemData?.business_types?.id">
                            {{ props.itemData?.business_types?.name }}
                        </span>

                    </div>
                    <div class="business-type-rating">
                        <span class="shop-distance">
                            {{
                                parseFloat(props.itemData?.distance)
                                    > 1000 ?
                                    (parseFloat(props.itemData?.distance) / 1000).toFixed(1) :
                                    (parseFloat(props.itemData?.distance)).toFixed(0) }}
                            <em>{{ parseFloat(props.itemData?.distance) > 1000 ? ' km' : ' m' }}</em>
                            <Icon name="solar:routing-2-bold"></Icon>
                        </span>
                        <div class="rating" v-if="props.itemData?.ratings">
                            <Icon name="solar:star-bold"></Icon> {{
                                props.itemData?.ratings ?? 0
                            }}<span>/{{
                                appConst.defaultMaxRate }}</span>
                        </div>
                    </div>

                </div>
            </nuxt-link>
            <div class="v-stack shop-products-container"
                v-if="props.itemData?.products && props.itemData?.products.length"
                v-for="itemProduct of props.itemData?.products">
                <nuxt-link :prefetch="false"
                    :to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
                    class="products-container" :title="showTranslateProductName(itemProduct)">
                    <img loading="lazy"
                        :src="itemProduct?.profile_picture?.length ? (domainImage + itemProduct?.profile_picture) : icon_for_product"
                        :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" />

                    <div class="products-content">
                        <span class="name">{{ showTranslateProductName(itemProduct) }}</span>
                        <span class="sold-like-amount">
                            <span v-if="itemProduct.sold_count">
                                {{ $t('AroundComponent.luot_ban', {
                                    count: itemProduct.sold_count ??
                                        0
                                }) }}
                            </span>
                            <span v-if="itemProduct.sold_count && itemProduct.likes">
                                &nbsp;|&nbsp;
                            </span>
                            <span v-if="itemProduct.likes">
                                {{ $t('AroundComponent.luot_thich', {
                                    count: itemProduct.likes ??
                                        0
                                }) }}
                            </span>

                        </span>
                        <div class="price-actions">
                            <div class="price-container">
                                <em class="origin-price" :class="{
                                    'hide': !(itemProduct.price_off != null && parseFloat(itemProduct.price_off) < parseFloat(itemProduct.price))
                                }">
                                    {{
                                        (parseFloat(itemProduct.price) == 0 ||
                                            parseFloat(itemProduct.price)
                                            == null)
                                            ? $t('AroundComponent.gia_lien_he')
                                            : formatCurrency(parseFloat(itemProduct.price),
                                                itemProduct.shop ?
                                                    itemProduct.shop?.currency : props.itemData?.currency)
                                    }}
                                </em>
                                <span class="price">
                                    {{
                                        (itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
                                            formatCurrency(parseFloat(itemProduct.price_off), itemProduct.shop ?
                                                itemProduct.shop.currency : itemProduct.currency) :
                                            (parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
                                                $t('AroundComponent.gia_lien_he') :
                                                formatCurrency(parseFloat(itemProduct.price), itemProduct.shop ?
                                                    itemProduct.shop?.currency : props.itemData?.currency) }} </span>

                            </div>
                            <button class="add-to-cart" v-on:click="async () => {
                                emit('item_click', JSON.parse(JSON.stringify(itemProduct)))
                                // selectedProduct = JSON.parse(JSON.stringify(itemProduct));
                                // showSelectedProduct = true;
                            }" v-on:click.stop="(e) => { e.preventDefault() }">
                                <Icon name="solar:cart-plus-linear"></Icon>
                            </button>
                        </div>
                    </div>
                </nuxt-link>
            </div>
        </div>
        <div class="shop-content loading" v-else></div>
    </div>
</template>
<style lang="scss" src="./ListAroundItemStyles.scss"></style>
<script lang="ts" setup>
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import icon_for_product from "~/assets/image/icon-for-product.png";

import { appConst, domainImage, formatCurrency, showTranslateProductName } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';


const props = defineProps({
    itemData: null,
    itemIndex: null
})

const emit = defineEmits(['item_click'])
const router = useRouter();

const observer = ref<IntersectionObserver | null>(null);

var targetItemResult = ref<HTMLElement>()
var isVisible = ref(false);

onMounted(() => {
    if (process.client) {
        observer.value = new IntersectionObserver(
            ([entry]) => {
                isVisible.value = entry.isIntersecting;
            },
        );

        if (targetItemResult.value) {
            observer.value.observe(targetItemResult.value);
        }
    }
    else{
        isVisible.value = true;
    }
});



const unobserveElement = (el: Element) => {
    if (observer.value) {
        observer.value.unobserve(el);
    }
};

onBeforeUnmount(() => {
    if (observer.value) {
        observer.value.disconnect();
    }
});
</script>