.about-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: white;

  & .about-content {
    padding: 0 10px;
    flex: 1;

    & > div{
        margin-top: 15px;
    }
  }
  & .content-title {
    text-transform: uppercase;
    font-size: 1.4em;
    border-bottom: thin solid black;
    display: flex;
    align-items: center;

    & span {
      padding: 10px 0;
      border-bottom: 3px solid var(--primary-color-1);
    }
  }

  & .content {
    font-size: 1em;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 0 0;
    color: var(--color-text-black);

    & .name {
        font-weight: 600;
        font-size: 1.2em;
    }
  }

  & .social-detail-options{
    display: flex;
    justify-content: center;
    padding: 10px 20px;

    & > .other-option.social-option{
      justify-content: flex-start;
      flex: unset;
      display: flex;
      width: auto;
      padding: 10px;
      margin: 0;
    } 

    & .social-icon{
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      object-fit: contain;
    }
  }

  & .download-app-detail-options{
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 10px 0;

    & > .other-option.download-app-option{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex: unset;
      width: auto;
      padding: 10px;
      margin: 0;
      flex: 1;
      max-width: 200px;
      background: #111111;
      border: thin solid white;
      border-radius: 15px;
      color: white;
      flex-wrap: nowrap;

      & > svg{
        font-size: 35px;
      }

      & > div{
        display: flex;
        flex-direction: column;
        line-height: 1;
        margin-left: 5px;
        // flex: 1;
        

        & > em{
          font-style: normal;
          font-size: 13px;
          white-space: nowrap;
          font-weight: 600;

          &.android{
            text-transform: uppercase;
          }
        }

        & > span{
          font-size: 20px;
          font-weight: 700;
          white-space: nowrap;
        }
      }
    } 
  }
}
