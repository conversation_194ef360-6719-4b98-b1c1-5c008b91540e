export default defineNuxtPlugin(() => {
    
    const router = useRouter();
    const localePath = useLocalePath()
  
    if (router) {
        const originalPush = router.push.bind(router)
    
        router.push = (location: any) => {
    
          if (typeof location === 'string') {
            location = localePath(location)
          } else if (location.path) {
            location.path = localePath(location.path)
          } else if (location.name) {
            location.name = localePath(location.name)
          }
          
          return originalPush(location)
        }
      }
  })
  