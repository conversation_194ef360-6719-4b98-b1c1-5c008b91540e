<template>
    <!-- <AgentCreateShopComponent/> -->
     <RegisterShopComponent :mode="'agent'" v-if="is_authorized == true"></RegisterShopComponent>
     <UnauthorizedComponent v-else-if="is_authorized == false" :message="$t('UnauthorizedComponent.khong_tao_dai_ly')"></UnauthorizedComponent>
</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import RegisterShopComponent from '~/components/myShop/registerShop/RegisterShopComponent.vue';
import { AuthService } from '~/services/authService/authService';

const nuxtApp = useNuxtApp();

var authService = new AuthService();

var is_authorized = ref<any>(null);
onBeforeMount(async ()=>{
    nuxtApp.$emit(appConst.event_key.show_footer, false);
    let author = (await authService.checkAuthorize(appConst.role_enum.agent) || await authService.checkAuthorize(appConst.role_enum.admin) || await authService.checkAuthorize(appConst.role_enum.admin2));
    is_authorized.value = author
})

</script>