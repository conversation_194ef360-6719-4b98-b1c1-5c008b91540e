.around-container {
  // width: 100%;
  position: relative;
  height: 100%;
  flex: 1;
  display: flex;
  /* min-height: inherit; */
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-view);
  background: white;
  // margin: 0;
  // overflow: auto;
  padding: 0 !important;
  // display: flex;
  flex-direction: column;
  // position: relative;
  z-index: 1;

  & > .around-content-container {
    height: 100%;
    min-height: 100%;
    flex: 1;
    background: white;
    margin: 0;
    // overflow: auto;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  & > .around-content-container.show-search {
    overflow: hidden;
  }
  // display: flex;
  // flex-direction: column;
  // flex: 1;
  // background-color: white;
  // position: relative;
  // padding: 10px;
  // overflow: auto;
  // font-size: calc(var(--font-size) * 1.3);
  // background-color: var(--color-background-2);

  & .location-and-search {
    width: 100%;
    background-size: 100%;
    // background-image: url("~/assets/image_13_3_2024/patter.jpg");
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 18px;
    transition: min-height 0.5s ease;
    .background-image {
      width: 200%;
      aspect-ratio: 1 / 1;
      position: absolute;
      background-position: 50% 50%;
      top: -150%;
      background-image: url('~/assets/image/imgpsh_fullsize_anim.jpg');
      animation: fullrotate 5s infinite ease-out;
      background-size: 100%;
      @keyframes fullrotate {
        0% {
          -webkit-transform: rotate(0);
          transform: rotate(0);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    }

    .location-text {
      padding: 5px 20px;
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      text-align: left;
      width: 100%;
      color: white;

      & > img {
        margin: auto;
        height: 40px;
        filter: drop-shadow(0 0 1px white);
      }

      & .address {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }

    & .search-bar-container {
      position: relative;
      width: calc(100% - 80px);
      top: unset;
      transform: none;
      border-radius: 2em;
      padding: 0 5px 10px;
      width: 100%;

      & > button {
        width: 40px;
        height: 100%;
        color: white;
        font-size: 1em;
        position: relative;
      }

      & > .cart-in-search {
        animation: none;
        & > em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 15px;
          height: 15px;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          position: absolute;
          bottom: 0px;
          right: 0px;
          line-height: 1;
          box-shadow: 0 0 0px 1px white;
          font-weight: 500;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        padding: 0 5px;
        box-shadow: 1px 1px 3px -1px;
        background: white;
        overflow: hidden;
        margin-left: auto;
      }

      & .search-input-container {
        height: 40px;
        border-color: transparent;
        outline: none;
        width: 100%;
      }
    }
    & .search-bar-container.search-focus {
      & .search-container {
        margin: unset;
      }
    }
    & .search-input-container {
      width: 100%;
      height: 50px;
      // background-color: #f5f6fa;
      // border-radius: 0 5px 5px 0 !important;
      border-color: transparent;
      outline: none;
    }

    & .search-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .clear-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .voice {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
      font-size: 1.25em;
    }
  }

  .location-value {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
  }

  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 40px;
    padding: 0 10px;
    aspect-ratio: 1;
    background: var(--primary-color-1);
    border-radius: 5px;
    color: white;
    border: none;
    font-size: 1.3em;
  }

  .filter-result-container.show-map {
    overflow: hidden;
  }
  .filter-result-container {
    display: flex;
    width: 100%;
    // height: 100%;
    flex: 1;
    position: relative;
  }

  #leaflet_map {
    // flex: 1;
    // align-self: unsafe;
    // height: 100%;
    // // min-height: 450px;
    // outline: none;
    z-index: 1;
    font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system, Tahoma,
      "Segoe UI";
    position: absolute;
  }

  .search-result {
    z-index: 101;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: #f4f4f4;

    & > .history-search {
      padding: 10px 15px;
      background: #fafafa;
      display: flex;
      flex-direction: column;
      gap: 10px;
      font-weight: 600;

      & > .list-history-search {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;

        & > button {
          border: thin solid #b2b2b2;
          padding: 8px 20px;
          border-radius: 10px;
          color: var(--color-text-black);
        }
      }
    }

    & > .categories-and-list-suggest {
      display: flex;
      // overflow: auto;
      position: relative;
      flex: 1;
      background: #fafafa;

      & > .categories {
        width: 15%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        background: #f4f4f4;
        height: fit-content;

        & > .item-category.active {
          border-right: 2px solid #ed1b24;
        }
        & > .item-category.active::after {
          content: "";
          width: 13px;
          height: 13px;
          background: #ed1b24;
          position: absolute;
          // border-radius: 5px 0;
          top: 50%;
          right: 0;
          transform: translate(50%, -50%) rotate(45deg);
        }
        & > .item-category {
          width: 100%;
          display: flex;
          flex-direction: column;
          background-color: white;
          padding: 10px 10px 5px;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: #626262;
          position: relative;
          overflow: hidden;

          & > img {
            width: 50px;
            height: 50px;
            aspect-ratio: 1;
            border-radius: 50%;
            background-color: #646464;
          }

          & > svg {
            width: 50px;
            height: 50px;
          }

          & > span {
            font-size: 0.8em;
          }
        }
      }
      & > .list-search-suggest-container {
        background: #fafafa;
        flex: 1;
        height: fit-content;
        min-height: 100%;
        display: flex;
        flex-wrap: wrap;

        & > .list-search-suggest {
          padding: 10px 15px;
          gap: 10px;
          flex: 1;
          height: fit-content;
          min-height: 100%;
          display: flex;
          flex-wrap: wrap;
          font-weight: 600;
          & > span {
            width: 100%;
          }
          & > .search-result-item-container {
            display: flex;
            width: calc(50% - 5px);
            align-self: normal;
            align-items: center;
            flex-direction: column;
            background: white;
            border-radius: 10px;
            color: #626262;
            box-shadow: 0 0 5px rgb(0 0 0 / 20%);

            & > img {
              border-radius: 10px 10px 0 0;
              width: 100%;
              max-height: 200px;
              aspect-ratio: 1;
              object-fit: cover;
            }

            & > .items-suggest-content {
              padding: 10px 10px 20px;
              display: flex;
              width: 100%;
              flex: 1;
              flex-direction: column;
              gap: 5px;
              justify-content: flex-end;

              & > .name {
                color: #626262;
                text-align: left;
                font-weight: 500;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
              & > .shop-name {
                display: flex;
                width: 100%;
                gap: 5px;
                align-items: baseline;
                overflow: hidden;
                font-weight: bold;
                font-style: italic;
                color: #399901;
                text-align: center;
                font-size: 0.9em;
                justify-content: center;
              }

              & > .price {
                text-align: center;
                color: #ed1b24;
                font-size: 1.3em;
                font-weight: bold;

              }

              & > .origin-price {
                text-align: center;
                color: #626262;
                font-size: 1.2em;
                text-decoration: line-through;
              }

              & > .add-to-cart {
                background: white;
                border: thin solid #ed1b24;
                color: #ed1b24;
                font-size: 1em;
                border-radius: 2em;
                padding: 5px 15px;
                font-weight: bold;
                margin-top: auto;
              }
            }
          }

          & > .empty-search,
          .search-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
          }

          & .search-placeholder > img {
            width: 200px;
            height: 200px;
            object-fit: cover;
          }
        }

        & .showed-all {
          color: #626262;
          font-style: italic;
          width: 100%;
          text-align: center;
          padding: 20px 0;
        }
      }
    }

    & > .empty-search,
    .search-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }

  .search-result.show,
  .list-result.show {
    display: flex;
  }

  .list-result {
    z-index: 2;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: fit-content;
    // overflow: auto;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: white;
    padding: 10px;
    position: absolute;
    top: 0;
    left: 0;

    & > .result-item-container {
      display: flex;
      border: thin solid transparent;
      border-radius: 10px;
      margin-bottom: 25px;

      // box-shadow: 2px 4px 8px 0 rgba(0, 0, 0, 0.15);
      gap: 10px;
      background: white;
      color: #2f3640;
      position: relative;

      & a {
        color: unset;
      }

      & .shop-content {
        display: flex;
        padding: 10px;
        border-radius: 10px;
        border: thin solid transparent;

        & > a {
          width: 100%;
          gap: 10px;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
        }
        & .shop-banner {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          box-shadow: 0 0 1em rgb(0, 0, 0, 0.1);
          aspect-ratio: 1;
          max-height: 200px;
          object-fit: cover;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          & > .logo-origin-container {
            transform: scale(calc(100 / var(--scale-origin)));
          }

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        & .shop-info {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          flex: 1;
          gap: 10px;
          text-align: left;

          & > .shop-name {
            color: var(--primary-color-1);
            font-weight: bold;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 1.1em;
          }

          & > .shop-address {
            color: var(--color-text-black);
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.9em;
          }

          & > .shop-distance {
            display: flex;
            align-items: flex-end;
            font-style: italic;
            font-size: 0.9em;
            color: #6f6e6e;
            font-weight: 600;
            white-space: nowrap;
            width: 100%;

            & > a {
              margin-left: auto;
              > button {
                padding: 0px 10px;
                color: #28282a;
                border: thin solid #7d7c82;
                border-radius: 5px;
                white-space: nowrap;
                height: fit-content;
              }
            }
            & > em{
              margin-left: 5px;
            }
          }
        }
      }
      & .shop-content:hover,
      .shop-content:active {
        background: linear-gradient(#fff7f8, #fff7f8) padding-box,
          linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
      }
      & .shop-products-container {
        width: 90%;
        margin-left: auto;

        & > a {
          width: 100%;
          gap: 10px;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
        }
        // & .product-content {
        //   width: 100%;
        //   display: flex;
        //   align-items: flex-start;
        //   justify-content: flex-start;
        //   gap: 10px;
        //   text-align: left;

        //   & img {
        //     width: 25%;
        //     aspect-ratio: 1;
        //     max-height: 200px;
        //     border-radius: 5px;
        //     object-fit: contain;
        //     background: var(--color-background-2);
        //   }

        //   & .product-info {
        //     display: flex;
        //     flex-direction: column;
        //     gap: 5px;

        //     & > .product-name {
        //       font-weight: 500;
        //       color: var(--color-text-black);
        //     }

        //     & > .product-price {
        //       font-size: 1.5em;
        //       color: var(--primary-color-1);

        //       & em.off{
        //         color: var(--color-text-note);
        //         font-size: .8em;
        //         font-style: normal;
        //         text-decoration: line-through;
        //       }
        //     }
        //   }
        // }

        & .product-detail {
          display: flex;
          flex: 1;
          gap: 10px;
          width: 100%;
          padding: 10px;
          border-radius: 10px;
          align-items: flex-start;
          line-height: normal;
          position: relative;
          border: thin solid transparent;

          & > a {
            display: flex;
            flex: 1;
            gap: 10px;

            align-items: flex-start;

            & > img {
              width: 33%;
              border-radius: 10px;
              aspect-ratio: 1;
              max-height: 200px;
              object-fit: cover;
              box-shadow: 0 0 1em rgb(0, 0, 0, 0.1);
            }

            & > .product-info {
              display: flex;
              flex-direction: column;
              text-align: left;
              align-self: stretch;
              padding: 5px 0;
              justify-content: space-between;
              flex: 1;
              overflow: hidden;

              & > .product-name {
                color: #595959;
                font-size: 1.1em;
                font-weight: bold;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                overflow: hidden;
              }
              & > .product-price-add-to-cart {
                display: flex;
                flex-wrap: wrap;
                white-space: nowrap;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;

                & > .v-stack {
                  margin-right: auto;
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  
                }
              }
              & .product-price {
                color: #ed1b24;
                font-size: 1.2em;
                font-weight: bold;
                margin-top: auto !important;
                text-overflow: ellipsis;
                width: 100%;
                overflow: hidden;
              }

              & .product-origin-price {
                color: #898989;
                font-size: 1em;
                font-weight: bold;
                text-decoration: line-through;
                text-overflow: ellipsis;
                width: 100%;
                overflow: hidden;
              }

              & .add-to-cart {
                // background: white;
                border: thin solid #ed1b24;
                color: #ed1b24;
                font-size: 1em;
                border-radius: 2em;
                padding: 5px 15px;
                white-space: nowrap;
                font-weight: bold;
                width: fit-content;
                margin-left: auto;
              }
            }
          }
        }

        & a:hover .product-detail,
        a:active .product-detail {
          background: linear-gradient(#fff7f8, #fff7f8) padding-box,
            linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
        }

        & .product-detail::after {
          content: "";
          height: 1px;
          width: 90%;
          background-color: #e5e5e5;
          position: absolute;
          bottom: 0;
          right: 0;
        }

        & .product-detail:hover::after {
          height: 0;
        }

        & .item-stack {
          width: 100%;
          height: 100%;
          border-radius: 15px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          background-color: #f0f0f0;

          & > .distance {
            position: absolute;
            top: 0;
            left: 0;
            padding: 0 10px;
            color: white;
            border-bottom: 2px solid white;
            border-right: 2px solid white;
            background: #f05976;
            border-radius: 15px 0;
            font-size: 0.9em;
          }
          & > img {
            flex: 1;
            background: var(--color-background-2);
          }

          & > .item-stack-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 10px;
            text-align: left;
            font-size: 1em;

            & > a {
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            & .name {
              font-weight: bold;
            }
            & .price-container {
              gap: 5px;
              justify-content: flex-start;
            }
            & .price {
              white-space: nowrap;
              font-weight: bold;
              width: fit-content;
              color: #f05976;
            }

            & .origin-price {
              white-space: nowrap;
              overflow: hidden;
              text-decoration: line-through;
              text-overflow: ellipsis;
              font-weight: bold;
              color: var(--color-text-note);
              overflow: hidden;
            }
          }
        }
      }
    }

    // & > .result-item-container:hover {
    //   background: linear-gradient(#fff7f8, #fff7f8) padding-box,
    //     linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
    // }

    // & > .result-item-container::after {
    //   content: "";
    //   height: 1px;
    //   width: 75%;
    //   background-color: #e5e5e5;
    //   position: absolute;
    //   bottom: 0;
    //   right: 0;
    // }

    // & > .result-item-container:hover::after {
    //   height: 0;
    // }

    & > .empty-search,
    .search-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      margin: auto;
    }

    & .search-placeholder > img {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }

    & .showed-all {
      color: #626262;
      font-style: italic;
      width: 100%;
      text-align: center;
      padding: 20px 0;
    }
  }

  .btn-leaflet {
    position: absolute;
    z-index: 1000;
  }

  .btn-leaflet-left {
    left: 10px;
  }

  .btn-leaflet-right {
    right: 10px;
  }

  .my-location-icon {
    font-size: 2em;
    color: rgb(0, 0, 0, 80%);
  }

  #fullScreen_map {
    background: white;
    z-index: 1000;
    position: absolute;
    bottom: 210px;
    color: black;
    display: flex;
    right: 10px;
    width: fit-content;
    min-width: 34px;
    padding: 9px;
    margin: 0 3px;
    font-size: 1.5em;
    cursor: pointer;
    border-radius: 3px;
  }

  .map-type-btn {
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    margin: 5px;
    width: fit-content;
    white-space: nowrap;
    height: 60px;
    min-width: 6em;
    border: 3px solid white;
    border-radius: 15px;
    cursor: pointer;
    bottom: 10px !important;
    right: 55px;
    left: auto !important;
    color: white;
  }

  .map-type-btn > span {
    margin-top: auto;
    width: 100%;
    height: 40%;
    display: flex;
    padding: 0 5px;
    justify-content: center;
    align-items: self-end;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    background-image: linear-gradient(
      to top,
      rgb(0, 0, 0, 80%),
      rgb(255, 255, 255, 0)
    );
  }

  .current-location-leaflet {
    position: absolute;
    bottom: 125px;
    right: 12px;
    background: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 0 5px rgb(0 0 0 / 20%);
  }

  .leaflet-marker-location {
    margin-left: -15px !important;
    margin-top: -40px !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  .leaflet-control-zoom {
    height: 100px !important;
    width: 40px !important;
    border-radius: 2em !important;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .leaflet-control-zoom > .leaflet-control-zoom-in {
    width: 100% !important;
    height: 50% !important;
    border-radius: inherit !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    align-items: center;
    justify-content: center;
    display: flex;
  }

  .leaflet-control-zoom > .leaflet-control-zoom-out {
    width: 100% !important;
    height: 50% !important;
    border-radius: inherit !important;
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
    align-items: center;
    justify-content: center;
    display: flex;
  }

  .detail-range-container {
    width: calc(100% - 20px);
    height: 100px;
    max-width: 500px;
    cursor: pointer;
    z-index: 1 !important;
    position: relative;
    margin: 10px 0 0 10px;
  }

  .detail-range-container.detail-range-leaflet {
    z-index: 1000 !important;
    margin: 5px;
    width: calc(100% - 10px);
    background: white;
    height: auto;
  }

  .detail-range-container > div {
    margin-right: 2em;
    background: white;
    z-index: 2;
  }

  .detail-range-container > .close {
    position: absolute;
    top: 5px;
    right: 5px;
    background: transparent;
    font-size: 2em;
    line-height: 1;
    color: #2f3640;
    z-index: 3;
  }

  .detail-range-container .detail-range {
    background: white;
    width: 100%;
    height: 100%;
    padding: 5px;
    margin: 0;
    display: flex;
    font-size: 15px;
    z-index: 1 !important;
    transition: all 1s ease-in-out;
  }

  .detail-range-container .detail-range > img {
    object-fit: cover;
    width: 30%;
    height: 95px;
  }

  .detail-range-container .detail-range .detail-range-content {
    width: 70%;
    padding: 0 5px;
    display: flex;
    flex-direction: column;
  }

  .detail-range-container .detail-range .detail-range-content .name,
  .detail-range-container .detail-range .detail-range-content .address {
    width: 100%;
  }

  .detail-range-container
    .detail-range
    .detail-range-content
    .name
    > strong
    > span {
    font-size: 1.3em;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .detail-range-container .detail-range .detail-range-content .address > span {
    font-size: 1.2em;
  }

  .detail-range-container .detail-range .detail-range-content .price {
    width: 100%;
    color: #274abb;
    font-size: 1.3em;
  }

  .detail-range-container .detail-range .detail-range-content > .type {
    height: fit-content;
  }

  .detail-range-container .detail-range .detail-range-content > .type > span {
    background: #f5f6fa;
    border-radius: 3px;
    color: #007dff;
    padding: 5px 15px;
  }

  .detail-range-map {
    background: white;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 5px;
    line-height: normal;
    margin: 0;
    padding: 0;
    display: flex;
    font-size: 15px;
    z-index: 1 !important;
    overflow: hidden;
    font-size: 0.85em;
    transition: all 1s ease-in-out;

    & img {
      object-fit: cover;
      height: 50%;
    }

    & .detail-range-content {
      height: 50%;
    }

    & span,
    strong {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      line-clamp: 2;
      overflow: hidden;
    }
  }

  .loading-more {
    background-color: transparent;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .list-map-buttons {
    position: absolute;
    left: 100%;
    top: 0;
  }

  .list-map-button {
    background: white;
    border: thin solid var(--primary-color-1);
    border-radius: 5px;
    padding: 10px;
    margin-left: 5px;
    color: var(--primary-color-1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3em;
  }

  .list-map-button:hover {
    background: var(--primary-color-1);
    color: white;
  }

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  & .shop-selected-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
  }
  & .shop-selected-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 5px;
    position: relative;
    padding: 10px 10px 20px 10px;

    & > .shop-seleted-logo {
      margin-top: -50px;
      margin-left: auto;
      margin-right: auto;
      box-shadow: 0 0 10px rgb(0, 0, 0, 20%);
      background: white;
      cursor: pointer;

      & > .logo-origin-container {
        transform: scale(calc(100 / var(--scale-origin)));
      }
      & > img {
        min-width: 100%;
        min-height: 100%;
        object-fit: cover;
      }
    }
    & > .close {
      position: absolute;
      width: fit-content;
      align-self: flex-end;
      cursor: pointer;
    }

    & > .name {
      color: var(--primary-color-1);
      font-size: 1.2em;
      font-weight: bold;
    }

    & > .distance {
      font-size: 1em;
      color: var(--color-text-note);
    }

    & > .products {
      width: 100%;
    }

    & > .address {
      color: #2f3640;
    }

    & > a {
      margin-top: 10px;
      font-weight: 600;
      color: var(--primary-color-1);
    }

    & .my-carousel {
      width: 100%;
      height: 100%;

      & img {
        width: 100%;
        height: 100%;
        min-height: 150px;
        max-height: 150px;
        object-fit: contain;
      }
    }
    & .stack-carousel {
      flex: 1;

      & .item-stack-slide {
        // margin-right: 10px;
        // padding: 0 5px !important;
        height: 100%;
        cursor: pointer;
      }

      & .item-stack-slide:last-child {
        padding-right: 0;
      }
    }
    & .item-stack {
      width: 100%;
      height: 100%;
      border-radius: 15px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background-color: #f0f0f0;

      & > .distance {
        position: absolute;
        top: 0;
        left: 0;
        padding: 0 10px;
        color: white;
        border-bottom: 2px solid white;
        border-right: 2px solid white;
        background: #f05976;
        border-radius: 15px 0;
        font-size: 0.9em;
      }
      & > img {
        flex: 1;
        background: var(--color-background-2);
      }

      & > .item-stack-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px;
        text-align: left;
        font-size: 1em;

        & > a {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & .name {
          font-weight: bold;
        }
        & .price-container {
          gap: 5px;
          justify-content: flex-start;
        }
        & .price {
          white-space: nowrap;
          font-weight: bold;
          width: fit-content;
          color: #f05976;
        }

        & .origin-price {
          white-space: nowrap;
          overflow: hidden;
          text-decoration: line-through;
          text-overflow: ellipsis;
          font-weight: bold;
          color: var(--color-text-note);
          overflow: hidden;
        }
      }
    }
  }
  & .my-carousel {
    width: 100%;
    height: 100%;

    & img {
      width: 100%;
      height: 100%;
      min-height: 150px;
      max-height: 150px;
      object-fit: contain;
    }
  }
  & .stack-carousel {
    flex: 1;

    & .item-stack-slide {
      // margin-right: 10px;
      // padding: 0 5px !important;
      height: 100%;
      cursor: pointer;
    }

    & .item-stack-slide:last-child {
      padding-right: 0;
    }
  }

  & .show-selected-product-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 20%);
  }
}

.empty-container {
  flex: 1;
  padding: 15px;
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-image {
  margin: 0px 10px;
  justify-content: center;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  object-fit: contain;
}

.empty-text {
  margin: 10px 0 0;
  font-size: 1.5em;
  text-align: center;
  color: var(--primary-color-1);
}

.go-map-and-zoom-out {
  cursor: pointer;
  margin: 10px 0 0;
  font-weight: bold;
  font-size: 1.1em;
  text-align: center;
  color: var(--primary-color-1);
}

.shop-logo {
  width: 40px !important;
  height: 40px !important;
  aspect-ratio: 1/1;
  // height: 100% !important;
  object-fit: cover;
  border-radius: 2em;
  // outline: 2px solid var(--primary-color-1);
  box-shadow: 0 0 0 2px var(--primary-color-1);
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;

  & > .logo-origin-container {
    transform: scale(calc(40 / var(--scale-origin)));
  }

  & > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.auto-click{
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  display: none;
}