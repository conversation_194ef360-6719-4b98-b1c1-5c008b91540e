.create-order-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-view);
  margin: auto;
  display: flex;
  flex-direction: column;
  background: #f5f4f9;
  font-size: 1.2em;
  overflow: auto;

  & .sub-title-v2-header{
    height: 50px;
    padding: 0 10px;

    & .header-create-order{
      line-height: normal;

      & h4{
        font-size: 17px;
        color: #70ff33;
      }
    }
  }

  & > .create-order-content-container {
    display: flex;
    flex-direction: column;
    background: #f5f4f9;
    font-size: 17px;

    & > .add-product-to-order-button {
      background-color: white;
      color: var(--primary-color-1);
    }

    & > .receiver-info {
      display: flex;
      background: white;
      margin-bottom: 10px;
      padding: 10px 15px;
      align-items: flex-start;
      font-size: 1rem;
      cursor: pointer;

      & > svg {
        color: var(--primary-color-1);
        font-size: 20px;
        margin-top: 5px;
      }

      & > .user-info {
        padding-left: 15px;
        display: flex;
        flex-direction: column;
        color: #19191b;
        font-weight: 600;
        font-size: 1em;
        flex: 1;

        & > .address {
          color: #9e9da2;
          font-weight: 500;
          font-size: 0.9em;
        }
        & > .address.error,
        .name-phone > .error {
          color: var(--primary-color-2);
        }
      }

      & > .select-receiver {
        display: flex;
        margin-left: auto;
        color: #7b7b7b;
        align-self: center;
        align-items: center;
        height: 100%;
      }
    }
    & .label-content {
      font-size: 1em;
      color: #2e2d30;
      display: flex;
      align-items: center;
      font-weight: 600;
      & > svg {
        color: var(--primary-color-1);
        font-size: 20px;
      }

      & > span {
        padding-left: 15px;
      }

      & > .right-button {
        margin-left: auto;
        font-weight: 600;
        color: var(--primary-color-1);
      }
    }
    & > .order-items {
      display: flex;
      flex-direction: column;
      padding: 10px 15px;
      background: white;
      flex: 1;
      margin-bottom: 10px;
      & > .order-item-container.odd {
        background: #f5f6fa;

        & input {
          background: white !important;
        }
      }
      & > .order-item-container {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 10px;
        margin-top: 10px;
        color: black;
        gap: 10px;
        border-radius: 10px;

        & > .checkbox {
          padding-top: 10px;
          & > svg.checked {
            color: var(--primary-color-1);
          }
          & > svg {
            color: #cfced3;
            font-size: 30px;
          }
        }
        & > img {
          width: 100px;
          height: 100px;
          aspect-ratio: 1;
          border-radius: 10px;
          object-fit: cover;
          background: var(--color-background-2);
        }

        & > .item-order-detail {
          align-items: flex-start;
          gap: 5px;
          flex: 1;
          overflow: hidden;

          & > .item-order-product-name {
            font-size: 17px;
            font-weight: 600;
            gap: 10px;
            color: var(--color-text-black);

            & .price {
              font-size: 15px;
              color: var(--primary-color-1);

              & > em.off {
                font-size: 13px;
                color: #7b7b7b;
                text-decoration: line-through;
              }
            }
          }

          & > .product-price {
            color: #828187;
            font-weight: 600;
            font-size: 20px;

            & > em {
              color: var(--color-text-note);
              text-decoration: line-through;
              font-weight: 500;
              font-size: 0.8em;
              margin-left: 5px;
              font-style: normal;
            }
          }

          & > .product-notes {
            color: #828187;
            font-style: italic;
          }

          & > .item-order-quantity {
            display: flex;
            align-items: center;
            width: 100%;

            & > span {
              margin: 0 10px;
            }

            & > .delete-item-order {
              margin-left: auto;
              color: var(--primary-color-1);
              align-self: flex-end;
            }

            .price-input {
              // border: thin solid var(--color-text-note);
              border-width: 0 0 1px 0;
              width: 50px;
              overflow: hidden;
              text-align: center;
              font-weight: 600;
              margin: 0 5px;
              outline: none;
              display: flex;
            }
          }

          & .price-quantity {
            justify-content: space-between;
            flex: 1;
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
            width: 100%;

            & > .quantity {
              align-items: center;
              gap: 0;
              flex: 1;
              width: 100%;

              & > .quantity-input {
                width: inherit;
                flex: 1;
                padding: 0;
                height: fit-content;
                max-width: 100px;
                border-bottom: none;
                background: transparent !important;
                text-align: center;
                font-weight: 500;
              }

              & > .quantity-button {
                color: #2e2d30;
                border: 2px solid;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 25px;
                height: 25px;
                padding: 0;
                font-size: 1.3em;
              }

              & > .quantity-button:disabled {
                opacity: 0.5;
              }

              & > .quantity-button:disabled {
                opacity: 0.5;
              }

              & > .delete-item {
                color: var(--primary-color-1);
                font-weight: 600;
                margin-left: auto;
              }
            }
            & .price-input-container {
              display: flex;
              align-items: center;
              justify-content: center;
              flex-wrap: wrap;
              gap: 5px;
              flex: 1;
              width: fit-content;
              max-width: 300px;
              text-align: center;

              & > .price-input {
                color: var(--primary-color-2);
                font-weight: 500;
                text-align: center;
                padding: 5px 10px;
                background: #f5f6fa;
                border-radius: 7px;
                border: none;
                height: 30px;
              }
              & > .price {
                color: var(--primary-color-1);
                font-size: 0.8em;
              }
            }
          }

          & > .note-input {
            background: #f5f6fa;
            border-radius: 7px;
            color: #2e2d30;
            padding: 5px 10px;
            outline: none;
            margin-top: 5px;
            width: 100%;
          }
        }
      }
    }

    & > .payment-method,
    > .note-container {
      display: flex;
      padding: 10px 15px;
      margin-bottom: 10px;
      background: white;
      align-items: flex-start;

      & > .icon-label {
        font-size: 20px;
        color: var(--primary-color-1);
        padding-top: 5px;
      }

      & > .content-container {
        padding-left: 15px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        & > .label {
          font-size: 1em;
          color: #2e2d30;
          font-weight: 600;
        }

        & > .content {
          color: #828187;
          font-size: 0.9em;
          width: 100%;

          & > .content-item {
            display: flex;
            gap: 5px;
            margin-right: auto;
            justify-self: stretch;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;

            & > svg {
              color: #e8e7e7;
              font-size: 20px;
            }
          }

          & > .content-item.active {
            color: #2e2d30;

            & > svg{
              color: var(--primary-color-1);
            }
          }
        }

        & > .note-order-input-container {
          width: 100%;
          background-color: var(--color-background-2);
          border-radius: 5px;

          & > .note-order-input {
            padding: 10px;
            width: 100%;
            outline: none;
            resize: none;
          }
        }
      }

      & .delivery-partners-content-container {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        flex: 1;
        width: 100%;

        & .delivery-partners-content{
          color: #545454;
          font-size: 14px;
          width: 100%;
          &>.partner-logo {
            height: 25px;
            margin-top: 5px;
            margin-right: 10px;
          }
        }

        & .change-partner {
          font-size: 13px;
          font-style: italic;
          margin-left: 10px;
          margin-top: 5px;
          font-weight: 700;
          color: var(--primary-color-1);
        }

        & .service-type {
          margin-top: 10px;

          &>em {
            font-weight: 400;
            color: var(--primary-color-1);
            margin-left: 10px;
          }
        }

        & .list-options-delivery-price {
          display: flex;
          flex: 1;
          justify-content: space-evenly;
          flex-wrap: wrap;
          width: 100%;

          & .delivery-price-option {
            display: flex;
            gap: 3px;
            margin: 5px auto 0 0;
            justify-self: stretch;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            color: #2e2d30;
            animation: none;

            &:disabled {
              opacity: .6;
            }

            &>svg {
              color: #e8e7e7;
              font-size: 20px;
            }

            & em {
              color: var(--primary-color-1);
              font-weight: 700;
            }
          }

          &>.delivery-price-option.selected>svg {
            color: var(--primary-color-1);
          }
        }

        & .non-delivery-partners {
          font-size: 15px;
          color: var(--primary-color-2);
          font-weight: 600;
        }
      }
    }

    & > .payment-info-container {
      padding: 15px 15px 0;
      display: flex;
      flex-direction: column;
      background-color: white;
      font-size: 17px;
      // margin-bottom: 10px;

      & > .payment-info-content {
        justify-content: space-between;
        align-items: flex-start;
        display: flex;
        padding: 10px 0;
        font-weight: 500;
        color: #828187;
        border-bottom: thin solid #ebeaef;
      }
      & > .payment-info-content > .value {
        font-size: 1em;
        color: #2e2d30;
        font-weight: 500;
      }
      & > .payment-info-content.off > .value {
        font-size: 1em;
      }
      & > .payment-info-content.total-left {
        // border-bottom: thin solid #88888a;
        color: #2e2d30;
        & > .value {
          font-weight: bold;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          font-size: 1.1em;

          & > .note {
            font-size: 1rem;
          }
        }
      }

      & > .payment-info-content .distance {
        font-style: italic;
      }
    }

    & > .order-footer {
      position: sticky;
      bottom: 0;
      left: 0;
      padding: 0 15px;
      background-color: white;
      font-size: 17px;

      & > .order-footer-content {
        border-top: thin solid #88888a;
        padding: 15px 0;
        align-items: center;
        justify-content: space-between;
        color: white;
        font-size: 1em;
        margin-top: auto;
        font-weight: 500;
        display: flex;
        & > .total {
          color: #575757;
          font-size: 1em;
          display: flex;
          flex-direction: column;
          & > span {
            font-size: 0.9em;
            color: #828187;
          }
          & > .price {
            color: var(--primary-color-1);
            font-weight: bold;
            font-size: 20px;
          }

          & > .none-price {
            color: var(--primary-color-1);
            font-weight: 500;
          }
        }

        & > button {
          width: 150px;
          height: 100%;
          margin-left: auto;
          border-radius: 5px;
          background-color: var(--primary-color-1);
          padding: 10px;
          color: white;
          font-weight: 500;
          border: none;
          gap: 5px;
          font-size: 1em;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

.add-product-to-create-order-modal {
  z-index: 1000;
  height: 90dvh !important;
  height: 88vh;
  width: 90%;
  border-radius: 10px;
  margin: auto;

  & .add-product-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    flex-direction: column;
    height: 100%;
  }

  & .search-bar {
    width: 100%;
    background: #f5f6fa;
    padding: 5px 10px;
    border-radius: 5px;
    gap: 5px;
  }

  & .search-input-container {
    width: 100%;
    background-color: #f5f6fa;
    border-radius: 0 !important;
    border-color: transparent;
    outline: none;
    height: 100%;
    font-size: var(--font-size);
  }

  & .none-search-result {
    margin: 10px 0;
    justify-content: center;
    border-radius: 50%;
    width: 250px;
    height: 250px;
    object-fit: contain;
    margin: auto;
  }

  & .products {
    overflow: auto;
    flex: 1;
    width: 100%;
  }

  & .product-result-item.disabled {
    opacity: 0.5;
  }

  & .product-result-item.odd {
    background: #f5f6fa;
  }

  & .product-result-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    font-size: 1.2em;
    padding: 5px;
    cursor: pointer;

    & .my-checkbox {
      width: 25px;
      min-width: 25px;
    }

    & > .product-detail {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 5px;
      flex: 1;

      & > img {
        width: 100px;
        height: 100px;
        min-width: 100px;
        object-fit: cover;
        border-radius: 10px;
      }

      & > .v-stack {
        & > span {
          font-size: 17px;
          color: var(--primary-color-1);
          font-weight: 600;
        }

        & > em {
          font-size: 15px;
          color: var(--color-text-note);
          font-weight: 600;
        }
      }
    }
  }

  & .actions {
    width: 100%;
    gap: 5px;
    padding: 5px;
    justify-content: space-between;
    border-top: thin solid #ccc;
    position: sticky;
    bottom: 0;
    background: white;
    margin-top: auto;

    & > .clear-selected-button {
      border: thin solid var(--color-button-special);
      color: var(--color-button-special);
      border-radius: 2em;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 15px;
      margin-left: auto;
      background-color: white;
      line-height: 1;
    }

    & > .accept-button {
      border: thin solid var(--primary-color-1);
      width: fit-content;
      padding: 5px 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      height: auto;
      font-size: 1em;
    }
  }
}

.add-product-to-order-backdrop {
  z-index: 1000;
}
