<template>
	<div class='v-stack product-not-found-container'>
		<img loading="lazy" :src='page_not_found' :placeholder="page_not_found"
			:alt="$t('Product404Component.khong_tim_thay_san_pham')" />
		<span>
			{{ $t('Product404Component.khong_tim_thay_san_pham') }}
		</span>
	</div>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import page_not_found from "~/assets/image/page-not-found.png"
const router = useRouter();
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.Product404Component'),
});
</script>
<style lang="scss" src="./Product404Styles.scss"></style>