.clear-search-history-container {
  padding: 10px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  justify-content: center;
  align-items: center;

  & > span {
    width: 100%;
    text-align: center;
    font-size: 20px;
  }

  & > .clear-actions {
    display: flex;
    justify-content: space-evenly;
    width: 100%;

    & > button {
      width: 40%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 0;
      border-radius: 5px;
      color: black;
      font-weight: 400;
      border: thin solid;
      box-shadow: 0 4px 4px 0 rgb(0, 0, 0, 20%);
    }

    & > button.accept {
      background: var(--primary-color-1);
      border: thin solid var(--primary-color-1);
      color: white;
    }
  }
}