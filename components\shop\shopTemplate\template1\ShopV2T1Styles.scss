.public-container#shop_content_container_1 {
  background: transparent;
  // padding-top: 30px;
  // margin-top: -30px;
  // background-image: url("~/assets/imageV2/shop-bg.jpg");
  background-image: var(--back-image);
  background-size: cover;
  background-position: center;
  // animation: scaleBackground 1s ease-in-out;
  @keyframes scaleBackground {
    from {
      transform: scale(1.3);
    }

    to {
      transform: none;
    }
  }

  @media screen {
    scrollbar-width: auto;
    /* Firefox */
    scrollbar-color: transparent;

    &::-webkit-scrollbar-thumb {
      background: var(--temp-color-5);
      border-radius: 5px;
      z-index: 10;
    }

    &::-webkit-scrollbar {
      width: 5px;
      height: 0;
      z-index: 1;
    }
  }

  // &::before {
  //   content: "";
  //   position: fixed;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 100%;
  //   pointer-events: none;
  //   // background-image: var(--back-image);
  //   background-image: url("~/assets/imageV2/shop-bg.jpg");
  //   background-size: cover;
  //   background-position: center;
  //   animation: scaleBackground 1s ease-in-out;
  //   z-index: -1;
  //   // filter: blur(15px);

  // }

  & .scroll-top-button {
    position: fixed;
    bottom: 70px;
    right: 25px;
    z-index: 10;

    & > button.go-up {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      // font-size: 30px;
      font-size: calc(30px + var(--font-resize));
      background: var(--temp-color-5);
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      z-index: 100;
      min-width: unset;
    }
  }

  & .shop-actions-speed-dial {
    position: absolute;
    bottom: 120px;
    right: 25px;
    z-index: 10;

    @media screen and (max-width: 500px) {
      display: none;
    }

    & button.shop-actions-button {
      padding: 0;
      width: 50px;
      height: 50px;
      // font-size: 30px;
      font-size: calc(30px + var(--font-resize));
      min-width: unset;
      color: var(--temp-color-2);
      border-radius: 50%;
    }

    & button:not(.shop-actions-button) {
      display: flex;
      align-items: center;
      flex: 1;
      border-radius: 2em;
      color: white;
      padding: 0;
      width: 35px;
      min-width: unset;
      height: 35px;
      min-height: 35px;

      & > .v-btn__content {
        border-radius: 2em;
        display: flex;
        align-items: center;
        justify-content: center;
        text-transform: uppercase;

        & > a,
        > span {
          font-weight: 700;
          // font-size: 15px;
          font-size: calc(15px + var(--font-resize));
          width: fit-content;
          max-width: 200px;
          flex: 1;
          padding: 5px;
          gap: 5px;
          height: 35px;
          white-space: nowrap;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      & svg {
        min-width: 20px;
        // height: 100%;
      }

      & span {
        // flex: 1;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        line-height: normal;
      }
    }

    & button.call-to-shop {
      background: var(--temp-color-2);
    }

    & button.chat-to-shop {
      background: var(--temp-color-3);
    }
  }

  & > .shop-paralax {
    position: absolute;
    top: 0;
    left: 0;
    // width: 100%;
    max-width: unset;
  }

  & .scroll-delay-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-width: unset;
    pointer-events: none;
    // background-image: var(--back-image);
    // background-image: url();
    background-size: cover;
    background-position: top left;
    transform: translateY(0);
    animation: scaleBackground 1s ease-in-out;
    // transition: all .1s ease-in-out;
    filter: brightness(0.5);
    will-change: transform;
  }
}

.shop-v2-t1-container {
  --banner-aspect-ratio: 2.2;
  will-change: transform;
  height: 100%;
  flex: 1;
  // background: #f4f4f4;
  background: transparent;
  margin: 0;
  overflow: auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-width: unset !important;
  // max-width: var(--max-width-content-view-1024) !important;

  & .search-input-container {
    display: flex;
    padding: 5px;
    gap: 2px;
    background: linear-gradient(to right, #00a873, var(--linear-color-1));
    height: 45px;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    max-width: var(--max-width-content-view-1024) !important;
    margin: 0 auto;
    transition: all 0.1s cubic-bezier(0.075, 0.82, 0.165, 1);

    &.top-55 {
      top: 55px;
      transition: all 0.3s ease !important;
    }

    .cart-button {
      color: white;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      & > svg {
        width: 30px;
      }

      & > .count {
        position: absolute;
        top: 3px;
        right: -2px;
        padding: 0 5px;
        border-radius: 2em;
        background-color: #cc313a;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        // font-size: 12px;
        font-size: calc(12px + var(--font-resize));
      }
    }

    & > .back-button {
      width: 30px;
      height: 30px;
      color: white;
      // font-size: 26px;
      font-size: calc(26px + var(--font-resize));
      display: flex;
      justify-content: center;
      align-items: center;
      align-self: center;
    }
  }

  & .loading-skeleton {
    gap: 10px;
    background: transparent;

    .h-stack,
    .v-stack {
      padding: 0 10px;
      gap: 5px;
      display: flex;
      align-items: flex-start;
      background: transparent;
    }

    & > .banner-skeleton {
      width: 100%;
      aspect-ratio: 2;
      max-height: 360px;
    }

    & .avt-skeleton {
      width: 75px;
      min-width: 75px;
      height: 75px;
      border-radius: 5px;
    }

    & .info-placeholder {
      display: flex;
      gap: 5px;
      flex: 1;
      width: 100%;
      padding: 0;
      background: transparent;

      & .info-skeleton {
        width: 100%;
        height: 100%;
        padding: 0;
        margin: -10px 0 0;
        background: transparent;
      }

      & .actions-skeleton {
        display: flex;
        flex-direction: row;
        flex: 1;
        width: 100%;
        background: transparent;
        border-radius: 2em;
        margin-top: -10px;
        z-index: 10;

        & > .action-skeleton {
          height: 25px;
          width: 100px;
          border-radius: 2em;
          margin-left: 15px;
          justify-content: flex-start;
          background: transparent;
        }
      }
    }
  }

  .shop-v2-content-container {
    width: 100%;
    height: 100%;
    flex: 1 1;
    /* min-height: inherit; */
    /* border-radius: 10px; */
    max-width: var(--max-width-content-view-1024);
    // background: white;
    background: transparent;
    margin: auto;
    display: flex;
    flex-direction: column;
    // font-size: 20px;
    font-size: calc(20px + var(--font-resize));
    position: relative;

    & > .shop-content-container {
      // font-size: 20px;
      font-size: calc(20px + var(--font-resize));
      position: relative;
      // background: #f4f4f4;
      background: transparent;
      flex: 1;
      padding-top: 15px;

      & > .banner-container {
        --banner-max-width: 100%;
        width: var(--banner-max-width);
        // margin: 20px auto 0;
        // padding: 0 10px;
        border-radius: 0 0 13px 13px;
        animation: fadeDown 1s ease-in-out;

        & > .banner-origin-container {
          border-radius: inherit;

          & > img {
            border-radius: inherit;
          }
        }
      }

      .slogan {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        overflow: hidden;
        padding: 10px;
        width: 100%;
        text-align: center;
        color: #ffd587;
        animation: fadeIn 0.75s ease-in-out;
        font-weight: 700;
        text-shadow: 0 0 1px white;
        // background: var(--secondary-color-1);

        & > svg {
          min-width: 25px;
          margin-top: 3px;
          // animation: showRide 2s infinite reverse ease-in-out;
        }

        .text {
          // font-size: 20px;
          font-size: calc(20px + var(--font-resize));
          // background-image: linear-gradient(to right, var(--temp-color-1), var(--temp-color-2), #0575e6, var(--linear-color-1));
          background-clip: text;
          color: #ffd587;
          background-position: 0%;
          background-size: 300%;
          width: auto;
          margin: 0 15px;
          max-width: 100%;
          font-weight: 700;
          font-style: italic;
          font-family: "Montserrat", "Nunito", "Mulish", "Roboto", sans-serif,
            -apple-system, Tahoma, "Segoe UI", monospace !important;
          // animation: showRide 2s infinite ease;
        }
      }

      @keyframes fadeIn {
        from {
          transform: translateY(10px);
          opacity: 0;
        }

        to {
          transform: none;
          opacity: 1;
        }
      }

      @keyframes fadeDown {
        from {
          transform: translateY(-150px);
          opacity: 0.5;
        }

        to {
          transform: none;
          opacity: 1;
        }
      }

      & > .shop-detail-container {
        display: flex;
        flex-direction: column;
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        padding: 0 15px 10px;
        gap: 15px;
        animation: fadeDown 1s ease-in-out;
        // margin-top: 150px;
        // margin-bottom: 30px;
        // background: rgb(255,255,255,.9);
        // border-radius: 20px;

        & > .shop-logo {
          position: relative;
          border-radius: 50%;
          border: 3px solid white;
          align-self: flex-start;
          box-shadow: none;
          margin: auto;
          margin-top: 150px;
          box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
        }

        & > .shop-detail-content {
          display: flex;
          flex-direction: column;
          // font-size: 17px;
          font-size: calc(17px + var(--font-resize));
          line-height: 1.125;
          flex: 1;
          justify-content: center;
          align-items: center;
          gap: 5px;

          & > .name-share {
            display: flex;
            align-items: center;
            overflow: hidden;
            gap: 5px;
            font-weight: bold;

            & > .name {
              display: flex;
              gap: 2px;
              align-items: center;
              // font-size: 35px;
              font-size: calc(35px + var(--font-resize));
              color: white;
              text-align: center;

              & > span {
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              & > svg {
                width: 20px;
                min-width: 20px;
                align-self: flex-start;
              }
            }
          }

          & > .share {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: auto;
            // font-size: 24px;
            font-size: calc(24px + var(--font-resize));
            align-self: flex-start;
          }

          & > .follows {
            // color: rgb(255, 255, 255, .75);
            color: white;
            // font-size: 15px;
            font-size: calc(15px + var(--font-resize));
            font-weight: 400;

            & > em {
              font-style: normal;
              font-weight: bold;
            }
          }

          & > .is-open {
            // font-size: 17px;
            font-size: calc(17px + var(--font-resize));
            font-weight: 700;
            margin: 5px 0;
            width: auto;
            max-width: max-content;
            color: white;

            &.closed {
              & > em.close {
                display: flex;
                flex-direction: column;
                padding: 0;
                background: transparent;

                & > span {
                  padding: 3px 15px;
                  border-radius: 2em;
                  white-space: nowrap;
                  height: auto;
                  max-height: max-content;
                  width: auto;
                  align-self: center;
                  background-color: var(--primary-color-2);
                  color: white;
                  // font-size: 15px;
                  font-size: calc(15px + var(--font-resize));
                  // background: linear-gradient(to right, var(--temp-color-2) 50%, transparent);
                  z-index: 2;
                }

                & > span.reason {
                  padding: 15px 25px 10px;
                  margin-top: -10px;
                  border-radius: 10px;
                  border: thin solid;
                  // font-size: 13px;
                  font-size: calc(13px + var(--font-resize));
                  // margin-left: -40px;
                  color: var(--primary-color-2);
                  white-space: break-spaces;
                  z-index: 1;
                  width: 100%;
                  text-align: center;
                  // background: linear-gradient(to right, white 80%, transparent);
                  background: white;
                }

                // background: linear-gradient(to right, var(--temp-color-2) 80%, transparent);
              }
            }

            & > em {
              border-radius: 2em;
              padding: 2px 15px;
              width: auto;
              white-space: normal;
              background-color: var(--primary-color-1);
              // font-size: 15px;
              font-size: calc(15px + var(--font-resize));
              text-shadow: 0 0 white;
            }
          }

          & > .open-time {
            font-weight: 600;
            // font-size: 20px;
            font-size: calc(20px + var(--font-resize));
            & > em {
              color: var(--primary-color-2);
            }
          }

          & > .actions {
            display: flex;
            justify-content: center;
            position: relative;
            margin-top: 5px;
            flex-wrap: wrap;
            gap: 7px 2px;

            & > .action-button {
              border-radius: 2em;
              color: white;
              font-weight: 600;
              margin-right: 3px;
              // font-size: 15px;
              font-size: calc(15px + var(--font-resize));
              white-space: nowrap;
              display: flex;
              align-items: center;
              justify-content: center;
              text-transform: none;
              gap: 1px;
              // padding: 2px 15px;
              height: auto;
              margin: 0 5px;

              &.direction {
                background: var(--temp-color-1);
                padding: 2px 10px 2px 5px;
                line-height: 1.125;
                border: thin solid;

                & svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }
              }

              &.follow {
                // background: linear-gradient(to right,
                //     var(--temp-color-1),
                //     var(--linear-color-1));
                padding: 2px 10px 2px 2px;
                line-height: 1.125;
                // color: var(--temp-color-1);
                color: white;
                // background: white;
                margin-right: 0;
                // box-shadow: inset 0 0 0 1px var(--temp-color-1);

                & svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }

                & > span {
                  padding: 0 5px;
                }
              }

              &.follow.followed {
                color: var(--temp-color-1);
                // box-shadow: inset 0 0 0 1px var(--temp-color-1);
                // padding: 2px 10px;
              }

              &.share {
                padding: 2px 7px 2px 2px;
                color: white;

                & svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }

                & > span {
                  padding: 0 5px;
                }
              }
            }
          }
        }
      }

      & > .advanced-detail {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        flex-wrap: wrap;
        row-gap: 10px;

        & > .shop-detail-open-time {
          // padding: 10px;
          border-radius: 13px;
          background: var(--temp-color-4);
          // background: white;
          display: flex;
          flex-direction: column;
          margin: 5px;
          animation: fadeIn 0.5s ease-in-out;
          width: 100%;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            width: calc(100% - 25px);
            height: 10px;
            border-radius: 0 0 10px 10px;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-50%);
            left: 50%;
            top: 100%;
            z-index: 0;
          }

          @media screen and (min-width: 1025px) {
            width: 32%;
            flex: 1;
          }

          & > .label {
            padding: 10px;
            // font-size: 17px;
            font-size: calc(17px + var(--font-resize));
            color: var(--temp-color-2);
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;

            & > svg {
              width: 21px;
              height: 21px;
              min-width: 21px;
              color: #d9d9d9;
            }
          }

          & > .line {
            margin: 0;
            height: 1px;
            background-color: #ececec;
          }

          & > .list-open-time {
            display: flex;
            flex-direction: column;
            gap: 5px;
            // font-size: 17px;
            font-size: calc(17px + var(--font-resize));
            padding: 10px;

            & > .item-open-time {
              display: flex;
              align-items: flex-start;
              // justify-content: space-between;

              & > .days {
                // font-size: 15px;
                font-size: calc(15px + var(--font-resize));
                color: var(--temp-color-1);
                font-weight: 400;
                width: 40%;
                line-height: 1.125;
              }

              & > .times {
                // font-size: 17px;
                font-size: calc(17px + var(--font-resize));
                color: var(--temp-color-1);
                font-weight: 700;
                margin-left: auto;
                line-height: 1.125;
              }
            }
          }
        }

        & > .shop-detail-notification {
          // padding: 10px;
          background: var(--temp-color-4);
          // background: white;
          display: flex;
          flex-direction: column;
          margin: 5px;
          border-radius: 13px;
          animation: fadeIn 0.5s ease-in-out;
          width: 100%;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            width: calc(100% - 25px);
            height: 10px;
            border-radius: 0 0 10px 10px;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-50%);
            left: 50%;
            top: 100%;
            z-index: 0;
          }

          @media screen and (min-width: 1025px) {
            width: 32%;
            flex: 1;
          }

          & > .label {
            padding: 10px;
            // font-size: 15px;font-size: calc(30px + var(--font-resize));
            font-size: calc(17px + var(--font-resize));
            color: var(--temp-color-2);
            font-weight: 700;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            text-transform: uppercase;

            & > svg {
              width: 25px;
              height: 25px;
              min-width: 25px;
              color: var(--temp-color-2);
              margin-right: 5px;
            }

            // & span {
            //   line-height: 1;
            // }
          }

          & > .line {
            margin: 0;
            height: 1px;
            background-color: #ececec;
          }

          & > .notification-content {
            flex: 1;
            padding: 10px;
            // font-size: 16px;
            font-size: calc(16px + var(--font-resize));
            white-space: break-spaces;
            color: var(--temp-color-1);
            font-weight: 700;
          }
        }

        & > .shop-detail-delivery-promotion {
          padding: 0;
          background: var(--temp-color-4);
          // background: white;
          display: flex;
          flex-direction: column;
          margin: 5px;
          border-radius: 13px;
          animation: fadeIn 0.5s ease-in-out;
          width: 100%;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            width: calc(100% - 25px);
            height: 10px;
            border-radius: 0 0 10px 10px;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-50%);
            left: 50%;
            top: 100%;
            z-index: 0;
          }

          @media screen and (min-width: 1025px) {
            width: 32%;
            flex: 1;
          }

          & > .label {
            padding: 10px;
            // font-size: 15px;
            font-size: calc(17px + var(--font-resize));
            color: var(--temp-color-2);
            font-weight: 800;
            display: flex;
            justify-content: flex-start;
            text-transform: uppercase;
            align-items: center;

            & > svg {
              width: 25px;
              height: 25px;
              min-width: 25px;
              color: var(--temp-color-2);
              margin-right: 5px;
            }
          }

          & > .line {
            margin: 0;
            height: 1px;
            background-color: #ececec;
          }

          & > .list-delivery-promotion {
            padding: 5px;

            & .delivery-promotion-item-slide {
              max-width: 90dvw;
              padding: 10px;
              background: color-mix(
                in srgb,
                var(--temp-color-5) 10%,
                transparent
              );
              display: flex;
              gap: 10px;
              border-radius: 10px;
              height: auto;
              width: 300px !important;

              & > .value-tag {
                height: 50px;
                min-width: 75px;
                width: 75px;
                // font-size: 20px;
                font-size: calc(20px + var(--font-resize));
                align-items: center;
                justify-content: center;
                display: flex;
                color: white;
                font-weight: 800;
                position: relative;

                & > img {
                  height: 50px;
                  width: 75px;
                  min-height: unset;
                  z-index: 1;
                  left: 0;
                  position: absolute;
                }

                & > span {
                  z-index: 2;
                }
              }

              & > .promotion-detail {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-start;
                line-height: normal;

                & > .name {
                  // font-size: 15px;
                  font-size: calc(15px + var(--font-resize));
                  color: var(--temp-color-1);
                  font-weight: 800;
                }

                & > .description {
                  // font-size: 14px;
                  font-size: calc(14px + var(--font-resize));
                  color: var(--temp-color-1);
                  font-weight: 500;
                  font-style: italic;
                }

                & > .max-value {
                  // font-size: 13px;
                  font-size: calc(13px + var(--font-resize));
                  font-weight: 700;
                  color: #6c6c6c;
                  font-style: italic;

                  & > em {
                    color: var(--temp-color-1);
                  }
                }

                & > .condition-action {
                  color: var(--temp-color-1);
                  // font-size: 13px;
                  font-size: calc(13px + var(--font-resize));
                  font-style: italic;
                  margin-top: auto;

                  &.no-conditions {
                    color: var(--temp-color-1);
                  }
                }
              }
            }
          }
        }
      }

      & > .categories-container.sticky-padding {
        // padding-top: 45px;

        border-bottom: thin solid #c1eee4;
        border-radius: 0 0 13px 13px;
      }

      & > .categories-container {
        // font-size: 0.7em;
        font-size: calc(0.7em + var(--font-resize));
        position: sticky;
        top: -2px;
        // background: white;
        background: var(--temp-color-4);
        z-index: 10;
        padding: 0;
        user-select: none;
        margin: 0 5px 10px 5px;
        border-radius: 13px;
        transition: all 0.2s ease-in-out;
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 0 10px;

        &::after {
          content: "";
          position: absolute;
          width: calc(100% - 25px);
          height: 10px;
          border-radius: 0 0 10px 10px;
          background: rgba(255, 255, 255, 0.2);
          transform: translateX(-50%);
          left: 50%;
          top: 100%;
          z-index: 0;
        }

        & > .categories-carousel {
          padding: 0px 20px;
          border-radius: inherit;
          flex: 1;
          height: 70px;
          white-space: nowrap;
          overflow: hidden;
          position: relative;

          & .swiper-wrapper {
            height: 100%;
          }

          & .swiper-button-prev,
          .swiper-button-next {
            color: #a8a7a7;
            // font-size: 15px;
            font-size: calc(15px + var(--font-resize));
            width: 20px;
            // background: white;
            background: var(--temp-color-4);
            height: 100%;
            top: 0;
            padding: 0;
            margin-top: 0;
            text-shadow: none;
            z-index: 10;

            &::after {
              position: relative;
              top: 0;

              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              padding: 0;
              // font-size: 15px;
              font-size: calc(15px + var(--font-resize));
              border: none;
            }
          }

          & .swiper-button-prev {
            left: 0;
            right: unset;
          }

          & .swiper-button-next {
            right: 0;
            left: unset;
          }
        }

        & > .logo-in-categories-carousel {
          display: flex;
          // width: 50px;
          // height: 50px;
          padding: 10px 10px 10px 0;

          @media screen and (max-width: 1024px) {
            display: none;
          }

          & > .shop-logo {
            box-shadow: 0 0 5px rgb(0, 0, 0, 0.1);
            border-radius: 5px;
          }
        }

        & > .search-in-categories-carousel {
          display: flex;
          position: relative;
          align-items: center;
          justify-content: center;

          & > .search-in-cs-button {
            width: 40px;
            height: 40px;
            color: var(--temp-color-5);
            font-weight: 700;
            // font-size: 20px;
            font-size: calc(20px + var(--font-resize));
            padding: 0;
            min-width: unset;
          }
        }

        & > .search-input-group-in-cs {
          position: absolute;
          right: 60px;
          top: 0;
          border-radius: inherit;
          height: 100%;
          width: calc(100% - 60px - 70px);
          max-width: calc(100% - 60px - 70px);
          z-index: 10;
          // background: white;
          background: var(--temp-color-4);
          transition: all 0.3s ease-in-out;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 10px 0;
          overflow: hidden;

          @media screen and (max-width: 1024px) {
            width: calc(100% - 70px);
            max-width: calc(100% - 70px);
          }

          &.hide {
            max-width: 0;
            overflow: hidden;
          }

          & > .search-input-group {
            display: flex;
            // background: white;
            border-radius: 5px;
            padding: 0 5px;
            flex: 1;
            // height: 35px;
            height: 100%;
            background: #f5f6fa;

            & > button {
              color: var(--temp-color-5);
              width: 35px;
              height: 35px;
              min-width: unset;
              // font-size: 20px;
              font-size: calc(20px + var(--font-resize));
              margin: auto;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            & > button.category-button {
              // font-size: 22px;
              font-size: calc(22px + var(--font-resize));
            }

            & > input {
              // background: white;
              // height: 35px;
              outline: none;
              // font-size: 13px;
              font-size: calc(13px + var(--font-resize));
              font-weight: bold;
              flex: 1;
            }
          }
        }

        & .category-item-slide {
          width: fit-content !important;
          padding: 1px;
          color: var(--temp-color-2);
          // font-size: 15px;
          font-size: calc(15px + var(--font-resize));
          font-weight: 800;
          text-transform: uppercase;
          align-items: center;
          display: flex;
          cursor: pointer;
          border-bottom: 3px solid transparent;
        }

        & .category-item-slide.active {
          color: var(--temp-color-2);

          ::after {
            content: "";
            height: 5px;
            background: var(--temp-color-5);
            border-radius: 3px;
            position: absolute;
            bottom: 2.5px;
            width: 100%;
            left: 0;
          }
        }
      }

      & > .shop-detail-description {
        & > .description {
          // font-size: 0.8em;
          font-size: calc(0.8em + var(--font-resize));
          color: var(--temp-color-1);
          line-height: 1.125;
          display: flex;
          flex-direction: column;
          transition: all 0.2s ease-in-out;
          max-height: calc(1.125 * 3);
          padding: 0 15px;
          margin: 15px 0;

          & > span {
            display: block;

            & > span {
              padding-bottom: 5px;
              white-space: break-spaces;
            }
          }
        }
      }

      & > .shop-products-container {
        display: flex;
        flex-direction: column;
        // font-size: 20px;
        font-size: calc(20px + var(--font-resize));
        position: relative;
        padding: 10px 0;
        margin: 15px 0;
        animation: fadeIn 0.5s ease-in-out;

        &.sale-off {
          border-radius: 20px;
          // background: white;
          background: var(--temp-color-4);
          margin: 15px 7px 7px;

          &::after {
            content: "";
            position: absolute;
            width: calc(100% - 25px);
            height: 10px;
            border-radius: 0 0 10px 10px;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-50%);
            left: 50%;
            top: 100%;
            z-index: 0;
          }
        }

        & > .title-stack {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;
          margin: auto;

          &.uppercase {
            text-transform: uppercase;
          }

          & > span {
            // font-size: 15px;
            font-size: calc(15px + var(--font-resize));
            font-weight: 800;
            color: var(--temp-color-2);
          }

          & > .button-fitler-sort {
            color: var(--temp-color-1);
            // font-size: 0.8em;
            font-size: calc(0.8em + var(--font-resize));
            display: flex;
            align-items: center;

            & > svg {
              color: #595959;
              // font-size: 40px;
              font-size: calc(40px + var(--font-resize));
            }
          }
        }

        & .line {
          margin: 0;
          height: 1px;
          background-color: #c1eee4;
        }

        & .grid-list-view {
          display: flex;
          justify-content: flex-end;
          padding: 10px;
          // background: white;
          background: var(--temp-color-4);
          border-radius: 15px;
          // margin-top: 20px;

          & > span {
            // font-size: 16px;
            font-size: calc(16px + var(--font-resize));
            color: var(--temp-color-1);
            font-weight: 700;
          }

          & > .type-view-buttons {
            display: flex;
            gap: 2px;
            align-items: center;
            justify-content: center;

            & > .type-view-button {
              // font-size: 27px;
              font-size: calc(27px + var(--font-resize));
              color: #d9d9d9;
              display: flex;
            }

            & > .type-view-button.active {
              color: var(--temp-color-2);
            }
          }
        }

        & > .products-container {
          flex-direction: column;
          flex-wrap: wrap;
          position: relative;
          display: flex;
          justify-content: flex-start;
          row-gap: 5px;
          padding: 0 5px 0 10px;
          // background: #f5f6fa;

          & .product-carousel {
            flex: 1;
            // padding: 0 5px;
            // padding: 10px;

            & > .swiper-wrapper {
              height: 100%;
              box-sizing: border-box;
            }

            & .product-item-container-grid-carousel {
              width: 100%;
              height: auto;
              display: flex;

              & > .product-item-container-grid {
                width: 100% !important;
                margin-top: 10px;

                & img {
                  max-height: unset !important;
                  min-height: unset !important;
                  aspect-ratio: 1;
                }
              }
            }
          }

          .empty-list {
            height: 100%;
            max-height: 500px;
            flex: 1;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 15px;
            text-align: center;
          }

          .empty-list > .empty-list-image {
            margin: 10px 0;
            justify-content: center;
            border-radius: 50%;
            width: 200px;
            height: 200px;
            object-fit: contain;
          }

          .empty-list > span {
            // font-size: 1em;
            font-size: calc(1em + var(--font-resize));
            color: var(--color-text-note);
          }

          & .swiper-button-prev,
          .swiper-button-next {
            color: white;
            // font-size: 15px;
            font-size: calc(15px + var(--font-resize));
            width: 30px;
            height: 50%;
            top: 5px;
            padding: 0;
            bottom: 0;
            margin-top: 0;
            filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
            text-shadow: none;

            &::after {
              position: absolute;
              top: 75px;
              transform: translateY(-50%);
              width: 30px;
              height: 30px;
              padding: 5px;
              border-radius: 50%;
              border: 2px solid white;
            }
          }

          & .swiper-button-prev {
            left: 20px;
            right: unset;
          }

          & .swiper-button-next {
            right: 20px;
            left: unset;
          }

          & > .container {
            margin: auto;
            width: 100%;
          }

          @media screen and (min-width: 576px) {
            .container {
              max-width: 540px;
            }
          }
          @media screen and (min-width: 768px) {
            .container {
              max-width: 720px;
            }
          }
          @media screen and (min-width: 992px) {
            .container {
              max-width: 960px;
            }
          }
          @media screen and (min-width: 1200px) {
            .container {
              max-width: 1140px;
            }
          }
        }
      }

      & > .shop-footer {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        padding: 10px;
        gap: 10px;
        // background: white;
        position: sticky;
        bottom: 0;
        box-shadow: 0 0px 10px rgb(0, 0, 0, 0.1);
        z-index: 10;
        margin-top: auto;

        @media screen and (min-width: 501px) {
          display: none;
        }

        & > button {
          display: flex;
          align-items: center;
          flex: 1;
          border-radius: 2em;
          color: white;
          max-width: 200px;

          & > .v-btn__content {
            border-radius: 2em;
            display: flex;
            align-items: center;
            justify-content: center;
            text-transform: uppercase;

            & > a,
            > span {
              font-weight: 700;
              // font-size: 15px;
              font-size: calc(15px + var(--font-resize));
              width: fit-content;
              max-width: 200px;
              flex: 1;
              padding: 5px;
              gap: 5px;
              height: 35px;
              white-space: nowrap;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          & svg {
            min-width: 30px;
            height: 100%;
          }

          & span {
            // flex: 1;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            // font-size: 13px;
            font-size: calc(13px + var(--font-resize));
            line-height: normal;
          }
        }

        & > .call-to-shop {
          background: var(--temp-color-2);
        }

        & > button.chat-to-shop {
          background: var(--temp-color-3);
        }
      }
    }

    @media screen and (min-width: calc(1025px * 100 / 85)) {
      max-width: calc(85%);
    }
  }

  & .chat-to-shop-button {
    background: var(--temp-color-3);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    // font-size: 26px;
    font-size: calc(26px + var(--font-resize));
    box-shadow: 0 4px 4px rgb(0, 0, 0, 0.25);
    position: fixed;
    z-index: 1000;
    // bottom: 150px;
    // right: 25px;
    touch-action: none;
    will-change: transform;
    user-select: none;
    transition: all 0.2s ease-in-out;
  }

  & .chat-to-shop-button.none-transition {
    transition: none;
  }

  & .chat-to-shop {
    width: fit-content;
    height: fit-content;
    pointer-events: auto;
  }

  & > .banner-delay-box {
    position: absolute;
    top: -30px;
    // padding-top: 30px;
    max-width: unset;
    margin: 0;
    width: 100dvw;
    height: 100dvh;
    transform-origin: top;
    animation: scaleBackground 1s ease-in-out;

    filter: brightness(0.6);
    overflow: visible;
  }
}

.promotion-content-modal-container {
  background: white;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: var(--max-width-content-view-1024);
  max-height: 50dvh;
  // height: fit-content;
  // min-height: fit-content;
  display: flex;
  flex-wrap: wrap;
  border-radius: 30px 30px 0 0;
  animation: slide-up 0.5s ease;
  padding: 0 10px 10px;
  overflow: auto;

  & > .header-promotion {
    display: flex;
    gap: 10px;
    padding: 10px 0;
    background: white;
    width: 100%;
    position: sticky;
    top: 0;
    border-bottom: thin solid #a7a7a7;

    & > .value-tag {
      height: 50px;
      width: 75px;
      // font-size: 20px;
      font-size: calc(20px + var(--font-resize));
      align-items: center;
      justify-content: center;
      display: flex;
      color: white;
      font-weight: 800;
      position: relative;
      position: sticky;
      top: 0;
      background: white;

      & > img {
        height: 50px;
        width: 75px;
        min-height: unset;
        z-index: 1;
        left: 0;
        position: absolute;
      }

      & > span {
        z-index: 2;
      }
    }

    & > .promotion-detail {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      line-height: normal;
      flex: 1;
      position: sticky;
      top: 0;
      background: white;

      & > .name {
        // font-size: 20px;
        font-size: calc(20px + var(--font-resize));
        color: #545454;
        font-weight: 700;
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      & > .value {
        font-weight: 500;
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        color: #545454;

        & > em {
          color: var(--temp-color-2);
          font-style: normal;
          font-weight: 700;
        }
      }

      & > .max-value {
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        font-weight: 700;
        color: #6c6c6c;
        font-style: italic;

        & > em {
          color: var(--temp-color-2);
        }
      }

      & > .condition-action {
        color: var(--temp-color-2);
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        font-style: italic;

        &.no-conditions {
          color: var(--temp-color-1);
        }
      }
    }

    & > .close-button {
      width: 30px;
      height: 30px;
      // font-size: 30px;
      font-size: calc(30px + var(--font-resize));
    }
  }

  & > .promotion-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-top: 10px;

    & > .promotion-info {
      width: 100%;

      overflow: auto;
      display: flex;
      flex-direction: column;
      line-height: normal;
      margin-bottom: 7px;

      & > label {
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        color: #585858;
        font-weight: 700;
        text-decoration: underline;
        text-underline-offset: 3px;
      }

      & > div {
        padding-left: 10px;
        font-style: italic;
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        color: #545454;

        & > .value-item {
          color: var(--temp-color-2);
          font-weight: 700;
          font-style: normal;
        }
      }
    }

    & > .promotion-info.h-stack {
      flex-direction: row;
      gap: 5px;

      & > div {
        padding-left: 0;
      }
    }

    & > .promotion-condition-list {
      display: flex;
      flex-direction: column;
      width: 100%;

      & > label {
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        color: #585858;
        font-weight: 700;

        text-underline-offset: 3px;

        & > span {
          text-decoration: underline;
          margin-right: 5px;
        }

        & > em {
          color: #868686;
          font-weight: 500;
          text-decoration: none;
        }
      }

      & > .promotion-condition-item {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-wrap: wrap;

        & > .relationship {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          padding-left: 25px;
          font-style: italic;
          font-weight: 600;
        }

        & > .condition-text {
          flex: 1;
        }

        & > svg {
          width: 20px;
          height: 20px;
          // font-size: 20px;
          font-size: calc(20px + var(--font-resize));
          margin-top: 2px;
          margin-right: 5px;
          color: var(--temp-color-1);
        }

        & > div {
          & > span {
            margin-right: 5px;
          }

          & > .value-item {
            color: var(--temp-color-2);
            font-weight: 700;
          }
        }
      }

      & > .empty-conditions {
        color: var(--temp-color-1);
        font-style: italic;
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        padding-left: 10px;
      }
    }
  }
}
