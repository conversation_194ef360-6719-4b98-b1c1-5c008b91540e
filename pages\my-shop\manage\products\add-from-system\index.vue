<template>
    <AddProductsFromSystemComponent/>
</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import CreatePrivateProductComponent from '~/components/myShop/createPrivateProduct/CreatePrivateProductComponent.vue';
import MyShopManageComponent from '~/components/myShop/management/MyShopManageComponent.vue';

const nuxtApp = useNuxtApp();
onBeforeMount(()=>{
    nuxtApp.$emit(appConst.event_key.show_footer, false)
})
</script>