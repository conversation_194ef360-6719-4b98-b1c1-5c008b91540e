.shop-delivery-partner-info-container {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  padding: 0;
  // min-height: 40dvh;
  overflow: auto;
  border-radius: 10px;
  background-color: white;

  & > .partner-info{
    display: flex;
    align-items: baseline;
    gap: 5px;
    padding: 0 10px;

    & > img{
      height: 25px;
      object-fit: contain;
    }

    & > span{
      color: var(--primary-color-1);
      font-size: 25px;
      font-weight: 800;
    }
  }

  &>.shop-delivery-partner-info-content {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 10px;
    gap: 10px;

    & > .form-field-container{
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
    }

    & .custom-input, .text-area-custom {
      background: white;
      border-radius: 7px;
      margin: 5px 0;
      padding: 10px;
      font-size: 15px;
      font-weight: 600;
      border: 1px solid rgb(231, 233, 236);
      outline: none;

      &:disabled{
        background: #f5f6fa;
      }

    }

    & .text-area-custom {
      height: 200px;
      resize: none;
    }
  }

  & > .shop-delivery-partner-info-footer{
    padding: 10px;
    display: flex;
    justify-content: space-evenly;

    & > button{
      border: thin solid;
    }

    & > .reject-button{
      color: var(--primary-color-2);
    }

    & > .accept-button{
      color: white;
      border: thin solid var(--primary-color-1);

    }
  }
}