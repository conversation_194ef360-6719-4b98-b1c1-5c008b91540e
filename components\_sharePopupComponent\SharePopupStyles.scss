.share-popup-container{
    padding: 10px 20px 25px 20px;
    background: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
    width: 500px;
    max-width: 90dvw;

    & > span.share-title{
        text-align: center;
        font-weight: 700;
        font-size: 20px;
        color: #545454;
    }

    & > button {
        font-size: 15px;
        font-weight: 500;
        color: #545454;
        padding: 5px 10px;
        width: 100%;
        height: 35px;
        border-radius: 5px;
        background: #F4F4F4;

        &.copy-link{
            font-weight: 400;
            color: white;
            background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
        }
    }

    & > button.zalo{
        background: #2079F8;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        & > img{
            width: 25px;
            height: 25px;
            border-radius: 40%;
            position: absolute;
            top: 5px;
            left: 5px;
        }
    }

    & > button.facebook{
        background: #0866FF;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        & > svg{
            width: 25px;
            height: 25px;
            border-radius: 40%;
            position: absolute;
            top: 5px;
            left: 5px;
        }
    }

    & > span.link-text {
        font-size: 15px;
        font-weight: 400;
        color: #545454;
        padding: 5px 10px;
        width: 100%;
        border-radius: 5px;
        border: thin solid #E2E1E1;
        background: white;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        gap: 3px;
        white-space: nowrap;

        & svg{
            min-width: 17px;
        }

        & > span{
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}