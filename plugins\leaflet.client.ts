import L from "leaflet";
import GestureHandling from "leaflet-gesture-handling";
import * as MarkerCluster from 'leaflet.markercluster';
import 'leaflet-rotatedmarker'
import 'leaflet-routing-machine';
import 'lrm-graphhopper';
import { appConst } from "~/assets/AppConst";
declare module 'leaflet' {
  namespace Routing {
      function graphHopper(apiKey: string, options:any): IRouter;
  }
}
export default defineNuxtPlugin(() => {
  
  L.Map.addInitHook("addHandler", "gestureHandling", GestureHandling);
  return {
    provide: {
      L: L,
      LMarkerCluster: MarkerCluster,
    },
  };
});
