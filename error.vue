<template>
	<div class='container-root contaner-root-1' id='container-root'>
		<div class="root-container">
			<v-app>
				<HeaderV2WithMenuComponent :header_level_2_id="'search_input_container'"
					v-if="!checkWindowWidthMax(1024)" :last_element_id="'last_of_list_product'">
				</HeaderV2WithMenuComponent>
				<PageNotFound/>
				<FooterV2Component v-if="checkWindowWidthMax(1024)">
				</FooterV2Component>
			</v-app>
		</div>
	</div>
	
</template>
<script setup lang="ts">

var windowInnerWidth = ref();
function checkWindowWidthMax(width: any) {
	if (windowInnerWidth.value <= width) return true;
	return false;
}
</script>