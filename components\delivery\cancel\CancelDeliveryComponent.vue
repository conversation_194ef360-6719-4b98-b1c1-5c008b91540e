<template>
    <VueFinalModal class="my-modal-container" :overlay-behavior="'persist'" content-class="v-stack cancel-delivery-container" :click-to-close="true"
        v-model="showCancelDeliveryModal" contentTransition="vfm-fade" v-on:closed="() => {
            close()
        }">
        <h2>{{ $t('CancelDeliveryComponent.huy_don_giao') }}</h2>
        <em class="delivery-code">#{{ orderData.delivery_id ?? orderData.delivery?.short_code ?? orderData.delivery?.id
            }}</em>
        
        <div class="parter-info">
            <label>{{ $t('CancelDeliveryComponent.don_vi_van_chuyen') }}</label>
            <img class="partner-logo" :src="orderData?.delivery_partner?.information?.logo"
                v-if="orderData?.delivery_partner?.information?.logo" :alt="`logo ${orderData?.delivery_partner?.name}`">
            <span class="partner-name" v-else>
                {{ orderData?.delivery_partner?.name }}
            </span>
            
        </div>
          <div class="cancel-reason">
            <label for="cancel-reason">{{ $t('CancelDeliveryComponent.ly_do_huy_don') }}</label>
            <div class="cancel-reason-options">
                <div class="reason-option" 
                     v-for="reason in appConst.delivery_cancel_reason" 
                     :key="reason"
                     :class="{ 'selected': selectedReason === reason }"
                     @click="selectReason(reason)">
                    <input type="radio" 
                           :id="reason" 
                           :value="reason" 
                           v-model="selectedReason" 
                           name="cancel-reason">
                    <label :for="reason">{{ $t(`delivery_cancel_reason.${reason}`) }}</label>
                </div>
            </div>
            
            <div class="additional-notes" v-if="selectedReason">
                <label for="additional-notes">{{ $t('CancelDeliveryComponent.ghi_chu_them') }} <em class="text-length">({{ notes?.length ?? 0 }}/{{ appConst.max_text_long }})</em></label>
                <textarea name="additional-notes" id="additional_notes" rows="3" v-model="notes"
                :maxlength="appConst.max_text_long"
                    :placeholder="$t('CancelDeliveryComponent.ghi_chu_them_placeholder')"></textarea>
            </div>
        </div>

        <div class="cancel-delivery-footer">
            <button class="close" v-on:click="close()">
                {{ $t('CancelDeliveryComponent.dong') }}
            </button>
            <button :disabled="submiting" class="cancel" v-on:click="() => {
                cancelDeliver()
            }">
                {{ $t('CancelDeliveryComponent.xac_nhan_huy') }}
            </button>
        </div>
    </VueFinalModal>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { appConst, domainImage, formatCurrency, formatNumber, validPhone } from '~/assets/AppConst';
import { DeliveryService } from '~/services/orderService/deliveryService';
import { toast } from 'vue3-toastify';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
import { DeliveryPartnerService } from '~/services/deliveryPartnerService/deliveryPartnerService';
import { VueFinalModal } from 'vue-final-modal';
import { OrderService } from '~/services/orderService/orderService';
import { AgentService } from '~/services/agentService/agentService';

const { t } = useI18n();
const nuxtApp = useNuxtApp();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    order_id: null,
    orderData: null,
    mode: null
});
var deliveryService = new DeliveryService();
var mqttService = new MqttService();
var deliverPartnerService = new DeliveryPartnerService();
var orderService = new OrderService();
var agentService = new AgentService();
var webInApp = ref(null as any);
var control: any;

var showCancelDeliveryModal = ref(false);
var notes = ref("");
var selectedReason = ref("");
var orderData = ref<any>(null);

var submiting = ref(false);
onMounted(async () => {
    if (props.orderData) {
        orderData.value = props.orderData;
    }
    else {
        await getOrderDetail()
    }
    showCancelDeliveryModal.value = true;
})

function getOrderDetail() {
    return new Promise((resolve) => {
        if (props.mode != 'agent') {
            orderService.detailOrder(props.order_id).then(res => {
                if (res.status == HttpStatusCode.Ok) {
                    orderData.value = JSON.parse(JSON.stringify(res.body.data));
                }
                else {
                    orderData.value = null;
                }
                resolve(orderData.value)
            }).catch(() => {
                toast.error(t('CancelDeliveryComponent.co_loi_xay_ra'));
                resolve(null)
            })
        }
        else {
            agentService.agentOrderDetail(props.order_id).then(res => {
                if (res.status == HttpStatusCode.Ok) {
                    orderData.value = JSON.parse(JSON.stringify(res.body.data));
                }
                else {
                    orderData.value = null;
                }
                resolve(orderData.value)
            }).catch(() => {
                toast.error(t('CancelDeliveryComponent.co_loi_xay_ra'));
                resolve(null)
            })
        }
    })
}

function cancelDeliver() {
    if (!selectedReason.value) {
        toast.error(t('CancelDeliveryComponent.vui_long_chon_ly_do'));
        return;
    }
    
    submiting.value = true;
    deliveryService.cancelDeliveryV2({
        partner: orderData.value?.delivery_partner?.name?.toLowerCase().includes('remagan') ? 'remagan' : orderData.value?.delivery_partner?.name?.toLowerCase(),
        shop_id: orderData.value?.shops?.id ?? orderData.value?.shop_id,
        note: `${t(`delivery_cancel_reason.${selectedReason.value}`)}${notes.value ? ` - ${notes.value}` : ''}`,
        order_id: orderData.value.id ?? props.order_id
    }).then(async (res) => {
        if (res.status == HttpStatusCode.Ok) {
            await close(true);
            // submiting.value = false;
        }
        else {
            toast.error(res.body?.message ?? t('CancelDeliveryComponent.khong_the_huy_luc_nay'));
            submiting.value = false;
        }
    }).catch((err) => {
        toast.error(t('CancelDeliveryComponent.huy_that_bai'));
        submiting.value = false;
    })
}

function selectReason(reason: string) {
    selectedReason.value = reason;
}

function close(canceld = false) {
    emit('close', canceld)
}
</script>

<style lang="scss" src="./CancelDeliveryStyles.scss"></style>