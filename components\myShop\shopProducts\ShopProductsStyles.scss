.show-product-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  background: var(--color-background-2);
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: auto;

  // & > .title-header {
  //   // font-size: 1.3em;
  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .list-product-container {
    flex: 1;
    overflow: auto;
    background: var(--color-background-2);

    & > .list-product-amount {
      position: sticky;
      top: 0;
      left: 0;
      background: var(--color-background-2);
      padding: 5px 10px;
    }
    & .product-loading {
      font-size: 1em;
      font-weight: 500;
      text-align: center;
      flex: 1;
      width: 100%;
    }
    & > .list-product-show {
      padding: 0 10px 50px 10px;
      flex-wrap: wrap;
      gap: 10px;
      display: flex;

      & > .product-item {
        display: flex;
        padding: 5px;
        margin-top: 10px;
        width: 100%;
        align-items: flex-start;
        gap: 10px;
        background-color: white;
        // box-shadow: 0 0 5px #ccc;
        border-radius: 5px;

        & > img {
          margin-right: 5px;
          height: 100px;
          width: 25%;
          aspect-ratio: 1;
          border-radius: 10px;
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > .product-detail {
          flex: 1;
          height: 100%;
          align-items: flex-start;
          gap: 5px;
          font-size: 0.9em;

          & > .name {
            font-weight: 500;
            color: var(--primary-color-1);
            font-size: 0.9em;
            /* display: -webkit-box;
                          -webkit-box-orient: vertical;
                          -webkit-line-clamp: 2;
                          overflow: hidden;
                          text-overflow: ellipsis; */
          }

          & > .price {
            color: var(--primary-color-1);
            font-weight: 500;

            & > em{
              color: var(--color-text-note);
              text-decoration: line-through;
            }
          }

          & > .action {
            margin-left: auto;
            margin-top: auto;
            color: var(--primary-color-1);
            border: thin solid #ccc;
            border-radius: 5px;
            background: white;
            padding: 2px 10px;
          }
        }
      }
    }
    & > .none-list-product {
      height: 100%;
      flex: 1;
      justify-content: center;
      align-items: center;
      gap: 10px;

      & > img {
        margin: 10px auto;
        justify-content: center;
        border-radius: 50%;
        width: 150px;
        height: 150px;
        object-fit: contain;
      }

      & > span {
        font-weight: 500;
        font-size: 1em;
        color: var(--color-text-note);
      }
    }
  }
  .categories-container {
    justify-content: flex-start;
    display: flex;
    // gap: 10px;
    padding: 5px;
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%;
    // height: 100px;
    background: var(--color-background-2);
    min-height: 50px;
    // max-height: unset;

    & > .category-item-button {
      margin: 5px;
      padding: 5px;
      background-color: white;
      border: 2px solid var(--primary-color-1);
      align-self: flex-start;
      border-radius: 10px;
      width: fit-content;
      color: var(--primary-color-1);
      text-align: center;
    }

    & > button.category-item-button > .name {
      font-size: 0.9em;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      white-space: nowrap;
    }

    & > button.category-item-button.active {
      background-color: var(--primary-color-1);
      color: white;
      font-weight: 500;
    }

    & > button.category-item-button > .name > span {
      font-style: italic;
    }
  }

  & .search-bar-container {
    align-items: center;
    padding: 0 3px;
    margin: 10px 0 5px 0;
    position: relative;
    height: 40px;
    margin-bottom: 5px;
  }

  & .search-input-container {
    width: 100%;
    background-color: white;
    border-radius: 0;
    border-color: transparent;
    outline: none;
    height: 100%;
    font-size: var(--font-size);
  }

  & .search-button {
    display: flex;
    align-items: center;
    background: white;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    padding: 0 10px;
    color: var(--color-text-note);
    height: 100%;
    border: none;
  }
  & .loading-more {
    background-color: white;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }
  & .categories-tab {
    text-transform: none;
    font-size: 0.8em;
    color: var(--color-text-note);
    box-shadow: 0 0 5px #ccc;
    z-index: 1;
    align-items: center;
  
    & .category-item-tab {
      margin: 5px;
      padding: 5px;
      background-color: white;
      border: 2px solid var(--primary-color-1);
      align-self: flex-start;
      border-radius: 10px !important;
      height: unset !important;
      width: -moz-fit-content;
      width: fit-content;
      color: var(--color-text-black);
      text-align: center;
      font-size: 0.8em;
  
      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;
  
        & span {
          text-transform: none;
          // color: var(--color-text-black);
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 600;
        }
      }
    }
  
    & .category-item-tab.active {
      color: white;
      font-weight: 600;
      background-color: var(--primary-color-1);
    }
  
    & .v-slide-group__next, .v-slide-group__prev{
      color: var(--primary-color-1);
      min-width: unset;
    }
  }
}
