.order-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-view);
  margin: auto;
  display: flex;
  flex-direction: column;
  background: #f5f4f9;
  font-size: 1.2em;
  overflow: auto;
  /* gap: 10px; */
  /* padding: 0 10px; */

  // > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   background: white;
  // }

  & > .order-content-container {
    display: flex;
    flex-direction: column;
    background: #f5f4f9;
    font-size: 17px;
    // overflow: auto;

    & > .receiver-info {
      display: flex;
      gap: 5px;
      background: white;
      margin-bottom: 10px;
      padding: 10px 15px;
      align-items: flex-start;
      font-size: 1rem;
      cursor: pointer;

      & > svg {
        color: var(--primary-color-1);
        font-size: 20px;
        margin-top: 5px;
      }

      & > .user-info {
        display: flex;
        flex-direction: column;
        color: #19191b;
        font-weight: 600;
        font-size: 1em;
        flex: 1;

        & > .address {
          color: #9e9da2;
          font-weight: 500;
          font-size: 0.9em;
        }
        & > .address.error,
        .name-phone > .error {
          color: var(--primary-color-2);
        }
      }

      & > .select-receiver {
        display: flex;
        margin-left: auto;
        color: #7b7b7b;
        align-self: center;
        align-items: center;
        height: 100%;
      }
    }

    & > .cart-selected-items {
      display: flex;
      flex-direction: column;
      padding: 10px 15px;
      background: white;
      flex: 1;
      margin-bottom: 10px;

      & > .cart-item-container {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 10px 0;
        color: black;
        gap: 5px;

        & > .checkbox {
          padding-top: 10px;
          & > svg.checked {
            color: var(--primary-color-1);
          }
          & > svg {
            color: #cfced3;
            font-size: 30px;
          }
        }
        & > img {
          width: 100px;
          height: 100px;
          aspect-ratio: 1;
          border-radius: 10px;
          object-fit: cover;
          background: var(--color-background-2);
        }

        & > .item-cart-detail {
          align-items: flex-start;
          gap: 5px;
          flex: 1;
          padding: 10px;
          overflow: hidden;

          & > .item-cart-product-name {
            font-size: 17px;
            font-weight: 600;
            color: var(--color-text-black);
          }

          & > .product-price {
            color: #828187;
            font-weight: 600;
            font-size: 20px;

            & > em {
              color: var(--color-text-note);
              text-decoration: line-through;
              font-weight: 500;
              font-size: 0.8em;
              margin-left: 5px;
              font-style: normal;
            }
          }

          & > .product-notes {
            color: #828187;
            font-style: italic;
          }

          & > .item-cart-quantity {
            display: flex;
            align-items: center;
            width: 100%;

            & > span {
              margin: 0 10px;
            }

            & > .quantity-button {
              color: #858585;
              border: none;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 35px;
              height: 35px;
              padding: 0;
              font-size: 1.3em;
            }

            & > .quantity-button:disabled {
              opacity: 0.5;
            }

            & > .delete-item-cart {
              margin-left: auto;
              color: var(--primary-color-1);
              align-self: flex-end;
            }

            .price-input {
              // border: thin solid var(--color-text-note);
              border-width: 0 0 1px 0;
              width: 50px;
              overflow: hidden;
              text-align: center;
              font-weight: 600;
              margin: 0 5px;
              outline: none;
              display: flex;
            }
          }
        }
      }
    }

    & > .payment-method,
    > .note-container {
      display: flex;
      padding: 10px 15px;
      margin-bottom: 10px;
      background: white;
      align-items: flex-start;

      & > .icon-label {
        font-size: 20px;
        color: var(--primary-color-1);
        padding-top: 5px;
      }

      & > .content-container {
        padding-left: 15px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        & > .label {
          font-size: 1em;
          color: #2e2d30;
          font-weight: 600;
        }

        & > .content {
          color: #828187;
          font-size: 0.9em;
          width: 100%;

          & > .content-item {
            display: flex;
            gap: 5px;
            margin: 0 auto;
            justify-self: stretch;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;

            & > svg {
              color: var(--primary-color-1);
              font-size: 20px;
            }
          }

          & > .content-item.active {
            color: #2e2d30;
          }
        }

        & > .note-order-input-container {
          width: 100%;
          background-color: var(--color-background-2);
          border-radius: 5px;

          & > .note-order-input {
            padding: 10px;
            width: 100%;
            outline: none;
            resize: none;
          }
        }

        & > .delivery-price{
          font-size: 13px;
          font-style: italic;
          margin-top: 5px;
        }
      }
    }

    & > .payment-info-container {
      padding: 15px 15px 0;
      display: flex;
      flex-direction: column;
      background-color: white;
      font-size: 17px;
      // margin-bottom: 10px;

      & > .payment-info-content {
        justify-content: space-between;
        align-items: flex-start;
        display: flex;
        padding: 10px 0;
        font-weight: 500;
        color: #828187;
        border-bottom: thin solid #ebeaef;
      }
      & > .payment-info-content > .value {
        font-size: 1em;
        color: #2e2d30;
        font-weight: 500;
      }
      & > .payment-info-content.off > .value {
        font-size: 1em;
      }
      & > .payment-info-content.total-left {
        // border-bottom: thin solid #88888a;
        color: #2e2d30;
        & > .value {
          font-weight: bold;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          font-size: 1.1em;

          & > .note {
            font-size: 1rem;
          }
        }
      }

      & > .payment-info-content .distance{
        font-style: italic;
      }
    }

    & > .order-footer {
      position: sticky;
      bottom: 0;
      left: 0;
      padding: 0 15px;
      background-color: white;
      font-size: 17px;

      & > .order-footer-content {
        border-top: thin solid #88888a;
        padding: 15px 0;
        align-items: center;
        justify-content: space-between;
        color: white;
        font-size: 1em;
        margin-top: auto;
        font-weight: 500;
        display: flex;
        & > .total {
          color: #575757;
          font-size: 1em;
          display: flex;
          flex-direction: column;
          & > span {
            font-size: 0.9em;
            color: #828187;
          }
          & > .price {
            color: var(--primary-color-1);
            font-weight: bold;
            font-size: 20px;
          }

          & > .none-price {
            color: var(--primary-color-1);
            font-weight: 500;
          }
        }

        & > button {
          width: 150px;
          height: 100%;
          margin-left: auto;
          border-radius: 5px;
          background-color: var(--primary-color-1);
          padding: 10px;
          color: white;
          font-weight: 500;
          border: none;
          gap: 5px;
          font-size: 1em;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .empty-cart-avt {
    margin: 0 10px;
    justify-content: center;
    width: 250px;
    height: 250px;
    object-fit: contain;
  }

  .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding-top: 30px;
    flex: 1;
    background: white;
  }

  .empty-cart > .text {
    color: var(--color-text-black);
    font-weight: bold;
    font-size: 1.2em;
  }

  .empty-cart > .action {
    color: var(--color-text-note);
    font-style: italic;
    font-size: 1em;
    border: none;
    background: transparent;
  }
}
.saved-address-modal-container{
  width: 500px !important;
  max-width: 95% !important;
  height: 800px !important;
  max-height: 95% !important;
  padding: 0 !important;
}
.complete-order-container {
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.complete-order-container > .complete-order-image {
  margin: 10px auto;
  justify-content: center;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: contain;
}

.complete-order-container > .complete-order-message {
  font-size: 1.3em;
  font-weight: 500;
  text-align: center;
}

.complete-order-container > .complete-order-action {
  font-size: 1.3em;
  color: var(--primary-color-1);
  cursor: pointer;
  font-weight: 500;
  text-align: center;
  background: transparent;
  border: none;
  outline: none;
}
