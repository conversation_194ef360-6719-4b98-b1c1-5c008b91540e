export const appRoute = {
    TabBottom: '/tab-bottom',
    HomeComponent: '/home',
    LoginComponent: '/login',
    SignupComponent: '/signup',
    SplashScreenComponent: '/splash',
    ProfileComponent: '/profile',
    MyOrdersComponent: '/my-orders',
    MyOrderDetailComponent: '/my-orders/:order_id',
    ProfileInfoComponent: '/profile-info',
    MyShopComponent: '/my-shop',
    RegisterShopComponent: '/shop-register',
    EditShopInfoComponent: '/my-shop/edit',
    // AddProductsFromSystemComponent: '/add-product-from-system',
    ShopProductsComponent: 'my-shop/products',
    // CreatePrivateProductComponent: '/create-private-product',
    // EditProductComponent: '/edit-product',
    DetailShopComponent: '/shop',
    ShopInfoComponent: '/shop/:id/info',
    ElegantMenuShopComponent: '/shop/:id/elegant-menu',
    MyShopManageComponent: '/my-shop/manage',
    EditProductComponent: '/my-shop/manage/products/edit/:id',
    CreatePrivateProductComponent: '/my-shop/manage/products/create',
    AddProductsFromSystemComponent: '/my-shop/manage/products/add-from-system',
    ManageCategoryComponent: '/my-shop/categories',
    CreateCategoryComponent: '/create-category',
    EditCategoryComponent: '/edit-category',
    // ProductsCategoryComponent: '/product-category',
    ProductsCategoryComponent: '/my-shop/manage/categories/:slug',
    ProductComponent: '/product',
    CartComponent: '/my-cart',
    OrderComponent: '/order',
    ManageShopsComponent: '/manage-shop',
    ManageOrdersComponent: '/my-shop/orders',
    MyShopOrderDetailComponent: '/my-shop/orders/:id',
    MyShopEditOrderComponent: '/my-shop-edit-order',
    MyShopCreateOrderComponent: '/my-shop/orders/new',
    OAuthZaloComponent: '/Oauth-zalo',
    QuotationComponent: '/my-shop/quotations',
    QuotationFilterComponent: '/quotation-filter',
    QuotationRequestComponent: '/quotation-request',
    QuotationReplyComponent:'/quotation-reply',
    ImportQuotationComponent: '/my-shop/quotations/import-quotation',
    RequestQuotationComponent: '/my-shop/quotations/request',
    DetailQuotationComponent: '/my-shop/quotations/detail/:quotation_id',
    ReplyQuotationComponent: '/quotation/:quotation_id/reply',
    CheckQuotationComponent:'/my-shop/quotations/check',
    PageNotFoundComponent: "*",
    DeleteAccountComponent: "/delete_account",
    OTPConfirmComponent: "/otp-confirm",
    ForgetPasswordComponent: '/forgot-password',
    ChangePasswordComponent: '/change-password',
    WelcomeComponent: '',
    AboutComponent: '/about',
    PolicyComponent: '/policy',
    AroundComponent: '/around',
    SearchComponent: '/search',

    // AgentDashboardComponent: '/agent',
    AgentShopManageComponent: '/manage-shop',
    AgentOrderManageComponent: '/agent/shop/:shop_id/orders',
    AgentOrderDetailComponent: '/agent/shop/:shop_id/orders/:id',
    AgentEditOrderComponent: '/agent-edit-order',
    AgentCreateOrderComponent: '/agent/shop/:shop_id/create-order',
    AgentCreateShopComponent: '/agent/create-shop',
    AgentEditShopInfoComponent: '/agent/shop/:id/edit',
    AgentShopDetailDashboardComponent: '/agent/shop/:id',
    AgentShopDetailInfoComponent: '/agent/shop/:id/preview',
    AgentShopConfigComponent: '/agent/shop/:id/config',
    
    AgentShopCategoriesComponent: '/agent/shop/:id/categories',
    AgentShowProductInAShopComponent: '/agent/shop/:id/products',

    AgentCreatePrivateProductComponent: '/agent/shop/:id/management/products/create',
    AgentShopManagementComponent: '/agent/shop/:id/management',
    AgentAddProductFromSystemComponent: '/agent/shop/:id/management/products/add-from-system',
    AgentShopEditProductComponent: '/agent/shop/:id/management/products/edit/:product_id',
    AgentProductsCategoryComponent: '/agent/shop/:id/management/categories/:category_id',
    AgentShopChatManageComponent: '/chat?object=agent_shop&shop_id=:shop_id',
    AgentShopManageDeliveryPartnerComponent: '/agent/shop/:id/management/delivery-partner',

    ReelsComponent: "/reels",
    SavedAddressComponent: "saved-address",
    MyShopConfigComponent: '/my-shop/config',
    CreateDeliveryComponent: '/delivery/create',
    SupportComponent: "/support",

    DriverDashboardComponent: '/driver-tools',
    DeliveryHistoryComponent: '/driver-tools/delivery-history',
    DeliveryDetailComponent: '/driver-tools/delivery-history/:delivery_id',

    ChatManageComponent: '/chat',
    ChatDetailComponent: "/chat/:channel_id",
    MyShopChatManageComponent: '/chat?object=my_shop',
    MyShopChatDetailComponent: "/my-shop/chat/:channel_id",
    MyShopManageDeliverPartnerComponent: "/my-shop/delivery-partner",

    // Stock Management Routes - My Shop
    StockManagementComponent: '/my-shop/stock',
    StockImportComponent: '/my-shop/stock/import',
    StockExportComponent: '/my-shop/stock/export',
    StockWasteComponent: '/my-shop/stock/waste',
    StockReportsComponent: '/my-shop/stock/reports',
    StockHistoryComponent: '/my-shop/stock/history/:product_id',
    StockCurrentComponent: '/my-shop/stock/current',

    // Stock Management Routes - Agent
    AgentStockManagementComponent: '/agent/shop/:id/stock',
    AgentStockImportComponent: '/agent/shop/:id/stock/import',
    AgentStockExportComponent: '/agent/shop/:id/stock/export',
    AgentStockWasteComponent: '/agent/shop/:id/stock/waste',
    AgentStockReportsComponent: '/agent/shop/:id/stock/reports',
    AgentStockHistoryComponent: '/agent/shop/:id/stock/history/:product_id',
    AgentStockCurrentComponent: '/agent/shop/:id/stock/current'
}

export default appRoute;