.add-delivery-promotion-container {
  max-height: 95dvh;
  height: fit-content;
  min-height: 700px;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  overflow: hidden;
  background-color: white;
  position: relative;

  &>.add-delivery-promotion-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-bottom: 10px;
    padding-bottom: 10px;
    overflow: auto;

    &>.add-delivery-promotion-content {
      display: flex;
      flex-direction: column;
      padding: 10px;
      gap: 10px;
    }

    & .form-field-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;

      & label {
        font-size: 15px;
        font-weight: 700;
        color: #868686;
      }

      &>.name-language {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 5px auto 0;

        &>button.lang-button {
          border-radius: 2em;
          padding: 2px 10px;
          height: 25px;
          font-size: 13px;
          color: #545454;
          border: thin solid;
          font-weight: 700;
          display: flex;
          align-items: baseline;
          justify-content: center;
          line-height: normal;
          gap: 5px;
          position: relative;

          &>svg {
            align-self: center;
            position: absolute;
            top: 1px;
            right: 1px;
            height: 20px;
            width: 20px;
          }
        }

        &>button.lang-button.filled {
          padding-right: 25px;
        }

        &>button.lang-button.active {
          background: #545454;
          color: white;
        }
      }

      &>.input-group-container {
        flex: 1;

        &>.price-text {
          line-height: 1;
        }


        &>.type-button-group {
          border-radius: 10px;
          display: flex;
          margin-left: 10px;

          &>.type-promotion-option {
            padding: 5px 10px;
            color: #bababa;
            background: white;
            border: 1px solid rgb(231, 233, 236);
            min-width: 60px;
            height: 100%;

            &:first-child {
              border-top-left-radius: 10px;
              border-bottom-left-radius: 10px;
              border-right: none;
            }

            &:last-child {
              border-top-right-radius: 10px;
              border-bottom-right-radius: 10px;
              border-left: none;
            }

            &.active {
              font-weight: 800;
              color: white;
              border-color: var(--primary-color-1);
              background: var(--primary-color-1);
            }
          }
        }
      }

      &>.field-header {
        display: flex;
        justify-content: space-between;

        &>button.add-condition {
          color: var(--primary-color-1);
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }

      &>.field-value {
        display: flex;
        justify-content: center;
        margin-top: 8px;

        &>.relationship-option {
          min-width: 100px;
          border-radius: 0;
          color: #545454;
          padding: 5px 10px;
          border: thin solid;

          &:first-child {
            border-radius: 2em 0 0 2em;
            border-right: none;
          }

          &:last-child {
            border-radius: 0 2em 2em 0;
            border-left: none;
          }

          &.active {
            background-color: var(--primary-color-1);
            color: white;
            font-weight: 700;
            border-color: var(--primary-color-1);
          }
        }
      }

      &>.field-value {
        display: flex;
        justify-content: center;
        margin-top: 8px;

        &>.relationship-option {
          min-width: 100px;
          border-radius: 0;
          color: #545454;
          padding: 5px 10px;
          border: thin solid;

          &:first-child {
            border-radius: 2em 0 0 2em;
            border-right: none;
          }

          &:last-child {
            border-radius: 0 2em 2em 0;
            border-left: none;
          }

          &.active {
            background-color: var(--primary-color-1);
            color: white;
            font-weight: 700;
            border-color: var(--primary-color-1);
          }
        }
      }

      &>.field-value {
        display: flex;
        justify-content: center;
        margin-top: 8px;

        &>.relationship-option {
          min-width: 100px;
          border-radius: 0;
          color: #545454;
          padding: 5px 10px;
          border: thin solid;

          &:first-child {
            border-radius: 2em 0 0 2em;
            border-right: none;
          }

          &:last-child {
            border-radius: 0 2em 2em 0;
            border-left: none;
          }

          &.active {
            background-color: var(--primary-color-1);
            color: white;
            font-weight: 700;
            border-color: var(--primary-color-1);
          }
        }
      }

      &>.list-conditions-container {
        display: flex;
        flex-direction: column;
        gap: 5px;

        &>.condition-item-container {
          display: flex;
          gap: 5px;

          border-radius: 10px;
          padding: 5px;
          flex-wrap: wrap;

          &>.relationship-condition-tag {
            flex: 100%;
            text-align: center;
          }

          &>.param-select {
            flex: calc(40% - 30px);
            background: #f5f6fa;
            margin-top: 0 !important;

            &>.v-input__control {
              padding: 0 5px;
              height: 50px !important;
            }
          }

          &>.operator-select {
            flex: none;
            width: 30px;
            margin-top: 0 !important;
            white-space: nowrap;
            background: #f5f6fa;
            min-width: fit-content;
            text-align: center;

            &>.v-input__control {
              padding: 10px 2px;
              align-items: center;

              & .v-field__input {
                justify-content: center;

                & .v-field.v-field {
                  justify-content: center;
                }
              }
            }
          }

          &>.field-value-group {
            height: 50px;
            flex: calc(40% - 30px);
            display: flex;
            border-radius: 5px;
            align-items: center;
            background: #f5f6fa;

            & .value-input {
              height: 50px;
              border: none;
              width: 100%;
              background: #f5f6fa;
              text-align: center;
            }

            & .unit {
              display: flex;
              align-items: center;
            }

            &>.input-group-container {
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              border-radius: 5px;
              padding: 0 5px;
              background: #f5f6fa;
              position: relative;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;

              &.type-input {
                display: flex;
                flex-wrap: wrap;
              }

              &>.price-text {
                position: absolute;
                width: 100%;
                text-align: center;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: fit-content;
                font-size: 11px;
                line-height: 1;
              }
            }
          }

          & .value-input {
            flex: 40%;
            margin: 0;

          }

          &>.remove-condition-button {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 30px;
            min-width: 30px;
            width: 30px;
            border-radius: 50%;
            animation: none;

            &:hover {
              background: #ccc;
            }
          }
        }

        &>.empty-condition {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 10px;
          border-radius: 10px;
          color: #545454;
          font-style: italic;
        }
      }
    }

    & .custom-input,
    .text-area-custom {
      background: white;
      border-radius: 7px;
      margin: 5px 0;
      padding: 10px;
      font-size: 15px;
      font-weight: 600;
      border: 1px solid rgb(231, 233, 236);
      outline: none;
      flex: 1;

      &:disabled {
        background: #f5f6fa;
      }

    }

    & .text-area-custom {
      height: 200px;
      resize: none;
    }
  }

  &>.action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;
    user-select: none;

    &>button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }

    &>.cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }

    &>.save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    &>.cancel-button:disabled {
      // color: var(--color-text-note);
      // border-color: var(--color-text-note);
      opacity: 0.5;
    }

    &>.save-button:disabled {
      // background: #ccc;
      // color: var(--primary-color-1);
      // border-color: #ccc;
      opacity: 0.5;
    }
  }

  &>.condition-provice-select {

    & .select-options-container {
      padding: 0;
    }

    & .item-option.selected {
      background: color-mix(in srgb, var(--primary-color-1) 90%, transparent) !important;
      color: white !important;
      // border-radius: 2em;
    }
  }
}