<template>
	<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'" content-class="v-stack my-modal-content-container" :click-to-close="true"
		v-model="showClearSearchHistoryModal" contentTransition="vfm-fade" v-on:closed="() => {
			showClearSearchHistoryModal = false
		}">
		<div class="clear-search-history-container">
			<span>
				{{ $t('ClearSearchHistoryComponent.xoa_lich_su_tim_kiem') }}
			</span>

			<div class="clear-actions">

				<button class="close" v-on:click="() => {
					emit('close');
				}">{{ $t('ClearSearchHistoryComponent.dong') }}</button>
				<button class="accept" v-on:click="() => {
					emit('submit');
				}">{{ $t('ClearSearchHistoryComponent.xoa_lich_su') }}</button>
			</div>
		</div>
	</VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
const props = defineProps({

});
const emit = defineEmits(['close', 'submit']);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var showClearSearchHistoryModal = ref(false);
var webInApp = ref(null as any);

onMounted(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	showClearSearchHistoryModal.value = true;
});


function close() {
	showClearSearchHistoryModal.value = false
	emit('close')
}
</script>

<style lang="scss" src="./ClearSearchHistoryStyles.scss"></style>