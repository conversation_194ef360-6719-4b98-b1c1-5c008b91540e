<template>
    <VueFinalModal class="my-modal-container" :modal-id="'update_commission_percent_modal'" :keep-overlay="true" :overlay-behavior="'persist'"
        :hide-overlay="false" :hide-overlay-on-blur="false" content-class="v-stack update-commission-percent-container"
        :click-to-close="false" :esc-to-close="false" v-on:before-close="() => {
            showUpdateCommissionPercentModal = false
        }" v-model="showUpdateCommissionPercentModal" contentTransition="vfm-slide-up">
        <SubHeaderV2Component :title="props.title ?? $t('ShopConfigComponent.phan_tram_hoa_hong')">
            <template v-slot:header_left></template>
        </SubHeaderV2Component>        <div class="update-commission-percent-content-container">
            <VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
                class="update-commission-percent-content">
                <div class="form-field-container">
                    <div class="form-label">
                        <Icon name="mdi:percent" size="20"></Icon>
                        <span>{{ $t('ShopConfigComponent.phan_tram_hoa_hong') }}</span>
                    </div>
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t('ShopConfigComponent.nhap_phan_tram_hoa_hong')" class="custom-input"
                            :validate-on-input="true" name="commission_percent_value" type="number" 
                            :min="0" :max="100" step="0.5"
                            id="commission_percent_value_input"
                            v-bind:model-value="commission_percent.value" v-on:update:model-value="($event: any) => {
                                commission_percent.value = $event;
                                validateCommissionPercent();
                            }"></Field>
                        <span class="input-suffix">%</span>
                    </div>
                    <small class="help-text">
                        <Icon name="mdi:information-outline" size="16"></Icon>
                        {{ $t('ShopConfigComponent.phan_tram_hoa_hong_tu_0_den_100') }}
                    </small>
                    <ErrorMessage class="error-message" name="commission_percent_value"></ErrorMessage>
                </div>

                <button hidden ref="submitFormButton" v-on:click="() => {
                    submit()
                }"></button>
            </VeeForm>

        </div>        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                <Icon name="mdi:close" size="18"></Icon>
                {{ $t('AddDeliveryPromotionComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => {
                submitFormButton?.click();
            }">
                <Icon name="mdi:check" size="18"></Icon>
                <span>{{ $t('ShopConfigComponent.cap_nhat') }}</span>
            </button>
        </div>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm } from 'vee-validate';

const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    init_value: null,
    title: null
})

const { t } = useI18n();

var submitFormButton = ref<HTMLElement | undefined>();
const formSchema = yup.object({
    commission_percent_value: yup.number()
        .transform((value) => value == Number(value) ? value : null)
        .typeError(t('UpdatePendingTimeComponent.du_lieu_phai_la_kieu_so'))
        .min(0, 'Commission percent must be at least 0%')
        .max(100, 'Commission percent cannot exceed 100%')
        .notRequired()
});

var commission_percent = ref<{
    value: number | null,
    enabled: boolean
}>({
    value: null,
    enabled: true
});

const { handleSubmit } = useForm({
    validationSchema: formSchema,
});

var showUpdateCommissionPercentModal = ref(false)

onMounted(async () => {
    commission_percent.value = {
        value: props.init_value?.value ?? null,
        enabled: props.init_value?.enabled ?? true,
    };
    showUpdateCommissionPercentModal.value = true;
})

function validateCommissionPercent() {
    const value = commission_percent.value.value;
    if (value == null) return;
    if (value < 0) {
        commission_percent.value.value = 0;
    } else if (value > 100) {
        commission_percent.value.value = 100;
    } else {
        commission_percent.value.value = Math.round(value * 100) / 100;
    }
}

function close(value?: any) {
    emit('close', value);
}

async function submit() {
    var valid = await formSchema.isValid({
        commission_percent_value: commission_percent.value.value, 
    });
    if (valid) {
        emit('submit', JSON.parse(JSON.stringify(commission_percent.value)));
    }
    else {
        console.log('form invalid')
    }
}

</script>

<style lang="scss" src="./UpdateCommissionPercentStyles.scss"></style>
