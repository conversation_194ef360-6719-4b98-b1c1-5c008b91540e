<template>
	<VueFinalModal class="my-modal-container image-viewer-container" content-class="v-stack" :click-to-close="true"
		:modal-id="'modal_image_viewer'"
		:overlay-behavior="'persist'"
		:overlay-class="'image-viewer-overlay'"
		:key="'modal_image_viewer'"
		v-model="showImageViewerModal" contentTransition="vfm-fade" v-on:closed="() => {
			close()
		}">
		<div class="image-viewer-content-container">
			<HeaderComponent :title="itemActiveTitle">
				<template v-slot:header_left></template>
				<template v-slot:header_right>
					<button class="close-button" v-on:click="() => { close() }">
						<Icon name="iconamoon:sign-times-duotone"></Icon>
					</button>
				</template>
			</HeaderComponent>
			<div class="viewer-container">
				<Swiper v-if="listObjectToView?.length" class="my-swiper stack-carousel"
					:modules="[SwiperFreeMode, SwiperNavigation, SwiperThumbs]" :thumbs="{ swiper: thumbsSwiper }"
					:allow-touch-move="true"
					:navigation="true" :slidesPerGroup="1" :initialSlide="indexActive" :grabCursor="true"
					:slides-per-view="1" :effect="'creative'" :loop="false" :autoplay="false"
					key="image-viewer-carousel" @init="(e: any) => {
						indexActive = e.activeIndex;
						itemActiveTitle = listObjectToView[e.activeIndex].title;
					}" @slideChange="(e: any) => {
						indexActive = e.activeIndex;
					}">
					<SwiperSlide class="item-stack-slide" v-for="(itemView, indexView) of listObjectToView" v-on:click="() => {
						close()
					}" :key="`viewer_${itemView?.id}`">
						<!-- <div class="fancy-box-container">
							<FancyboxPanzoomComponent v-on:zooming="(e:any) => {
								isMovingPanzoom = e
							}" v-on:close="(e) => {
								close()
							}" :imageUrl="itemView.path ? (itemView.isBase64 ? itemView.path : (domainImage + itemView.path)) : icon_for_broken_image"
								:imageId="itemView.id">
							</FancyboxPanzoomComponent>
						</div> -->
						<img :id="`img_show_${indexView}`" loading="lazy" v-on:click="(e:any) => {
							e.stopPropagation()
						}" :src="itemView.path ? (itemView.isBase64 ? itemView.path : (domainImage + itemView.path)) : icon_for_broken_image"
							alt="" />

					</SwiperSlide>

				</Swiper>
				<div class="thumb-container" v-show="listObjectToView?.length > 1">
					<swiper @swiper="setThumbsSwiper" :spaceBetween="10" :slidesPerView="4" :freeMode="true"
						:watchSlidesProgress="true" :modules="[SwiperFreeMode, SwiperNavigation, SwiperThumbs]"
						:grabCursor="true" :centeredSlides="true" :centeredSlidesBounds="true" class="my-swiper-thumb">
						<SwiperSlide v-for="(itemView, indexView) of listObjectToView" class="item-swiper-thumb"
							:key="`viewer_thumb_${itemView?.id}`">
							<img loading="lazy"
								:src="itemView.path ? (itemView.isBase64 ? itemView.path : (domainImage + itemView.path)) : icon_for_broken_image"
								alt=""/>
						</SwiperSlide>
					</swiper>
				</div>
				<!-- <cropper class="cropper" @change="(e: any) => {
					console.log(e);
				}" @flip="(e: any) => { console.log(e) }" :stencil-props="{
						movable: true,
						resizable: true,
					}" :src="listObjectToView[0].path ? (listObjectToView[0].isBase64 ? listObjectToView[0].path : (domainImage + listObjectToView[0].path)) : icon_for_broken_image" /> -->

			</div>

		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName } from "~/assets/AppConst";
import { Cropper } from 'vue-advanced-cropper'

const nuxtApp = useNuxtApp();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close']);
const props = defineProps({
	listObject: null,
	indexActive: 0 as any,
	showImageViewerModal: false as any
})

var showImageViewerModal = ref(props.showImageViewerModal);
var listObjectToView = ref(props.listObject);
var indexActive = ref(props.indexActive);

var itemActiveTitle = ref("");
var isMovingPanzoom = ref(false);
const thumbsSwiper = ref(null);
onUpdated(() => {
	showImageViewerModal.value = props.showImageViewerModal
	listObjectToView.value = props.listObject;
	indexActive.value = props.indexActive;
})
onMounted(async () => {
})

function close(value?: any) {
	showImageViewerModal.value = false;
	emit('close', value);
}
var setThumbsSwiper = (swiper: any) => {
	thumbsSwiper.value = swiper;
};
</script>

<style lang="scss" src="./ImageViewerStyles.scss"></style>