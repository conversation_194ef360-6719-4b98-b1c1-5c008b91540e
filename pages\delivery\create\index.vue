<template>
    <CreateDeliveryComponent/>
</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import CreateDeliveryComponent from '~/components/delivery/create/CreateDeliveryComponent.vue';

const nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.CreateDeliveryComponent')
})
onBeforeMount(()=>{
    nuxtApp.$emit(appConst.event_key.show_footer, false)
})
</script>