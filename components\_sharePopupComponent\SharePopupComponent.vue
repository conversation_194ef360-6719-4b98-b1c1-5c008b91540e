<template>
    <VueFinalModal 
        class="my-modal-container" :overlay-behavior="'persist'"
        content-class="v-stack share-popup-container" 
        :click-to-close="true"
		v-model="showShareModal" 
        contentTransition="vfm-fade" 
        v-on:closed="() => {
			close()
		}">
        <div class="zalo-share-button" id="share_zalo_button"
            :data-href="props.link_share"
            :data-oaid="zaloConfig.appIDZaloLogin" :data-customize='true'>
            {{ $t('ShopComponent.chia_se_qua_zalo') }}
        </div>
		<span class="share-title">{{ $t('ShopComponent.chia_se') }}</span>
        <button class="zalo" v-if="!webInApp" v-on:click="()=>{shareToZalo()}">
			<img :src="logo_zalo" loading="lazy">
            {{ $t('SharePopupComponent.chia_se_qua_zalo') }}
        </button>
        <button class="facebook" v-if="!webInApp" v-on:click="()=>{shareToFacebook()}">
			<Icon name="dashicons:facebook-alt"></Icon>
            {{ $t('SharePopupComponent.chia_se_qua_facebook') }}
        </button>
        <button class="app" v-if="webInApp" v-on:click="()=>{shareToApp()}">
            {{ $t('SharePopupComponent.chia_se_qua_ung_dung') }}
        </button>
        <span class="link-text">
            <Icon name="solar:link-linear"></Icon> 
			<span>{{ props.link_share }}</span>
			
        </span>
        <button class="copy-link" v-on:click="()=>{copyToClipboard()}">
            {{ $t('SharePopupComponent.sao_chep_link') }}
        </button>
    </VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
const props = defineProps({
    link_share: null,
    name_share: null,
    image_share: null
});
const emit = defineEmits(['close']);
const video = ref<HTMLVideoElement | null>(null);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var showShareModal = ref(false);
var webInApp = ref(null as any);

onMounted(async () => {

    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
    showShareModal.value = true;
	loadScript();
});
function loadScript() {
	const script = document?.createElement('script');
	script.type = 'text/javascript';
	script.src = 'https://sp.zalo.me/plugins/sdk.js';
	script.async = false;
	script.defer = true;
	script.id = "zalo_share";
	document?.body.appendChild(script);
}
function shareToZalo() {
	document?.getElementById('share_zalo_button')?.click();
}
function shareToApp() {

	let messageShare = {
		message: props.name_share,
		url: props.link_share,
		filename: props.image_share ? props.image_share : baseLogoUrl
	}

	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.share,
		data: messageShare
	})
}
function shareToFacebook() {
	let url = props.link_share;
	let hrefLinkShare = "https://www.facebook.com/sharer/sharer.php?u=" + url;
	var x = (window.outerWidth) / 2 - 350;
	var y = (window.outerHeight) / 2 - 300;
	var responsive = "width=700,height=600,left=" + x + ",top=" + y
	window.open(hrefLinkShare, 'name', responsive)
	// close();
}

function copyToClipboard() {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(props.link_share);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: props.link_share
		})
	}
	toast.info(t('ShopComponent.da_sao_chep_lien_ket'), {
		hideProgressBar: true,
	})
	close();
}

function close(){
    showShareModal.value = false
    emit('close')
}
</script>

<style lang="scss" src="./SharePopupStyles.scss"></style>