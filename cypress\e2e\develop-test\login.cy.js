/// <reference types="Cypress" />
import { environment } from '/assets/environment/environment.dev.ts';
describe('Login Test', () => {
    it('should log in successfully', () => {
      // Visit the login page
      cy.visit('https://' + environment.domain + '/login'); // replace with your login page URL
  
      // Type username and password
      cy.wait(1000)
      cy.get('input[name="user-name"]').type('tester01');
      cy.get('input[name="password"]').type('12346789@ArK');
  
      // Click the login button
      cy.get('button#login-submit').click();
      cy.wait(3000)
      // Assert that the login is successful
      cy.url().should('include', '/home'); // replace with the expected dashboard URL
    //   cy.contains('Welcome'); // replace with an element that indicates a successful login
    });
  });