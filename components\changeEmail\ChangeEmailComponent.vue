<template>
  <VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false" :overlay-behavior="'persist'"
    content-class="v-stack change-email-modal" v-model="showChangeEmailModal" v-on:closed="() => {
      showChangeEmailModal = false
    }" contentTransition="vfm-fade">
    <div class="change-email-container">
      <HeaderComponent :title="title">
        <template v-slot:header_left></template>
        <template v-slot:header_right>
          <button v-on:click="close()">
            <Icon name="clarity:times-line"></Icon>
          </button>
        </template>
      </HeaderComponent>
      <div class="change-email-content">
        <div class="step-content" v-if="step == 1">
          <div class="v-stack">
            <span class="label" v-html="$t('ChangeEmailComponent.ma_xac_thuc_da_gui_toi_email', {
              'email': 'current_email'
            }).replaceAll('current_email', `<em>${getEmailHiding(profileData.email)}</em>`)"></span>
            <div class="content-input-group">
              <input :title="$t('ChangeEmailComponent.ma_xac_thuc_email')" type="phone" maxLength=255
                class='content-input input-custom' :placeholder="$t('ChangeEmailComponent.nhap_ma_xac_thuc')"
                v-model="otpCurrentEmail" v-on:input="($event: any) => {
                  otpCurrentEmail = $event.currentTarget.value;
                }" />
              <button class="send-code" v-on:click="() => { sendCodeOTPCurrentEmail() }" :disabled="isGettingOTP"
                v-if="!time_remaining_current_email">
                <Icon name="eos-icons:loading" v-if="isGettingOTP"></Icon> {{ $t('ChangeEmailComponent.lay_ma') }}
              </button>
              <button v-if="time_remaining_current_email > 0" class="send-code">{{
                $t('ChangeEmailComponent.lay_lai_sau')
              }}&nbsp;
                <vue-countdown :time="time_remaining_current_email * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                  time_remaining_current_email = 0;
                }" v-on:progress="({ totalSeconds }) => { }">
                  {{ minutes ? minutes + $t('ChangeEmailComponent.phut') : '' }} {{ seconds }} {{
                    $t('ChangeEmailComponent.giay') }}
                </vue-countdown>
              </button>
            </div>
            <span class="error-message">{{ otpCurrentEmailErr }}</span>
          </div>
          <div class="change-email-footer">
            <button class='accept-button' :disabled="isSubmitting" v-on:click="() => {
              if (otpCurrentEmail) {
                confirmOTPCurrentEmail()
              }
              else {
                otpCurrentEmailErr = t('ChangeEmailComponent.vui_long_nhap_ma_xac_thuc');
                hightlightError()
              }
            }">
              <Icon name="eos-icons:loading" v-if="isSubmitting"></Icon> {{ $t('ChangeEmailComponent.xac_thuc') }}
            </button>
          </div>
        </div>
        <div class="step-content" v-if="step == 2">
          <div class="v-stack">
            <span class="required label"> {{ $t('ChangeEmailComponent.email_moi') }} </span>
            <input :title="$t('ChangeEmailComponent.email_moi')" type="text" name="current-email" maxLength="255"
              autoComplete="name" class="input-custom" :placeholder="$t('ChangeEmailComponent.email_moi_placeholder')"
              :value="newEmail || null" v-on:input="($event: any) => {
                newEmail = nonAccentVietnamese($event.currentTarget.value)
                newEmailValidation()
              }" v-on:blur="() => {
                newEmailValidation()
              }
                " />
            <span class="error-message">{{ newEmailErr }}</span>
          </div>
          <div class="v-stack">
            <span class="label required"> {{ $t('ChangeEmailComponent.ma_xac_thuc_email') }}</span>
            <div class="content-input-group">
              <input :title="$t('ChangeEmailComponent.ma_xac_thuc_email')" type="phone" maxLength=255
                :disabled="!newEmail.length || newEmailErr.length > 0" class='content-input input-custom'
                :placeholder="$t('ChangeEmailComponent.nhap_ma_xac_thuc')" v-model="otpNewEmail" v-on:input="($event: any) => {
                  otpNewEmail = $event.currentTarget.value;
                  otpNewEmailErr = '';
                }" />
              <button class="send-code" :disabled="!newEmail.length || newEmailErr.length > 0 || isGettingOTP"
                v-on:click="() => { sendCodeOTPNewEmail() }" v-if="!time_remaining_new_email">
                <Icon name="eos-icons:loading" v-if="isGettingOTP"></Icon> {{ $t('ChangeEmailComponent.lay_ma') }}
              </button>
              <button v-if="time_remaining_new_email > 0" :disabled="!newEmail.length || newEmailErr.length > 0"
                class="send-code">{{
                  $t('ChangeEmailComponent.lay_lai_sau')
                }}&nbsp;
                <vue-countdown :time="time_remaining_new_email * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                  time_remaining_new_email = 0;
                }" v-on:progress="({ totalSeconds }) => { }">
                  {{ minutes ? minutes + $t('ChangeEmailComponent.phut') : '' }} {{ seconds }} {{
                    $t('ChangeEmailComponent.giay') }}
                </vue-countdown>
              </button>
            </div>
            <span class="error-message">{{ otpNewEmailErr }}</span>
          </div>
          <div class="change-email-footer">
            <button class='accept-button' :disabled="isSubmitting" v-on:click="() => {
              submit()
            }">
              {{ $t('ChangeEmailComponent.luu') }}
            </button>
          </div>
        </div>

      </div>

    </div>
  </VueFinalModal>
</template>

<style lang="scss" src="./ChangeEmailStyles.scss"></style>

<script setup lang="ts">
import { appConst, appDataStartup, encryptPassword, nonAccentVietnamese } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import Vue3Toastify, { toast } from "vue3-toastify";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import { PublicService } from "~/services/publicService/publicService";
import VueCountdown from '@chenfengyuan/vue-countdown';

import change_password from "~/assets/image/change-password.png"
import { VueFinalModal } from "vue-final-modal";
import { HttpStatusCode } from "axios";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
var emit = defineEmits(['close']);

let authService = new AuthService();
let userService = new UserService();
let publicService = new PublicService();
let placeService = new PlaceService();
let shopService = new ShopService();
let productService = new ProductService();

var step = ref(1);
var profileData = ref(null as any);
var showChangeEmailModal = ref(false);

var newEmail = ref("");
var newEmailErr = ref("");
var otpCurrentEmail = ref(null as any);
var otpCurrentEmailErr = ref("");
var otpNewEmail = ref(null as any);
var otpNewEmailErr = ref("");


let title = ref(t('ChangeEmailComponent.doi_email'));
var time_remaining_current_email = ref(0);
var time_remaining_new_email = ref(0);
var isGettingOTP = ref(false);
var isSubmitting = ref(false);
var checkExistingEmailTimeout: any;

onUnmounted(async () => { });
onMounted(async () => {
  useSeoMeta({
    title: title.value,
  });

  let profile$ = JSON.parse(localStorage.getItem(appConst.storageKey.userInfo) as string);
  profileData.value = profile$;
  if (profileData.value?.email) {
    step.value = 1;
  }
  else {
    step.value = 2;
  }

  showChangeEmailModal.value = true;
});
function newEmailValidation() {
  let re = appConst.validateValue.email;
  if (newEmail.value?.length) {
    if (!re.test(newEmail.value)) {
      newEmailErr.value = t('ChangeEmailComponent.email_khong_dung_dinh_dang');
      return;
    } else {
      clearTimeout(checkExistingEmailTimeout);
      checkExistingEmailTimeout = setTimeout(async () => {
        newEmailErr.value = await checkExistingEmail(newEmail.value);
      }, 500);
    }
  }
  else {
    newEmailErr.value = t('ChangeEmailComponent.vui_long_nhap_email_moi');
  }
}
async function checkExistingEmail(email: any) {
  let body = {
    key: email,
    type: appConst.check_exist_enum.email,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check && email) {
    return t('ChangeEmailComponent.email_da_duoc_su_dung');
  }
  return "";
}
function sendCodeOTPCurrentEmail() {
  isGettingOTP.value = true;
  publicService.getOTP(
    profileData.value?.email,
    'email'
  ).then(res => {
    if (res.status == HttpStatusCode.Ok) {
      time_remaining_current_email.value = res.body?.data?.otp_cooldown;
    }
    else {
      time_remaining_current_email.value = res.otp_cooldown;
    }
    isGettingOTP.value = false;
  })
}
function sendCodeOTPNewEmail() {
  isGettingOTP.value = true;
  publicService.getOTP(
    newEmail.value,
    'email'
  ).then(res => {
    if (res.status == HttpStatusCode.Ok) {
      time_remaining_new_email.value = res.body?.data?.otp_cooldown;
    }
    else {
      time_remaining_new_email.value = 0;
    }
    isGettingOTP.value = false;
  })
}

async function submit() {
  if (submitValidation() == true) {
    isSubmitting.value = true;
    let body = {
      email: newEmail.value,
      otp: otpNewEmail.value
    }
    userService.changeEmail(body).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        toast.success(t('ChangeEmailComponent.doi_email_thanh_cong'));
        close(true);
      }
      else {
        toast.error(res.body?.message ?? t('ChangeEmailComponent.doi_email_that_bai'));
        handleSubmitFailed(res)
      }
      isSubmitting.value = false;
    }).catch(err => {
      console.log(err);

      isSubmitting.value = false;
      handleSubmitFailed()
    });
  }
  else {
    hightlightError();
  }

}
function submitValidation() {
  newEmailValidation();
  if (!otpNewEmail.value) {
    otpNewEmailErr.value = t('ChangeEmailComponent.vui_long_nhap_ma_xac_thuc')
    return false;
  }
  if (newEmailErr.value) {
    return false
  }
  return true;
}
function handleSubmitFailed(response?: any) {
  if (response) {
    if (response.status == HttpStatusCode.Unauthorized) {
      router.push(appRoute.LoginComponent);
    }
    else {
      let indexOTPFailed = response.errors.findIndex(function (er: any) {
        return er.field == 'otp'
      });

      if (indexOTPFailed != -1) {
        if (otpNewEmail.value) {
          otpNewEmailErr.value = t('ChangeEmailComponent.ma_xac_thuc_sai');
        }
        else {
          otpNewEmailErr.value = t('ChangeEmailComponent.vui_long_nhap_ma_xac_thuc');
        }
      }
      else {
        otpNewEmailErr.value = ''
      }

      let indexEmailFailed = response.errors.findIndex(function (er: any) {
        return er.field == 'email'
      });

      if (indexEmailFailed != -1) {
        newEmailErr.value = t('ChangeEmailComponent.email_khong_dung_dinh_dang');
      }
      else {
        newEmailErr.value = ''
      }
    }

  }
  hightlightError();
}
function close(submit = false) {
  emit('close', submit);
}

function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    // Do stuff here
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}

function getEmailHiding(email: string) {
  let indexAt = email.indexOf("@");
  var startIndex = indexAt * .2 | 0;
  var endIndex = indexAt * .9 | 0;
  return email.slice(0, startIndex) +
    email.slice(startIndex, endIndex).replace(/./g, '*') +
    email.slice(endIndex);
}

async function confirmOTPCurrentEmail() {
  isSubmitting.value = true;
  let data$;
  data$ = {
    email: profileData.value.email,
    otp: otpCurrentEmail.value
  }

  let confirm = await publicService.confirmOtp(data$);
  if (confirm.status == HttpStatusCode.Ok) {
    if (confirm.body.data == true) {
      otpCurrentEmailErr.value = '';
      step.value = 2;
    }
    else {
      otpCurrentEmailErr.value = t('OTPConfirmComponent.ma_xac_thuc_sai')
    }
  }
  else {
    otpCurrentEmailErr.value = t('OTPConfirmComponent.ma_xac_thuc_sai')
  }
  isSubmitting.value = false;
  hightlightError();
}
</script>
