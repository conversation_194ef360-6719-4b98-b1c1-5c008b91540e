<template>
	<RenderCacheable :cache-key="[locale].join('--')" class="public-container h-fit footer-public-container">
		<div class="footer-v2-container">
			<nuxt-link :to="appRoute.HomeComponent" class='footer-button'
				:class="{ 'active': checkActive() == appRoute.HomeComponent }" v-on:click="() => {
					if (checkActive() == appRoute.HomeComponent) {
						nuxtApp.$emit(appConst.event_key.refresh_home)
					}
				}">
				<!-- <img loading="lazy" :src="home_icon" :placeholder="home_icon" alt="" /> -->
				<Icon name="solar:home-smile-bold"></Icon>
				<span>
					{{ $t("FooterV2Component.trang_chu") }}
				</span>
			</nuxt-link>

			<nuxt-link :to="appRoute.ChatManageComponent" class='footer-button'
				:class="{ 'active': checkActive() == appRoute.ChatManageComponent }">
				<Icon name="mynaui:chat-dots" />
				<span>
					{{ $t("FooterV2Component.chat") }}
				</span>
				<em v-if="unreadMessageCount && unreadMessageCount > 0">
					{{ unreadMessageCount <= 10 ? unreadMessageCount : "10+" }} </em>
			</nuxt-link>

			<nuxt-link :to="appRoute.AroundComponent" class='footer-button'
				:class="{ 'active': checkActive() == appRoute.AroundComponent }">

				<div class="middle-button">
					<div>
						<Icon name="solar:point-on-map-bold" />
					</div>
				</div>
				<!-- <Icon name="gis:pois-o" />
				<span>
					{{ $t("FooterV2Component.gan_day") }}
				</span> -->

			</nuxt-link>

			<nuxt-link :to="appRoute.CartComponent" class='footer-button' id="cart_footer"
				:class="{ 'active': checkActive() == appRoute.CartComponent }">
				<Icon name="solar:cart-3-linear"></Icon>
				<span>
					{{ $t("FooterV2Component.gio_hang") }}
				</span>
				<em v-if="cartData && cartData.length">
					{{ cartData.length <= 10 ? cartData.length : "10+" }} </em>

			</nuxt-link>


			<nuxt-link :to="appRoute.ProfileComponent" class='footer-button'
				:class="{ 'active': checkActive() == appRoute.ProfileComponent }">
				<Icon name="solar:user-circle-linear" />
				<span>
					{{ $t("FooterV2Component.toi") }}
				</span>

			</nuxt-link>
		</div>
	</RenderCacheable>

</template>

<style lang="scss" src="./FooterV2Styles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appConst, appDataStartup } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { member_type } from "../chatManage/ChatDTO";
import { HttpStatusCode } from "axios";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();
let dataCategory = appDataStartup.listCategory;
let dataFilterSort = [
	{
		label: "Giá từ thấp đến cao",
		value: 1,
	},
	{
		label: "Giá từ cao đến thấp",
		value: 2,
	},
	{
		label: "Mới nhất",
		value: 3,
	},
	// {
	//     label: "Sản phẩm bán chạy",
	//     value: 4
	// },
	// {
	//     label: "Sản phẩm khuyến mãi",
	//     value: 5
	// }
];
let authService = new AuthService();
let userService = new UserService();
let placeService = new PlaceService();
let shopService = new ShopService();
let productService = new ProductService();
let chatService = new ChatService();
let isShowList = ref(true);
let cartData = ref();
let profileInfo = ref<any>();
let unreadMessageCount = ref(0);
onUnmounted(async () => {
	nuxtApp.$unsubscribe(appConst.event_key.check_unread_message, getUnreadMessageCount);
	nuxtApp.$unsubscribe(appConst.event_key.cart_change);
	nuxtApp.$unsubscribe(appConst.event_key.cart_add_animation);
	nuxtApp.$unsubscribe(appConst.event_key.login);
	nuxtApp.$unsubscribe(appConst.event_key.logout);
});
onBeforeMount(async () => {
	profileInfo.value = await authService.checkAuth();
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCart();
	})

	nuxtApp.$listen(appConst.event_key.cart_add_animation, (params: any) => {
		runAnimationAddToCart(params.containerId, params.imgId)
	})

	nuxtApp.$listen(appConst.event_key.login, async ()=>{
		profileInfo.value = await authService.checkAuth();
		setTimeout(async () => {
			await getUnreadMessageCount();
		}, 1000);
		
		
	});

	nuxtApp.$listen(appConst.event_key.logout, async ()=>{
		profileInfo.value = await authService.checkAuth();
		setTimeout(async () => {
			await getUnreadMessageCount();
		}, 1000);
	});

	nuxtApp.$listen(appConst.event_key.check_unread_message, getUnreadMessageCount);
	getUnreadMessageCount();

	getCart();
});

function checkActive() {
	if (route.path.includes(appRoute.AroundComponent)) return appRoute.AroundComponent;
	if (route.path.includes(appRoute.HomeComponent)) return appRoute.HomeComponent;
	if (route.path.includes(appRoute.ProfileComponent)) return appRoute.ProfileComponent;
	if (route.path.includes(appRoute.CartComponent)) return appRoute.CartComponent;
	if (route.path.includes(appRoute.SearchComponent)) return appRoute.SearchComponent;
	if (route.path.includes(appRoute.ReelsComponent)) return appRoute.ReelsComponent;
	if (route.path.includes(appRoute.ChatManageComponent)) return appRoute.ChatManageComponent;
	return '';
}

function getCart() {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$;
}

function runAnimationAddToCart(containerElId: string, imgElId: string) {
	let valueElement = document.getElementById(containerElId) as HTMLElement;

	let imgEl = document.getElementById(imgElId) as HTMLElement;
	let elClone = imgEl.cloneNode(true) as HTMLElement;
	elClone.style.background = 'white';
	valueElement.appendChild(elClone);

	let begintop = imgEl?.offsetTop;
	let beginLeft = imgEl?.offsetLeft;
	let beginWidth = imgEl?.offsetWidth;

	let destinationElement = document.getElementById('cart_footer');
	let destinationTop = destinationElement?.getBoundingClientRect().top;
	let destinationLeft = destinationElement?.getBoundingClientRect().left;

	elClone.style.position = 'absolute'
	elClone.style.top = begintop?.toString() + "px";
	elClone.style.left = (beginLeft)?.toString() + "px";
	elClone.style.zIndex = '10000';
	elClone.style.transition = 'scale .3s ease-in-out, top 2s ease, left 2s ease'

	elClone.style.borderRadius = '100px'
	setTimeout(() => {
		elClone.style.scale = '.15'
		elClone.style.top = `${destinationTop}px`;
		elClone.style.left = `${destinationLeft}px`;

		setTimeout(() => {
			elClone.remove();
			document.getElementById('cart_footer')?.classList.add('shake');
			setTimeout(() => {
				document.getElementById('cart_footer')?.classList.remove('shake')
			}, 500);
		}, 400)

	}, 200);
}

const getUnreadMessageCount = () => {
	return new Promise(async (resolve) => {
		// chatService.listChannel(profileInfo.value?.id, member_type.user).then((listChannel) => {
		// 	if (listChannel.status == HttpStatusCode.Ok) {
		// 		unreadMessageCount.value = listChannel.data.unread_count;
		// 		resolve(listChannel.data);
		// 	}
		// 	else {
		// 		resolve(null);
		// 	}

		// }).catch(() => {
		// 	resolve(null);
		// });
		if (profileInfo.value?.id) {
			chatService.countUnreadMessage().then((res) => {
				unreadMessageCount.value = (res.body?.data?.user ?? 0) + (res.body?.data?.shop ?? 0) + (res.body?.data?.agent ?? 0);
				resolve(res);
			}).catch(() => {
				unreadMessageCount.value = 0;
				resolve(0);
			})
		}
		else{
			unreadMessageCount.value = 0;
			resolve(0);
		}

	})
}
</script>
