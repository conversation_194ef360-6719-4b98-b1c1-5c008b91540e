<template>
    <VueFinalModal class="my-modal-container" :modal-id="'update_holiday_setting_modal'" :keep-overlay="true"
        :overlay-behavior="'persist'" :hide-overlay="false" :hide-overlay-on-blur="false"
        content-class="v-stack form-modal update-holiday-setting-container" :click-to-close="false"
        :esc-to-close="false" v-on:before-close="() => {
            showModal = false
        }" v-model="showModal" contentTransition="vfm-slide-down">
        <SubHeaderV2Component :title="props.title ?? $t('UpdateHolidaySettingComponent.lich_dong_ua')">
            <template v-slot:header_left></template>
        </SubHeaderV2Component>
        <div class="update-holiday-setting-content-container">
            <div v-if="props.mode == day_off_type.days_off" id="days_off_container">
                <v-btn class="add-holiday" variant="outlined" v-on:click="() => {
                    data_holiday_setting.splice(0, 0, {
                        date: null,
                        date_format_picker: null,
                        reason: {
                            vi: null,
                            en: null,
                            ko: null,
                            ru: null
                        }
                    })
                }">
                    {{ $t('UpdateHolidaySettingComponent.them_ngay_nghi') }}
                </v-btn>
                <div class="holiday-list-container" v-if="data_holiday_setting.length">
                    <div class="holiday-item" v-for="(itemHoliday, indexItem) in data_holiday_setting" hide-actions
                        static>
                        <div :class="'holiday-item-info'">
                            <v-menu class="bootstrap-dropdown-container" location="bottom left" contained
                                :close-on-content-click="false" :ref="`selectDate${indexItem}`" v-model="itemHoliday.open_menu">
                                <template v-slot:activator="{ props }">
                                    <v-btn class="hoilday-text" v-bind="props" :variant="itemHoliday.date ? 'tonal' : 'outlined'">
                                        <span>
                                            {{ itemHoliday.date ?
                                                moment(itemHoliday.date).format(appConst.formatDate.toShow) : $t('UpdateHolidaySettingComponent.chon_ngay')
                                            }}
                                        </span>
                                    </v-btn>
                                </template>
                                <v-date-picker 
                                    class="holiday-picker" 
                                    :hide-header="true"
                                    v-model:model-value="itemHoliday.date_format_picker"
                                    v-on:update:model-value="()=>{
                                        itemHoliday.open_menu = false;
                                        itemHoliday.date = moment(itemHoliday.date_format_picker).format(appConst.formatDate.toSave)
                                    }"
                                    :allowed-dates="(date: any) => {
                                        let indexInCurrentData = data_holiday_setting.findIndex(function(e:any){
                                            return moment(e.date).isSame(date)
                                        });
                                        return indexInCurrentData == -1
                                    }"
                                    show-adjacent-months
                                    :min="dateFormat(new Date(), 'yyyy-MM-dd')"
                                ></v-date-picker>
                            </v-menu>

                            <v-btn class="delete-holiday-item" variant="tonal" v-on:click="() => {
                                data_holiday_setting.splice(indexItem, 1)
                            }">
                                <Icon name="iconamoon:trash-light"></Icon>
                            </v-btn>
                        </div>
                        <div class="holiday-item-reason">
                            <label> {{ $t('UpdateHolidaySettingComponent.ly_do') }}: </label>
                            <div class="reason-item" v-for="(key, value) in itemHoliday.reason">
                                <label :for="`reason_${indexItem}_${value}`">
                                    {{ $t(`LocalizedLanguageName.${value}`) }}:
                                </label>
                                <UTextarea class="text-area-custom" autoresize :maxrows="3" :rows="1"
                                    :max="5000"
                                    :placeholder="$t(`UpdateHolidaySettingComponent.nhap_ly_do_${value}`)" v-model="itemHoliday.reason[value]"
                                    :id="`reason_${indexItem}_${value}`"></UTextarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="holiday-list-container none-list" v-else>
                    <em>{{ $t('UpdateHolidaySettingComponent.chua_cap_nhat') }}</em>
                </div>

            </div>
            <div v-if="props.mode == day_off_type.day_off_week" id="day_of_week_off_container">

            </div>
            <div v-if="props.mode == day_off_type.day_off_month_lunar" id="day_of_week_off_container">

            </div>
        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                {{ $t('UpdateHolidaySettingComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => {
                if(props.mode == day_off_type.days_off){
                    emit('submit_days_off', data_holiday_setting)
                }
                else if(props.mode == day_off_type.day_off_week) {
                    emit('submit_day_off_week', data_holiday_setting)
                }
                else if(props.mode == day_off_type.day_off_month_lunar){
                    emit('submit_day_off_month_lunar', data_holiday_setting)
                }
                else{
                    emit('submit', data_holiday_setting)
                }
            }">
                <span>{{ $t('UpdateHolidaySettingComponent.hoan_tat') }}</span>
            </button>
        </div>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import { format as dateFormat } from "date-fns";
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, nonAccentVietnamese } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { DeliveryFeeConfig, type delivery_fee_condition, type delivery_fee_config, condition_params, operators, condition_relationship, day_off_type } from '../ShopConfigDTO';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit', 'submit_days_off', 'submit_day_off_week', 'submit_day_off_month_lunar']);
const props = defineProps({
    init_value: null,
    mode: null,
    title: null,
})

var nuxtApp = useNuxtApp();
const { t, locale, locales, availableLocales } = useI18n();

var showModal = ref(false);

var data_holiday_setting = ref<any>(null)
onBeforeUnmount(() => {
})
onBeforeMount(() => {

})
onMounted(async () => {
    console.log(props.init_value);
    data_holiday_setting.value = JSON.parse(JSON.stringify(props.init_value));
    data_holiday_setting.value.map((itemHoliday:any)=>{
        itemHoliday.date_format_picker = new Date(itemHoliday.date);
    })
    showModal.value = true;
})
onUpdated(() => {
})

function close(value?: any) {
    emit('close', value);
}

async function submit() {

    emit('submit', JSON.parse(JSON.stringify(data_holiday_setting.value)));
}

</script>

<style lang="scss">
@import url("./UpdateHolidaySettingStyles.scss");
</style>