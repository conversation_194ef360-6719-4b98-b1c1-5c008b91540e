<template>
	<div class="product-nearby-list content-list">
		<div class="stack-content-title">
			<span class="section-title">
				<Icon name="gis:signpost"></Icon>
				{{ $t('HomeV2Component.san_pham_gan_ban') }}
			</span>
			<nuxt-link :to="{
				path: appRoute.AroundComponent,
				query: {
					filter: JSON.stringify({
						...props.filter_data,
						sortBy: filter_sort.gan_nhat
					})
				}
			}" class="view-all">
				{{ $t('HomeV2Component.xem_tat_ca') }}
				<Icon name="ic:round-chevron-right"></Icon>
			</nuxt-link>
		</div>
		<div class="stack-content-list">
			<div class="stack-content-list-container" v-if="listProductNearby?.length && !loadingProduct">
				<div class="item-stack-slide" v-for="item of listProductNearby" :key="'product_nearby_' + item.id"
					:id="'product_nearby_' + item.id">
					<nuxt-link :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
						class="item-stack" :title="showTranslateProductName(item)">
						<span class="distance" v-if="item.distance">
							{{ parseFloat(item.distance) > 1000 ? (parseFloat(item.distance) /
								1000).toFixed(1) :
								(parseFloat(item.distance)).toFixed(0) }}
							<em>{{ parseFloat(item.distance) > 1000 ? 'km' : 'm' }}</em> </span>
						<img loading="lazy" :src="item && item.profile_picture
							? domainImage + item.profile_picture
							: icon_for_product" :placeholder="icon_for_product" />
						<!-- <div class="shop-stack-content">
							<AvatarComponent class="shop-logo" :imgTitle="item?.shop?.name"
								:imgStyle="item?.shop?.logo?.style" :imgSrc="item?.shop?.logo?.path?.length
									? (domainImage + item?.shop?.logo?.path)
									: ''" :width="40" :height="40" />
							<span class="shop-name">
								<span>
									{{ item?.shop?.name }}
								</span>
							</span>
						</div> -->
						<div class="item-stack-content">
							<span class="name">{{ showTranslateProductName(item) }}</span>
							<span class="price">
								{{
									(item.price_off != null && item.price_off < item.price) ?
										formatCurrency(parseFloat(item.price_off), item.shop ? item.shop.currency :
											item.currency) : (parseFloat(item.price) == 0 || item.price == null) ?
											$t('HomeV2Component.gia_lien_he') : formatCurrency(parseFloat(item.price), item.shop
												? item.shop.currency : item.currency) }} <em class="off"
									v-if="(item.price_off != null && item.price_off < item.price)">{{
										(parseFloat(item.price) == 0 || item.price == null)
											? $t('HomeV2Component.gia_lien_he')
											: formatCurrency(parseFloat(item.price), item.shop ?
												item.shop.currency
												: item.currency)
									}}</em>
							</span>
						</div>
					</nuxt-link>
				</div>

			</div>
			<div v-else class="none-content-list">
				<!-- {{ loadingProduct ? '' : $t('HomeV2Component.chua_co_san_pham') }} -->
			</div>
			<v-overlay v-model="loadingProduct" :z-index="100" :absolute="false" contained
				content-class='spinner-container' persistent scrim="#fff" key="loading_hot_deal" no-click-animation>
				<Icon name="eos-icons:loading"></Icon>
			</v-overlay>
		</div>
	</div>
</template>

<style lang="scss" src="./ProductNearbySectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { filter_sort, type CartDto } from "~/assets/appDTO";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();

const props = defineProps({
	filter_data: null,
	load_more: null,
	enable_load: null
})

var listProductNearby = useState<any>('dashboard_product_nearby', () => { return [] });
var listProductNearbyCount = ref(0);
var loadingProduct = ref(false);

var publicService = new PublicService();

watch(() => [props.enable_load], () => {
	if (props.enable_load) {
		initProductAround()
	}
})

onBeforeMount(() => {

})
onUnmounted(async () => {
});
onMounted(async () => {
	// initProductAround()
});

function initProductAround() {
	if (props.filter_data.latitude_user != null && props.filter_data.longitude_user != null) {
		let body = {
			section: "product_around", //suggest,sale_off,best_around,hot_deal,hot_sale, product_around
			latitude_user: props.filter_data.latitude_user,
			longitude_user: props.filter_data.longitude_user
		}
		if (!listProductNearby.value?.length)
			publicService.dashboard({ ...body, section: 'product_around', limit: 20 }).then(res => {
				if (res.status == HttpStatusCode.Ok) {
					listProductNearby.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : [];
					listProductNearbyCount.value = res.body.data?.count
				}

				loadingProduct.value = false;
			});
	}
}

</script>
