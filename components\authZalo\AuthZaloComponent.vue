<template>
	<div class="public-container">
		<div class="stack zalo-oauth-container">
			<img :src="logo_zalo" :placeholder="logo_zalo" alt="zalo" />
			<span>
				{{ $t('AuthZaloComponent.dang_cho_xac_nhan') }}
			</span>

			<VueFinalModal class="my-modal-container" content-class="v-stack user-disabled-modal" :overlay-behavior="'persist'"
				v-model="showUserDisabledModal" v-on:closed="() => {
					showUserDisabledModal = false
				}" contentTransition="vfm-slide-up">
				<div>
					<button v-on:click="() => {
						showUserDisabledModal = false
					}">
						<Icon name="material-symbols:cancel-outline"></Icon>
					</button>
					<div class='v-stack'>
						<img loading="lazy" :src="access_denied" :placeholde="access_denied" />
						<p class='message'>
							{{ $t('LoginComponent.tai_khoan_da_bi_khoa') }}
						</p>
						<p class='message'>
							{{ $t('LoginComponent.vui_long_lien_he') }}
							<nuxt-link :to="appRoute.SupportComponent">{{ $t('LoginComponent.ho_tro')
								}}</nuxt-link>
						</p>
					</div>

				</div>
			</VueFinalModal>
		</div>
	</div>


</template>

<script lang="ts" setup>
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.OAuthZaloComponent')
});
import axios, { HttpStatusCode } from 'axios';
import { toast } from 'vue3-toastify';
import { appConst } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { FCMService } from '~/services/fcmService';
import { ImageService } from '~/services/imageService/imageService';

import access_denied from "~/assets/image/access_denied.png";
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import { VueFinalModal } from 'vue-final-modal';

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
var code = ref(route.query.code);

var authService = new AuthService();
var imageService = new ImageService();
var fcmService = new FCMService();
var redirect_url = ref<any>(route.query.redirect ?? null);

var showUserDisabledModal = ref(false);
onUnmounted(()=>{
	nuxtApp.$unsubscribe(appConst.event_key.require_login)
})

onMounted(async () => {
	let codeTemp = code.value ? code.value : '';
	if (codeTemp) {
		let accessData = await authService.getZaloAccesstoken(codeTemp as string);
		if (accessData.access_token) {
			let zaloInfo = await authService.getZaloUserInfo(accessData.access_token);
			if (!zaloInfo.error) {
				let body = {
					id: zaloInfo.id,
					name: zaloInfo.name,
					picture: zaloInfo.picture
				}

				let loginZalo = await authService.loginWithZalo(body);
				if (loginZalo.status == HttpStatusCode.Ok) {
					toast.success(t('AuthZaloComponent.dang_nhap_thanh_cong'));
					localStorage.setItem(appConst.storageKey.token, loginZalo.body["token"]);
					// if (loginZalo.body.data.is_register) {
					// 	await insertImagesZalo(zaloInfo.picture.data.url, zaloInfo.name);
					// }
					localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(loginZalo.body.data));
					let defaultRedirectUrl = await authService.getDefaultRedirectUrl(loginZalo?.body?.data?.role_id, loginZalo?.body?.data?.shop?.length);
					await fcmService.getFcmToken();
					setTimeout(() => {
						useNuxtApp().$emit(appConst.event_key.login);
						router.replace(redirect_url.value ?? defaultRedirectUrl)
					}, 1000);

					// props.navigation.popToTop();
				}
				else if (loginZalo.status == HttpStatusCode.Forbidden && loginZalo.message_code == "C_E_008") {
					showUserDisabledModal.value = true;
				}
				else {
					toast.error(loginZalo.body?.message ?? t('AuthZaloComponent.dang_nhap_that_bai'));
					router.push(appRoute.LoginComponent);
				}
			}
			else {
				toast.error(t('AuthZaloComponent.dang_nhap_that_bai'));
				router.push(appRoute.LoginComponent);
			}
		}
		else {
			toast.error(t('AuthZaloComponent.khong_nhan_duoc_token'));
			router.push(appRoute.LoginComponent);
		}
	}
	else {
		toast.error(t('AuthZaloComponent.khong_nhan_duoc_verify_code'));
		router.push(appRoute.LoginComponent);
	}
});
async function insertImagesZalo(image: any, title = '') {
	await encodeBase64(image, title)
}
async function encodeBase64(image: any, title = '') {
	await getBase64ImageFromUrl(image, title);
}

async function getBase64ImageFromUrl(imageUrl: any, title = '') {
	return axios.get(imageUrl, {
		responseType: 'blob',
		responseEncoding: "base64",
	}).then(async (response) => {

		var reader = new FileReader();
		reader.onloadend = fileLoadedEvent => {
			const base64Image = fileLoadedEvent.target?.result?.toString().replaceAll('/jpg;', '/jpeg;');
			imageService.insertImagesZalo(base64Image, title).then(result => {
			}).catch(err => console.error(err));
		};
		reader.readAsDataURL(response.data);
	});
}
</script>

<style lang="scss" src="./AuthZaloStyles.scss"></style>