
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class PlaceService extends BaseHTTPService {
    provinces() {
        return this.https('GET', appConst.apiURL.provinces);
    }

    districts() {
        return this.https('GET', appConst.apiURL.districts);
    }

    wards() {
        return this.https('GET', appConst.apiURL.wards);
    }

    provinceDetail(province_id: number) {
        let url = appConst.apiURL.provinceDetail + "?id=" + province_id;
        return this.https('GET', url);
    }

    districtDetail(district_id: number) {
        let url = appConst.apiURL.districtDetail + "?id=" + district_id;
        return this.https('GET', url);
    }

    wardDetail(ward_id: number) {
        let url = appConst.apiURL.wardDetail + "?id=" + ward_id;
        return this.https('GET', url);
    }

    myGeocoderByLatLngToAddress(lat: number, lng: number) {
        let body = {
            latitude: lat,
            longitude: lng,
        }
        return this.https("POST", appConst.apiURL.geocodeLatLngToAddress, body);
    }
}