<template>
	<v-overlay v-model="showMonthYearPicker" location="bottom" contained :z-index="1001" key="show_month_year_select"
		v-on:click:outside="() => {
			close()
		}" class="month-year-picker-overlay-container" persistent content-class='month-year-picker-container'
		no-click-animation>
		<HeaderComponent :title="$props.title ? $props.title : $t('MonthYearPickerComponent.chon_thang_nam')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close" v-on:click="() => {
					close()
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="month-year-picker-content-container">
			<div class="picker-content year-picker" v-if="showYearPicker">
				<span>{{ $t('MonthYearPickerComponent.nam') }}</span>
				<div class="picker-container">
					<Swiper class="my-carousel picker-buttons-carousel" :modules="[Swiper<PERSON>reeMode, SwiperMousewheel]"
						mousewheel :centeredSlides="true" :freeMode="{
							enabled: true,
							sticky: true
						}" :direction="'vertical'" :centeredSlidesBounds="true" :slides-per-view="'auto'" :loop="false"
						:effect="'creative'" :autoplay="false" :initialSlide="selectYearIndex"
						key="year-picker-carousel" @slideChange="(e: any) => {
							selectYear = yearList[e.activeIndex].value;
							selectYearIndex = e.activeIndex;
							if(selectYear == null){
								monthCarouselButton?.slideTo(0);
								monthCarouselButton?.disable()
							}
							else{
								monthCarouselButton?.enable()
							}
						}" @init="(e: any) => {
							yearCarouselButton = e;
						}">
						<SwiperSlide class="picker-item" v-for="(itemYear, indexYear) of yearList" v-on:click="async () => {
							yearCarouselButton.slideTo(indexYear)
						}">
							<div class="tab-title">
								<span class='name'>
									{{ itemYear.displayName }}
								</span>
							</div>
						</SwiperSlide>
					</Swiper>
				</div>

			</div>

			<span>-</span>
			<div class="picker-content month-picker" v-if="showMonthPicker">
				<span>{{ $t('MonthYearPickerComponent.thang') }}</span>
				<div class="picker-container">
					<Swiper class="my-carousel picker-buttons-carousel" :modules="[SwiperFreeMode, SwiperMousewheel]"
						:centeredSlides="true" mousewheel :direction="'vertical'" :centeredSlidesBounds="true"
						:slides-per-view="'auto'" :loop="false" :freeMode="{
							enabled: true,
							sticky: true
						}" :effect="'creative'" :autoplay="false" key="month-picker-carousel" :initialSlide="selectMonthIndex"
						@slideChange="(e: any) => {

							selectMonth = monthList[e.activeIndex].value;
							selectMonthIndex = e.activeIndex;
						}" @init="(e: any) => {
							monthCarouselButton = e;
						}">
						<SwiperSlide class="picker-item" v-for="(itemMonth, indexMonth) of monthList" v-on:click="async () => {
							monthCarouselButton.slideTo(indexMonth)
						}">
							<div class="tab-title">
								<span class='name'>
									{{ itemMonth.displayName }}
								</span>
							</div>
						</SwiperSlide>
					</Swiper>
				</div>

			</div>
		</div>
		<div class="footer-actions">
			<button class="submit-button" v-on:click="() => {
				submit();
			}">
				{{ $t('MonthYearPickerComponent.chon') }}
			</button>
		</div>
	</v-overlay>

</template>

<script lang="ts" setup>
import moment from 'moment';
const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
	title: null,
	initialMonth: null,
	initialYear: null,
	showMonthPicker: null,
	showYearPicker: null,
	nullable: null
})

var showMonthYearPicker = ref(false);
var monthList = ref([] as any);
var yearList = ref([] as any);
var showMonthPicker = ref(props.showMonthPicker ? props.showMonthPicker : true);
var showYearPicker = ref(props.showYearPicker ? props.showYearPicker : true);

var monthCarouselButton = ref();
var yearCarouselButton = ref();

var selectMonthIndex = ref(0);
var selectYearIndex = ref(0);
var selectMonth = ref(null as any);
var selectYear = ref(null as any);
onMounted(async () => {
	if (showMonthPicker.value) {
		await monthListConstructor();
		await setFirstSelectMonth();
	}
	if (showYearPicker.value) {
		await yearListConstructor();
		await setFirstSelectYear();
	}
	showMonthYearPicker.value = true;
})

function monthListConstructor(intervalMonth = 1) {
	let startMonth = 1;
	let endMonth = 12;
	var months: any = [];
	for (let i = startMonth; i <= endMonth; i += intervalMonth) {
		months.push({
			value: i,
			displayName: t(`MonthYearPickerComponent.thang_${i}`)
		});
	}
	if (props.nullable) {
		months.splice(0, 0, {
			value: null,
			displayName: t(`MonthYearPickerComponent.tat_ca`)
		});
	}
	monthList.value = months;
	return;
}

function yearListConstructor() {

	const startYear = moment('1990-01-01');
	const endYear = moment();

	const years = [];
	while (startYear.year() <= endYear.year()) {
		years.push({
			value: startYear.year(),
			displayName: startYear.year()
		});
		startYear.add(1, 'year');
	}
	if (props.nullable) {
		years.push({
			value: null,
			displayName: t(`MonthYearPickerComponent.tat_ca`)
		});
	}
	yearList.value = years;
	return
}
function setFirstSelectMonth() {

	let initMonthIndex = monthList.value.findIndex(function (e: any) {
		return e.value == props.initialMonth;
	});
	selectMonthIndex.value = initMonthIndex != -1 ? initMonthIndex : 0;
	selectMonth.value = monthList.value[selectMonthIndex.value]?.value;
}
function setFirstSelectYear() {
	let initYearIndex = yearList.value.findIndex(function (e: any) {
		return e.value == props.initialYear
	});
	selectYearIndex.value = initYearIndex != -1 ? initYearIndex : 0;
	selectYear.value = yearList.value[selectYearIndex.value].value;
}

function close(value?: any) {
	showMonthYearPicker.value = false;
	emit('close', value);
}
function submit() {
	showMonthYearPicker.value = false;
	emit('submit', {
		year: yearList.value[selectYearIndex.value].value,
		month: monthList.value[selectMonthIndex.value].value,
	})
}
</script>

<style lang="scss" src="./MonthYearPickerStyles.scss"></style>