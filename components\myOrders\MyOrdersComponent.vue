<template>
	<div class="public-container" v-on:scroll="listOrderScroll(tabIndex != 'all' ? tabIndex : null)">

		<div class="my-orders-container" v-if="isAuth">
			<SubHeaderV2Component :title="$t('AppRouteTitle.MyOrdersComponent')">
				<template v-slot:header_right v-if="isAuth">
					<button class="filter-button" :class="{ 'active': checkFilterActive() }" v-on:click="async () => {
						showFilterMenu = !showFilterMenu
					}">
						<Icon name="ph:funnel" />
					</button>
					<button v-on:click="async () => {
						refreshListOrder()
					}">
						<Icon name="lets-icons:refresh" />
					</button>
				</template>
			</SubHeaderV2Component>
			<div class="sticky-header">
				<div class="search-bar-container">
					<div class='h-stack search-bar'>
						<Icon name="ion:search" size="20" v-show="!searchOrderLoading" />
						<Icon name="eos-icons:loading" size="20" v-show="searchOrderLoading" />

						<input type="search" :value="search_text"
							:maxlength="appConst.max_text_short"
							:placeholder="$t('AgentOrderManageComponent.tim_theo_ma_don_hang')" v-on:input="async ($event: any) => {
								search_text = $event.target.value;
								searchOrders();
							}" />
					</div>
				</div>
				<v-tabs v-model="tabIndex" hide-slider class="orders-tab">
					<v-tab class="order-tab-title" :class="tabIndex == 'all' ? 'active' : ''" value="all" key="all"
						v-on:click="async () => {
							if (tabIndex == 'all') {
								await getListOrder();
							}
							router.replace({ hash: '' });
						}">
						<div class="tab-title">
							{{ $t('MyOrdersComponent.tat_ca') }}
							<!-- <em>{{ getStatusCount() }}</em> -->
						</div>
					</v-tab>

					<v-tab v-for="(key, indexTab) in Object.keys(appConst.order_status)" class="order-tab-title"
						v-on:click="async () => {
							if (tabIndex == key) {
								await getListOrder(appConst.order_status[key].value);
							}
							router.replace({ hash: '#' + key });
						}" :class="tabIndex == key ? 'active' : ''" :value="key" :key="key" :id="'tab_' + key">
						<div class="tab-title">
							{{ $t('MyOrdersComponent.' + appConst.order_status[key].nameKey) }}
							<!-- <em>{{ getStatusCount(appConst.order_status[key].value) }}</em> -->
						</div>
					</v-tab>

				</v-tabs>
			</div>

			<div class='v-stack my-orders-content'>

				<v-window v-model="tabIndex" class="tab-content-container">
					<v-window-item value="all" id="tab_content_all">
						<div v-for="(itemOrder) in listOrder['all'] " :key="'all_' + itemOrder.id"
							class='v-stack item-order' v-if="listOrder['all'] && listOrder['all'].length > 0">
							<nuxt-link :to="appRoute.MyOrderDetailComponent.replace(':order_id', itemOrder.short_code)"
								class='order-button-container'>
								<div class='v-stack'>
									<div class='h-stack'>
										<span class='customer-name'>
											{{ itemOrder.customer_name }}
										</span>
										<span class="is-new"
											v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
											{{ $t('MyOrdersComponent.moi') }}
										</span>
										<span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
											? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
											: ' taken')">
											{{
												$t('MyOrdersComponent.' +
													appConst.order_status[
														parseInt(itemOrder.status.toString()) != 3
															? Object.keys(appConst.order_status).find(key =>
																appConst.order_status[key].value ==
																parseInt(itemOrder.status)) as string
															: "taken"
													].nameKey
												)
											}}
										</span>
									</div>
									<div class='h-stack order-detail-advanced'>
										<span class='ordered-at'>
											{{
												moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
											}}
										</span>
										<span class='short-code'>
											[{{ itemOrder.short_code }}]
										</span>
									</div>
									<div class='h-stack'>
										<span>
											{{ itemOrder?.address }}
										</span>
										<span class='customer-phone'>
											{{ itemOrder?.customer_phone }}
										</span>
									</div>
									<div class='h-stack shop-name'>
										<Icon name="material-symbols:storefront-outline"></Icon>
										<span>
											{{ itemOrder?.shops?.name }}
										</span>
										<nuxt-link :to="directionToShop(itemOrder)" v-on:click="($event: Event) => {
											$event.stopPropagation()
										}" target="_blank">
											({{ $t('ManageOrdersComponent.chi_duong') }})
										</nuxt-link>
									</div>
									<div class='h-stack'>
										<span class='title'>{{ $t('MyOrdersComponent.tong_cong') }}</span>
										<span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
											itemOrder.shops?.currency)
											}}</span>
									</div>
									<div class="h-stack">
										<span class='title' :class="{ 'delivery': itemOrder.delivery?.driver_id }">{{
											$t('ManageOrdersComponent.thong_tin_giao_hang') }}</span>
										<div class="delivery-info" v-if="itemOrder.delivery_type">
											{{ $t('ManageOrdersComponent.khach_hang_tu_toi_lay') }}
										</div>
										<div class="delivery-info" v-else-if="itemOrder.delivery">
											<div class="driver-info" v-if="itemOrder.delivery?.driver_id">
												<img :src="itemOrder?.delivery?.driver?.profile_picture
													? ((appConst.provider_img_domain.some(e => itemOrder?.delivery?.driver?.profile_picture?.includes(e))) ? itemOrder?.delivery?.driver?.profile_picture : (domainImage + itemOrder?.delivery?.driver?.profile_picture))
													: non_avatar" alt="" />
												<span v-html="$t(`ManageOrdersComponent.trang_thai_giao_hang_${itemOrder.delivery.status}`, {
													driver_name: 'driver_name'
												}).replaceAll('driver_name', `<span class='total'>${itemOrder.delivery?.driver?.name}</span>`)"></span>
											</div>
											<div v-else class="none">
												{{ $t('ManageOrdersComponent.chua_chon_shipper') }}
											</div>
										</div>
										<div class="delivery-partner-info" v-else-if="itemOrder?.delivery_partner">
											<img class="partner-logo"
												:src="itemOrder?.delivery_partner?.information?.logo"
												v-if="itemOrder?.delivery_partner?.information?.logo"
												:alt="`logo ${itemOrder?.delivery_partner?.name}`">
											<span class="partner-name" v-else>
												{{ itemOrder?.delivery_partner?.name }}
											</span>
											<div class="none"
												v-if="!itemOrder.delivery && itemOrder?.delivery_partner?.name.toLowerCase().includes('remagan')">
												- {{ $t('ManageOrdersComponent.chua_tao') }}
											</div>
										</div>
										<div v-else class="delivery-info">
											<div class="none">
												{{ $t('ManageOrdersComponent.chua_tao') }}
											</div>

										</div>
									</div>
								</div>
							</nuxt-link>

						</div>
						<div class='v-stack empty-list' v-else>
							<img loading="lazy" :src="list_empty" :placeholder="list_empty"
								:alt="$t('MyOrdersComponent.danh_sach_trong')" />
							<span>
								{{ $t('MyOrdersComponent.khong_tim_thay_don_hang_nao') }}
							</span>
							<span class='refresh' v-on:click="() => {
								getListOrder();
							}
								">
								{{ $t('MyOrdersComponent.lam_moi') }}
							</span>
						</div>
						<span class='load-more' v-if="loadMore || isRefreshing">
							{{ $t('MyOrdersComponent.dang_tai') }}
						</span>
						<div id="last_of_list_all"></div>
					</v-window-item>


					<v-window-item :id="'tab_content_' + key"
						v-for="(key, indexTab) in Object.keys(appConst.order_status)" :value="key">
						<div v-for="(itemOrder) in listOrder[key] " :key="key + '_' + itemOrder.id"
							class='v-stack item-order' v-if="listOrder[key] && listOrder[key].length > 0">
							<nuxt-link :to="appRoute.MyOrderDetailComponent.replace(':order_id', itemOrder.short_code)"
								class='order-button-container'>
								<div class='v-stack'>
									<div class='h-stack'>
										<span class='customer-name'>
											{{ itemOrder.customer_name }}
										</span>
										<span class="is-new"
											v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
											{{ $t('MyOrdersComponent.moi') }}
										</span>
										<span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
											? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
											: ' taken')">
											{{
												$t('MyOrdersComponent.' +
													appConst.order_status[
														parseInt(itemOrder.status.toString()) != 3
															? Object.keys(appConst.order_status).find(key =>
																appConst.order_status[key].value ==
																parseInt(itemOrder.status)) as string
															: "taken"
													].nameKey
												)
											}}
										</span>
									</div>
									<div class='h-stack order-detail-advanced'>
										<span class='ordered-at'>
											{{
												moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
											}}
										</span>
										<span class='short-code'>
											[{{ itemOrder.short_code }}]
										</span>
									</div>
									<div class='h-stack'>
										<span>
											{{ itemOrder.address }}
										</span>
										<span class='customer-phone'>
											{{ itemOrder.customer_phone }}
										</span>
									</div>
									<div class='h-stack shop-name'>
										<Icon name="material-symbols:storefront-outline"></Icon>
										<span>
											{{ itemOrder.shops?.name }}
										</span>
										<nuxt-link :to="directionToShop(itemOrder)" v-on:click="($event: Event) => {
											$event.stopPropagation()
										}" target="_blank">
											({{ $t('ManageOrdersComponent.chi_duong') }})
										</nuxt-link>
									</div>
									<div class='h-stack'>
										<span class='title'>{{ $t('MyOrdersComponent.tong_cong') }}</span>
										<span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
											itemOrder.shops?.currency)
											}}</span>
									</div>
									<div class="h-stack">
										<span class='title' :class="{ 'delivery': itemOrder.delivery?.driver_id }">{{
											$t('ManageOrdersComponent.thong_tin_giao_hang') }}</span>
										<div class="delivery-info" v-if="itemOrder.delivery_type">
											{{ $t('ManageOrdersComponent.khach_hang_tu_toi_lay') }}
										</div>
										<div class="delivery-info" v-else-if="itemOrder.delivery">
											<div class="driver-info" v-if="itemOrder.delivery?.driver_id">
												<img :src="itemOrder?.delivery?.driver?.profile_picture
													? ((appConst.provider_img_domain.some(e => itemOrder?.delivery?.driver?.profile_picture?.includes(e))) ? itemOrder?.delivery?.driver?.profile_picture : (domainImage + itemOrder?.delivery?.driver?.profile_picture))
													: non_avatar" alt="" />
												<span v-html="$t(`ManageOrdersComponent.trang_thai_giao_hang_${itemOrder.delivery.status}`, {
													driver_name: 'driver_name'
												}).replaceAll('driver_name', `<span class='total'>${itemOrder.delivery?.driver?.name}</span>`)"></span>
											</div>
											<div v-else class="none">
												{{ $t('ManageOrdersComponent.chua_chon_shipper') }}
											</div>
										</div>
										<div class="delivery-partner-info" v-else-if="itemOrder?.delivery_partner">
											<img class="partner-logo"
												:src="itemOrder?.delivery_partner?.information?.logo"
												v-if="itemOrder?.delivery_partner?.information?.logo"
												:alt="`logo ${itemOrder?.delivery_partner?.name}`">
											<span class="partner-name" v-else>
												{{ itemOrder?.delivery_partner?.name }}
											</span>
											<div class="none"
												v-if="!itemOrder.delivery && itemOrder?.delivery_partner?.name.toLowerCase().includes('remagan')">
												- {{ $t('ManageOrdersComponent.chua_tao') }}
											</div>
										</div>
										<div v-else class="delivery-info">
											<div class="none">
												{{ $t('ManageOrdersComponent.chua_tao') }}
											</div>

										</div>
									</div>
								</div>
							</nuxt-link>
						</div>
						<div class='v-stack empty-list' v-else>
							<img loading="lazy" :src="list_empty" :placeholder="list_empty"
								:alt="$t('MyOrdersComponent.danh_sach_trong')" />
							<span>
								{{ $t('MyOrdersComponent.khong_tim_thay_don_hang_nao') }}
							</span>
							<span class='refresh' v-on:click="() => {
								getListOrder(appConst.order_status[key].value);
							}
								">
								{{ $t('MyOrdersComponent.lam_moi') }}
							</span>
						</div>
						<span class='load-more' v-if="loadMore || isRefreshing">
							{{ $t('MyOrdersComponent.dang_tai') }}
						</span>
						<div :id="'last_of_list_' + key"></div>
					</v-window-item>

				</v-window>
			</div>


		</div>
		<div class="my-orders-container" v-else-if="!isRefreshing">
			<SubHeaderV2Component :title="$t('AppRouteTitle.MyOrdersComponent')">
			</SubHeaderV2Component>
			<div class="my-orders-content">
				<div class='v-stack empty-list'>
					<img loading="lazy" :src="list_empty" :placeholder="list_empty"
						:alt="$t('MyOrdersComponent.danh_sach_trong')" />
					<span>
						{{ $t('MyOrdersComponent.vui_long_dang_nhap_truoc_khi_xem_cac_don_hang_da_dat') }}
					</span>
					<span class='refresh' v-on:click="() => {
						router.push({
							path: appRoute.LoginComponent,
							query: {
								redirect: JSON.stringify(route.fullPath)
							}
						})
					}
						">
						{{ $t('MyOrdersComponent.dang_nhap') }}
					</span>
				</div>

			</div>

		</div>
		<v-overlay v-model="showFilterMenu" :z-index="100" :absolute="false" contained key="show_filter_menu"
			class="filter-menu-overlay-container" content-class='filter-menu-container' no-click-animation
			v-on:click:outside="() => {
				showFilterMenu = false;
				filterTimeTemp = filterTime;
			}">
			<div class="filter-menu-content">
				<div class="filter-title">
					{{ $t('MyOrdersComponent.loc_don_hang') }}
				</div>
				<div class="time-filter">
					<div class="item-time-filter" :class="{ 'active': filterTimeTemp == item.value }"
						v-for="item of dataFilterTime" v-on:click="() => { filterTimeTemp = item.value }">
						<Icon name="ic:sharp-radio-button-checked" v-if="filterTimeTemp == item.value"></Icon>
						<Icon name="ic:sharp-radio-button-unchecked" v-else></Icon>
						<span>{{ $t('MyOrdersComponent.' + item.nameKey) }}</span>
					</div>
					<div class="h-stack select-date" v-if="filterTimeTemp == 5">
						<input name="start-day" class="input-custom" id="start-day" type="date" :value="start_date"
							placeholder="{{ $t('MyOrdersComponent.chua_cung_cap') }}"
							:max="moment(end_date ? end_date : new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
								start_date = event.target.value;
							}" />
						<Icon name="material-symbols:arrow-forward-rounded"></Icon>
						<input name="end-day" class="input-custom" id="end-day" type="date" :value="end_date"
							placeholder="{{ $t('MyOrdersComponent.chua_cung_cap') }}"
							:min="moment(start_date ? start_date : new Date()).format('YYYY-MM-DD')"
							:max="moment(new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
								end_date = event.target.value;
							}" />
					</div>
					<div class="h-stack footer">
						<button class="close" v-on:click="() => {
							showFilterMenu = false;
							filterTimeTemp = filterTime;
						}">
							{{ $t('MyOrdersComponent.dong') }}
						</button>
						<button class="apply" v-on:click="() => {
							showFilterMenu = false;
							if (filterTimeTemp != filterTime || filterTimeTemp == 5) {
								filterTime = filterTimeTemp;
								switch (filterTime as any) {
									case 0: case null: {
										start_date = null;
										end_date = null;
										break;
									}
									case 1: {
										start_date = moment(new Date()).format(appConst.formatDate.toSave);
										end_date = moment(new Date()).format(appConst.formatDate.toSave);
										break;
									}
									case 2: {
										start_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
										end_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
										break;
									}
									case 3: {
										start_date = moment().startOf('month').format(appConst.formatDate.toSave);
										end_date = moment().endOf('month').format(appConst.formatDate.toSave);
										break;
									}
									case 4: {
										start_date = moment().add(-1, 'months').startOf('month').format(appConst.formatDate.toSave);
										end_date = moment().add(-1, 'months').endOf('month').format(appConst.formatDate.toSave);
										break;
									}
									case 5: {
										start_date = start_date ? moment(start_date).format(appConst.formatDate.toSave) : null;
										end_date = end_date ? moment(end_date).format(appConst.formatDate.toSave) : null;
										break;
									}
									default: break;
								}
							}
							getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
						}">
							{{ $t('MyOrdersComponent.ap_dung') }}
						</button>
					</div>
				</div>
			</div>
		</v-overlay>
	</div>

</template>

<script lang="ts" setup>

import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg";
import non_avatar from '~/assets/image/non-avatar.jpg';
import moment from 'moment';
import { appConst, domainImage, formatCurrency, formatNumber } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { HttpStatusCode } from 'axios';

const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
	shopData: {},
	tabIndex: null
})
var nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.MyOrdersComponent')
});

var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var shopService = new ShopService();
var searchOrderTimeout: any;

var tabIndex = ref();
var profileData = ref(null as any);
var isAuth = ref(false);
var listOrder = ref({} as any);
var countOrder = ref(0);
var loadMoreTimeOut: any;
var loadMore = ref(false);

var countOrderByStatus = ref([] as any[]);
var searchOrderLoading = ref(false);
var showFilterMenu = ref(false);
var filterTime = ref(null);
var filterTimeTemp = ref(null as any);
var dataFilterTime = [
	{
		value: 0 || null,
		name: "Toàn thời gian",
		nameKey: "toan_thoi_gian",
	},
	{
		value: 1,
		name: "Hôm nay",
		nameKey: "hom_nay",
	},
	{
		value: 2,
		name: "Hôm qua",
		nameKey: "hom_qua",
	},
	{
		value: 3,
		name: "Tháng này",
		nameKey: "thang_nay",
	},
	{
		value: 4,
		name: "Tháng trước",
		nameKey: "thang_truoc",
	},
	{
		value: 5,
		name: "Thời gian khác",
		nameKey: "thoi_gian_khac",
	},
];
var start_date = ref(null as any);
var end_date = ref(null as any);
var search_text = ref("")

var refreshTimeout: any;
var isRefreshing = ref(true);
var searchTimeout: any;
onMounted(() => {

	nuxtApp.$listen('refresh_order_manage', () => {
		getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
		tabIndex.value = route.hash ? route.hash.replaceAll('#', '') : 'all'
	})
	setTimeout(async () => {
		await init();

		tabIndex.value = route.hash ? route.hash.replaceAll('#', '') : 'all'
	}, 0);
})

async function init() {
	isRefreshing.value = true;
	getProfileInfo().then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			isAuth.value = true;
			profileData.value = res.data;
			await getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
			// isRefreshing.value = false;
		}
		else {
			isAuth.value = false;
			profileData.value = null;
			localStorage.removeItem(appConst.storageKey.token);
			localStorage.removeItem(appConst.storageKey.userInfo);
			sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
			isRefreshing.value = false;
		}

	}).catch(err => {
		isAuth.value = false;
		profileData.value = null;
		localStorage.removeItem(appConst.storageKey.token);
		localStorage.removeItem(appConst.storageKey.userInfo);
		sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
	})

}
async function getProfileInfo() {
	return await userService.profileInfo();
}
function getListOrder(status?: number | null) {
	isRefreshing.value = true;

	orderService.orderByCustomerId(profileData.value.id, 0, 20, status || null, search_text.value, start_date.value, end_date.value).then(async res => {
		if (res.status && res.status == HttpStatusCode.Ok) {
			// listOrder.value = JSON.parse(JSON.stringify(res.body.data.result));
			// countOrder.value = res.body.data.count;
			// isRefreshing.value = false;
			let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
			statusName = statusName ? statusName : "all";
			document.getElementById('tab_content_' + statusName)?.scrollTo({
				behavior: 'smooth',
				top: 0
			});
			let list = JSON.parse(JSON.stringify(listOrder.value));

			list[statusName] = JSON.parse(JSON.stringify(res.body.data.result));
			let index = countOrderByStatus.value.findIndex(function (e: any) {
				return e.status == status;
			})
			if (index != -1) {
				countOrderByStatus.value[index].count = res.body.data.count
			}
			else {
				countOrderByStatus.value.push({
					status: status,
					count: res.body.data.count
				})
			}
			isRefreshing.value = false;
			listOrder.value = JSON.parse(JSON.stringify(list));
		}
		else {
			listOrder.value = [];
			isRefreshing.value = false;
		}
	}).catch(err => {
		listOrder.value = [];
		isRefreshing.value = false;
		console.error(err)
	})
}

function refreshListOrder() {
	clearTimeout(refreshTimeout);
	refreshTimeout = setTimeout(() => {
		getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
	}, 1000);
}
function checkFilterActive() {
	if (filterTime.value || (start_date.value && end_date.value)) return true;
	return false;
}
function checkNewOrder(order: any) {
	var orderCreatedAt = moment(order.created_at).valueOf();
	var currentTime = moment().valueOf();
	let count = currentTime - orderCreatedAt;
	if (Math.round(count / (60 * 60 * 24)) < 60)
		return true;
	return false;
}
function searchOrders() {
	clearTimeout(searchTimeout);
	searchTimeout = setTimeout(() => {
		getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
	}, 100)
}
function getMoreOrderInAList(offset = 0, limit = 20, status?: number | null) {
	let index = countOrderByStatus.value.findIndex(function (e: any) {
		return e.status == (status ? status : undefined);
	});
	if (offset < countOrderByStatus.value[index].count) {
		clearTimeout(loadMoreTimeOut);

		loadMoreTimeOut = setTimeout(() => {
			loadMore.value = true;
			let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
			orderService.orderByCustomerId(profileData.value.id, offset, limit, status || null).then(async res => {

				if (res.status && res.status == HttpStatusCode.Ok) {
					// let list = JSON.parse(JSON.stringify(listOrder.value));
					// list = [...list, ...res.body.data.result];

					// loadMore.value = false;
					// listOrder.value = JSON.parse(JSON.stringify(list));
					statusName = statusName ? statusName : "all";
					let list = JSON.parse(JSON.stringify(listOrder.value));
					list[statusName] = [...list[statusName], ...res.body.data.result];

					loadMore.value = false;
					listOrder.value = JSON.parse(JSON.stringify(list));
				}
				else {
					loadMore.value = false;
				}

			})

		}, 500)
	}
}
function listOrderScroll(key: string | null = null) {

	let el = document.getElementById('last_of_list_' + (key ? key : 'all'))?.getBoundingClientRect().bottom;
	if (el && (el <= window.innerHeight + 300)) {
		getMoreOrderInAList(listOrder.value[key ? key : "all"].length, 20, key ? appConst.order_status[key].value : null)
	}
}

function directionToShop(order: any) {
	// await getUserLocation();
	let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${order?.customer_latitude},${order?.customer_longitude}&destination=${order?.shops?.latitude},${order?.shops?.longitude}&trabelmode=bicycling`;
	return textToDirections ?? "";
}
</script>

<style lang="scss" src="./MyOrdersStyles.scss"></style>
