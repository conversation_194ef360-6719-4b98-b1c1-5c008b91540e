.new-delivery-noti-container {
  max-height: 95dvh;
  height: fit-content;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  background-color: #3b3b3b;

  & > .new-delivery-detail-container {
    display: flex;
    width: 100%;
    flex-direction: column;
    border-radius: 10px;
    overflow: auto;
    & > .header {
      padding: 10px;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      & > button {
        color: white;
        border-radius: 2em;
        border: 2px solid;
        font-weight: bold;
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
      }
    }

    & > .new-delivery-detail-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      & > .primary-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        width: 100%;
        padding: 0 10px;
        margin: 10px 0;

        & > .short-code {
          color: yellow;
        }

        & > .price {
          font-size: 40px;
          font-weight: bold;
          color: #64ff64;
        }

        & > .pickup-time-distance {
          display: flex;
          justify-content: space-between;
          width: 100%;

          & > .pickup-time,
          > .distance {
            font-size: 15px;
            display: flex;
            flex-direction: column;

            & > em {
              font-size: 20px;
              font-weight: 600;
            }
          }
        }
      }

      & > .start-place,
      > .destination-place {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: #000131;
        color: white;
        padding: 20px 10px;
        width: 100%;
        position: relative;

        & > svg {
          font-size: 30px;
          position: absolute;
          top: 100%;
          transform: translateY(-50%);
          z-index: 1;
          background: #000131;
        }

        & > .name {
          font-size: 18px;
          font-style: italic;
          font-weight: 400;
        }

        & > .address {
          font-size: 25px;
          font-weight: bold;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      & > .destination-place {
        background: #161616;
        border-top: 2px solid white;
      }
    }

    & > .footer {
      padding: 5px;
      display: flex;
      justify-content: stretch;
      align-items: center;
      background: #f5f6fa;
      gap: 5px;

      & > button.decline-button{
        width: auto;
        background: var(--primary-color-2);
        font-size: 15px;
        font-weight: bold;
        padding: 10px;
        color: white;
        white-space: nowrap;
        position: relative;
        height: 50px;
        border-radius: 0 0 0 10px;
      }

      & > button.accept-button {
        font-size: 20px;
        font-weight: bold;
        padding: 10px;
        color: white;
        width: 100%;
        position: relative;
        background: var(--primary-color-1);
        height: 50px;
        flex: 1;
        border-radius: 0 0 10px 0;
        max-width: unset;
        display: flex;
        justify-content: center;

        & > .time-remaining {
          display: flex;
          align-items: center;
          padding: 5px 15px;
          background: yellow;
          border-radius: 2em;
          margin-left: 10px;
          // position: absolute;
          // right: 0;
          // top: 50%;
          // transform: translate(-10px, -50%);
          color: black;
        }
      }
    }
  }

  & > .new-delivery-detail-container.not-found{
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    justify-content: center;
    position: relative;

    & > .close-button{
      position: absolute;
      top: 10px;
      right: 10px;

    }
    & > img{
      width: 200px;
      height: auto;
    }

    & > span{
      font-size: 25px;
      font-weight: bold;
      color: #3b3b3b;
    }
  }
}
