.signup-container, .skeleton-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  padding: 10px;
  font-size: calc(var(--font-size) * 1.3);
  background-color: var(--color-background-2);
  overflow: auto;

  &>.content-container{
      gap: 10px;
  }

  .signup-icon {
      margin: 10px 0;
      justify-content: center;
      border-radius: 50%;
      width: 75px;
      height: 75px;
      object-fit: contain;
  }

  .signup-slogan {
      margin-left: 10px;
      vertical-align: middle;
      font-weight: 500;
      font-size: 1.5em;
  }

  .login-button-container {
      margin-top: 10px;
      justify-content: center;
  }

  .login-button-container .login-button {
      margin-left: 10px;
      font-weight: 500;
      color: var(--primary-color-1);
  }

  .back-home {
      text-align: center;
      cursor: pointer;
      color: var(--primary-color-1);
  }

  .signup-button{
      color: white;
      border: none;
  }

  .password-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
  }

  .password-input-group>input {
      flex: 1;
  }

  .password-input-group>button {
      background: transparent;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      color: var(--primary-color-1);
  }

  .password-input-group>button:hover {
      background-color: #ddd;
  }

  .password-input-group>button.checked{
    color: rgb(56, 136, 14)
  }
  .password-input-group>button.checked:hover{
    background-color: transparent;
  }
}