@media screen and (min-width: 721px) {
  .add-category-button {
    right: calc((100vw - var(--max-width-view)) / 2 + 15px) !important;
  }
}
.manage-categories-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--color-background-2);
  position: relative;
  align-items: center;
  justify-content: center;
  overflow: auto;

  & > .list-category-container {
    display: flex;
    flex: 1;
    overflow: auto;
    padding: 0 10px;
    z-index: 2;
    width: 100%;

    & .v-expansion-panels {
      padding: 10px 0;
      padding-bottom: 70px;
    }

    & .drag-drop-container {
      width: 100%;
    }
    
    // & .item-category-accordion:active {
    //   transform: scale(0.95);
    //   transition: transform 0.2s;
    //   transition-delay: 0.5s;
    // }
    & .item-category-container {
      padding: 10px;
      border: none;
      border-bottom: none;
      border-radius: 10px;
      margin: 10px 0;
      background: white !important;
      display: flex;
      gap: 5px;
      align-items: center;
      cursor: pointer;

      & > .drag-handle {
        cursor: move;
        width: 40px !important;
        height: 40px;
        color: #737373;
        font-size: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 40px;
    
      }

      & > .v-stack {
        font-weight: bold;
        width: 100%;

        & > span {
          color: var(--primary-color-1);
          font-style: italic;
          font-weight: 500;
        }
      }

      // & .v-expansion-panel-title--active > .v-expansion-panel-title__overlay,
      // .v-expansion-panel-title[aria-haspopup="menu"][aria-expanded="true"]
      //   > .v-expansion-panel-title__overlay {
          
      //   opacity: 0;
      // }

      // & .v-expansion-panel-title:hover > .v-expansion-panel-title__overlay {
      //   opacity: 0;
      // }

      

      & .accordion-header {
        font-weight: 500;
        font-size: 1em;
        padding: 5px 10px;
        gap: 10px;
        outline: none !important;
        box-shadow: none;
        background: white;
        animation: none !important;

        & > .v-stack {
          font-weight: 600;
          width: 100%;

          & > span {
            color: var(--primary-color-1);
            font-style: italic;
            font-weight: 500;
          }
        }

        & .stt {
          width: fit-content;
          background: var(--color-button-special);
          color: white !important;
          padding: 1px 10px;
          border-radius: 2em;
          font-style: normal !important;
          line-height: normal;
        }
      }
      & [class*="v-expansion-panel-text"] {
        gap: 5px;
        flex: 1;
        padding: 0;
        background: white;
        border-radius: inherit;
        & > .h-stack {
          justify-content: space-between;
          padding: 5px;
        }
        & button {
          border: none;
          border-radius: 2em;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          width: 31%;
          font-size: 13px;
          white-space: nowrap;
          font-weight: 500;
          padding: 5px;

          & > span {
            font-size: 1.2em;
            display: flex;
          }
        }
        & button.view-product {
          background: var(--primary-color-1);
        }
        & button.edit {
          background: var(--color-text-note);
        }
        & button.delete {
          background: var(--color-button-special);
        }
      }
    }
    & .item-category-accordion::after {
      border: none !important;
    }
  }

  & .loading {
    flex: 1;
    text-align: center;
  }

  & > .add-category-button {
    color: white;
    background-color: var(--primary-color-1);
    gap: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid white;
    box-shadow: 0 0 20px rgb(0, 0, 0, 10%);
    font-size: 1.2em;
    font-weight: 600;
    padding: 10px 20px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    border-radius: 2em;
    cursor: pointer;
    z-index: 2;
  }

  & .categories-list-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 10px;

    & > img {
      width: 200px;
      height: 200px;
      align-items: center;
      object-fit: contain;
      position: relative;
      border-radius: 50%;
    }

    & span {
      font-size: 1.5em;
      font-weight: 500;
    }
  }
}
.delete-category-content {
  font-size: 1.3em;
  text-align: center;
}

.delete-category-title {
  font-size: 1em;
  font-weight: bold;
}

.delete-category-message {
  font-size: 1em;
  text-align: center;
}

.delete-category-message > .category-name {
  color: var(--primary-color-2);
  font-weight: 600;
}
.category-content-container {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.ghost {
  opacity: 0 !important;
}