.notifications-overlay-container {
    overflow: hidden;
    z-index: 1001 !important;
    max-width: 100% !important;

    @keyframes slide-up {
        0% {
            bottom: -50%;
        }

        100% {
            bottom: 0;
        }
    }

    & .notifications-content-container {
        background: #f5f6fa;
        position: absolute;
        bottom: 0;
        // left: 0;
        left: 50%;
        width: 100%;
        height: fit-content;
        border-radius: 20px 20px 0 0;
        animation: slide-up 0.5s ease;
        max-width: var(--max-width-content-view-1024);
        transform: translateX(-50%);
        overflow: auto;
        max-height: 95dvh;
        display: flex;
        flex-direction: column;

        & .noti-label {
            font-size: 20px;
            font-weight: 700;
            color: #545454;
            top: 0;
            position: sticky;
            background: inherit;
            padding: 5px;
            z-index: 10;
            text-align: center;
            height: 50px;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: thin solid #f5f5f5;

            &>.close-noti {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                justify-content: center;
                animation: none;
                width: 40px;
                height: 40px;
            }

            &>.refresh-noti {
                position: absolute;
                right: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 50%;
                transform: translateY(-50%);
                color: var(--primary-color-1);
                animation: none;
                width: 40px;
                height: 40px;
            }
        }
    }
}

.notifications-container {
    padding: 0 10px 10px;
    // border-radius: 10px;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: auto;
    position: relative;

    &>.noti-item-content-container {
        display: flex;
        flex-direction: column;
    }

    & .scroll-top-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 100;

        &>button.go-up {
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            color: white;
            font-size: 30px;
            background: var(--primary-color-1);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }
    }

    & .loadmore-button {
        color: var(--primary-color-1);
        font-weight: 700;
        font-size: 15px;
        text-align: center;
    }

    & .view-all {
        color: #868686;
        font-size: 13px;
        font-style: italic;
        font-weight: 400;
        text-align: center;
    }

    & .loading-more {
        background: white;
        text-align: center;
        font-size: 15px;
    }
}

.mark-read-all {
    color: var(--primary-color-1);
    font-size: 15px;
    font-weight: 500;
    font-style: italic;
    position: sticky;
    top: 0;
    width: auto;
    background-color: white;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.none-noti {
    width: 100%;
    height: 100px;
    background: color-mix(in srgb, var(--primary-color-1) 5%, transparent);
    color: #868686;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;

}