<template>
  <div class='edit-order-container'>
    <!-- <div class='title-header'>
      <h3>
        {{ appRouteTitle.AgentEditOrderComponent }}
      </h3>
    </div> -->
    <HeaderComponent :title="$t('AppRouteTitle.AgentEditOrderComponent')">
      <template v-slot:header_left></template>
      <template v-slot:header_right></template>
    </HeaderComponent>

    <div class='edit-order-content-container' v-if="order && order.id">
      <div class='main-stack'>
        <div class='h-stack'>
          <span class='label'>{{ $t('AgentEditOrderComponent.ma_don_hang') }}</span>
          <span class='short-code'>{{ order.short_code }}</span>
        </div>
      </div>
      <div class='main-stack add-product-to-order-stack'>
        <button class='add-product-to-order-button' v-on:click="() => {
          showModalAddProduct = true
        }">
          <Icon name="ic:outline-plus" />
          <span>{{ $t('AgentEditOrderComponent.them_san_pham') }}</span>
        </button>
      </div>

      <div class='main-stack order-items-container'>
        <div v-for="(itemOrder, index) in order.items" :key="'edit_order_' + itemOrder.id"
          class='h-stack order-item-container'>
          <img loading="lazy"
            :src="itemOrder?.profile_picture ? (domainImage + itemOrder.profile_picture) : icon_for_product"
            :placeholder="icon_for_product" :alt="$t('AgentEditOrderComponent.anh_san_pham')" />
          <div class='v-stack item-order-detail'>
            <span class='name'>
              {{ itemOrder.parent_id ? (showTranslateProductName(itemOrder.parent_product) + " - ") : "" }}{{
                showTranslateProductName(itemOrder) }}
            </span>
            <span class='price'>
              {{
                (itemOrder.price_off != null && itemOrder.price_off < itemOrder.price) ?
                  formatCurrency(parseFloat(itemOrder.price_off), itemOrder.shop?.currency) :
                  (parseFloat(itemOrder.price) == 0 || itemOrder.price == null) ? $t('AgentEditOrderComponent.gia_lien_he') :
                    formatCurrency(parseFloat(itemOrder.price), itemOrder.shop?.currency) }} <em class="off"
                v-if="(itemOrder.price_off != null && parseFloat(itemOrder.price_off) < parseFloat(itemOrder.price))">
                {{
                  itemOrder.price != null ? formatCurrency(itemOrder.price != null ?
                    parseFloat(itemOrder.price) : 0, itemOrder.shop?.currency) : $t('AgentEditOrderComponent.gia_lien_he')
                }}
                </em>
            </span>
            <span class='note'>
              {{ itemOrder.pivot.notes }}
            </span>
            <div class='h-stack price-quantity'>
              <div class='h-stack quantity'>
                <button class='quantity-button minus' :disabled="isUpdating || itemOrder.pivot.quantity <= 0"
                  v-on:click="() => {
                    itemOrder.pivot.quantity = itemOrder.pivot.quantity - 1 > 0 ? (parseFloat(itemOrder.pivot.quantity) - 1) : 0;
                    itemOrder.pivot.price_total = itemOrder.pivot.price_temp * parseFloat(itemOrder.pivot.quantity);
                    changeOrderItem(itemOrder.pivot, index);
                  }">
                  <Icon name="ic:outline-minus" />
                </button>
                <input type="number" v-model="itemOrder.pivot.quantity" v-currency="appConst.v_currency_option"
                  class="quantity-input input-custom" max=1000000000000 v-on:input="($event: any) => {
                    itemOrder.pivot.quantity = parseFloat(itemOrder.pivot.quantity);
                    itemOrder.pivot.price_total = itemOrder.pivot.price_temp * itemOrder.pivot.quantity;
                    changeOrderItem(itemOrder.pivot, index, true);
                  }">

                <button class='quantity-button plus' :disabled="isUpdating" v-on:click="() => {
                  itemOrder.pivot.quantity = itemOrder.pivot.quantity > 0 ? (parseFloat(itemOrder.pivot.quantity) + 1) : 1;
                  itemOrder.pivot.price_total = itemOrder.pivot.price_temp * parseFloat(itemOrder.pivot.quantity);
                  changeOrderItem(itemOrder.pivot, index);
                }">
                  <Icon name="ic:outline-plus" />
                </button>
              </div>
              <div class="price-input-container">
                <input type="number" v-model="itemOrder.pivot.price_temp" v-currency="appConst.v_currency_option"
                  class="input-custom price-input" max=1000000000000 v-on:input="($event: any) => {
                    itemOrder.pivot.price_total = itemOrder.pivot.price_temp * itemOrder.pivot.quantity;
                    changeOrderItem(itemOrder.pivot, index, true);
                  }">
                <span class='price'>
                  {{ formatCurrency(parseFloat(itemOrder.pivot.price_temp) || 0, order.shops.currency) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class='main-stack v-stack'>
        <div class='h-stack customer-info'>
          <img loading="lazy" :src="order?.customer?.profile_picture
            ? ((appConst.provider_img_domain.some(e => order?.customer?.profile_picture?.includes(e))) ? order?.customer?.profile_picture : (domainImage + order?.customer?.profile_picture))
            : non_avatar" :alt="$t('AgentEditOrderComponent.anh_dai_dien')"
            :placeholder="non_avatar" />

          <div class='v-stack'>
            <span class='name'>{{ order.customer_name }}</span>
            <span class='phone'>
              {{ order.customer_phone }}
            </span>
          </div>
        </div>
        <div class='v-stack address-info'>
          <span class='label'>{{ $t('AgentEditOrderComponent.dia_chi') }}</span>
          <input type="text" class='input-custom address-input' :value="order.address" v-on:input="async ($event: any) => {
            order = {
              ...order,
              address: $event.target.value
            }
            if ($event.target.value.length) {
              addressErr = '';
            }
            else {
              addressErr = $t('AgentEditOrderComponent.vui_long_nhap_dia_chi')
            }
          }" :maxlength="appConst.max_text_short" :placeholder="$t(' AgentEditOrderComponent.dia_chi')" />
          <span class='error-message'>{{ addressErr }}</span>
        </div>
      </div>

      <div class='main-stack v-stack'>
        <div class='h-stack order-payment-info'>
          <span class='label'>{{ $t('AgentEditOrderComponent.tong') }} {{ order.items.length }} {{
            $t('AgentEditOrderComponent.san_pham') }}</span>
          <span class='data'>{{ formatNumber(order.total_amount || 0) }}</span>
        </div>
        <div class='h-stack order-payment-info' v-if="false">
          <span class='label'>{{ $t('AgentEditOrderComponent.phi_van_chuyen') }}</span>
          <input type="number" v-model="order.delivery_price" v-currency="appConst.v_currency_option"
            class="input-custom price-input" max=1000000000000>
        </div>
        <div class='h-stack  order-payment-info'>
          <span class='label'>
            {{ $t('AgentEditOrderComponent.giam_gia') }}
          </span>
          <span class='data'>
            {{ formatNumber(order.discount_amount || 0) }}
          </span>
        </div>
        <div class='h-stack order-payment-info total'>
          <span class='label'>{{ $t('AgentEditOrderComponent.tong_cong') }}</span>
          <span class='data'>
            {{
              formatNumber(
                parseFloat(order.grand_total) || 0)
            }}
          </span>
        </div>
      </div>

      <div class='main-stack v-stack'>
        <span class='label'>{{ $t('AgentEditOrderComponent.ghi_chu') }} <em class="text-length">({{ order.notes?.length ?? 0 }}/{{ appConst.max_text_short }})</em></span>
        <textarea :placeholder="$t('AgentEditOrderComponent.ghi_chu_don_hang')" name="note-order" id="note-order"
        :maxlength="appConst.max_text_long"
          :rows="5" class='note-order-input' v-model="order.notes"></textarea>
      </div>
    </div>


    <div class='h-stack edit-actions' v-if="order && order.id">
      <button class='reject-button' v-on:click="() => {
        close()
      }">
        {{ $t('AgentEditOrderComponent.huy_thay_doi') }}
      </button>
      <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
        showModalConfirmResetChange = false; isUpdating = true;
        if (parseFloat(order.status.toString()) <= appConst.order_status.taken.value) { updateOrder(); }
      }">
        <span v-if="!isUpdating">{{ $t('AgentEditOrderComponent.cap_nhat') }}</span>
        <Icon name="eos-icons:loading" size="20" v-else />
      </button>
    </div>

    <VueFinalModal class="my-modal-container" content-class="my-modal-content-container add-product-to-order-modal" :overlay-behavior="'persist'"
      v-model="showModalAddProduct" v-on:closed="() => {
        showModalAddProduct = false
      }" contentTransition="vfm-slide-up">
      <div class='add-product-container'>
        <h3 class='title'>{{ $t('AgentEditOrderComponent.them_san_pham') }}</h3>

        <div class='h-stack search-bar'>
          <Icon name="ion:search" size="20" v-show="!searchProductLoading" />
          <Icon name="eos-icons:loading" size="20" v-show="searchProductLoading" />

          <input type="search" :value="searchProduct" class='search-input-container'
            :maxlength="appConst.max_text_short"
            :placeholder="$t('AgentEditOrderComponent.tim_san_pham')" v-on:input="async ($event: any) => {
              searchProductLoading = true;
              searchProduct = $event.target.value;
              searchingProduct($event.target.value);
            }" />
        </div>
        <div class='products v-stack' v-on:scroll="() => {
          listFilterScroll()
        }">

          <img loading="lazy" :src="none_result" :placeholder="none_result" class='none-search-result'
            v-if="!shopProductsFilterResult || !shopProductsFilterResult.length" />
          <label v-if="shopProductsFilterResult && shopProductsFilterResult.length"
            v-for="(itemProductFilter, index) in shopProductsFilterResult " class="product-result-item"
            :class="(itemProductFilter.existing ? 'disabled' : '')" v-on:click="() => {
              if (!itemProductFilter.existing) {
                itemProductFilter.selected = !itemProductFilter.selected;
                let arrFilter = JSON.parse(JSON.stringify(shopProductsFilterResult));
                let arrSelect = JSON.parse(JSON.stringify(listProductSelect));

                arrFilter[index].selected = itemProductFilter.selected;
                let indexSelected = arrSelect.findIndex(function (e: any) {
                  return e.id == itemProductFilter.id
                });
                if (indexSelected != -1) {
                  arrSelect.splice(indexSelected, 1)
                }
                else if (indexSelected == -1) {
                  arrSelect.push(itemProductFilter)
                }
                shopProductsFilterResult = JSON.parse(JSON.stringify(arrFilter));
                listProductSelect = JSON.parse(JSON.stringify(arrSelect))
              }
            }" :key="itemProductFilter.id">
            <Icon name="mdi:checkbox-marked-outline" class=' my-checkbox' v-if="itemProductFilter.selected"></Icon>
            <Icon name="mdi:checkbox-blank-outline" class='my-checkbox' v-else></Icon>
            {{
              showTranslateProductName(itemProductFilter)
            }}
          </label>
          <div id="last_of_list_filter"></div>
          <div class='h-stack actions'>
            <span>
              {{ $t('AgentEditOrderComponent.da_chon') }}: {{ listProductSelect.length }}
            </span>

            <button v-if="shopProductsFilterResult.filter((e: any) => { return e.selected && !e.existing }).length > 0"
              v-on:click="() => {
                let arrFilter = JSON.parse(JSON.stringify(shopProductsFilterResult));
                arrFilter.forEach((item: any) => {
                  if (!item.existing && item.selected) {
                    item.selected = false;
                  }
                });
                shopProductsFilterResult = arrFilter;
                listProductSelect = [];
              }" class='clear-selected-button'>
              {{ $t('AgentEditOrderComponent.bo_chon') }}
            </button>

            <button class='accept-button' v-on:click="async () => {
              let arrFilter = JSON.parse(JSON.stringify(shopProductsFilterResult));
              let listOrderProducts = JSON.parse(JSON.stringify(order.items));
              listProductSelect.forEach((e: any) => {
                let indexFilter = arrFilter.findIndex(function (itemFilter: any) {
                  return e.id == itemFilter.id
                });
                if (indexFilter != -1) {
                  arrFilter[indexFilter].existing = true
                }

                let newItem = {
                  ...e,
                  pivot: {
                    order_id: order.id,
                    product_id: e.id,
                    quantity: 1,
                    notes: '',
                    price: e.price,
                    price_off: e.price_off,
                    price_temp: e.price_off ? e.price_off : e.price
                  }
                }
                listOrderProducts.push(newItem);
              })
              showModalAddProduct = false;
              order = {
                ...order,
                items: listOrderProducts,
              }
              shopProductsFilterResult = JSON.parse(JSON.stringify(arrFilter));
              listProductSelect = [];

              let total = getOrderTotalPrice();
              let total_price_off = getOrderPriceOffTotalPrice();
              order = {
                ...order,
                total_amount: total,
                grand_total: total_price_off,
                discount_amount: total_price_off - total
              }
            }">
              {{ $t('AgentEditOrderComponent.xong') }}
            </button>
          </div>
        </div>

      </div>
    </VueFinalModal>


    <div class='none-order-result' v-if="!(order && order.id) && !isRefreshing">
      {{ $t('AgentEditOrderComponent.khong_tim_thay_don_hang') }}
    </div>


  </div>
</template>

<script lang="ts" setup>
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_product from '../../assets/image/icon-for-product.png';
import none_result from '~/assets/image/none-result-2.webp';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, nonAccentVietnamese, showTranslateProductName } from '~/assets/AppConst';
import type { CartDto } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';


const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
  orderId: null
})
var nuxtApp = useNuxtApp();

const { t } = useI18n();

useSeoMeta({
  title: t('AppRouteTitle.AgentEditOrderComponent')
})

var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var shopService = new ShopService();
var agentService = new AgentService();
var searchOrderTimeout: any;
var searchProductTimeout: any;

var loadMoreTimeOut: any;
var backHandlerListener: any;

var searchProduct = ref("");
var showModalConfirmResetChange = ref(false);
var showModalAddProduct = ref(false);
var order = ref(null as any);
var orderId = ref(props.orderId || null);
var isRefreshing = ref(true);
var addressErr = ref("");
var shopProductsFilterResult = ref([] as any[]);
var shopProductsFilterResultTemp = ref([] as any[]);
var shopProductsFilterResultCount = ref(0);
var listProductSelect = ref([]);
var updated = ref(false);
var isUpdating = ref(false);
var searchProductLoading = ref(false);
var webInApp = ref(null as any);
onMounted(async () => {
  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
  await getDetailOrder(orderId.value);

})

function close(updated: any = null) {
  emit('close', updated);
  // backHandler();
}

function getDetailOrder(id: string) {
  agentService.agentOrderDetail(id).then(res => {
    if (res.status == HttpStatusCode.Ok) {
      isRefreshing.value = false;
      order.value = JSON.parse(JSON.stringify(res.body.data));
      if (order.value.items.length) {
        order.value.items.forEach((e: any) => {
          e.pivot.price_temp = e.pivot.price_off ? e.pivot.price_off : e.pivot.price
        })
      }
      filterProductInShop();
    }
    else {
      toast.error(res.body?.message ?? t('AgentEditOrderComponent.don_hang_khong_ton_tai'));
      close();
    }
  })
}

function statusTagStyle(status: number) {
  switch (status) {
    case appConst.order_status.waiting.value:
      return "waiting";
    case appConst.order_status.confirmed.value:
      return "confirmed";
    // case appConst.order_status.ready.value:
    //     return "ready";
    case appConst.order_status.taken.value:
      return 'taken';
    case appConst.order_status.return.value:
      return 'return';
    case appConst.order_status.cancel.value:
      return "cancel";
    default:
      return 'taken';
  }
}

function paymentStatusStyle(status: number) {
  switch (status) {
    case appConst.order_status.waiting.value:
      return "cancel";
    case appConst.order_status.confirmed.value:
      return "cancel";
    // case appConst.order_status.ready.value:
    //     return "cancel";
    case appConst.order_status.taken.value:
      return 'taken';
    case appConst.order_status.return.value:
      return 'return';
    case appConst.order_status.cancel.value:
      return "cancel";
    default:
      return 'taken';
  }
}

function copyToClipboard(text: string) {
  if (!webInApp.value) {
    window.navigator.clipboard.writeText(text);
  }
  else {
    nuxtApp.$emit(appConst.event_key.send_request_to_app, {
      action: appConst.webToAppAction.copyToClipboard,
      data: text
    })
  }
}

async function updateOrder() {
  isUpdating.value = true;
  let orderTmp = JSON.parse(JSON.stringify(order.value));
  orderTmp.items = [];
  order.value.items.forEach((e: any) => {
    e.pivot.price_off = e.pivot.price_temp
    e.price_off = e.pivot.price_temp
    orderTmp.items.push(e.pivot)
  })

  agentService.agentUpdateOrder(orderTmp).then(res => {
    isUpdating.value = false;
    updated.value = true;
    close(updated.value);
  })
}

async function changeOrderItem(orderItem: CartDto, index: number, event?: boolean) {
  let orderItems = JSON.parse(JSON.stringify(order.value.items))
  if (orderItem.quantity) {
    orderItems[index].pivot = JSON.parse(JSON.stringify(orderItem));
  }
  else if (!event) {
    orderItems.splice(index, 1);
  }
  let total = getOrderTotalPrice();
  let total_price_off = getOrderPriceOffTotalPrice();
  order.value = {
    ...order.value,
    items: JSON.parse(JSON.stringify(orderItems)),
    total_amount: total,
    grand_total: total_price_off,
    discount_amount: total_price_off - total
  }
  setOptionMyShopProduct();
}
function getOrderTotalPrice() {
  if (!order.value.items.length) return 0;
  return order.value.items
    .reduce((total: number, current: any) =>
      total + (
        current.pivot.quantity
          ? parseFloat(current.pivot.price.toString()) * parseFloat(current.pivot.quantity.toString())
          : 0
      ), 0
    )
}

function getOrderPriceOffTotalPrice() {
  if (!order.value.items.length) return 0;
  return order.value.items
    .reduce((total: number, current: any) =>
      total + (
        parseFloat(current.pivot.price_temp)
          ? parseFloat(current.pivot.price_temp) * parseFloat(current.pivot.quantity.toString())
          : 0)
      , 0
    )
}

function filterProductInShop() {
  searchProductLoading.value = true;
  shopService.searchProductsInShop(
    searchProduct.value,
    order.value.shop_id,
    [],
    20,
    0,
    null,
    null,
    false
  ).then(async res => {
    if (res.status && res.status == HttpStatusCode.Ok) {
      let listTemp = res.body.data.result;
      shopProductsFilterResult.value = listTemp;
      shopProductsFilterResultTemp.value = listTemp;
      shopProductsFilterResultCount.value = res.body.data.count;
      searchProductLoading.value = false;
      setOptionMyShopProduct();
    }
    else {
      searchProductLoading.value = false;
    }
  }).catch(() => {
    searchProductLoading.value = false;
  })

  // shopService.getProductsWithCategory(order.value?.shop_id).then(async res => {
  //   if (res.status && res.status == HttpStatusCode.Ok) {
  //     let listTemp:any = [];
  //     res.body.data.categories.forEach((category:any) => {
  //       listTemp = [...listTemp, ...category.products]
  //     });

  //     listTemp.forEach((item: any) => {
  //       item.existing = false;
  //       item.selected = false;
  //       let indexExisting = order.value.items.findIndex(function (e: any) {
  //         return e.id == item.id
  //       });
  //       if (indexExisting != -1) {
  //         item.existing = true;
  //         item.selected = true;
  //       }
  //     });
  //     shopProductsFilterResult.value = listTemp;
  //     shopProductsFilterResultTemp.value = listTemp;
  //     setOptionMyShopProduct();
  //   }

  // })
}

function getMoreFilterProducts() {
  clearTimeout(loadMoreTimeOut);
  if (shopProductsFilterResult.value.length < shopProductsFilterResultCount.value) {
    loadMoreTimeOut = setTimeout(() => {
      searchProductLoading.value = true
      shopService.searchProductsInShop(
        searchProduct.value,
        order.value.shop_id,
        [],
        20,
        shopProductsFilterResult.value.length,
        null,
        null,
        false
      ).then(async res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
          let listTemp = res.body.data.result;
          shopProductsFilterResult.value = [...shopProductsFilterResult.value, ...listTemp];
          searchProductLoading.value = false;
          setOptionMyShopProduct()
        }
        else {
          searchProductLoading.value = false;
        }
      }).catch(() => {
        searchProductLoading.value = false;
      })
    }, 1000);

  }
}

function listFilterScroll() {
  let el = document.getElementById('last_of_list_filter')?.getBoundingClientRect().bottom;
  if (el && (el <= window.innerHeight + 10)) {
    getMoreFilterProducts()
  }
}

function setOptionMyShopProduct() {
  let listTemp = JSON.parse(JSON.stringify(shopProductsFilterResult.value));
  listTemp.forEach((item: any) => {
    item.existing = false;
    item.selected = false;
    let indexExisting = order.value.items.findIndex(function (e: any) {
      return e.id == item.id
    });
    if (indexExisting != -1) {
      item.existing = true;
      item.selected = true;
    }
    let indexSelected = listProductSelect.value.findIndex(function (e: any) {
      return e.id == item.id
    });
    if (indexSelected != -1) {
      item.selected = true;
    }
  });

  shopProductsFilterResult.value = listTemp
}

function searchingProduct(searchText: string) {

  clearTimeout(searchProductTimeout);
  searchProductTimeout = setTimeout(() => {
    filterProductInShop()
    //   let list = shopProductsFilterResultTemp.value.filter((item: any) => {
    //     return nonAccentVietnamese(item.name).includes(nonAccentVietnamese(searchText))
    //   });
    //   searchProductLoading.value = false;
    //   shopProductsFilterResult.value = JSON.parse(JSON.stringify(list))
    //   setOptionMyShopProduct()
  }, 200)
}
</script>

<style lang="scss" src="./AgentEditOrderStyles.scss"></style>