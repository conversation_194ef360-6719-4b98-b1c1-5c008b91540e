<template>
	<div class='create-category-container'>
		<!-- <div class='title-header'>
			<div class="header-left">
			</div>
			<h3>{{ appRouteTitle.CreateCategoryComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
		<HeaderComponent :title="$t('AppRouteTitle.CreateCategoryComponent')">
			<template v-slot:header_left>

			</template>
		</HeaderComponent>
		<div class='v-stack'>
			<div class="v-stack category-image">
				<span class="optional label">{{ $t('CreateCategoryComponent.anh_dai_dien') }}</span>
				<div class="image-container">
					<img class="selected-banner" :src="banner" alt="" v-if="banner" />
					<img :src="no_image" :alt="$t('CreateCategoryComponent.chua_chon_anh')" v-if="!banner" />
				</div>
				<div class="image-actions">
					<button class="select-image-button">
						{{ $t('CreateCategoryComponent.doi_anh') }}
						<input type="file" accept="image/*" :multiple="false" v-on:change="($event: any) => {
							fileChangeBanner($event);
						}" ref="bannerFileName" />
					</button>
					<button class="delete-image-button" v-if="banner" :disabled="banner && banner.length ? false : true"
						v-on:click="() => {
							banner = '';
							bannerFileName.value = null;
							orientationBanner = 0;
						}">
						{{ $t('CreateCategoryComponent.xoa_anh') }}
					</button>
				</div>
			</div>
			<span class='required label'>
				{{ $t('CreateCategoryComponent.ten_danh_muc') }}
			</span>
			<div class="language-options">
				<button class="lang-button" v-for="item in shopLanguage" :class="{ 'active': selectLanguage == item }"
					v-on:click="() => {
						selectLanguage = item;
					}">
					{{ ISO6391.getNativeName(item) }}
				</button>
			</div>
			<input v-for="item in shopLanguage" v-show="item == selectLanguage"
				:title="$t('CreateCategoryComponent.ten_danh_muc')" name='category-name' :disabled="isSaving"
				class='input-custom' :maxlength="appConst.max_text_short"
				:placeholder="`${$t('CreateCategoryComponent.ten_danh_muc')} - ${ISO6391.getName(item)}`"
				:value="getNameTranslate(selectLanguage) || ''" v-on:input="async ($event: any) => {
					await nameCategoryChange($event.target.value, selectLanguage);
					validateName()
				}" />
			<!-- <input :title="$t('CreateCategoryComponent.ten_danh_muc')" name='category-name' class='input-custom'
				:maxlength="appConst.max_text_short" :placeholder="$t('CreateCategoryComponent.ten_danh_muc')"
				:value="name" v-on:input="($event: any) => {
					name = $event.target.value;
					if (!name || !name.length) {
						nameErr = $t('CreateCategoryComponent.vui_long_nhap_ten_danh_muc');
					} else {
						nameErr = '';
					}
				}" v-on:blur="() => {
					if (!name || !name.length) {
						nameErr = $t('CreateCategoryComponent.vui_long_nhap_ten_danh_muc');
					} else {
						nameErr = '';
					}
				}" /> -->
			<span class='error-message'>{{ nameErr }}</span>
		</div>
		<span class='label'>
			{{ $t('CreateCategoryComponent.danh_muc_cha') }}
		</span>
		<v-autocomplete :custom-filter="(item: any, queryText: any, itemObj: any) => {
			let name = nonAccentVietnamese(itemObj.value.name).toLocaleLowerCase();
			let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
			return name.includes(query)
		}" class="custom-v-select mt-2 category-select dropdown-select-container"
			:placeholder="$t('CreateCategoryComponent.chon_danh_muc_cha')" v-model="parent_category_obj"
			:menu-icon="''" :items="dataCategories" variant="plain" :return-object="false"
		>
			<template v-slot:selection="{ item, index }">
				<span>{{ showTranslateProductName(item.value) }}</span>
				
			</template>
			<template v-slot:item="{ props, item }">
				<v-list-item v-bind="props" :title="showTranslateProductName(item.value)"></v-list-item>
			</template>
			<template v-slot:no-data>
				<v-list-item :title="$t('CreateCategoryComponent.khong_tim_thay')"></v-list-item>
			</template>
			<template v-slot:append-inner>
				<Icon name="mdi:chevron-down"></Icon>
			</template>
		</v-autocomplete>
		<!-- <USelectMenu class="dropdown-select-container category-select" searchable
			:searchable-placeholder="$t('CreateCategoryComponent.tim_danh_muc')" :search-attributes="['name']"
			variant="none" :options="dataCategories" :placeholder="$t('CreateCategoryComponent.danh_muc')"
			v-model="parent_id" v-on:change="(newValue: any) => { }" value-attribute="id" option-attribute="name">
			<template #label>
				<span class="truncate">
					{{parent_id ? (dataCategories[dataCategories.findIndex((e: any) => e.id == parent_id)].name) :
						$t('CreateCategoryComponent.chon_danh_muc_cha')}}
				</span>
			</template>
			<template #option="{ option: categoryOption }">
				<span class="truncate">{{ categoryOption.name }}</span>
			</template>
			<template #empty>
				{{ $t('CreateCategoryComponent.chua_co_danh_muc_duoc_luu') }}
			</template>
			<template #option-empty="{ query }">
				<span class="search-category-empty">
					{{ $t('CreateCategoryComponent.khong_tim_thay', { query }) }}
				</span>
			</template>
		</USelectMenu> -->

		<div class='h-stack action-buttons'>
			<button class='cancel-button' :disabled="isSaving" v-on:click="() => close()">
				{{ $t('CreateCategoryComponent.thoat') }}
			</button>
			<button class='save-button' :disabled="isSaving" v-on:click="() => createCategory()">
				{{ $t('CreateCategoryComponent.tao') }}
			</button>
		</div>

	</div>
</template>

<script lang="ts" setup>
import ISO6391 from 'iso-639-1';
import no_image from '~/assets/image/no-image.webp'
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, nonAccentVietnamese, showTranslateProductName } from '~/assets/AppConst';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { AuthService } from '~/services/authService/authService';
import { HttpStatusCode } from 'axios';
import exifr from "exifr";
import { Buffer } from "buffer";

var router = useRouter();
var route = useRoute();
var props = defineProps({
	shopId: "" as any,
	dataCategories: {},
	mode: null,
	shop_data: null
})
const { t, locale } = useI18n();
var emit = defineEmits(['close']);

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var imageService = new ImageService();
var shopService = new ShopService();
var categoryService = new CategoryService();

var name = ref("");
var nameErr = ref("");
var parent_id = ref<any>(null);
var parent_category_obj = ref<any>(null)
var parentIdErr = ref("");
var shopId = ref(props.shopId ? props.shopId : null);
var dataCategories = ref((props.dataCategories ? props.dataCategories : []) as any);
var nameTranslation = ref<any[]>([])
var isSaving = ref(false);

var shopLanguage = ref<string[]>([])
var selectLanguage = ref(null as any);

var banner = ref("");
var bannerFileName = ref(null as any);
var orientationBanner = ref(0);
var isBannerChange = ref(false);

onMounted(() => {
	shopLanguage.value = props.shop_data.language.at(0) != '[' ? props.shop_data.language : JSON.parse(props.shop_data.language);
	if (shopLanguage.value.indexOf(appConst.defaultLanguage) == -1) {
		shopLanguage.value.splice(0, 0, appConst.defaultLanguage);
	}
	if (shopLanguage.value.indexOf('en') == -1) {
		shopLanguage.value.splice(shopLanguage.value.indexOf(appConst.defaultLanguage) + 1, 0, 'en');
	}
	selectLanguage.value = shopLanguage.value[shopLanguage.value.indexOf(locale.value)] ?? shopLanguage.value[0] ?? appConst.defaultLanguage;
	console.log(shopLanguage.value);
});
function createCategory() {
	isSaving.value = true;
	categoryService.createCategory({ name: name.value, parent_id: parent_category_obj.value?.id ?? null, shop_id: shopId.value, translation: nameTranslation.value }).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			// toast.success(t('CreateCategoryComponent.tao_danh_muc_thanh_cong'));
			// setTimeout(() => {
			// 	close(res.body.data);
			// }, 1500);
			if (isBannerChange.value) {
				try {
					if (banner.value && banner.value.length) {
						let indexFirstName = nameTranslation.value?.findIndex(function (el: any) {
							return el.name?.length > 0
						})
						let banner$ = {
							path: banner.value,
							object_type: appConst.object_type.category,
							image_type: 'banner',
							title: nameTranslation.value[indexFirstName]?.name ?? name.value,
							description: nameTranslation.value[indexFirstName]?.name ?? name.value,
							index: 0,
							orientation: orientationBanner.value,
							parent_id: res.body.data.id,
						}
						await imageService.insertImage(banner$).then(res => {
							if (res.status == HttpStatusCode.Ok) { }
							else {
								toast.error(t('CreateCategoryComponent.luu_anh_that_bai'));
							}
							// isEditingShop.value = false;
						});
					}
				}
				finally {
					toast.success(t('CreateCategoryComponent.tao_danh_muc_thanh_cong'));
					setTimeout(() => {
						close(res.body.data);
					}, 1500)
				}
			}
			else {
				toast.success(t('CreateCategoryComponent.tao_danh_muc_thanh_cong'));
				setTimeout(() => {
					close(res.body.data);
				}, 1500)
			}
		}
		else {
			toast.error(t('CreateCategoryComponent.tao_danh_muc_that_bai'));
			isSaving.value = false;
		}

	}).catch(err => {
		console.log(err);
		toast.error(t('CreateCategoryComponent.tao_danh_muc_that_bai'));
		isSaving.value = false;
	})
}
function close(returnObj = null) {
	emit('close', returnObj);
}

function getNameTranslate(language: string) {
	let indexTranslate = nameTranslation.value.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? nameTranslation.value[indexTranslate].name : '';
}

function nameCategoryChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.name = text;
	// 	if (!productData.value.name || !productData.value.name.length) {
	// 		nameErr.value = 'Vui lòng nhập tên Tiếng Việt của sản phẩm';
	// 	} else {
	// 		nameErr.value = '';
	// 	}
	// }

	let indexTranslate = nameTranslation.value.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		nameTranslation.value[indexTranslate].name = text;
	}
	else {
		nameTranslation.value.push({
			language_code: translation,
			name: text
		})
	}

	console.log(nameTranslation.value);
}
function validateName() {
	let indexVietnamese = nameTranslation.value.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && nameTranslation.value[indexVietnamese]?.name?.length > 0) {
		nameErr.value = "";
		name.value = nameTranslation.value[indexVietnamese].name;
		return true
	}

	let indexFirstName = nameTranslation.value.findIndex(function (el: any) {
		return el.name?.length > 0
	})
	if (indexFirstName != -1) {
		nameErr.value = "";
		name.value = nameTranslation.value[indexFirstName].name;
		return true
	}
	nameErr.value = t('CreateCategoryComponent.vui_long_nhap_ten_danh_muc');
	return false;
}

async function fileChangeBanner(fileInput: any) {
	if (fileInput.target.files.length) {
		if (fileInput.target.files[0].size > appConst.image_size.max) {
			let imgErr = t('CreateCategoryComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
			toast.error(imgErr);
		}
		else {
			bannerFileName.value = fileInput.target.files;

			const reader = new FileReader();
			reader.onload = async (e: any) => {
				let imageFile = e.target;
				const image = new Image();

				image.src = e.target.result;
				banner.value = e.target.result;
				isBannerChange.value = true;
				let orientationExif;
				if (fileInput.target.files[0].type != 'image/webp') {
					orientationExif = await exifr.orientation(image) || 0;
				}
				else orientationExif = 0;
				orientationBanner.value = orientationExif ? orientationExif : 0;

				bannerFileName.value = fileInput.target.files[0].name;
			};
			await reader.readAsDataURL(fileInput.target.files[0]);
		}
	}
}
</script>

<style lang="scss" src="./CreateCategoryStyles.scss"></style>