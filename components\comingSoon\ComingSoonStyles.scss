.coming-soon-container{
  height: 100%;
  flex: 1;
  background: white;
  margin: 0;
  overflow: hidden;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  z-index: 100;

  & > .logo{
    padding: 5px 20px;
    width: 100%;
    background-size: 100%;
    // background-image: url("~/assets/image_13_3_2024/patter.jpg");
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 18px;
    transition: min-height 0.5s ease;

    & > img {
      margin: auto;
      height: 40px;
      filter: drop-shadow(0 0 1px white);
    }
  }

  & > .coming-soon-content{
    flex: 1;
    flex-direction: column;
    position: relative;
    align-items: center;
    justify-content: center;
    display: flex;
    & > img {
      width: 70%;
    }
  
    & > span {
      font-size: 30px;
      font-weight: 600;
      color: #646464;
      text-align: center;
      padding: 0 10%;
    }
  }
  
}