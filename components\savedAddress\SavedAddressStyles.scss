.public-container.order-mode-container{
  border-radius: 10px;
  height: 100%;
  & > .saved-address-container{
    overflow: auto;
    height: 100%;
  }
  & .saved-user-list{
    flex: 1;
    padding: 0 10px;
    overflow: auto;
  }

  & .use-temporary-address{
    padding: 0 5px;
    display: flex;
    justify-content: flex-end;
    & > button{
      border-radius: 7px;
      margin-left: auto;
      background-color: #3591ec;
      color: white;
      height: 100%;
      padding: 5px 10px;
      font-weight: 600;
    }
  }
}
.saved-address-container {
  height: 100%;
  flex: 1;
  margin: 0;
  overflow: hidden;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
  background: white;

  & > .saved-user-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: 100%;
    background: white;
    // overflow: auto;

    & > .item-saved-user {
      display: flex;
      gap: 5px;
      text-align: left;
      padding: 5px 10px;
      border-bottom: thin solid #bcbcbc;

      & > .saved-user-info {
        display: flex;
        flex-direction: column;
        flex: 1;
        gap: 5px;

        cursor: pointer;

        & > .name-phone {
          display: flex;
          justify-content: space-between;
          color: #36353b;
          font-size: 20px;
          font-weight: 600;
          align-items: center;

          & > .phone {
            padding-left: 10px;
            border-left: thin solid #d5d5d5;
          }
        }

        & > .address {
          color: #707070;

          & > .address-type{
            background: #ddd;
            color: #2e2d30;
            padding: 0px 10px;
            border-radius: 5px;
            font-weight: 600;
            margin-right: 5px;
          }
          & > .address-type.home{
            background: #eafff5;
            color: #00ad53;
          }
          & > .address-type.work{
            background: #ebf3ff;
            color: #0a68fe;
          }
        }
        & > .h-stack{
          gap: 10px;
        }
        & .default {
          display: flex;
          color: #f05976;
          font-weight: 600;
          gap: 5px;
        }
        & .selected {
          display: flex;
          color: #1f75cb;
          font-weight: 600;
          gap: 5px;
        }
      }

      & > .saved-user-actions {
        display: flex;
        align-items: flex-start;
        padding: 3px 0;
        & > button {
          display: flex;
          font-size: 25px;
          color: #83818c;
        }
      }
    }
  }

  & > .none-user-list {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: white;
    // height: 100%;
    margin: auto;
    overflow: hidden;

    & > img {
      height: 100%;
      width: 80%;
      object-fit: contain;
    }

    & > span {
      font-weight: 600;
      font-size: 20px;
      color: #828187;
    }
  }

  & > .footer {
    display: flex;
    padding: 5px 10px 20px;
    background-color: white;
    width: 100%;
    margin-top: auto;
    position: sticky;
    bottom: 0;

    & > button {
      width: 100%;
      border-radius: 7px;
      color: white;
      display: flex;
      font-size: 17px;
      font-weight: 600;
      justify-content: center;
      align-items: center;
      padding: 10px;
      background: var(--primary-color-1);
    }
  }
}

.saved-user-actions-list {
  border-radius: 10px 0 10px 10px !important;
  padding: 0 !important;
  margin-right: 10px;
  color: black;
  text-align: left;
  box-shadow: 0 0 10px rgb(0, 0, 0, 30%) !important;

  & .saved-user-dropdown-item {
    min-height: 40px;

    & .v-list-item-title {
      font-weight: bold;
    }
  }
}
