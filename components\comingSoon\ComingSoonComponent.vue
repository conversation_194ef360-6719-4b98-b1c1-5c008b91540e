<template>
	<div class="public-container">
		<div class="coming-soon-container">
			<div class="h-stack logo">
				<img loading="lazy" :src="logo" :placeholder="logo" alt="logo" />
			</div>
			<div class="coming-soon-content">
				<img loading="lazy" :src="coming_soon" :placeholder="coming_soon" alt="Coming Soon" />
				<!-- <span>Chức năng này đang trong giai đoạn phát triển.</span> -->
			</div>
		</div>
	</div>

</template>

<script lang="ts" setup>

import logo from "~/assets/image_13_3_2024/logo.png";
import coming_soon from "~/assets/image/comingsoon.png"
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";

const nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();
var emit = defineEmits(['accept', 'close', 'reject']);
var show = ref(true);
onMounted(async () => {

})
</script>

<style lang="scss" src="./ComingSoonStyles.scss"></style>