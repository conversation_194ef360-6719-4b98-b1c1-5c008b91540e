.edit-category-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 17px;
  overflow: visible;

  &>.title-header {
    // font-size: 1.3em;

    // margin: 0;
    // text-align: center;
    color: var(--primary-color-1);
    border-bottom: none;
  }

  &>.v-stack {
    margin-bottom: 10px;
  }

  & .category-image {
    margin-bottom: 10px;
    font-size: 1em;
    gap: 5px;

    &>.image-container {
      // aspect-ratio: var(--aspect-ratio);
      position: relative;
      aspect-ratio: 2.2;
      display: flex;
      justify-content: center;
      width: 100%;
      align-items: center;
      box-shadow: 0 0 10px rgb(0, 0, 0, 15%);
      border-radius: 10px;
      overflow: hidden;
    }

    & img.selected-banner {
      object-fit: cover;
      width: 100%;
      background-color: var(--color-background-2);
    }

    & .image-actions {
      display: flex;
      gap: 10px;



      &>button {
        background-color: #efefef;
        color: #595e63;
        border-radius: 10px;
        padding: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        flex: 1;
        font-weight: 600;

        & input {
          opacity: 0;
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          cursor: pointer;
          z-index: 2;
        }

        & input::file-selector-button {
          cursor: pointer;
        }
      }

      &>button:not(:disabled):active,
      >button.active {
        background-color: var(--primary-color-1);
        color: white;
      }
    }
  }

  & .language-options {
    display: flex;
    gap: 5px;
    font-size: 15px;
    justify-content: center;
    width: 100%;
    padding: 10px 0;
    white-space: nowrap;
    flex-wrap: wrap;

    &>.lang-button {
      background-color: #dbdbdb;
      color: #202020;
      padding: 0 10px;
      border-radius: 2em;
    }

    &>.lang-button.active {
      background-color: #202020;
      color: white;
      font-weight: 600;
    }
  }

  &>.action-buttons {
    justify-content: space-evenly;
    margin: 10px 0;

    &>button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
    }

    &>.cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }

    &>.save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    &>.cancel-button:disabled {
      color: var(--color-text-note);
      border-color: var(--color-text-note);
      opacity: 1;
    }

    &>.save-button:disabled {
      background: #ccc;
      color: var(--primary-color-1);
      border-color: #ccc;
      opacity: 1;
    }
  }

  & .search-category-empty {
    color: var(--color-text-note);
  }

  & .input-custom {
    background: #f5f6fa;
    border: none;
    border-radius: 7px;
    padding: 10px;
  }

  & .dropdown-select-container {
    background: #f5f6fa;
    border: none;
    border-radius: 7px;
    padding: 0;

    &>.v-input__control {
      padding: 10px;
    }
  }
}

.category-content-container {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}