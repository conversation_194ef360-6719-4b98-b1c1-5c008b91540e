<template>
  <div class="public-container">
    <div class="forget-password-container">
      <!-- <div class="title-header">
      <div class="header-left">
        <button class="back-button" v-on:click="() => {
          close();
        }
          ">
          <Icon name="lucide:chevron-left" />
        </button>
      </div>
      <h3>{{ title }}</h3>
      <div class="header-right"></div>
    </div> -->
      <SubHeaderV2Component :title="title">
      </SubHeaderV2Component>
      <div class="forget-password-content">
        <div class="step-content" v-if="step == 1">
          <img loading="lazy" :src="forget_password" :placeholder="forget_password"
            alt="$t('ForgetPasswordComponent.quen_mat_khau')" />
          <span class="step-title">{{ $t('ForgetPasswordComponent.khong_nho_duoc_mat_khau') }}</span>

          <button class="get-protocol" v-on:click="() => {
            protocol = protocol == 'email' ? 'phone' : 'email';
          }">
            {{ protocol == 'email' ? $t('ForgetPasswordComponent.so_dien_thoai') :
              $t('ForgetPasswordComponent.dia_chi_email') }}
          </button>
          <div class="v-stack" v-if="protocol == 'email'">
            <span class="label">{{ $t('ForgetPasswordComponent.email_cua_ban') }}</span>
            <div class="content-input-group">
              <input title="email" name="email" maxLength="255" autoComplete="new-password" class="content-input"
                placeholder="<EMAIL>" v-model="email" v-on:input="($event: any) => {
                  emailValidation(true);
                }" v-on:blur="() => {
                emailValidation(true);
              }" />
            </div>

            <span class="error-message">{{ emailErr }}</span>

            <!-- <button class="step-action" :disabled="saving || !email || emailErr" v-on:click="() => {
    sentMailForget();
  }">
    {{ $t('ForgetPasswordComponent.lay_ma') }}
    <Icon name="material-symbols:arrow-right-alt" size="20" v-show="!saving" />
    <Icon name="eos-icons:loading" size="20" v-show="saving" />
  </button> -->
          </div>

          <div class='v-stack' v-if="protocol == 'email'" v-show="email?.length">
            <span class='label'>
              {{ $t('ForgetPasswordComponent.ma_xac_thuc_email') }}
            </span>
            <div class="content-input-group">
              <input :title="$t(' ForgetPasswordComponent.ma_xac_thuc_email')" name='email_confirm_code' type="phone"
                maxLength=255 class='content-input' :placeholder="$t('ForgetPasswordComponent.nhap_ma_xac_thuc')"
                v-model="emailConfirmCode" v-on:input="($event: any) => {
                  emailConfirmCode = $event.currentTarget.value;
                  emailConfirmValidation();
                }" />
              <button class="send-code" :disabled="emailErr?.length > 0 || saving" v-on:click="() => { sendCodeOTP() }"
                v-if="!time_remaining">{{ $t('ForgetPasswordComponent.lay_ma') }}</button>

              <button v-if="time_remaining > 0" class="send-code" disabled>{{
                $t('ForgetPasswordComponent.lay_lai_ma_sau')
                }} &nbsp;
                <vue-countdown :time="time_remaining * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                  time_remaining = 0;
                }" v-on:progress="({ totalSeconds }) => {
                // time_remaining = totalSeconds;
              }">
                  {{ minutes ? `${minutes} ${$t('ForgetPasswordComponent.phut')}` : '' }} {{ seconds }} {{
                    $t('ForgetPasswordComponent.giay') }}
                </vue-countdown>
              </button>
            </div>
            <span class='error-message'>{{ emailConfirmCodeErr }}</span>
          </div>

          <div class="v-stack" v-if="protocol == 'phone'">
            <span class="label">{{ $t('ForgetPasswordComponent.so_dien_thoai_cua_ban') }}</span>
            <div class="content-input-group">
              <input :title="$t('ForgetPasswordComponent.so_dien_thoai')" name="phone" type="phone" maxLength="255"
                autoComplete="off" class="content-input"
                :placeholder="$t('ForgetPasswordComponent.so_dien_thoai_placeholder')" :value="phone || ''" v-on:input="($event: any) => {
                  phone = validPhone($event.currentTarget.value);
                  phoneValidation(true);
                }" v-on:blur="() => {
                phoneValidation(true);
              }" />
            </div>
            <span class="error-message">{{ phoneErr }}</span>

            <!-- <button class="step-action" :disabled="saving || phoneErr || !phone" v-on:click="() => {
    sendOTP()
  }">
    {{ $t('ForgetPasswordComponent.lay_ma') }}
    <Icon name="material-symbols:arrow-right-alt" size="20" v-show="!saving" />
    <Icon name="eos-icons:loading" size="20" v-show="saving" />
  </button> -->
          </div>

          <div class='v-stack' v-if="protocol == 'phone'" v-show="validPhone(phone)?.length">
            <span class='label'>
              {{ $t('ForgetPasswordComponent.ma_xac_thuc_dien_thoai_di_dong') }}
            </span>
            <div class="content-input-group">
              <input :title="$t('ForgetPasswordComponent.ma_xac_thuc_so_dien_thoai')" name='phone_confirm_code'
                type="phone" maxLength=255 class='content-input'
                :placeholder="$t('ForgetPasswordComponent.nhap_ma_xac_thuc')" v-model="phoneConfirmCode" v-on:input="($event: any) => {
                  phoneConfirmCode = $event.currentTarget.value;
                  phoneConfirmValidation();
                }" />
              <button class="send-code" :disabled="phoneErr?.length > 0 || saving" v-on:click="() => { sendCodeOTP() }"
                v-if="!time_remaining">{{ $t('ForgetPasswordComponent.lay_ma') }}</button>

              <button v-if="time_remaining > 0" class="send-code" disabled>{{
                $t('ForgetPasswordComponent.lay_lai_ma_sau')
                }} &nbsp;
                <vue-countdown :time="time_remaining * 1000" v-slot="{ minutes, seconds }" v-on:end="() => {
                  time_remaining = 0;
                }" v-on:progress="({ totalSeconds }) => {
                // time_remaining = totalSeconds;
              }">
                  {{ minutes ? `${minutes} ${$t('ForgetPasswordComponent.phut')}` : '' }} {{ seconds }} {{
                    $t('ForgetPasswordComponent.giay') }}
                </vue-countdown>
              </button>
            </div>

            <span class='h-stack '>
              <span class="error-message">{{ phoneConfirmCodeErr }}</span>

              <label class='checkbox-input-label'>
                <v-switch v-model="agent" flat color="var(--primary-color-1)" hide-details class="my-switches">
                </v-switch>
                <span>
                  {{ $t('ForgetPasswordComponent.lay_ma_qua') }} {{ agent ? $t('ForgetPasswordComponent.zalo') :
                    $t('ForgetPasswordComponent.sms') }}
                </span>
              </label>
            </span>
          </div>

          <!-- <span class="step-title"> Tạo mật khẩu mới </span> -->
          <div class="v-stack">
            <span class="required label">{{ $t('ForgetPasswordComponent.mat_khau_moi') }}</span>
            <div class="content-input-group">
              <input :title="$t('ForgetPasswordComponent.mat_khau')" name="password" :maxLength="255"
                autoComplete="new-password" class="content-input" placeholder="******"
                :type="passwordShow ? 'text' : 'password'" :value="password" v-on:input="($event: any) => {
                  password = $event.currentTarget.value;
                  passwordValidation();
                }" v-on:blur="() => {
                passwordValidation();
              }" />
              <button v-on:click="() => {
                passwordShow = !passwordShow;
              }">
                <Icon name="mage:eye-closed" v-if="!passwordShow" />
                <Icon name="mage:eye" v-if="passwordShow" />
              </button>
            </div>

            <!-- <span class="error-message">{{ passwordErr }}</span> -->
            <span class="error-message" :class="{ 'success': passwordErr1 == false }">
              <Icon name="ph:dot-outline-fill" v-if="passwordErr1"></Icon>
              <Icon name="material-symbols:check-small-rounded" v-else></Icon>
              {{ $t('ForgetPasswordComponent.phai_chua_so_chu_cai_ky_tu_dac_biet') }}&nbsp;
              <v-tooltip location="bottom" open-on-click
                :text="`${$t('ForgetPasswordComponent.ky_tu_dac_biet')}: !@#$%^&*()_+-=[]{};:|,.<>?\\`">
                <template v-slot:activator="{ props }">
                  <span v-bind="props">
                    <Icon name="bi:question-circle"></Icon>
                  </span>
                </template>
              </v-tooltip>
            </span>
            <span class="error-message" :class="{ 'success': passwordErr2 == false }">
              <Icon name="ph:dot-outline-fill" v-if="passwordErr2"></Icon>
              <Icon name="material-symbols:check-small-rounded" v-else></Icon>
              {{ $t('ForgetPasswordComponent.phai_tu_6_den_20_ky_tu') }}
            </span>
          </div>

          <div class="v-stack">
            <span class="required label">{{ $t('ForgetPasswordComponent.xac_nhan_mat_khau_moi') }}</span>
            <div class="content-input-group">
              <input :title="$t('ForgetPasswordComponent.xac_nhan_mat_khau')" name="password" :maxLength="255"
                autoComplete="new-password" class="content-input" placeholder="******"
                :type="passwordConfirmationShow ? 'text' : 'password'" :value="passwordConfirmation" v-on:input="($event: any) => {
                  passwordConfirmation = $event.currentTarget.value;
                  confirmPasswordValidation();
                }" v-on:blur="() => {
                confirmPasswordValidation();
              }" />
              <button v-on:click="() => {
                passwordConfirmationShow = !passwordConfirmationShow;
              }">
                <Icon name="mage:eye-closed" v-if="!passwordConfirmationShow" />
                <Icon name="mage:eye" v-if="passwordConfirmationShow" />
              </button>
            </div>

            <span class="error-message">{{ passwordConfirmationErr }}</span>
          </div>

          <button class="step-action" :disabled="saving
            || (protocol == 'email' ? emailErr?.length : false)
            || (protocol == 'phone' ? phoneErr?.length : false)
            " v-on:click="() => {
            saveNewPassword();
          }">
            {{ $t('ForgetPasswordComponent.dat_lai_mat_khau') }}
            <Icon name="eos-icons:loading" size="20" v-show="saving" />
          </button>

        </div>
        <!-- <div class="step-content step-2" v-if="step == 2">
        <span class="step-text">
          Mã xác nhận đã được gửi tới <span>{{ protocol == 'email' ? email : phone }}</span>
        </span>
        <v-otp-input v-model="code" class="otp-input"></v-otp-input>
        <span class="error-message">{{ codeErr }}</span>

        <button class="step-action" :disabled="saving" v-on:click="() => {
          protocol == 'email' ? checkCode() : confirmOTP()
        }
          ">
          Xác nhận
          <Icon name="eos-icons:loading" size="20" v-show="saving" />
        </button>

        <span class="step-note">
          Chưa nhận được mã?
          <button v-if="protocol == 'email'" v-on:click="() => {
            resendMail();
          }
            " v-show="!resendCodeTimer">Gửi lại</button>
          <button v-if="protocol == 'phone'" v-on:click="() => {
            resendPhone();
          }" :disabled="time_remaining > 0" v-show="!resendCodeTimer">
            {{ time_remaining > 0 ? 'Gửi lại sau' : 'Gửi lại' }}
            <vue-countdown v-if="time_remaining > 0" :time="time_remaining * 1000" v-slot="{ minutes, seconds }"
              v-on:end="() => {
                time_remaining = 0;
              }" v-on:progress="({ totalSeconds }) => {
                // time_remaining = totalSeconds;
              }">
              {{ minutes ? minutes + ' phút' : '' }} {{ seconds }} giây
            </vue-countdown>
          </button>
          <span v-show="resendCodeTimer">Đang gửi...</span>
        </span>
        <button class="use-other-protocol" v-on:click="() => {
          gotoStep(1);
        }">
          Dùng phương thức khác
        </button>
      </div> -->
        <!-- <div class="step-content" v-if="step == 3">
        <span class="step-title"> Tạo mật khẩu mới </span>
        <div class="v-stack">
          <span class="required label"> Mật khẩu </span>
          <div class="password-input-group">
            <input title="Mật khẩu" name="password" :maxLength="255" autoComplete="password" class="input-custom"
              placeholder="******" :type="passwordShow ? 'text' : 'password'" :value="password" v-on:input="($event: any) => {
                password = $event.currentTarget.value;
                if (!password || !password.length) {
                  passwordErr = 'Vui lòng nhập mật khẩu'
                }
                else {
                  passwordValidation();
                }
              }" v-on:blur="() => {
                if (!password || !password.length) {
                  passwordErr = 'Vui lòng nhập mật khẩu';
                } else {
                  passwordValidation();
                }
              }
                " />
            <button v-on:click="() => {
              passwordShow = !passwordShow;
            }
              ">
              <Icon name="ion:eye" v-if="!passwordShow" />
              <Icon name="ion:eye-off" v-if="passwordShow" />
            </button>
          </div>

          <span class="error-message">{{ passwordErr }}</span>
        </div>
        <div class="v-stack">
          <span class="required label"> Xác nhận mật khẩu </span>
          <div class="password-input-group">
            <input title="Xác nhận Mật khẩu" name="password" :maxLength="255" autoComplete="password"
              class="input-custom" placeholder="******" :type="passwordConfirmationShow ? 'text' : 'password'"
              :value="passwordConfirmation" v-on:input="($event: any) => {
                passwordConfirmation = $event.currentTarget.value;
                confirmPasswordValidation();
              }" v-on:blur="() => {
                confirmPasswordValidation();
              }
                " />
            <button v-on:click="() => {
              passwordConfirmationShow = !passwordConfirmationShow;
            }
              ">
              <Icon name="ion:eye" v-if="!passwordConfirmationShow" />
              <Icon name="ion:eye-off" v-if="passwordConfirmationShow" />
            </button>
          </div>

          <span class="error-message">{{ passwordConfirmationErr }}</span>
        </div>

        <button class="step-action" :disabled="saving
          || password != passwordConfirmation
          || !password
          || !password.length
          || !passwordConfirmation
          || !passwordConfirmation.length
          || passwordErr.length > 0
          || passwordConfirmationErr.length > 0" v-on:click="() => {
            saveNewPassword();
          }
            ">
          Lưu
          <Icon name="eos-icons:loading" size="20" v-show="saving" />
        </button>
      </div> -->
      </div>
    </div>
  </div>

</template>

<style lang="scss" src="./ForgetPasswordStyles.scss"></style>

<script setup lang="ts">
import { useSSRContext } from "vue";
import { appConst, appDataStartup, encryptPassword, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import Vue3Toastify, { toast } from "vue3-toastify";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import forget_password from "~/assets/image/forget_password.png";
import VueCountdown from '@chenfengyuan/vue-countdown';
import { PublicService } from "~/services/publicService/publicService";
import { HttpStatusCode } from "axios";
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();

let checkEmailTimeOut: any = null;
let resendTimeInterval: any = null;

let authService = new AuthService();
let userService = new UserService();
let publicService = new PublicService();
let placeService = new PlaceService();
let shopService = new ShopService();
let productService = new ProductService();

var protocol = ref("phone");

let title = ref(t('ForgetPasswordComponent.quen_mat_khau'));
let email = ref();
let emailErr = ref();
let phone = ref();
let phoneErr = ref();
let code = ref("" as any);
let codeErr = ref();
let confirm_code = ref();
let step = ref(1);
let saving = ref(false);
var agent = ref(true);
let resendCodeTimer = ref();

let password = ref("");
// let passwordErr = ref("");
var passwordErr1 = ref(true);
var passwordErr2 = ref(true);
let passwordShow = ref(false);
let passwordConfirmation = ref("");
let passwordConfirmationShow = ref(false);
let passwordConfirmationErr = ref("");
let user_id = ref(null);
let messageAccountBlock = ref(false);
let messageAccountActive = ref(false);
var time_remaining = useState('otp_cooldown_forget', () => { return 0 });

var phoneConfirmCode = ref(null as any);
var phoneConfirmCodeErr = ref("");
var emailConfirmCode = ref(null as any);
var emailConfirmCodeErr = ref("");

onUnmounted(async () => { });
onMounted(async () => {
  title.value = 'Quên mật khẩu?'
  useSeoMeta({
    title: title.value,
  });
});

function emailValidation(checkExist = false) {
  clearTimeout(checkEmailTimeOut);
  checkEmailTimeOut = setTimeout(() => {
    let re = appConst.validateValue.email;
    if (!email.value || !email.value?.length) {
      emailErr.value = t('ForgetPasswordComponent.vui_long_nhap_email');
      return;
    } else if (email.value && email.value?.length && !re.test(email.value)) {
      emailErr.value = t('ForgetPasswordComponent.email_khong_dung_dinh_dang');
      return;
    } else if (checkExist) {
      checkExistingEmail();
      return
    }
    else {
      emailErr.value = "";
    }
  }, 500);
}

function phoneValidation(checkExist = false) {
  let re = appConst.validateValue.phone;
  if (!phone?.value?.length) {
    phoneErr.value = t('ForgetPasswordComponent.vui_long_nhap_sdt');
    return;
  }
  if (!re.test(validPhone(phone.value))) {
    phoneErr.value = t('ForgetPasswordComponent.sdt_khong_dung');
    return;
  }
  else if (checkExist) {
    checkExistingPhone();
    return
  }
  else {
    phoneErr.value = "";
  }
}

async function checkExistingPhone() {
  let body = {
    key: validPhone(phone.value),
    type: appConst.check_exist_enum.phone,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check == false) {
    phoneErr.value = t('ForgetPasswordComponent.sdt_chua_duoc_su_dung');
  }
  else {
    phoneErr.value = "";
  }
}

async function checkExistingEmail() {
  if (email.value && email.value?.length) {
    let body = {
      key: email.value,
      type: appConst.check_exist_enum.email,
    };

    let check = await userService.check_exist(body);
    check = check.body.data;
    if (check == false) {
      emailErr.value = t('ForgetPasswordComponent.email_chua_duoc_su_dung');
    }
    else {
      emailErr.value = "";
    }
  }
}

function sentMailForget() {
  saving.value = true;
  let body = {
    email: email.value,
  };
  userService.forgotPasswordClient(body).then((res) => {
    if (res["status"] == HttpStatusCode.Ok) {
      gotoStep(2);
      saving.value = false;
    } else if (res["status"] == HttpStatusCode.BadRequest) {
      if (res["body"] == "login_000_E_003") {
        messageAccountBlock.value = true;
      } else if (res["body"] == "login_000_E_002") {
        messageAccountBlock.value = true;
      } else {
        emailValidation();
      }
      saving.value = false;
    }
  });
}

function sendOTP() {
  saving.value = true;
  publicService.getOTP(validPhone(phone.value), protocol.value).then((res) => {
    time_remaining.value = res.otp_cooldown;
    gotoStep(2);
    saving.value = false;
  });
}

function sendCodeOTP() {
  saving.value = true;
  publicService.getOTP(
    protocol.value == 'phone' ? validPhone(phone.value) : email.value,
    protocol.value,
    agent.value).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        time_remaining.value = res.body?.data?.otp_cooldown;
      }
      else {
        time_remaining.value = res.otp_cooldown;
      }
      saving.value = false;
    })
}

function emailConfirmValidation() {
  if (!emailConfirmCode.value || !emailConfirmCode.value?.length) {
    emailConfirmCodeErr.value = t('ForgetPasswordComponent.vui_long_nhap_ma_xac_thuc');
  } else {
    emailConfirmCodeErr.value = "";
  }
}
function phoneConfirmValidation() {
  if (!phoneConfirmCode.value || !phoneConfirmCode.value?.length) {
    phoneConfirmCodeErr.value = t('ForgetPasswordComponent.vui_long_nhap_ma_xac_thuc');
  } else {
    phoneConfirmCodeErr.value = "";
  }
}

function resendMail() {
  resendCodeTimer.value = true;
  let body = {
    email: email.value,
  };
  userService.forgotPasswordClient(body).then((res) => {
    if (res["status"] == HttpStatusCode.Ok) {
      gotoStep(2);
      resendCodeTimer.value = false;
    } else if (res["status"] == HttpStatusCode.BadRequest) {
      if (res["body"] == "login_000_E_003") {
        messageAccountBlock.value = true;
      } else if (res["body"] == "login_000_E_002") {
        messageAccountActive.value = true;
      } else {
        emailValidation();
      }
      resendCodeTimer.value = false;
    }
  });
}

function resendPhone() {
  resendCodeTimer.value = true;
  publicService.getOTP(validPhone(phone.value), protocol.value).then(res => {
    time_remaining.value = res.otp_cooldown;
    gotoStep(2);
    resendCodeTimer.value = false;

  })
}

function checkCode() {
  if (code.value && code.value?.length) {
    saving.value = true;
    let body = {
      code: code.value,
      action_type: appConst.confirm_code_action.forget_password,
      user_id: "",
    };

    userService.checkCode(body).then((res) => {
      if (res.status == HttpStatusCode.Ok) {
        if (res.body.data != false) {
          user_id.value = res.body.data.user_id;
          confirm_code.value = code.value;
          gotoStep(3);
        } else {
          codeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
        }
      }
      saving.value = false;
    });
  } else {
    codeErr.value = t('ForgetPasswordComponent.chua_nhap_ma_xac_thuc');
  }
}

async function confirmOTP() {
  let data = {
    phone: validPhone(phone.value),
    otp: code.value
  }
  let confirm = await publicService.confirmOtp(data);
  if (confirm.status == HttpStatusCode.Ok) {
    if (confirm.body.data != false) {
      user_id.value = confirm.body.user_id;
      confirm_code.value = code.value;
      gotoStep(3);
    } else {
      codeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
    }
  }
  else {
    toast.error(t('ForgetPasswordComponent.ma_xac_thuc_sai'))
  }
  saving.value = false;
}

async function saveNewPassword() {
  saving.value = true;

  if (protocol.value == 'email') {
    await emailValidation();
    emailConfirmValidation();
  }
  else {
    emailErr.value = "";
  }
  if (protocol.value == 'phone') {
    await phoneValidation();
    phoneConfirmValidation();
  }
  else {
    phoneErr.value = "";
  }
  passwordValidation();
  confirmPasswordValidation();
  if (
    !passwordErr1?.value &&
    !passwordErr2?.value &&
    !passwordConfirmationErr?.value?.length &&
    (protocol.value == 'phone' ? phoneConfirmCode.value?.length : true) &&
    (protocol.value == 'email' ? emailConfirmCode.value?.length : true) &&
    !emailErr.value?.length &&
    !phoneErr.value?.length
  ) {
    let body;
    if (protocol.value == 'phone') {
      body = {
        password: await encryptPassword(password.value),
        // id: user_id.value,
        otp: phoneConfirmCode.value,
        phone: validPhone(phone.value),
        agent: agent.value ? 'zalo' : 'phone'
      };
    }
    else {
      body = {
        password: await encryptPassword(password.value),
        // id: user_id.value,
        otp: emailConfirmCode.value,
        email: email.value,
        agent: 'email'
      };
    }

    userService
      .changeForgetPassword(body)
      .then((res) => {
        if (res.status == HttpStatusCode.Ok) {
          if (res.body.data && res.body.data.result) {
            toast.success(t('ForgetPasswordComponent.cap_nhat_mat_khau_thanh_cong'));
            close();
          } else {
            passwordValidation();
            if (protocol.value == 'email') {
              emailValidation(true);
            }
            else {
              phoneValidation(true);
            }

            toast.error(t('ForgetPasswordComponent.cap_nhat_mat_khau_that_bai'));
          }
        } else {
          passwordValidation();
          if (protocol.value == 'email') {
            emailValidation(true);
          }
          else {
            phoneValidation(true);
          }

          let indexOTPFailed = res.errors.findIndex(function (er: any) {
            return er.field == 'otp'
          })

          if (indexOTPFailed != -1) {
            if (protocol.value == 'phone') {
              phoneConfirmCodeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
            }
            if (protocol.value == 'email') {
              emailConfirmCodeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
            }
          }
          hightlightError();
          saving.value = false;
        }
        saving.value = false;
      })
      .catch((err) => {
        passwordValidation();
        if (protocol.value == 'email') {
          emailValidation(true);
        }
        else {
          phoneValidation(true);
        }

        if (err.errors.field == 'otp') {
          if (protocol.value == 'phone') {
            phoneConfirmCodeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
          }
          if (protocol.value == 'email') {
            emailConfirmCodeErr.value = t('ForgetPasswordComponent.ma_xac_thuc_sai');
          }
        }
        hightlightError();
        saving.value = false;
      });
  }
  else {
    // toast.error("Vui lòng kiểm tra lại thông tin");
    hightlightError();
    saving.value = false;
  }

}
function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    // Do stuff here
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}
function gotoStep(stepRef: number) {
  step.value = stepRef;
  switch (stepRef) {
    case 1:
      title.value = t('ForgetPasswordComponent.quen_mat_khau');
      break;
    case 2:
      title.value = "Xác nhận";
      break;
    case 3:
      title.value = "Tạo mật khẩu";
      break;
  }
  useSeoMeta({
    title: title.value,
  });
}

function passwordValidation() {
  let re = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{1,}$/;
  if (!re.test(password.value)) {
    passwordErr1.value = true;
  }
  else {
    passwordErr1.value = false;
  }
  if (password.value?.length < 6 || password.value?.length > 20) {
    passwordErr2.value = true;
  }
  else {
    passwordErr2.value = false;
  }

}
function confirmPasswordValidation() {
  if (!passwordConfirmation.value || !passwordConfirmation.value?.length) {
    passwordConfirmationErr.value = t('ForgetPasswordComponent.vui_long_nhap_mat_khau_xac_nhan');
  } else if (passwordConfirmation.value != password.value) {
    passwordConfirmationErr.value = t('ForgetPasswordComponent.nhap_lai_mat_khau_khong_khop');
  } else {
    passwordConfirmationErr.value = "";
  }
}
function close() {
  router.options.history.state.back
    ? router.back()
    : router.push(appRoute.HomeComponent);
}
</script>
