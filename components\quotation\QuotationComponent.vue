<template>
    <div class="public-container">
        <div class="quotation-container">
            <SubHeaderV2Component :title="$t('QuotationComponent.danh_sach_bao_gia_nguyen_vat_lieu')">
            </SubHeaderV2Component>
            <div class="quotation-actions" v-if="myShopData?.id">
                <v-btn id="check-quotation" color="var(--secondary-color-2)">
                    <nuxt-link :to="appRoute.CheckQuotationComponent">
                        {{ $t('QuotationComponent.kiem_bao_gia') }}
                    </nuxt-link>
                </v-btn>
                <v-btn id="filter-quotation" color="var(--secondary-color-1)" v-on:click="() => {
                    showMonthYearPickerModal = true
                }">
                    <span>{{ $t('QuotationComponent.loc_bao_gia') }}</span>
                    <em v-if="filterQuotationMonth || filterQuotationYear">
                        <span v-if="filterQuotationMonth">{{ moment().month(filterQuotationMonth).format("MM")
                            }}/</span>
                        <span>{{ filterQuotationYear }}</span>
                    </em>
                </v-btn>
                <v-btn id="request-quotation" color="var(--secondary-color-3)">
                    <nuxt-link :to="appRoute.RequestQuotationComponent">{{
                        $t('QuotationComponent.yeu_cau_bao_gia') }}</nuxt-link></v-btn>
                <v-btn id="import-quotation">
                    <nuxt-link :to="appRoute.ImportQuotationComponent">{{ $t('QuotationComponent.nhap_bao_gia')
                        }}</nuxt-link>
                </v-btn>
            </div>
            <div class="quotation-list-container flex-1" v-if="myShopData?.id">

                <v-list class="quotation-list-content" v-if="quotationList?.length > 0" variant="elevated">
                    <v-list-item class="quotation-item-container" v-for="itemQuotation in quotationList"
                        :key="itemQuotation.id">
                        <nuxt-link
                            :to="appRoute.DetailQuotationComponent.replaceAll(':quotation_id', itemQuotation.id ?? '')">
                            <div class="name-notes">
                                <v-chip class="supplier-name" v-if="itemQuotation.suppliers?.id">
                                    {{ itemQuotation.suppliers?.name }}
                                </v-chip>
                                <span class="name">
                                    {{ itemQuotation.name }}
                                </span>
                                <em class="notes">
                                    {{ itemQuotation.notes }}
                                </em>

                            </div>
                            <v-divider vertical inset></v-divider>
                            <span class="from-to">
                                {{ itemQuotation.from ? moment(itemQuotation.from, 'YYYY-MM-DD').format("DD/MM/YYYY") :
                                    '--' }}
                                -
                                {{ itemQuotation.to ? moment(itemQuotation.to, 'YYYY-MM-DD').format("DD/MM/YYYY") : '--'
                                }}
                            </span>
                            <v-divider vertical inset></v-divider>
                            <span class="status">
                                <v-chip
                                    :color="itemQuotation.status == quotation_status.saved ? 'green' : itemQuotation.status == quotation_status.responsed ? 'warning' : 'default'">
                                    {{ $t(`QuotationComponent.trang_thai_${itemQuotation.status}`) }}
                                </v-chip>
                            </span>
                        </nuxt-link>

                    </v-list-item>
                </v-list>

                <div class="none-list" v-else>
                    <img loading="lazy" :src="none_list_quotations"
                        :alt="$t('MyShopComponent.chua_dang_ky_cua_hang')" />

                    <span>
                        {{ $t('QuotationComponent.chua_tim_thay_bao_gia') }}
                    </span>
                </div>
            </div>
            <NoneMyShopComponent v-else :show_header="false" :title="$t('MyShopComponent.ban_chua_co_cua_hang')"
                class="flex-1"></NoneMyShopComponent>
        </div>
    </div>
    <MonthYearPickerComponent v-if="showMonthYearPickerModal" :nullable="true"
        :initialMonth="filterQuotationMonth ? filterQuotationMonth + 1 : null"
        :initialYear="filterQuotationYear ? filterQuotationYear : null" v-on:close="() => {
            showMonthYearPickerModal = false;
        }" v-on:submit="(e: any) => {
            console.log(e);
            showMonthYearPickerModal = false;
            filterQuotationMonth = e.year ? (e.month ? e.month - 1 : null) : null;
            filterQuotationYear = e.year;
            if (filterQuotationMonth && filterQuotationYear) {
                getListQuotation(
                    moment().year(filterQuotationYear).month(filterQuotationMonth).startOf('month').format('YYYY-MM-DD'),
                    moment().year(filterQuotationYear).month(filterQuotationMonth).endOf('month').format('YYYY-MM-DD')
                )
            }
            else if (filterQuotationYear && filterQuotationMonth == null) {
                getListQuotation(
                    moment().year(filterQuotationYear).startOf('year').format('YYYY-MM-DD'),
                    moment().year(filterQuotationYear).endOf('year').format('YYYY-MM-DD')
                )
            }
            else {
                getListQuotation()
            }

        }" />
</template>

<script lang="ts" setup>
import none_list_quotations from "~/assets/image/list-empty-2.jpg"
import { domainImage, appConst, nonAccentVietnamese, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { HttpStatusCode } from "axios";
import { quotation_status, type QuotationListItemDTO } from "./QuotationDTO";
import { QuotationService } from "~/services/quotationService/quotationService";
import { ShopService } from "~/services/shopService/shopService";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var imageService = new ImageService();
var quotationService = new QuotationService();
var shopService = new ShopService();

var myShopData = useState<any>('my_shop', () => { });
var isGettingQuotationList = ref<boolean>(false);
var quotationList = ref<QuotationListItemDTO[]>([]);
var filterQuotationMonth = useState<any>('filter_quotation_month', () => { return null })
var filterQuotationYear = useState<any>('filter_quotation_year', () => { return null })
var showMonthYearPickerModal = ref(false);
onMounted(async () => {
    await getMyShop();
    if (myShopData.value?.id) {
        getListQuotation();
    }
})

function getMyShop() {
    return new Promise((resolve) => {
        shopService.myShop().then(async res => {
            if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
                myShopData.value = res?.body?.data;
            }
            else {
                myShopData.value = null
            }
            resolve(myShopData.value)
        })
    })

}

function getListQuotation(from?: any, to?: any) {
    isGettingQuotationList.value = true;
    quotationService.list(myShopData.value?.id ?? '', from, to).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            quotationList.value = JSON.parse(JSON.stringify(res.body.data));
        }
        isGettingQuotationList.value = false;
    }).catch(err => {
        isGettingQuotationList.value = false;
    })
}
// function getListSupplier() {
//     this.supplierService.list().then(res => {
//         if (res.status == 200) {
//             this.setState({
//                 listSupplier: JSON.parse(JSON.stringify(res.body.data))
//             })
//         }
//     })
// }
// function getListMaterial() {
//     this.materialService.list().then(res => {
//         if (res.status == 200) {
//             this.setState({
//                 listMaterial: JSON.parse(JSON.stringify(res.body.data))
//             })
//         }
//     })
// }
// function addSupplier() {
//     this.setState({
//         isSaving: true,
//         newSupplier: {
//             ...this.state.newSupplier,
//             nameErr: (!this.state.newSupplier.name || !this.state.newSupplier.name.length) ? "Vui lòng nhập tên nhà cung cấp" : "",
//             addressErr: (!this.state.newSupplier.address || !this.state.newSupplier.address.length) ? "Vui lòng nhập địa chỉ" : "",
//             phoneErr: (!this.state.newSupplier.phone || !this.state.newSupplier.phone.length) ? "Vui lòng nhập sđt" : "",
//             emailErr: (!this.state.newSupplier.email || !this.state.newSupplier.email.length) ? "Vui lòng nhập email" : "",
//         }
//     });
//     this.supplierService.add(
//         this.state.newSupplier.name,
//         this.state.newSupplier.phone,
//         this.state.newSupplier.address,
//         this.state.newSupplier.email,
//         this.state.newSupplier.shop_id,
//     ).then(res => {
//         if (res.status == 200) {
//             this.getListSupplier();
//             this.setState({
//                 showAddSupplierModal: false
//             });
//         }
//         else {
//             this.toastService.error("Thêm không thành công", "Vui lòng kiểm tra lại")
//         }
//         this.setState({
//             isSaving: false
//         });
//     }).catch(err => {
//         this.setState({
//             isSaving: false
//         });
//         this.toastService.error("Thêm không thành công", "Vui lòng kiểm tra lại")
//     })
// }
// function selectFile(fileInput: any) {
//     if (fileInput.target.files) {
//         for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
//             let file = fileInput.target.files[i];
//             this.setState({
//                 currentFile: file
//             })
//         }
//     }
// }
// async function readFile() {
//     this.setState({
//         reading: true
//     });
//     this.quotationService.readFile(
//         this.state.currentFile,
//         this.state.nameRowNumber,
//         this.state.dataStartRow,
//         this.state.dataStartColumn,
//         this.state.dataEndColumn
//     ).then(res => {

//         if (res.status == 200) {
//             this.setState({
//                 dataView: JSON.parse(JSON.stringify(res.body.data)),
//                 sheetLists: Object.keys(JSON.parse(JSON.stringify(res.body.data)).sheet_lists).map((key) => {
//                     return {
//                         label: JSON.parse(JSON.stringify(res.body.data)).sheet_lists[key],
//                         value: key
//                     }
//                 }),
//                 sheetData: JSON.parse(JSON.stringify(res.body.data.sheet_data)),
//                 fileUrl: res.body.file,
//                 name: this.state.currentFile.name,
//             }, () => {
//                 this.setState({
//                     currentSheet: this.state.sheetLists[0].value,
//                     notes: this.state.sheetLists[0].label,
//                 }, () => {
//                     this.showData();
//                     this.setState({
//                         reading: false,
//                         showReadFileModal: false
//                     });
//                 })
//             })
//         }
//         else {
//             this.toastService.error("Đọc file thất bại", "Vui lòng thử lại sau");
//             this.setState({
//                 reading: false,
//                 showReadFileModal: false
//             });
//         }
//     }).catch(err => {
//         this.toastService.error("Đọc file thất bại", "Vui lòng thử lại sau");
//         this.setState({
//             reading: false,
//             showReadFileModal: false
//         });
//     });
// }
// function showData() {
//     this.setState({
//         currentSheetData: JSON.parse(JSON.stringify(this.state.dataView.sheet_data[this.state.currentSheet]))
//     }, () => {
//         this.setState({
//             currentSheetFields: JSON.parse(JSON.stringify(this.state.currentSheetData.column_names)),
//             currentSheetRowData: JSON.parse(JSON.stringify(this.state.currentSheetData.row_data)),
//             dataFieldStructure: JSON.parse(JSON.stringify(this.dataFieldStructureTemp))
//         }, () => {
//             this.setState({
//                 currentSheetFieldsToSelection:
//                     Object.keys(this.state.currentSheetFields)
//                         .filter(itemKey => {
//                             return this.state.currentSheetFields[itemKey] && this.state.currentSheetFields[itemKey].length
//                         })
//                         .map(key => {
//                             return {
//                                 label: this.state.currentSheetFields[key],
//                                 value: key
//                             }
//                         })
//             }, async () => {
//                 this.autoSelectField();
//             })

//         })
//     })
// }

// function autoSelectField() {
//     let structure = JSON.parse(JSON.stringify(this.state.dataFieldStructure));

//     Object.keys(this.state.currentSheetFieldsToSelection).map(async (key: string) => {
//         let fields = Object.keys(this.dataFieldStructureCompare).filter((item: any) => {
//             return this.dataFieldStructureCompare[item].includes(nonAccentVietnamese(this.state.currentSheetFieldsToSelection[key].label))
//         });
//         if (fields.length) {
//             structure[fields[0]] = this.state.currentSheetFieldsToSelection[key].label;
//             // await this.fillDataToField(key);
//         }
//     });
//     this.setState({
//         dataFieldStructure: JSON.parse(JSON.stringify(structure))
//     }, () => {
//         this.fillDataToTable();
//     });
// }
// function fillDataToTable() {
//     let rows = JSON.parse(JSON.stringify(this.state.currentSheetRowData));
//     Object.keys(this.state.dataFieldStructure).map(async key => {
//         rows.map((itemRow: any) => {
//             itemRow[key] = itemRow[this.state.dataFieldStructure[key]];
//         })
//     });
//     this.setState({
//         currentSheetRowData: rows
//     })
// }
// function fillDataToField = (field: string) => {
//     let rows = JSON.parse(JSON.stringify(this.state.currentSheetRowData));
//     rows.map((itemRow: any) => {
//         itemRow[field] = itemRow[this.state.dataFieldStructure[field]];
//     })
//     this.setState({
//         currentSheetRowData: rows
//     })
// }

// function copyToClipboard(text: any) {
//     navigator.clipboard.writeText(text.toString());
//     this.toastService.success("Đã sao chép vào bộ nhớ tạm");
// }
// function duplicateRow(index: number) {
//     let rows: any[] = JSON.parse(JSON.stringify(this.state.currentSheetRowData));

//     rows.splice(index + 1, 0, rows[index]);
//     this.setState({
//         currentSheetRowData: JSON.parse(JSON.stringify(rows))
//     })
// }
// function addRowData(index: number) {
//     let rows: any[] = JSON.parse(JSON.stringify(this.state.currentSheetRowData));
//     let newItem: any = {};
//     Object.keys(rows[0]).map((key) => {
//         newItem[key] = ""
//     })

//     rows.splice(index + 1, 0, newItem);
//     this.setState({
//         currentSheetRowData: JSON.parse(JSON.stringify(rows))
//     })
// }
// function deleteRowData(index: number) {
//     let rows = JSON.parse(JSON.stringify(this.state.currentSheetRowData));
//     rows.splice(index, 1);

//     this.setState({
//         currentSheetRowData: JSON.parse(JSON.stringify(rows))
//     })
// }
// function saveQuotation() {
//     let dataMaterialQuotations: any[] = [];
//     this.state.currentSheetRowData.map((item: any) => {
//         let newObj = JSON.parse(JSON.stringify(this.dataFieldStructureTemp));
//         Object.keys(newObj).map(key => {
//             newObj[key] = item[key] ? item[key] : "";
//         });
//         newObj.material_id = item.material_id ? item.material_id : null;
//         newObj.barcode = null;
//         dataMaterialQuotations.push(newObj);
//     })
//     // dataMaterialQuotations = JSON.parse(JSON.stringify(this.state.currentSheetRowData))

//     this.setState({
//         saving: true,
//         material_quotations: JSON.parse(JSON.stringify(dataMaterialQuotations))
//     }, () => {
//         this.quotationService.add(
//             this.state.supplier_id,
//             this.state.name,
//             this.state.from,
//             this.state.to,
//             this.state.notes,
//             this.state.fileUrl,
//             this.state.material_quotations,
//         ).then(res => {
//             if (res.status == 200) {
//                 this.toastService.success("Lưu báo giá thành công");
//                 this.getListMaterial();
//                 this.getListQuotation();
//                 this.getListSupplier();
//             }
//             else {
//                 this.toastService.error("Lưu báo giá thất bại", "Vui lòng thử lại");
//                 this.checkValidateQuotation();
//             }
//             this.setState({
//                 saving: false
//             })
//         }).catch(err => {
//             this.toastService.error("Lưu báo giá thất bại", "Vui lòng thử lại");
//             this.setState({
//                 saving: false
//             })
//         })
//     })
// }
// function getQuotationDetail(quotationId: any) {
//     this.setState({
//         isGettingQuotationList: true
//     })
//     this.quotationService.detail(quotationId).then(async res => {

//         if (res.status == 200) {
//             // let structure = JSON.parse(JSON.stringify(this.dataFieldStructureTemp));
//             // await Object.keys(structure).map(key => {
//             //     structure[key] = key;
//             // });
//             this.setState({
//                 // currentSheetRowData: JSON.parse(JSON.stringify(res.body.data.material_quotations)),
//                 // dataFieldStructure: structure,
//                 currentQuotation: res.body.data,
//                 showQuotationDetail: true,
//                 isGettingQuotationList: false
//                 // currentFile: null,
//                 // dataView: JSON.parse(JSON.stringify(res.body.data)),
//                 // sheetLists: [],
//                 // sheetData: [],
//                 // currentSheet: null,
//                 // fileUrl: res.body.data.file,
//                 // name: res.body.data.name,
//                 // currentSheetData: null,
//                 // from: res.body.data.from,
//                 // to: res.body.data.to,
//                 // notes: res.body.data.notes,
//                 // supplier_id: res.body.data.supplier_id,

//             }, () => {
//                 // this.setState({
//                 //     currentSheetFieldsToSelection: Object.keys(
//                 //         this.state.currentSheetFields.filter(
//                 //             (e: any, i: any) => { return this.state.currentSheetFields[i] && this.state.currentSheetFields[i].length })
//                 //     ).map(key => {
//                 //         return {
//                 //             label: this.state.currentSheetFields[key],
//                 //             value: key
//                 //         }
//                 //     })
//                 // })
//                 // document.getElementById("quotation_view")?.scrollIntoView({ behavior: 'smooth' });
//             })
//         }
//     })
// }

// function checkValidateQuotation() {
//     this.setState({
//         nameErr: (this.state.name && this.state.name.length) ? false : true,
//         fromErr: (this.state.from && this.state.from.length) ? false : true,
//         toErr: (this.state.to && this.state.to.length) ? false : true,
//         supplierErr: (this.state.supplier_id && this.state.supplier_id.length) ? false : true,
//     })
// }

// function getSupplierName(id: string) {
//     let index = this.state.listSupplier.findIndex(function (el: any) {
//         return el.id == id
//     });
//     return index != -1 ? this.state.listSupplier[index].name : ''
// }
</script>

<style lang="scss" src="./QuotationStyles.scss"></style>
