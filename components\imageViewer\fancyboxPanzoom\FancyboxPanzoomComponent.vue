<template>
    <a class="f-panzoom" :id="`image_content_${props.imageId}`">
        <img :src="props.imageUrl" class="fancybox-image" alt="Sample Image" v-on:click="(e) => {
            e.stopPropagation()
        }"/>
    </a>
</template>

<script setup lang="ts">
// import { Panzoom } from "@fancyapps/ui";
import { Toolbar } from "@fancyapps/ui/dist/panzoom/panzoom.toolbar.esm";

const nuxtApp = useNuxtApp();
const props = defineProps({
    imageUrl: null,
    imageId: null,
})
const emit = defineEmits(['close', 'zooming'])

onMounted(() => {
    new nuxtApp.$panzoom(document.getElementById(`image_content_${props.imageId}`), {
        // bounds: true,
        // boundsPadding: 0.1,
        // maxScale: 5,
        minScale: .6,
        panOnlyZoomed: true,
        pinchToZoom: true,
        click: false,
        bounds: 'auto',
        Toolbar: {
            display: [
                "rotateCCW",
                "rotateCW",
                "flipX",
                "flipY",
                "reset",
            ],
        },
    }, { Toolbar });
});
</script>

<style lang="scss" src="./FancyboxPanzoomStyles.scss"></style>