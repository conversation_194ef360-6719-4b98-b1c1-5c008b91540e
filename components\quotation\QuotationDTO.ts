import type { _100 } from "#tailwind-config/theme/backdropBrightness";

export interface MaterialQuotationDTO {
    name_en: string,
    name: string,
    brand: string,
    origin: string,
    packing: string,
    unit: string,
    price: number,
    price_vat: number,
    notes: string,
}

export interface QuotationListItemDTO {
    id?: string;
    link?: string;
    name: string;
    notes: string;
    file?: string;
    from?: string;
    to: string;
    shop_id: string;
    status: quotation_status;
    supplier_id: string;
    suppliers?: any;
    created_at?: string;
    updated_at?: string;
}

export interface SupplierDto {
    id: string;
    name: string;
    phone?: string;
    address?: string;
    email?: string;
    shop_id?: string | null;
}

export interface MaterioDto{
    id: string;
    barcode?: string | null;
    brand?: string | null;
    origin?: string | null;
    name: string;
    name_en?: string | null;
    packing?: string | null;
    shop_id?: string | null;
    unit?: string | null;
    notes?: string | null
}

export enum quotation_status {
    pending = 1,
    responsed = 2,
    saved = 3
}

export interface IDataFieldStructor {
    stt: {
        label_key: null,
        value: null,
        compare_arr: []
    },
    name_en: {
        label_key: null,
        value: null,
        compare_arr: []
    },
    name: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    brand: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    origin: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    packing: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    unit: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    price: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    price_vat: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
    notes: {
        label_key: null,
        value: null,
        compare_arr: [],
    },
}

export class DataFieldStructor implements IDataFieldStructor {
    stt: any = {
        label_key: 'stt',
        value: null, 
        compare_arr: ["stt", "so thu tu", "sttno."],
        width: 100,
    };
    material_id: any = {
        label_key: 'nguyen_lieu_tuong_ung',
        value: null, 
        compare_arr: [] ,
        width: 250,
    };
    name_en:any = { 
        label_key: 'ten_tieng_anh',
        value: null, 
        compare_arr: ["product name en", "ten mat hang (tieng anh)", "ten nguyen vat lieu (tieng anh)", "tieng anh", "english", "name_en"],
        width: 250,
    };
    name:any = { 
        label_key: 'ten',
        value: null, 
        compare_arr: ["product name vn", "ten mat hang", "ten nguyen vat lieu", "ten san pham", "product_name", "product name", "name"],
        width: 250,
    };
    brand:any = { 
        label_key: 'thuong_hieu',
        value: null,
        compare_arr: ["thuong hieu", "nhan hang", "brand"],
        width: 200,
    };
    origin:any = { 
        label_key: 'xuat_xu',
        value: null, 
        compare_arr: ["xuat xu", "nguon goc", "origin"],
        width: 200,
    };
    packing:any = { 
        label_key: 'dong_goi',
        value: null, 
        compare_arr: ["dong goi", "packing"],
        width: 200,
    };
    unit:any = { 
        label_key: 'don_vi',
        value: null, 
        compare_arr: ["don vi", "dvt", "unit"],
        width: 200,
    };
    price:any = { 
        label_key: 'gia',
        value: null, 
        compare_arr: ["gia", "don gia (-vat)", "gia (-vat)", "gia goc", "price"],
        width: 200,
    };
    price_vat:any = { 
        label_key: 'gia_vat',
        value: null, 
        compare_arr: ["gia", "don gia (+vat)", "gia (+vat)", "gia co thue", "price_vat"],
        width: 200,
    };
    notes:any = { 
        label_key: 'mo_ta',
        value: null, 
        compare_arr: ["product description", "mo ta", "description", "ghi chu", "notes"],
        width: 200,
    };

    constructor(){}
}