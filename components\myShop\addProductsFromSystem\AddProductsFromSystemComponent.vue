<template>
  <div class="public-container">
    <div class="add-products-from-system-container">
      <!-- <div class="title-header">
      <div class="header-left"></div>
      <h3>{{ appRouteTitle.AddProductsFromSystemComponent }}</h3>
      <div class="header-right"></div>
    </div> -->
      <div class="add-from-system-sticky-header fit">
        <SubHeaderV2Component :title="$t('AppRouteTitle.AddProductsFromSystemComponent')">
          <!-- <template v-slot:header_left>
				
			</template> -->
        </SubHeaderV2Component>
        <div class="h-stack search-bar-container">
          <button class="search-button" :disabled="searchProductLoading" v-on:click="getSearchProductResult()">
            <Icon name="ion:search" size="20" v-show="!searchProductLoading" />
            <Icon name="eos-icons:loading" size="20" v-show="searchProductLoading" />
          </button>

          <input type="search" class="search-input-container" :value="search_text"
            :maxlength="appConst.max_text_short"
            :placeholder="$t('AddProductsFromSystemComponent.tim_san_pham')" v-on:input="async ($event: any) => {
              search_text = $event.target.value;
              search = $event.target.value;
              getSearchProductResult();
            }" v-on:focus="() => {
            searchFocus = true;
          }" />
        </div>
      </div>

      <div class="selected-products-container" :class="{ 'show-search': searchFocus }">
        <div class="search-result" :class="{ 'show': searchFocus }" id="search-result" v-on:scroll="(e: any) => {
          listSuggestScroll(e);
        }">
          <img loading="lazy" class="none-search-result" :src="none_result" :placeholder="none_result"
            v-if="!listProductSuggest || !listProductSuggest.length"
            :alt="$t('AddProductsFromSystemComponent.khong_tim_thay')" />

          <div v-if="listProductSuggest && listProductSuggest.length" v-for="(itemSuggest, index) of listProductSuggest"
            class="search-result-item-container" v-on:click="() => {
              itemSuggest.selected = !itemSuggest.selected;
              let arrSuggest = listProductSuggest;
              arrSuggest[index].selected = itemSuggest.selected;

              let arrSelected = listProductSelected;
              let indexSelected = arrSelected.findIndex(function (e: any) {
                return e.id == itemSuggest.id;
              });
              if (indexSelected != -1 && !itemSuggest.selected) {
                arrSelected.splice(indexSelected, 1);
              } else if (indexSelected == -1 && itemSuggest.selected) {
                itemSuggest.system_price = itemSuggest.price;
                arrSelected.push(itemSuggest);
              }

              listProductSuggest = arrSuggest;
              listProductSelected = arrSelected;
            }">
            <img loading="lazy" :src="itemSuggest && itemSuggest.profile_picture && itemSuggest.profile_picture.length
              ? domainImage + itemSuggest.profile_picture
              : icon_for_product" :placeholder="icon_for_product"
              :alt="$t('AddProductsFromSystemComponent.anh_dai_dien')" />
            <div class="v-stack">
              <span>{{ showTranslateProductName(itemSuggest) }}</span>
              <span class="price">{{ formatCurrency(itemSuggest.price) }}</span>
            </div>

            <Icon name="mdi:checkbox-marked-outline" class="my-checkbox" v-if="itemSuggest.selected"></Icon>
            <Icon name="mdi:checkbox-blank-outline" class="my-checkbox" v-else></Icon>
          </div>
          <div class="loading-more" v-if="searchProductLoadingMore">
            {{ $t('AddProductsFromSystemComponent.loading') }}
          </div>
          <div id="last_of_list_suggest"></div>

          <div class="search-footer">
            <span>{{ $t('AddProductsFromSystemComponent.da_chon') }}: {{
              listProductSuggest.filter((e: any) => {
                return e.selected && !e.existing;
              }).length
            }}</span>
            <div class="select-all" v-if="listProductSuggest && listProductSuggest.length" v-on:click="() => {
              let arrSuggest = listProductSuggest;
              let arrSelected = listProductSelected;
              arrSuggest.map((el) => {
                el.selected = true;
                let indexSelected = arrSelected.findIndex(function (e: any) {
                  return e.id == el.id;
                });
                if (indexSelected == -1) {
                  el.system_price = el.price;
                  arrSelected.push(el);
                }
              });

              listProductSuggest = arrSuggest;
              listProductSelected = arrSelected;
            }">
              <Icon name="mdi:checkbox-marked-outline" class="my-checkbox" v-if="checkSelectAll()"></Icon>
              <Icon name="mdi:checkbox-blank-outline" class="my-checkbox" v-else></Icon>
              <span>{{ $t('AddProductsFromSystemComponent.chon_het') }}</span>
            </div>
            <button v-on:click="() => {
              let arr = listProductSuggest;
              let arrSelect = listProductSelected;
              arr.forEach((item: any) => {
                if (!item.existing && item.selected) {
                  item.selected = false;
                  let indexSelect = arrSelect.findIndex(function (e: any) {
                    return e.id == item.id;
                  });
                  if (indexSelect != -1) {
                    arrSelect.splice(indexSelect, 1);
                  }
                }
              });
              listProductSuggest = arr;
              listProductSelected = arrSelect;
            }" class="unselect-all-button"
              v-if="listProductSuggest.filter((e: any) => { return e.selected && !e.existing }).length">
              {{ $t('AddProductsFromSystemComponent.bo_chon') }}
            </button>

            <Button v-on:click="() => {
              searchFocus = false;
            }" class="accept-button">
              {{ $t('AddProductsFromSystemComponent.xong') }}
            </Button>
          </div>
        </div>
        <div class="selected-products" :class="{ 'show': !searchFocus }">
          <div v-if="!listProductSelected || !listProductSelected.length" class="none-selected">
            <img loading="lazy" :src="list_empty" :placeholder="list_empty"
              :alt="$t('AddProductsFromSystemComponent.chua_chon')" />
            <span>{{ $t('AddProductsFromSystemComponent.chua_chon') }}</span>
          </div>
          <div class="list-product-selected" v-if="listProductSelected && listProductSelected.length">
            <div class="h-stack item-product-seleted" :class="indexSelected % 2 ? 'odd' : ''"
              v-for="(itemSelected, indexSelected) in listProductSelected">
              <img loading="lazy" :src="itemSelected && itemSelected.profile_picture
                ? domainImage + itemSelected.profile_picture
                : icon_for_product" :placeholder="icon_for_product" />
              <div class="v-stack">
                <span class="product-name">
                  {{ showTranslateProductName(itemSelected) }}
                </span>
                <span class="product-price">
                  {{ formatCurrency(itemSelected.system_price || 0) }}
                </span>
                <div class="v-stack product-content-edit">
                  <span class="label">{{ $t('AddProductsFromSystemComponent.gia_muon_ban') }}</span>
                  <input type="text" inputmode="numeric" :value="itemSelected.price" class="input-custom price-input" max="1000000000000" v-on:input="($event: any) => {
                    
                    $event.target.value = parseDecimal2Digits($event.target?.value) ?? null; 
                    itemSelected.price = parseDecimal2Digits($event.target?.value) ?? null;
                  }" />
                  <span class="price-text" v-if="itemSelected.price">{{ formatCurrency(itemSelected.price,
                    shopData.currency)
                    }}</span>
                </div>
                <div class="v-stack product-content-edit">
                  <span class="label">{{ $t('AddProductsFromSystemComponent.ton_kho') }}</span>
                  <input type="number" inputmode="numeric" :value="itemSelected.stock"
                    max="1000000000000"
                    :placeholder="$t('AddProductsFromSystemComponent.de_trong_khi_khong_quan_ly_ton_kho')"
                    class="input-custom price-input" v-on:input="($event: any) => {
                      // itemSelected.stock = parseFloat($event.target?.value) || null;
                      $event.target.value = parseFloat($event.target?.value) ?? null; 
                      itemSelected.stock = parseFloat($event.target?.value) ?? null;
                    }" />
                  <span class="price-text notice" v-if="itemSelected.stock != null && !isNaN(itemSelected.stock)">{{
                    formatNumber(itemSelected.stock)
                    }}</span>
                  <span class="price-text notice" v-else>{{ $t('AddProductsFromSystemComponent.khong_quan_ly_ton_kho')
                    }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="selected-footer">
          <button class="reject-button" v-on:click="() => {
            close();
          }">
            {{ $t('AddProductsFromSystemComponent.thoat') }}
          </button>

          <button class="accept-button" :disabled="!listProductSelected || !listProductSelected.length || isSaving"
            v-on:click="() => {
              addProducts();
            }">
            {{ $t('AddProductsFromSystemComponent.luu') }}
          </button>
        </div>
      </div>

    </div>
  </div>

</template>

<script lang="ts" setup>

import icon_for_product from "~/assets/image/icon-for-product.png";
import none_result from "~/assets/image/none-result-2.webp";
import list_empty from "~/assets/image/list-empty-2.jpg";

import { toast } from "vue3-toastify";
import { appConst, domainImage, formatCurrency, formatNumber, parseDecimal2Digits, showTranslateProductName } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import { AgentService } from "~/services/agentService/agentService";
import { HttpStatusCode } from "axios";

const { t } = useI18n();
var nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();
var props = defineProps({
  shopData: {},
  listProductExisting: [],
  mode: null
});
var emit = defineEmits(["close"]);

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var agentService = new AgentService();
var searchProductTimeout: any;

var search = ref("");
var search_text = ref("");
var shop_id = ref(route.params.id ? route.params.id : null);
var shopData = ref(
  props.shopData ? JSON.parse(JSON.stringify(props.shopData)) : null
);
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref();
var searchFocus = ref(true);
var listProductSuggest = ref([] as any[]);
var listProductSuggestCount = ref();
var searchSuggestAll = ref();
var listProductSelected = ref([] as any[]);
var listProductExisting = ref(
  props.listProductExisting
    ? JSON.parse(JSON.stringify(props.listProductExisting))
    : []
);
var mode = ref(props.mode ? props.mode : null);
var isSaving = ref(false);
onMounted(async () => {
  shopData.value = router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)) : null;
  if (mode.value != 'agent') {
    if (!shopData?.value?.id) {
      await getMyShop();
    }
  }
  else {
    if (shop_id.value) {
      await getShopDetailByAgent()
    }
  }

  getSearchProductResult()
})
function getMyShop() {
  shopService.myShop().then(res => {
    if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {

      shopData.value = res.body.data;
    }
    else {

      shopData.value = null;
      toast.error(t('AddProductsFromSystemComponent.chua_dang_ky_cua_hang'));
      setTimeout(() => {
        close()
      }, 1000);
    }
    return
  }).catch((err) => {
    toast.error(t('AddProductsFromSystemComponent.chua_dang_ky_cua_hang'));
    setTimeout(() => {
      close()
    }, 1000);
  })
}
function getShopDetailByAgent() {
  agentService.agentShopDetail(route.params.id as any).then(res => {
    if (res.status && res.status == HttpStatusCode.Ok) {

      shopData.value = res.body.data;
    }
    else {

      shopData.value = null;
      toast.error(t('AddProductsFromSystemComponent.chua_dang_ky_cua_hang'));
      setTimeout(() => {
        close()
      }, 1000);
    }
    return
  }).catch((err) => {
    toast.error(t('AddProductsFromSystemComponent.chua_dang_ky_cua_hang'));
    setTimeout(() => {
      close()
    }, 1000);
  })
}
function getSearchProductResult() {
  searchProductLoading.value = true;
  clearTimeout(searchProductTimeout);
  searchProductTimeout = setTimeout(() => {
    productService
      .searchBase(search.value ? search.value : "")
      .then(async (res) => {
        if (res.status == HttpStatusCode.Ok) {
          let listSuggest: any[] = res.body.data.result || res.body.data;
          let listExisting: any[] = [];
          let listSelected: any[] = [];
          listSuggest.forEach((item: any) => {
            item.existing = false;
            item.selected = false;
            let indexSelect = listProductSelected.value.findIndex(function (
              e: any
            ) {
              return e.id == item.id;
            });
            let indexExisting = listProductExisting.value.findIndex(function (
              e: any
            ) {
              return e.parent_id == item.id;
            });
            if (indexExisting != -1) {
              item.existing = true;
              item.selected = true;

              listExisting.push(item);
            } else if (indexSelect != -1) {
              item.selected = true;

              listSelected.push(item);
            }
          });

          listSuggest = listSuggest.filter((e: any) => {
            return e.selected == false;
          });
          listSuggest = [...listExisting, ...listSelected, ...listSuggest];
          listProductSuggest.value = listSuggest;
          listProductSuggestCount.value = res.body.data.count;
          searchProductLoading.value = false;
          document.getElementById("search-result")?.scrollTo({ top: 0 });
          searchSuggestAll.value = false
        }
      })
      .catch(() => {
        searchProductLoading.value = false;
      });
  }, 1000);
}

function loadmoreSearchProductResult() {
  if (
    listProductSuggest.value.length < listProductSuggestCount.value &&
    !searchSuggestAll.value
  ) {
    searchProductLoadingMore.value = true;
    clearTimeout(searchProductTimeout);
    searchProductTimeout = setTimeout(() => {
      productService
        .searchBase(search.value, listProductSuggest.value.length, 25)
        .then(async (res) => {
          if (res.status == HttpStatusCode.Ok) {
            let listSuggest: any[] = res.body.data.result;
            if (listSuggest.length > 0) {
              let listExisting: any[] = [];
              let listSelected: any[] = [];
              listSuggest.forEach((item: any) => {
                item.existing = false;
                item.selected = false;
                let indexSelect = listProductSelected.value.findIndex(function (
                  e: any
                ) {
                  return e.id == item.id;
                });
                let indexExisting = listProductExisting.value.findIndex(
                  function (e: any) {
                    return e.parent_id == item.id;
                  }
                );
                if (indexExisting != -1) {
                  item.existing = true;
                  item.selected = true;

                  // listExisting.push(item);
                } else if (indexSelect != -1) {
                  item.selected = true;

                  // listSelected.push(item);
                }
              });

              // listSuggest = listSuggest.filter((e: any) => { return e.selected == false });
              // listSuggest = [...listExisting, ...listSelected, ...listSuggest];
              listProductSuggest.value = [
                ...listProductSuggest.value,
                ...listSuggest,
              ];
            }
            else {
              searchSuggestAll.value = true;
            }
            searchProductLoadingMore.value = false;
          }
        })
        .catch(() => {
          searchProductLoadingMore.value = false;
        });
    }, 500);
  }
}

function addProducts() {
  isSaving.value = true;
  let body = {
    shop_id: shopData.value.id,
    list_product: JSON.parse(JSON.stringify(listProductSelected.value)),
  };
  if (mode.value == 'agent') {
    return agentService
      .addProductFromSystem(body)
      .then(
        (res) => {
          if (res.status == HttpStatusCode.Ok) {
            close(true);
          } else if (res.status == 400) {
            toast.error(t('AddProductsFromSystemComponent.ban_khong_phai_chu_shop'));
          } else {
            toast.error(t('AddProductsFromSystemComponent.co_loi_xay_ra'));
          }
          // isSaving.value = false;
        },
        (err) => {
          toast.error(t('AddProductsFromSystemComponent.co_loi_xay_ra'));
          console.error(err);
          isSaving.value = false;
        }
      )
      .catch((err) => {
        console.error(err);
      });
  }
  return productService
    .addProductFromSystem(body)
    .then(
      (res) => {
        if (res.status == HttpStatusCode.Ok) {
          close(true);
        } else if (res.status == 400) {
          toast.error(t('AddProductsFromSystemComponent.ban_khong_phai_chu_shop'));
        } else {
          toast.error(t('AddProductsFromSystemComponent.co_loi_xay_ra'));
        }
        isSaving.value = false;
      },
      (err) => {
        toast.error(t('AddProductsFromSystemComponent.co_loi_xay_ra'));
        console.error(err);
        isSaving.value = false;
      }
    )
    .catch((err) => {
      console.error(err);
      isSaving.value = false;
    });
}
function checkSelectAll() {
  var all = true;
  listProductSuggest.value.filter((el) => {
    let indexSelected = listProductSelected.value.findIndex(function (e) {
      return e.id == el.id
    })
    if (indexSelected == -1) all = false;
  })
  return all;
}
function listSuggestScroll(event: any) {
  let el = document
    .getElementById("last_of_list_suggest")
    ?.getBoundingClientRect().bottom;
  if (el && el <= window.innerHeight + 300) {
    if (listProductSuggest.value.length < listProductSuggestCount.value) {
      loadmoreSearchProductResult();
    }
  }
}

function close(updated = false) {
  // emit("close", e);
  router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
  // if (updated == true) {
  //   nuxtApp.$emit('refresh_product_manage', true)
  // }
}
</script>

<style lang="scss" src="./AddProductsFromSystemStyles.scss"></style>
