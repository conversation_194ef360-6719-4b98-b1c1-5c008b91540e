<template>
    <div class="public-container">
        <div class="create-delivery-container">
            <client-only hidden>
                <LMap id="temp_leaflet_map" v-on:ready="(e: any) => {
                    tempLeafletMap = e;
                }"></LMap>
            </client-only>
            <SubHeaderV2Component :title="$t('AppRouteTitle.CreateDeliveryComponent')">
                <template v-slot:header_left></template>
                <template v-slot:header_right>
                    <button class="close" v-on:click="() => {
                        close()
                    }">
                        <Icon name="clarity:times-line" size="25"></Icon>
                    </button>
                </template>
            </SubHeaderV2Component>
            <div class="create-delivery-content-container">

                <!-- place info -->
                <div class="stack-content">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.lo_trinh') }}&nbsp;<em v-if="distance">{{ distance }} km</em>
                        </span>
                    </div>
                    <div class="stack-item-content selectable" v-on:click="() => {
                        if (props.mode == 'update_order') {
                            showSenderSelectModal = true;
                        }
                    }">
                        <Icon name="line-md:my-location-loop" class="icon-left from-marker"></Icon>
                        <div class="stack-label" v-if="address_from?.length || name_from?.length || phone_from?.length">
                            <span>{{ name_from }} <span v-if="phone_from?.length">- {{ phone_from }}</span></span>
                            <em>{{ address_from }}</em>
                        </div>
                        <div class="stack-label" v-else>
                            <span>{{ $t('CreateDeliveryComponent.lay_hang_tai') }}</span>
                        </div>
                        <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button>
                    </div>
                    <div class="stack-item-content">
                        <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon>
                    </div>
                    <div class="stack-item-content selectable" v-on:click="() => {
                        if (props.mode == 'update_order') {
                            showReceiverSelectModal = true;
                        }
                    }">
                        <Icon name="line-md:map-marker" class="icon-left to-marker"></Icon>
                        <div class="stack-label" v-if="address_to?.length || name_to?.length || phone_to?.length">
                            <span>{{ name_to }} <span v-if="phone_to?.length">- {{ phone_to }}</span></span>
                            <em>{{ address_to }}</em>
                        </div>
                        <div class="stack-label" v-else>
                            <span>{{ $t('CreateDeliveryComponent.giao_den') }}</span>
                        </div>

                        <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button>
                    </div>
                </div>

                <!-- time info -->
                <div class="stack-content">
                    <div class="stack-item-content selectable" v-on:click="() => {
                        showServiceTypeModal = true
                    }">
                        <Icon v-if="!service_type_checking" :name="service_type?.icon ?? 'noto:rocket'"
                            class="icon-left"></Icon>
                        <Icon v-if="service_type_checking" name="eos-icons:loading" class="icon-left"></Icon>
                        <div class="stack-label" v-if="service_type_checking">
                            <span>{{ $t('CreateDeliveryComponent.loai_giao_hang') }}</span>
                            <em>{{ $t('CreateDeliveryComponent.dang_kiem_tra') }}</em>
                        </div>
                        <div class="stack-label" v-if="!service_type && !service_type_checking">
                            <span>{{ $t('CreateDeliveryComponent.loai_giao_hang') }}</span>
                            <em>{{ $t('CreateDeliveryComponent.mo_ta_loai_giao_hang') }}</em>
                        </div>
                        <!-- <div class="stack-label"
                            v-if="service_type && !service_type_checking && delivery_partner?.name?.toLowerCase().includes('remagan')">
                            <span>{{ $t(`CreateDeliveryComponent.${service_type.key}`) }}</span>
                            <em>{{ $t(`CreateDeliveryComponent.${service_type.description}`) }}</em>
                        </div> -->
                        <div class="stack-label" v-if="service_type && !service_type_checking">
                            <span>{{ service_type.name }}</span>
                            <em v-if="!service_type.error">{{ service_type.price != null ?
                                formatCurrency(service_type.price) :
                                $t('CreateDeliveryComponent.chua_xac_dinh_phi_ship') }}</em>
                            <em v-else class="error">{{ $t('CreateDeliveryComponent.khong_ho_tro') }}</em>
                            <!-- <em v-else class="error">{{ service_type.error?.message?.[locale] ?? service_type.error?.message?.vi }}</em> -->
                        </div>
                        <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button>
                    </div>
                    <div class="mid-line"></div>
                    <div class="stack-item-content selectable" v-on:click="() => {
                        showDateTimePickerModal = true
                    }">
                        <Icon name="ic:baseline-access-time" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.thoi_gian_lay_hang') }}</span>
                            <em>{{ pickup_time ? pickup_time : $t('CreateDeliveryComponent.bay_gio') }}</em>
                        </div>
                        <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button>
                    </div>


                </div>

                <!-- Delivery partner selection -->
                <div class="stack-content driver" v-if="listPartners?.length">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.don_vi_van_chuyen') }}
                        </span>
                        <button class="content-detail" v-on:click="() => {
                            showSelectDeliveryPartner = true
                        }">
                            <span>{{ $t('CreateDeliveryComponent.thay_doi') }}</span>
                        </button>
                    </div>
                    <div class="v-stack stack-item-content delivery-info" v-if="delivery_partner">
                        <img class="partner-logo" :src="delivery_partner?.information?.logo"
                            v-if="delivery_partner?.information?.logo" :alt="`logo ${delivery_partner?.name}`">
                        <span class="partner-name" v-else>
                            {{ delivery_partner?.name }}
                        </span>

                        <div class="driver-info" v-if="delivery_partner?.name?.toLowerCase().includes('remagan')">
                            <div class="h-stack driver-content" v-if="driver">
                                <img loading="lazy" class='user-avatar' :src="driver?.profile_picture
                                    ? ((appConst.provider_img_domain.some(e => driver?.profile_picture?.includes(e))) ? driver?.profile_picture : (domainImage + driver?.profile_picture))
                                    : non_avatar" :placeholder="non_avatar" alt="" />
                                <div class="user-detail">
                                    <div class="h-stack">
                                        <div class="user-name">
                                            <span>{{ driver?.name }}</span>
                                        </div>
                                        <nuxt-link :to="driver.phone?.length ? `tel:${validPhone(driver?.phone)}` : '#'"
                                            :target="webInApp ? '_blank' : ''" class="user-phone"
                                            :class="{
                                                'disabled': !driver.phone?.length
                                            }"
                                        >
                                            <Icon name="material-symbols:call"></Icon>
                                            <span>{{ driver.phone?.length ? driver?.phone : $t('CreateDeliveryComponent.chua_cap_sdt') }}</span>
                                        </nuxt-link>
                                    </div>

                                    <button class="select-other-driver" v-if="driver" v-on:click="() => {
                                        showFindShipper = true
                                    }">
                                        <span>{{ $t('CreateDeliveryComponent.chon_shipper_khac') }}</span>
                                    </button>
                                </div>

                            </div>
                            <div class="h-stack driver-content" v-else>
                                <img loading="lazy" class='user-avatar' :src="non_avatar" :placeholder="non_avatar"
                                    alt="" />
                                <div class="user-detail">
                                    <div class="user-name">
                                        <span>{{ $t('CreateDeliveryComponent.chua_chon_shipper') }}</span>
                                    </div>
                                    <button class="content-detail" v-on:click="() => {
                                        showFindShipper = true
                                    }">
                                        <span>{{ $t('CreateDeliveryComponent.chon_ngay') }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="mid-line"></div> -->
                    <div class="stack-item-content pending-time-content"
                        v-if="delivery_partner?.name?.toLowerCase().includes('remagan')">
                        <Icon name="mdi:box-time-outline" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.thoi_gian_cho_tai_xe_nhan_don') }}</span>
                            <div class="h-stack input-group-container">
                                <Field :placeholder="$t('CreateDeliveryComponent.mac_dinh_30_phut')"
                                    class="custom-input" :validate-on-input="true" name="pending_time_value"
                                    type="number" id="pending_time_value_input" v-bind:model-value="pending_time?.value || 30"
                                    v-on:update:model-value="($event: any) => {
                                        pending_time.value = $event;
                                    }"></Field>
                                <v-select class="custom-v-select mt-2 time-unit-select" label="" :menu-props="{
                                    contentClass: 'text-center'
                                }" :placeholder="$t('CreateDeliveryComponent.don_vi_thoi_gian')"
                                    v-model="pending_time.unit" :item-value="'value'" :menu-icon="''"
                                    :items="appConst.time_units" variant="plain">
                                    <template v-slot:item="{ props, item }">
                                        <v-list-item v-bind="props"
                                            :title="$t(`TimeUnit.${item.raw.key}`)"></v-list-item>
                                    </template>
                                    <template v-slot:selection="{ item }">
                                        <span>{{ $t(`TimeUnit.${item.raw.key}`) }}</span>
                                    </template>
                                    <template v-slot:append-inner></template>
                                </v-select>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- package info -->
                <div class="stack-content">
                    <div class="stack-item-content selectable" v-if="false" v-on:click="() => {
                        showPackageInfoModal = true;
                    }">
                        <Icon name="ph:package-duotone" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.thong_tin_goi_hang') }}</span>
                            <em v-if="package_info">
                                <span v-if="package_info.product_type?.length">{{ package_info.product_type }}</span>
                                <span v-if="package_info.weight?.length">{{
                                    $t(`CreateDeliveryComponent.${package_info.weight}`)
                                    }}</span>
                                <span v-if="package_info.size?.length">{{ package_info.size }}</span>
                            </em>
                            <em v-else>{{ $t('CreateDeliveryComponent.mo_ta_thong_tin_goi_hang') }}</em>
                        </div>
                        <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button>
                    </div>
                    <div class="mid-line" v-if=false></div>
                    <div class="stack-item-content selectable note-content">
                        <Icon name="hugeicons:cash-02" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.cod') }}</span>
                            <input class="price-input" :placeholder="$t('CreateDeliveryComponent.cod_placeholder')"
                                :maxlength="appConst.max_text_short" v-on:input="($event: any) => {
                                    cod_price = $event.target?.value?.length ? parseFloat($event.target?.value) : null;
                                }" v-model="cod_price"></input>
                            <span class="price-text" v-if="cod_price">{{ formatCurrency(cod_price) }}</span>

                        </div>
                        <!-- <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button> -->
                    </div>
                    <div class="mid-line"></div>
                    <div class="stack-item-content selectable note-content">
                        <Icon name="mdi:account-cash-outline" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.nguoi_tra_phi') }}</span>
                            <div class="h-stack stack-content">
                                <div class="content-item" v-for="item in delivery_payment_method_enum"
                                    :class="{ 'active': delivery_payment_method == delivery_payment_method_enum[item] }"
                                    v-on:click="() => {
                                        // deliveryType = false;
                                        // checkDeliveryPrice();
                                        delivery_payment_method = delivery_payment_method_enum[item];
                                    }">
                                    <Icon name="ion:radio-button-on"
                                        v-if="delivery_payment_method == delivery_payment_method_enum[item]"></Icon>
                                    <Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
                                    <span>{{ $t(`CreateDeliveryComponent.nguoi_tra_${item}`) }}</span>
                                </div>
                            </div>

                        </div>
                        <!-- <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button> -->
                    </div>
                    <div class="mid-line"></div>
                    <div class="stack-item-content selectable note-content">
                        <Icon name="ph:note" class="icon-left"></Icon>
                        <div class="stack-label">
                            <span>{{ $t('CreateDeliveryComponent.ghi_chu_cho_tai_xe') }} <em class="text-length">({{
                                    notes?.length
                                    ?? 0 }}/{{ appConst.max_text_short }})</em></span>
                            <textarea :placeholder="$t('CreateDeliveryComponent.ghi_chu_cho_tai_xe_placeholder')"
                                :maxlength="appConst.max_text_long" v-model="notes"></textarea>
                        </div>
                        <!-- <button class='icon-right'>
                            <Icon name="material-symbols:chevron-right-rounded" size="25px"></Icon>
                        </button> -->
                    </div>
                </div>

                <!-- special requirement -->
                <div class="stack-content full-width" v-if="false">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.yeu_cau_dac_biet') }}
                        </span>
                    </div>
                    <div class="mid-line"></div>
                    <div class="stack-item-content selectable special-item"
                        :class="{ 'active': checkSpecialItem(itemSpecial.key) }" v-on:click="() => {
                            setSpecialItemRequire(itemSpecial)
                        }" v-for="itemSpecial in specialOptionList">
                        <div class="stack-label">
                            <span>{{ $t(`CreateDeliveryComponent.${itemSpecial.key}`) }}</span>
                            <em>{{ $t(`CreateDeliveryComponent.${itemSpecial.description}`, {
                                price:
                                    formatCurrency(itemSpecial.price)
                            }) }}</em>
                        </div>
                        <button class='icon-right add-button'>
                            <Icon name="ic:outline-radio-button-unchecked" size="25px"
                                v-if="!checkSpecialItem(itemSpecial.key)">
                            </Icon>
                            <Icon name="ic:round-check-circle" size="25px" v-else></Icon>
                        </button>
                    </div>
                </div>

            </div>
            <div class="create-delivery-footer">
                <div class="estimate-delivery-price">
                    <div class="estimate-price">
                        <span>{{ $t('CreateDeliveryComponent.phi_ship_khach_chon') }}:</span>
                        <em>{{
                            delivery_price_estimate ? formatCurrency(delivery_price_estimate) :
                                $t('CreateDeliveryComponent.chua_xac_dinh') }}</em>
                    </div>
                    <div class="discount-price" v-if="discount_delivery_price">
                        <span>{{ $t('CreateDeliveryComponent.phi_ship_khach_duoc_giam')
                        }}:</span>
                        <em>{{
                            discount_delivery_price ? formatCurrency(discount_delivery_price) :
                                $t('CreateDeliveryComponent.chua_xac_dinh') }}</em>
                    </div>
                    <div class="estimate-notice" v-if="delivery_price_estimate != delivery_price">
                        <em>{{ $t('CreateDeliveryComponent.luu_y') }}:</em>
                        <span>{{
                            $t('CreateDeliveryComponent.luu_y_phi_ship')
                        }}</span>
                        <nuxt-link :to="`tel:${validPhone(phone_to)}`" :target="webInApp ? '_blank' : ''"
                            class="user-phone">
                            <span>({{ phone_to }})</span>
                        </nuxt-link>
                    </div>
                </div>
                <div class="total">
                    <span>{{ $t('CreateDeliveryComponent.phi_ship') }}</span>
                    <span class="price">{{ delivery_price != null ? formatCurrency(calcDeliveryPrice()) :
                        $t('CreateDeliveryComponent.chua_xac_dinh') }}</span>
                </div>

                <button :disabled=isSubmiting v-on:click="() => {
                    // if (!delivery_partner_id || delivery_partner.name?.toLowerCase().includes('remagan')) {
                    //     submit()
                    // }
                    // else {
                    submitDeliveryPartner()
                    // }
                }">
                    <Icon name="eos-icons:loading" size="20" v-if="isSubmiting" />{{ $t('CreateDeliveryComponent.dat')
                    }}
                </button>
            </div>
        </div>
        <SelectUserInfoComponent :title="$t('CreateDeliveryComponent.thong_tin_dia_diem_lay_hang')" :initialUserInfo="{
            name: name_from,
            phone: validPhone(phone_from),
            address: address_from,
            latitude: latitude_from,
            longitude: longitude_from,
        }" v-on:close="() => {
            showSenderSelectModal = false
        }" v-on:submit="(e: any) => {
            name_from = e.name;
            phone_from = validPhone(e.phone);
            address_from = e.address;
            latitude_from = e.latitude;
            longitude_from = e.longitude;
            showSenderSelectModal = false;
            getDistance();
        }" v-if="showSenderSelectModal"></SelectUserInfoComponent>
        <SelectUserInfoComponent :title="$t('CreateDeliveryComponent.thong_tin_dia_diem_giao_hang')" :initialUserInfo="{
            name: name_to,
            phone: validPhone(phone_to),
            address: address_to,
            latitude: latitude_to,
            longitude: longitude_to,

        }" v-on:close="() => {
            showReceiverSelectModal = false
        }" v-on:submit="(e: any) => {
            name_to = e.name;
            phone_to = validPhone(e.phone);
            address_to = e.address;
            latitude_to = e.latitude;
            longitude_to = e.longitude;
            showReceiverSelectModal = false
            getDistance();
        }" v-if="showReceiverSelectModal"></SelectUserInfoComponent>
        <DateTimePickerComponent v-if="showDateTimePickerModal" :title="t('CreateDeliveryComponent.thoi_gian_lay_hang')"
            :startDate="moment()" :endDate="moment().add(30, 'days')"
            :startTime="moment().hour(7).minutes(0).seconds(0).milliseconds(0)"
            :endTime="moment().hour(22).minutes(31).seconds(0).milliseconds(0)" :stepMinute="5"
            :firstItemTimeIsNull="true" :firstStepMinute="shopData.min_time_to_delivery ?? 5"
            :initialDate="pickup_time ? moment(pickup_time, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY') : null"
            :initialTime="pickup_time ? moment(pickup_time, 'DD/MM/YYYY HH:mm').format('HH:mm') : null" v-on:close="() => {
                showDateTimePickerModal = false
            }" v-on:submit="(e: any) => {
                showDateTimePickerModal = false
                pickup_time = e
            }" />

        <CustomSelectComponent v-if="showSelectDeliveryPartner" :_key="'select_delivery_partner'"
            :list_item="listPartners" :field_value="'id'" :multiple="false"
            :title="`${t('OrderComponent.don_vi_van_chuyen')}`"
            :class="'my-custom-select delivery-partner-custom-select'" :searchable="false"
            :model_value="delivery_partner?.id" v-on:close="() => {
                showSelectDeliveryPartner = false;
            }" v-on:model:update="(e: any) => {
                if (e && e != delivery_partner?.id) {
                    let indexSelected = listPartners.findIndex(function (itemPartner: any) {
                        return e == itemPartner.id
                    })
                    delivery_partner = indexSelected != -1 ? listPartners[indexSelected] : delivery_partner;
                    checkDeliveryPriceV2()
                }

            }">
            <template v-slot:title_icon_left>
                <Icon name="carbon:delivery"></Icon>
            </template>
            <template v-slot:render_item_option="{ item }">
                <div class="delivery-partner-item" :class="{
                    'selected': item.id == delivery_partner?.id
                }">
                    <img class="partner-logo" :src="item?.information?.logo" v-if="item?.information?.logo"
                        :alt="`logo ${item?.name}`">
                    <span class="partner-name" v-else>
                        {{ item?.name }}
                    </span>
                </div>

            </template>
        </CustomSelectComponent>

        <v-overlay v-model="showServiceTypeModal" location="bottom" contained :z-index="1001" key="service_type_select"
            class="service-type-overlay-container" persistent content-class='service-type-container' no-click-animation>
            <HeaderComponent :title="$t('CreateDeliveryComponent.loai_giao_hang')">
                <template v-slot:header_left></template>
                <template v-slot:header_right>
                    <button class="close" v-on:click="() => {
                        showServiceTypeModal = false
                    }">
                        <Icon name="clarity:times-line" size="25"></Icon>
                    </button>
                </template>
            </HeaderComponent>
            <!-- <div class="service-type-list"
                v-if="delivery_partner?.name?.toLowerCase().includes('remagan') && serviceTypeList.length > 0">
                <div class="service-type-item" v-for="itemService in serviceTypeList" v-on:click="() => {
                    service_type = itemService;
                    showServiceTypeModal = false;
                }">
                    <Icon class="icon-left" :name="itemService.icon"></Icon>
                    <div class="item-type-label">
                        <span>{{ $t(`CreateDeliveryComponent.${itemService.key}`) }}</span>
                        <em>{{ $t(`CreateDeliveryComponent.${itemService.description}`) }}</em>
                    </div>
                </div>
            </div> -->
            <div class="service-type-list" v-if="deliveryPartnerServiceTypeList?.length > 0">
                <button class="service-type-item" v-for="itemService in deliveryPartnerServiceTypeList"
                    :disabled="itemService.error || itemService.price == null" v-on:click="() => {
                        service_type = itemService;
                        delivery_price = itemService.price
                        showServiceTypeModal = false;
                    }">
                    <Icon class="icon-left" :name="itemService.icon"></Icon>
                    <div class="item-type-label">
                        <span>{{ itemService.name }}</span>
                        <em v-if="!itemService.error">{{ itemService.price != null ? formatCurrency(itemService.price) :
                            $t('CreateDeliveryComponent.chua_xac_dinh_phi_ship') }}</em>
                        <!-- <em class="error" v-else>{{ itemService.error.message }}</em> -->
                        <em class="error" v-else>{{ $t('CreateDeliveryComponent.khong_ho_tro') }}</em>
                    </div>
                </button>
            </div>
            <div class="service-type-list none-list" v-else>
                <span>{{ $t('CreateDeliveryComponent.khong_tim_thay_dich_vu_phu_hop') }}</span>
            </div>
        </v-overlay>

        <v-overlay v-model="showPackageInfoModal" location="bottom" contained :z-index="1001" key="service_type_select"
            class="package-info-overlay-container" persistent content-class='package-info-container' no-click-animation>
            <HeaderComponent :title="$t('CreateDeliveryComponent.thong_tin_goi_hang')">
                <template v-slot:header_left></template>
                <template v-slot:header_right>
                    <button class="close" v-on:click="() => {
                        showPackageInfoModal = false
                    }">
                        <Icon name="clarity:times-line" size="25"></Icon>
                    </button>
                </template>
            </HeaderComponent>
            <div class="package-info-container">
                <div class="stack-content">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.khoi_luong') }}
                        </span>
                    </div>
                    <div class="stack-item-content">
                        <label class="item-weight" :class="{ 'active': package_info?.weight == itemWeight.key }"
                            v-for="itemWeight in packageWeightList" v-on:click="() => {
                                package_info = {
                                    ...package_info,
                                    weight: itemWeight.key
                                };
                            }">
                            <Icon name="ic:outline-radio-button-checked" v-if="package_info?.weight == itemWeight.key"
                                size='20px'></Icon>
                            <Icon name="ic:outline-radio-button-unchecked" v-else size='20px'></Icon>
                            {{ $t(`CreateDeliveryComponent.${itemWeight.key}`) }}
                        </label>
                    </div>
                </div>
                <div class="stack-content">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.kich_thuoc') }}
                        </span>
                    </div>
                    <div class="stack-item-content size-info">
                        <input :placeholder="$t('CreateDeliveryComponent.kich_thuoc_placeholder')"
                            :value="package_info?.size" v-on:input="(e: any) => {
                                package_info = {
                                    ...package_info,
                                    size: e.target.value
                                };
                            }"></input>
                    </div>
                </div>
                <div class="stack-content">
                    <div class="stack-title">
                        <span>
                            {{ $t('CreateDeliveryComponent.loai_hang_hoa') }}
                        </span>
                    </div>
                    <div class="stack-item-content product-type-info">
                        <button class="item-product-type" :class="{ 'active': package_info?.product_type == itemType }"
                            v-for="itemType in productTypeList" v-on:click="() => {
                                package_info = {
                                    ...package_info,
                                    product_type: itemType
                                };
                            }">
                            {{ itemType }}
                        </button>
                    </div>
                </div>


            </div>
            <div class="package-info-footer">
                <button :disabled=isSubmiting v-on:click="() => {
                    showPackageInfoModal = false
                }">
                    <Icon name="eos-icons:loading" size="20" v-if="isSubmiting" />{{
                        $t('CreateDeliveryComponent.xong')
                    }}
                </button>
            </div>
        </v-overlay>

        <VueFinalModal class="my-modal-container" content-class="v-stack empty-partners-container"
            :overlay-behavior="'persist'" :click-to-close="false" v-model="showEmptyPartnersModal"
            contentTransition="vfm-fade" v-on:closed="() => {
                close();
            }">
            <h3>{{ $t('CreateDeliveryComponent.chua_ket_noi_don_vi_van_chuyen') }}</h3>
            <div class="empty-partner-footer">
                <v-btn variant="flat" class="close" v-on:click="close()">{{ $t('CreateDeliveryComponent.dong') }}</v-btn>
                <v-btn variant="outlined" color="var(--primary-color-1)">
                    <nuxt-link 
                        :to="shopData.agent_id == profileInfo.id 
                            ? appRoute.AgentShopManageDeliveryPartnerComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id) 
                            : appRoute.MyShopManageDeliverPartnerComponent"
                    >
                        {{ $t('CreateDeliveryComponent.ket_noi_ngay') }}
                    </nuxt-link>
                </v-btn>
            </div>

        </VueFinalModal>
    </div>
    <FindShipperComponent v-if="showFindShipper" :absolute="false" :latitude="latitude_from" :longitude="longitude_from"
        :object_id_from="$props.initData?.shop_id" :object_id_type="appConst.object_type?.shop" v-on:close="() => {
            showFindShipper = false
        }" v-on:submit="async (e: any) => {
            showFindShipper = false
            driver = e;
        }">

    </FindShipperComponent>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { appConst, domainImage, formatCurrency, formatNumber, validPhone } from '~/assets/AppConst';
import { DeliveryService } from '~/services/orderService/deliveryService';

import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import SelectUserInfoComponent from '../selectUserInfo/SelectUserInfoComponent.vue';
import DateTimePickerComponent from '~/components/dateTimePicker/DateTimePickerComponent.vue';
import type { AnyFn } from '@vueuse/core';
import FindShipperComponent from '~/components/findShipper/FindShipperComponent.vue';
import { toast } from 'vue3-toastify';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
import { DeliveryPartnerService } from '~/services/deliveryPartnerService/deliveryPartnerService';
import { VueFinalModal } from 'vue-final-modal';
import appRoute from '~/assets/appRoute';
import { delivery_payment_method_enum } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';

const { t, locale } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    order_id: null,
    mode: null,
    initData: null,
    order_data: null
});
const nuxtApp = useNuxtApp();

var specialOptionList = [
    {
        key: 'giao_hang_cong_kenh',
        description: "mo_ta_giao_hang_cong_kenh",
        price: 10000,
    },
    {
        key: 'giao_tan_tay',
        description: "gia_di_kem",
        price: 10000,
    },
    {
        key: 'giao_hang_de_vo',
        description: "gia_di_kem",
        price: 10000,
    },
    {
        key: 'quay_lai_diem_lay_hang',
        description: "gia_di_kem",
        price: 37000,
    },
    {
        key: 'gui_sms_cho_nguoi_nhan',
        description: "gia_di_kem",
        price: 10000,
    },
    {
        key: 'tui_giu_nhiet',
        description: "gia_di_kem",
        price: 10000,
    },
    {
        key: 'ho_tro_tai_xe',
        description: "gia_di_kem",
        price: 5000,
    }
]

var profileInfo = ref(null as any);

// var serviceTypeList = [
//     {
//         key: 'sieu_toc',
//         description: "mo_ta_sieu_toc",
//         time: 30,
//         icon: 'noto:rocket'
//     },
//     {
//         key: 'nhanh',
//         description: "mo_ta_nhanh",
//         time: 60,
//         icon: 'emojione:delivery-truck'
//     },
//     {
//         key: '2_gio',
//         description: "mo_ta_2_gio",
//         time: 60 * 2,
//         icon: 'lucide:clock-2'
//     },
//     {
//         key: '4_gio',
//         description: "mo_ta_4_gio",
//         time: 60 * 4,
//         icon: 'lucide:clock-4'
//     },
// ]

var packageWeightList = [
    {
        key: 'less_2_kg'
    },
    {
        key: 'from_2_to_5_kg'
    },
    {
        key: 'from_5_to_10_kg'
    },
    {
        key: 'more_10_kg'
    },
]

var productTypeList = ['Quần áo', "Thực phẩm", "Dược phẩm & thuốc", "Sách & Văn phòng phẩm", "Mỹ phẩm", "Đồ gốm, sứ", "Khác"]

var deliveryService = new DeliveryService();
var mqttService = new MqttService();
var deliverPartnerService = new DeliveryPartnerService();
var authService = new AuthService();
var webInApp = ref(null as any);
var control: any;
var tempLeafletMap: L.Map;

var latitude_from = ref(null as any);
var latitude_to = ref(null as any);
var longitude_from = ref(null as any);
var longitude_to = ref(null as any);
var distance = ref(0);
var name_from = ref(null as any);
var name_to = ref(null as any);
var phone_from = ref(null as any);
var phone_to = ref(null as any);
var address_from = ref("");
var address_to = ref("");
var special_require = ref([] as any[]);
var pickup_time = ref("" as any);
var service_type = ref(null as any);
var package_info = ref({
    weight: null as any,
    size: null as any,
    product_type: null as any
} as any);
var notes = ref("" as any)
var delivery_price = ref(null as any);
var delivery_price_estimate = ref(null as any);
var discount_delivery_price = ref<any>(null)
var cod_price = ref(0 as any);
var driver = ref(null as any);
var delivery_partner_id = ref<string | null>(null);
var delivery_partner = ref<any>(null);
var deliveryPartnerServiceTypeList = ref<any>([]);
var delivery_payment_method = ref<delivery_payment_method_enum>(delivery_payment_method_enum.cash_by_recipient)
var shopData = ref<any>(null);
var listPartners = ref<any>([]);
var pending_time = ref({
    value: null,
    unit: 'minute',
});

var isSubmiting = ref(false);
var showSenderSelectModal = ref(false);
var showReceiverSelectModal = ref(false);
var showDateTimePickerModal = ref(false);
var showServiceTypeModal = ref(false);
var showPackageInfoModal = ref(false);
var showFindShipper = ref(false);
var showSelectDeliveryPartner = ref(false);
var service_type_checking = ref(false);
var showEmptyPartnersModal = ref(false);

watch(() => [service_type.value], (newValue, oldValue) => {
    delivery_price.value = service_type.value?.price;
    discount_delivery_price.value = service_type.value?.discount;
    // cod_price.value = props.initData.cod_price + service_type.value?.price + service_type.value?.discount
    if (Math.abs(service_type.value?.discount)) {
        delivery_payment_method.value = delivery_payment_method_enum.cash
    }
})
onMounted(async () => {

    profileInfo.value = await authService.checkAuth();

    latitude_from.value = props.initData.latitude_from;
    latitude_to.value = props.initData.latitude_to;
    longitude_from.value = props.initData.longitude_from;
    longitude_to.value = props.initData.longitude_to;
    distance.value = props.initData.distance ?? 0;
    name_from.value = props.initData.name_from;
    name_to.value = props.initData.name_to;
    phone_from.value = props.initData.phone_from;
    phone_to.value = props.initData.phone_to;
    address_from.value = props.initData.address_from;
    address_to.value = props.initData.address_to;
    special_require.value = props.initData.special_require ? JSON.parse(props.initData.special_require) : [];
    pickup_time.value = props.initData.pickup_time ? moment(props.initData.pickup_time).format("DD/MM/YYYY HH:mm") : null;
    // service_type.value = serviceTypeList[Object.keys(serviceTypeList).findIndex(function (e: any) {
    //     return serviceTypeList[e].time == props.initData.duration
    // })];
    package_info.value = props.initData.package_info ? JSON.parse(props.initData.package_info) : null;
    cod_price.value = props.initData.cod_price ?? 0;
    driver.value = props.initData.driver;
    notes.value = props.initData.notes;
    distance.value = props.initData.distance ?? 0;
    delivery_partner_id.value = props.initData.delivery_partner_id;
    delivery_partner.value = props.initData.delivery_partner;
    shopData.value = props.initData.shopData;
    delivery_price_estimate.value = props.initData.delivery_price_estimate;
    pending_time.value = props.initData.pending_time ? {
        value: props.initData.pending_time ?? 30,
        unit: 'minute',
    } : (shopData.value?.settings?.general?.delivery_expire_pending_time ?? pending_time.value);
    console.log(props.initData)
    // checkDeliveryPrice(distance.value)
    getDeliveryPartners().then(() => {
        checkDeliveryPriceV2();
    });
})
function checkDeliveryPrice(distance = 0) {
    if (props.mode != 'update_order') {
        let duration =
            pickup_time.value && moment(pickup_time.value, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes') < 30
                ? 30
                : moment(pickup_time.value, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes');
        deliveryService.checkDeliveryPrice(distance, duration).then(res => {
            if (res.status == HttpStatusCode.Ok) {
                delivery_price.value = res.body.data
            }
        })
    }
    else {
        delivery_price.value = props.initData?.delivery_price
    }
}

function checkDeliveryPriceV2() {
    if (delivery_partner.value?.id) {
        service_type_checking.value = true;
        let duration =
            pickup_time.value && moment(pickup_time.value, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes') < 30
                ? 30
                : moment(pickup_time.value, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes');

        let bodyRef = {};
        let orderTemp = JSON.parse(JSON.stringify(props.order_data));
        // orderTemp.price_off = orderTemp.discount_amount;

        delete orderTemp.delivery;
        delete orderTemp.customer;
        delete orderTemp.shops;
        delete orderTemp.images;

        bodyRef = {
            partner: (delivery_partner.value?.name.toLowerCase().includes('remagan')) ? 'remagan' : delivery_partner.value?.name.toLowerCase(),
            shop_id: shopData.value?.id,
            distance: parseFloat(distance.value?.toString()),
            duration: duration,
            path: [
                {
                    lat: latitude_from.value,
                    lng: longitude_from.value,
                    address: address_from.value,
                    name: name_from.value,
                    phone: phone_from.value
                },
                {
                    lat: latitude_to.value,
                    lng: longitude_to.value,
                    address: address_to.value,
                    name: name_to.value,
                    phone: phone_to.value,
                }
            ],
            order: orderTemp,
            filter:'all'
        }

        deliveryService.checkDeliveryPriceFromPartner(bodyRef).then((res) => {
            if (res.status == HttpStatusCode.Ok) {

                deliveryPartnerServiceTypeList.value = isArray(res.body.data) ? res.body.data : [res.body.data];
                // serviceTypeList.values = isArray(res.body.data) ? res.body.data : [res.body.data];
                let indexSelected = deliveryPartnerServiceTypeList.value.findIndex(function (e: any) {
                    return e.service_id == delivery_partner.value?.service_id || e.price == delivery_price_estimate.value
                });
                // if (delivery_partner.value?.name?.toLowerCase().includes('remagan')) {
                //     service_type.value = serviceTypeList[0];
                //     delivery_price.value = delivery_price_estimate.value;
                // }
                // else {
                service_type.value = deliveryPartnerServiceTypeList.value[indexSelected != -1 ? indexSelected : 0];

                // }
                service_type_checking.value = false;
            }
            else {
                delivery_price.value = null;
                deliveryPartnerServiceTypeList.value = null;
                service_type.value = null;
                service_type_checking.value = false;
            }
        }).catch(() => {
            delivery_price.value = null;
            deliveryPartnerServiceTypeList.value = null;
            service_type.value = null;
            service_type_checking.value = false;
        })
    }

}

function getDeliveryPartners() {
    let shop_id = shopData.value?.id;
    return new Promise((resolve) => {
        deliverPartnerService.shopListPartner(shop_id).then((res) => {
            if (res.status == 200) {
                listPartners.value = res.body?.partners.filter((e: any) => e.is_connected && e.is_enabled);
                if (listPartners.value.length) {
                    let defaultPartner = listPartners.value.filter((e: any) => e.is_connected && e.is_enabled && e.is_default)
                    delivery_partner.value = delivery_partner_id.value ? delivery_partner.value : defaultPartner.length ? defaultPartner[0] : listPartners.value[0];
                    delivery_partner_id.value = delivery_partner_id.value ? delivery_partner_id.value : defaultPartner.length ? defaultPartner[0].id : listPartners.value[0].id;
                }
                else {
                    delivery_partner.value = null;
                    delivery_partner_id.value = null;
                    showEmptyPartnersModal.value = true;
                }
            }
            resolve(listPartners.value);
        })
    })
}
async function getDistance() {
    if (control) {
        control.remove();
    }
    console.log("get distance")

    control = nuxtApp.$L.Routing.control({
        waypointMode: 'connect',
        router: nuxtApp.$L.Routing.osrmv1({
            serviceUrl: appConst.urlOSRMv1,
            requestParameters: {
                overview: 'full',
                annotations: true,
                steps: true,
                alternatives: 2,

            },
        }),
        plan: new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(latitude_from.value, longitude_from.value),
            nuxtApp.$L.latLng(latitude_to.value, longitude_to.value)
        ], {
            createMarker: () => (false),
        }),
    })
    if (tempLeafletMap) {
        control.addTo(tempLeafletMap).on('routesfound', (e: any) => {
            distance.value = parseFloat((e.routes[0].summary.totalDistance / 1000).toFixed(1));
            // checkDeliveryPrice(distance.value);
            checkDeliveryPriceV2()
        });
    }

}
function setSpecialItemRequire(itemSpecial: any) {
    let index = special_require.value.findIndex(function (e: any) {
        return e.key == itemSpecial.key
    });
    if (index == -1) {
        special_require.value.push(itemSpecial);
    }
    else {
        special_require.value.splice(index, 1)
    }
}

function checkSpecialItem(itemCheck: any) {
    let index = special_require.value?.findIndex(function (e: any) {
        return e.key == itemCheck
    });
    return index != -1 ? true : false;
}

function submit() {
    if (!name_from.value?.length || !phone_from.value?.length || !address_from.value.length || !latitude_from.value || !longitude_from.value) {
        showSenderSelectModal.value = true;
        return;
    }
    if (!name_to.value?.length || !phone_to.value?.length || !address_to.value.length || !latitude_to.value || !longitude_to.value) {
        showReceiverSelectModal.value = true;
        return;
    }
    if (!service_type.value) {
        showServiceTypeModal.value = true;
        return;
    }
    isSubmiting.value = true;
    let body = {
        id: props.initData?.id,
        order_id: props.initData?.order_id,
        driver_id: driver.value?.id ? driver.value?.id : null,
        duration: service_type.value?.duration,
        pickup_time: pickup_time.value ? moment(pickup_time.value, "DD/MM/YYYY HH:mm").format('YYYY-MM-DD HH:mm') : null,
        notes: notes.value,
        latitude_from: latitude_from.value,
        longitude_from: longitude_from.value,
        latitude_to: latitude_to.value,
        longitude_to: longitude_to.value,
        distance: distance.value,
        address_from: address_from.value,
        name_from: name_from.value,
        phone_from: phone_from.value,
        address_to: address_to.value,
        name_to: name_to.value,
        phone_to: phone_to.value,
        cod_price: cod_price.value,
        // status_time: JSON.stringify(data?.status_time) || null,
        package_info: JSON.stringify(package_info.value),
        special_require: JSON.stringify(special_require.value),
        total_amount: calcDeliveryPrice() + (cod_price.value || 0),
        grand_total: null,
        status: appConst.delivery_status.pending
    }
    if (props.initData?.id && false) {
        deliveryService.updateDelivery(body).then(res => {
            if (res.status == HttpStatusCode.Ok) {
                emit('submit', true);
                mqttService.publish(driver.value?.id, JSON.stringify({
                    mess: {
                        type: appConst.notification_type.delivery,
                        url: props.initData?.id
                    }
                }))
                isSubmiting.value = false;
            }
            else if (res.status == HttpStatusCode.Unauthorized) {
                toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
                isSubmiting.value = false;
            }
            else {
                toast.error(res.body?.message ?? t('MyShopOrderDetailComponent.co_loi_xay_ra'));
                isSubmiting.value = false;
            }
        })
    }
    else {
        deliveryService.createDelivery(body).then(res => {
            if (res.status == HttpStatusCode.Ok) {
                emit('submit', true);
                mqttService.publish(driver.value?.id, JSON.stringify({
                    mess: {
                        type: appConst.notification_type.delivery,
                        url: res.body?.data?.id
                    }
                }))
                isSubmiting.value = false;
            }
            else if (res.status == HttpStatusCode.Unauthorized) {
                toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
                isSubmiting.value = false;
            }
            else {
                toast.error(res.body?.message ?? t('MyShopOrderDetailComponent.co_loi_xay_ra'));
                isSubmiting.value = false;
            }
        })
    }
}

async function submitDeliveryPartner() {
    if (!name_from.value?.length || !phone_from.value?.length || !address_from.value.length || !latitude_from.value || !longitude_from.value) {
        showSenderSelectModal.value = true;
        return;
    }
    if (!name_to.value?.length || !phone_to.value?.length || !address_to.value.length || !latitude_to.value || !longitude_to.value) {
        showReceiverSelectModal.value = true;
        return;
    }
    if (!service_type.value && deliveryPartnerServiceTypeList.value?.length) {
        showServiceTypeModal.value = true;
        return;
    }

    // if (!pickup_time.value) {
    //     showDateTimePickerModal.value = true;
    //     return;
    // }

    if (service_type.value?.price == null) {
        toast.error(t('CreateDeliveryComponent.khong_the_dat_don_giao_khi_khong_ho_tro'))
        return;
    }

    isSubmiting.value = true;

    // if (delivery_partner.value?.name?.toLowerCase().includes('remagan') || !delivery_partner.value) {
    //     let body = {
    //         id: props.initData?.id,
    //         order_id: props.initData?.order_id,
    //         driver_id: driver.value?.id ? driver.value?.id : null,
    //         duration: delivery_partner.value?.name?.toLowerCase().includes('remagan') ? (service_type.value?.duration ?? 30) : service_type.value?.duration,
    //         pickup_time: pickup_time.value ? moment(pickup_time.value, "DD/MM/YYYY HH:mm").format('YYYY-MM-DD HH:mm') : null,
    //         notes: notes.value,
    //         latitude_from: latitude_from.value,
    //         longitude_from: longitude_from.value,
    //         latitude_to: latitude_to.value,
    //         longitude_to: longitude_to.value,
    //         distance: distance.value,
    //         address_from: address_from.value,
    //         name_from: name_from.value,
    //         phone_from: phone_from.value,
    //         address_to: address_to.value,
    //         name_to: name_to.value,
    //         phone_to: phone_to.value,
    //         cod_price: cod_price.value ?? 0,
    //         // status_time: JSON.stringify(data?.status_time) || null,
    //         package_info: JSON.stringify(package_info.value),
    //         special_require: JSON.stringify(special_require.value),
    //         total_amount: calcDeliveryPrice(),
    //         grand_total: null,
    //         status: appConst.delivery_status.pending,
    //         pending_time: await calcPendingTime(),
    //         partner: delivery_partner.value?.name?.toLowerCase().includes('remagan') ? 'remagan' : delivery_partner.value?.name?.toLowerCase(),
    //     }
    //     if (props.initData?.id && props.mode == 'select_driver') {
    //         deliveryService.updateDelivery(body).then(res => {
    //             if (res.status == HttpStatusCode.Ok) {
    //                 emit('submit', true);
    //                 mqttService.publish(driver.value?.id, JSON.stringify({
    //                     mess: {
    //                         type: appConst.notification_type.delivery,
    //                         url: props.initData?.id
    //                     }
    //                 }))
    //                 isSubmiting.value = false;
    //             }
    //             else if (res.status == HttpStatusCode.Unauthorized) {
    //                 toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
    //                 isSubmiting.value = false;
    //             }
    //             else {
    //                 toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
    //                 isSubmiting.value = false;
    //             }
    //         })
    //     }
    //     else {
    //         deliveryService.createDelivery(body).then(res => {
    //             if (res.status == HttpStatusCode.Ok) {
    //                 emit('submit', true);
    //                 mqttService.publish(driver.value?.id, JSON.stringify({
    //                     mess: {
    //                         type: appConst.notification_type.delivery,
    //                         url: res.body?.data?.id
    //                     }
    //                 }))
    //                 isSubmiting.value = false;
    //             }
    //             else if (res.status == HttpStatusCode.Unauthorized) {
    //                 toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
    //                 isSubmiting.value = false;
    //             }
    //             else {
    //                 toast.error(t('MyShopOrderDetailComponent.co_loi_xay_ra'));
    //                 isSubmiting.value = false;
    //             }
    //         })
    //     }
    // }
    // else {
    let body = {
        partner: delivery_partner.value?.name?.toLowerCase().includes('remagan') ? 'remagan' : delivery_partner.value?.name?.toLowerCase(),
        shop_id: shopData.value?.id,
        order_id: props.initData?.order_id,
        delivery_service: service_type.value?.service_id ?? "",
        payment_method: delivery_payment_method.value,
        note: notes.value,
        path: [
            {
                lat: latitude_from.value,
                lng: longitude_from.value,
                address: address_from.value,
                name: name_from.value,
                phone: phone_from.value,
            },
            {
                lat: latitude_to.value,
                lng: longitude_to.value,
                address: address_to.value,
                name: name_to.value,
                phone: phone_to.value,
                cod: cod_price.value ?? 0,
            }
        ],

        id: props.initData?.id,
        driver_id: driver.value?.id ? driver.value?.id : null,
        duration: delivery_partner.value?.name?.toLowerCase().includes('remagan') ? (service_type.value?.duration ?? 30) : service_type.value?.duration,
        pickup_time: pickup_time.value ? moment(pickup_time.value, "DD/MM/YYYY HH:mm").format('YYYY-MM-DD HH:mm') : null,
        notes: notes.value,
        latitude_from: latitude_from.value,
        longitude_from: longitude_from.value,
        latitude_to: latitude_to.value,
        longitude_to: longitude_to.value,
        distance: distance.value,
        address_from: address_from.value,
        name_from: name_from.value,
        phone_from: phone_from.value,
        address_to: address_to.value,
        name_to: name_to.value,
        phone_to: phone_to.value,
        cod_price: cod_price.value ?? 0,
        // status_time: JSON.stringify(data?.status_time) || null,
        package_info: JSON.stringify(package_info.value),
        special_require: JSON.stringify(special_require.value),
        total_amount: calcDeliveryPrice(),
        grand_total: null,
        status: appConst.delivery_status.pending,
        pending_time: await calcPendingTime()
    }

    if (delivery_partner.value?.name?.toLowerCase().includes('remagan')) {
        delete body.delivery_service
    }

    if (props.initData.id && props.mode == 'select_driver') {
        deliveryService.updateDelivery(body).then(res => {
            if (res.status == HttpStatusCode.Ok) {
                emit('submit', true);
                mqttService.publish(driver.value?.id, JSON.stringify({
                    mess: {
                        type: appConst.notification_type.delivery,
                        url: props.initData?.id
                    }
                }))
                isSubmiting.value = false;
            }
            else if (res.status == HttpStatusCode.Unauthorized) {
                toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
                isSubmiting.value = false;
            }
            else {
                toast.error(res.body?.message ?? t('MyShopOrderDetailComponent.co_loi_xay_ra'));
                isSubmiting.value = false;
            }
        })
    }
    else {
        deliveryService.createDeliveryV2(body).then((res: any) => {
            if (res.status == HttpStatusCode.Ok) {
                emit('submit', true);
                mqttService.publish(driver.value?.id, JSON.stringify({
                    mess: {
                        type: appConst.notification_type.delivery,
                        url: res.body?.data?.id
                    }
                }))
                isSubmiting.value = false;
            }
            else if (res.status == HttpStatusCode.Unauthorized) {
                toast.error(t('MyShopOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
                isSubmiting.value = false;
            }
            else {
                toast.error(res.body?.message ?? t('MyShopOrderDetailComponent.co_loi_xay_ra'));
                isSubmiting.value = false;
            }
        })
    }

    // }
}

function calcDeliveryPrice() {
    let total = 0;
    special_require.value?.forEach((e: any) => {
        total = parseFloat(total?.toString()) + parseFloat(e?.price?.toString());
    });
    total += parseFloat(delivery_price.value?.toString());
    return total || 0;
}

function close() {
    emit('close')
}

function calcPendingTime() {
    if (pending_time.value?.value) {
        return pending_time.value?.value * (
            pending_time.value?.unit == 'hour' ? 60
                : pending_time.value.unit == 'day' ? 60 * 24
                    : pending_time.value?.unit == 'second' ? (1 / 60)
                        : 1
        )
    }
    return 30
}

</script>

<style lang="scss" src="./CreateDeliveryStyles.scss"></style>