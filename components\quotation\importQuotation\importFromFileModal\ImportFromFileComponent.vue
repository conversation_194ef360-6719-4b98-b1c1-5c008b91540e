<template>
    <VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
        content-class="v-stack import-from-file-container" :click-to-close="true" v-model="showImportFileModal"
        contentTransition="vfm-fade" v-on:closed="() => {
            close()
        }">
        <SubHeaderV2Component :title="$t('ImportFromFileComponent.nhap_tu_excel')">
            <template v-slot:header_left></template>
            <template v-slot:header_right>
                <button class="close-button" v-on:click="() => {
                    close();
                }">
                    <Icon name="clarity:times-line" size="25"></Icon>
                </button>
            </template>
        </SubHeaderV2Component>
        <div class="import-from-file-content">
            <span class="current-file-name" v-if="currentFile">{{ currentFile.name }}</span>
            <label class="select-file">
                <span>{{ currentFile ? $t('ImportFromFileComponent.chon_file_khac') : $t('ImportFromFileComponent.chon_file') }}</span>
                <input type="file" :placeholder="currentFile ? currentFile.name : $t('ImportFromFileComponent.chon_file')" :disabled="readingFile" accept=".xlsx, .xls, .csv"
                    :multiple="false"
                    ref="uploadFile" v-on:change="($event:any)=>{
                        console.log($event);
                        currentFile = $event.target?.files?.[0];
                        console.log(currentFile);
                    }">
            </label>

            <v-row class="define-field-select">
                <v-col cols="12" md="3" lg="3" sm="12" class="field-col">
                    <div class="field-context">
                        <label for="name_role_number"> {{ $t('ImportFromFileComponent.hang_chua_ten_cot') }} </label>
                        <input type="text" :disabled="readingFile" v-model="nameRowNumber" name="name_role_number" :placeholder="$t('ImportFromFileComponent.hang_placeholder')">
                    </div>

                </v-col>
                <v-col cols="12" md="3" lg="3" sm="12" class="field-col">
                    <div class="field-context">
                        <label for="data_start_row"> {{ $t('ImportFromFileComponent.hang_bat_dau_du_lieu') }} </label>
                        <input type="text" :disabled="readingFile" v-model="dataStartRow" name="data_start_row" :placeholder="$t('ImportFromFileComponent.hang_placeholder')">
                    </div>

                </v-col>
                <v-col cols="12" md="3" lg="3" sm="12" class="field-col">
                    <div class="field-context">
                        <label for="data_start_col"> {{ $t('ImportFromFileComponent.cot_bat_dau_du_lieu') }} </label>
                        <input type="text" :disabled="readingFile" v-model="dataStartColumn" name="data_start_col" :placeholder="$t('ImportFromFileComponent.cot_placeholder')">
                    </div>

                </v-col>
                <v-col cols="12" md="3" lg="3" sm="12" class="field-col">
                    <div class="field-context">
                        <label for="data_end_col"> {{ $t('ImportFromFileComponent.cot_ket_thuc_du_lieu') }} </label>
                        <input type="text" :disabled="readingFile" v-model="dataEndColumn" name="data_end_col" :placeholder="$t('ImportFromFileComponent.cot_placeholder')">
                    </div>
                </v-col>
            </v-row>
            <v-btn class="read-file" variant="flat" :loading="readingFile" :disabled="readingFile || !currentFile" v-on:click="()=>{
                readFile();
            }">
                {{ $t('ImportFromFileComponent.doc_file') }}
            </v-btn>
        </div>
    </VueFinalModal>
</template>

<script lang="ts" setup>
import { HttpStatusCode } from 'axios';
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { QuotationService } from '~/services/quotationService/quotationService';
const props = defineProps({

})
const {t} = useI18n();
const emit = defineEmits(['close', 'submit']);
const nuxtApp = useNuxtApp();
const quotationService = new QuotationService();

var showImportFileModal = ref(false);
var uploadFile = ref<HTMLElement | undefined>();

var currentFile = ref<any>(null);
var nameRowNumber = ref();
var dataStartRow = ref();
var dataStartColumn = ref<any>();
var dataEndColumn = ref<any>();

var readingFile = ref(false);
onMounted(() => {
    showImportFileModal.value = true;
})

function close() {
    showImportFileModal.value = false
    emit('close')
}

function readFile() {
    readingFile.value = true;
    
    quotationService.readFile(
        currentFile.value,
        nameRowNumber.value,
        dataStartRow.value,
        dataStartColumn.value,
        dataEndColumn.value
    ).then(res => {

        if (res.status == HttpStatusCode.Ok) {
            let sheetListData = Object.keys(JSON.parse(JSON.stringify(res.body.data)).sheet_lists).map((key) => {
                    return {
                        label: JSON.parse(JSON.stringify(res.body.data)).sheet_lists[key],
                        value: key
                    }
                });
            emit('submit', {
                dataView: JSON.parse(JSON.stringify(res.body.data)),
                sheetLists: sheetListData,
                sheetData: JSON.parse(JSON.stringify(res.body.data.sheet_data)),
                fileUrl: res.body.file,
                name: currentFile.value.name,
                currentSheet: sheetListData[0].value,
                notes: sheetListData[0].label,
            });
            close();
            readingFile.value = false;
            
        }
        else {
            readingFile.value = false;
            toast.error(t('ImportFromFileComponent.doc_that_bai'));
        }
    }).catch(err => {
        readingFile.value = false;
        toast.error(t('ImportFromFileComponent.doc_that_bai'));
    });
}

</script>

<style lang="scss" src="./ImportFromFileStyles.scss"></style>