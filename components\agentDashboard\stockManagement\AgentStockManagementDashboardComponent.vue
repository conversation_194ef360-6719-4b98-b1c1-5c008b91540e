<template>
  <div class="agent-stock-management-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">{{ $t('StockManagement.quan_ly_kho') }}</h1>
      <p class="dashboard-subtitle">{{ shopName }}</p>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
      <div class="summary-card">
        <div class="card-icon">
          <Icon name="solar:box-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ currentStockValue }}</h3>
          <p>{{ $t('StockManagement.gia_tri_kho') }}</p>
        </div>
      </div>
      
      <div class="summary-card">
        <div class="card-icon">
          <Icon name="solar:package-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ totalProducts }}</h3>
          <p>{{ $t('StockManagement.so_san_pham') }}</p>
        </div>
      </div>
      
      <div class="summary-card low-stock" v-if="lowStockCount > 0">
        <div class="card-icon">
          <Icon name="solar:danger-triangle-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ lowStockCount }}</h3>
          <p>{{ $t('StockManagement.ton_kho_thap') }}</p>
        </div>
      </div>
    </div>

    <!-- Action Cards Grid -->
    <div class="action-cards-grid">
      <!-- Stock Import -->
      <div class="action-card" @click="navigateToImport">
        <div class="card-icon-wrapper import">
          <Icon name="solar:import-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.nhap_kho') }}</h3>
          <p>{{ $t('StockManagement.ghi_nhan_hang_nhap') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>

      <!-- Stock Export -->
      <div class="action-card" @click="navigateToExport">
        <div class="card-icon-wrapper export">
          <Icon name="solar:export-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.xuat_kho') }}</h3>
          <p>{{ $t('StockManagement.ghi_nhan_ban_hang') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>

      <!-- Stock Waste -->
      <div class="action-card" @click="navigateToWaste">
        <div class="card-icon-wrapper waste">
          <Icon name="solar:trash-bin-trash-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.hong_mat') }}</h3>
          <p>{{ $t('StockManagement.ghi_nhan_hong_mat') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>

      <!-- Stock History -->
      <div class="action-card" @click="navigateToHistory">
        <div class="card-icon-wrapper history">
          <Icon name="solar:history-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.lich_su') }}</h3>
          <p>{{ $t('StockManagement.xem_lich_su_kho') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>

      <!-- Reports -->
      <div class="action-card" @click="navigateToReports">
        <div class="card-icon-wrapper reports">
          <Icon name="solar:chart-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.bao_cao') }}</h3>
          <p>{{ $t('StockManagement.thong_ke_bao_cao') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>

      <!-- Current Stock -->
      <div class="action-card" @click="navigateToCurrentStock">
        <div class="card-icon-wrapper current">
          <Icon name="solar:warehouse-bold" size="32" />
        </div>
        <div class="card-content">
          <h3>{{ $t('StockManagement.ton_kho_hien_tai') }}</h3>
          <p>{{ $t('StockManagement.xem_ton_kho') }}</p>
        </div>
        <div class="card-arrow">
          <Icon name="solar:arrow-right-bold" size="20" />
        </div>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="recent-activities" v-if="recentActivities.length > 0">
      <h2>{{ $t('StockManagement.lich_su') }}</h2>
      <div class="activity-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="activity.type">
            <Icon :name="getActivityIcon(activity.type)" size="20" />
          </div>
          <div class="activity-content">
            <p class="activity-title">{{ activity.description }}</p>
            <p class="activity-time">{{ formatTime(activity.created_at) }}</p>
          </div>
          <div class="activity-value">
            <span :class="getValueClass(activity.type)">
              {{ formatValue(activity.quantity, activity.type) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
      <p>{{ $t('StockManagement.dang_tai') }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import { appRoute } from '~/assets/appRoute'
import { MqttService } from '~/services/mqttService/mqttService'

// Props
const props = defineProps<{
  shopId: string
}>()

// Composables
const router = useRouter()
const { t } = useI18n()

// Services
const stockService = new StockService()
const mqttService = new MqttService()

// Reactive data
const loading = ref(false)
const currentStockValue = ref('0 VND')
const totalProducts = ref(0)
const lowStockCount = ref(0)
const recentActivities = ref([])
const shopName = ref('')

// Methods
const loadDashboardData = async () => {
  loading.value = true
  try {
    // Load stock summary for specific shop
    const summaryResponse = await stockService.getDailySummary({
      shop_id: props.shopId,
      date_from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      date_to: new Date().toISOString().split('T')[0]
    })
    
    if (summaryResponse.data) {
      currentStockValue.value = formatCurrency(summaryResponse.data.total_value || 0)
      totalProducts.value = summaryResponse.data.total_products || 0
      lowStockCount.value = summaryResponse.data.low_stock_count || 0
      
      if (summaryResponse.data.activities) {
        recentActivities.value = summaryResponse.data.activities.slice(0, 5)
      }
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    loading.value = false
  }
}

const setupMqttSubscription = () => {
  const topic = `stock_updates_${props.shopId}`
  mqttService.subscribe(topic, (message) => {
    handleStockUpdate(message)
  })
}

const handleStockUpdate = (message: any) => {
  if (message.type === 'stock_low') {
    lowStockCount.value = message.data.low_stock_count
  } else if (message.type === 'stock_update') {
    loadDashboardData() // Refresh data
  }
}

// Navigation methods
const navigateToImport = () => {
  router.push(appRoute.AgentStockImportComponent.replace(':id', props.shopId))
}

const navigateToExport = () => {
  router.push(appRoute.AgentStockExportComponent.replace(':id', props.shopId))
}

const navigateToWaste = () => {
  router.push(appRoute.AgentStockWasteComponent.replace(':id', props.shopId))
}

const navigateToHistory = () => {
  router.push(appRoute.AgentStockHistoryComponent.replace(':id', props.shopId).replace(':product_id', ''))
}

const navigateToReports = () => {
  router.push(appRoute.AgentStockReportsComponent.replace(':id', props.shopId))
}

const navigateToCurrentStock = () => {
  router.push(appRoute.AgentStockCurrentComponent.replace(':id', props.shopId))
}

// Utility methods
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(value)
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const formatValue = (quantity: number, type: string) => {
  const sign = type === 'import' ? '+' : '-'
  return `${sign}${quantity}`
}

const getValueClass = (type: string) => {
  return {
    'value-positive': type === 'import',
    'value-negative': type === 'export' || type === 'waste'
  }
}

const getActivityIcon = (type: string) => {
  const icons = {
    import: 'solar:import-bold',
    export: 'solar:export-bold',
    waste: 'solar:trash-bin-trash-bold'
  }
  return icons[type] || 'solar:box-bold'
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
  setupMqttSubscription()
})
</script>

<style scoped src="../../../myShop/stockManagement/StockManagementStyles.scss"></style>
