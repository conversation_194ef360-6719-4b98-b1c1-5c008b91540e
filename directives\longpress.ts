// plugins/longpress.js
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive("longpress", {
    mounted(el, binding) {
      let pressTimer: any = null;

      const start = (e: any) => {
        if (e.type === "click" && e.button !== 0) return; // Only left click
        cancel()
        if (pressTimer === null) {
          pressTimer = setTimeout(() => {
            binding.value(e); // Trigger the long press event
          },  600); // Default duration for long press
        }
      };

      const cancel = () => {
        if (pressTimer !== null) {
          clearTimeout(pressTimer);
          pressTimer = null;
        }
      };

      const addListeners = () => {
        el.addEventListener("mousedown", start);
        el.addEventListener("touchstart", start);
        el.addEventListener("mouseup", cancel);
        el.addEventListener("mouseleave", cancel);
        el.addEventListener("touchend", cancel);
        el.addEventListener("touchcancel", cancel);
      };

      const removeListeners = () => {
        el.removeEventListener("mousedown", start);
        el.removeEventListener("touchstart", start);
        el.removeEventListener("mouseup", cancel);
        el.removeEventListener("mouseleave", cancel);
        el.removeEventListener("touchend", cancel);
        el.removeEventListener("touchcancel", cancel);
      };

      el.__longpress = { start, cancel, addListeners, removeListeners };
      // Add event listeners
      addListeners();
    },
    updated(el, binding) {
      // Ensure the updated directive has the latest binding value
      const { removeListeners, addListeners } = el.__longpress;
      removeListeners(); // Remove old listeners
      addListeners(); // Add listeners again with the new binding
    },
    unmounted(el) {
      const { removeListeners } = el.__longpress || {};
      if (removeListeners) removeListeners(); // Cleanup listeners
      delete el.__longpress; // Cleanup element-specific properties
    },
  });
});
