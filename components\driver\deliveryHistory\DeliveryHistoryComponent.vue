<template>
  <div class="public-container" id="public_container" v-on:scroll="() => {
    loadMoreListDelivery()
  }">
    <div class="v-stack delivery-history-container">

      <div class="sticky-header">
        <SubHeaderV2Component :title="$t('AppRouteTitle.DeliveryHistoryComponent')">
          <template v-slot:header_right>
            <button class="filter-button" :class="{ 'active': checkFilterActive() }" v-on:click="async () => {
            showFilterMenu = !showFilterMenu
          }">
            <Icon name="ph:funnel" />
          </button>
            <button v-on:click="async () => {
              refreshListDelivery()
            }">
              <Icon name="lets-icons:refresh" />
            </button>
          </template>
        </SubHeaderV2Component>
        <v-tabs v-model="tabIndex" class="delivery-tab" color="var(--primary-color-1)">
          <v-tab class="delivery-tab-title" :class="tabIndex == 'all' ? 'active' : ''" value="all" key="all" v-on:click="async () => {
            tabIndex = 'all';
            router.replace({
              ...route,
              hash: ''
            });
          }">
            <div class="tab-title">
              {{ $t('DeliveryHistoryComponent.tat_ca') }}
            </div>
          </v-tab>
          <v-tab class="delivery-tab-title" :class="tabIndex == 'completed' ? 'active' : ''" value="completed"
            key="completed" v-on:click="async () => {
              tabIndex = 'completed';
              router.replace({
                ...route,
                hash: '#completed'
              });
            }">
            <div class="tab-title">
              {{ $t('DeliveryHistoryComponent.da_hoan_thanh') }}
            </div>
          </v-tab>
          <v-tab class="delivery-tab-title" :class="tabIndex == 'canceled' ? 'active' : ''" value="canceled"
            key="canceled" v-on:click="async () => {
              tabIndex = 'canceled';
              router.replace({
                ...route,
                hash: '#canceled'
              });
            }">
            <div class="tab-title">
              {{ $t('DeliveryHistoryComponent.da_huy') }}
            </div>
          </v-tab>

        </v-tabs>
        <span class="amount-delivery" v-html="$t('DeliveryHistoryComponent.hien_thi_tren_tong_don_hang', { amount: ':amount' }).replaceAll(':amount',
          `<strong>${listDelivery?.length}/${totalAmount}</strong>`
        )"></span>
      </div>

      <div class="delivery-history-content-container">


        <div class="list-delivery-container" v-if="totalAmount">
          <div class="item-delivery-container" v-for="itemDelivery in listDelivery">
            <div class="stack-content">
              <div class="stack-header" :class="{
                'pending': itemDelivery.status == appConst.delivery_status.pending,
                'confirmed': itemDelivery.status == appConst.delivery_status.confirmed,
                'prepared': itemDelivery.status == appConst.delivery_status.prepared,
                'picked_up': itemDelivery.status == appConst.delivery_status.picked_up,
                'in_transit': itemDelivery.status == appConst.delivery_status.in_transit,
                'delivered': itemDelivery.status == appConst.delivery_status.delivered,
                'cancelled': itemDelivery.status == appConst.delivery_status.cancelled,
                'failed': itemDelivery.status == appConst.delivery_status.failed,
              }">
                <div class="stack-header-1">
                  <strong class="short-code">#{{ itemDelivery.short_code }}</strong>
                  <span class="delivery-status">{{
                    $t(`DeliveryHistoryComponent.trang_thai_don_hang_${itemDelivery.status}`) }}</span>
                </div>
                <div class="stack-header-2">
                  <span class="delivery-created-time">{{ moment(itemDelivery.created_at).format('DD/MM, H:mm A')
                    }}</span>
                </div>
              </div>
              <div class="stack-item-content">
                <Icon name="line-md:my-location-loop" class="icon-left from-marker"></Icon>
                <div class="stack-label">
                  <span>{{ itemDelivery.name_from }} - {{ itemDelivery.phone_from }}
                    <nuxt-link :to="`tel:${validPhone(itemDelivery.phone_from)}`" :target="webInApp ? '_blank' : ''"
                      class="call-customer">
                      <Icon name="material-symbols:call"></Icon>
                      {{ $t('DeliveryHistoryComponent.goi_dien') }}
                    </nuxt-link>
                  </span>
                  <em>{{ itemDelivery.address_from }}</em>
                </div>

              </div>
              <div class="stack-item-content">
                <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon> <em
                  class="distance">{{
                    itemDelivery.distance }} km</em>
              </div>
              <div class="stack-item-content">
                <Icon name="line-md:map-marker" class="icon-left to-marker"></Icon>
                <div class="stack-label">
                  <span>{{ itemDelivery.name_to }} - {{ validPhone(itemDelivery.phone_to) }}
                    <nuxt-link :to="`tel:${validPhone(itemDelivery.phone_to)}`" :target="webInApp ? '_blank' : ''"
                      class="call-customer">
                      <Icon name="material-symbols:call"></Icon>
                      {{ $t('DeliveryHistoryComponent.goi_dien') }}
                    </nuxt-link>
                  </span>
                  <em>{{ itemDelivery.address_to }}</em>
                </div>

              </div>
              <div class="stack-footer">
                <div class="prices">
                  <div class="delivery-price">
                    <span>{{ $t('DeliveryHistoryComponent.phi_ship') }}: </span>
                    <!-- <strong>{{ formatCurrency(itemDelivery.total_amount) }}</strong> -->
                    <strong>{{ formatCurrency(itemDelivery.order?.delivery_price) }}</strong>
                  </div>
                  <div class="cod-price" v-if="false">
                    <span>{{ $t('DeliveryHistoryComponent.cod') }}: </span>
                    <strong>{{ formatCurrency(itemDelivery.cod_price || 0) }}</strong>
                  </div>
                </div>
                <div class="actions">
                  <nuxt-link :to="appRoute.DeliveryDetailComponent.replaceAll(':delivery_id', itemDelivery.id)"
                    class="action-button view">
                    {{ $t('DeliveryHistoryComponent.chi_tiet') }}
                  </nuxt-link>
                  <button class="action-button receive" v-if="itemDelivery.status == appConst.delivery_status.pending"
                    v-on:click="() => {
                      setDeliveryStatus(itemDelivery, appConst.delivery_status.confirmed)
                    }">
                    {{ $t('DeliveryHistoryComponent.nhan_don') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <span class="loading-more" v-if="loadingMore">{{ $t('DeliveryHistoryComponent.dang_tai') }}</span>
          <div id="last_of_list_delivery_history"></div>
        </div>
        <div class="list-delivery-container none-list" v-else-if="!isRefreshing">
          <img :src="not_found_shipper" />
          <span>{{ $t('DeliveryHistoryComponent.chua_co_don_giao') }}</span>
        </div>

      </div>

    </div>
    <v-overlay v-model="isRefreshing" :z-index="100" :absolute="false" contained content-class='spinner-container'
      persistent scrim="#fff" key="loading" no-click-animation>
      <Icon name="eos-icons:loading"></Icon>
    </v-overlay>
    <v-overlay v-model="showFilterMenu" :z-index="100" :absolute="false" contained key="show_filter_menu"
      class="filter-menu-overlay-container" content-class='filter-menu-container' no-click-animation v-on:click:outside="() => {
        showFilterMenu = false;
        filterTimeTemp = filterTime;
      }">
      <div class="filter-menu-content">
        <div class="filter-title">
          {{ $t('DeliveryHistoryComponent.loc_don_giao') }}
        </div>
        <div class="time-filter">
          <div class="item-time-filter" :class="{ 'active': filterTimeTemp == item.value }"
            v-for="item of dataFilterTime" v-on:click="() => { filterTimeTemp = item.value }">
            <Icon name="ic:sharp-radio-button-checked" v-if="filterTimeTemp == item.value"></Icon>
            <Icon name="ic:sharp-radio-button-unchecked" v-else></Icon>
            <span>{{ $t('DeliveryHistoryComponent.' + item.nameKey) }}</span>
          </div>
          <div class="h-stack select-date" v-if="filterTimeTemp == 5">
            <input name="start-day" class="input-custom" id="start-day" type="date" :value="start_date"
              placeholder="{{ $t('DeliveryHistoryComponent.chua_cung_cap') }}"
              :max="moment(end_date ? end_date : new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                start_date = event.target.value;
              }" />
            <Icon name="material-symbols:arrow-forward-rounded"></Icon>
            <input name="end-day" class="input-custom" id="end-day" type="date" :value="end_date"
              placeholder="{{ $t('DeliveryHistoryComponent.chua_cung_cap') }}"
              :min="moment(start_date ? start_date : new Date()).format('YYYY-MM-DD')"
              :max="moment(new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                end_date = event.target.value;
              }" />
          </div>
          <div class="h-stack footer">
            <button class="close" v-on:click="() => {
              showFilterMenu = false;
              filterTimeTemp = filterTime;
            }">
              {{ $t('DeliveryHistoryComponent.dong') }}
            </button>
            <button class="apply" v-on:click="() => {
              showFilterMenu = false;
              if (filterTimeTemp != filterTime || filterTimeTemp == 5) {
                filterTime = filterTimeTemp;
                switch (filterTime as any) {
                  case 0: case null: {
                    start_date = null;
                    end_date = null;
                    break;
                  }
                  case 1: {
                    start_date = moment(new Date()).format(appConst.formatDate.toSave);
                    end_date = moment(new Date()).format(appConst.formatDate.toSave);
                    break;
                  }
                  case 2: {
                    start_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
                    end_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
                    break;
                  }
                  case 3: {
                    start_date = moment().startOf('month').format(appConst.formatDate.toSave);
                    end_date = moment().endOf('month').format(appConst.formatDate.toSave);
                    break;
                  }
                  case 4: {
                    start_date = moment().add(-1, 'months').startOf('month').format(appConst.formatDate.toSave);
                    end_date = moment().add(-1, 'months').endOf('month').format(appConst.formatDate.toSave);
                    break;
                  }
                  case 5: {
                    start_date = start_date ? moment(start_date).format(appConst.formatDate.toSave) : null;
                    end_date = end_date ? moment(end_date).format(appConst.formatDate.toSave) : null;
                    break;
                  }
                  default: break;
                }
              }
              getListDelivery();
            }">
              {{ $t('DeliveryHistoryComponent.ap_dung') }}
            </button>
          </div>
        </div>
      </div>
    </v-overlay>
  </div>
</template>

<script lang="ts" setup>
import { DeliveryService } from '~/services/orderService/deliveryService';
import { appConst, domainImage, formatCurrency, formatNumber, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import moment from 'moment';
import not_found_shipper from '~/assets/image/not-found-shipper.webp'
import { DriverService } from '~/services/driverService/driverService';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
import { RefSymbol } from '@vue/reactivity';

const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

var deliveryService = new DeliveryService();
var driverService = new DriverService();
var mqttService = new MqttService();

var dataFilterTime = [
  {
    value: 0 || null,
    name: "Toàn thời gian",
    nameKey: "toan_thoi_gian",
  },
  {
    value: 1,
    name: "Hôm nay",
    nameKey: "hom_nay",
  },
  {
    value: 2,
    name: "Hôm qua",
    nameKey: "hom_qua",
  },
  {
    value: 3,
    name: "Tháng này",
    nameKey: "thang_nay",
  },
  {
    value: 4,
    name: "Tháng trước",
    nameKey: "thang_truoc",
  },
  {
    value: 5,
    name: "Thời gian khác",
    nameKey: "thoi_gian_khac",
  },
];

var isRefreshing = ref(false);
var notes = ref('');
var listDelivery = ref([] as any);
var totalAmount = ref(0);
var showFilterMenu = ref(false);
var filterTime = ref(null);
var filterTimeTemp = ref(null as any);
var start_date = ref(null as any);
var end_date = ref(null as any);
var webInApp = ref(null as any);
var driver_latitude = ref<any>();
var driver_longitude = ref<any>();

var loadingMore = ref(false);
var loadingMoreTimeout: any;
// var driverWatcherId: any;

var tabIndex = ref<any>(null);
var filter_status = ref<any>(null);

var user_latitude = useState<any>('user_latitude', () => { return null});
var user_longitude = useState<any>('user_longitude', () => { return null });

watch(() => [user_latitude.value, user_longitude?.value], () => {
	driver_latitude.value = user_latitude?.value;
	driver_longitude.value = user_longitude?.value;
});

watch(
  () => [route.hash],
  async (newQuery, oldQuery) => {
    tabIndex.value = route.hash.length ? route.hash.replaceAll('#', '') : 'all'
  },
  { deep: true }
);

watch(
  () => [tabIndex.value],
  async (newQuery, oldQuery) => {
    switch (tabIndex.value) {
      case 'completed':
        filter_status.value = appConst.delivery_status.delivered;
        break;
      case 'canceled':
        filter_status.value = appConst.delivery_status.cancelled;
        break;
      default:
        filter_status.value = null;
        break;
    }
    refreshListDelivery();
  },
  { deep: true }
);

onBeforeMount(() => {
	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;
	});
})

onMounted(async () => {
  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
  tabIndex.value = route.hash?.length ? route.hash.replaceAll("#", '') : 'all';

  driver_latitude.value = user_latitude?.value;
	driver_longitude.value = user_longitude?.value;
  
  console.log(tabIndex.value);
  // getListDelivery();
  // getDriverLocation();
  nuxtApp.$listen(appConst.event_key.refresh_delivery_detail, async () => {
    console.log("refresh")
    await refreshListDelivery();
  })
});

onUnmounted(() => {
  // if ("geolocation" in navigator) {
  //   navigator.geolocation.clearWatch(driverWatcherId);
  // }
  nuxtApp.$unsubscribe(appConst.event_key.user_moving);
});

function getListDelivery() {
  isRefreshing.value = true
  deliveryService.listOfDriver(0, 20, filter_status.value, start_date.value, end_date.value).then((res) => {
    if (res.status == HttpStatusCode.Ok) {
      listDelivery.value = res.body.data;
      totalAmount.value = res.body.total
    }
    isRefreshing.value = false;
  })

}

function refreshListDelivery() {
  isRefreshing.value = true;
  deliveryService.listOfDriver(0, listDelivery.value?.length > 0 ? listDelivery.value?.length : 20, filter_status.value, start_date.value, end_date.value).then((res) => {
    if (res.status == HttpStatusCode.Ok) {
      setTimeout(() => {
        document.getElementById('public_container')?.scrollTo({top: 0, behavior: 'smooth' })
      });
      listDelivery.value = res.body.data;
      totalAmount.value = res.body.total;

      
      isRefreshing.value = false;
    }
    else{
      listDelivery.value = [];
      isRefreshing.value = false;
    }
  })
}

function loadMoreListDelivery() {
  clearTimeout(loadingMoreTimeout);

  loadingMoreTimeout = setTimeout(() => {
    loadingMore.value = true;
    let lastPosition = document.getElementById('last_of_list_delivery_history')?.getBoundingClientRect()?.bottom ?? 0;
    if (lastPosition <= window.innerHeight + 300 && listDelivery.value.length < totalAmount.value) {
      deliveryService.listOfDriver(listDelivery.value.length, 20, filter_status.value, start_date.value, end_date.value).then((res) => {
        if (res.status == HttpStatusCode.Ok) {
          listDelivery.value = [
            ...listDelivery.value,
            ...res.body.data
          ];
          totalAmount.value = res.body.total;
        }
        loadingMore.value = false;
      })
    }
    else {
      loadingMore.value = false;
    }
  }, 1000);

}
function checkFilterActive() {
  if (filterTime.value || (start_date.value && end_date.value)) return true;
  return false;
}

function setDeliveryStatus(delivery: any, newStatus: any) {
  driverService.driverAction({
    delivery_id: delivery.id,
    status: newStatus,
    action: appConst.driver_action.update_delivery_status,
    notes: notes.value,
    latitude: driver_latitude.value,
    longitude: driver_longitude.value
  }).then(res => {
    if (newStatus != appConst.delivery_status.delivered) {
      let messShop = {
        mess: {
          type: 'delivery_update',
          url: delivery.id
        },
        topic: appConst.mqtt_topic.shop.replaceAll(':shop_id', delivery?.order?.shop_id),
      }
      mqttService.publish(appConst.mqtt_topic.shop.replaceAll(':shop_id', delivery?.order?.shop_id), JSON.stringify(messShop))

      let messOrder = {
        mess: {
          type: 'delivery_update',
          url: delivery.id
        },
        topic: appConst.mqtt_topic.order.replaceAll(':order_id', delivery?.order?.id),
      }
      mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', delivery?.order?.id), JSON.stringify(messOrder))
    }
    else {
      let messShop = {
        mess: {
          type: 'delivery_update',
          url: delivery.id
        },
        topic: appConst.mqtt_topic.shop.replaceAll(':shop_id', delivery?.order?.shop_id),
      }
      mqttService.publish(appConst.mqtt_topic.shop.replaceAll(':shop_id', delivery?.order?.shop_id), JSON.stringify(messShop))

      let messOrder = {
        mess: {
          type: 'delivery_completed',
          url: delivery?.id
        },
        topic: appConst.mqtt_topic.order.replaceAll(':order_id', delivery?.order?.id),
      }
      mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', delivery?.order?.id), JSON.stringify(messOrder))
    }
    refreshListDelivery();
  })
}

function getDriverLocation() {
  // if ("geolocation" in navigator) {
  //   driverWatcherId = navigator.geolocation.watchPosition(
  //     (position) => {
  //       // console.log(position);
  //       driver_latitude.value = position.coords.latitude;
  //       driver_longitude.value = position.coords.longitude;
  //       // setLocationLeafletMarker(position.coords.latitude, position.coords.longitude);
  //     },
  //     (error) => {

  //       driver_latitude.value = appConst.defaultCoordinate.latitude;
  //       driver_longitude.value = appConst.defaultCoordinate.longitude;
  //     },
  //     {
  //       enableHighAccuracy: false,
  //       maximumAge: 0,
  //       timeout: 1000
  //     }
  //   );
  // }
}

</script>

<style lang="scss" src="./DeliveryHistoryStyles.scss"></style>