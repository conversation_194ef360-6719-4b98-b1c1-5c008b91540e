.custom-select-overlay-container {
  overflow: hidden;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }

  & .custome-select-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    // height: 100%;
    border-radius: 10px 10px 0 0;
    animation: slide-up 0.5s ease;
    font-size: 17px;
    display: flex;
    flex-direction: column;
    height: fit-content;
    max-height: 60dvh;
    border-radius: 10px 10px 0 0;
    max-width: var(--max-width-content-view-1024) !important;
    background: white;

    & > .title-select {
      display: flex;
      gap: 5px;
      text-transform: uppercase;
      color: var(--primary-color-1);
      font-size: 17px;
      padding: 7px 7px 7px 15px;
      align-items: center;
      font-weight: 800;
      line-height: 1;

      & svg {
        font-size: 25px;
      }

      & > .close-button {
        color: #a8a7a7;
        display: flex;
        margin-left: auto;
      }
    }
    & .search-select-options {
      padding: 0 10px;
      display: flex;

      & > .search-input-group {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        background: #f5f6fa;
        padding: 5px 10px;
        flex: 1;
        border-radius: 5px;
        gap: 5px;

        & > input {
          color: #545454;
          font-weight: 600;
          outline: none;

          &::placeholder {
            color: #a8a7a7;
            font-weight: 400;
          }
        }

        & > svg {
          color: var(--primary-color-1);
        }
      }
    }
    & .select-options-container {
      margin: 5px 15px;
      padding: 7px 15px;
      display: flex;
      flex-direction: column;
      border-radius: 5px;
      box-shadow: 0 0 20px 0 rgb(0, 0, 0, 20%);
      height: auto;
      overflow: auto;
      background: linear-gradient(
        to bottom right,
        #d0ffbc66 10%,
        #ffffff66 50%,
        #e8ffde66 90%
      );

      & > .item-option {
        padding: 10px 10px;
        text-align: left;
        color: #8c8c8c;
        font-weight: 400;
        animation: none !important;
        border-bottom: thin solid #f5f6fa;

        & > span {
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      & > .item-option.selected {
        color: var(--primary-color-1);
        font-weight: 800;
      }

      &.none-list {
        align-items: center;
        justify-content: center;
        font-style: italic;
        font-weight: 400;
        color: #8c8c8c;
        height: 200px;
      }
    }

    & .select-footer {
      display: flex;
      padding: 7px 15px;
      justify-content: flex-end;
      gap: 5px;

      & > button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 35px;
        min-width: 100px;
        padding: 0 10px;
        border-radius: 2em;
        flex: 1;
        color: white;
        background: var(--primary-color-1);
        font-weight: 700;
      }

      & > button.unselect-all{
        background: white;
        border: thin solid var(--primary-color-2);
        color: var(--primary-color-2);
      }
    }
  }
}

.custom-shop-select{

  & .select-options-container{
    padding: 10px 0 !important;
  }
  & .item-option:hover{
    background-color: color-mix(in srgb, var(--primary-color-1) 10%, transparent);
  }

  & .item-option.selected{
    background-color: color-mix(in srgb, var(--primary-color-1) 90%, transparent);
    color: white !important;
  }
  & .custom-shop-select-option{
    display: flex;
    gap: 5px;
    align-items: center;

    & span{
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      flex: 1;
      gap: 5px;

      & > em{
        color: white;
        font-size: 15px;
        background-color: var(--primary-color-2);
        text-align: center;
        width: fit-content;
        padding: 0 10px;
        border-radius: 2em;
      }
    }
  }
}
