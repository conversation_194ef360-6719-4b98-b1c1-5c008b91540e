<script setup lang="ts">
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import category_temp from "~/assets/imageV2/shop-bg.jpg";

import { useI18n } from 'vue-i18n';
import { domainImage, formatCurrency, showTranslateProductName } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import { fa } from 'vuetify/locale';

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const route = useRoute();
const props = defineProps({
	_key: null,
	categoryData: null,
	indexCategory: null,
	shopData: null,
	view_mode: null,
	preview_mode: null
});

const emit = defineEmits(['product_click', 'add_to_cart_click']);

var targetItemResult = ref<HTMLElement>()
var isVisible = ref(false);
const observer = ref<IntersectionObserver | null>(null);
onMounted(() => {
	console.log(props.categoryData)
	if (process.client) {
		observer.value = new IntersectionObserver(
			
			([entry], observer) => {
				const el = entry.target;
				if (entry.isIntersecting) {
					el.classList.add('animate-in');
					el.classList.remove('animate-out');
				} else {
					el.classList.remove('animate-in');
					el.classList.add('animate-out');
				}
			},

		), {
			threshold: 0.5,
		};

		if (targetItemResult.value) {
			observer.value.observe(targetItemResult.value);
		}
	}
	else {
		isVisible.value = true;
	}
});
</script>
<template>
	<div class="category-products-container-1" ref="targetItemResult" :key="props._key" :id="props._key" :style="{
		'animation-name': (isVisible) ? 'fadeInUp' : 'none',
		'animation-duration': '1s',
		'animation-timing-function': 'ease',
	}">
		<slot name="top_header" v-if="$slots.top_header"></slot>
		<div class="category-title container" :id="`category_products_title_${props.categoryData?.id}`" :style="{
			'--category-image': props.categoryData?.profile_picture
				? `url(${domainImage + props.categoryData?.profile_picture})`
				: `url(${category_temp})`,
			'--category-filter': !props.categoryData?.profile_picture ? `none` : `brightness(0.6)`,
		}">
			<span>{{ showTranslateProductName(props.categoryData) }}
				<!-- <em>({{ itemCategory.products.length }})</em> -->
			</span>
		</div>
		<div class="category-products-list container">
			<!-- copilot: component uses Tailwind + Vue TransitionGroup for smooth list/grid toggle -->
			<!-- copilot: prefer transition-group over v-if/v-show -->
			<!-- copilot: bind class dynamically to apply scale/opacity -->
			<!-- copilot: animate using transform and opacity only -->
				
				<ItemProductGridComponent
					:key="`category_${props.categoryData?.id || 'all'}_product_item_${itemProduct.id}`"
					v-for="(itemProduct, indexSelected) in props.categoryData?.products" :animation="true"
					:product-data="JSON.parse(JSON.stringify(itemProduct))"
					:shop-data="JSON.parse(JSON.stringify(props.shopData))" :product-index="indexSelected"
					:view_mode="props.view_mode"
					v-on:add_to_cart_click="() => {
						emit('add_to_cart_click', JSON.parse(JSON.stringify(itemProduct)))
					}"
					:preview_mode = props.preview_mode
					>
				</ItemProductGridComponent>
			
		</div>
	</div>


</template>

<style lang="scss">
@import url('./ItemCategoryProductStyles.scss');
</style>
