<template>
	<VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false" :overlay-behavior="'persist'"
		content-class="v-stack modal-content-container new-delivery-noti-container" v-model="showNewDeliveryModal"
		v-on:closed="() => {
			close();
			showNewDeliveryModal = false
		}" contentTransition="vfm-fade">
		<div class="new-delivery-detail-container" v-if="deliveryData && !isRefreshing">
			<div class="header">
				<button :disabled="isUpdating" v-on:click="close()">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</div>
			<div class="new-delivery-detail-content">
				<div class="primary-info">
					<span class="short-code">#{{ deliveryData.short_code }}</span>
					<span class="price">
						{{ formatCurrency(deliveryData.total_amount) }}
					</span>
					<div class="pickup-time-distance">
						<span class="distance">{{ $t('NewDeliveryNotiComnponent.khoang_cach') }} <em>{{
							deliveryData.distance }} km</em></span>
						<span class="pickup-time">{{ $t('NewDeliveryNotiComnponent.thoi_gian_lay_hang') }}
							<em>
								{{ deliveryData.pickup_time
									? moment(deliveryData.pickup_time, 'YYYY-MM-DD HH:mm').format('DD/MM/YYYY HH:mm')
									: $t('NewDeliveryNotiComnponent.bay_gio')
								}}</em></span>
					</div>
				</div>
				<div class="start-place">
					<span class="name">
						{{ deliveryData.name_from }}
					</span>
					<span class="address">{{ deliveryData.address_from }}</span>
					<Icon name="fa6-solid:circle-chevron-down"></Icon>
				</div>
				<div class="destination-place">
					<span class="name">
						{{ deliveryData.name_to }}
					</span>
					<span class="address">{{ deliveryData.address_to }}</span>
				</div>
			</div>
			<div class="footer">
				<button class="decline-button" :disabled="isUpdating" v-on:click="decline()">
					{{ $t('NewDeliveryNotiComnponent.khong_nhan') }}
				</button>
				<button class="accept-button" :disabled="isUpdating" v-on:click="() => {
					accept()
				}">
					{{ $t('NewDeliveryNotiComnponent.nhan_don') }}

					<vue-countdown v-if="time_remaining > 0" class="time-remaining" :time="time_remaining * 1000"
						v-slot="{ minutes, seconds }" v-on:end="() => {
							time_remaining = 0;
							decline()
						}" v-on:progress="({ totalSeconds }) => { }">
						{{ moment().minutes(minutes).format('m') }}:{{ moment().seconds(seconds).format('ss') }}
					</vue-countdown>
				</button>
			</div>
		</div>
		<div class="new-delivery-detail-container not-found" v-if="!deliveryData && !isRefreshing">
			<button class="close-button" v-on:click="() => { close() }">
				<Icon name="clarity:times-line" size="25"></Icon>
			</button>
			<img :src="not_found_delivery" alt="" />
			<span>{{ $t('NewDeliveryNotiComnponent.khong_tim_thay_don_hang') }}</span>
		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>
import moment from 'moment';
import not_found_delivery from '~/assets/image/not-found-shipper.webp'
import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName } from "~/assets/AppConst";
import { VueFinalModal } from 'vue-final-modal';
import VueCountdown from '@chenfengyuan/vue-countdown';
import { toast } from 'vue3-toastify';
import { DeliveryService } from '~/services/orderService/deliveryService';
import { DriverService } from '~/services/driverService/driverService';
import appRoute from '~/assets/appRoute';
import { ref } from 'vue';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';

const { t } = useI18n();
// const emit = defineEmits(['close', 'submit']);
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

var props = defineProps({
	delivery_id: null,
	new: true as any
});
var emit = defineEmits(['close'])

var deliveryService = new DeliveryService();
var driverService = new DriverService();
var mqttService = new MqttService();

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

var deliveryData = ref()
var showNewDeliveryModal = ref(false);
var isUpdating = ref(false);
var isRefreshing = ref(true);
var time_remaining = ref(0);
var read = ref(props.new == true ? false : true);
var countdown: any;
var timeoutDelivery = ref(5 * 60);
var driver_latitude = useState<any>(() => { return user_latitude.value });
var driver_longitude = useState<any>(() => { return user_longitude.value });
var driverWatcherId: any;

watch(() => [user_latitude.value, user_longitude?.value], () => {
	driver_latitude.value = user_latitude?.value;
	driver_longitude.value = user_longitude?.value;
})

onBeforeMount(() => {
	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;

	});
})

onMounted(async () => {
	console.log('new delivery mount');
	showNewDeliveryModal.value = true;
	read.value = true;
})

onUpdated(async () => {
	// if (deliveryData.value?.id != props.delivery_id) {
	// 	await getDeliveryInfo();
	// 	time_remaining.value = 0;
	// 	setTimeout(() => {
	// 		time_remaining.value = 5 * 60;	
	// 	}, 1000);
	// }
	console.log('new delivery update');

	// await getDriverLocation();
	await getDeliveryInfo();

	showNewDeliveryModal.value = true;
	read.value = true;

})

onUnmounted(() => {
	// if ('geolocation' in navigator) {
	// 	navigator.geolocation.clearWatch(driverWatcherId)
	// }
	nuxtApp.$unsubscribe(appConst.event_key.user_moving);
})

function getDeliveryInfo() {
	isRefreshing.value = true;
	deliveryService.detail(props.delivery_id).then(res => {

		if (res.status == HttpStatusCode.Ok) {
			deliveryData.value = res.body.data;
			deliveryData.value.special_require = deliveryData.value?.special_require?.length ? JSON.parse(deliveryData.value.special_require) : null;
			deliveryData.value.package_info = deliveryData.value?.package_info?.length ? JSON.parse(deliveryData.value.package_info) : null;
			timeoutDelivery.value = (deliveryData.value?.pending_time ?? 5) * 60;
			let timeTemp = moment().diff(moment(deliveryData.value.updated_at, 'YYYY-MM-DD HH:mm:ss'),'seconds');
			if (timeTemp > timeoutDelivery.value) {
				decline();
			}
			else {
				time_remaining.value = timeoutDelivery.value - timeTemp;
			}
		}
		else {
			deliveryData.value = null;
		}
		isRefreshing.value = false;
	})
}
function decline() {
	isUpdating.value = true;
	driverService.driverAction({
		delivery_id: deliveryData.value.id,
		action: 'update_delivery_status',
		status: appConst.delivery_status.cancelled,
		latitude: driver_latitude.value ?? null,
		longitude: driver_longitude.value ?? null,
	}).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			toast.info(t('NewDeliveryNotiComnponent.da_tu_choi_nhan_don'));
			router.push(appRoute.DeliveryHistoryComponent);
			publishDeliveryUpdate()
			close()
		}
		else {
			toast.error(res.body?.message ?? t('NewDeliveryNotiComnponent.co_loi_xay_ra'))
		}
		isUpdating.value = false;
	}).catch(() => {
		toast.error(t('NewDeliveryNotiComnponent.co_loi_xay_ra'));
		isUpdating.value = false;
	})
}
function accept() {
	isUpdating.value = true;
	driverService.driverAction({
		delivery_id: deliveryData.value.id,
		action: 'update_delivery_status',
		status: appConst.delivery_status.confirmed,
		latitude: driver_latitude.value,
		longitude: driver_longitude.value,
	}).then(res => {
		if (res.status == HttpStatusCode.Ok) {

			router.push(appRoute.DeliveryDetailComponent.replaceAll(':delivery_id', deliveryData.value.id));
			publishDeliveryUpdate()
			close()
		}
		else {
			toast.error(res.body?.message ?? t('NewDeliveryNotiComnponent.co_loi_xay_ra'))
		}
		isUpdating.value = false;
	}).catch(() => {
		toast.error(t('NewDeliveryNotiComnponent.co_loi_xay_ra'));
		isUpdating.value = false;
	})
}

function close() {
	emit('close');
}

function getDriverLocation() {
	if ("geolocation" in navigator) {
		navigator.geolocation.getCurrentPosition(
			async (position) => {
				driver_latitude.value = position.coords.latitude;
				driver_longitude.value = position.coords.longitude;
			},
			(error) => {
				driver_latitude.value = appConst.defaultCoordinate.latitude;
				driver_longitude.value = appConst.defaultCoordinate.longitude;
			}
		), {
			enableHighAccuracy: false,
			maximumAge: 0,
			timeout: 1000
		};
	}
	else {
		driver_latitude.value = appConst.defaultCoordinate.latitude;
		driver_longitude.value = appConst.defaultCoordinate.longitude;
	}
}

function publishDeliveryUpdate() {
	let messShop = {
		mess: {
			type: 'delivery_update',
			url: deliveryData.value?.id
		},
		topic: appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id),
	}
	mqttService.publish(appConst.mqtt_topic.shop.replaceAll(':shop_id', deliveryData.value?.order?.shop_id), JSON.stringify(messShop))

	let messOrder = {
		mess: {
			type: 'delivery_update',
			url: deliveryData.value?.id
		},
		topic: appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id),
	}
	mqttService.publish(appConst.mqtt_topic.order.replaceAll(':order_id', deliveryData.value?.order?.id), JSON.stringify(messOrder))
}
</script>

<style lang="scss" src="./NewDeliveryNotiStyles.scss"></style>