import { environment } from "./environment/environment";
import { appRoute } from "./appRoute";
import ISO6391 from "iso-639-1";
import { useI18n } from "vue-i18n";
import CryptoJS from "crypto-js";
import { user_status } from "~/components/chatManage/ChatDTO";
// import marker_location_icon from "./image/marker-location.png";
// import blue_marker_location_icon from "./image/blue-dot-location.png";
// import { environment } from "./environment/environment.prod";
export const domain = environment.domain;
export const api_url = environment.api_url;
export const domainImage = environment.domainImage;
export const baseLogoUrl = environment.baseLogoUrl;
var year = new Date().getFullYear();

export const appConst = {
  apiURL: {
    login: api_url + "/api/v1/client/login",
    loginDriver: api_url + "/api/v1/client/login/driver",
    forgotPasswordClient: api_url + "/api/v1/client/forget_password",
    checkCodeConfirm: api_url + "/api/v1/confirm_code/check_code",
    changeForgetPasswordClient: api_url + "/api/v1/client/change_password",
    updatePasswordClient: api_url + "/api/v1/client/update/password",
    logout: api_url + "/api/v1/logout",
    listUser: api_url + "/api/v1/user/list?limit=10&offset=0",
    updateUser: api_url + "/api/v1/client/update",
    registerClient: api_url + "/api/v1/client/register",
    changeEmail: api_url + "/api/v1/client/change_email",

    provinces: api_url + "/api/v1/client/address/province/list",
    provinceDetail: api_url + "/api/v1/client/address/province",
    districts: api_url + "/api/v1/client/address/district/list",
    districtDetail: api_url + "/api/v1/client/address/district",
    wards: api_url + "/api/v1/client/address/ward/list",
    wardDetail: api_url + "/api/v1/client/address/ward",
    // geocodeLatLngToAddress: 'https://api-dev-1.nomnomland.net/api/v1/geocode/search',
    geocodeLatLngToAddress: api_url + "/api/v1/client/public/search_by_latlong",
    geocodeAddressToLatLng: api_url + "/api/v1/client/public/search_by_address",

    getOTP: api_url + "/api/v1/client/get_otp",
    confirmOTP: api_url + "/api/v1/client/confirm_otp",

    checkExist: api_url + "/api/v1/client/check_exist",
    profileDetail: api_url + "/api/v1/client/user/infor",
    userDetail: api_url + "/api/v1/client/user/detail/",
    switchLanguage: api_url + "/api/v1/client/user/language",
    listNotify: api_url + "/api/v1/client/user/notify",
    notifyDetail: api_url + "/api/v1/client/user/detail/notify/:id",

    shopList: api_url + "/api/v1/shop/list",
    myShop: api_url + "/api/v1/shop/my_shop",
    detailShop: api_url + "/api/v1/shop/detail",
    superMenuShop: api_url + "/api/v1/shop/super_menu",
    createShop: api_url + "/api/v1/shop/create",
    editShop: api_url + "/api/v1/shop/update",
    updateShopSettings: api_url + "/api/v1/shop/setting",

    getProductsByShopId: api_url + "/api/v1/shop/get_products_by_shop_id",
    searchProductsInShop: api_url + "/api/v1/shop/search_products_in_shop",

    // Stock Management API endpoints
    stockImport: api_url + "/api/v1/stock/import",
    stockExport: api_url + "/api/v1/stock/export",
    stockWaste: api_url + "/api/v1/stock/waste",
    stockHistory: api_url + "/api/v1/stock/history",
    stockDailySummary: api_url + "/api/v1/stock/daily-summary",
    stockMyShopsSummary: api_url + "/api/v1/stock/my-shops-summary",
    searchProductsInShopClient:
      api_url + "/api/v1/client/public/search_products_in_shop",
    getProductWithCategoryInShop:
      api_url + "/api/v1/shop/products_with_category/:shop_id",

    categoryList: api_url + "/api/v1/category/list",
    categoryListLevel: api_url + "/api/v1/category/list_level",
    categoryListByShopId: api_url + "/api/v1/category/list_by_shop_id",
    createCategory: api_url + "/api/v1/category/create",
    editCategory: api_url + "/api/v1/category/update",
    removeCategory: api_url + "/api/v1/category/remove",
    updateListProduct: api_url + "/api/v1/category/update_list_product",
    detailCategory: api_url + "/api/v1/category/detail",
    updateIndexCategory: api_url + "/api/v1/category/update_index",

    insertImageZalo: api_url + "/api/v1/client/update/image/driver",
    insertImage: api_url + "/api/v1/image/insert",
    deleteImage: api_url + "/api/v1/image/delete",

    filter: api_url + "/api/v1/client/public/filter",
    dashboard: api_url + "/api/v1/client/public/dashboard",
    dashboardVideo: api_url + "/api/v2/video/list",
    reviewVideo: api_url + "/api/v2/video/list_review",

    productSearchSuggest: api_url + "/api/v1/client/public/suggest_search",
    reels: api_url + "/api/v1/client/public/reels",
    publicOrderDetail: api_url + "/api/v1/client/public/order/detail/:id",
    publicProductDetail: api_url + "/api/v1/client/public/product/detail/",
    listShopInteracted: api_url + "/api/v1/client/public/user/shops_interacted",

    productDetail: api_url + "/api/v1/product/detail/",
    relatedProduct: api_url + "/api/v1/product/relate/:id",
    productSearchBase: api_url + "/api/v1/product/search_base",

    addProductFromSystem: api_url + "/api/v1/product/add_product_from_system",
    createPrivateProduct: api_url + "/api/v1/product/create_private_product",
    updateProduct: api_url + "/api/v1/product/update",
    deleteProduct: api_url + "/api/v1/product/delete",

    orderCreate: api_url + "/api/v1/order/create",
    orderUpdate: api_url + "/api/v1/order/update",
    orderByShopId: api_url + "/api/v1/order/list_order_by_shop_id",
    orderByCustomerId: api_url + "/api/v1/order/list_order_by_customer_id",
    orderByCustomerIdAndShopId: api_url + "/api/v1/order/list_order",
    orderDetail: api_url + "/api/v1/order/detail",
    countOrderByStatus: api_url + "/api/v1/order/count_order_by_status",

    deliveryCheckPrice: api_url + "/api/v1/delivery/check_price",
    deliveryCheckPriceFromPartner: api_url + "/api/v2/delivery/check_price",
    deliveryFindShipper: api_url + "/api/v1/delivery/find_driver",
    deliveryDetail: api_url + "/api/v1/delivery/detail/:delivery_id",
    deliveryDetailByOrderId:
      api_url + "/api/v1/delivery/detail_by_order_id/:order_id",
    deliveryDetailV2By: api_url + "/api/v2/delivery/detail",
    deliveryUpdateDriverId: api_url + "/api/v1/delivery/update_driver_id",
    deliveryCreate: api_url + "/api/v1/delivery/create",
    deliveryCreateV2: api_url + "/api/v2/delivery/create",
    deliveryCancelV2: api_url + "/api/v2/delivery/cancel",
    deliveryUpdate: api_url + "/api/v1/delivery/update",
    deliveryDriverUpdate: api_url + "/api/v1/delivery/driver/update",
    deliveryListOfDriver: api_url + "/api/v1/delivery/list_by_driver",

    driverAction: api_url + "/api/v1/driver/action",

    businessTypeList: api_url + "/api/v1/business_type/list",

    addKeyHashZalo: api_url + "/api/v1/client/public/add_zalo_hey_hash",

    insertOrUpdateFCMToken: api_url + "/api/v1/token/insert_or_update",
    deleteToken: api_url + "/api/v1/token/delete_token",
    deleteAccount: api_url + "/api/v1/client/delete_account",

    quotationReadFile: api_url + "/api/v1/quotation/read_excel",
    quotationAdd: api_url + "/api/v1/quotation/add",
    quotationUpdate: api_url + "/api/v1/quotation/update",
    quotationRequest: api_url + "/api/v1/quotation/request",
    quotationList: api_url + "/api/v1/quotation/list",
    quotationDetail: api_url + "/api/v1/quotation/detail",
    quotationFilter: api_url + "/api/v1/quotation/filter",
    materialSaveImportHistory: api_url + "/api/v1/material/import_history",

    supplierList: api_url + "/api/v1/supplier/list",
    supplierAdd: api_url + "/api/v1/supplier/add",

    materialList: api_url + "/api/v1/material/list",
    materialAdd: api_url + "/api/v1/material/add",

    agentListShop: api_url + "/api/v1/agent/list_shop",
    agentShopDetail: api_url + "/api/v1/agent/shop_detail",
    ordersByAgent: api_url + "/api/v1/agent/shop_orders",
    agentCreateShop: api_url + "/api/v1/agent/create_shop",
    agentUpdateShop: api_url + "/api/v1/agent/update_shop",
    agentDisableShop: api_url + "/api/v1/agent/disable_shop",
    agentDeleteShop: api_url + "/api/v1/agent/delete_shop",
    agentDuplicateShop: api_url + "/api/v1/agent/copy_shop",
    agentListOrder: api_url + "/api/v1/agent/list_order_by_agent",
    agentOrderDetail: api_url + "/api/v1/agent/order_detail",
    agentOrderUpdate: api_url + "/api/v1/agent/order_update",
    agentAddProductFromSystem:
      api_url + "/api/v1/agent/add_product_from_system",
    agentCreatePrivateProduct: api_url + "/api/v1/agent/create_private_product",
    agentUpdateProduct: api_url + "/api/v1/agent/update_product",
    agentUpdateListProduct:
      api_url + "/api/v1/agent/category_update_list_product",

    ratingDetail: api_url + "/api/v1/rating/detail/:id",
    listRatingByObject: api_url + "/api/v1/rating/list_by_object_id",
    listRating: api_url + "/api/v1/rating/list",
    createRating: api_url + "/api/v1/rating/create",
    updateRating: api_url + "/api/v1/rating/update",
    deleteRating: api_url + "/api/v1/rating/delete",
    calcObjectRating: api_url + "/api/v1/rating/calc_object_rating/:object_id",
    myRatingToObject: api_url + "/api/v1/rating/user_rating/:object_id",

    myUserAddressList: api_url + "/api/v1/user_address/my_addresses",
    myUserAddressSetDefault: api_url + "/api/v1/user_address/set_default",
    myUserAddressCreate: api_url + "/api/v1/user_address/create",
    myUserAddressUpdate: api_url + "/api/v1/user_address/update",
    myUserAddressDelete: api_url + "/api/v1/user_address/delete",

    interactionCreateOrDelete: api_url + "/api/v1/interaction/create_or_delete",
    interactionList: api_url + "/api/v1/interaction/list",
    interactionCheck: api_url + "/api/v1/interaction/check",
    interactionIncrementView: api_url + "/api/v1/interaction/increment_view",

    chatListChannel: api_url + "/api/v2/channel/list",
    chatGetChannelByMemberIds: api_url + "/api/v2/channel/get_channel",
    chatListMessage: api_url + "/api/v2/chat/list",
    chatSendMessage: api_url + "/api/v2/chat/send",
    chatUpdateMessage: api_url + "/api/v2/chat/update",
    chatRetractMessage: api_url + "/api/v2/chat/retract",
    chatCountUnreadMessage: api_url + "/api/v2/channel/count_unread",

    listDeliveryPartnerOfShop: api_url + "/api/v2/delivery_partner/shop/list",
    infoDeliveryPartnerOfShop: api_url + "/api/v2/delivery_partner/shop/detail",
    addDeliveryPartnerToShop: api_url + "/api/v2/delivery_partner/shop/add",
    updateDeliveryPartnerToShop: api_url + "/api/v2/delivery_partner/shop/update",
  },
  storageKey: {
    token: "token",
    userInfo: "user_info",
    cart: "cart",
    fcmToken: "fcm_token",
    savedInfo: "saved_info",
    productRecent: "product_recent",
    shopRecent: "shop_recent",
    searchRecent: "search_recent",
    radiusFilter: "radius_filter",
    language: "language",
    stateRestore: {
      HomeComponent: appRoute.HomeComponent,
      ShopProductsComponent: appRoute.ShopProductsComponent,
      MyShopComponent: appRoute.MyShopComponent,
      AroundComponent: appRoute.AroundComponent,
      SearchComponent: appRoute.SearchComponent,
      AgentShopManageComponent: appRoute.AgentShopManageComponent,
    },
    // userLocation: "user_location",
    webInApp: "web_in_app",
    webInAppVersion: "web_in_app_version",
    confirm18Age: "confirm_18_age",
  },
  leafletMapTileUrl: {
    roadmap: "https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=1.5",
    // roadmap: "https://tiles.stadiamaps.com/tiles/outdoors/{z}/{x}/{y}{r}.png",
    hyprid: "https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}&scale=1.5",
    streetmap: "https://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}&scale=1.5",
    // roadmapStadia: "https://tiles.stadiamaps.com/tiles/outdoors/{z}/{x}/{y}{r}.png",
  },

  leafletMapTileOption: {
    attribution:
      "Dữ liệu &copy Google Map " +
      year +
      ' <a href="https://www.google.com/intl/vi-VN_US/help/terms_maps/" target="_blank">Điều khoản sử dụng</a>',
    subdomains: ["mt0", "mt1", "mt2", "mt3"],
    maxZoom: 22,
    maxNativeZoom: 18,
  },
  appToWebAction: {
    setType: 1,
    userLocation: 2,
    logout: 3,
    notificationToken: 4,
    notiClick: 5,
    back: 6,
    newDeliveryNoti: 7,
    loginWithSocial: 8,
    newChatMessage: 9,
    goHome: 10
  },
  webToAppAction: {
    requestNotification: 1,
    requestUserLocation: 2,
    logout: 3,
    requestCheckType: 4,
    share: 5,
    copyToClipboard: 6,
    setLanguage: 7,
    loginWithSocial: 8,
    newChatMessage: 9
  },
  defaultCoordinate: {
    latitude: 12.2404092,
    longitude: 109.1947338,
  },
  formatDate: {
    toSave: "YYYY-MM-DD",
    toShow: "DD/MM/YYYY",
  },
  validateValue: {
    userName: /^[a-zA-Z0-9]([a-zA-Z0-9\_]){7,}$/,
    // phone: /^(0|\+?\d{1,3})\s?[-.\(]?\d{1,4}[\)]?[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/,
    phone: /^(0|\+\d{1,3})[1-9]{1}\d{8}$/,
    email:
      /^([a-zA-Z0-9_\-\/]+)(\.[a-zA-Z0-9_\-\/]+)*@([a-zA-Z0-9\-]+\.)+[a-z]{2,6}$/u,
    password: /^\S*(?=\S{8,})(?=\S*[a-zA-Z])(?=\S*[\d])\S*$/u,
    referral_code: /^[a-zA-Z0-9]([a-zA-Z0-9_]){5,19}$/,
  },

  role_enum: {
    admin: 1,
    admin2: 2,
    user: 6,
    owner: 7,
    agent: 4,
    driver: 5,
  },

  object_type: {
    user: 1,
    product: 2,
    shop: 3,
    category: 4,
    image: 5,
    brand: 6,
    option: 7,
    role: 8,
    order: 9,
    comment_rating: 10,
    message: 11,
    file: 12,
    channel: 13,
    user_address: 14,
  },

  product_type: {
    base_system: 1,
    add_from_system: 2,
    private: 3,
  },

  order_status: {
    waiting: { value: 1, name: "Chờ xác nhận", nameKey: "cho_xac_nhan" },
    confirmed: { value: 2, name: "Đang xử lý", nameKey: "dang_xu_ly" },
    // ready: { value: 3, name: "Sẵn sàng giao", nameKey:"san_sang_giao"},
    taken: { value: 4, name: "Hoàn thành", nameKey: "hoan_thanh" },
    return: { value: 5, name: "Trả hàng", nameKey: "tra_hang" },
    cancel: { value: 6, name: "Từ chối", nameKey: "tu_choi" },
  } as any,

  delivery_status: {
    pending: 1,
    confirmed: 2,
    prepared: 3,
    picked_up: 4,
    in_transit: 5,
    delivered: 6,
    cancelled: 7,
    failed: 8,
  },

  check_exist_enum: {
    user_name: 1,
    email: 2,
    phone: 3,
    referral_code: 4,
  },

  driver_action: {
    set_online: "set_online",
    set_offline: "set_offline",
    update_delivery_status: "update_delivery_status",
    moving: 'moving'
  },

  user_status: {
    online: 1,
    offline: 0,
    busy: 2
  },

  confirm_code_action: {
    forget_password: 1,
    verify_email: 2,
    verify_phone_number: 3,
  },

  quotation_status: {
    request: { value: 1, name: "Đang yêu cầu" },
    reply: { value: 2, name: "Đã phản hổi" },
    save: { value: 3, name: "Đã lưu" },
  },

  event_key: {
    send_request_to_app: "send_request_to_app",
    app_data_loaded: "app_data_loaded",
    search_focus: "search_focus",
    home_click: "home_click",
    cart_change: "cart_change",
    shop_updated: "shop_updated",
    cart_add_animation: "cart_add_animation",
    show_footer: "show_footer",
    logout: "log_out",
    login: "log_in",
    device_rotated: "device_rotated",
    check_password: "check_password",
    user_info_change: "user_info_change",
    refresh_delivery_detail: "refresh_delivery_detail",
    new_chat_message: "new_chat_message",
    scrolling: 'scrolling',
    share: "share",
    request_listen_mqtt: "request_listen_mqtt",
    change_permission: "change_permission",
    require_login: 'require_login',
    check_unread_message: 'check_unread_message',
    check_delivery_update: 'check_delivery_update',
    refresh_home: 'refresh_home',
    user_moving: 'user_moving',
    check_user_notification: 'check_user_notification'
  },
  delivery_cancel_reason:[
    "user_incorrect_pickup",
    "user_incorrect_dropoff",
    "user_not_input_promotion",
    "user_incorrect_time_delivery",
    "user_incorrect_recipient",
    "user_no_driver_accept",
    "user_pickup_is_so_far_from_driver",
    "user_driver_asked_cancel",
    "user_driver_takes_too_time_to_pickup",
    "user_no_item",
    "user_user_change_driver",
    "user_user_change_vehicle",
    "user_user_use_another_app",
    "user_incorrect_item_description",
    "user_incorrect_special_requests",
    "user_incorrect_payment_method",
    "user_package_too_large",
    "user_driver_collect_extra_fee",
    "user_driver_behavior",
    "user_incorrect_vehicle",
    "user_user_wrong_address"
  ],
  notification_type: {
    // thông báo cho shop
    order_new: "order_new",
    order_failed: "order_failed",
    order_cancel: "order_cancel",

    // thông báo cho khách hàng
    order_status: "order_status",
    order_status_in_process: "order_status_in_process",
    order_status_ready_to_deliver: "order_status_ready_to_deliver",
    order_status_done: "order_status_done",
    order_status_cancel: "order_status_cancel",

    // thông báo cho agent
    agent_order_new: "agent_order_new",
    agent_order_failed: "agent_order_failed",
    agent_order_cancel: "agent_order_cancel",

    // thông báo cho shipper
    delivery_new: "delivery_new",
    delivery: "delivery",
    delivery_cancel: "delivery_cancel",

    // thông báo có tin nhắn
    message_new: "message_new",
    mesage_new: "mesage_new",
    shop_message_new: "shop_message_new",
    shop_mesage_new: "shop_mesage_new",
    agent_message_new: "agent_message_new",
  },

  v_currency_option: {
    valueAsInteger: true,
    autoDecimalMode: false,
    precision: 2,
    valueRange: { min: 0 },
    allowNegative: false,
  },

  graph_hopper_id: "89753dab-2904-4103-b004-c71db68a2019",
  urlOSRMv1: "https://osrm.remagan.com/route/v1",

  langCodes: ISO6391.getAllCodes(),
  max_language_shop: 5,

  defaultLanguage: "vi",
  flag_icons: {
    vi: "twemoji:flag-vietnam",
    en: "twemoji:flag-united-kingdom",
    es: "twemoji:flag-spain",
    fr: "twemoji:flag-france",
    de: "twemoji:flag-germany",
    zh: "twemoji:flag-china",
    ja: "twemoji:flag-japan",
    ru: "twemoji:flag-russia",
    ko: "twemoji:flag-south-korea",
  } as any,
  image_size: {
    max: 10 * 1000 * 1000, // MB*KB*Byte
  },
  max_text_short: 500,
  max_text_long: 5000,

  markerCustom: {
    defaultIcon: {
      // url: "./image/marker-location.png",
      size: [30, 40] as any,
      rotatePosition: "bottom center",
      class: "leaflet-marker-location",
    },
    rotatedIcon: {
      // url: "./image/blue-dot-location.png",
      size: [55, 55] as any,
      rotatePosition: "center 75%",
      class: "leaflet-marker-location",
    },
    driverIcon: {
      // url: "./image/driver-marker.png",
      size: [20, 40] as any,
      rotatePosition: "center 50%",
      class: "leaflet-driver-marker-location",
    },
  },
  rating_text_key: {
    1: "rat_khong_hai_long",
    2: "khong_hai_long",
    3: "tam_on",
    4: "tot",
    5: "tuyet_voi",
  } as any,
  defaultMaxRate: 5,

  interactionType: {
    like: 1,
    follow: 2,
  },

  provider_name: {
    google: "GOOGLE",
    zalo: "ZALO",
    apple: "APPLE"
  },

  provider_img_domain: [
    'googleusercontent',
    'zadn.vn'
  ],

  productImageMaxAmount: 4,
  orderImageMinAmount: 1,
  orderImageMaxAmount: 6,
  defaultMaxImageAmout: 6,

  mqtt_topic: {
    chat: 'remagan_chat/:channel_id',
    delivery: 'remagan_delivery/:delivery_id',
    shop: 'remagan_shop/:shop_id',
    order: 'remagan_order/:order_id'
  },

  time_units: [
    // {
    //   value: 'second',
    //   key: 'giay',
    // },
    {
      value: 'minute',
      key: 'phut',
    },
    {
      value: 'hour',
      key: 'gio',
    },
    {
      value: 'day',
      key: 'ngay',
    },
    // {
    //   value: 'month',
    //   key: 'thang',
    // },
    // {
    //   value: 'year',
    //   key: 'nam',
    // }
  ]
};

export var appDataStartup = {
  listProvince: [],
  listDistrict: [],
  listWard: [],
  listCategory: [],
  listBusinessType: [],
};

export const currencySign = {
  vi_VN: {
    code: "vi-VN",
    name: "Vietnam Dong (VND)",
    currencySign: "VND",
  },
  en_AU: {
    code: "en-AU",
    name: "Australian Dollar (AUD)",
    currencySign: "AUD",
  },
  en_US: {
    code: "en-US",
    name: "United States Dollar (USD)",
    currencySign: "USD",
  },
} as any;

export function formatCurrency(price: any, locale?: any) {
  return Intl.NumberFormat(locale ? currencySign[locale].code : "vi-VN", {
    style: "currency",
    currency: locale ? currencySign[locale].currencySign : "VND",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
}
// export const { format: formatCurrency } = Intl.NumberFormat('vi-VN', {
//     currency: 'VND',
//     // // currencySign: 'VND',
//     style: 'currency',
// });

export const { format: formatNumber } = Intl.NumberFormat("vi-VN", {
  // currency: 'VND',
  // // currencySign: 'VND',
  // style: 'currency',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});
export function nonAccentVietnamese(string: string) {
  return string
    .normalize("NFD")
    .toLowerCase()
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/đ/g, "d");
}
export const appFireBaseConfig = environment.firebaseConfig;

export const zaloConfig = environment.zaloConfig;

export function filterLanguage(item: any, queryText: any, itemText: any) {
  var nativeName = ISO6391.getNativeName(item).toLowerCase();
  var name = ISO6391.getName(item).toLowerCase();
  var query = queryText.toLowerCase();

  return (
    item.includes(query) || name.includes(query) || nativeName.includes(query)
  );
}

export function showTranslateProductName(product: any) {
  if (product.translation?.length) {
    let indexTranslation = product.translation?.findIndex(function (
      itemTranslation: any
    ) {
      return itemTranslation.language_code == useNuxtApp().$i18n.locale.value;
    });
    return indexTranslation != -1 &&
      product.translation?.[indexTranslation].name?.length
      ? product.translation?.[indexTranslation].name
      : product.name;
  }
  return product.name;
}
export function showTranslateProductDescription(product: any) {
  if (product.translation?.length) {
    let indexTranslation = product.translation?.findIndex(function (
      itemTranslation: any
    ) {
      return itemTranslation.language_code == useNuxtApp().$i18n.locale.value;
    });
    return indexTranslation != -1 &&
      product.translation[indexTranslation].description?.length
      ? product.translation[indexTranslation].description
      : product.notes;
  }
  return product.notes;
}

export const dayInWeek = [
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
  "sunday",
];

export function formatOpeningHours(hoursObj: any, return_arr = false) {
  function formatHours(hours: any) {
    return hours
      .map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`)
      .join(" & ");
  }
  const { t } = useI18n();

  const result = [];
  let currentGroup: any = [];
  let currentHours = hoursObj[dayInWeek[0]];

  if (hoursObj) {
    for (let i = 0; i < dayInWeek.length; i++) {
      const day = dayInWeek[i];
      const hours = hoursObj[day];

      if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
        currentGroup.push(t(`DayInWeek.${day}`));
      } else {
        if (currentGroup.length > 1) {
          result.push(
            `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : "--:--"
            }`
          );
        } else {
          result.push(
            `${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : "--:--"
            }`
          );
        }
        currentGroup = [t(`DayInWeek.${day}`)];
        currentHours = hours;
      }
    }
    // Xử lý nhóm cuối cùng
    if (currentGroup.length > 1) {
      result.push(
        `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : "--:--"
        }`
      );
    } else {
      result.push(
        `${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : "--:--"
        }`
      );
    }
  }

  if (return_arr) {
    return result;
  }
  return result.join(", ");
}

export function formatDayInMonth(day: number, lunar_mode = false): string {
  if (useNuxtApp().$i18n.locale.value === "en") {
    const suffixes = ["th", "st", "nd", "rd"];
    const v = day % 100;
    const suffix = suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
    return `${day}${suffix}`;
  }

  if (useNuxtApp().$i18n.locale.value === "vi") {
    if(lunar_mode){
      if(day == 15) return "Rằm"
      return day <= 10 ? `Mùng ${day}` : `Ngày ${day}`;
    }
    return `Ngày ${day}`;
  }

  if (useNuxtApp().$i18n.locale.value === "ru") {
    return `${day}-е`;
  }

  if (useNuxtApp().$i18n.locale.value === "ko") {
    return `${day}일`;
  }

  return day.toString(); // Mặc định nếu không có ngôn ngữ nào khớp
}

export async function encryptPassword(password: string) {
  let secretKey = environment.secret_key_password;

  let key = CryptoJS.enc.Hex.parse(secretKey.split("::")[0]);
  let iv = CryptoJS.enc.Hex.parse(secretKey.split("::")[1]);

  // encrypt the message
  let encrypted = CryptoJS.AES.encrypt(password, key, {
    iv: iv,
    padding: CryptoJS.pad.ZeroPadding,
  }).toString();
  return encrypted;
}

export function validPhone(phone: string) {
  if (phone?.length) {
    let replaceChars = " ._-()/\\*[]";
    for (let i = 0; i < replaceChars.length; i++) {
      phone = phone.replaceAll(replaceChars[i], '');
    }
    return phone;
  }
  return ""
}

export function validNumber(str: any) {
  // console.log(str, isNaN(str));
  // if(!isNaN(str)) return true;
  return /^\d+$/.test(str)
}

export function validDecimal2Digits(str: any) {
  if (typeof str !== "string") return false;

  // Loại bỏ khoảng trắng thừa
  str = str.trim();

  // Thay dấu ',' thành '.' để đồng nhất cách xử lý
  str = str.replaceAll(",", ".");

  // Loại bỏ tất cả ký tự không phải số hoặc dấu '.'
  str = str.replaceAll(/[^0-9.]/g, "");

  // Nếu chuỗi chỉ chứa ".", không hợp lệ
  if (str === ".") str = "0.";

  // Nếu có nhiều hơn một dấu ".", không hợp lệ
  if ((str.match(/\./g) || []).length > 1) return false;

  // Kiểm tra có đúng định dạng số thập phân (tối đa 2 số thập phân)
  return /^\d+(\.\d{0,2})?$/.test(str);
}

export function parseDecimal2Digits(str: any): string {
  if (typeof str !== "string") return "";

  // Loại bỏ khoảng trắng thừa
  str = str.trim();

  // Thay dấu ',' thành '.' để đồng nhất cách xử lý và xóa ký tự không hợp lệ
  str = str.replaceAll(",", ".").replaceAll(/[^0-9.]/g, "");

  // Nếu chuỗi chỉ là ".", trả về "0."
  if (str === ".") return "0.";

  // Nếu có nhiều hơn một dấu ".", chỉ giữ dấu đầu tiên
  const parts = str.split(".");
  if (parts.length > 2) {
    str = parts[0] + "." + parts.slice(1).join("");
  }

  // Chỉ cho phép tối đa 2 chữ số sau dấu '.'
  if (parts.length === 2) {
    str = parts[0] + "." + parts[1].slice(0, 2);
  }

  return str;
}

export function deepCompareJsons(obj1: any, obj2: any) {
  if (obj1 === obj2) return true;

  if (typeof obj1 !== "object" || typeof obj2 !== "object" || obj1 === null || obj2 === null) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepCompareJsons(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
}

export function isiOS() {
  const device = useDevice();
  var Apple = device.isApple;
  var Mac = device.isMacOS;
  var iOS = device.isIos;
  return (Apple || Mac || iOS);
}

export function formatNumberToShort(num: any) {
  if (num >= 1e12) return (num / 1e12).toFixed(1).replace(/\.0$/, "") + "T"; // Trillions
  if (num >= 1e9) return (num / 1e9).toFixed(1).replace(/\.0$/, "") + "B";   // Billions
  if (num >= 1e6) return (num / 1e6).toFixed(1).replace(/\.0$/, "") + "M";   // Millions
  if (num >= 1e3) return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";   // Thousands
  return num.toString(); // Numbers less than 1000 remain unchanged
}

export function checkFirstValueNotEmpty(object: any) {
  // Loop through all keys in the object
  for (const key in object) {
    if (object[key]) {
      // Return the first key that has a non-empty value
      return object[key]
    }
  }
  // If no key has a non-empty value, return null
  return null;
}