.delivery-history-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  z-index: 100;

  & > .sticky-header{
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    // flex: 1;
    width: 100%;

    & > .amount-delivery {
      padding: 7px 15px;
      margin-top: 0;
      // position: sticky;
      // top: 50px;
      background: white;
    }

    & .delivery-tab {
      text-transform: none;
      font-weight: 600;
      font-size: 1em;
      color: var(--color-text-note);
      background: white;
      // box-shadow: 0 0 5px #ccc;
      z-index: 1;
      min-height: 50px;
      background: white;
  
      & .delivery-tab-title {
        flex: 1;
        text-transform: none;
        // border-bottom: 2px solid transparent;
        color: #79787d;
        font-weight: 600;
        gap: 5px;
        font-size: 1em;
        background-color: white;
        min-width: auto;
  
        & div.tab-title {
          display: flex;
          align-items: center;
          gap: 5px;
  
          & em {
            border-radius: 2em;
            color: white;
            background: var(--color-button-error);
            min-width: 25px;
            height: 25px;
            font-size: 0.8em;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: normal;
            font-weight: 600;
  
            & > span {
              font-size: 0.8em;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
  
      & .delivery-tab-title.active {
        font-weight: 600;
        // border-bottom: 2px solid var(--primary-color-1);
      }
    }
    & .tab-content-container {
      width: 100%;
      flex: 1;
      display: flex;
  
      & > div {
        width: 100%;
      }
  
      & div.v-window-item {
        padding: 0;
        overflow: auto;
        flex: 1;
        display: flex;
        flex-direction: column;
        width: 100%;
      }
    }
  }

  & .header-right {
    & > .filter-button.active {
      color: var(--primary-color-1);
    }
  }

  & > .delivery-history-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: white;
    width: 100%;
    padding-bottom: 25px;

    

    & > .list-delivery-container {
      display: flex;
      flex-direction: column;

      & .stack-content {
        background: white;
        border: thin solid #ccc;
        border-radius: 10px;
        width: calc(100% - 30px);
        // box-shadow: 0 0 15px rgb(0,0,0,.1);
        margin: 10px auto;
        display: flex;
        flex-direction: column;

        & > .stack-title {
          padding: 0 15px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: bold;
          color: var(--color-text-black);

          & em {
            font-weight: 600;
            font-size: 0.9em;
            color: var(--color-text-note);
          }
        }

        & > .stack-item-content {
          display: flex;
          align-items: flex-start;
          gap: 10px;
          width: 100%;
          padding: 0 15px;

          & > .stack-label {
            display: flex;
            flex-direction: column;
            font-weight: 600;
            overflow: hidden;
            & > span{
              display: flex;
            }
            & > em {
              font-size: 0.8em;
              overflow: hidden;
              text-overflow: ellipsis;
              // white-space: nowrap;
              color: var(--color-text-note);

              & > span:not(:first-child) {
                border-left: thin solid;
                margin-left: 5px;
                padding-left: 5px;
              }
            }
          }

          & > .icon-left {
            font-size: 25px;
            min-width: 25px;
            color: #2d8f7e;
          }

          & > .icon-right {
            margin-left: auto;
            font-size: 25px;
            min-width: 25px;
            color: #818181;
          }

          & > .from-marker {
            color: var(--primary-color-2);
          }

          & > .icon-from-to {
            font-size: 25px;
            color: var(--color-text-note);
          }

          & > .distance {
            color: var(--primary-color-1);
            font-size: 13px;
            font-weight: bold;
            align-self: flex-end;
          }

          & .call-customer {
            color: #5f5e66;
            font-size: 15px;
            padding: 2px 7px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            border: thin solid #ecebf3;
            font-weight: 600;
            white-space: nowrap;
            margin-left: auto;
            width: auto;
            height: 30px;

            & > svg {
              width: 20px;
            }

            & > img {
              border-radius: 50%;
              border: thin solid #ecebf3;
              width: 20px;
            }
          }
        }

        & > .mid-line {
          width: 90%;
          margin: 7px auto;
          height: 1px;
          background: #f1f1f1;
        }
        & > .stack-header {
          padding: 10px;
          border-radius: 10px 10px 0 0;
          margin-bottom: 10px;
          flex-wrap: wrap;

          & > .short-code {
            color: #3f3f3f;
          }

          & .delivery-status {
            font-weight: 600;
            float: right;
            font-style: italic;
            font-size: 15px;
            min-width: 100px;
          }

          & .delivery-created-time{
            color: #818181;
            margin-right: auto;
            font-style: normal;
            font-size: 15px;
          }
        }
        & > .stack-header.pending {
          color: var(--color-text-note);
          background: color-mix(
            in srgb,
            var(--color-text-note) 7%,
            transparent
          );
        }
        & > .stack-header.confirmed {
          color: rgb(0, 162, 255);
          background: color-mix(in srgb, rgb(0, 162, 255) 7%, transparent);
        }
        & > .stack-header.prepared {
          color: rgb(0, 110, 255);
          background: color-mix(in srgb, rgb(0, 110, 255) 7%, transparent);
        }
        & > .stack-header.picked_up {
          color: #2aa792;
          background: color-mix(in srgb, #2aa792 7%, transparent);
        }
        & > .stack-header.in_transit {
          color: #20cb7b;
          background: color-mix(in srgb, #20cb7b 7%, transparent);
        }
        & > .stack-header.delivered {
          color: rgb(39, 179, 69);
          background: color-mix(in srgb, rgb(39, 179, 69) 7%, transparent);
        }
        & > .stack-header.cancelled {
          color: #ff7800;
          background: color-mix(in srgb, #ff7800 7%, transparent);
        }
        & > .stack-header.failed {
          color: rgb(204, 24, 24);
          background: color-mix(in srgb, rgb(204, 24, 24) 7%, transparent);
        }
        & > .stack-footer {
          background: #f5f6fa;
          padding: 10px;
          border-radius: 0 0 10px 10px;
          margin-top: 10px;
          display: flex;
          justify-content: space-between;

          & > .prices {
            display: f;
            flex-direction: column;
            gap: 5px;
            & > .delivery-price,
            .cod-price {
              & > span {
                font-weight: 600;
              }
              & > strong {
                color: var(--primary-color-1);
                font-weight: bold;
                font-size: 17px;
              }
            }
          }

          & > .actions {
            display: flex;
            gap: 5px;

            & > .action-button {
              padding: 3px 10px;
              border-radius: 7px;
              border: thin solid;
              align-self: flex-end;
              font-weight: 600;
              font-size: 13px;
              line-height: normal;
            }

            & > .action-button.view {
              color: #818181;
            }
            & > .action-button.receive {
              color: white;
              border-color: #2aa792;
              background: #2aa792;
            }
          }
        }
      }

      & .loading-more{
        position: sticky;
        bottom: 0;
        width: 100%;
        color: #3f3f3f;
        font-style: italic;
        text-align: center;
      }
    }

    & > .list-delivery-container.none-list{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      
      & > img{
        width: 100%;
        max-width: 300px;
      }

      & > span{
        font-style: italic;
        font-size: 20px;
        color: #5f5e66;
      }
    }

    
  }
}

.filter-menu-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .filter-menu-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    max-width: var(--max-width-content-view-1024);
    transform: translateX(-50%);

    & > .filter-menu-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      text-align: center;
      padding: 10px 10px 20px 10px;

      & > .filter-title {
        font-size: 1.3em;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color-1);
        position: relative;

        & > button {
          position: absolute;
          right: 0;
          color: var(--color-text-black);
          display: flex;
          align-items: center;
          top: 50%;
          font-size: 1.5em;
          transform: translateY(-50%);
          animation: none;
        }
      }

      & > .time-filter {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 10px;
        font-size: 1.3em;
        gap: 10px;

        & > .item-time-filter {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        & > .item-time-filter.active {
          color: var(--primary-color-1);
        }

        & > .select-date {
          width: 100%;
          justify-content: space-evenly;
        }

        & > .footer {
          display: flex;
          justify-content: space-evenly;
          width: 100%;

          & > button {
            width: 35%;
            padding: 5px 10px;
            display: flex;
            border-radius: 10px;
            white-space: nowrap;
            justify-content: center;
            align-items: center;
            color: var(--primary-color-1);
            border: thin solid var(--primary-color-1);
          }

          & > button.apply {
            background-color: var(--primary-color-1);
            color: white;
          }
        }
      }
    }
  }
}
