@media screen and (max-width: 720px) {
  .elegant-menu-container {
    & .categories-content {
      --item-width: 42px !important;
      --item-font-size: 10px !important;
    }

    & .product-info {
      font-size: 25px !important;
    }
    & .product-info {
      & > div:not(.quantity-actions),
      > span {
        margin-top: 5px !important;
      }
    }
    & .product-detail {
      font-size: 20px !important;
    }

    & .item-stack-slide.swiper-slide-prev img {
      transform: translateX(20%) !important;
      // transform-origin: right;
    }
    & .item-stack-slide.swiper-slide-next img {
      transform: translateX(-20%) !important;
      // transform-origin: left;
    }
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0%);
  }
}

.elegant-menu-container {
  width: 100%;
  height: 100% !important;
  flex: 1 1;
  max-width: var(--max-width-content-view) !important;
  /* min-height: inherit; */
  /* border-radius: 10px; */
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.3em;
  overflow: auto;
  position: relative;

  & > .tab-content-container {
    flex: 1;
    display: flex;

    & > div {
      flex: 1;
      width: 100%;
      display: flex;

      & > .v-window-item {
        flex: 1;
        display: flex;
        height: 100%;
      }
    }
  }
  @keyframes shop-name-animation {
    from {
      transform: translateY(100vh);
    }
    to {
      transform: translateY(-100%);
    }
  }
  & .elegant-menu-start {
    display: flex;
    flex: 1;
    background: #f28b14;
    padding: 40px;

    & > .menu-label {
      writing-mode: vertical-rl;
      text-orientation: upright;
      color: white;
      font-size: 20vh;
      line-height: 1;
      height: 100%;
      font-weight: 900;
      position: absolute;
      top: -0.2em;
      left: -0.2em;
    }
    & > .shop-name {
      writing-mode: vertical-rl;
      white-space: nowrap;
      text-orientation: upright;
      color: white;
      font-size: 15vh;
      line-height: 1;
      height: auto;
      font-weight: 900;
      position: absolute;
      top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60%;
      right: 0;
      animation: shop-name-animation 30s 0s linear infinite;
    }

    & .shop-logo {
      border-radius: 50%;
      box-shadow: 0 0 15px rgb(0, 0, 0, 10%);
    }

    & > button {
      position: absolute;
      left: 40px;
      bottom: 40px;
      background: black;
      color: white;
      width: calc(100% - 80px);
      padding: 20px;
      height: auto;
      border-radius: 2em;
      margin: auto auto 0;
    }
  }

  & .elegant-menu-view {
    display: flex;
    flex: 1;
    padding: 10px 0;
    width: 100%;

    & > .title-header {
      position: absolute;
      left: 0;
      justify-content: space-between;
      align-items: flex-start;
      top: 50px;
      transform: translateY(-1em);
      width: 100%;
      padding: 0 5px;
      border: none;
      z-index: 2;
      background: transparent;

      & > .header-left {
        flex: none;
        margin-right: 10px;
        > button {
          background-color: transparent;
          width: 40px;
          height: 40px;
          font-size: 30px;
          color: #4f070d;
          padding: 0px;
        }

        > button.back-button {
          font-size: 40px;
        }
      }

      & > .header-right {
        flex: none;
        margin-left: 10px;
        font-size: 1em;
        gap: 5px;
        position: relative;
        flex-direction: column;
        justify-content: flex-start;

        & > .cart-in-search {
          animation: none;
          width: 40px;
          height: 40px;
          font-size: 30px;
          color: #4f070d;
          position: relative;
          padding: 0px;
          background-color: transparent;

          & > em {
            border-radius: 2em;
            color: white;
            background: var(--color-button-error);
            min-width: 15px;
            width: auto;
            height: 15px;
            padding: 2px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: normal;
            position: absolute;
            bottom: 0px;
            right: 0px;
            line-height: 1;
            font-weight: 500;

            & > span {
              font-size: 0.8em;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        & > button {
          font-size: 35px;
        }
      }
    }

    & > .products-content {
      display: flex;
      width: 100%;
      flex-direction: column;
      z-index: 1;

      & > .carousel-products-container {
        flex: 1;
        display: flex;
        margin-top: auto;
        max-height: 100%;
        // max-height: calc(100% - 100px);

        & > .categories-carousel {
          height: 100%;

          & .item-category-stack-slide {
            background-size: 100% auto;
            background-position: 0 100px;
            height: 100% !important;

            & > .products-carousel {
              height: 100%;
              flex: 1;
            }

            & > .none-products {
              justify-content: center;
              align-items: center;
              text-align: center;
              color: #7f8fa6;
              font-size: 20px;
              padding: 40px;
              animation: slide-up 1s linear;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              height: 100%;

              & > .shop-logo {
                border-radius: 50%;
                box-shadow: 0 0 15px rgb(0, 0, 0, 10%);
              }

              & > .shop-name {
                margin: 10px 0 0;
                color: #f05675;
                font-weight: 700;
              }
            }
          }
        }
      }
    }

    & > .products-content.none-menu {
      justify-content: center;
      align-items: center;
      text-align: center;
      color: #7f8fa6;
      font-size: 20px;
      padding: 40px;
      animation: slide-up 1s linear;

      & > .shop-logo {
        // border-radius: 50%;
        box-shadow: 0 0 15px rgb(0, 0, 0, 10%);
      }

      & > .shop-name {
        margin: 10px 0;
        color: #f05675;
        font-weight: 700;
      }
    }

    & > .categories-content {
      --item-width: 50px;
      --item-font-size: 15px;
      position: absolute;
      // display: flex;
      // flex-direction: column;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      bottom: 20px;
      border-radius: 0 10px 10px 0;
      background: white;
      box-shadow: 4px 0 18px 0px rgba(0, 0, 0, 14%);
      white-space: break-spaces;
      text-align: center;
      z-index: 1;
      box-sizing: border-box;
      max-height: calc(var(--item-width) * 7);
      overflow: hidden;
      font-size: var(--item-font-size);
      display: flex;

      & .category-item {
        width: var(--item-width);
        min-height: var(--item-width);
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        padding: 10px 5px;
        text-transform: uppercase;
        color: #7f8fa6;
        font-weight: 700;
      }
      & .category-item.active {
        background: #f28b14;
        color: white;
      }

      & .category-buttons-carousel {
        height: inherit;
      }
    }

    & .my-carousel {
      width: 100%;
      flex: 1;
      display: flex;
      user-select: none;

      & > .carousel__viewport {
        height: 100%;
        display: flex;
        gap: 10px;

        & img {
          width: 100%;
          height: 100%;
          // max-height: 150px;
          object-fit: cover;
        }
      }

      & .item-stack-slide {
        // flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 275px !important;
        // max-width: 60vw;
        transition: all 0.3s ease-in-out;

        & .product-detail {
          width: 130%;
          max-width: 70vw;
          padding: 0;
          display: flex;
          flex: 1;
          height: auto;
          flex-direction: column;
          justify-content: center;
          transition: all 0.3s ease;
          font-size: 20px;

          & > .categories {
            background-image: url("~/assets/image_12_06_2024/category.png");
            background-size: 100% 100%;
            color: white;
            padding: 5px 80px 5px 25px;
            font-size: 0.8em;
            line-height: normal;
            width: auto;
            max-width: 100%;
            font-family: "Noto Serif Display";
            font-weight: 700;
            transition: inherit;

            & > span {
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          & > .name {
            color: #4f070d;
            font-size: 1em;
            line-height: 1.2;
            font-weight: 900;
            margin-top: 0px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: inherit;
          }

          & > .notes {
            color: #4f070d;
            font-size: 0.75em;
            line-height: 1.2;
            font-weight: 700;
            margin-top: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: inherit;
          }
        }
      }
      & .item-stack-slide.swiper-slide-active {
        & .product-item-container > img {
          width: 120%;
          transform: translateX(0);
          max-height: max-content !important;
        }
      }
      & .item-stack-slide:not(.swiper-slide-active) {
        position: relative;
        scale: 0.65;
        // display: flex;
        & .product-detail {
          opacity: 0;
          height: 30%;
        }
        & .product-item-container {
          height: 70%;
          // position: absolute;
          // top: 20%;
          img {
            // width: 65% !important;
            // scale: 50%;
            pointer-events: none;
          }
        }
        & .product-info {
          // max-height: 0;
          // width: 0;
          opacity: 0;
          transform-origin: 50% 0%;
          // max-height: 0;
          overflow: hidden;
          // padding: 0;
          // margin-top: 0;
          // margin-bottom: 100%;
        }
      }

    }

    & .product-item-container {
      // width: 250px;
      // max-width: 70vw;
      width: 100%;
      height: 100%;
      padding: 10px 0 30px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease-in-out;

      & img {
        width: 100%;
        max-height: max-content !important;
        max-width: auto;
        transition: all 0.3s ease-in-out;
        aspect-ratio: 1;
        border-radius: 50%;
        box-shadow: 0 0 15px rgb(0, 0, 0, 10%);
        object-fit: cover;
        z-index: 2;
        background-color: white;

        @supports not (aspect-ratio: 1) {
          &::before {
            float: left;
            padding-top: 100%;
            content: "";
          }

          &::after {
            display: block;
            content: "";
            clear: both;
          }
        }
      }

      & > .product-info {
        flex: 1;
        scale: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100%;
        box-sizing: border-box;
        border-radius: 0 0 200px 200px;
        margin-top: -150px;
        padding: 150px 5% 10%;
        box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.2);
        background-color: white;
        z-index: 1;
        font-size: 30px;
        position: relative;
        transition: all 0.5s ease-in-out;

        & > .name {
          text-transform: uppercase;
          text-align: center;
          font-family: "Smooch Sans";
          color: #4f070d;
          font-size: 1em;
          line-height: 1.1;
          font-weight: 700;
          transition: inherit;
        }

        & > .price {
          text-align: center;
          font-size: 1em;
          font-weight: 900;
          display: flex;
          // flex-direction: column;
          justify-content: center;
          gap: 10px;
          align-items: center;
          color: #4f070d;
          margin-top: 10px;
          transition: inherit;
          justify-content: center;
          flex-wrap: wrap;

          & > .off {
            font-size: 0.7em;
            font-weight: 600;
            color: #7f8fa6;
            text-decoration: line-through;
          }
        }

        & > .descriptions {
          color: #4f070d;
          font-size: 15px;
          line-height: 1.2;
          font-weight: 700;
          margin-top: 5px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: inherit;
        }

        & > .show-note-button {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 10px;

          & > button {
            border-radius: 2em;
            padding: 5px 15px;
            background-color: #f5f6fa;
            color: #4f070d;
            font-size: 17px;
            font-weight: 700;
          }
        }

        & > .quantity-actions {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #4f070d;
          margin: auto 0;
          transition: inherit;

          & > span {
            font-size: 5/6em;
            font-weight: 700;
            line-height: 1;
            margin: 0 30px;
          }

          & > button {
            display: flex;
            color: #f28b14;
            font-size: 30px;
          }
        }
        & > .notes.show {
          scale: 1;
          max-height: 100vh;
        }
        & > .notes {
          width: 100%;
          background-color: var(--color-background-2);
          border-radius: 20px;
          font-size: 0.5em;
          max-height: 0;
          flex: 1;
          margin: 20px 0;
          transition: inherit;
          overflow: hidden;
          // display: none;

          & > .note-order-input {
            padding: 10px;
            width: 100%;
            height: 100%;
            outline: none;
            resize: none;
            overflow: hidden;
          }
          & > .note-order-input::placeholder {
            font-style: italic;
          }
        }

        & > .add-to-cart {
          width: 40px;
          height: 40px;
          border-radius: 5px;
          font-size: 25px;
          background-color: #f28b14;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translate(-50%, 50%) !important;
        }

        & > .rating{
          display: flex;
          justify-content: center;
          margin-top: 15px;
        }
      }
    }
    & .selected-product-container {
      z-index: 100;
      height: 100%;
      overflow: visible;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      & .product-detail {
        min-height: auto;
        margin-top: 20px;
        align-items: center;
        justify-content: center;
        width: calc(100% - 100px);
        & > .name {
          text-transform: uppercase;
          text-align: center;
          font-family: "Smooch Sans";
          font-size: 30px !important;
          line-height: 1.1;
          font-weight: 700 !important;
        }
      }

      & .product-item-container {
        width: 350px !important;
        max-width: 80dvw !important;
        margin: 0;
        transition: all 0.3s ease-in-out;
        padding-top: 0;

        & > img {
          // width: 90%;
          scale: 90%;
        }
        & > .product-info {
          width: 100%;
          margin-top: -200px;
          padding-top: 175px;
          border-radius: 0 0 20px 20px;
          background: linear-gradient(to top, white 95%, transparent);

          & > .name {
            display: none;
            scale: 0;
          }
        }
        & .notes {
          display: block;
          scale: 1;
        }
      }
    }
  }
}

.language-dropdown-container {
  & .v-list {
    overflow: visible !important;
    width: auto;
  }
  & .v-list-item__content {
    width: 100%;
  }
  & .v-list-item--one-line {
    width: 100%;
    overflow: visible;
    max-width: unset;
  }
  & .lang-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 17px;
    gap: 5px;
    font-weight: 600;
    width: fit-content;
    overflow: visible;

    & svg {
      width: 35px;
    }
  }
}

.default-lang-icon {
  background-color: #f28b14;
  color: white;
  width: 35px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-size: 15px;
}
