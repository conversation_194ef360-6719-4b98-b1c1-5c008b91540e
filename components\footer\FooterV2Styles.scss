.public-container.h-fit.hide {
  height: 0;
  overflow: hidden;
}
.footer-public-container{
  background-color: white;
  box-shadow: 0px 0px 7px -1px rgb(0, 0, 0, 0.2);

  @media screen and (min-width: 1025px) {
    display: none;
  }
  // max-width: 1320px;
}
.footer-v2-container {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  // box-shadow: 0px 0px 7px -1px rgb(0, 0, 0, 0.2);
  z-index: 1000;
  margin-top: auto;
  max-width: var(--max-width-view);
  width: 100%;
  // max-height: 50px;
  .footer-button {
    flex: 1;
    border: none;
    background: white;
    color: var(--color-text-note);
    font-size: 1em;
    height: 55px;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    padding: 5px 0;
    font-weight: bold;
    overflow: visible;

    & > .middle-button {
      width: 60px;
      height: 60px;
      min-height: 60px;
      margin-top: -10px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px -4px 4px 0 rgb(0, 0, 0, 5%);
      padding: 7px;

      & > div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(to top, var(--primary-color-1), var(--linear-color-1)) ;
        border-radius: inherit;
        color: white;
        border: 3px solid white;
        box-shadow: 0 4px 8px 0px rgb(0, 0, 0, 10%);
        font-size: 25px;
      }
    }

    > img {
      width: 2em;
      height: 2em;
      filter: grayscale(1);
    }

    & > em {
      border-radius: 2em;
      color: white;
      background: var(--primary-color-2);
      min-width: 15px;
      height: 15px;
      padding: 0 2px;
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-style: normal;
      position: absolute;
      top: 3px;
      left: 50%;
      line-height: 1;
      font-weight: 600;

      & > span {
        font-size: 0.8em;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .footer-button > svg {
    font-size: 25px;
  }
  .footer-button > span {
    font-size: 13px;
    display: flex;
    font-weight: 800;
  }
  .footer-button.active {
    /* background-color: var(--primary-color-1); */
    color: var(--primary-color-1) !important;
    box-shadow: none !important;

    & > img {
      filter: grayscale(0);
    }
  }
  @keyframes shakeCart {
    25% {
      transform: translateX(6px);
    }
    50% {
      transform: translateX(-4px);
    }
    75% {
      transform: translateX(2px);
    }
    100% {
      transform: translateX(0);
    }
  }
  .shake {
    animation: shakeCart 0.4s ease-in-out forwards;
  }
}
.footer-v2-container > a {
  width: 100%;
  text-align: center;
  text-decoration: none;
  display: flex;
}
