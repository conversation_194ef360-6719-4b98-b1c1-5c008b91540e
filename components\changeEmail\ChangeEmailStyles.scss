.change-email-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 0 10px 10px;
  background-color: white;
}

.change-email-container {
  flex: 1;
  display: flex;
  font-size: 1.2em;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  background: white;

  & > .change-email-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 15px 0;

    & > .step-content {
      display: flex;
      flex-direction: column;
      & > .v-stack {
        margin-bottom: 10px;
        width: 100%;

        & .content-input-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          background: #f7f7f7;
          border-radius: 5px;
          font-size: 17px;

          & > input {
            flex: 1;
            overflow: auto;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }
          & > input:-webkit-autofill {
            background-color: initial !important;
          }
          & > button.send-code {
            width: fit-content;
            min-width: fit-content;
            white-space: nowrap;
            margin: 0 10px;
            font-weight: 700;
            font-size: 15px;
            background: transparent;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            gap: 5px;
            color: var(--primary-color-1);
          }
        }

        & .label {
          font-weight: 600;
          font-size: 15px;
          color: #525252;

          & > em {
            color: var(--primary-color-1);
          }
        }

        & .input-custom {
          background: #f7f7f7;
          border-radius: 7px;
          padding: 10px;
          height: 45px;
          font-size: 15px;
          font-weight: 600;
          border: none;
        }

        & > input.input-custom:-webkit-autofill {
          background-color: initial !important;
        }

        & .input-custom:disabled,
        > .input-date-custom:disabled {
          background-color: rgb(231, 233, 236) !important;
          cursor: default;
        }

        & > .dropdown-custom {
          background: white;
          border-radius: 7px;
          margin: 5px 0;
          padding: 0;
          font-size: 15px;
          font-weight: 600;
          border: 1px solid rgb(231, 233, 236);
          cursor: pointer;
          & button {
            background-color: white;
            font-size: 1em;
            font-weight: 600;
            color: var(--color-text-black);
            padding: 10px;
            border: none;
            opacity: 1;
            width: 100%;
            animation: none;
            cursor: pointer !important;
          }
          & button:disabled {
            cursor: default !important;
            background-color: rgb(231, 233, 236) !important;
          }
          & input {
            outline: none;
          }
          & .group {
            width: 95%;
            max-height: 500% !important;
            overflow: auto;
            box-shadow: 0 0 5px #ccc;
            border-radius: 10px;
            opacity: 1;
            z-index: 1000;
            background: white;
          }
          & ul {
            background: white !important;
            border: none !important;
            box-shadow: 0 0 5px #aaa !important;
            color: var(--color-text-black);
            margin: 0;
            border-radius: 5px;
            padding: 5px;
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 5px;
            border-radius: 0;

            & li {
              color: var(--color-text-black) !important;
              padding: 5px;
              background-color: white !important;
              border-radius: 5px;
              cursor: pointer;
              display: flex;
              font-weight: 500;

              &:hover {
                background-color: #f5f6fa !important;
              }

              & span {
                color: var(--color-text-black) !important;
              }
            }
            & li[data-headlessui-state~="selected"] {
              background-color: var(--primary-color-1) !important;
              color: white !important;

              & span {
                color: white !important;
              }
            }
            & li[data-headlessui-state="selected"]:hover {
              background-color: var(--primary-color-1) !important;
              color: white !important;
            }
          }
        }
        & .error-message {
          font-size: 13px;
        }
        & > .unlink-google {
          padding: 5px 15px;
          border-radius: 7px;
          background: var(--primary-color-2);
          color: white;
          width: fit-content;
          margin: auto;
        }
      }
    }

    & .error-message.success {
      font-style: normal;
      color: green;
    }
    @keyframes high-light {
      50% {
        opacity: 0;
      }
    }
    & .error-message.hight-light:not(.success) {
      transform-origin: 0 0;
      animation: high-light 0.5s ease-in-out infinite;
    }
  }

  & .change-email-footer {
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
  }
}
