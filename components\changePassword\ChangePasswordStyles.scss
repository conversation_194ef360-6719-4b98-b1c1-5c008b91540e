.change-password-container {
  flex: 1;
  display: flex;
  font-size: 1.2em;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  background: white;

  // > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;

  //   & > .header-left {
  //     margin-right: auto;
  //   }
  // }

  & .change-password-content {
    padding: 20px 10px 30px 10px;

    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;

    & > img {
      width: 300px;
      object-fit: contain;
    }

    & .label {
      font-weight: 600;
      font-size: 15px;
      color: #2e2d30;
    }

    & .label-content {
      color: #3b3b3b;
      font-weight: 500;

      & > em {
        color: var(--primary-color-1);
        font-weight: bold;
      }
    }

    & > .stack-content {
      width: 100%;
      display: flex;
      margin-bottom: 5px;

      & .user-name {
        color: var(--primary-color-1);
        margin-left: 10px;
      }

      & .content-input-group {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        background: var(--color-background-2);
        padding-left: 10px;
        border-radius: 5px;
        font-size: 17px;
        margin-bottom: 5px;

        & > input {
          flex: 1;
          overflow: auto;
        }
        & > input:-webkit-autofill {
          background-color: initial !important;
        }
        & > button {
          background: transparent;
          border-radius: 50%;
          width: 35px;
          height: 35px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          color: var(--primary-color-1);
        }
      }
      & .content-input {
        background: transparent;
        border: none;
        outline: none;
        padding: 10px 0;
        border-radius: inherit;
      }
      & .error-message {
        font-size: 13px;
      }
      & .error-message.hight-light:not(.success) {
        transform-origin: 0 0;
        animation: high-light 0.5s ease-in-out infinite;
      }
      @keyframes high-light {
        50% {
          opacity: 0;
        }
      }
      & .error-message.success {
        font-style: normal;
        color: green;
      }
    }

    & > .button-action{
      color: white;
      width: 100px;
      padding: 5px 15px;
      margin: 0;
    }
  }
}
