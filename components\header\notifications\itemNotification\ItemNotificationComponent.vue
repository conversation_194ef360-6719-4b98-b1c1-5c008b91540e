<template>
  <div class="notification-item-ref" :id="`item_noti_${noti_data.id}`" ref="targetItemNoti">
    <nuxt-link class='notification-item-container' v-if="isVisible" :class="{ 'unread': !props.noti_data.read_at }"
      v-on:click="(e: Event) => {
        emit('mark_read')
      }" :to="notify_data?.target_url ?? appRoute.ProfileComponent">
      <img class="noti-avt" v-if="notify_data?.image?.length" :src="notify_data?.image?.includes('https') ? notify_data?.image : (domainImage+notify_data?.image)" alt="">
      <img class="noti-avt" v-else-if="notify_data?.icon?.length" :src="notify_data?.icon?.includes('https') ? notify_data?.icon : (domainImage+notify_data?.icon)" alt="">
      <div class="noti-detail">
        <span class="noti-title" v-if="notify_data?.title?.length">{{ notify_data?.title }}</span>
        <span class="noti-content" v-if="notify_data?.body?.length"> {{ notify_data?.body
        }}</span>
        <em class="noti-at">{{ checkTime(props.noti_data?.created_at) }}</em>
      </div>
      <v-badge class="new" v-if="!props.noti_data.read_at" color="var(--primary-color-1)" dot></v-badge>
      <v-menu class="bootstrap-dropdown-container" location="bottom right" v-if="!props.noti_data.read_at" contained
        v-on:click="(e: Event) => {
          e.stopPropagation();
        }">
        <template v-slot:activator="{ props }">
          <button class="options-button" v-bind="props" v-on:click="(e: Event) => {
            e.preventDefault();
            e.stopPropagation();
          }">
            <Icon name="solar:menu-dots-bold"></Icon>
            <!-- <div class="count">{{ unreadNotificationCount }}</div> -->
          </button>
        </template>
        <v-list>
          <v-list-item :key="'mark_read'" v-on:click="(e: Event) => {

            e.stopPropagation();
            e.preventDefault();
            emit('mark_read')
          }">
            <v-list-item-title class="mark-as-read-item">
              <span>{{ $t('NotificationsComponent.danh_dau_da_doc') }}</span>
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </nuxt-link>
  </div>
  <v-divider class="border-opacity-10 divider" color="var(--primary-color-1)"></v-divider>
</template>

<style lang="scss" src="./ItemNotificationStyles.scss"></style>

<script setup lang="ts">
import moment from 'moment';
import { domainImage } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';

const { t } = useI18n();
const props = defineProps({
  noti_data: null,
  index_noti: null
})
const emit = defineEmits(['mark_read'])
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const observer = ref<IntersectionObserver | null>(null);
var targetItemNoti = ref<HTMLElement>()
var notify_data = ref(JSON.parse(props.noti_data.data));
var isVisible = ref(true);
onMounted(() => {
  setTimeout(() => {
    if (process.client) {
      observer.value = new IntersectionObserver(
        ([entry]) => {
          isVisible.value = entry.isIntersecting;
        },
      );

      if (targetItemNoti.value) {
        observer.value.observe(targetItemNoti.value);
      }
    }
    else {
      isVisible.value = true;
    }
  }, 1000);

})

function checkTime(time: any) {
  let now = moment();
  let timeTemp = moment(time, 'YYYY-MM-DD HH:mm:ss');

  let diffInDay = now.diff(timeTemp, 'seconds');
  if (diffInDay < 60) return `${t('NotificationsComponent.vua_xong')}`;
  if (diffInDay < 60 * 60) return `${Math.floor(diffInDay / 60)} ${t('NotificationsComponent.phut_truoc')}`;
  if (diffInDay < 60 * 60 * 24) return `${Math.floor(diffInDay / (60 * 60))} ${t('NotificationsComponent.gio_truoc')}`;

  let diffInWeek = now.diff(timeTemp, 'days');
  if (diffInWeek == 1) return `${t('NotificationsComponent.hom_qua')}`;
  if (diffInWeek <= 7) return `${diffInWeek} ${t('NotificationsComponent.ngay_truoc')}`;

  return timeTemp.format("DD/MM/YYYY HH:mm")
}

</script>