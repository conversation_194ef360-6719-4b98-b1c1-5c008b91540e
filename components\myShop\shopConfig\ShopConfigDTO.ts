export interface shop_config_json {
    general: general_config,
    order: order_config
}

interface general_config {
    is_open: {
        value: boolean | null,
        reason: template_translate
    },
    holiday_settings: {
        holiday_mode: {
            enabled: boolean,
        },
        days_off: templage_day_off[] | null,
        days_of_week_off: string[],
        days_of_month_lunar: string[]
    },
    pending_time: {
        value: number | null,
        unit: string,
        enabled: boolean,
    },
    delivery_expire_pending_time: {
        value: number | null,
        unit: string,
        enabled: boolean,
    },
    commission_percent: {
        value: number | null,
        enabled: boolean,
    },
    message: {
        greeting: template_translate | null,
        notification: template_translate | null
    },
    view_template: {
        template_id: string,
        colors: any,
        font: any,
        banner: any,
        use_shop_banner: any;
        background: any;
        use_default_backgrond: any;
        font_resize: any;
    }
}

interface order_config {
    order_online: {
        value: boolean,
    }
    delivery: delivery_fee_config[],
    delivery_default_message: template_translate | null
}

export interface delivery_fee_config {
    id: any,
    name: template_translate | any,
    description: template_translate | any,
    type: 'percent' | 'money',
    value: number | null,
    max_value: number | null,
    enabled: boolean,
    conditions: delivery_fee_condition[],
    relationship_condition: condition_relationship,
}

export interface template_translate {
    vi?: string,
    en?: string,
    ko?: string,
    ru?: string
}

export interface templage_day_off {
    date: string,
    reason: template_translate
}

export interface delivery_fee_condition {
    id: any,
    param: string,
    operator: 'equal_to' | 'greater_than' | 'less_than' | 'greater_than_or_equal_to' | 'less_than_or_equal_to' | 'not_equal_to' | 'in' | 'not_in',
    value: any,
    unit: any,
    input_type: any,
    raw_value?: any
}

export class ShopConfigJSON implements shop_config_json {
    general: general_config;
    order: order_config;
    constructor(shopConfigJSON?: shop_config_json | null) {
        this.general =
        {
            is_open: {
                value: shopConfigJSON?.general?.is_open?.value ?? true,
                reason: {
                    vi: shopConfigJSON?.general?.is_open?.reason?.vi ?? undefined,
                    en: shopConfigJSON?.general?.is_open?.reason?.en ?? undefined,
                    ko: shopConfigJSON?.general?.is_open?.reason?.ko ??undefined,
                    ru: shopConfigJSON?.general?.is_open?.reason?.ru ?? undefined,
                }
            },
            holiday_settings: {
                holiday_mode: {
                    enabled: shopConfigJSON?.general?.holiday_settings?.holiday_mode?.enabled ?? true,
                },
                days_off: shopConfigJSON?.general?.holiday_settings?.days_off?.length ? shopConfigJSON?.general?.holiday_settings?.days_off : [],
                days_of_week_off: shopConfigJSON?.general?.holiday_settings?.days_of_week_off?.length ? shopConfigJSON?.general?.holiday_settings?.days_of_week_off : [],
                days_of_month_lunar: shopConfigJSON?.general?.holiday_settings?.days_of_month_lunar?.length ? shopConfigJSON?.general?.holiday_settings?.days_of_month_lunar : [],

            },
            pending_time: {
                value: shopConfigJSON?.general?.pending_time?.value ?? null,
                unit: shopConfigJSON?.general?.pending_time?.unit ?? 'minute',
                enabled: shopConfigJSON?.general?.pending_time?.enabled ?? true
            },            delivery_expire_pending_time: {
                value: shopConfigJSON?.general?.delivery_expire_pending_time?.value ?? null,
                unit: shopConfigJSON?.general?.delivery_expire_pending_time?.unit ?? 'minute',
                enabled: shopConfigJSON?.general?.delivery_expire_pending_time?.enabled ?? false
            },
            commission_percent: {
                value: shopConfigJSON?.general?.commission_percent?.value ?? null,
                enabled: shopConfigJSON?.general?.commission_percent?.enabled ?? false
            },
            message: {
                greeting: {
                    vi: shopConfigJSON?.general?.message?.greeting?.vi ?? '',
                    en: shopConfigJSON?.general?.message?.greeting?.en ?? '',
                    ko: shopConfigJSON?.general?.message?.greeting?.ko ?? '',
                    ru: shopConfigJSON?.general?.message?.greeting?.ru ?? '',
                },
                notification: {
                    vi: shopConfigJSON?.general?.message?.notification?.vi ?? '',
                    en: shopConfigJSON?.general?.message?.notification?.en ?? '',
                    ko: shopConfigJSON?.general?.message?.notification?.ko ?? '',
                    ru: shopConfigJSON?.general?.message?.notification?.ru ?? '',
                }
            },
            view_template: {
                template_id: shopConfigJSON?.general?.view_template?.template_id ?? 'mac_dinh',
                colors: shopConfigJSON?.general?.view_template?.colors ?? '',
                font: shopConfigJSON?.general?.view_template?.font ?? null,
                banner: shopConfigJSON?.general?.view_template?.banner ?? null,
                use_shop_banner: shopConfigJSON?.general?.view_template?.use_shop_banner ?? false,
                background: shopConfigJSON?.general?.view_template?.background ?? null,
                use_default_backgrond: shopConfigJSON?.general?.view_template?.use_default_backgrond ?? false,
                font_resize: shopConfigJSON?.general?.view_template?.font_resize ?? 0,
            }
        };
        this.order = {
            order_online: {
                value: shopConfigJSON?.order?.order_online.value ?? true
            },
            delivery: shopConfigJSON?.order?.delivery ?? [],
            delivery_default_message: {
                vi: shopConfigJSON?.order?.delivery_default_message?.vi ?? '',
                en: shopConfigJSON?.order?.delivery_default_message?.en ?? '',
                ko: shopConfigJSON?.order?.delivery_default_message?.ko ?? '',
                ru: shopConfigJSON?.order?.delivery_default_message?.ru ?? '',
            }
        };
    }
}

export class DeliveryFeeConfig implements delivery_fee_config {
    id: any;
    name: template_translate;
    description: template_translate;
    type: 'percent' | 'money';
    value: number | null;
    max_value: number | null;
    enabled: boolean;
    conditions: delivery_fee_condition[];
    relationship_condition: condition_relationship;

    constructor(deliveryFeeConfig?: delivery_fee_config | null) {
        this.id = deliveryFeeConfig?.id ?? null;
        this.name = deliveryFeeConfig?.name ?? { vi: '' };
        this.description = deliveryFeeConfig?.description ?? { vi: '' };
        this.type = deliveryFeeConfig?.type ?? 'percent';
        this.value = deliveryFeeConfig?.value ?? null;
        this.max_value = deliveryFeeConfig?.max_value ?? null;
        this.enabled = deliveryFeeConfig?.enabled ?? true;
        this.conditions = deliveryFeeConfig?.conditions ?? [];
        this.relationship_condition = deliveryFeeConfig?.relationship_condition ?? condition_relationship.and;
    }

}

export const operators = [
    {
        value: 'equal_to',
        symbol: '=',
        type_enable: 'string'
    },
    {
        value: 'greater_than',
        symbol: '>',
        type_enable: 'string'
    },
    {
        value: 'less_than',
        symbol: '<',
        type_enable: 'string'
    },
    {
        value: 'greater_than_or_equal_to',
        symbol: '>=',
        type_enable: 'string'
    },
    {
        value: 'less_than_or_equal_to',
        symbol: '<=',
        type_enable: 'string'
    },
    {
        value: 'not_equal_to',
        symbol: '≠',
        type_enable: 'string'
    },
    {
        value: 'in',
        symbol: 'in',
        type_enable: 'select'
    },
    {
        value: 'not_in',
        symbol: 'not in',
        type_enable: 'select'
    }
]

export const condition_params = [
    {
        param: 'price',
        name: 'gia_tri_don_hang',
        unit: 'VNĐ',
        input_type: 'string'
    },
    {
        param: 'delivery_distance',
        name: 'khoang_cach',
        unit: 'km',
        input_type: 'string'
    },
    {
        param: 'province_id',
        name: 'tinh_thanh_pho',
        input_type: 'select'
    },
    // {
    //     param: 'district_id',
    //     name: 'quan_huyen',
    // },
    // {
    //     param: 'ward_id',
    //     name: 'xa_phuong',
    // }
];

export enum condition_relationship {
    and = 'AND',
    or = 'OR'
}

export enum day_off_type{
    days_off = 1,
    day_off_week = 2,
    day_off_month_lunar = 3
}