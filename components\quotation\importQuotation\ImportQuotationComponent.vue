<template>
    <div class="import-quotation-container">
        <SubHeaderV2Component :title="$t('ImportQuotationComponent.nhap_bao_gia_nguyen_vat_lieu')">
            <!-- <template v-slot:header_left>
                <button class="back-button" v-on:click="() => {
                    close();
                }">
                    <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                </button>
            </template> -->
        </SubHeaderV2Component>
        <div class="import-quotation-content-container">
            <v-row class="header-import">
                <v-col cols="12" md="4" lg="4" sm="12" class="information" v-show="expandHeader">
                    <input type="text" class="quotation-detail-input" :class="{
                        'error': nameErr,
                        'animation': runErrAnimation
                    }" :placeholder="$t('ImportQuotationComponent.tieu_de')" v-model="name"
                        v-on:input="validateName()" />
                    <input type="text" class="quotation-detail-input"
                        :placeholder="$t('ImportQuotationComponent.ghi_chu')" v-model="notes" />
                </v-col>
                <v-col cols="12" md="4" lg="4" sm="12" class="expiration" v-show="expandHeader">
                    <div class="select-expire-date">
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained
                            v-model="menuFrom" :closeOnContentClick="false" key="expire_from">
                            <template v-slot:activator="{ props }">
                                <button class="select-date" v-bind="props" :class="{
                                    'error': fromErr,
                                    'animation': runErrAnimation
                                }">
                                    {{ $t('ImportQuotationComponent.tu') }}: {{ moment(from).isValid() ?
                                        moment(from).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :max="to" show-adjacent-months v-model:modelValue="from" v-on:update:model-value="() => {
                                validateFrom();
                                menuFrom = false;
                            }"></v-date-picker>
                        </v-menu>
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained v-model="menuTo"
                            :closeOnContentClick="false">
                            <template v-slot:activator="{ props }" key="expire_to">
                                <button class="select-date" v-bind="props" :class="{
                                    'error': toErr,
                                    'animation': runErrAnimation
                                }">
                                    {{ $t('ImportQuotationComponent.den') }}: {{ moment(to).isValid() ?
                                        moment(to).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :min="from" show-adjacent-months v-model:modelValue="to" v-on:update:model-value="() => {
                                validateTo();
                                menuTo = false;
                            }"></v-date-picker>
                        </v-menu>
                    </div>
                    <v-btn class="select-from-file-btn" variant="outlined" v-on:click="() => {
                        showImportFileModal = true
                    }">
                        {{ $t('ImportQuotationComponent.nhap_tu_excel') }}
                    </v-btn>
                </v-col>
                <v-col cols="8" md="2" lg="2" sm="8" class="actions">
                    <div class="supplier-group">
                        <v-select label="" class="custom-v-select supplier-select" :menu-props="{
                            contentClass: 'text-center'
                        }" v-on:updated:model-value="validateSupplier()"
                            :placeholder="$t('ImportQuotationComponent.nha_cung_cap')" v-model="current_supplier"
                            :item-value="'value'" :menu-icon="''" :items="data_supplier" variant="plain" :class="{
                                'error': supplierErr,
                                'animation': runErrAnimation
                            }">
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :title="item.value.name"></v-list-item>
                            </template>
                            <template v-slot:selection="{ item }">
                                <span>{{ item.value.name }}</span>
                            </template>
                            <template v-slot:append-inner></template>
                        </v-select>
                        <v-btn class="add-supplier" :title="$t('ImportQuotationComponent.them_nha_cung_cap')" v-on:click="()=>{
                            showAddSupplierModal = true;
                        }">
                            <Icon name="typcn:plus"></Icon>
                        </v-btn>
                    </div>
                    <div class="sheet-group">
                        <v-select label="" class="custom-v-select sheet-select" :menu-props="{
                            contentClass: 'text-center'
                        }" :placeholder="$t('ImportQuotationComponent.chon_sheet')" :model-value="current_sheet"
                            :item-value="'value'" :item-title="'label'" :menu-icon="''" v-on:update:modelValue="changeSheet" :items="sheet_list" variant="plain">
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :title="item.title"></v-list-item>
                            </template>
                            <template v-slot:selection="{ item }">
                                <span>{{ (item.raw as any).label ? (item.raw as any).label :
                                    $t('ImportQuotationComponent.chon_sheet') }}</span>
                            </template>
                            <template v-slot:append-inner></template>
                        </v-select>
                    </div>
                </v-col>
                <v-col cols="4" md="2" lg="2" sm="4" class="actions">
                    <v-btn class="save-quotation" variant="outlined" :loading="saving "
                        :disabled="saving || !current_sheet_data?.row_data?.length" v-on:click="() => {
                            console.log(current_sheet_data?.row_data);
                            saveQuotationSheet()
                        }">
                        {{ current_sheet_data?.saved ? $t('ImportQuotationComponent.da_luu') :
                            $t('ImportQuotationComponent.luu')
                        }}
                    </v-btn>
                </v-col>
            </v-row>
            <v-btn class="expand-header-import" v-on:click="() => {
                collapseHeader()
            }">{{ expandHeader ? $t('ImportQuotationComponent.thu_gon') : $t('ImportQuotationComponent.mo_rong')
                }}</v-btn>

            <v-data-table class="data-import" v-if="current_sheet_data" v-model:page="current_data_page"
                v-model:items-per-page="item_per_page" :headers="[
                    ...Object.keys(data_field_structor).map((key, index) => {
                        return {
                            value: key,
                            title: t(`ImportQuotationComponent.${(data_field_structor as any)?.[key].label_key}`),
                        }
                    }), {
                        value: 'action',
                        title: t(`ImportQuotationComponent.tac_vu`),
                    },
                ]" :items="current_sheet_data.row_data">
                <template v-slot:headers="{ columns }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="head-col" :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }" :class="{
                                'short': column.value == data_field_structor.stt.label_key,
                                'auto': column.value == 'action'
                            }">
                                <span class="cursor-pointer">{{ column.title }}</span>
                            </th>
                        </template>
                    </tr>
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="head-select-field" :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                v-if="column.value != 'material_id' && column.value != 'action' && column.value != data_field_structor.stt?.label_key">
                                <v-select label="" clearable class="custom-v-select field-col-select" :menu-props="{
                                    contentClass: 'text-center'
                                }" :placeholder="$t('ImportQuotationComponent.chon_cot_tuong_ung')"
                                    v-model="(data_field_structor as any)[column.value as any].value"
                                    :item-value="'value'" :item-title="'label'" :menu-icon="''" v-on:update:modelValue="() => {
                                        fillDataToField(column.key)
                                    }"
                                    :items="current_sheet_data.column_names.filter((e: any) => { return e?.length })"
                                    variant="plain">
                                    <template v-slot:item="{ props, item }">
                                        <v-list-item v-bind="props" :title="item.title"></v-list-item>
                                    </template>
                                    <template v-slot:append-inner></template>
                                </v-select>
                            </th>
                            <th class="head-select-field" v-else>
                                <div class="custom-v-select"></div>
                            </th>
                        </template>
                    </tr>
                </template>
                <template v-slot:item="{ item, columns, index }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="body-col stt" v-if="column.value == data_field_structor.stt?.label_key"
                                :id="`stt_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <span>{{ index + (current_data_page - 1) * item_per_page + 1 }}</span>
                            </th>
                            <th class="body-col actions" v-else-if="column.value == 'action'"
                                :id="`body_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <v-btn-group>
                                    <v-btn class="action duplicate" v-on:click="() => { duplicateRow(index) }">
                                        <Icon name="heroicons-outline:document-duplicate"></Icon>
                                    </v-btn>
                                    <v-btn class="action add-new" v-on:click="() => { addRowData(index) }">
                                        <Icon name="octicon:feed-plus-16"></Icon>
                                    </v-btn>
                                    <v-btn class="action delete" v-on:click="() => { deleteRowData(index) }">
                                        <Icon name="tabler:trash"></Icon>
                                    </v-btn>
                                </v-btn-group>

                            </th>
                            <th class="body-col material-id" v-else-if="column.value == 'material_id'"
                                :id="`material_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <v-autocomplete label="" :filter-mode="'intersection'"
                                    class="custom-v-select material-select" clearable :menu-props="{
                                        contentClass: 'text-center'
                                    }" v-on:update:model-value="($event) => {
                                        console.log($event, (item as any).material_id)
                                    }" :placeholder="$t('ImportQuotationComponent.nguyen_lieu_moi')"
                                    v-model="(item as any).material_id" :item-value="'id'" :menu-icon="''"
                                    :items="data_material" variant="plain" :custom-filter="(item: any, queryText: any, itemObj: any) => {
                                        let name = nonAccentVietnamese(item).toLocaleLowerCase();
                                        let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
                                        return name.includes(query)
                                    }" :item-title="'name'">
                                    <!-- <template v-slot:item="{ props, item }">
                                        <v-list-item v-bind="props" :title="item.value.name"></v-list-item>
                                    </template>
        <template v-slot:selection="{ item }">
                                        <span>{{ item.value.name ?? $t('ImportQuotationComponent.nguyen_lieu_moi')
                                            }}</span>
                                    </template> -->
                                    <template v-slot:append-inner>
                                        <!-- <Icon name="mdi:chevron-down"></Icon> -->
                                    </template>
                                </v-autocomplete>
                            </th>
                            <th class="body-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :id="`body_long_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`"
                                :class="{
                                    'long-text': ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name?.label_key)
                                        || ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name_en?.label_key)
                                }" v-else>
                                <UTextarea autoresize resize :maxrows="5" :rows="1" type="text"
                                    class="quotation-detail-input" v-on:input="($event: any) => {
                                        console.log($event.target.value);
                                        console.log(column.key, data_field_structor.name?.label_key);
                                        (item as any)[column.key ?? ''] = $event.target.value;
                                    }" :value="(item as any)?.[column.key ?? '']" />
                            </th>

                        </template>
                    </tr>
                </template>
            </v-data-table>
            <div class="none-data" v-else>
                <img :src="none_list_quotations" alt="">
                {{ $t('ImportQuotationComponent.chua_co_du_lieu') }}
            </div>
        </div>
    </div>

    <ImportFromFileComponent v-if="showImportFileModal" v-on:close="() => {
        showImportFileModal = false;
    }" v-on:submit="(e) => {
        current_sheet = e.currentSheet;
        data_view = e.dataView;
        name = e.name;
        notes = e.notes;
        sheet_data = e.sheetData;
        sheet_list = e.sheetLists;
        current_file_url = e.fileUrl;
        showData();
    }"></ImportFromFileComponent>

    <AddSupplierComponent v-if="showAddSupplierModal" v-on:close="()=>{
        showAddSupplierModal = false;
    }" v-on:submit="()=>{
        showAddSupplierModal = false;
        getListSupplier();
    }"></AddSupplierComponent>
</template>

<script lang="ts" setup>
import no_data_found from "~/assets/imageV2/nodata-found.png"
import none_list_quotations from "~/assets/image/list-empty-2.jpg"
import { domainImage, appConst, nonAccentVietnamese, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { HttpStatusCode } from "axios";
import { QuotationService } from "~/services/quotationService/quotationService";
import { ShopService } from "~/services/shopService/shopService";
import { DataFieldStructor, type MaterioDto, type SupplierDto } from "../QuotationDTO";
import { SupplierService } from "~/services/quotationService/supplierService";
import { MaterialService } from "~/services/quotationService/materialService";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var quotationService = new QuotationService();
var supplierService = new SupplierService();
var materialService = new MaterialService();
var shopService = new ShopService();

var name = ref<string>("");
var nameErr = ref(false);
var notes = ref<string>("");
var from = ref<string | null>(null);
var fromErr = ref(false);
var to = ref<string | null>(null);
var toErr = ref(false);
var current_file_url = ref("");
var current_sheet = ref("");
var current_sheet_data = ref<any>(null);
var data_view = ref<any>(null);
var sheet_list = ref<any>();
var sheet_data = ref<any>();
var current_supplier = ref<SupplierDto>();
var supplierErr = ref(false);
var data_field_structor = ref<DataFieldStructor>(new DataFieldStructor());

var runErrAnimation = ref(false);

var data_supplier = ref<SupplierDto[]>([]);
var data_material = ref<MaterioDto[]>([]);
var showImportFileModal = ref(false);
var expandHeader = ref(true);

var current_data_page = ref(1);
var item_per_page = ref(10);

var menuFrom = ref(false);
var menuTo = ref(false);

var saving = ref(false);
var showAddSupplierModal = ref(false);

onMounted(async () => {
    window.addEventListener('resize', () => {
        if (window.innerWidth > 961) {
            expandHeader.value = true;
        }
    })

    console.log(data_field_structor.value);
    getListMaterial();
    await getListSupplier();
})

onBeforeUnmount(() => {
});

function getListSupplier() {
    return new Promise((resolve) => {
        supplierService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_supplier.value = JSON.parse(JSON.stringify(res.body.data))
            }
            resolve(data_supplier.value);
        }).catch(() => {
            toast.error(t('ImportQuotationComponent.lay_danh_sach_nha_cung_cap_that_bai'))
        })
    })

}

function getListMaterial() {
    return new Promise((resolve) => {
        materialService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_material.value = JSON.parse(JSON.stringify(res.body.data));
            }
            resolve(data_material.value);
        })
    })

}

function showData() {
    current_sheet_data.value = JSON.parse(JSON.stringify(sheet_data.value?.[current_sheet.value]));
    console.log(current_sheet_data.value);
    autoSelectField();
}

const changeSheet = (e:any) => {
    console.log(e, current_sheet.value, sheet_data.value?.[current_sheet.value]);
    sheet_data.value?.[current_sheet.value]
    if(sheet_data.value?.[current_sheet.value].saved){
        current_sheet.value = e;
        notes.value = sheet_data.value?.[current_sheet.value].sheet_name;
        showData();
    }
    else{
        let confirming = confirm(t('ImportQuotationComponent.chua_luu_du_lieu'));
        console.log(confirming);
        if(confirming){
            current_sheet.value = e;
            notes.value = sheet_data.value?.[current_sheet.value].sheet_name;
            showData();
        }
    }
}

function autoSelectField() {
    let structure = JSON.parse(JSON.stringify(data_field_structor.value));
    console.log(structure);
    console.log(current_sheet_data.value.column_names);
    Object.keys(data_field_structor.value).map(async (key: string) => {
        let indexKey = current_sheet_data.value.column_names.findIndex(function (e: any) {
            return (data_field_structor.value as any)[key].compare_arr.includes(nonAccentVietnamese(e))
        })
        if (indexKey != -1) {
            structure[key].value = current_sheet_data.value.column_names[indexKey];
            // await this.fillDataToField(key);
        }
    });
    structure.material_id.value = 'material_id';
    console.log(structure);
    data_field_structor.value = JSON.parse(JSON.stringify(structure));
    fillDataToTable();
    // this.setState({
    //     dataFieldStructure: JSON.parse(JSON.stringify(structure))
    // }, () => {
    //     this.fillDataToTable();
    // });
}
function fillDataToTable() {
    let rows = JSON.parse(JSON.stringify(current_sheet_data.value.row_data));

    Object.keys(data_field_structor.value).map(async key => {
        rows.map((itemRow: any) => {
            itemRow[key] = itemRow[(data_field_structor.value as any)?.[key].value]?.length ? itemRow[(data_field_structor.value as any)?.[key].value] : null;
        })
    });
    current_sheet_data.value.row_data = JSON.parse(JSON.stringify(rows));
}
const fillDataToField = (field: string | null) => {
    let rows = JSON.parse(JSON.stringify(current_sheet_data.value.row_data));
    rows.map((itemRow: any) => {
        itemRow[field ?? ''] = itemRow[(data_field_structor.value as any)[field ?? ''].value];
    })
    current_sheet_data.value.row_data = JSON.parse(JSON.stringify(rows));
}

function collapseHeader() {
    if (window.innerWidth > 961) {
        expandHeader.value = true;
    }
    else {
        expandHeader.value = !expandHeader.value
    }
}

async function saveQuotationSheet() {
    await validateName();
    await validateFrom();
    await validateTo();
    await validateSupplier();
    console.log(nameErr.value, fromErr.value, toErr.value, supplierErr.value)

    if (nameErr.value || fromErr.value || toErr.value || supplierErr.value) {
        runErrAnimation.value = true;
        expandHeader.value = true;
        setTimeout(() => {
            runErrAnimation.value = false;
        }, 1000);
    }
    else {
        saving.value = true;
        let dataMaterialQuotations: any[] = [];
        current_sheet_data.value.row_data.map((item: any) => {
            let newObj = JSON.parse(JSON.stringify(data_field_structor.value));
            Object.keys(newObj).map(key => {
                newObj[key] = item[key] ? item[key] : "";
            });
            newObj.material_id = item.material_id ? item.material_id : null;
            newObj.barcode = null;
            dataMaterialQuotations.push(newObj);
        })
        console.log(dataMaterialQuotations);

        quotationService.add(
            (current_supplier.value as any)?.id ?? null,
            name.value,
            moment(from.value).format("YYYY-MM-DD"),
            moment(to.value).format("YYYY-MM-DD"),
            notes.value,
            current_file_url.value,
            dataMaterialQuotations
        ).then((res) => {
            if (res.status == HttpStatusCode.Ok) {
                current_sheet_data.value.saved = true;
                sheet_data.value[current_sheet.value].saved = true;
                toast.success(t('ImportQuotationComponent.luu_thanh_cong'));
                // close()
            }
            else {
                toast.error(t('ImportQuotationComponent.luu_that_bai'))
            }
            saving.value = false;
        })

    }
}
function validateName() {
    if (name.value?.length) {
        nameErr.value = false;
        return true;
    }
    else {
        nameErr.value = true;
        return false;
    }
}

function validateFrom() {
    console.log(from.value, moment(from.value).isValid())
    if (from.value && moment(from.value).isValid()) {
        fromErr.value = false;
        return true;
    }
    else {
        fromErr.value = true;
        return false;
    }
}

function validateTo() {
    if (to.value && moment(to.value).isValid()) {
        toErr.value = false;
        return true;
    }
    else {
        toErr.value = true;
        return false;
    }
}

function validateSupplier() {
    console.log(current_supplier.value)
    if (current_supplier.value?.id) {
        supplierErr.value = false;
        return true;
    }
    else {
        supplierErr.value = true;
        return false;
    }
}

function duplicateRow(index: number) {
    console.log(index);
    let rows: any[] = JSON.parse(JSON.stringify(current_sheet_data.value?.row_data));

    rows.splice(index + 1, 0, rows[index]);
    current_sheet_data.value.row_data = JSON.parse(JSON.stringify(rows));
}
function addRowData(index: number) {
    let rows: any[] = JSON.parse(JSON.stringify(current_sheet_data.value?.row_data));
    let newItem: any = {};
    Object.keys(rows[0]).map((key) => {
        newItem[key] = ""
    });
    current_sheet_data.value.row_data.splice(index, 0, newItem)
}

function deleteRowData(index: number) {
    let rows: any[] = JSON.parse(JSON.stringify(current_sheet_data.value?.row_data));
    let newItem: any = {};
    Object.keys(rows[0]).map((key) => {
        newItem[key] = ""
    });
    current_sheet_data.value.row_data.splice(index, 1)
}

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.QuotationComponent)
}
</script>

<style lang="scss" src="./ImportQuotationStyles.scss"></style>
