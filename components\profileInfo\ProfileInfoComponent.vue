<template>
  <div class="public-container">
    <div class="v-stack loading-skeleton" v-if="isRefreshing">
      <!-- <div class="title-header">
      <div class="header-left">
        <button
          class="back-button"
          v-on:click="
            () => {
              router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
            }
          "
        >
          <Icon name="lucide:chevron-left"/>
        </button>
      </div>
      <h3>{{ appRouteTitle.ProfileInfoComponent }}</h3>
      <div class="header-right"></div>
    </div> -->
      <SubHeaderV2Component :title="$t('AppRouteTitle.ProfileInfoComponent')">
      </SubHeaderV2Component>
      <div class="profile-info-content">
        <v-skeleton-loader class="skeleton-content-avt"></v-skeleton-loader>
        <v-skeleton-loader class="skeleton-content-btn"></v-skeleton-loader>
        <v-skeleton-loader class="skeleton-content-btn"></v-skeleton-loader>
        <v-skeleton-loader class="skeleton-content-btn"></v-skeleton-loader>
        <v-skeleton-loader class="skeleton-content-btn"></v-skeleton-loader>
        <v-skeleton-loader class="skeleton-content-btn"></v-skeleton-loader>
      </div>
    </div>
    <div class="profile-info-container" v-if="!isRefreshing">
      <!-- <button class="edit-button" v-if="editable && !editing" v-on:click="() => {
			editing = true
		}">
			<Icon name="fa6-solid:user-pen"></Icon>
		</button> -->
      <!-- <div class="title-header">
      <div class="header-left">
        <button
          class="back-button"
          v-on:click="
            () => {
              router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
            }
          "
        >
          <Icon name="lucide:chevron-left"/>
        </button>
      </div>
      <h3>{{ appRouteTitle.ProfileInfoComponent }}</h3>
      <div class="header-right"></div>
    </div> -->
      <SubHeaderV2Component :title="$t('AppRouteTitle.ProfileInfoComponent')">
        <template #header_left>
          <button class="back-button" v-show="route.path != '/' && route.path != appRoute.HomeComponent" v-on:click="() => {
            router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
          }">
            <Icon name="solar:round-alt-arrow-left-linear"></Icon>
          </button>
        </template>
      </SubHeaderV2Component>
      <div class="profile-info-content">
        <div class="profile-avatar-container">
          <img :src="profileAvtUpdate ? profileAvtUpdate : non_avatar" alt="avatar" class="avatar" />
          <div class="avatar-action">
            <button class="action-button" :disabled="isUpdatingAvt">
              <label>
                {{ $t('ProfileInfoComponent.doi_anh') }}
                <input type="file" accept='image/*' :multiple=false v-on:change="($event: any) => {
                  fileChangeInput($event)
                }" ref="avatarFileName" />
              </label>

            </button>
            <button class="action-button" v-show="profileAvt != profileAvtUpdate" :disabled="isUpdatingAvt" v-on:click="() => {
              updateProfileAvt()
            }">
              <label v-if="!isUpdatingAvt">
                {{ $t('ProfileInfoComponent.luu') }}
              </label>
              <label v-else>
                <Icon name="eos-icons:loading"></Icon>
              </label>
            </button>
            <button class="action-button" v-show="profileAvt != profileAvtUpdate" :disabled="isUpdatingAvt" v-on:click="() => {
              profileAvtUpdate = profileAvt;
              avatarFileName.value = null;
            }">
              <label>
                {{ $t('ProfileInfoComponent.hoan_tac') }}

              </label>
            </button>
          </div>
        </div>

        <div class="v-stack">
          <span class="required label"> {{ $t('ProfileInfoComponent.ten') }} </span>
          <input :title="$t('ProfileInfoComponent.ten')" type="text" name="name" maxLength="255" autoComplete="name"
            class="input-custom" :placeholder="$t('ProfileInfoComponent.ten_placeholder')" :disabled="!editing"
            :value="profileData.name || null" v-on:input="($event: any) => {
              profileData = {
                ...profileData,
                name: $event.target.value
              }
              nameValidation()
            }" v-on:blur="() => {
              nameValidation()
            }
            " />
          <span class="error-message">{{ nameErr }}</span>
        </div>
        <div class="v-stack">
          <span class="required label"> {{ $t('ProfileInfoComponent.ten_dang_nhap') }} </span>
          <input :title="$t('ProfileInfoComponent.ten_dang_nhap')" type="text" name="user-name" maxLength="255"
            autoComplete="user_name" class="input-custom" :disabled="!editing"
            :placeholder="$t('ProfileInfoComponent.ten_dang_nhap_placeholder')" :value="profileData.user_name || ''"
            v-on:input="($event: any) => {
              profileData = {
                ...profileData,
                user_name: nonAccentVietnamese($event.currentTarget.value)
              }
              userNameValidation();
            }" v-on:blur="() => {
              userNameValidation();
            }
            " />
          <span class="error-message">{{ userNameErr }}</span>
        </div>
        <div class="v-stack">
          <span class="required label"> {{ $t('ProfileInfoComponent.so_dien_thoai') }} </span>
          <input :title="$t('ProfileInfoComponent.so_dien_thoai')" type="phone" name="phone-number" maxLength="255"
            autoComplete="phone" class="input-custom" :disabled="!editing"
            :placeholder="$t('ProfileInfoComponent.chua_cung_cap')" :value="profileData.phone || ''" v-on:input="($event: any) => {
              profileData = {
                ...profileData,
                phone: validPhone($event.currentTarget.value)
              }
              phoneValidation();
            }" v-on:blur="() => {
              phoneValidation();
            }
            " />
          <span class="error-message">{{ phoneErr }}</span>
        </div>
        <span class="link-social"
          v-if="profileData.provider_name && nonAccentVietnamese(profileData.provider_name) == nonAccentVietnamese(appConst.provider_name.zalo)">
          <!-- <Icon name="heroicons:link-slash"></Icon> -->
          <img loading="lazy" :src='zalo_icon' :placeholder="zalo_icon" />
          {{ $t('ProfileInfoComponent.da_lien_ket_zalo') }}
        </span>
        <div class="v-stack">
          <span class="label"> {{ $t('ProfileInfoComponent.dia_chi_email') }} </span>
          <div class="content-input-group">
            <input :title="$t('ProfileInfoComponent.dia_chi_email')" name="email" type="email" maxLength="255"
              autoComplete="email" class="input-custom" :placeholder="$t('ProfileInfoComponent.chua_cung_cap')"
              :readonly="true" :value="profileData.email || ''" v-on:input="($event: any) => {
                profileData = {
                  ...profileData,
                  email: nonAccentVietnamese($event.target.value)
                }
                emailValidation(true);
              }" v-on:blur="() => {
                // emailValidation(true);
              }
              " />
            <button class="right-button" v-on:click="() => { showChangeEmailModal = true }">
              {{ $t('ProfileInfoComponent.doi_email') }}
            </button>
          </div>

          <span class="error-message">{{ emailErr }}</span>

        </div>
        <span class="link-social"
          v-if="profileData.provider_name && nonAccentVietnamese(profileData.provider_name) == nonAccentVietnamese(appConst.provider_name.google)">
          <!-- <Icon name="heroicons:link-slash"></Icon> -->
          <img loading="lazy" :src='google_icon' :placeholder="google_icon" />
          {{ $t('ProfileInfoComponent.da_lien_ket_google') }}
        </span>

        <!-- Custom Referral Code Section -->
        <div class="v-stack">
          <span class="label"> {{ $t('ProfileInfoComponent.ma_gioi_thieu') }} </span>
          <div class="content-input-group" v-if="profileData.referral_code">
            <input :title="$t('ProfileInfoComponent.ma_gioi_thieu')" name="referral-code" type="text" maxLength="255"
              class="input-custom" :placeholder="$t('ProfileInfoComponent.ma_gioi_thieu_placeholder')"
              :readonly="true" :value="profileData.referral_code || ''" />
            <button class="right-button" v-on:click="copyReferralCode">
              {{ $t('ProfileInfoComponent.sao_chep') }}
            </button>
          </div>       
              <input v-else-if="editing" :title="$t('ProfileInfoComponent.ma_gioi_thieu')" name="referral-code" type="text" maxLength="255"
              class="input-custom" :placeholder="$t('ProfileInfoComponent.ma_gioi_thieu_placeholder')" :maxlength="20"
              :value="customReferralCode" v-on:input="($event: any) => {
                customReferralCode = nonAccentVietnamese($event.target.value).toUpperCase();
                referralCodeValidation();
              }" v-on:blur="() => {
                referralCodeValidation();
              }" />
          <div v-else>
            <button class="accept-button" v-on:click="() => { editing = true; }">
              {{ $t('ProfileInfoComponent.tao_ma_gioi_thieu') }}
            </button>
          </div>
          <span class="error-message">{{ referralCodeErr }}</span>
        </div>

        <div class="v-stack">
          <span class="label"> {{ $t('ProfileInfoComponent.gioi_tinh') }} </span>
          <USelectMenu class="dropdown-custom" variant="none" v-model="profileData.gender" :options="dataGender"
            v-on:change="(e: any) => { }" :disabled="!editing" value-attribute="value" option-attribute="label">
            <template #label>
              <span class="truncate">{{ $t('ProfileInfoComponent.' + dataGender[dataGender.findIndex(function (e) {
                return e.value == profileData.gender
              })].label)
                }}</span>
            </template>
            <template #option="{ option: genderOption }">
              <span class="truncate">{{ $t('ProfileInfoComponent.' + genderOption.label) }}</span>
            </template>
          </USelectMenu>
        </div>
        <div class="v-stack">
          <span class="label"> {{ $t('ProfileInfoComponent.ngay_sinh') }} </span>
          <input name="birth-day" class="input-date-custom" id="birth-day" type="date" :value="date_of_birth"
            :disabled="!editing" placeholder="{{ $t('ProfileInfoComponent.chua_cung_cap') }}"
            :max="moment(new Date()).subtract(17, 'year').format('YYYY-MM-DD')" v-on:input="(event: any) => {
              date_of_birth = event.target.value;
            }" />
        </div>

      </div>

      <div class="h-stack footer" v-if="editing">
        <button class="reject-button" :disabled="isUpdating" v-on:click="() => { resetProfileData(); }">
          {{ $t('ProfileInfoComponent.huy') }}
        </button>
        <button class="accept-button" :disabled="isUpdating" v-on:click="() => { updateProfile(); }">
          <Icon name="eos-icons:loading" size="20" v-if="isUpdating" />
          <span v-else>{{ $t('ProfileInfoComponent.luu') }}</span>
        </button>
      </div>
      <div class="h-stack footer" v-if="editable && !editing">
        <button class="accept-button" :disabled="isUpdating" v-on:click="() => { editing = true; }">
          {{ $t('ProfileInfoComponent.sua') }}
        </button>
      </div>
      <ChangeEmailComponent v-if="showChangeEmailModal" v-on:close="(e) => {
        if (e) {
          onRefresh()
        }
        showChangeEmailModal = false

      }"></ChangeEmailComponent>
    </div>
  </div>

</template>

<script lang="ts" setup>
import { domainImage, appConst, nonAccentVietnamese, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import non_avatar from "~/assets/image/non-avatar.jpg";
import google_icon from "~/assets/image/google-icon-2.png";
import zalo_icon from "~/assets/image/Logo-Zalo-Arc.webp";
import exifr from "exifr";
import { HttpStatusCode } from "axios";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var imageService = new ImageService();
var dataGender = [
  { label: "nam", value: true },
  { label: "nu", value: false },
  { label: "khac", value: null },
];

var isRefreshing = ref(true);
var isUpdating = ref(false);
var profileId = ref(
  (route.params && route.params.profileId
    ? route.params.profileId
    : null) as string
);
var profileData = ref(null as any);
var profileDataBackup = ref(null as any);
var profileAvt = ref("" as string);
var profileAvtUpdate = ref("");
var nameErr = ref("");
var userNameErr = ref("");
var phoneErr = ref("");
var emailErr = ref("");
var referralCodeErr = ref("");
var customReferralCode = ref("");
var date_of_birth = ref(null);
var editable = ref(false);
var editing = ref(false);
var isUpdatingAvt = ref(false);
var isChangedAvt = ref(false);
var orientation = ref(0);
var avatarFileName = ref(null as any);

var showChangeEmailModal = ref(false);
useSeoMeta({
  title: t('AppRouteTitle.ProfileInfoComponent')
});

onMounted(async () => {
  moment().daysInMonth();
  await loadData();
});
async function getProfileInfo() {
  return await userService.profileInfo();
}
async function getUserInfo() {
  return await userService.profileInfo(profileId.value);
}

async function loadData(forceRefresh = false) {
  isRefreshing.value = true;
  if (route.params.profileId) {
    getUserInfo().then(async (res) => {
      if (res.status == HttpStatusCode.Ok) {
        profileId.value = res.data.id;
        profileData.value = JSON.parse(JSON.stringify({
          ...res.data,
          gender: res.data.gender != null ? res.data.gender : null,
          email: res.data.email
        }));
        date_of_birth.value = res.data.date_of_birth
          ? res.data.date_of_birth
          : null;
        let profilePictureTemp = res?.data?.profile_picture ? ((appConst.provider_img_domain.some(e => res?.data?.profile_picture?.includes(e))) ? res?.data?.profile_picture : domainImage + res?.data?.profile_picture) : null;
        profileAvt.value = profilePictureTemp;
        profileAvtUpdate.value = profilePictureTemp;
        editable.value = await checkEditable();
        profileDataBackup.value = {
          ...res.data,
          gender: res.data.gender != null ? res.data.gender : null,
          date_of_birth: res.data.date_of_birth ? res.data.date_of_birth : "",
          email: res.data.email
        };
      }
      else {
        router.push({
          path: appRoute.LoginComponent,
          query: {
            redirect: JSON.stringify(route.fullPath)
          }
        });
        profileData.value = null
      }
      isRefreshing.value = false;
    }).catch((err) => {
      isRefreshing.value = false;
    });
  }
  else {
    getProfileInfo().then(async (res) => {
      if (res.status == HttpStatusCode.Ok) {
        profileId.value = res.data.id;
        profileData.value = {
          ...res.data,
          gender: res.data.gender != null ? res.data.gender : null,
          email: res.data.email
        };
        date_of_birth.value = res.data.date_of_birth
          ? res.data.date_of_birth
          : null;
        let profilePictureTemp = res?.data?.profile_picture ? ((appConst.provider_img_domain.some(e => res?.data?.profile_picture?.includes(e))) ? res?.data?.profile_picture : domainImage + res?.data?.profile_picture) : null;
        profileAvt.value = profilePictureTemp;
        profileAvtUpdate.value = profilePictureTemp;
        editable.value = await checkEditable();
        profileDataBackup.value = {
          ...res.data,
          gender: res.data.gender != null ? res.data.gender : null,
          date_of_birth: res.data.date_of_birth ? res.data.date_of_birth : "",
          email: res.data.email
        };
        saveUserInfo()
      }
      else {
        router.push({
          path: appRoute.LoginComponent,
          query: {
            redirect: JSON.stringify(route.fullPath)
          }
        });
        profileData.value = null
      }
      isRefreshing.value = false;
    }).catch((err) => {
      isRefreshing.value = false;
    });
  }
}
var onRefresh = () => {
  loadData(true);
};
async function checkEditable() {
  let resInfo = await localStorage.getItem(appConst.storageKey.userInfo);

  let infoLocal = JSON.parse(resInfo as string);
  if (infoLocal.id == profileId.value) return true;
  return false;
}

async function logout() {
  return await userService.logout().then((res) => {
    if (res.status == HttpStatusCode.Ok) {
      onRefresh();
    }
  });
}

async function checkExistingEmail() {
  if (profileData.value?.email && profileData.value?.email?.length) {
    let body = {
      key: profileData.value.email,
      type: appConst.check_exist_enum.email,
    };

    let check = await userService.check_exist(body);
    check = check.body.data;
    if (check && profileData.value.email != profileDataBackup.value.email) {
      emailErr.value = t('ProfileInfoComponent.email_da_duoc_su_dung');
    }
    else {
      emailErr.value = "";
    }
  }
}

function emailValidation(checkExist = false) {
  let re = appConst.validateValue.email;
  if (profileData.value?.email && profileData.value?.email?.length) {
    if (!re.test(profileData.value?.email)) {
      emailErr.value = t('ProfileInfoComponent.email_khong_dung_dinh_dang');
    }
    else if (checkExist) {
      checkExistingEmail();
    }
  }
  else {
    emailErr.value = "";
  }
}
function nameValidation() {
  if (!profileData.value?.name || !profileData.value?.name?.length) {
    nameErr.value = t('ProfileInfoComponent.vui_long_nhap_ten');
    return
  }
  else {
    nameErr.value = '';
  }
}
async function phoneValidation() {
  let re = appConst.validateValue.phone;
  let test = await re.test(validPhone(profileData.value?.phone));
  if (!profileData.value?.phone || !profileData.value?.phone?.length) {
    phoneErr.value = t('ProfileInfoComponent.vui_long_nhap_sdt');
    return;
  }
  if (!test) {
    phoneErr.value = t('ProfileInfoComponent.sdt_khong_dung');
    return;
  } else {
    phoneErr.value = "";
    checkExistingPhone();
  }
}

async function checkExistingPhone() {
  let body = {
    key: validPhone(profileData.value?.phone),
    type: appConst.check_exist_enum.phone,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check && validPhone(profileData.value?.phone) != validPhone(profileDataBackup.value?.phone)) {
    phoneErr.value = t('ProfileInfoComponent.sdt_da_duoc_su_dung');
  }
  else {
    phoneErr.value = "";
  }
}

async function userNameValidation() {
  let re = appConst.validateValue.userName;
  let test = re.test(profileData.value?.user_name);
  if (!profileData.value?.user_name || !profileData.value?.user_name?.length) {
    userNameErr.value = t('ProfileInfoComponent.vui_long_nhap_ten_dang_nhap');
    return;
  } else if (!test) {
    userNameErr.value =
      t('ProfileInfoComponent.ten_dang_nhap_regex');
    return;
  } else {
    userNameErr.value = "";
    checkExistingUserName();
    return;
  }
}
async function checkExistingUserName() {
  let body = {
    key: profileData.value?.user_name,
    type: appConst.check_exist_enum.user_name,
  };
  let check = await userService.check_exist(body);
  check = check.body.data;
  if (check && profileData.value?.user_name != profileDataBackup.value?.user_name) {
    userNameErr.value = t('ProfileInfoComponent.ten_dang_nhap_da_duoc_su_dung');
  } else {
    userNameErr.value = "";
  }
  return;
}

async function referralCodeValidation() {
  let re = appConst.validateValue.referral_code; // Same validation as username
  let test = re.test(customReferralCode.value);
  if (!customReferralCode.value || !customReferralCode.value?.length) {
    referralCodeErr.value = t('ProfileInfoComponent.vui_long_nhap_ma_gioi_thieu');
    return;
  } else if (!test) {
    referralCodeErr.value = t('ProfileInfoComponent.ma_gioi_thieu_regex');
    return;
  } else {
    referralCodeErr.value = "";
    checkExistingReferralCode();
    return;
  }
}

async function checkExistingReferralCode() {
  let body = {
    key: customReferralCode.value,
    type: appConst.check_exist_enum.referral_code, // Assuming this type exists in the API
  };
  try {
    let check = await userService.check_exist(body);
    check = check.body.data;
    if (check) {
      referralCodeErr.value = t('ProfileInfoComponent.ma_gioi_thieu_da_duoc_su_dung');
    } else {
      referralCodeErr.value = "";
    }
  } catch (error) {
    // If the API doesn't support referral_code type, just clear the error
    referralCodeErr.value = "";
  }
  return;
}

async function updateProfile() {
  isUpdating.value = true;
  
  // Prepare the profile data for update
  let updateData = {
    ...profileData.value,
    phone: validPhone(profileData.value?.phone),
    date_of_birth: date_of_birth?.value
      ? moment(date_of_birth?.value).format(appConst.formatDate.toSave)
      : null,
  };
    // If user is creating a custom referral code and doesn't have one yet
  if (!profileData.value?.referral_code && customReferralCode.value) {
    updateData.referral_code = customReferralCode.value.toUpperCase();
  }
  
  userService.updateInfo(updateData).then(async (res) => {
    if (res.status == HttpStatusCode.Ok) {
      // localStorage.setItem(
      //   appConst.storageKey.userInfo,
      //   JSON.stringify(res?.body?.data)
      // );
      // profileData.value = res?.body?.data;
      // profileDataBackup.value = res?.body?.data;
      // date_of_birth.value = res?.body?.data.date_of_birth
      //   ? res?.body?.data?.date_of_birth
      //   : "";
      await onRefresh();
      editing.value = false;
      toast.success(t('ProfileInfoComponent.cap_nhat_thanh_cong'));
    }
    else if (res.status == HttpStatusCode.Unauthorized) {
      router.push({
        path: appRoute.LoginComponent,
        query: {
          redirect: JSON.stringify(route.fullPath)
        }
      });
      profileData.value = null
    }    else {
      userNameValidation();
      phoneValidation();
      referralCodeValidation();
      // emailValidation();
      toast.error(t('ProfileInfoComponent.cap_nhat_that_bai'));
      hightlightError()
    }
    isUpdating.value = false;
  }).catch(() => {

    userNameValidation();
    phoneValidation();
    referralCodeValidation();
    // emailValidation();
    toast.error(t('ProfileInfoComponent.cap_nhat_that_bai'));
    hightlightError()
  });
}
function resetProfileData() {
  profileData.value = {
    ...profileDataBackup.value,
    gender:
      profileDataBackup.value?.gender != null
        ? profileDataBackup.value?.gender
        : null,
  };  date_of_birth.value = profileDataBackup.value?.date_of_birth
    ? profileDataBackup.value?.date_of_birth
    : "";

  // Reset custom referral code and clear errors
  customReferralCode.value = "";
  referralCodeErr.value = "";

  editing.value = false;
}
async function fileChangeInput(fileInput: any, childProduct?: any) {
  if (fileInput.target.files.length) {
    avatarFileName.value = fileInput?.target?.files[0];
    // nuxtApp.$Exif.getData(fileInput.target.files[0], function(this:any){
    //   const allMetaData = nuxtApp.$Exif.getAllTags(this);
    //   console.log(allMetaData); // Log all EXIF data
    // });

    // await new Promise(resolve => {
    // 	EXIF.getData(fileInput.target.files[0], (e:any) => {
    // 		resolve(EXIF.getAllTags(fileInput.target.files[0]))
    // 	})
    // })
    if (avatarFileName?.value?.size > appConst.image_size.max) {
      let imgErr = t('ProfileInfoComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
      toast.error(imgErr);
    }
    else {
      const reader = new FileReader();
      reader.onload = async (e: any) => {
        const image = new Image();
        image.src = e.target?.result;

        let orientationExif;
        if (avatarFileName.value?.type != 'image/webp') {
          orientationExif = await exifr.orientation(image) || 0;
        }
        else orientationExif = 0;
        orientation.value = orientationExif ? orientationExif : 0;
        profileAvtUpdate.value = image.src;
      }
      await reader.readAsDataURL(fileInput.target.files[0]);
    }


  }
}

function updateProfileAvt() {
  isUpdatingAvt.value = true;
  let profilePicture = {
    path: profileAvtUpdate.value,
    object_type: appConst.object_type.user,
    title: profileData.value.name + " profile picture",
    description: profileData.value.name + " description",
    index: 0,
    orientation: orientation.value ? orientation.value : 0,
    isEdit: false,
    parent_id: profileData.value.id,
  };
  imageService
    .insertImage(profilePicture)
    .then(async (res) => {
      if (res.status == HttpStatusCode.Ok) {
        toast.success(t('ProfileInfoComponent.cap_nhat_anh_dai_dien_thanh_cong'));
        let resInfo = await localStorage.getItem(appConst.storageKey.userInfo);
        let infoLocal = JSON.parse(resInfo as string);
        infoLocal.profile_picture = domainImage + res.body.data.images.path;
        localStorage.setItem(
          appConst.storageKey.userInfo,
          JSON.stringify(infoLocal)
        );
        isUpdatingAvt.value = false;
        profileData.value = {
          ...profileData.value,
          profile_picture: res.body.data.images.path,
        };
        profileAvt.value = domainImage + res.body.data.images.path;
        profileAvtUpdate.value = domainImage + res.body.data.images.path;
      } else {
        toast.error(res.body?.message ?? t('ProfileInfoComponent.cap_nhat_anh_dai_dien_that_bai'));
        isUpdatingAvt.value = false;
      }
    })
    .catch((err) => {
      toast.error(t('ProfileInfoComponent.cap_nhat_anh_dai_dien_that_bai'));
      isUpdatingAvt.value = true;
    });
  // imageService.insertImagesZalo(state.profileAvtUpdate).then(result => {
  //     if(result.status == HttpStatusCode.Ok){

  //         setState({
  //             showModalUpdateAvt: false,
  //             isUpdatingAvt: false
  //         });
  //     }
  //     else {

  //     }
  // }).catch(err =>  console.error(err));
}

async function encodeBase64(image: any) {
  await getBase64ImageFromUrl(image);
}

async function getBase64ImageFromUrl(imageUrl: any) {
  // return axios.get(imageUrl, {
  //     responseType: 'blob',
  //     responseEncoding: "base64",
  // }).then(async (response) => {
  //     var reader = new FileReader();
  //     reader.onloadend = fileLoadedEvent => {
  //         const base64Image = fileLoadedEvent.target?.result.toString().replace("application/octet-stream", response.headers["content-type"]);
  //         return base64Image;
  //     };
  //     return reader.readAsDataURL(response.data);
  // });
}

function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}
function saveUserInfo() {
  localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(profileData.value))
}

function copyReferralCode() {
  if (profileData.value?.referral_code) {
    navigator.clipboard.writeText(profileData.value.referral_code).then(() => {
      toast.success(t('ProfileInfoComponent.da_sao_chep_ma_gioi_thieu'));
    }).catch(() => {
      toast.error(t('ProfileInfoComponent.sao_chep_that_bai'));
    });
  }
}
</script>

<style lang="scss" src="./ProfileInfoStyles.scss"></style>
