.side-bar-overlay-container {
  overflow: hidden;
  z-index: 1001 !important;
  max-width: 100% !important;

  @keyframes slide-left {
    0% {
      right: -50%;
    }

    100% {
      right: 0;
    }
  }

  .side-bar-container {
    background: white;
    position: absolute;
    right: 0;
    height: fit-content;
    animation: slide-left 0.5s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 480px;
    max-width: 85%;
    transition: all 0.5s ease;
    padding-top: 50px;

    &>.side-bar-content-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow-y: auto;

      &>.user-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px;
        height: auto;
        text-transform: none;
        // flex: 1;
        gap: 10px;

        & .user-info-buttons {
          display: flex;
          flex: 1;
          width: 100%;
        }

        & a {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          // flex-direction: column;
        }

        & img.user-avatar {
          width: 75px;
          height: 75px;
          border-radius: 50%;
          // margin-right: 10px;
          object-fit: cover;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        & .user-name {
          font-size: 20px;
          font-weight: 700;
        }
      }

      & .side-bar-btn {
        display: flex;
        flex: 1;
        padding: 10px;
        margin: 5px 10px;
        text-transform: none;

        &>.v-btn__content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;

          &>a {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: inherit;
            gap: 10px;
          }
        }
      }

      &>.side-bar-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        margin: 5px 10px;
        text-transform: none;

        &>.side-bar-item {
          display: flex;
          padding: 0 10px;
          height: auto;
          color: #545454;
          text-transform: none;
          font-weight: 600;
          border-radius: 0;

          &.active {
            // color: var(--primary-color-1);
            background: linear-gradient(to right, color-mix(in srgb, var(--primary-color-1) 10%, transparent) 50%, transparent);
            border-left: 2px solid var(--primary-color-1);
          }

          &>.v-btn__content{
            display: flex;
            flex: 1;
            padding: 10px 0;

            &>a.side-bar-item-content {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              flex: 1;
              gap: 15px;
              font-size: 17px;

              & > svg {
                color: var(--primary-color-1);
              }
            }

            
          }

          & .side-bar-badge .v-badge__badge {
            font-weight: 700 !important;
            color: white;
          }
        }

        &>.side-bar-item.language-options {
          display: flex;
          padding: 0 10px;
          height: auto;
          color: #545454;
          text-transform: none;
          font-weight: 600;
          border-radius: 0;

          &>.side-bar-item-content {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex: 1;
            gap: 15px;
            font-size: 17px;
            padding: 10px 0;
            align-items: flex-start;

            & > svg {
              color: var(--primary-color-1);
            }

            & > div {
              display: flex;
              gap: 10px;
              flex-wrap: wrap;

            }
          }

          & .language-option {
            font-size: 13px;
            padding: 3px 10px;
            height: fit-content;
            cursor: pointer;
            background: #f4f4f4;
            color: #565656;
            border-radius: 2em;
            font-weight: 600;
            white-space: nowraps;

            &.active{
              background-color: #565656;
              color: white;
            }
          }
        }
        & > .other-detail-options.download-app-detail-options{
          display: grid;
          justify-content: center;
          gap: 10px;
          align-items: center;
          align-content: center;
  
          & > .other-option.download-app-option{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: unset;
            width: auto;
            padding: 10px;
            margin: 0;
            flex: 1;
            max-width: 200px;
            background: #ececec;
            border: thin solid white;
            border-radius: 15px;
            color: black;
            flex-wrap: nowrap;
            border: 1px solid #545454;
  
            & > svg{
              font-size: 25px;
            }
  
            & > div{
              display: flex;
              flex-direction: column;
              line-height: 1;
              margin-left: 5px;
              // flex: 1;
              
  
              & > em{
                font-style: normal;
                font-size: 11px;
                white-space: nowrap;
                font-weight: 600;
  
                &.android{
                  text-transform: uppercase;
                }
              }
  
              & > span{
                font-size: 15px;
                font-weight: 700;
                white-space: nowrap;
              }
            }
          } 
        }
  
        & > .other-detail-options.grid{
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
  
          & > .other-option{
            width: 100px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            justify-content: center;
            align-items: center;
            padding: 0;
            margin: 0;
  
            & > .option-icon{
              width: 100px;
              height: 100px;
              border-radius: 10px;
              color: var(--primary-color-1);
              background-color: #fff6f8;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 30px;
            } 
            & > .option-label{
              white-space: break-spaces;
              text-align: center;
            }
          }
        }
      }
      
      & .version {
        margin: 30px auto;
        text-align: center;
      }
      
      
    }
  }
}