import { appConst } from '~/assets/AppConst';
import { UserService } from '~/services/userService/userService';
import { useNuxtApp } from '#app';
import { ref } from 'vue';

export function useLogout() {
  const userService = new UserService();
  const nuxtApp = useNuxtApp();
  const logingOut = ref(false);

  async function logout({
    onAfterLogout,
    onBeforeLogout,
    clearProfileData,
    clearAuthData,
  }: {
    onAfterLogout?: () => void,
    onBeforeLogout?: () => void,
    clearProfileData?: () => void,
    clearAuthData?: () => void,
  } = {}) {
    logingOut.value = true;
    if (onBeforeLogout) onBeforeLogout();
    // Always clear local data regardless of API response
    const clearLocalData = () => {
      if (clearAuthData) clearAuthData();
      if (clearProfileData) clearProfileData();
      localStorage.removeItem(appConst.storageKey.token);
      localStorage.removeItem(appConst.storageKey.userInfo);
      sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
      setTimeout(() => {
        nuxtApp.$emit(appConst.event_key.logout);
        if (onAfterLogout) onAfterLogout();
      }, 1000);
    };
    try {
      await userService.logout();
      clearLocalData();
    } catch (error) {
      console.warn('Logout API failed, clearing local data anyway:', error);
      clearLocalData();
    } finally {
      logingOut.value = false;
    }
  }

  return { logout, logingOut };
}
