@media screen and (max-width: 720px) {
  .quotation-menu {
    display: none !important;
  }
}
.profile-skeleton-container{
  display: flex;
  flex-direction: column;
  flex: 1;
  // background-color: white;
  position: relative;
  padding: 10px;
  overflow: auto;
  font-size: calc(var(--font-size) * 1.3);
  background-color: var(--color-background-2);
  & .skeleton-content {
    justify-content: center;
    padding-top: 65px;
    & .v-skeleton-loader__avatar {
      height: 75px;
      max-height: unset;
      width: 75px;
      max-width: unset;
    }  

    & .skeleton-content-btn {
      margin-top: 10px;
      width: 100%;
      height: 50px;
    }
  }
}
.profile-container{
  display: flex;
  flex-direction: column;
  flex: 1;
  // background-color: white;
  position: relative;
  padding: 0 0 50px 0 !important;
  overflow: auto;
  // background-image: url('~/assets/image_03_04_2024/profie-background.jpg');
  background-size: 100%;
  font-size: calc(var(--font-size) * 1.3);
  background-color: var(--color-background-2);
  max-width: var(--max-width-content-view-1024) !important;

  & .login-button {
    font-size: 1em;
    color: var(--primary-color-1);
    font-weight: bold;
    background: transparent;
    border: none;
    padding: 0;
  }

  & .login-quote {
    color: var(--color-text-note);
  }

  & .login-right-button {
    background-color: transparent;
    border: transparent;
    height: 100%;
    width: 30px;
    font-size: 1.5em;
    padding: 0;
    margin: auto 0 auto auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  & .menu-public {
    padding: 10px;
    width: 100%;
    margin-top: 10px;

    & > a {
      width: 100%;
      gap: 10px;
    }
  }

  // & .menu-button.delete-account {
  //   color: var(--primary-color-2);
    
  //   > .button-go-cart-left-icon, span {
  //     color: inherit;
  //   }
  // }

  // & .go-cart-button {
  //   padding: 5px 5%;
  //   background: white;
  //   border: none;
  //   border-radius: 2px;
  //   width: 100%;
  //   /* box-shadow: 0 0 10px rgb(0, 0, 0, .1); */
  //   display: flex;
  //   justify-content: flex-start;
  //   height: unset;
  //   box-shadow: none;
  //   border-bottom: thin solid #ccc;
  //   gap: 10px;
  //   font-size: 1em;
  //   text-transform: none;

  //   & > span {
  //     color: var(--primary-color-1);
  //   }
  // }

  // & .button-go-cart-left-icon {
  //   color: var(--primary-color-1);
  //   font-size: 1.5em;
  // }

  // & .go-cart-button-right-icon {
  //   width: 28px;
  //   height: 28px;
  //   background: var(--color-button-error);
  //   border-radius: 50%;
  //   padding: 0;
  //   display: flex;
  //   color: white;
  //   border: none;
  //   margin-left: 5px;
  //   font-size: 0.8em;
  //   font-weight: 600;
  //   align-items: center;
  //   justify-content: center;
  // }

  // & .primary-user-info {
  //   justify-content: flex-start;
  //   align-items: center;
  //   flex-wrap: nowrap;
  //   gap: 10px;
  //   background: white;
  //   padding: 0 10px;
  //   margin: 0 10px;
  // }

  // & .user-detail {
  //   font-size: 0.9em;
  //   color: var(--color-text-note);
  //   font-weight: 500;
  // }
  // & .menu-link {
  //   display: flex;
  // }
  // & .menu-button {
  //   padding: 10px 5%;
  //   background: white;
  //   display: flex;
  //   flex: 1;
  //   align-items: center;
  //   justify-content: flex-start;
  //   border: none;
  //   border-bottom: thin solid #ccc;
  //   gap: 10px;
  //   font-size: 1em;
  //   margin: 0 10px;
  //   color: var(--primary-color-1);
  // }

  // & .menu-button > span {
  //   color: var(--primary-color-1);
  //   font-weight: 500;
  // }

  // & .menu-button > span > span {
  //   color: var(--primary-color-1);
  // }

  // & .my-shop-button {
  //   width: 100%;
  //   padding: 10px 5%;
  //   background: var(--primary-color-1);
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   border: none;
  //   border-radius: 2em;
  //   gap: 10px;
  //   font-size: 1em;
  //   margin: 1em 0px;
  //   color: white;
  //   font-weight: 600;
  // }

  // & .link-to-shop {
  //   margin: 0 10px;
  //   padding: 0 10px;
  // }

  & .primary-user-info{
    padding: 100px 15px 0;
    margin-bottom: 75px;

    & > .primary-info-content.skeleton{
      background: white;
    }
    & > .primary-info-content{
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 15px;
      // background: white;
      // background-image: url('~/assets/image_03_04_2024/user-info-background.jpg');
      background-size: 100% 100%;
      box-shadow: 0 0 5px rgb(0,0,0,20%);
      margin-bottom: -50px;

      & > .user-info-buttons{
        display: flex;
        justify-content: space-between;
        height: 50px;
        width: 100%;

        > .button-skeleton {
          width: 55%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          font-size: .75em;
          font-weight: bold;
          padding: 0 !important;
        }

        & > button {
          width: 55%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          font-size: .75em;
          font-weight: bold;
          white-space: nowrap;
        }

        & > .profile-info-button{
          background-color: var(--primary-color-1);
          color: white;
          border-radius: 0 0 0 15px;
          clip-path: polygon(0 0, calc(100% - 35px) 0, 100% 100%, 0% 100%);
          margin-right: -13px;
          padding-right: 7%;
          white-space: nowrap;
        }

        & > .my-shop-button{
          background-color: #efefef;
          color: #595e63;
          border-radius: 0 0 15px 0;
          clip-path: polygon(0 0, 100% 0, 100% 100%, 35px 100%);
          margin-left: -13px;
          padding-left: 7%;
          justify-content: center;
          overflow: hidden;
          white-space: balance;
          text-overflow: ellipsis;
          flex: 1;

          & > svg{
            width: 25px;
            min-width: 25px;
          }

          & > span {
            flex: 1;
            // overflow: hidden;
            // text-overflow: ellipsis;
          }
        }
      }

      & .user-avatar {
        margin: 10px;
        width: 90px;
        min-width: 90px;
        height: 90px;
        border: 3px solid white;
        margin-top: -35px;
        object-fit: cover;
        border-radius: 50%;
        cursor: pointer;
      }
    
      & .none-avatar {
        /* flex: 1; */
        margin: 10px;
        justify-content: center;
        width: 75px;
        height: 75px;
        object-fit: contain;
        border-radius: 50%;
        cursor: pointer;
      }

      & .name{
        font-weight: bold;
        font-size: 1.3em;
        color: black;
      }

      & .user-detail{
        color: #595e63;
        font-size: .8em;
      }
    }

    
  }

  & .other-detail{
    padding: 15px;
    filter: drop-shadow(0 0 2px rgb(0, 0, 0, 10%));

    & > :first-child {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
    & > :last-child {
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
    }
    & > a {
      width: 100%;
    }
    & .other-detail-button {
    padding: 10px 15px 0;
    background: white;
    border: none;
    display: flex;
    width: 100%;
    border-radius: inherit;
    align-items: center;

      & > .icon {
        font-size: 1.3em;
        justify-content: center;
        color: #818086;
      }
      & > span {
        font-weight: bold;
        align-items: center;
        margin-right: auto;
      }
      & > .icon-right {
        font-size: 1.3em;
        justify-content: center;
      }
    }
    & > .other-detail-options {
      gap: 10px;
      padding: 10px;
      justify-content: space-between;
      background-color: white;

      & > button, > div, > a {
        width: 30%;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: none;
        gap: 5px;
        

        & > .button-icon{
          background-color: #fff6f8;
          width: 60%;
          aspect-ratio: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--primary-color-1);
          border-radius: 10px;
          font-size: 1.5em;
          position: relative;

          & > em {
            border-radius: 2em;
            color: white;
            background: var(--primary-color-1);
            min-width: 25px;
            height: 25px;
            font-size: 15px;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: normal;
            position: absolute;
            top: -10px;
            right: -10px;
            line-height: 1;
            font-weight: 500;
  
            & > span {
              font-size: 0.8em;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
        & > span {
          font-size: 0.8em;
          font-weight: 600;
          white-space: nowrap;
        }
        
      }

      & > .other-option{
        padding: 0 20px;
        margin: 10px 0;
        width: 100%;
        justify-content: space-between;
        flex-direction: row;
        flex-wrap: wrap;
        cursor: pointer;

        & > .icon-right {
          font-size: 1.3em;
          justify-content: center;
          color: #818086;
        }
        & > .language-option{
          font-size: 0.65em;
          padding: 3px 10px;
          height: fit-content;
          cursor: pointer;
          background: #f4f4f4;
          color: #565656;
          border-radius: 2em;
          font-weight: 600;
          white-space: nowraps;
        }
        & > .radius-option{
          font-size: 0.65em;
          padding: 3px 10px;
          height: fit-content;
          cursor: pointer;
          background: #f4f4f4;
          color: #565656;
          border-radius: 2em;
          text-transform: uppercase;
          font-weight: 600;
          white-space: nowraps;
        }
        & > .radius-option.active, > .language-option.active{
          background-color: #565656;
          color: white;
        }
        & > .radius-option.first, .language-option.first{
          margin-left: auto;
        }

        & > .language-toggle{
          background-color: #565656;
          color: white;
          border-radius: 2em;
          justify-content: space-around;
          position: relative;
          height: 35px;
          padding: 0 5px;
          width: 80px;
          display: flex;

          & > .toggle{
            position: absolute;
            top: 5px;
            left: calc(50% - 5px);
            // right: 5px;
            background: white;
            width: 50%;
            height: calc(100% - 10px);
            border-radius: 2em;
            z-index: 1;
            transition: all .3s ease;
          }

          & > .toggle.left{
            left: 5px;
            // right: unset;
          }

          & > .label-toggle{
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 15px;
            font-weight: bold;
            text-transform: uppercase;
            width: 45%;
            height: 100%;
            padding-top: 5px;
            color: white;
            z-index: 2;
            cursor: pointer;
            transition: all .3s ease;
          }
          & > .label-toggle.active{
            width: 55%;
            color: #565656;
          }
        }

        
      }

      & > .other-option.language-options{
        justify-content: flex-start;
      }

      & > .other-option.notification-options{
        justify-content: flex-start;

        & > .value{
          font-size: 15px;
          font-weight: 700;
          font-style: italic;

          &.on{
            color: var(--primary-color-1);
          }
          &.off{
            color: var(--primary-color-2);
          }
        }

        & > .change-value{
          padding: 0px 10px;
          font-size: 15px;
          line-height: 1;
          color: #565656;
          font-weight: 500;
          border: thin solid;
          border-radius: 2em;
          font-style: normal;
          margin-left: 10px;
        }
      }
    }

    & > .other-detail-options.social-detail-options{
      justify-content: center;
      padding: 0 20px 10px;

      & > .other-option.social-option{
        justify-content: flex-start;
        flex: unset;
        width: auto;
        padding: 10px;
        margin: 0;
      } 
      & .social-icon{
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        object-fit: contain;
      }
    }

    & > .other-detail-options.download-app-detail-options{
      display: flex;
      justify-content: center;
      gap: 10px;

      & > .other-option.download-app-option{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex: unset;
        width: auto;
        padding: 10px;
        margin: 0;
        flex: 1;
        max-width: 200px;
        background: #111111;
        border: thin solid white;
        border-radius: 15px;
        color: white;
        flex-wrap: nowrap;

        & > svg{
          font-size: 35px;
        }

        & > div{
          display: flex;
          flex-direction: column;
          line-height: 1;
          margin-left: 5px;
          // flex: 1;
          

          & > em{
            font-style: normal;
            font-size: 13px;
            white-space: nowrap;
            font-weight: 600;

            &.android{
              text-transform: uppercase;
            }
          }

          & > span{
            font-size: 20px;
            font-weight: 700;
            white-space: nowrap;
          }
        }
      } 
    }

    & > .other-detail-options.grid{
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      & > .other-option{
        width: 100px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        justify-content: center;
        align-items: center;
        padding: 0;
        margin: 0;

        & > .option-icon{
          width: 100px;
          height: 100px;
          border-radius: 10px;
          color: var(--primary-color-1);
          background-color: #fff6f8;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 30px;
        } 
        & > .option-label{
          white-space: break-spaces;
          text-align: center;
        }
      }
    }
    
  }

  & > .version{
    width: 100%;
    text-align: center;
    font-size: 15px;
  }

  & > .profile-footer{
    display: flex;
    flex: unset;
    // justify-content: space-between;
    justify-content: center;
    padding: 15px;
    filter: drop-shadow(0 0 2px rgb(0, 0, 0, 10%));

    & > .profile-footer-button{
      width: 48%;
      background: white;
      padding: 10px;
      border-radius: 15px;
      font-size: 0.9em;
      font-weight: 600;
      white-space: nowrap;
    }
  }
  
}