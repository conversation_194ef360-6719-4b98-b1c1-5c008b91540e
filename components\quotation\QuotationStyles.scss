.quotation-container {
  display: flex;
  flex-direction: column;

  &>.quotation-actions {
    position: sticky;
    top: 54px;
    width: 100%;
    display: flex;
    z-index: 10;
    background: white;

    &>button {
      flex: 1;
      padding: 5px;
      text-align: center;
      min-height: 50px;
      border-radius: 0;
      box-shadow: none;
      display: flex;

      // &#check-quotation{
      //   background: var(--secondary-color-1);
      //   color: #868686;
      // }

      &>.v-btn__content {
        display: flex;
        flex-direction: column;
        color: #545454;
        font-weight: 700;
        flex: 1;
        height: 100%;
        width: 100%;

        &>a {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &>span {
          font-weight: 700;
        }

        &>em {
          color: var(--primary-color-1);
        }
      }
    }
  }

  &>.flex-1 {
    flex: 1;
  }

  &>.quotation-list-container {
    display: flex;
    flex-direction: column;

    &>.quotation-list-content {
      flex: 1;

      &>.quotation-item-container {
        display: flex;
        padding: 10px;
        min-height: 50px;
        cursor: pointer;

        &:hover {
          background: #f5f6fa;
        }

        &>.v-list-item__content {
          flex: 1;
          display: flex;

          &>a {
            flex: 1;
            display: flex;

            &>hr {
              margin: 0 5px;
              height: 100%;
              max-height: unset;

            }

            &>span {
              flex: 1;
              align-self: center;
              max-width: 33%;
            }

            & span.status {
              flex: 1;
            }

            & .name-notes {
              display: flex;
              flex-direction: column;
              flex: 1;
              min-width: 33%;

              &>.name {
                font-size: 15px;
                font-weight: 600;
                color: #545454;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &>.notes {
                font-size: 13px;
                font-weight: 500;
                color: #868686;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &>.supplier-name {
                font-size: 13px;
                font-weight: 600;
                color: var(--primary-color-1);
                height: auto;
                align-self: flex-start;
              }
            }

            & span.from-to {
              text-align: center;
              align-self: center;
            }

            & span.status {
              text-align: center;
            }
          }

        }


      }
    }

    &>.none-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: center;
      justify-content: center;
      flex: 1;
      background: white;
    }
  }
}