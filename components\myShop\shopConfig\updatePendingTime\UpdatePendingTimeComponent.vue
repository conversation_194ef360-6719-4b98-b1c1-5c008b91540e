<template>
    <VueFinalModal class="my-modal-container" :modal-id="'update_pending_time_modal'" :keep-overlay="true" :overlay-behavior="'persist'"
        :hide-overlay="false" :hide-overlay-on-blur="false" content-class="v-stack update-pending-time-container"
        :click-to-close="false" :esc-to-close="false" v-on:before-close="() => {
            showUpdatePendingTimeModal = false
        }" v-model="showUpdatePendingTimeModal" contentTransition="vfm-slide-up">
        <SubHeaderV2Component :title="props.title ?? $t('UpdatePendingTimeComponent.thoi_gian_cho_nhan_don_giao')">
            <template v-slot:header_left></template>
        </SubHeaderV2Component>
        <div class="update-pending-time-content-container">
            <VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
                class="update-pending-time-content">
                <div class="form-field-container">
                    <div class="h-stack input-group-container">
                        <Field :placeholder="$t('UpdatePendingTimeComponent.thoi_gian_cho')" class="custom-input"
                            :validate-on-input="true" name="pending_time_value" type="number" id="pending_time_value_input"
                            v-bind:model-value="pending_time.value" v-on:update:model-value="($event: any) => {
                                pending_time.value = $event;
                            }"></Field>
                        <v-select class="custom-v-select mt-2 time-unit-select" label="" :menu-props="{
                            contentClass: 'text-center' 
                        }" :placeholder="$t('UpdatePendingTimeComponent.don_vi_thoi_gian')" v-model="pending_time.unit"
                            :item-value="'value'" :menu-icon="''" :items="appConst.time_units" variant="plain">
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :title="$t(`TimeUnit.${item.raw.key}`)"></v-list-item>
                            </template>
                            <template v-slot:selection="{ item }">
                                <span>{{ $t(`TimeUnit.${item.raw.key}`) }}</span>
                            </template>
                            <template v-slot:append-inner></template>
                        </v-select>
                    </div>

                    <ErrorMessage class="error-message" name="pending_time_value"></ErrorMessage>
                </div>

                <button hidden ref="submitFormButton" v-on:click="() => {
                    submit()
                }"></button>
            </VeeForm>

        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' v-on:click="() => close()">
                {{ $t('UpdatePendingTimeComponent.huy') }}
            </button>
            <button class='save-button' v-on:click="() => {
                submitFormButton?.click();
            }">
                <span>{{ $t('UpdatePendingTimeComponent.luu') }}</span>
            </button>
        </div>
    </VueFinalModal>


</template>
<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, nonAccentVietnamese } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { DeliveryFeeConfig, type delivery_fee_condition, type delivery_fee_config, condition_params, operators, condition_relationship } from '../ShopConfigDTO';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
    init_value: null,
    mode: null,
    title: null
})

var nuxtApp = useNuxtApp();
const { t, locale, locales } = useI18n();

var submitFormButton = ref<HTMLElement | undefined>();
const formSchema = yup.object({
    pending_time_value: yup.number().transform((value) => value == Number(value) ? value : null)
        .typeError(t('UpdatePendingTimeComponent.du_lieu_phai_la_kieu_so'))
        .notRequired()
});

var pending_time = ref({
    value: null,
    unit: null,
    unit_title: null
});
var selectLanguage = ref<any>(locale.value);
const { handleSubmit } = useForm({
    validationSchema: formSchema,
});

var showUpdatePendingTimeModal = ref(false)

var showConditionSelectValues = ref(false);
var indexSelectedCondition = ref<any>(null);
onBeforeUnmount(() => {
})
onBeforeMount(() => {
    
})
onMounted(async () => {
    pending_time.value = {
        value: props.init_value?.value ?? 30,
        unit: props.init_value?.unit ?? 'minute',
        unit_title: props.init_value?.unit ?? 'minute',
    };
    showUpdatePendingTimeModal.value = true;
})
onUpdated(() => {
})

function close(value?: any) {
    emit('close', value);
}

async function submit() {

    var valid = await formSchema.isValid({
        pending_time_value: pending_time.value.value, 
    });
    if (valid) {
        emit('submit', JSON.parse(JSON.stringify(pending_time.value)));
    }
    else {
        console.log('form invalid')
    }
}

</script>

<style lang="scss" src="./UpdatePendingTimeStyles.scss"></style>