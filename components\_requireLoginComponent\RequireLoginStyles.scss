.require-login-popup-overlay{
    z-index: 10000000 !important;
}
.require-login-popup-container {
    min-height: unset !important;
    background-color: white;
    gap: 10px;
    width: 500px !important;
    // max-height: 90%;
    max-width: 95% !important;
    padding: 0;
    // min-height: 40dvh;
    border-radius: 10px;
    background-color: white;
    z-index: 10000000 !important;
  
    & > .require-login-content-container{
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        gap: 10px;

        & > img {
            width: 355px;
            padding: 35px 10px;
            object-fit: contain;
        }
        & > span {
            font-size: 20px;
            color: #545454;
            font-weight: 600;
        }

        & > .footer-actions {
            display: flex;
            padding: 5px 10px;
            justify-content: flex-end;
            gap: 15px;
        
            & > .close-button {
              color: var(--primary-color-2);
              font-weight: 600;
              background: color-mix(in srgb, var(--primary-color-2) 10%, transparent);
              padding: 3px 20px;
              border-radius: 5px;
            }

            & > .goto-login-button{
                color: var(--primary-color-1);
                font-weight: 600;
                background: color-mix(in srgb, var(--primary-color-1) 10%, transparent);
                padding: 3px 20px;
                border-radius: 5px;
            }
          }
    }
  
    
  }