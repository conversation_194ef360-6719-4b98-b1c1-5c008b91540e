.hour-minute-picker-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .hour-minute-picker-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    height: 50vh;
    max-height: 400px;
    display: flex;
    flex-direction: column;

    & > .title-header {
      border-radius: inherit;
    }

    & > .hour-minute-picker-content-container {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;

      & > .picker-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 49%;
        height: 100%;
        padding: 0 15px;
        overflow: hidden;

        & > span {
          font-weight: bold;
          font-size: 17px;
        }

        & > .picker-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          overflow: hidden;
          & > .picker-buttons-carousel {
            height: 50px;
            width: 100%;
            overflow: visible;
            border: thin solid #ccc;
            border-left: none;
            border-right: none;

            & .picker-item {
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              transition: all 0.2s ease-in-out;
              font-size: 1em;
              user-select: none;
            }

            & .picker-item.swiper-slide-active {
              font-size: 1.3em;
              color: var(--primary-color-1);
              font-weight: bold;
            }
          }
        }
      }

      & > span {
        align-items: center;
        display: flex;
        font-size: 30px;
        font-weight: bolder;
        color: var(--primary-color-1);
        margin-top: 20px;
      }
    }

    & > .footer-actions {
      padding: 5px 10px;
      width: 100%;

      & > .submit-button {
        padding: 5px;
        background-color: var(--primary-color-1);
        color: white;
        width: 100%;
        border-radius: 7px;
      }
    }
  }
}
