<template>
	<div class="public-container">
		<div class="show-config-container" v-if="shopData?.id && !isRefreshing">
			<SubHeaderV2Component :title="$t('AppRouteTitle.ShopConfigComponent')">
			</SubHeaderV2Component>
			<div class="shop-config-content">
				<!-- general config -->
				<div class='v-stack config-item-container'>
					<div class="config-item">
						<label class="label">
							{{ $t(`ShopConfigComponent.chung`) }}
						</label>
						<div class="config-item-children">
							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.trang_thai`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value status-config">
									<div class="config-value">
										<label class="switch-config">
											<v-switch :model-value="shopConfig.general?.is_open?.value"
												v-on:update:model-value="(e: any) => {
													shopConfig.general.is_open.value = e;
												}" flat color="var(--primary-color-1)" hide-details class="my-switches" name="is_open">

											</v-switch>
											{{ $t(shopConfig.general?.is_open?.value ?
												'ShopConfigComponent.dang_mo_cua'
												: 'ShopConfigComponent.da_dong_cua') }}
										</label>
									</div>
									<div class="reason-close" v-if="!shopConfig.general?.is_open?.value">
										<label for="reason_close">{{ $t('ShopConfigComponent.ly_do') }}</label>
										<div class="reason-item"
											v-for="(key, value) in shopConfig.general?.is_open?.reason">
											<label :for="`reason_${value}`">
												{{ $t(`LocalizedLanguageName.${value}`) }}:
											</label>
											<UTextarea class="text-area-custom" autoresize :maxrows="3" :rows="1"
												:max="5000" :placeholder="$t(`ShopConfigComponent.nhap_ly_do_${value}`)"
												v-model="shopConfig.general.is_open.reason[value]"
												:id="`reason_${value}`"></UTextarea>
										</div>
									</div>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.giao_dien`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value view-config">
									<div class="config-value ">
										<label class="label">
											{{
												$t(`ShopTemplateComponent.${shopConfig?.general?.view_template?.template_id
													?? 'mac_dinh'}`) }}
										</label>
										
										<label> 
											{{ $t(`ShopConfigComponent.phong_chu`) }}: <em :style="{
												'font-family': `${shopConfig?.general?.view_template?.font}, ` + 'Nunito, Montserrat, Mulish, Roboto, sans-serif'
											}"> {{ shopConfig?.general?.view_template?.font }} </em> 
											&nbsp;<em v-if="shopConfig?.general?.view_template?.font_resize">(<span>{{ shopConfig?.general?.view_template?.font_resize > 0 ? '+' : '-' }}</span>{{ shopConfig?.general?.view_template?.font_resize }}px)</em>
										</label>
										<div class="colors"
											v-if="shopConfig?.general?.view_template?.template_id != 'mac_dinh'">
											<div class="color-preview" :style="{
												background: shopConfig?.general?.view_template?.colors?._color_1
											}"></div>
											<div class="color-preview" :style="{
												background: shopConfig?.general?.view_template?.colors?._color_2
											}"></div>
											<div class="color-preview" :style="{
												background: shopConfig?.general?.view_template?.colors?._color_3
											}"></div>
											<div class="color-preview" :style="{
												background: shopConfig?.general?.view_template?.colors?._color_4
											}"></div>
											<div class="color-preview" :style="{
												background: shopConfig?.general?.view_template?.colors?._color_5
											}"></div>

										</div>
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateViewTemplateModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.lich_nghi`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value holiday-config">
									<div class="holiday-config-item config-value">
										<label class="switch-config holiday-enable">
											<v-switch
												:model-value="shopConfig.general?.holiday_settings.holiday_mode?.enabled"
												v-on:update:model-value="(e: any) => {
													shopConfig.general.holiday_settings.holiday_mode.enabled = e;
												}" flat color="var(--primary-color-1)" hide-details class="my-switches" name="is_open">

											</v-switch>
											{{ $t(shopConfig.general?.holiday_settings.holiday_mode?.enabled ?
												'ShopConfigComponent.ap_dung'
												: 'ShopConfigComponent.khong_ap_dung') }}
										</label>
									</div>
									<div class="holiday-config-item">
										<div class="holiday-config-value">
											<label class="label">
												{{ $t(`ShopConfigComponent.ngay_nghi`) }}
											</label>
											<div class="day-off-container"
												v-if="shopConfig?.general?.holiday_settings?.days_off?.length">
												<v-chip variant="tonal"
													v-for="day in shopConfig?.general?.holiday_settings?.days_off"
													class="day-off-item">
													{{ moment(day.date).format("DD/MM/YYYY") }}
												</v-chip>
											</div>
											<div class="day-off-container empty" v-else>
												<span>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</span>
											</div>
										</div>
										<div class="holiday-config-item-action">
											<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')"
												v-on:click="() => {
													showUpdateHolidayDaysOffModal = true;
												}">
												<Icon name="iconamoon:edit-light"></Icon>
											</button>
										</div>
									</div>

									<div class="holiday-config-item">
										<div class="holiday-config-value">
											<label class="label">
												{{ $t(`ShopConfigComponent.ngay_nghi_hang_tuan`) }}
											</label>
											<div class="day-off-container"
												v-if="shopConfig?.general?.holiday_settings?.days_of_week_off?.length">
												<v-chip variant="tonal"
													v-for="day in shopConfig?.general?.holiday_settings?.days_of_week_off"
													class="day-off-item in-week">
													{{ $t(`DayInWeek.${day.toLocaleLowerCase()}`) }}
												</v-chip>
											</div>
											<div class="day-off-container empty" v-else>
												<span>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</span>
											</div>
										</div>
										<div class="holiday-config-item-action">
											<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')"
												v-on:click="() => {
													showUpdateDayOfWeekOffModal = true;
												}">
												<Icon name="iconamoon:edit-light"></Icon>
											</button>
										</div>
									</div>

									<div class="holiday-config-item">
										<div class="holiday-config-value">
											<label class="label">
												{{ $t(`ShopConfigComponent.ngay_nghi_hang_thang_am_lich`) }}
											</label>
											<div class="day-off-container"
												v-if="shopConfig?.general?.holiday_settings?.days_of_month_lunar?.length">
												<v-chip variant="tonal"
													v-for="day in shopConfig?.general?.holiday_settings?.days_of_month_lunar"
													class="day-off-item in-month">
													{{ formatDayInMonth(parseInt(day), true) }}
												</v-chip>
											</div>
											<div class="day-off-container empty" v-else>
												<span>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</span>
											</div>
										</div>
										<div class="holiday-config-item-action">
											<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')"
												v-on:click="() => {
													showUpdateDayOfMonthLunarOffModal = true;
												}">
												<Icon name="iconamoon:edit-light"></Icon>
											</button>
										</div>
									</div>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.loi_chao`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value ">
									<div class="config-value message-option"
										v-if="shopConfig?.general?.message?.greeting">
										<span v-for="(key, value) in shopConfig?.general?.message?.greeting">
											{{ $t(`LocalizedLanguageName.${value}`) }}:
											<span v-if="shopConfig?.general?.message?.greeting[value]?.length"> {{
												shopConfig?.general?.message?.greeting[value] }}</span>
											<em v-else>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</em>
										</span>
									</div>
									<div class="config-value none-config" v-else>
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateMessageGreetingModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.thong_bao_cho_khach_hang`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value ">
									<div class="config-value message-option"
										v-if="shopConfig?.general?.message?.notification">
										<span v-for="(key, value) in shopConfig?.general?.message?.notification">
											{{ $t(`LocalizedLanguageName.${value}`) }}:
											<span v-if="shopConfig?.general.message?.notification[value]?.length"> {{
												shopConfig?.general.message?.notification[value] }}</span>
											<em v-else>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</em>
										</span>
									</div>
									<div class="config-value none-config" v-else>
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateMessageNotificationModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>

							<div class="config-item-child" v-if="false">
								<label class="label">{{
									$t(`ShopConfigComponent.thoi_gian_hoan_thanh_don_toi_thieu`)
								}}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value">
									<div class="config-value" v-if="shopConfig?.general?.pending_time?.value">
										{{ shopConfig.general.pending_time.value }} {{
											$t(`TimeUnit.${shopConfig?.general?.pending_time?.unit}`) }}
									</div>
									<div class="config-value none-config" v-else>
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdatePendingTimeModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.thoi_gian_cho_tai_xe_nhan_don`)
								}}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value">
									<div class="config-value message-option"
										v-if="shopConfig?.general?.delivery_expire_pending_time?.value">
										{{ shopConfig.general?.delivery_expire_pending_time?.value }} {{
											$t(`TimeUnit.${shopConfig.general.delivery_expire_pending_time.unit}`) }}
									</div>
									<div class="config-value none-config" v-else>
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateDeliveryExpireTimeModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>							<div class="config-item-child">
								<label class="label">{{
									$t(`ShopConfigComponent.phan_tram_hoa_hong`)
								}}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value">
									<div class="config-value">
										<label class="switch-config">
											<v-switch :model-value="shopConfig.general?.commission_percent?.enabled"
												v-on:update:model-value="(e: any) => {
													shopConfig.general.commission_percent.enabled = e;
													if (!e) {
														shopConfig.general.commission_percent.value = null;
													}
												}" flat color="var(--primary-color-1)" hide-details class="my-switches" name="commission_percent_enabled">
											</v-switch>
											{{ $t(shopConfig.general?.commission_percent?.enabled ?
												'ShopConfigComponent.ap_dung'
												: 'ShopConfigComponent.khong_ap_dung') }}
										</label>
									</div>
									<div class="config-value message-option" v-if="shopConfig.general?.commission_percent?.enabled && shopConfig.general?.commission_percent?.value !== null">
										{{ shopConfig.general.commission_percent.value }}%
									</div>
									<div class="config-value none-config" v-else-if="shopConfig.general?.commission_percent?.enabled">
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateCommissionPercentModal = true
									}" v-if="shopConfig.general?.commission_percent?.enabled">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- order config -->
				<div class='v-stack config-item-container'>

					<div class=config-item>
						<label class="label">
							{{ $t(`ShopConfigComponent.don_hang`) }}
						</label>

						<div class="config-item-children">
							<div class="config-item-child" v-if="false">
								<label class="label">{{
									$t(`ShopConfigComponent.nhan_don_online`)
								}}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value">
									<label class="switch-config">
										<v-switch :model-value="shopConfig.order?.order_online?.value"
											v-on:update:model-value="(e: any) => {
												shopConfig.order.order_online.value = e;
											}" flat color="var(--primary-color-1)" hide-details class="my-switches">

										</v-switch>
										{{ $t(shopConfig.order?.order_online.value ?
											'ShopConfigComponent.cho_phep_nhan_don_online'
											: 'ShopConfigComponent.khong_nhan_don_online') }}
									</label>
								</div>
							</div>

							<div class=config-item-child>
								<label class="label delivery-fee-label">
									{{ $t(`ShopConfigComponent.thong_bao_khong_tim_duoc_dich_vu_van_chuyen`) }}
								</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value">
									<div class="config-value message-option"
										v-if="shopConfig?.order?.delivery_default_message">
										<span v-for="(key, value) in shopConfig?.order?.delivery_default_message">
											{{ $t(`LocalizedLanguageName.${value}`) }}:
											<span v-if="shopConfig?.order?.delivery_default_message?.[value]?.length">
												{{
													shopConfig?.order?.delivery_default_message?.[value] }}</span>
											<em v-else>{{ $t('ShopConfigComponent.chua_cap_nhat') }}</em>
										</span>
									</div>
									<div class="config-value none-config" v-else>
										{{ $t('ShopConfigComponent.chua_cap_nhat') }}
									</div>
									<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')" v-on:click="() => {
										showUpdateDeliveryDefaultMessageModal = true
									}">
										<Icon name="iconamoon:edit-light"></Icon>
									</button>
								</div>
							</div>

							<div class="config-item-child">
								<label class="label delivery-fee-label">{{
									$t(`ShopConfigComponent.phi_giao_hang`) }}</label>
								<v-divider vertical></v-divider>
								<div class="config-item-child-value delivery-fee-config"
									v-if="shopConfig.order?.delivery?.length">
									<div class="delivery-fee-item"
										v-for="(deliveryFee, index) in shopConfig.order?.delivery" :key="index">
										<div class="delivery-fee-item-content">
											<div class="delivery-fee-item-name">
												{{
													deliveryFee.name?.[locale]?.length ? deliveryFee.name?.[locale]
														: deliveryFee.name?.vi?.length ? deliveryFee.name.vi
															: checkFirstValueNotEmpty(deliveryFee.name)
												}}


											</div>

											<div class="delivery-fee-item-description" v-if="deliveryFee.description">
												{{
													deliveryFee.description?.[locale]?.length ?
														deliveryFee.description?.[locale]
														: deliveryFee.description?.vi?.length ? deliveryFee.description.vi
															: checkFirstValueNotEmpty(deliveryFee.description)
												}}
											</div>

											<em class="delivery-fee-item-value">
												<Icon name="ph:arrow-fat-line-down-duotone"></Icon>
												<span> {{ deliveryFee.type == 'money' ?
													formatCurrency(deliveryFee.value) : `${deliveryFee.value}%`
												}}</span>
											</em>

											<em class="delivery-fee-item-max-value" v-if="deliveryFee.max_value">
												<span> {{ $t('ShopConfigComponent.giam_toi_da') }} {{
													formatCurrency(deliveryFee.max_value) }}</span>
											</em>

											<div class="delivery-fee-item-condition-count">
												{{ deliveryFee.conditions?.length
													? $t('ShopConfigComponent.dieu_kien_ap_dung', {
														count:
															deliveryFee.conditions?.length
													})
													: $t('ShopConfigComponent.khong_can_dieu_kien') }}
											</div>
											<div class="actions">
												<label class="delivery-fee-item-enable">
													<v-switch :model-value="deliveryFee.enabled"
														v-on:update:model-value="(e: any) => {
															deliveryFee.enabled = e;
														}" flat color="var(--primary-color-1)" hide-details class="my-switches">

													</v-switch>
													{{ $t(deliveryFee.enabled ?
														'ShopConfigComponent.ap_dung'
														: 'ShopConfigComponent.khong_ap_dung') }}
												</label>
											</div>
										</div>
										<div class="delivery-fee-item-action">
											<button class="edit-config" :title="$t('ShopConfigComponent.cap_nhat')"
												v-on:click="() => {
													selectedDeliveryPromotion = JSON.parse(JSON.stringify(deliveryFee));
													indexSelectedDeliveryPromotion = index;
													showUpdateDeliveryPromotionModal = true
												}">
												<Icon name="iconamoon:edit-light"></Icon>
											</button>
											<button class="delete-config" :title="$t('ShopConfigComponent.xoa')"
												v-on:click="() => {
													selectedDeliveryPromotion = JSON.parse(JSON.stringify(deliveryFee));
													indexSelectedDeliveryPromotion = index;
													showDeleteDeliveryPromotionModal = true
												}">
												<Icon name="iconamoon:trash-light"></Icon>
											</button>
										</div>

									</div>
								</div>
								<div class="config-item-child-value delivery-fee-config empty" v-else>
									<div class="delivery-fee-item">
										{{ $t('ShopConfigComponent.chua_tao_khuyen_mai') }}
									</div>
								</div>
								<div class="config-item-footer">
									<button class="add-config" v-on:click="() => {
										showAddDeliveryPromotionModal = true
									}">
										<Icon name="iconamoon:sign-plus-circle-light"></Icon>
										{{ $t('ShopConfigComponent.them_khuyen_mai') }}
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="shop-config-footer" v-if="isUpdated || !deepCompareJsons(shopConfig, shopConfigBackup)">
				<v-btn class="undo-button" v-on:click="undoConfig()" :disabled="isSaving">
					{{ $t('ShopConfigComponent.hoan_tac') }}
				</v-btn>
				<v-btn class="save-button" v-on:click="saveConfig()" :disabled="isSaving" :loading="isSaving">
					{{ $t('ShopConfigComponent.luu') }}
				</v-btn>

			</div>
		</div>

		<NoneMyShopComponent v-else-if="!isRefreshing" :show_header="true"
			:title="$t('AppRouteTitle.ShopConfigComponent')">
		</NoneMyShopComponent>

		<UpdateDeliveryPromotionComponent :mode="props.mode" :shop_id="shop_id" :shop_data="shopData"
			:delivery_propmotion_data="selectedDeliveryPromotion" v-if="showUpdateDeliveryPromotionModal" v-on:close="() => {
				showUpdateDeliveryPromotionModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.order.delivery[indexSelectedDeliveryPromotion != -1 ? indexSelectedDeliveryPromotion : 0] = JSON.parse(JSON.stringify(e));
				indexSelectedDeliveryPromotion = -1;
				showUpdateDeliveryPromotionModal = false;
			}">
		</UpdateDeliveryPromotionComponent>

		<AddDeliveryPromotionComponent :mode="props.mode" :shop_id="shop_id" :shop_data="shopData"
			v-if="showAddDeliveryPromotionModal" v-on:close="() => {
				showAddDeliveryPromotionModal = false
			}" v-on:submit="(e: any) => {
				if (shopConfig.order?.delivery == null) {
					shopConfig.order.delivery = [e];
				}
				else {
					shopConfig.order?.delivery?.splice(0, 0, e);
				}

				showAddDeliveryPromotionModal = false;
			}">

		</AddDeliveryPromotionComponent>

		<DeleteDeliveryPromotionComponent :mode="props.mode" :shop_id="shop_id" :shop_data="shopData"
			:delivery_propmotion_data="JSON.parse(JSON.stringify(selectedDeliveryPromotion))"
			v-if="showDeleteDeliveryPromotionModal" v-on:close="() => {
				showDeleteDeliveryPromotionModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.order?.delivery?.splice(indexSelectedDeliveryPromotion, 1);

			 showDeleteDeliveryPromotionModal = false;
			}"></DeleteDeliveryPromotionComponent>

		<SaveDataBeforeLeaveComponent v-if="showModalSaveDataBeforeLeave" v-on:close="() => {
			showModalSaveDataBeforeLeave = false;
			handleNextStep(false);
		}" v-on:leave="() => {
			handleNextStep(true);
			showModalSaveDataBeforeLeave = false
		}" v-on:save_and_leave="() => {
			handleSaveConfig();
			handleNextStep(true);
			showModalSaveDataBeforeLeave = false;

		}"></SaveDataBeforeLeaveComponent>

		<UpdatePendingTimeComponent v-if="showUpdatePendingTimeModal"
			:title="$t('ShopConfigComponent.thoi_gian_hoan_thanh_don_toi_thieu')"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.pending_time))" v-on:close="() => {
				showUpdatePendingTimeModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.general.pending_time = {
					value: e.value,
					unit: e.unit,
					enabled: shopConfig.general?.pending_time?.enabled ?? true,
				};

				showUpdatePendingTimeModal = false;
			}">

		</UpdatePendingTimeComponent>

		<UpdatePendingTimeComponent v-if="showUpdateDeliveryExpireTimeModal"
			:title="$t('ShopConfigComponent.thoi_gian_cho_tai_xe_nhan_don')"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.delivery_expire_pending_time))" v-on:close="() => {
				showUpdateDeliveryExpireTimeModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.general.delivery_expire_pending_time = {
					value: e.value,
					unit: e.unit,
					enabled: shopConfig?.general?.delivery_expire_pending_time?.enabled ?? true,
				};

				showUpdateDeliveryExpireTimeModal = false;
			}">		</UpdatePendingTimeComponent>

		<UpdateCommissionPercentComponent v-if="showUpdateCommissionPercentModal"
			:title="$t('ShopConfigComponent.phan_tram_hoa_hong')"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.commission_percent))" v-on:close="() => {
				showUpdateCommissionPercentModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.general.commission_percent = {
					value: e.value,
					enabled: shopConfig.general?.commission_percent?.enabled ?? true,
				};

				showUpdateCommissionPercentModal = false;
			}">
		</UpdateCommissionPercentComponent>

		<UpdateMessageComponent v-if="showUpdateMessageGreetingModal" :max_content="255" :message_type="'greeting'"
			:title="$t('ShopConfigComponent.loi_chao')"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.message?.greeting))" v-on:close="() => {
				showUpdateMessageGreetingModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.general.message.greeting = e;
				showUpdateMessageGreetingModal = false;
			}">
		</UpdateMessageComponent>

		<UpdateMessageComponent v-if="showUpdateMessageNotificationModal" :max_content="500"
			:title="$t('ShopConfigComponent.thong_bao_cho_khach_hang')" :message_type="'notification'"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.message?.notification))" v-on:close="() => {
				showUpdateMessageNotificationModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.general.message.notification = e;
				showUpdateMessageNotificationModal = false;
			}">
		</UpdateMessageComponent>

		<UpdateMessageComponent v-if="showUpdateDeliveryDefaultMessageModal" :max_content="500"
			:message_type="'default_delivery_message'"
			:title="$t('ShopConfigComponent.thong_bao_khong_tim_duoc_dich_vu_van_chuyen')"
			:init_value="JSON.parse(JSON.stringify(shopConfig.order?.delivery_default_message))" v-on:close="() => {
				showUpdateDeliveryDefaultMessageModal = false
			}" v-on:submit="(e: any) => {
				shopConfig.order.delivery_default_message = e;
				showUpdateDeliveryDefaultMessageModal = false;
			}">
		</UpdateMessageComponent>

		<UpdateHolidaySettingComponent :mode="day_off_type.days_off" v-if="showUpdateHolidayDaysOffModal"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.holiday_settings.days_off))"
			:title="$t('ShopConfigComponent.ngay_nghi_dip_dac_biet')" v-on:close="() => {
				showUpdateHolidayDaysOffModal = false
			}" v-on:submit_days_off="(data) => {
				console.log(data);
				let dataTemp = data.map((itemDate: any) => {
					return {
						date: moment(itemDate.date).format(appConst.formatDate.toSave),
						reason: { ...itemDate.reason }
					}
				});
				console.log(dataTemp);
				shopConfig.general.holiday_settings.days_off = dataTemp;
				showUpdateHolidayDaysOffModal = false;
			}">
		</UpdateHolidaySettingComponent>

		<UpdateViewTemplateComponent v-if="showUpdateViewTemplateModal" :init_value="shopConfig?.general?.view_template"
			:shop_data="JSON.parse(JSON.stringify(shopData))" v-on:close="() => {
				showUpdateViewTemplateModal = false;
				shopConfig.general.view_template = {
					template_id: shopConfig.general.view_template?.template_id ?? 'mac_dinh',
					colors: shopConfig.general.view_template?.colors ?? '',
					font: shopConfig.general.view_template?.font ?? null,
					banner: shopConfig.general.view_template?.banner ?? null,
					use_shop_banner: shopConfig.general.view_template?.use_shop_banner ?? null,
					background: shopConfig.general.view_template?.background ?? null,
					use_default_backgrond: shopConfig.general.view_template?.use_default_backgrond ?? null,
					font_resize: shopConfig.general.view_template?.font_resize ?? 0,
				}
			}" v-on:submit="(result) => {
				shopConfig.general.view_template = {
					template_id: result.template_id,
					colors: result.colors,
					font: result.font,
					banner: result.banner ?? shopConfig.general.view_template?.banner,
					use_shop_banner: result.use_shop_banner,
					background: result.background ?? shopConfig.general.view_template?.background,
					use_default_backgrond: result.use_default_backgrond,
					font_resize: result.font_resize ?? 0,
				}
				showUpdateViewTemplateModal = false;
				console.log(shopConfig)
			}">
		</UpdateViewTemplateComponent>

		<!-- <UpdateHolidaySettingComponent 
			:mode="day_off_type.day_off_week" 
			v-if="showUpdateDayOfWeekOffModal"
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.holiday_settings.days_of_week_off))"
			:title="$t('ShopConfigComponent.ngay_nghi_hang_tuan')"
			v-on:close="()=>{
				showUpdateDayOfWeekOffModal = false
			}"
		>
		</UpdateHolidaySettingComponent>

		<UpdateHolidaySettingComponent 
			:mode="day_off_type.day_off_month_lunar" 
			:init_value="JSON.parse(JSON.stringify(shopConfig.general.holiday_settings.days_of_month_lunar))"
			v-if="showUpdateDayOfMonthLunarOffModal"
			:title="$t('ShopConfigComponent.ngay_nghi_hang_thang_am_lich')"
			v-on:close="()=>{
				showUpdateDayOfMonthLunarOffModal = false
			}"
		>
		</UpdateHolidaySettingComponent> -->
		<CustomSelectComponent v-if="showUpdateDayOfWeekOffModal" :_key="'select_category_filter'" :list_item="dayInWeek.map((itemDay) => {
			return {
				value: itemDay,
				label: $t(`DayInWeek.${itemDay}`)
			}
		})" :field_title="'label'" :field_value="'value'" :multiple="true"
			:title="`${t('ShopConfigComponent.ngay_nghi_hang_tuan')}`" :class="'my-custom-select'" :searchable="false"
			:model_value="shopConfig?.general?.holiday_settings?.days_of_week_off" v-on:close="() => {
				showUpdateDayOfWeekOffModal = false;
			}" v-on:model:update="(e) => {
				shopConfig.general.holiday_settings.days_of_week_off = e;
			}">
			<template v-slot:title_icon_left>
				<Icon name="solar:calendar-broken"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<span class="text-center">{{ item.label }}</span>
			</template>
		</CustomSelectComponent>

		<CustomSelectComponent v-if="showUpdateDayOfMonthLunarOffModal" :_key="'select_category_filter'" :list_item="Array.from({ length: 30 }, (_, i) => i + 1).map((itemDay) => {
			return {
				value: itemDay,
				label: formatDayInMonth(itemDay, true)
			}
		})" :field_title="'label'" :field_value="'value'" :multiple="true"
			:title="`${t('ShopConfigComponent.ngay_nghi_hang_thang_am_lich')}`" :class="'my-custom-select'"
			:searchable="false" :model_value="shopConfig?.general?.holiday_settings?.days_of_month_lunar" v-on:close="() => {
				showUpdateDayOfMonthLunarOffModal = false;
			}" v-on:model:update="(e) => {
				shopConfig.general.holiday_settings.days_of_month_lunar = e;
			}">
			<template v-slot:title_icon_left>
				<Icon name="solar:calendar-broken"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<span class="text-center">{{ item.label }}</span>
			</template>
		</CustomSelectComponent>
	</div>

</template>

<script lang="ts" setup>
import EditProductComponent from '../editProduct/EditProductComponent.vue'
import { VueFinalModal } from 'vue-final-modal';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { appConst, appDataStartup, domainImage, formatCurrency, formatNumber, deepCompareJsons, checkFirstValueNotEmpty, formatDayInMonth, dayInWeek } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import icon_for_product from '~/assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg"
import none_shop from "~/assets/image/none-shop.jpg";
import ISO6391 from 'iso-639-1';
import { HttpStatusCode } from 'axios';
import { AgentService } from '~/services/agentService/agentService';
import { toast } from 'vue3-toastify';
import { enabled } from 'virtual:nuxt-pwa-configuration';
import { day_off_type, ShopConfigJSON, type shop_config_json } from './ShopConfigDTO';
import localeCode from 'iso-639-1';
import { containsProp } from '@vueuse/core';
import moment from 'moment';
import { resolveFiles } from 'nuxt/kit';

const { t, locale } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.ShopConfigComponent')
});
const router = useRouter();
const route = useRoute();
const props = defineProps({
	mode: null
})

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var searchProductTimeout: any;
var loadMoreTimeOut: any;

var shop_id = ref<any>(route.params.id ?? null);
var shopData = ref(null as any);

var isRefreshing = ref(true);
var isUpdated = ref(false);
var isSaving = ref(false);

var shopConfig = ref<ShopConfigJSON>(new ShopConfigJSON());
var shopConfigBackup = ref<ShopConfigJSON>(new ShopConfigJSON());

var showAddDeliveryPromotionModal = ref(false);

var selectedDeliveryPromotion = ref<any>(null);
var indexSelectedDeliveryPromotion = ref<any>(-1)
var showUpdateDeliveryPromotionModal = ref(false);
var showDeleteDeliveryPromotionModal = ref(false);
var showModalSaveDataBeforeLeave = ref(false);
var showUpdatePendingTimeModal = ref(false);
var showUpdateDeliveryExpireTimeModal = ref(false);
var showUpdateCommissionPercentModal = ref(false);
var showUpdateMessageGreetingModal = ref(false);
var showUpdateMessageNotificationModal = ref(false);
var showUpdateDeliveryDefaultMessageModal = ref(false);
var showUpdateHolidayDaysOffModal = ref(false);
var showUpdateDayOfWeekOffModal = ref(false);
var showUpdateDayOfMonthLunarOffModal = ref(false);
var showUpdateViewTemplateModal = ref(false);
var nextStep = ref<any>();
onMounted(() => {
	isRefreshing.value = true;
	getShopDetail().then((res: any) => {
		if (shopData.value) {
			shopConfig.value = initShopConfig(JSON.parse(JSON.stringify(shopData.value?.settings)));
			shopConfigBackup.value = initShopConfig(JSON.parse(JSON.stringify(shopConfig.value)));
			console.log(shopConfig.value);
		}

		isRefreshing.value = false;
	});

	window.addEventListener('beforeunload', handleBeforeUnload)
});
const handleBeforeUnload = (event: any) => {
	checkDataChangeBeforeLeave().then((res) => {
		if (res) {
			event.preventDefault()
			event.returnValue = '' // Show browser's generic unload confirmation
			return ''
		}
	})
}
onBeforeRouteLeave((to, from, next) => {
	checkDataChangeBeforeLeave().then(res => {
		if (res) {
			showModalSaveDataBeforeLeave.value = true;
			nextStep.value = next;
			// next(false);
		}
		else {
			next();
		}
	})
})

onUnmounted(() => {
	window.removeEventListener('beforeunload', handleBeforeUnload)
})

function handleNextStep(confirmed: any) {
	if (nextStep.value) {
		if (confirmed) {
			nextStep.value();
		}
		else {
			nextStep.value(false);
		}
	}
}

async function checkDataChangeBeforeLeave(event?: any) {
	return new Promise(async (resolve) => {
		let dataChange = await !deepCompareJsons(shopConfig.value, shopConfigBackup.value);
		console.log('check before leave', dataChange);
		resolve(dataChange);
	})

}
function initShopConfig(settings: any): shop_config_json {
	let settingObj = new ShopConfigJSON(settings ?? shopConfig.value);
	return settingObj;
}
function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

function getMyShop() {
	return new Promise((resolve) => {
		shopService.myShop().then(res => {
			if (res.status && res.status == HttpStatusCode.Ok) {
				if (res.body.data == null) {
					toast.error(t('ShopConfigComponent.ban_chua_dang_ky_cua_hang'));
					shopData.value = null;
					resolve(shopData.value);
				}
				else {
					shopData.value = res.body.data;
					resolve(shopData.value);
				}
			}
			else {
				shopData.value = null;
				toast.error(t('ShopConfigComponent.co_loi_xay_ra'));
				resolve(shopData.value);
			}
		}).catch(() => {
			toast.error(t('ShopConfigComponent.ban_chua_dang_ky_cua_hang'));
		})
	})
}

function getShopDetailAgent() {
	return new Promise((resolve) => {
		agentService.agentShopDetail(shop_id.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				shopData.value = res.body.data;
				shop_id.value = shopData.value?.id;
				resolve(res.body.data)
			}
			else {
				shopData.value = null;
				toast.error(t('ShopConfigComponent.khong_co_quyen_quan_ly_shop'));
				resolve(null)
			}
		}).catch(e => {
			shopData.value = null;
			toast.error(t('ShopConfigComponent.khong_co_quyen_quan_ly_shop'));
			resolve(null)
		})
	})

}

async function getShopDetail() {
	return new Promise((resolve) => {
		if (props.mode != 'agent') {
			getMyShop().then(res => {
				resolve(res);
			});
		}
		else {
			getShopDetailAgent().then(res => {
				resolve(res);
			});
		}
	})
}

function saveConfig() {
	isSaving.value = true;
	handleSaveConfig().then(res => {
		isSaving.value = false;
		getShopDetail().then((res2: any) => {
			shopConfig.value = initShopConfig(JSON.parse(JSON.stringify(shopData.value.settings)));
			shopConfigBackup.value = initShopConfig(JSON.parse(JSON.stringify(shopConfig.value)));
			isRefreshing.value = false;
		});
	})
}
function handleSaveConfig() {
	return new Promise((resolve) => {
		isSaving.value = true;
		let payload = {
			shop_id: shopData.value?.id ?? shop_id.value,
			setting: JSON.parse(JSON.stringify(shopConfig.value))
		}
		shopService.updateShopSettings(payload).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				toast.info(t('ShopConfigComponent.cap_nhat_thanh_cong'));
				isSaving.value = false;
				resolve(true);
			}
			else {
				toast.error(res.body?.message ?? t('ShopConfigComponent.co_loi_xay_ra'));
				isSaving.value = false;
				resolve(false);
			}
		}).catch(() => {
			toast.error(t('ShopConfigComponent.co_loi_xay_ra'));
			isSaving.value = false;
			resolve(false);
		})
	})
}
function undoConfig() {
	shopConfig.value = JSON.parse(JSON.stringify(shopConfigBackup.value));
}


</script>

<style lang="scss">
@import url("./ShopConfigStyles.scss");
</style>