.shop-info-v2-container {
  height: 100%;
  flex: 1;
  background: #f4f4f4;
  margin: 0;
  overflow: auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-width: var(--max-width-content-view-1024) !important;

  & .loading-skeleton {
    gap: 10px;
    padding: 10px;
    background: transparent;

    .h-stack,
    .v-stack {
      padding: 0 10px;
      gap: 5px;
      display: flex;
      align-items: flex-start;
      background: transparent;
    }

    & > .banner-skeleton {
      width: 100%;
      aspect-ratio: 2.2;
    }

    & .avt-skeleton {
      width: 75px;
      min-width: 75px;
      height: 75px;
      border-radius: 5px;
    }

    & .info-placeholder {
      display: flex;
      gap: 5px;
      flex: 1;
      width: 100%;
      padding: 0;
      background: transparent;

      & .info-skeleton {
        width: 100%;
        height: 100%;
        padding: 0;
        margin: -10px 0 0;
        background: transparent;
      }

      & .actions-skeleton {
        display: flex;
        flex-direction: row;
        flex: 1;
        width: 100%;
        background: transparent;
        border-radius: 2em;
        margin-top: -10px;
        z-index: 10;

        & > .action-skeleton {
          height: 25px;
          width: 100px;
          border-radius: 2em;
          margin-left: 15px;
          justify-content: flex-start;
          background: transparent;
        }
      }
    }
  }

  .shop-info-container {
    width: 100%;
    height: 100%;
    flex: 1 1;
    /* min-height: inherit; */
    /* border-radius: 10px; */
    max-width: var(--max-width-view);
    background: white;
    margin: auto;
    display: flex;
    flex-direction: column;
    font-size: 20px;
    position: relative;

    & > .shop-info-content-container {
      font-size: 20px;
      position: relative;
      background: #f4f4f4;
      flex: 1;

      & > .shop-detail-container {
        display: flex;
        font-size: 15px;
        padding: 13px 10px 12px 10px;
        gap: 15px;

        & > .shop-logo {
          position: relative;
          border-radius: 5px;
          border: 0;
          align-self: flex-start;
          box-shadow: none;
          box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
        }

        & > .shop-detail-content {
          display: flex;
          flex-direction: column;
          font-size: 17px;
          line-height: 1.125;
          flex: 1;
          justify-content: center;

          & > .name-share {
            display: flex;
            align-items: center;
            overflow: hidden;
            gap: 5px;
            color: var(--primary-color-1);
            font-weight: bold;

            & > .name {
              display: flex;
              gap: 2px;
              align-items: center;
              & > span {
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              & > svg {
                width: 20px;
                min-width: 20px;
                align-self: flex-start;
              }
            }

            & > .share {
              width: 24px;
              height: 24px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-left: auto;
              font-size: 24px;
            }
          }

          & > .follows {
            color: #8c8c8c;
            font-size: 14px;
            font-weight: 300;

            & > em {
              font-style: normal;
              font-weight: bold;
            }
          }

          & > .open-time {
            font-weight: 600;
            font-size: 15px;

            & > em {
              color: var(--primary-color-1);
            }
          }

        }
      }
      & > .shop-detail-open-time {
        padding: 10px 0;
        background: white;
        display: flex;
        flex-direction: column;
        & > .label {
          padding: 0 10px;
          font-size: 17px;
          color: var(--primary-color-1);
          font-weight: 700;
          display: flex;
          justify-content: space-between;

          & > svg {
            width: 21px;
            height: 21px;
            min-width: 21px;
            color: #d9d9d9;
          }
        }

        & > .line {
          margin: 10px 0;
          height: 1px;
          background-color: #ececec;
        }

        & > .list-open-time {
          display: flex;
          flex-direction: column;
          gap: 5px;
          font-size: 17px;
          padding: 0 10px;

          & > .item-open-time {
            display: flex;
            align-items: center;
            // justify-content: space-between;

            & > .days {
              font-size: 15px;
              color: #545454;
              font-weight: 400;
              width: 40%;
              line-height: normal;
            }

            & > .times {
              font-size: 17px;
              color: var(--primary-color-2);
              font-weight: 700;
              margin-left: auto;
              line-height: normal;
            }
          }
        }
      }
      & .line {
        margin: 10px 0;
        height: 1px;
        background-color: #ececec;
      }
      & .label {
        padding: 0 10px;
        font-size: 17px;
        color: var(--primary-color-1);
        font-weight: 700;
        display: flex;
        justify-content: space-between;

        & > svg {
          width: 21px;
          height: 21px;
          min-width: 21px;
          color: #d9d9d9;
        }
      }
      & > .shop-detail-summary {
        padding: 10px 0;
        background: white;
        display: flex;
        flex-direction: column;
        margin-top: 5px;

        & > .detail-item {
          display: flex;
          gap: 5px;
          padding: 0 10px;
          align-items: flex-start;

          & > .detail-label {
            flex: 30%;
            font-size: 14px;
            color: #545454;
            font-weight: 400;
            width: 30%;
          }

          & > .detail-value {
            font-size: 17px;
            flex: 70%;
            color: var(--primary-color-2);
            font-weight: 700;
            margin-left: auto;

            & > .secondary-detail{
              color: #545454;
              font-size: 15px;
              font-weight: 400;
            }

            &.weight-400{
              font-weight: 400
            }

            &.opened{
              color: var(--primary-color-1);
            }
          }
          
        }
      }

      & > .shop-detail-description{
        padding: 10px 0;
        background: white;
        display: flex;
        flex-direction: column;
        margin-top: 5px;
        
        & > .description{
          padding: 0 10px 10px;
          font-size: 15px;
          line-height: normal;
          font-weight: 400;
          color: #545454;
          
          & > .description-content{
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          & > .description-content.expanding{
            display: block;
          }
        }
        & > .expand-button{
          font-size: 14px;
          color: #A8A7A7;
          padding: 10px 0 0 ;
          border-top: thin solid #ECECEC;
        }

        & > .none-description{
          padding: 0 10px 10px;
          font-size: 15px;
          font-style: italic;
          line-height: normal;
          font-weight: 400;
          color: #545454;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      & > .shop-ratings{
        padding: 10px 0;
        background: white;
        display: flex;
        flex-direction: column;
        margin-top: 5px;

        & > .rating-overview {
          display: flex;
          align-items: baseline;
          padding: 0 15px;
          font-size: 15px;
  
          & > .comment {
            line-height: 1;
            margin-left: 5px;
            color: #a8a7a7;
            font-size: 13px;
          }
  
          & .rating {
            display: flex;
            font-weight: 400;
            color: var(--primary-color-2);
            font-size: 15px;
            gap: 5px;
          }
        }
  
        & > .comment-container {
          display: flex;
          flex-direction: column;
          font-size: 15px;
  
          & > .comment-item-container:first-child {
            border-top: thin solid #ececec;
          }
          & > .comment-item-container {
            display: flex;
            flex-direction: column;
            padding: 10px 15px;
            border-bottom: thin solid #ececec;
            // gap: 5px;
            & > .rate {
              display: flex;
              gap: 10px;
              align-items: baseline;
              font-weight: 600;
              color: black;
              font-size: 20px;
            }
  
            & > .comment-detail {
              color: #545454;
              font-weight: 400;
              line-height: normal;
              white-space: break-spaces;
            }
  
            & > .comment-images {
              display: flex;
              flex-wrap: wrap;
              gap: 5px;
              margin: 5px 0;
              
  
              & > .comment-image-item {
                width: calc(20% - 5px);
                aspect-ratio: 1;
                min-width: 100px;
                min-height: 100px;
                max-width: 200px;
                border-radius: 2px;
                border: thin solid #e7e9ec;
                color: #e7e9ec;
                font-size: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                line-height: 1;
                position: relative;
                & > img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  border-radius: inherit;
                }
              }
            }
  
            & > .comment-time {
              display: flex;
              justify-content: space-between;
              font-size: 13px;
              color: #8c8c8c;
              font-weight: 400;
              margin: 7px 0;
  
              & > button.open-shop-reply {
                display: flex;
                align-items: flex-start;
                justify-content: flex-end;
                gap: 2px;
                & > svg {
                  font-size: 20px;
                }
              }
            }
  
            & > .shop-reply {
              background: #f5f5f5;
              padding: 5px 10px;
              font-size: 15px;
              display: flex;
              flex-direction: column;
              font-weight: 400;
  
              & > .label {
                font-size: 15px;
                color: #545454;
                font-weight: 400;
              }
  
              & > .content {
                color: #8c8c8c;
              }
            }
  
            & > .user-info {
              display: flex;
              gap: 10px;
              align-items: center;
              color: #7e7d83;
  
              & img {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 50%;
              }
              & > .name-rate {
                display: flex;
                flex-direction: column;
  
                & > .name {
                  color: #545454;
                  font-size: 15px;
                  font-weight: 700;
                }
              }
            }
          }
  
          &.my-comment{
            background: color-mix(in srgb, var(--primary-color-1) 5%, transparent);
            padding: 10px;
            border-radius: 10px;
            margin: 10px;
  
            & > .comment-header{
              display: flex;
              align-items: center;
              justify-content: flex-start;
  
              & > label{
                color: var(--primary-color-2);
                font-weight: 700;
              }
  
              & > .update-comment{
                font-weight: 600;
                font-style: italic;
                color: var(--primary-color-1);
                margin-left: auto;
              }
            }
            
  
            & > .comment-item-container{
              border-bottom: none;
  
              & .update-comment{
                font-style: italic;
                color: var(--primary-color-1);
              }
            }
          }
        }
  
        & .rate-loadmore {
          margin: 10px 0 0;
          font-style: italic;
          color: #a8a7a7;
          text-align: center;
          font-size: 13px;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
  
          & > svg {
            font-size: 20px;
          }
        }
        & .show-all-rating {
          margin: 10px 0 0;
          font-style: italic;
          color: #7e7d83;
          text-align: center;
          width: 100%;
          font-size: 15px;
        }
      }
      
    }
  }

  .title-header-container {
    // padding: 10px;
    background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 900;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.1s cubic-bezier(0.075, 0.82, 0.165, 1);

    & > .back-button,
    > a {
      width: 30px;
      height: 30px;
      color: white;
      font-size: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      align-self: center;
    }
  }

  .shop-info-content-container {
    font-size: 20px;
    position: relative;
    background: #f4f4f4;
    flex: 1;

    & > .shop-detail-container {
      display: flex;
      font-size: 15px;
      padding: 13px 10px 12px 10px;
      gap: 15px;
      background: white;
      // margin-top: 5px;

      & > .shop-logo {
        position: relative;
        border-radius: 5px;
        border: 0;
        align-self: flex-start;
        box-shadow: none;
        box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
      }

      & > .shop-detail-content {
        display: flex;
        flex-direction: column;
        font-size: 15px;
        line-height: 1.125;
        flex: 1;
        justify-content: flex-start;
        padding-top: 5px;

        & > .name-share {
          display: flex;
          align-items: center;
          overflow: hidden;
          gap: 5px;
          color: var(--primary-color-1);
          font-weight: bold;

          & > .name {
            display: flex;
            gap: 2px;
            align-items: center;
            & > span {
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            & > svg {
              width: 20px;
              min-width: 20px;
              align-self: flex-start;
            }
          }

          & > .share {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: auto;
            font-size: 24px;
          }
        }

        & > .follows {
          color: #8c8c8c;
          font-size: 11px;
          font-weight: 300;

          & > em {
            font-style: normal;
            font-weight: bold;
          }
        }

        & > .open-time {
          font-weight: 600;
          font-size: 15px;

          & > em {
            color: var(--primary-color-1);
          }
        }
      }
    }
  }
}
