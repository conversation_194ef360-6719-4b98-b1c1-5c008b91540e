<template>
	<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'" :click-to-close="false" :esc-to-close="false" content-class="v-stack login-first-modal"
		v-model="showModalLoginFirst" v-on:closed="() => {
			showModalLoginFirst = false
		}" contentTransition="vfm-slide-up">
		<div>
			<div class='v-stack'>
				<img loading="lazy" :src="access_denied" :placeholde="access_denied" />
				<span class='login-first-message'>
					{{ $t('LoginFirstComponent.ban_can_dang_nhap_truoc') }}
				</span>
			</div>
			<div class='h-stack buttons'>
				<button class='cancel-button' v-on:click="() => {
					close();
					showModalLoginFirst = false;
				}">
					{{ $t('LoginFirstComponent.thoat') }}
				</button>
				<button class='redirect-button' v-on:click="() => {
					goToLoginPage()
				}">
					{{ $t('LoginFirstComponent.dang_nhap') }}
				</button>
			</div>
		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>
import {appRoute } from '~/assets/appRoute';
import access_denied from "~/assets/image/access_denied.png";
import { VueFinalModal } from 'vue-final-modal';
const emit = defineEmits([
	'close'
])
const nuxtApp = useNuxtApp();
const { t } = useI18n();
var router = useRouter();
var route = useRoute();

var showModalLoginFirst = ref(false);
onMounted(async () => {
	// loginWithGoogleOneTap.login();
	showModalLoginFirst.value = true;
})

function goToLoginPage(){
	router.push(appRoute.LoginComponent);
}

function close() {
	emit('close')
}
</script>

<style lang="scss" src="./LoginFirstStyles.scss"></style>