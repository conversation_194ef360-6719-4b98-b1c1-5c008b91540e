<template>
    <div class="public-container">

        <div class='create-order-container'>
            <client-only hidden>
                <LMap id="temp_leaflet_map" v-on:ready="(e: any) => {
                    tempLeafletMap = e;
                }"></LMap>
            </client-only>
            <SubHeaderV2Component>
                <template v-slot:header_middle>
                    <div class="header-create-order">
                        <h3>{{ $t('AppRouteTitle.MyShopCreateOrderComponent') }}</h3>
                        <h4>{{ shopData?.name }}</h4>
                    </div>
                </template>
            </SubHeaderV2Component>
            <div class='create-order-content-container'>

                <div class="receiver-info" v-on:click="() => {
                    if (userInfo) {
                        showSelectUser = true;
                        isEditingCustomerInfo = false;
                    }
                    else {
                        isEditingCustomerInfo = true;
                        showSelectUser = false;
                    }
                }">
                    <Icon name="material-symbols:person-pin-circle-rounded"></Icon>
                    <div class="user-info">
                        <span class="label-content">{{ $t('MyShopCreateOrderComponent.thong_tin_nguoi_nhan') }}</span>
                        <span class="name-phone">
                            <span :class="{ 'error': !(orderData.customer_name && orderData.customer_name.length) }">
                                {{ orderData.customer_name && orderData.customer_name.length
                                    ? orderData.customer_name
                                    : $t('MyShopCreateOrderComponent.chua_co_ten')
                                }}
                            </span>

                            {{ " | " }}

                            <span :class="{ 'error': !(orderData.customer_phone && orderData.customer_phone.length) }">
                                {{ orderData.customer_phone && orderData.customer_phone.length
                                    ? orderData.customer_phone
                                    : $t('MyShopCreateOrderComponent.chua_co_sdt')
                                }}
                            </span>
                        </span>

                        <span class="address" :class="{ 'error': !(orderData.address && orderData.address.length) }">
                            {{ orderData.address && orderData.address.length
                                ? orderData.address
                                : $t('MyShopCreateOrderComponent.chua_co_dia_chi')
                            }}
                        </span>
                    </div>
                    <button class="select-receiver">
                        <Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
                    </button>
                </div>
                <div class="receiver-info" v-on:click="() => {
                    showDateTimePickerModal = true;

                }">
                    <Icon name="material-symbols:nest-clock-farsight-analog-rounded"></Icon>
                    <div class="user-info">
                        <span class="name-phone">
                            <span class="label-content">
                                {{ $t('MyShopCreateOrderComponent.thoi_gian_nhan_hang') }}
                            </span>
                        </span>

                        <span class="address">
                            {{ orderData.delivery_time?.length
                                ? orderData.delivery_time
                                : $t('MyShopCreateOrderComponent.chua_chon')
                            }}
                        </span>
                    </div>
                    <button class="select-receiver">
                        <Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
                    </button>
                </div>
                <div class="order-items">

                    <span class="label-content">
                        <Icon name="material-symbols:format-list-bulleted"></Icon>
                        <span>
                            {{ $t('MyShopCreateOrderComponent.san_pham') }} {{ orderItems.length ?
                                `(${orderItems.length})`
                                : '(0)' }}
                        </span>

                        <button class="right-button" v-on:click="() => {
                            showModalAddProduct = true;
                            if (!shopProducts.length) {
                                searchingProduct();
                            }
                        }">
                            {{ $t('MyShopCreateOrderComponent.them_san_pham') }}
                        </button>
                    </span>
                    <div class="order-item-container" :class="{ 'odd': index % 2 == 0 }"
                        v-for=" (itemProductOrder, index) in orderItems">
                        <img loading="lazy" :src="(itemProductOrder && itemProductOrder.product?.profile_picture)
                            ? (domainImage + itemProductOrder.product?.profile_picture)
                            : icon_for_product" :placeholder="icon_for_product"
                            :alt="showTranslateProductName(itemProductOrder.product)" />
                        <div class='v-stack item-order-detail'>
                            <div class='h-stack item-order-product-name'
                                :title="showTranslateProductName(itemProductOrder.product)">
                                <span>
                                    {{ showTranslateProductName(itemProductOrder.product) }}
                                    &nbsp;|&nbsp;
                                    <span class='price'>
                                        {{ (itemProductOrder.product.price_off != null &&
                                            itemProductOrder.product.price_off
                                            < itemProductOrder.product.price) ?
                                            formatCurrency(itemProductOrder.product.price_off, shopData?.currency) :
                                            formatCurrency(itemProductOrder.product.price, shopData?.currency) }} <em
                                            class="off"
                                            v-if="(itemProductOrder.product.price_off != null && itemProductOrder.product.price_off < itemProductOrder.product.price)">
                                            {{
                                                formatCurrency(itemProductOrder.product.price, shopData?.currency) }}</em>
                                    </span>
                                </span>

                            </div>
                            <div class='v-stack price-quantity'>
                                <div class="price-input-container" v-if="false">
                                    <input type="number" v-model="itemProductOrder.price"
                                        class="input-custom price-input"
                                        max=1000000000000 v-on:input="($event: any) => {

                                        }">
                                    <span class='price'>
                                        {{ formatCurrency(itemProductOrder.price || 0,
                                            shopData?.currency) }}
                                    </span>
                                </div>
                                <div class='h-stack quantity'>
                                    <button class='quantity-button minus' :disabled="itemProductOrder.quantity <= 0"
                                        v-on:click="() => {
                                            itemProductOrder.quantity = parseFloat(itemProductOrder.quantity.toFixed(1)) - 1 > 0 ? parseFloat(itemProductOrder.quantity.toFixed(1)) - 1 : 0;
                                            checkDeliveryPrice();
                                        }">
                                        <Icon name="ic:outline-minus" />
                                    </button>
                                    <input type="number" :value="itemProductOrder.quantity"
                                        class="quantity-input input-custom"
                                        max=1000000000000 v-on:input="($event: any) => {
                                            itemProductOrder.quantity = parseFloat(itemProductOrder.quantity.toFixed(1));
                                        }" v-on:change="()=>{
                                            checkDeliveryPrice();
                                        }">
                                    <button class='quantity-button plus' v-on:click="() => {
                                        itemProductOrder.quantity = parseFloat(itemProductOrder.quantity.toFixed(1)) > 0 ? parseFloat(itemProductOrder.quantity.toFixed(1)) + 1 : 1;
                                        checkDeliveryPrice();
                                    }">
                                        <Icon name="ic:outline-plus" />
                                    </button>

                                    <button class="delete-item" v-on:click="() => {
                                        orderItems.splice(index, 1);
                                        setOptionShopProduct()

                                    }">{{ $t('MyShopCreateOrderComponent.xoa') }}</button>
                                </div>

                            </div>
                            <input autoComplete="off" class='note-input'
                                :placeholder="$t('CartComponent.ghi_chu_cho_san_pham')"
                                :maxlength="appConst.max_text_short"
                                :title="$t('CartComponent.ghi_chu_cho_san_pham')" :value="itemProductOrder.notes"
                                v-on:input="($event: any) => {
                                    itemProductOrder.notes = $event.currentTarget.value;
                                }" />
                        </div>
                    </div>
                </div>
                <div class="payment-method">
                    <Icon class="icon-label" name="carbon:wallet"></Icon>
                    <div class="content-container">
                        <span class="label">
                            {{ $t('MyShopCreateOrderComponent.hinh_thuc_thanh_toan') }}
                        </span>
                        <span class="content">
                            {{ $t('MyShopCreateOrderComponent.thanh_toan_khi_nhan_hang') }}
                        </span>
                    </div>
                </div>

                <div class="payment-method">
                    <Icon class="icon-label" name="carbon:delivery-parcel"></Icon>
                    <div class="content-container">
                        <span class="label">
                            {{ $t('MyShopCreateOrderComponent.phuong_thuc_nhan_hang') }}
                        </span>
                        <span class="h-stack content">
                            <div class="content-item" :class="{ 'active': !deliveryType }" v-on:click="() => {
                                deliveryType = false;
                                checkDeliveryPrice()
                            }">
                                <Icon name="ion:radio-button-on" v-if="!deliveryType"></Icon>
                                <Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
                                <span>{{ $t('MyShopCreateOrderComponent.giao_tan_noi') }}</span>
                            </div>
                            <div class="content-item" :class="{ 'active': deliveryType }" v-on:click="() => {
                                deliveryType = true;
                                deliveryPrice = 0;
                            }">
                                <Icon name="ion:radio-button-on" v-if="deliveryType"></Icon>
                                <Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
                                <span>{{ $t('MyShopCreateOrderComponent.tu_lay_hang') }}</span>
                            </div>

                        </span>

                        <div class="delivery-partners-content-container"
                            v-if="!deliveryType && listPartners?.length && listOptionsDeliveryPrice?.length">
                            <span class="h-stack delivery-partners-content">
                                <img class="partner-logo" :src="selectedPartner?.information?.logo"
                                    v-if="selectedPartner?.information?.logo" :alt="`logo ${selectedPartner?.name}`">
                                <span class="partner-name" v-else>
                                    {{ selectedPartner?.name }}
                                </span>
                            </span>

                            <span class="label service-type">
                                {{ $t('OrderComponent.goi_dich_vu') }} <em v-if="delivery_price_checking">{{
                                    $t('OrderComponent.dang_kiem_tra') }}</em>
                            </span>
                            <div class="list-options-delivery-price">
                                <button class="delivery-price-option" :disabled="delivery_price_checking" v-on:click="() => {
                                    selectedDeliveryPrice = itemPrice;
                                    deliveryPrice = itemPrice.price
                                }" :class="{ 'selected': itemPrice?.service_id == selectedDeliveryPrice?.service_id }"
                                    v-for="itemPrice of listOptionsDeliveryPrice">
                                    <Icon name="ion:radio-button-on"
                                        v-if="itemPrice?.service_id == selectedDeliveryPrice?.service_id"></Icon>
                                    <Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
                                    <span>{{ itemPrice.name }} <em>({{ itemPrice.distance ?? driving_distance }} km - {{
                                        itemPrice.price ? formatCurrency(itemPrice.price) :
                                            $t('MyShopCreateOrderComponent.doi_bao_gia') }})</em></span>
                                </button>

                            </div>
                        </div>
                        <div class="delivery-partners-content-container" v-if="!deliveryType && !listPartners?.length">

                            <em class="non-delivery-partners">{{ $t('OrderComponent.chua_ket_noi_don_vi_van_chuyen')
                                }}</em>
                        </div>
                    </div>
                </div>

                <div class='note-container'>
                    <Icon class="icon-label" name="uil:comment-alt-notes"></Icon>
                    <div class="content-container">
                        <span class='title'>
                            {{ $t('MyShopCreateOrderComponent.ghi_chu') }} <em class="text-length">({{ orderData.notes?.length ?? 0 }}/{{ appConst.max_text_short }})</em>
                        </span>
                        <div class='h-stack note-order-input-container'>
                            <textarea :placeholder="$t('MyShopCreateOrderComponent.ghi_chu_placeholder')"
                            :maxlength="appConst.max_text_long"
                                v-model="orderData.notes" name="note-cart" id="note-cart" rows={5}
                                class='note-order-input'></textarea>
                        </div>
                    </div>


                </div>

                <div class="payment-info-container">
                    <div class="payment-info-content">
                        <span class="title">
                            {{ $t('MyShopCreateOrderComponent.tam_tinh') }}
                        </span>
                        <span class="value">
                            {{
                                getCartTotalPrice() ?
                                    formatCurrency(getCartTotalPrice() || 0, shopData?.currency)
                                    : $t('MyShopCreateOrderComponent.gia_lien_he')
                            }}
                        </span>
                    </div>
                    <div class="payment-info-content off">
                        <span class="title">
                            {{ $t('MyShopCreateOrderComponent.giam_gia') }}
                        </span>
                        <span class="value">
                            {{ formatCurrency(((getCartTotalPriceOff() || 0) - (getCartTotalPrice() || 0)) || 0,
                                shopData?.currency) }}
                        </span>
                    </div>
                    <div class="payment-info-content off">
                        <span class="title">
                            {{ $t('MyShopCreateOrderComponent.phi_van_chuyen') }}
                            <span v-if="driving_distance" class="distance">
                                ({{ driving_distance }} km)
                            </span>

                        </span>
                        <span class="value">
                            {{ deliveryPrice != null ? formatCurrency(deliveryPrice || 0) :
                                $t('MyShopCreateOrderComponent.doi_bao_gia') }}
                        </span>
                    </div>
                    <div class="payment-info-content total-left">
                        <span class="title">
                            {{ $t('MyShopCreateOrderComponent.thanh_tien') }}
                        </span>
                        <span class="value">
                            {{
                                getCartTotalPriceOff() ?
                                    formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice ||
                                        0).toString()),
                                        shopData?.currency)
                                    :
                                    $t('MyShopCreateOrderComponent.gia_lien_he')
                            }}
                        </span>
                    </div>

                </div>
                <div class="order-footer">
                    <div class="order-footer-content">
                        <div class="total">
                            <span>{{ $t('MyShopCreateOrderComponent.tong_tien') }}</span>
                            <span class="price">
                                {{ getCartTotalPriceOff() ?
                                    formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice ||
                                        0).toString()),
                                        shopData?.currency)
                                    : $t('MyShopCreateOrderComponent.goi_bao_gia')
                                }}
                            </span>
                        </div>
                        <button :disabled="isSubmiting" v-on:click="() => {
                            createOrder()
                        }">
                            <Icon name="eos-icons:loading" v-if="isSubmiting"></Icon>
                            {{
                                $t('MyShopCreateOrderComponent.luu')
                            }}
                        </button>
                    </div>

                </div>
            </div>
            <!-- <VueFinalModal class="my-modal-container" :click-to-close="false"
                content-class="my-modal-content-container edit-customer-info-modal" v-model="isEditingCustomerInfo"
                v-on:closed="() => {
                    if (latitude && longitude) {
                        getDistance()
                    }
                    isEditingCustomerInfo = false;

                }" contentTransition="vfm-slide-up">
                <div class='v-stack edit-customer-info-container'>
                    <span class='title'>
                        {{ $t('MyShopCreateOrderComponent.thong_tin_nguoi_nhan') }}
                    </span>


                    <button class="select-user-saved" v-if="userInfo" v-on:click="() => {
                        showSelectUser = true;
                        isEditingCustomerInfo = false;
                    }">
                        {{ $t('MyShopCreateOrderComponent.chon_tu_so_dia_chi') }}
                    </button>
                    <div class='v-stack'>
                        <span class='label required'>
                            {{ $t('MyShopCreateOrderComponent.ten') }}
                        </span>
                        <input :title="$t(' MyShopCreateOrderComponent.ten')" name='customer-name' maxLength=255
                            autoComplete="off" class='input-order'
                            :placeholder="$t('MyShopCreateOrderComponent.ten_placeholder')" :value="name || null"
                            v-on:input="($event: any) => {
                                name = $event.target.value;
                                nameValidation()
                            }" v-on:blur="() => {
                                nameValidation()
                            }" />
                        <span class='error-message'>{{ nameErr }}</span>
                    </div>
                    <div class='v-stack'>
                        <span class='label required'>
                            {{ $t('MyShopCreateOrderComponent.so_dien_thoai') }}
                        </span>
                        <input :title="$t(' MyShopCreateOrderComponent.so_dien_thoai')" name='customer-phone'
                            maxLength=255 autoComplete="off" class='input-order' type="phone"
                            :placeholder="$t('MyShopCreateOrderComponent.so_dien_thoai_placeholder')"
                            :value="phone || null" v-on:input="($event: any) => {
                                phone = validPhone($event.target.value);
                                phoneValidation();
                            }" v-on:blur="() => {
                                phoneValidation();
                            }" />
                        <span class='error-message'>{{ phoneErr }}</span>
                    </div>
                    <div class='v-stack'>
                        <span class='label required'>
                            {{ $t('MyShopCreateOrderComponent.dia_chi') }}
                        </span>
                        <input :title="$t('MyShopCreateOrderComponent.dia_chi')" name='customer-address' maxLength=255
                            autoComplete="off" class='input-order'
                            :placeholder="$t('MyShopCreateOrderComponent.dia_chi_placeholder')" :value="address || null"
                            v-on:input="($event: any) => {
                                address = $event.target.value;
                                addressValidation()
                            }" v-on:blur="() => {
                                addressValidation()
                            }" />
                        <span class='error-message'>{{ addressErr }}</span>
                    </div>
                    <div class="map-container">
                        <client-only>
                            <LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
                                leafletMap = e;
                                leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

                                initLeafletMap();
                            }" :max-zoom="appConst.leafletMapTileOption.maxZoom" v-on:update:center="async (bounds: any) => {
                                console.log('center change');
                                latitude = leafletMap.getCenter().lat;
                                longitude = leafletMap.getCenter().lng;
                                await getUserAddress();

                            }" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true"
                                :use-global-leaflet="true">
                                <LControlZoom position="bottomright"></LControlZoom>
                                <span class="current-location-leaflet" :title="$t('Map.vi_tri_cua_ban')" v-on:click="() => {
                                    gotoCurrentLocationLeaflet();
                                }
                                    ">
                                    <Icon name="line-md:my-location-loop" class="my-location-icon" />
                                </span>
                                <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
                                    data-placement="right" :title="$t('Map.nhan_de_chuyen_loai_map')" v-bind:style="{
                                        backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                                    }" v-on:click="(event: any) => {
                                        if (event.isTrusted) {
                                            if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.roadmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.hyprid;
                                                mapTypeTitle = $t('Map.ve_tinh');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_sateline;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.hyprid
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.streetmap;
                                                mapTypeTitle = $t('Map.co_dien');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_streetmap;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.streetmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.roadmap;
                                                mapTypeTitle = $t('Map.ve_tinh_va_nhan');
                                                mapType = 'roadmap';
                                                buttonMapTileBackgound = map_sateline;
                                            }
                                        } else event.preventDefault();
                                    }
                                        ">
                                    <span>{{ mapTypeTitle }}</span>
                                </div>
                                <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                    :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
                                    name="GoogleMap">
                                </LTileLayer>
                                <div class="marker-location">
                                    <img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon"
                                        alt="" />
                                </div>
                            </LMap>
                        </client-only>
                    </div>

                    <div class='h-stack edit-customer-info-actions'>
                        <button class='reject-button' :disabled=isSavingUserInfo v-on:click="() => {
                            name = orderData.customer_name;
                            phone = validPhone(orderData.customer_phone);
                            address = orderData.address;
                            latitude = orderData.latitude;
                            longitude = orderData.longitude;
                            isEditingCustomerInfo = false;
                            phoneValidation();

                        }">
                            {{ $t('MyShopCreateOrderComponent.huy') }}
                        </button>
                        <button class='accept-button' :disabled="isSavingUserInfo == true
                            || !name?.length
                            || !phone?.length
                            || !address?.length
                            || nameErr.length > 0
                            || phoneErr.length > 0
                            || addressSeacrhing
                            " v-on:click="() => {
                                updateUserInfo()
                            }">
                            {{ $t('MyShopCreateOrderComponent.xac_nhan') }}
                        </button>
                    </div>
                </div>


            </VueFinalModal> -->
            <EditReceiverInfoComponent :init_data="{
					name: name,
					phone: phone,
					address: address,
					latitude: latitude,
					longitude: longitude,
					province_id: province_id,
					district_id: district_id,
					ward_id: ward_id,
                    note: address_note,
                    images: JSON.parse(JSON.stringify(address_images))
				}" :user_info="JSON.parse(JSON.stringify(userInfo))" v-if="isEditingCustomerInfo" v-on:close="() => {
					if (latitude && longitude) {
						getDistance()
					}
					isEditingCustomerInfo = false;
					phoneValidation();
				}" v-on:show_select_user="() => {
					showSelectUser = true;
					isEditingCustomerInfo = false;
				}" v-on:submit_user="(data)=>{
                    console.log(data);
					name = data.name;
					phone = data.phone;
					address = data.address;
					latitude = data.latitude;
					longitude = data.longitude;
					province_id = data.province_id;
					district_id = data.district_id;
					ward_id = data.ward_id;
                    address_images = data.images?.length ? JSON.parse(JSON.stringify(data.images)) : [];
                    address_note = data.note;
                    // isEditingCustomerInfo = false;
					updateUserInfo();
				}">

				</EditReceiverInfoComponent>
            <VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
                content-class="my-modal-content-container form-modal saved-address-modal-container" :click-to-close="false"
                :keep-overlay="true"
                :modal-id="'select_saved_address'"
                v-model="showSelectUser" v-on:closed="() => {
                    showSelectUser = false
                }" contentTransition="vfm-slide-down">
                <SavedAddressComponent :mode="'order'" :selectedObject="{
                    name: orderData.customer_name,
                    phone: validPhone(orderData.customer_phone),
                    address: orderData.address,
                    latitude: orderData.latitude,
                    longitude: orderData.longitude,
                    note: orderData.extra_data?.location?.note,
                    images: orderData.extra_data?.location?.images,
                }" v-on:close="() => {
                    showSelectUser = false;
                }" v-on:enterTempAddres="() => {
                    showSelectUser = false;
                    isEditingCustomerInfo = true;
                }" v-on:selectedUser="(selectedObj: any) => {
                    showSelectUser = false;
                    setDataOnSelected(selectedObj);
                }"></SavedAddressComponent>
            </VueFinalModal>
            <VueFinalModal class="my-modal-container"
                content-class="my-modal-content-container add-product-to-create-order-modal" :overlay-behavior="'persist'"
                v-model="showModalAddProduct" v-on:closed="() => { showModalAddProduct = false }"
                contentTransition="vfm-slide-up">
                <div class='add-product-container'>
                    <h3 class='title'>{{ $t('MyShopCreateOrderComponent.them_san_pham') }}</h3>

                    <div class='h-stack search-bar'>
                        <Icon name="ion:search" size="20" v-show="!searchProductLoading" />
                        <Icon name="eos-icons:loading" size="20" v-show="searchProductLoading" />

                        <input type="search" :value="searchProduct" class='search-input-container'
                            :placeholder="$t('MyShopCreateOrderComponent.tim_san_pham')" v-on:input="async ($event: any) => {
                                searchProductLoading = true;
                                searchProduct = $event.target.value;
                                searchingProduct();
                            }" />
                    </div>
                    <div class='products v-stack' v-on:scroll="() => {
                        listFilterScroll()
                    }">
                        <img loading="lazy" :src="none_result" :placeholder="none_result" class='none-search-result'
                            v-if="!shopProducts || !shopProducts.length" />
                        <label v-if="shopProducts && shopProducts.length"
                            v-for="(itemShopProduct, index) in shopProducts " class="product-result-item"
                            :class="({ 'disabled': itemShopProduct.existing, 'odd': index % 2 == 1 })" v-on:click="async () => {
                                if (!itemShopProduct?.existing) {
                                    itemShopProduct.selected = !itemShopProduct?.selected;
                                    let arrFilter = JSON.parse(JSON.stringify(shopProducts));
                                    let arrSelect = JSON.parse(JSON.stringify(listProductSelect));

                                    let indexSelected = arrSelect.findIndex(function (e: any) {
                                        return e.id == itemShopProduct.id
                                    });
                                    if (indexSelected != -1) {
                                        arrSelect.splice(indexSelected, 1)
                                    }
                                    else if (indexSelected == -1) {
                                        arrSelect.push(itemShopProduct)
                                    }
                                    shopProducts = JSON.parse(JSON.stringify(arrFilter));
                                    listProductSelect = JSON.parse(JSON.stringify(arrSelect))
                                }
                            }" :key="itemShopProduct.id">
                            <Icon name="material-symbols:check-circle-outline-rounded" class='my-checkbox'
                                v-if="itemShopProduct.selected"></Icon>
                            <Icon name="ic:outline-radio-button-unchecked" class='my-checkbox' v-else></Icon>
                            <div class="product-detail">
                                <img :src="itemShopProduct.profile_picture?.length ? domainImage + itemShopProduct.profile_picture : icon_for_product"
                                    v-on:error="(event: any) => {
                                        event.target.src = icon_for_broken_image;
                                    }" />
                                <div class="v-stack">
                                    <span>{{ showTranslateProductName(itemShopProduct) }}</span>
                                    <em>{{ formatCurrency(itemShopProduct.price, shopData?.currency) }}</em>
                                </div>
                            </div>

                        </label>
                        <div id="last_of_list_filter"></div>
                        <div class='h-stack actions'>
                            <span>
                                {{ $t('MyShopCreateOrderComponent.da_chon') }}: {{ listProductSelect.length }}
                            </span>
                            <button
                                v-if="shopProducts.filter((e: any) => { return e.selected && !e.existing }).length > 0"
                                v-on:click="() => {
                                    let arrFilter = JSON.parse(JSON.stringify(shopProducts));
                                    arrFilter.forEach((item: any) => {
                                        if (!item.existing && item.selected) {
                                            item.selected = false;
                                        }
                                    });
                                    shopProducts = arrFilter;
                                    listProductSelect = [];
                                }" class='clear-selected-button'>
                                {{ $t('MyShopCreateOrderComponent.bo_chon') }}
                            </button>
                            <button class='accept-button' v-on:click="async () => {
                                let arrFilter = JSON.parse(JSON.stringify(shopProducts));
                                let listOrderProducts = JSON.parse(JSON.stringify(orderItems));
                                listProductSelect.forEach((e: any) => {
                                    let indexFilter = arrFilter.findIndex(function (itemFilter: any) {
                                        return e.id == itemFilter.id
                                    });
                                    if (indexFilter != -1) {
                                        arrFilter[indexFilter].existing = true
                                    }
                                    let newItem: CartDto = {
                                        product_id: e.id,
                                        product: JSON.parse(JSON.stringify(e)),
                                        price: e.price_off ? e.price_off : e.price,
                                        quantity: 1,
                                        shop_id: shopData.id,
                                        shop: JSON.parse(JSON.stringify(shopData)),
                                        notes: ''
                                    }
                                    listOrderProducts.push(newItem);
                                })
                                showModalAddProduct = false;
                                orderItems = JSON.parse(JSON.stringify(listOrderProducts));
                                shopProducts = JSON.parse(JSON.stringify(arrFilter));
                                listProductSelect = [];
                                orderItems
                            }">
                                {{ $t('MyShopCreateOrderComponent.xong') }}
                            </button>
                        </div>
                    </div>
                </div>
            </VueFinalModal>
        </div>
        <DateTimePickerComponent v-if="showDateTimePickerModal"
            :title="t('MyShopCreateOrderComponent.thoi_gian_nhan_hang')" :startDate="moment()"
            :endDate="moment().add(30, 'days')" :startTime="moment().hour(7).minutes(0).seconds(0).milliseconds(0)"
            :endTime="moment().hour(22).minutes(31).seconds(0).milliseconds(0)"
            :initialDate="orderData.delivery_time && moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').isValid() ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY') : null"
            :initialTime="orderData.delivery_time && moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').isValid() ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('HH:mm') : null"
            :stepMinute="5" :firstStepMinute="shopData.min_time_to_delivery ?? 30" v-on:close="() => {
                showDateTimePickerModal = false
            }" v-on:submit="(e: any) => {
                showDateTimePickerModal = false
                orderData.delivery_time = e;
                checkDeliveryPrice();
            }"></DateTimePickerComponent>
    </div>

</template>

<script lang="ts" setup>
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_product from '../../assets/image/icon-for-product.png';
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import none_result from "~/assets/image/none-result-2.webp";
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber, nonAccentVietnamese, showTranslateProductName, validPhone } from '~/assets/AppConst';
import type { CartDto, OrderDto } from '~/assets/appDTO';
import moment from 'moment'
import appRoute from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import { PlaceService } from '~/services/placeService/placeService';
import { DeliveryService } from '~/services/orderService/deliveryService';
import { UserAddressService } from '~/services/userAddressService/userAddressService';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { DeliveryPartnerService } from '~/services/deliveryPartnerService/deliveryPartnerService';


const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    mode: null,
    shop_id: null
})
const nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.MyShopCreateOrderComponent')
})
var shopService = new ShopService()
var agentService = new AgentService();
var orderService = new OrderService();
var placeService = new PlaceService();
var deliveryService = new DeliveryService();
var userAddressService = new UserAddressService();
var deliverPartnerService = new DeliveryPartnerService();

var leafletMap: L.Map;
var tempLeafletMap: L.Map;

var userInfo = ref(null as any);

var orderData = ref({} as OrderDto);
var deliveryType = ref(false);
var deliveryPrice = ref<number | null>(0);
var paymentMethod = ref(1);
var name = ref("");
var nameErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var address = ref("");
var addressErr = ref("");
var province_id = ref(null as any);
var district_id = ref(null as any);
var ward_id = ref(null as any);
var address_note = ref<any>("");
var address_images = ref<any>([]);

var orderItems = ref([] as CartDto[]);
var total_amount = ref(0);
var grand_total = ref(0);
var discount_amount = ref(0);

var dataSavedUsers = ref([] as any);
var showDateTimePickerModal = ref(false);

var shop_id = ref(route.params.id ?? null);
var shopData = ref(null as any)

var driving_distance = ref(0);
var control: any;

var isSubmiting = ref(false);
var saveUserInfo = ref(false);
var isSavingUserInfo = ref(false);
var isEditingCustomerInfo = ref(false);
var showSelectUser = ref(false);

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('Map.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var latitude = ref(null as any);
var longitude = ref(null as any);
var disabledDragTab = ref(false);
var addressSeacrhing = ref(false);
var buttonMapTileBackgound = ref(map_sateline);

var searchAddressTimeout: any;

var searchProduct = ref("");
var showModalConfirmResetChange = ref(false);
var showModalAddProduct = ref(false);
var searchProductLoading = ref(false);
var shopProducts = ref([] as any[]);
var myShopProductsCount = ref(0)
var listProductSelect = ref([]);

var searchProductTimeout: any;
var loadMoreTimeOut: any;

var webInApp = ref(null as any);

var listPartners = ref<any>([]);
var selectedPartner = ref<any>(null);
var showSelectDeliveryPartner = ref(false);
var delivery_price_checking = ref(false);
var listOptionsDeliveryPrice = ref<any>(null);
var selectedDeliveryPrice = ref<any>();

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
// watch(() => [user_latitude.value, user_longitude?.value], () => {
// 	latitude.value = user_latitude?.value;
// 	longitude.value = user_longitude?.value;
// });
onUnmounted(()=>{
    nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onBeforeMount(async () => {
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });
})
onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    let userInfoCurrent = await localStorage.getItem(appConst.storageKey.userInfo);
    userInfo.value = JSON.parse(userInfoCurrent as string);
    if (userInfo.value) {
        orderData.value = {
            ...orderData.value,
            customer_id: userInfo.value ? userInfo.value.id : null,
        };
        setDataOnSelected(userInfo.value);
    }
    await getMyListAddress();
    await getShopInfo().then(async () => {
        await getShopDeliveryParters().then(res => {
            if (!listPartners.value.length) {
                // deliveryType.value = true;
            }
        });
    });
})
function getShopInfo() {
    return new Promise((resolve) => {
        if (props.mode != 'agent') {
            getMyShop().then((res) => {
                if (res) {
                    resolve(res);
                }
                resolve(null)
            });
        }
        else {
            getShopDetailByAgent().then((res) => {
                if (res) {
                    resolve(res);
                }
                resolve(null)
            });
        }
    })
}
function getMyShop() {
    return new Promise((resolve) => {
        shopService.myShop().then(res => {
            if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
                shopData.value = res.body.data;
            }
            else {
                shopData.value = null;
            }
            resolve(shopData.value)
        })
    })

}

function getShopDetailByAgent() {
    return new Promise((resolve) => {
        agentService.agentShopDetail(shop_id.value as any).then(res => {
            if (res.status && res.status == HttpStatusCode.Ok) {
                shopData.value = res.body.data;
            }
            else {
                shopData.value = null;
            }
            resolve(shopData.value)
        })
    })

}

function getShopDeliveryParters() {
    let shop_id = shopData.value?.id;
    return new Promise((resolve) => {
        deliverPartnerService.shopListPartner(shop_id).then((res) => {
            if (res.status == 200) {
                listPartners.value = res.body?.partners.filter((e: any) => e.is_connected && e.is_enabled);
                let defaultPartner = res.body?.partners.filter((e: any) => e.is_connected && e.is_enabled && e.is_default);
                selectedPartner.value = defaultPartner[0] ?? listPartners.value[0] ?? null;
            }
            resolve(listPartners.value);
        })
    })

}

async function getMyListAddress() {
    let res = await userAddressService.myListAddress();
    if (res.status == HttpStatusCode.Ok) {
        dataSavedUsers.value = res.body.data;
        let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
            return e.is_default == true;
        })

        if (indexDefault != -1) {
            setDataOnSelected(dataSavedUsers.value[indexDefault]);
        }
        else {
            setDataOnSelected(dataSavedUsers.value[0]);

        }
        setTimeout(() => {
            getDistance();
        }, 1000);
    }
    else {
        dataSavedUsers.value = [];
    }
}
function setDataOnSelected(selectedObj: any) {
    console.log(selectedObj);
    orderData.value = {
        ...orderData.value,
        customer_name: selectedObj ? selectedObj.name : null,
        customer_phone: selectedObj ? selectedObj.phone : null,
        address: selectedObj ? selectedObj.address : null,
        latitude: selectedObj ? selectedObj.latitude : null,
        longitude: selectedObj ? selectedObj.longitude : null,
        province_id: selectedObj ? selectedObj.province_id : null,
        district_id: selectedObj ? selectedObj.district_id : null,
        ward_id: selectedObj ? selectedObj.ward_id : null,
        extra_data: {
            location: {
                note: selectedObj?.note,
                images: selectedObj?.images ? JSON.parse(JSON.stringify(selectedObj?.images)) : []
            }
        }
    };
    name.value = selectedObj ? selectedObj.name : null;
    phone.value = selectedObj ? selectedObj.phone : null;
    address.value = selectedObj ? selectedObj.address : null;
    latitude.value = selectedObj ? selectedObj.latitude : null;
    longitude.value = selectedObj ? selectedObj.longitude : null;
    province_id.value = selectedObj ? selectedObj.province_id : null;
    district_id.value = selectedObj ? selectedObj.district_id : null;
    ward_id.value = selectedObj ? selectedObj.ward_id : null;
    address_note.value = selectedObj ? selectedObj.note : null;
    address_images.value = selectedObj?.images ? JSON.parse(JSON.stringify(selectedObj.images)) : null;
    getDistance();
}
async function updateUserInfo() {
    if (saveUserInfo.value) {
        let arr = JSON.parse(JSON.stringify(dataSavedUsers.value));
        let index = arr.findIndex((e: any) => {
            return e.name == name.value
                && e.phone == phone.value
                && e.address == address.value
                && e.latitude == latitude.value
                && e.longitude == longitude.value
        })

        if (index == -1 && userInfo?.value?.id) {
            let info = {
                id: null,
                name: name.value,
                address: address.value,
                phone: validPhone(phone.value),
                latitude: latitude.value,
                longitude: longitude.value,
                province_id: province_id.value,
                district_id: district_id.value,
                ward_id: ward_id.value,
                title: "new",
                is_default: false,
            }
            // arr.push({
            // 	customer_name: name.value,
            // 	customer_phone: phone.value,
            // 	address: address.value,
            // 	latitude: latitude.value,
            // 	longitude: longitude.value,

            // });
            // localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(arr));
            // dataSavedUsers.value = arr;
            userAddressService.create(info).then((res) => {
                if (res.status == HttpStatusCode.Ok) {
                    getMyListAddress();
                }
                else {
                    toast.error(t('SavedAddressComponent.luu_dia_chi_that_bai'))
                }
            })
        }

    }
    orderData.value = {
        ...orderData.value,
        customer_name: name.value,
        customer_phone: await validPhone(phone.value),
        address: address.value,
        latitude: latitude.value,
        longitude: longitude.value,
		extra_data: {
			location: {
                note: address_note.value,
                images: address_images.value?.length ? JSON.parse(JSON.stringify(address_images.value)): []
            }
		}
    };
    isEditingCustomerInfo.value = false;
}
async function getDistance() {
    if (control) {
        control.remove();
    }

    control = nuxtApp.$L.Routing.control({
        waypointMode: 'connect',
        router: nuxtApp.$L.Routing.osrmv1({
            serviceUrl: appConst.urlOSRMv1,
            requestParameters: {
                overview: 'full',
                annotations: true,
                steps: true,
                alternatives: 2,

            },
        }),
        plan: new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(shopData.value?.latitude, shopData.value?.longitude),
            nuxtApp.$L.latLng(latitude.value, longitude.value)
        ], {
            createMarker: () => (false),
        }),
    })
    if (tempLeafletMap) {
        control.addTo(tempLeafletMap).on('routesfound', (e: any) => {
            driving_distance.value = parseFloat((e.routes[0].summary.totalDistance / 1000).toFixed(1));
            checkDeliveryPrice();
        });
    }

}

async function initLeafletMap() {
    // markersCluster = new nuxtApp.$L.MarkerClusterGroup({
    // 	maxClusterRadius: 5,
    // 	iconCreateFunction: (cluster) => createClusterElement(cluster),
    // }).addTo(leafletMap);
    console.log("init map")
    await setCurrentLocationLeaflet();
    // (leafletMap as any)["gestureHandling"].enable();
}
async function setCurrentLocationLeaflet() {
    if (latitude.value && longitude.value) {
        console.log("có lat lng");
        leafletMap.setView([latitude.value, longitude.value], 17);
    } else {
        leafletMap.setView([user_latitude.value ?? appConst.defaultCoordinate.latitude, user_longitude.value ?? appConst.defaultCoordinate.longitude], 17);
        // if ("geolocation" in navigator) {
        //     navigator.geolocation.getCurrentPosition(
        //         (position) => {

        //             latitude.value = position.coords.latitude;
        //             longitude.value = position.coords.longitude;
        //             leafletMap.setView(
        //                 [position.coords.latitude, position.coords.longitude],
        //                 17
        //             );
        //         },
        //         (error) => {
        //             latitude.value = appConst.defaultCoordinate.latitude;
        //             longitude.value = appConst.defaultCoordinate.longitude;
        //             leafletMap.setView([latitude.value, longitude.value], 17);
        //         },
        //         {
        //             enableHighAccuracy: false, // Use less accurate but faster methods
        //             timeout: 5000, // Set a timeout (in milliseconds)

        //         }
        //     );
        // }
    }
}
function getUserAddress() {
    clearTimeout(searchAddressTimeout);
    addressSeacrhing.value = true;
    searchAddressTimeout = setTimeout(() => {
        placeService
            .myGeocoderByLatLngToAddress(latitude.value, longitude.value)
            .then((res: any) => {
                if (res.body.data && res.body.data.length) {
                    address.value = res.body.data[0].address
                        ? res.body.data[0].address
                        : "";
                    province_id.value = res.body.data[0].province_id ? res.body.data[0].province_id : null;
                    district_id.value = res.body.data[0].district_id ? res.body.data[0].district_id : null;
                    ward_id.value = res.body.data[0].ward_id ? res.body.data[0].ward_id : null;
                    addressValidation()
                }
                addressSeacrhing.value = false;
            });
    }, 500);

}
async function gotoCurrentLocationLeaflet(event?: Event) {
    if (!event || event.isTrusted == true) {
        // if ("geolocation" in navigator) {
        //     navigator.geolocation.getCurrentPosition(
        //         (position) => {
        //             leafletMap.flyTo(
        //                 [position.coords.latitude, position.coords.longitude],
        //                 17
        //             );
        //             latitude.value = position.coords.latitude;
        //             longitude.value = position.coords.longitude;
        //             getUserAddress()
        //         },
        //         (error) => {
        //             latitude.value = appConst.defaultCoordinate.latitude;
        //             longitude.value = appConst.defaultCoordinate.longitude;
        //             leafletMap.flyTo([latitude.value, longitude.value], 17);
        //             getUserAddress()
        //         },
        //         {
        //             enableHighAccuracy: false, // Use less accurate but faster methods
        //             timeout: 5000, // Set a timeout (in milliseconds)

        //         }
        //     );
        // }
        leafletMap.flyTo(
            [user_latitude.value, user_longitude.value],
            17
        );
        latitude.value = user_latitude.value;
        longitude.value = user_longitude.value;
    }
}
function nameValidation() {
    if (!name.value || !name.value.length) {
        nameErr.value = t('MyShopCreateOrderComponent.vui_long_nhap_ten_nguoi_nhan')
    }
    else {
        nameErr.value = '';
    }
}

function phoneValidation() {
    let re = appConst.validateValue.phone;
    if (!phone.value || !validPhone(phone.value).length) {
        phoneErr.value = t('MyShopCreateOrderComponent.vui_long_nhap_sdt')
        return;
    }
    if (!re.test(validPhone(phone.value))) {
        phoneErr.value = t('MyShopCreateOrderComponent.sdt_khong_dung');
        return;
    }
    else {
        phoneErr.value = '';
    }
}

function addressValidation() {
    if (address.value?.length) {
        addressErr.value = "";
    }
    else addressErr.value = t('MyShopCreateOrderComponent.vui_long_nhap_dia_chi');
}

function checkDeliveryPrice() {
    if (!deliveryType.value && listPartners.value.length) {
        delivery_price_checking.value = true;
        let duration =
            (!orderData.value.delivery_time || moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes') < 30)
                ? 30
                : moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes');

        let bodyRef = {};

        // let orderTemp = JSON.parse(JSON.stringify(orderData.value));

        // delete orderTemp.delivery;

        bodyRef = {
            partner: selectedPartner.value.name.toLowerCase().includes('remagan') ? 'remagan' : selectedPartner.value.name.toLowerCase(),
            shop_id: shopData.value?.id,
            distance: driving_distance.value,
            duration: duration,
            path: [
                {
                    lat: shopData.value?.latitude,
                    lng: shopData.value?.longitude,
                    address: shopData.value?.address,
                    name: shopData.value?.name,
                    phone: shopData.value?.phone
                },
                {
                    lat: latitude.value,
                    lng: longitude.value,
                    address: address.value,
                    name: name.value,
                    phone: phone.value,
                }
            ],
            order: {
					...orderData.value,
					price: getCartTotalPrice(),
					price_off: getCartTotalPriceOff(),
					delivery_type: deliveryType.value,
					delivery_price: deliveryPrice.value ?? null,
					delivery_time: orderData.value?.delivery_time?.length && moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").isValid() ? moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").format("YYYY-MM-DD HH:mm") : null,
					delivery_partner: selectedPartner.value?.name?.includes('remagan') ? 'remagan' : selectedPartner.value?.name?.toLowerCase(),
					delivery_partner_id: selectedPartner.value?.id ?? '',
					// 	delivery_price: 0,
					payment_method: paymentMethod.value,
					
					shop_id: shopData.value?.id,
					delivery_distance: driving_distance.value,
					province_id: province_id.value,
					district_id: district_id.value,
					ward_id: ward_id.value,
					notes: orderData.value.notes ? orderData.value.notes : ''
				}
        }

        deliveryService.checkDeliveryPriceFromPartner(bodyRef).then((res) => {
            if (res.status == HttpStatusCode.Ok) {
                listOptionsDeliveryPrice.value = isArray(res.body.data) ? res.body.data : [res.body.data];
                selectedDeliveryPrice.value = listOptionsDeliveryPrice.value[0];
                deliveryPrice.value = selectedDeliveryPrice.value.price;
            }
            else {
                deliveryPrice.value = null;
                listOptionsDeliveryPrice.value = null;
                selectedDeliveryPrice.value = null;
            }
            delivery_price_checking.value = false;
        }).catch(() => {
            deliveryPrice.value = null;
            delivery_price_checking.value = false;
            listOptionsDeliveryPrice.value = null;
            selectedDeliveryPrice.value = null;
        })
    }
    else {
        deliveryPrice.value = 0
    }

}

function getShopProducts() {
    searchProductLoading.value = true;
    shopService.searchProductsInShop(
        searchProduct.value,
        shopData.value.id,
        [],
        20,
        0,
        null,
        null,
        true
    ).then(async res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
            let listTemp = res.body.data.result;
            shopProducts.value = listTemp;
            myShopProductsCount.value = res.body.data.count;
            searchProductLoading.value = false;
            setOptionShopProduct();
        }
        else {
            searchProductLoading.value = false;
        }
    }).catch(() => {
        searchProductLoading.value = false;
    })
}

function setOptionShopProduct() {
    let listTemp = JSON.parse(JSON.stringify(shopProducts.value));
    listTemp.forEach((item: any) => {
        item.existing = false;
        item.selected = false;
        let indexExisting = orderItems.value.findIndex(function (e: any) {
            return e.product_id == item.id
        });
        if (indexExisting != -1) {
            item.existing = true;
            item.selected = true;
        }
        let indexSelected = listProductSelect.value.findIndex(function (e: any) {
            return e.id == item.id
        });
        if (indexSelected != -1) {
            item.selected = true;
        }
    });

    shopProducts.value = JSON.parse(JSON.stringify(listTemp));
}

function getMoreFilterProducts() {
    clearTimeout(loadMoreTimeOut);
    if (shopProducts.value.length < myShopProductsCount.value) {
        loadMoreTimeOut = setTimeout(() => {
            searchProductLoading.value = true
            shopService.searchProductsInShop(
                searchProduct.value,
                shopData.value.id,
                [],
                20,
                shopProducts.value.length,
                null,
                null,
                true
            ).then(async res => {
                if (res.status && res.status == HttpStatusCode.Ok) {
                    let listTemp = res.body.data.result;
                    shopProducts.value = [...shopProducts.value, ...listTemp];
                    searchProductLoading.value = false;
                    setOptionShopProduct()
                }
                else {
                    searchProductLoading.value = false;
                }
            }).catch(() => {
                searchProductLoading.value = false;
            })
        }, 1000);

    }
}
function listFilterScroll() {
    let el = document.getElementById('last_of_list_filter')?.getBoundingClientRect().bottom;
    if (el && (el <= window.innerHeight + 10)) {
        getMoreFilterProducts()
    }
}
function searchingProduct() {
    clearTimeout(searchProductTimeout);
    searchProductTimeout = setTimeout(() => {
        getShopProducts();
    }, 500)
}

function getCartTotalPrice() {
    if (!orderItems.value.length) return 0;
    let total = 0;
    let indexPriceNull = orderItems.value.findIndex((e: any) => {
        return (e.product.price == 0 || e.product.price == null)
    })
    if (indexPriceNull != -1) return null;
    return orderItems.value
        .reduce((total: number, current: any) =>
            total + (parseFloat(current.product.price.toString()) ? parseFloat(current.product.price.toString()) * current.quantity : 0), 0
        )
}
function getCartTotalPriceOff() {
    if (!orderItems.value.length) return 0;
    let total = 0;
    let indexPriceNull = orderItems.value.findIndex((e: any) => {
        return e.price == 0 || e.price == null
    })
    if (indexPriceNull != -1) return null;
    return orderItems.value
        .reduce((total: number, current: any) =>
            total + (
                parseFloat(current.price.toFixed(1)) * current.quantity
            ), 0)
}

function createOrder() {
    
    if (!latitude.value || !longitude.value || !address.value) {
        isEditingCustomerInfo.value = true;
        return;
    }
    if (!orderItems.value?.length) {
        showModalAddProduct.value = true;
        if (!shopProducts.value.length) {
            searchingProduct();
        }
        return;
    }

    if (!orderData.value.delivery_time?.length) {
        showDateTimePickerModal.value = true;
        return;
    }
    isSubmiting.value = true;
    
    let orderTemp = {
        ...orderData.value,
        // price: getCartTotalPrice(),
        // price_off: getCartTotalPriceOff(),
        delivery_type: deliveryType.value,
        delivery_price_estimate: deliveryPrice.value || 0,
        delivery_time: orderData.value?.delivery_time?.length && moment(orderData.value?.delivery_time, "DD/MM/YYYY HH:mm").isValid() ? moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").format("YYYY-MM-DD HH:mm") : null,
        // 	delivery_price: 0,
        payment_method: paymentMethod.value,
        items: JSON.parse(JSON.stringify(orderItems.value)),
        shop_id: shopData.value.id,
        delivery_distance: driving_distance.value,
        province_id: province_id.value,
        district_id: district_id.value,
        ward_id: ward_id.value,
        notes: orderData.value.notes ? orderData.value.notes : '',
        extra_data: {
            location: {
                note: address_note.value,
                images: address_images.value?.length ? JSON.parse(JSON.stringify(address_images.value)): []
            }
        }
        // delivery: {
        //     latitude_from: shopData.value.latitude,
        //     longitude_from: shopData.value.longitude,
        //     latitude_to: latitude.value,
        //     longitude_to: longitude.value,
        //     distance: driving_distance.value,
        //     driver_id: null,
        // }
    }
    orderService.create(orderTemp).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            // isSubmiting.value = false;
            toast.success(t('MyShopCreateOrderComponent.tao_don_hang_thanh_cong'), {
                autoClose: 1000
            });
            setTimeout(() => {
                if (props.mode != 'agent') {
                    router.replace(appRoute.MyShopOrderDetailComponent.replace(':id', res.body.data.id))
                }
                else {
                    router.replace(appRoute.AgentOrderDetailComponent.replace(':shop_id', shopData.value?.id ?? shop_id.value).replaceAll(':id', res.body.data.id))
                }
            }, 1000);

        }
        else {
            isSubmiting.value = false;
            toast.error(res.body.message ?? t('MyShopCreateOrderComponent.tao_don_hang_that_bai'), {
                autoClose: 1000
            });
        }
    }).catch(err => {
        console.log(err);
        toast.error(t('MyShopCreateOrderComponent.tao_don_hang_that_bai'), {
            autoClose: 1000,
        });
        isSubmiting.value = false;
    })
}

function close(updated: any = null) {
    emit('close', updated);
    //  backHandler();
}


</script>

<style lang="scss" src="./MyShopCreateOrderStyles.scss"></style>