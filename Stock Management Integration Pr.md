# Stock Management Integration for NuxtJS Food Shop Application

## Task Overview
Create a complete Stock Management system integration for an existing NuxtJS food shop application. The system should handle inventory operations including stock imports, exports, waste tracking, and comprehensive reporting with real-time updates.

## API Endpoints to Integrate
- `POST /api/v1/stock/import` - Record stock purchases/imports
- `POST /api/v1/stock/export` - Record stock sales/exports
- `POST /api/v1/stock/waste` - Record waste and losses
- `GET /api/v1/stock/history/{product_id}` - Retrieve product stock history
- `POST /api/v1/stock/daily-summary` - Get daily stock summary reports
- `GET /api/v1/stock/my-shops-summary` - Get user's shops stock overview

## Implementation Requirements

### 1. Service Layer Creation
**Task**: Create a StockService class following the existing project pattern
- **Location**: `services/stockService/stockService.tsx`
- **Pattern**: Extend BaseHTTPService class (follow existing services like QuotationService, ProductService)
- **Methods Required**: stockImport, stockExport, stockWaste, getStockHistory, getDailySummary, getMyShopsSummary
- **Authentication**: Use existing JWT token system via BaseHTTPService (authentication_required=true)
- **URL Constants**: Add stock API endpoints to `~/assets/AppConst.ts` in the apiURL object

### 2. Mobile-First UI Components
**Task**: Create icon-based, mobile-optimized components following the existing project's design patterns
- **Main Component**: StockManagementDashboardComponent with 6 action cards (Import, Export, Waste, History, Reports, Current Stock)
- **Design Pattern**: Follow MyShopComponent.vue card layout with button-icon structure
- **Icons**: Use solar icon set (solar:warehouse-bold for main icon, specific icons for each action)
- **Styling**: High-contrast gradients, mobile-first responsive design, touch-friendly (44px minimum touch targets)
- **Location**: `components/myShop/stockManagement/` directory

### 3. Navigation Integration
**Task**: Add Stock Management access points to existing interfaces
- **My Shop Integration**: Add stock management option to MyShopComponent.vue in the shop management section
- **Agent Integration**: Add stock management option to AgentShopDetailDashboardComponent.vue
- **Pattern**: Follow existing "other-option" link structure with button-icon and chevron-right
- **Routes**: Add stock management routes to `~/assets/appRoute.ts`

### 4. Page Structure Implementation
**Task**: Create page hierarchy for both user types
- **My Shop Pages**: `/pages/my-shop/stock/` (index, import, export, waste, reports, history/[productId])
- **Agent Pages**: `/pages/agent/shop/[id]/stock/` (same structure as my-shop)
- **Components**: Each page should have corresponding components in `components/myShop/stockManagement/`
- **Layout**: Follow existing page patterns with SubHeaderV2Component and proper navigation

### 5. Real-time Updates Integration
**Task**: Implement MQTT-based real-time notifications
- **Service**: Use existing MqttService class
- **Topics**: `stock_updates_${shopId}` for shop-specific updates
- **Message Types**: stock_low, stock_update, stock_import_complete, stock_export_complete, stock_waste_recorded
- **Implementation**: Subscribe in components, publish after successful operations
- **UI Feedback**: Toast notifications, live stock level updates, low stock alerts

### 6. Multi-language Support
**Task**: Add comprehensive translations for all 4 supported languages
- **Files**: `locales/vi.json`, `locales/en.json`, `locales/ko.json`, `locales/ru.json`
- **Namespace**: "StockManagement" object with all required keys
- **Coverage**: UI labels, form fields, error messages, success messages, action descriptions
- **Keys Required**: quan_ly_kho, nhap_kho, xuat_kho, hong_mat, lich_su, bao_cao, ton_kho_hien_tai, so_luong, don_vi, gia_nhap, gia_ban, ly_do, ghi_chu, and more

### 7. Form Components and Validation
**Task**: Create mobile-optimized forms for stock operations
- **Import Form**: Product selection, quantity, unit, purchase price, supplier info, notes, receipt upload
- **Export Form**: Product selection with stock validation, quantity, sale price, order ID (optional), notes
- **Waste Form**: Product selection, quantity, unit, reason categories, photo upload, notes
- **Validation**: Client-side validation, stock availability checks, numeric validations
- **UI Pattern**: Follow existing form patterns with proper error handling and loading states

### 8. Reports and Analytics
**Task**: Create comprehensive reporting interface
- **Dashboard**: Summary cards with key metrics (total imports, exports, waste, current stock value)
- **Charts**: Visual representations using chart libraries compatible with the project
- **Filters**: Date range, product categories, operation types
- **Export**: PDF/Excel export functionality for reports
- **Performance**: Pagination for large datasets, lazy loading

### 9. Stock History and Tracking
**Task**: Implement detailed stock movement tracking
- **History View**: Timeline-based display of all stock movements for products
- **Details**: Date/time, operation type, quantity, price, executor, notes
- **Filtering**: By date range, operation type, product
- **Search**: Product name/ID search functionality
- **Mobile UX**: Swipe gestures, pull-to-refresh, infinite scroll

### 10. Error Handling and User Experience
**Task**: Implement comprehensive error handling and user feedback
- **Error Patterns**: Follow existing toast notification patterns
- **Loading States**: Skeleton screens for data loading, button loading states
- **Offline Handling**: Graceful degradation when offline
- **Confirmation Dialogs**: For destructive actions (waste recording, bulk operations)
- **Success Feedback**: Visual confirmations, real-time updates

## Technical Specifications

### Architecture Requirements
- **Service Pattern**: Extend BaseHTTPService for API integration
- **Component Structure**: Follow existing component organization in `components/myShop/`
- **Styling**: SCSS files following existing naming conventions (`*Styles.scss`)
- **State Management**: Use Vue 3 Composition API with reactive refs
- **Routing**: Nuxt 3 file-based routing with proper middleware

### Design Requirements
- **Mobile-First**: Responsive design optimized for mobile devices
- **Icon System**: Use existing Icon component with solar icon set
- **Color Scheme**: High-contrast gradients following existing design patterns
- **Typography**: Follow existing font sizing and weight conventions
- **Spacing**: Use existing padding/margin patterns (15px, 20px standards)

### Integration Requirements
- **Authentication**: JWT tokens via existing BaseHTTPService
- **Real-time**: MQTT integration using existing MqttService
- **Localization**: i18n integration with existing translation system
- **Navigation**: Integration with existing route structure and navigation patterns
- **Permissions**: Role-based access (shop owners, agents, admin)

### Performance Requirements
- **Mobile Optimization**: Fast loading, efficient scrolling, minimal bundle size
- **Caching**: Leverage existing caching mechanisms
- **API Efficiency**: Pagination, lazy loading, optimistic updates
- **Memory Management**: Proper component cleanup, MQTT subscription management

## Expected Deliverables

1. **Complete Stock Management System** with all CRUD operations
2. **Mobile-Optimized UI** with icon-based navigation and touch-friendly interface
3. **Real-time Updates** via MQTT integration for live stock notifications
4. **Multi-language Support** for all 4 languages (vi, en, ko, ru)
5. **Agent and Shop Owner Access** with proper role-based functionality
6. **Comprehensive Reporting** with visual analytics and export capabilities
7. **Error Handling and Validation** following existing project patterns
8. **Documentation** for new components and API integration

## Success Criteria

- Seamless integration with existing codebase following established patterns
- Mobile-first responsive design matching current app aesthetics
- Real-time functionality working with existing MQTT infrastructure
- Complete localization in all supported languages
- Proper error handling and user feedback
- Performance optimized for mobile devices
- Code quality matching existing project standards

### 4. Page Structure

#### `/pages/my-shop/stock/index.vue` - Main Stock Management Dashboard
- Overview cards with icon-based navigation
- Real-time stock level alerts
- Quick action buttons for common operations
- Recent stock movements summary

#### `/pages/my-shop/stock/import.vue` - Stock Import Page
- Mobile-optimized form with product selection
- Bulk import functionality
- Image upload for receipts
- Import history

#### `/pages/my-shop/stock/export.vue` - Stock Export Page
- Sales recording interface
- Order-based exports
- Stock validation
- Export history

#### `/pages/my-shop/stock/waste.vue` - Waste Management Page
- Waste recording form with reason categories
- Photo documentation
- Waste analytics

#### `/pages/my-shop/stock/reports.vue` - Reports & Analytics
- Daily/weekly/monthly summaries
- Visual charts and graphs
- Export capabilities

#### `/pages/my-shop/stock/history/[productId].vue` - Product Stock History
- Detailed timeline view
- Stock level charts
- Transaction details

#### `/pages/agent/shop/[id]/stock/index.vue` - Agent Stock Dashboard
- Same functionality as my-shop but for agent-managed shops
- Shop selection if agent manages multiple shops

### 5. Real-time Updates with MQTT
**Task**: Implement real-time stock notifications using existing MQTT infrastructure
- **Service**: Import and use existing MqttService class
- **Topic Pattern**: `stock_updates_${shopId}` for shop-specific updates
- **Message Types**: Handle stock_low, stock_update, stock_import_complete, stock_export_complete, stock_waste_recorded
- **Subscription Logic**: Subscribe to topics in relevant components, implement message type switching
- **Publishing**: Publish events after successful stock operations with proper message structure
- **Message Format**: Include type, data, timestamp in mess object with topic specification
- **UI Integration**: Show toast notifications, update stock levels, display alerts based on message types

### 6. Localization (All 4 Languages)
**Task**: Add comprehensive stock management translations to all locale files
- **Files**: Update `locales/vi.json`, `locales/en.json`, `locales/ko.json`, `locales/ru.json`
- **Structure**: Create "StockManagement" object in each file with all required translation keys
- **Key Categories**:
  - Navigation: quan_ly_kho, nhap_kho, xuat_kho, hong_mat, lich_su, bao_cao, ton_kho_hien_tai
  - Actions: ghi_nhan_hang_nhap, ghi_nhan_ban_hang, ghi_nhan_hong_mat, xem_lich_su_kho, thong_ke_bao_cao, xem_ton_kho
  - Form Fields: so_luong, don_vi, gia_nhap, gia_ban, ly_do, ghi_chu
  - Status Messages: thanh_cong, that_bai, khong_du_ton_kho, cap_nhat_thanh_cong
  - Reports: bao_cao_nhap_kho, bao_cao_xuat_kho, bao_cao_hong_mat, tong_ket_kho
- **Languages**: Provide accurate translations for Vietnamese (vi), English (en), Korean (ko), and Russian (ru)
- **Usage**: Reference translations using `$t('StockManagement.key')` pattern throughout components
    "chon_san_pham": "Chọn sản phẩm",
    "nhap_so_luong": "Nhập số lượng",
    "ton_kho_thap": "Tồn kho thấp",
    "cap_nhat_thanh_cong": "Cập nhật thành công",
    "loi_cap_nhat": "Lỗi cập nhật"

### 7. Component Styling (Mobile-First Design)
**Task**: Create SCSS styling following existing project patterns
- **File**: Create `components/myShop/stockManagement/StockManagementStyles.scss`
- **Mobile-First**: Start with mobile layout, use media queries for larger screens
- **Grid Layout**: 2-column grid on mobile, 3-column on tablet+, responsive gap spacing
- **Card Design**: White background, no border, border-radius: 8px, drop shadow
- **Colors**: High-contrast gradients matching existing app theme (primary to secondary color transitions)
- **Touch Targets**: Minimum 44px height for buttons, adequate padding for finger taps
- **Icons**: 24px size for main action icons, proper alignment and spacing
- **Typography**: Follow existing font-size patterns (16px primary, 14px secondary, 12px labels)
- **Spacing**: Use consistent 15px padding, 20px for larger sections
- **States**: Hover effects on desktop, active states for mobile touches
### 8. Route Configuration
**Task**: Add stock management routes to the application routing system
- **File**: Update `~/assets/appRoute.ts` with new route constants
- **My Shop Routes**: StockManagementComponent, StockImportComponent, StockExportComponent, StockWasteComponent, StockReportsComponent, StockHistoryComponent
- **Agent Routes**: AgentStockManagementComponent, AgentStockImportComponent, AgentStockExportComponent, AgentStockWasteComponent, AgentStockReportsComponent
- **Pattern**: Follow existing route naming conventions and path structures
- **Parameters**: Include dynamic parameters for shop ID in agent routes (`:id` placeholder)
- **Base Paths**: `/my-shop/stock/*` for shop owners, `/agent/shop/:id/stock/*` for agents

### 9. Implementation Priority

**Phase 1: Core Setup**
1. Create StockService following existing patterns
2. Add API endpoints to AppConst
3. Create main dashboard component with icon-based navigation
4. Add stock management access to MyShop and Agent shop views

**Phase 2: Basic Operations**
1. Implement stock import functionality
2. Implement stock export functionality  
3. Implement waste recording
4. Add basic form validation

**Phase 3: Advanced Features**
1. Add stock history viewing
2. Implement reports and analytics
3. Add real-time MQTT notifications
4. Implement low stock alerts

**Phase 4: Enhancement**
1. Add photo documentation
2. Implement bulk operations
3. Add export capabilities (PDF/Excel)
4. Performance optimization

### 10. Mobile App Integration

Since this is primarily a mobile app, ensure:
- Touch-friendly interface elements (minimum 44px touch targets)
- Swipe gestures for navigation
- Pull-to-refresh functionality
- Optimized for one-handed use
- Fast loading with skeleton screens
- Offline capability for critical operations

### 11. Technical Considerations

- **Authentication**: Uses existing JWT token system via BaseHTTPService
- **Error Handling**: Follows existing patterns with toast notifications
- **State Management**: Local component state with reactive updates via MQTT
- **Caching**: Leverage existing caching mechanisms
- **Validation**: Client-side validation with server-side verification
- **Accessibility**: WCAG compliance with screen reader support

## Expected Deliverables

1. **Complete stock management interface** with mobile-first design
2. **All CRUD operations** for stock import, export, and waste
3. **Real-time notifications** via MQTT integration  
4. **Comprehensive reporting** with visual analytics
5. **Multi-language support** (vi, en, ko, ru)
6. **Mobile-optimized UI** with icon-based navigation
7. **Agent shop integration** for multi-shop management
8. **Proper error handling** and user feedback
9. **Documentation** for components and usage