.cart-overview-overlay-container {
    overflow: hidden;
    z-index: 1001 !important;
    max-width: 100% !important;

    @keyframes slide-up {
        0% {
            bottom: -50%;
        }

        100% {
            bottom: 0;
        }
    }
    
    @media screen and (min-width: 1025px) {
        display: none;
    }

    & .cart-overview-content-container {
        background: #f5f6fa;
        position: absolute;
        bottom: 0;
        // left: 0;
        left: 50%;
        width: 100%;
        height: fit-content;
        border-radius: 20px 20px 0 0;
        animation: slide-up 0.5s ease;
        max-width: var(--max-width-content-view-1024);
        transform: translateX(-50%);
        overflow: auto;
        max-height: 95dvh;
        display: flex;
        flex-direction: column;


        & .noti-label {
            font-size: 20px;
            font-weight: 700;
            color: #545454;
            top: 0;
            position: sticky;
            background: inherit;
            padding: 5px;
            z-index: 10;
            text-align: center;
            height: 50px;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: thin solid #f5f5f5;

            &>.close-noti {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                justify-content: center;
                animation: none;
                width: 40px;
                height: 40px;
            }

            &>.refresh-noti {
                position: absolute;
                right: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 50%;
                transform: translateY(-50%);
                color: var(--primary-color-1);
                animation: none;
                width: 40px;
                height: 40px;
            }
        }
    }
}

.cart-overview-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &>.cart-overview-header {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px 20px;
        background-color: white;
        border-bottom: 1px solid #e0e0e0;

        &>h3 {
            font-size: 20px;
            margin: 0;
            text-align: center;
            justify-content: center;
        }

        &>.close-cart-overview {
            background-color: transparent;
            border: none;
            cursor: pointer;
            font-size: 20px;
            padding: 0;
            color: #333;
            position: absolute;
            right: 5px;
            min-width: unset;
            width: 30px;
            height: 30px;
            border-radius: 50%;

        }
    }

    &>.cart-overview-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        overflow-y: auto;
        min-height: 300px;

        &>.cart-overview-item {
            --cart-item-width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 10px;
            color: black;
            gap: 10px;
            position: relative;
            width: var(--cart-item-width);

            &>img {
                width: 70px;
                height: 70px;
                aspect-ratio: 1;
                border-radius: 5px;
                object-fit: cover;
                background: var(--color-background-2);
            }

            &>.item-cart-detail {
                align-items: flex-start;
                flex: 1;
                overflow: hidden;

                &>.item-cart-product-name {
                    font-weight: 500;
                    color: black;
                    line-height: normal;
                    text-align: left;
                    overflow: hidden;
                    font-size: 15px;
                    flex: 1;

                }

                &>.price-quantity {
                    display: flex;
                    justify-content: space-between;
                    flex: 1;
                    width: 100%;
                    gap: 10px;
                    margin-top: 10px;
                }

                & .product-price {
                    color: var(--primary-color-2);
                    font-weight: 700;
                    font-size: 17px;
                    display: flex;
                    flex-direction: column;
                    line-height: 20px;
                    flex: 1;
                    white-space: nowrap;

                    &>em {
                        color: #a8a7a7;
                        text-decoration: line-through;
                        font-weight: 400;
                        font-size: 13px;
                        line-height: 15px;
                        min-height: 15px;
                        font-style: normal;
                        opacity: 0;
                        text-align: left;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &>em.show {
                        opacity: 1;
                    }
                }

                & .item-cart-quantity {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    width: 100%;
                    align-self: flex-start;
                    font-size: 14px;

                    &>span {
                        margin: 0 10px;
                    }
                }
            }

            &::after {
                content: "";
                height: 1px;
                width: calc(100% - 10px * 2);
                background: #f3f3f3;
                position: absolute;
                bottom: 0;
            }
        }

        &>.empty-cart {
            font-style: italic;
            color: #868686;
            font-size: 15px;
            margin: auto !important;
        }
    }

    &>.cart-overview-footer {
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        border-top: thin solid #e0e0e0;

        & > a {
            flex: 1;
            text-transform: none;
            margin: 0 5px;
            padding: 0;
            display: flex;
        }

        & .cart-overview-button {
            flex: 1;
            text-transform: none;
            margin: 0 5px;
            padding: 0;
            display: flex;

            & .v-btn__overlay{
                width: 100%;
                height: 100%;
            }
        }

        &>.cart-subtotal {
            color: #575757;
            margin-bottom: 5px;
            font-size: 1em;
            justify-content: space-between;
            display: flex;
            width: 100%;

            &>span {
                font-size: 15px;
                color: black;
                line-height: normal;
                font-weight: 700;

                &>em {
                    font-weight: 400;
                    font-style: normal;
                }
            }

            &>.price {
                color: var(--primary-color-2);
                font-weight: 800;
                font-size: 15px;
            }

            &>.none-price {
                color: var(--primary-color-2);
                font-weight: 500;
            }
        }
    }
}