<template>
	<div class="banner-container">
		<img loading="lazy" :src="shop_banner_temp" alt="shop banner" v-if="!img?.length" />
		<div class="banner-origin-container" :class="{ 'none-style': !style?.length }" v-else>
			<!-- <img :src="img" 
				v-on:click="() => {
					imgClick();
				}" 
				loading="lazy"
				:style="{
					transform: style?.length ? style
					: 'none'
				}" 
				:alt="imgTitle ?? 'Banner'" /> -->
			<img :src="img" v-on:click="() => {
				imgClick();
			}" loading="lazy" :alt="imgTitle ?? 'Banner'" />
		</div>
	</div>

</template>

<script lang="ts" setup>
import shop_banner_temp from "~/assets/imageV2/logo-landscape-1024.jpg";

const props = defineProps([
	'imgTitle',
	'imgSrc',
	'imgStyle',
	'width',
	'height',
]);
const emit = defineEmits(['img_click']);

var title = ref(props.imgTitle ? props.imgTitle : "");
var img = ref(props.imgSrc ? props.imgSrc : '');
var style = ref(props.imgStyle ? props.imgStyle : "");

onMounted(() => {
})

onUpdated(() => {
	title.value = props.imgTitle;
	img.value = props.imgSrc;
	style.value = props.imgStyle;
},)

function imgClick() {
	emit('img_click');
}
</script>

<style lang="scss" src="./ShopBannerStyles.scss"></style>