

import { UserService } from "../userService/userService";

export class Authentication {
    // userService = new UserService();
    // isAuth = false;
    // async authCheck() {

    //     let token = await AsyncStorage.getItem(appConst.storageKey.token);

    //     if (token) {
    //         let currentInfo: any;
    //         await AsyncStorage.getItem(appConst.storageKey.userInfo).then(res => {
    //             currentInfo = JSON.parse(res as string)
    //         });
    //         let infoToken = await this.userService.profileInfo();
    //         infoToken = infoToken.status == '200' ? infoToken.data : null;

    //         if (infoToken && currentInfo && infoToken.id == currentInfo.id) {
    //             this.isAuth = true
    //         }
    //         else if (infoToken && currentInfo && infoToken.id != currentInfo.id) {
    //             AsyncStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(infoToken));
    //             this.isAuth = true
    //         }
    //         else {
    //             AsyncStorage.removeItem(appConst.storageKey.userInfo);
    //             AsyncStorage.removeItem(appConst.storageKey.token);
    //             this.isAuth = false
    //         }
    //     }
    //     else {
    //         AsyncStorage.removeItem(appConst.storageKey.userInfo);
    //         AsyncStorage.removeItem(appConst.storageKey.token);
    //         this.isAuth = false;
    //     }
    // }
}