<template>
	<div class="public-container">
		<div class='cart-container'>
			<!-- <HeaderComponent :title="$t('AppRouteTitle.CartComponent')">
				<template v-slot:header_right>
				</template>
</HeaderComponent> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.CartComponent')"></SubHeaderV2Component>
			<div class="v-stack empty-cart" v-if="!refreshing && (!cartData || !cartData.length)">
				<img loading="lazy" :src='none_cart' :placeholder="none_cart" class="empty-cart-avt" />
				<span class="text">
					{{ $t('CartComponent.gio_hang_trong') }}
				</span>
				<button class="action" v-on:click="() => {
					router.push({
						path: appRoute.AroundComponent,
						query: {
							isShowList: 'true'
						}
					})
				}">
					{{ $t('CartComponent.hay_them_san_pham_vao_gio_hang') }}
				</button>
			</div>
			<client-only>
				<div class="cart-info-container" v-if="!refreshing && (cartData && cartData.length)">
					<div class="v-stack cart-list-item-container">
						<div class="cart-overview">
							<div class="check-all" v-on:click="() => {
								selectAll()
							}">
								<Icon
									:name="cartSelectedItems.length < cartData.length ? 'mynaui:square' : 'mynaui:check-square'"
									:class="{ 'checked': cartSelectedItems.length >= cartData.length }"
									v-if="checkCanOrder() == true"></Icon>
								<span>{{ $t('CartComponent.tat_ca_$sl_san_pham', { count: cartData.length }) }}</span>
							</div>
							<button class="clear-all" v-on:click="() => {
								showModalConfirmDeleteCartData = true;
							}">
								<Icon name="solar:trash-bin-trash-linear"></Icon>
							</button>
						</div>
						<div class="line"></div>
						<div class="cart-item-container" v-for="(itemCart, index) in cartData">
							<button class="checkbox" v-if="checkCanOrder() == true" v-on:click="() => {
								selectCartItem(itemCart)
							}">
								<Icon
									:name="checkSelectedItemCart(itemCart.product_id) ? 'mynaui:check-square' : 'mynaui:square'"
									:class="{ 'checked': checkSelectedItemCart(itemCart.product_id) }"></Icon>
							</button>
							<img loading="lazy" :src="(itemCart && itemCart.product?.profile_picture)
								? (domainImage + itemCart.product?.profile_picture)
								: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemCart.product)" />
							<div class='v-stack item-cart-detail'>
								<div class='h-stack item-cart-product-name'
									:title="showTranslateProductName(itemCart.product)">
									<span>
										{{ showTranslateProductName(itemCart.product) }}
									</span>
								</div>
								<span class='product-price'>
									{{
										(itemCart.product?.price_off != null && itemCart.product?.price_off <
											itemCart.product?.price) ?
											formatCurrency(parseFloat(itemCart.product?.price_off),
												itemCart.product?.shop?.currency) : (parseFloat(itemCart.product?.price) == 0 ||
													itemCart.product.price == null) ? $t('CartComponent.gia_lien_he') :
												formatCurrency(parseFloat(itemCart.product?.price),
													itemCart.product.shop?.currency) }} <em class="off"
										:class="{ 'show': (itemCart.product?.price_off != null && itemCart.product?.price_off < itemCart.product?.price) }">
										{{
											formatCurrency(itemCart.product?.price ? parseFloat(itemCart.product?.price) :
												0,
												itemCart.product?.shop?.currency)
										}}
										</em>
								</span>
								<div class='h-stack item-cart-quantity' v-if="checkCanOrder() == true">
									<button class='quantity-button left' title="-1"
										:disabled="(itemCart.quantity <= 0 || undefined)" v-on:click="(e) => {
											e.stopPropagation();
											if (parseFloat((itemCart.quantity - 1).toString()) > 0) {
												let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
												itemCartTemp.quantity = itemCartTemp.quantity - 1 > 0 ? (parseFloat(itemCartTemp.quantity) - 1) : 0;
												itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
												changeCartItem(itemCartTemp, index);
											} else {
												currentCartItemIndex = index;
												showModalConfirmDeleteCartItem = true;
											}
										}">
										<!-- <Icon name="mdi:minus-circle-outline" /> -->
										-
									</button>
									<input type="number" v-model="itemCart.quantity" min="1" class="price-input"
										max=1000000000000 v-on:input="($event: any) => {

											if (itemCart.quantity < 1) {
												itemCart.quantity = 1
											}
											let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
											itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
											changeCartItem(itemCartTemp, index);
										}">
									<button class='quantity-button right' title="+1" v-on:click="(e) => {
										e.stopPropagation();
										let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
										itemCartTemp.quantity = itemCartTemp.quantity ? (parseFloat(itemCartTemp.quantity) + 1) : 1;
										itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
										changeCartItem(itemCartTemp, index);
									}">
										<!-- <Icon name="material-symbols:add-circle-rounded" /> -->
										+
									</button>
									<!-- <button class="delete-item-cart" v-on:click="() => {
									currentCartItemIndex = index;
									showModalConfirmDeleteCartItem = true;
								}">{{ $t('CartComponent.xoa') }}</button> -->
								</div>
								<div class='h-stack item-cart-quantity' v-else>
									x{{ itemCart.quantity }}
								</div>
								<div class="note-input" v-if="checkCanOrder() == true">
									<Icon name="solar:chat-line-linear"></Icon>
									<input autoComplete="off" :placeholder="$t('CartComponent.ghi_chu_cho_san_pham')"
										:maxlength="appConst.max_text_short"
										:title="$t('CartComponent.ghi_chu_cho_san_pham')" :value="itemCart.notes"
										v-on:input="($event: any) => {
											let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
											itemCartTemp.notes = $event.currentTarget.value;
											changeCartItem(itemCartTemp, index);
										}" />
								</div>

							</div>

						</div>
					</div>
					<div class="shop-detail-container">
						<div class="shop-info">
							<AvatarComponent :imgTitle="shopData?.name" :imgStyle="shopData?.logo?.style" :imgSrc="shopData?.logo?.path?.length
								? (domainImage + shopData?.logo?.path)
								: ''" :width="50" :height="50" v-on:img_click="() => {
									router.push(appRoute.DetailShopComponent + '/' + (shopData?.slug ? shopData?.slug : shopData?.id))
								}" class="cart-shop-logo" />
							<div class="shop-name-address">
								<div class="h-stack">
									<div class="v-stack">
										<nuxt-link
											:to="appRoute.DetailShopComponent + '/' + (shopData?.slug ? shopData?.slug : shopData?.id)"
											class="name">
											{{ cartData[0].shop?.name }}
										</nuxt-link>
										<span class="address">
											{{ cartData[0].shop?.address }}
										</span>
									</div>
									<v-btn class="chat-to-shop" variant="tonal" v-on:click="() => {
										chatToShop()
									}">
										<Icon name="humbleicons:chat"></Icon>
										<span>{{ $t('CartComponent.nhan_tin') }}</span>
									</v-btn>
								</div>


								<div class="closed" v-if="checkCanOrder() == false">
									<span>{{ $t('CartComponent.cua_hang_dang_dong_cua') }}</span>
								</div>
							</div>

						</div>


					</div>

				</div>
				<div class="cart-check-container" v-if="cartData.length && checkCanOrder() == true">
					<div class="cart-check-content">
						<div class="total">
							<span>{{ $t('CartComponent.tong_tien') }} <em>({{ $t('CartComponent.mat_hang', {
								count:
									cartSelectedItems.length
							}) }})</em></span>
							<span class="price" v-if="cartSelectedItems.length">{{
								getTotalSelectedItems() ? formatCurrency(getTotalSelectedItems(), shopData?.currency) :
									$t('CartComponent.gia_lien_he')
							}}</span>
							<span v-else class="none-price">
								{{ $t('CartComponent.vui_long_chon_san_pham') }}
							</span>
						</div>
						<button :disabled="!cartSelectedItems.length" v-on:click="() => {
							router.push({
								path: appRoute.OrderComponent,
								state: {
									selectedItemsCart: JSON.stringify(cartSelectedItems)
								}
							})
						}">{{ $t('CartComponent.tiep_theo') }}</button>
					</div>
				</div>
			</client-only>

			<client-only>
				<VueFinalModal class="my-modal-container" content-class="v-stack delete-product-in-cart-modal"
					:overlay-behavior="'persist'" v-model="showModalConfirmDeleteCartItem" v-on:closed="() => {
						showModalConfirmDeleteCartItem = false
					}" contentTransition="vfm-slide-up">
					<div>
						<div class='v-stack'>
							<span class='delete-cart-message'>
								{{ $t('CartComponent.xoa_san_pham') }} {{ " " }}
								<span class='delete-cart-item-name'>
									{{
										currentCartItemIndex != -1
											? showTranslateProductName(cartData[currentCartItemIndex].product)
											: ""
									}}
								</span>
								{{ " " }} {{ $t('CartComponent.khoi_gio_hang') }}
							</span>
						</div>
						<div class='h-stack confirm-modal-buttons'>
							<button class='reject-button' :disabled="isUpdating" v-on:click="() => {
								showModalConfirmDeleteCartItem = false
							}">
								{{ $t('CartComponent.khong') }}
							</button>
							<button class='accept-button' :disabled="isUpdating" v-on:click="() => {
								deleteCartItem(currentCartItemIndex);
							}">
								{{ $t('CartComponent.co') }}
							</button>
						</div>
					</div>
				</VueFinalModal>
				<VueFinalModal class="my-modal-container" content-class="v-stack delete-product-in-cart-modal"
					:overlay-behavior="'persist'" v-model="showModalConfirmDeleteCartData" v-on:closed="() => {
						showModalConfirmDeleteCartData = false
					}" contentTransition="vfm-slide-up">
					<div>
						<div class='v-stack'>
							<span class='delete-cart-message'>
								{{ $t('CartComponent.xoa_tat_ca_san_pham_khoi_gio_hang') }}
							</span>
						</div>
						<div class='h-stack confirm-modal-buttons'>
							<button class='reject-button' :disabled="isUpdating" v-on:click="() => {
								showModalConfirmDeleteCartData = false
							}">
								{{ $t('CartComponent.khong') }}
							</button>
							<button class='accept-button' :disabled="isUpdating" v-on:click="() => {
								clearAll();
								showModalConfirmDeleteCartData = false
							}">
								{{ $t('CartComponent.co') }}
							</button>
						</div>
					</div>
				</VueFinalModal>
			</client-only>

		</div>
	</div>

	<v-overlay v-model="showChatToShop" :z-index="100" :absolute="false" :close-on-back="true" contained
		key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
		no-click-animation v-on:click:outside="() => {
			showChatToShop = false;
		}">
		<ChatDetailComponent v-if="showChatToShop" :mode="member_type.user" :receiver_id="shopData?.id"
			:chat_info="chatToShopInfo" :receiver_type="true" v-on:close="() => {
				showChatToShop = false;
			}"></ChatDetailComponent>
	</v-overlay>

</template>


<script lang="ts" setup>

import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";

import { appConst, appDataStartup, domain, domainImage, formatCurrency, showTranslateProductName, showTranslateProductDescription } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import { ShopService } from "~/services/shopService/shopService";
import { HttpStatusCode } from "axios";
import { channel_type, member_type, type ChannelDTO } from "../chatManage/ChatDTO";
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.CartComponent')
});
const nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();

var shopService = new ShopService();

var shopData = ref(null as any);
var cartData = ref([] as CartDto[]);
var cartSelectedItems = ref([] as CartDto[]);
var refreshing = ref(true);
var showModalConfirmDeleteCartItem = ref(false);
var showModalConfirmDeleteCartData = ref(false)
var currentCartItemIndex = ref(-1);
var isUpdating = ref(false);

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();

onBeforeMount(async () => {
	document.title = t('AppRouteTitle.CartComponent');
	refreshing.value = true;
	try {
		let storage = await localStorage.getItem(appConst.storageKey.cart);
		if (storage) {
			cartData.value = JSON.parse(storage as string)
			if (cartData.value.length) getShopDetail();
			if (!cartSelectedItems.value?.length) selectAll();
		}
	}
	finally {
		refreshing.value = false;
	}

	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		// selectAll();
	})
})

function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

async function changeCartItem(cartItem: CartDto, index: number) {
	let cartArr = JSON.parse(JSON.stringify(cartData.value));
	cartArr[index] = JSON.parse(JSON.stringify(cartItem));
	cartData.value = await JSON.parse(JSON.stringify(cartArr));
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));

	let indexSelect = cartSelectedItems.value.findIndex(function (e) {
		return e.product_id == cartItem.product_id
	})
	if (indexSelect != -1) {
		cartSelectedItems.value[indexSelect] = JSON.parse(JSON.stringify(cartItem));
	}

	nuxtApp.$emit('cart_change');
}

function getCartTotalPrice() {
	if (!cartData.value.length) return 0;
	let indexPriceNull = cartData.value.findIndex(e => {
		return (e.price == 0 || e.price == null)
	})
	if (indexPriceNull != -1) return null;
	let total = 0;
	return cartData.value
		.reduce((total: number, current: any) =>
			total + (current.price ? parseFloat(current.price.toString()) : 0), 0
		)
}
function getShopDetail() {
	shopService.detailShop(cartData.value[0].shop_id).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));
		}
		else {
			shopData.value = JSON.parse(JSON.stringify(cartData.value[0].shop))
		}
		console.log(shopData.value.logo.path);
	}).catch(err => {
		console.log(err);
	})
}
async function deleteCartItem(index: number) {
	let list = JSON.parse(JSON.stringify(cartData.value));
	list.splice(index, 1);

	let indexSelect = cartSelectedItems.value.findIndex(function (e) {
		return e.product_id == cartData.value[index].product_id
	})
	if (index != -1) {
		cartSelectedItems.value.splice(indexSelect, 1);
	}
	showModalConfirmDeleteCartItem.value = false;
	cartData.value = JSON.parse(JSON.stringify(list));
	currentCartItemIndex.value = -1;
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value))
	nuxtApp.$emit('cart_change');
}
function selectCartItem(cartItemSelect: CartDto) {
	let index = cartSelectedItems.value.findIndex(function (e) {
		return e.product_id == cartItemSelect.product_id
	})
	if (index == -1) {
		cartSelectedItems.value.push(cartItemSelect);
	}
	else {
		cartSelectedItems.value.splice(index, 1);
	}
}
function selectAll() {
	if (cartData.value.length && cartSelectedItems.value.length < cartData.value.length) {
		cartSelectedItems.value = JSON.parse(JSON.stringify(cartData.value));
	}
	else cartSelectedItems.value = [];

}

function clearAll() {
	cartSelectedItems.value = [];
	cartData.value = [];
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));
	nuxtApp.$emit('cart_change');
}

function checkSelectedItemCart(itemCartId: string) {
	let index = cartSelectedItems.value.findIndex(function (e) {
		return e.product_id == itemCartId
	})
	return index != -1 ? true : false;
}

function getTotalSelectedItems() {
	if (!cartSelectedItems.value.length) return 0;
	let indexPriceNull = cartSelectedItems.value.findIndex(e => {
		return (e.price == 0 || e.price == null)
	})
	if (indexPriceNull != -1) return null;
	return cartSelectedItems.value
		.reduce((total: number, current: any) =>
			total + (current.price ? parseFloat(current.price.toString()) : 0), 0
		)
}
function checkCanOrder() {
	console.log(shopData.value?.settings?.general?.is_open?.value)
	return shopData.value?.settings?.general?.is_open?.value == false ? false : true
}

function chatToShop() {
	chatToShopInfo.value = {
		members: [{
			member_id: shopData.value.id,
			member: JSON.parse(JSON.stringify(shopData.value)),
			member_type: member_type.shop
		}],
		name: null,
		type: channel_type.user,
		avatar: shopData.value.logo
	};
	showChatToShop.value = true;
}
</script>

<style lang="scss" src="./CartStyles.scss"></style>