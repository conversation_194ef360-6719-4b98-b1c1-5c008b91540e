import axios from "axios";

import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";


export class ShopService extends BaseHTTPService {
    myShopCancelToken: any;
    createShopCancelToken: any;
    myShop(){
        if (typeof (this.myShopCancelToken) != typeof undefined) {
            this.myShopCancelToken.cancel()
        }

        this.myShopCancelToken = axios.CancelToken.source();

        let url = appConst.apiURL.myShop;
        return this.https('GET', url, null, this.myShopCancelToken.token, true);
    }

    detailShop(detailShopId: any){
        let url = appConst.apiURL.detailShop + '/' + detailShopId;
        return this.https('GET', url);
    }

    superMenuShop(detailShopId: any){
        let url = appConst.apiURL.superMenuShop + '/' + detailShopId;
        return this.https('GET', url);
    }

    list(offset = 0, limit = 20, all = false){
        let url = appConst.apiURL.shopList + '?offset=' + offset +'&limit=' + limit + '&all=' + all;
        return this.https('GET', url);
    }

    create(body:any){
        if (typeof (this.createShopCancelToken) != typeof undefined) {
            this.createShopCancelToken.cancel()
        }
        this.createShopCancelToken = axios.CancelToken.source();
        return this.https('POST', appConst.apiURL.createShop, body, this.createShopCancelToken.token, true);
    }

    edit(body:any){
        return this.https('POST', appConst.apiURL.editShop, body, null, true);
    }
    
    productByShopId(shop_id:any, offset = 0, limit = 20, sort_by = 1){
        let body = {
            shop_id: shop_id,
            offset: offset,
            limit: limit,
            sortBy: sort_by
        }

        return this.https('POST', appConst.apiURL.getProductsByShopId, body);
    }

    searchProductsInShop(search:string, shop_id:any, category_ids:any[] = [], limit:any = 50, offset:any = 0, sort_by:any = 3, product_type:any = null, is_owner = false, is_saleoff = false){
        let body = {
            shop_id: shop_id,
            search: search,
            category_ids: category_ids,
            limit: limit,
            offset: offset,
            sort_by: sort_by,
            product_type: product_type,
            is_owner: is_owner,
            is_saleoff: is_saleoff
        }

        return this.https('POST', appConst.apiURL.searchProductsInShop, body);
    }

    searchProductsInShopClient(search:string, shop_id:any, category_ids:any[] = [], limit:any = 50, offset:any = 0, sort_by:any = 3, product_type:any = null, is_owner = false, is_saleoff = false, is_feature = false){
        let body = {
            shop_id: shop_id,
            search: search,
            category_ids: category_ids,
            limit: limit,
            offset: offset,
            sort_by: sort_by,
            product_type: product_type,
            is_owner: is_owner,
            is_saleoff: is_saleoff,
            is_feature: is_feature
        }

        return this.https('POST', appConst.apiURL.searchProductsInShopClient, body);
    }

    getProductsWithCategory(shop_id:string){
        let url = appConst.apiURL.getProductWithCategoryInShop.replaceAll(':shop_id', shop_id)

        return this.https('GET', url);
    }

    updateShopSettings(body:any){
        return this.https('POST', appConst.apiURL.updateShopSettings, body, null, true);
    }
}