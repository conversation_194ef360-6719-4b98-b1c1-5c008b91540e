.advertisments {
  min-height: 200px;
  max-width: var(--max-width-content-view-1320);
  width: 100%;
  aspect-ratio: 2/1;
  margin: auto;

  & .my-carousel.my-adv-carousel {
    max-width: var(--max-width-content-view-1320);

    & img {
      width: 100%;
      // max-width: 720px;
      max-height: unset;
      object-fit: cover;
    }
  }
  & .my-adv-carousel-pagination {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    margin-left: 10px;
    margin-bottom: 10px;
    align-items: center;
    display: flex;

    &>.swiper-pagination-bullet {
      background: white;
      width: 10px;
      height: 10px;
      opacity: 1;
      border-radius: 50%;
    }

    &>.swiper-pagination-bullet[class*="active"] {
      background: var(--primary-color-1);
    }
  }
}

@media screen and (max-width: 500px) {

  .home-v2-container .advertisments {
    height: 50dvw !important;
    min-height: unset !important;
    aspect-ratio: unset !important;
  }

  .home-v2-container .my-carousel.my-adv-carousel {
    & img {
      height: 50dvw !important;
    }
  }
}