<template>
	<div class='edit-category-container'>
		<!-- <div class='title-header'>
			<div class="header-left">
			</div>
			<h3>{{ appRouteTitle.EditCategoryComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
		<HeaderComponent :title="$t('AppRouteTitle.EditCategoryComponent')">
			<template v-slot:header_left>

			</template>
		</HeaderComponent>
		<div class='v-stack'>

			<div class="v-stack category-image">
				<span class="optional label">{{ $t('EditCategoryComponent.anh_dai_dien') }}</span>
				<div class="image-container">
					<img class="selected-banner" :src="banner" alt="" v-if="banner" />
					<img :src="no_image" :alt="$t('EditCategoryComponent.chua_chon_anh')" v-if="!banner" />
				</div>
				<div class="image-actions">
					<button class="select-image-button">
						{{ $t('EditCategoryComponent.doi_anh') }}
						<input type="file" accept="image/*" :multiple="false" v-on:change="($event: any) => {
							fileChangeBanner($event);
						}" ref="bannerFileName" />
					</button>
					<button class="delete-image-button" v-if="banner" :disabled="banner && banner.length ? false : true"
						v-on:click="() => {
							banner = '';
							bannerFileName.value = null;
							orientationBanner = 0;
						}">
						{{ $t('EditCategoryComponent.xoa_anh') }}
					</button>
				</div>
			</div>

			<span class='required label'>
				{{ $t('EditCategoryComponent.ten_danh_muc') }}
			</span>
			<div class="language-options">
				<button class="lang-button" v-for="item in shopLanguage" :class="{ 'active': selectLanguage == item }"
					v-on:click="() => {
						selectLanguage = item;
					}">
					{{ ISO6391.getNativeName(item) }}
				</button>
			</div>
			<input v-for="item in shopLanguage" v-show="item == selectLanguage"
				:title="$t('EditCategoryComponent.ten_danh_muc')" name='category-name' :disabled="isSaving"
				class='input-custom' :maxlength="appConst.max_text_short"
				:placeholder="`${$t('EditCategoryComponent.ten_danh_muc')} - ${ISO6391.getName(item)}`"
				:value="getNameTranslate(selectLanguage) || ''" v-on:input="async ($event: any) => {
					await nameCategoryChange($event.target.value, selectLanguage);
					validateName()
				}" />
			<!-- <input :title="$t(' EditCategoryComponent.ten_danh_muc')" name='category-name' class='input-custom'
			:maxlength="appConst.max_text_short"
				:placeholder="$t('EditCategoryComponent.ten_danh_muc')" :value="categoryData?.name" v-on:input="($event: any) => {
					categoryData.name = $event.currentTarget.value;
					if (!categoryData.name || !categoryData.name.length) {
						nameErr = $t('EditCategoryComponent.vui_long_nhap_ten_danh_muc');
					} else { nameErr = '' }
				}" v-on:blur="() => {
			if (!categoryData.name || !categoryData.name.length) {
				nameErr = $t('EditCategoryComponent.vui_long_nhap_ten_danh_muc');
			} else { nameErr = '' }
		}" /> -->
			<span class='error-message'>{{ nameErr }}</span>
		</div>
		<div class="v-stack">
			<span class='label'>
				{{ $t('EditCategoryComponent.danh_muc_cha') }}
			</span>
			<v-autocomplete :custom-filter="(item: any, queryText: any, itemObj: any) => {
				let name = nonAccentVietnamese(itemObj.value.name).toLocaleLowerCase();
				let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
				return name.includes(query)
			}" class="custom-v-select mt-2 category-select dropdown-select-container"
				:placeholder="$t('EditCategoryComponent.chon_danh_muc_cha')" v-model="parent_category_obj"
				:menu-icon="''" :items="dataCategories" variant="plain">
				<template v-slot:selection="{ item, index }">
					<span>{{ showTranslateProductName(item.value) }}</span>

				</template>
				<template v-slot:item="{ props, item }">
					<v-list-item v-bind="props" :title="showTranslateProductName(item.value)"
						v-show="item.value.id != categoryData.id"></v-list-item>
				</template>
				<template v-slot:no-data>
					<v-list-item :title="$t('EditCategoryComponent.khong_tim_thay')"></v-list-item>
				</template>
				<template v-slot:append-inner>
					<Icon name="mdi:chevron-down"></Icon>
				</template>
			</v-autocomplete>
			<!-- <USelectMenu class="dropdown-select-container category-select" searchable
				:searchable-placeholder="$t('EditCategoryComponent.tim_danh_muc')" :search-attributes="['name']"
				variant="none" :options="dataCategories" :placeholder="$t('EditCategoryComponent.danh_muc')"
				v-model="categoryData.parent_id" v-on:change="(newValue: any) => {
				}" value-attribute="id" option-attribute="name">
				<template #label>
					<span class="truncate">
						{{categoryData.parent_id ? (dataCategories[dataCategories.findIndex((e: any) => {
							return e.id == categoryData.parent_id
						})]?.name) : $t('EditCategoryComponent.chon_danh_muc_cha')}}</span>
				</template>
				<template #option="{ option: categoryOption }">
					<span class="truncate">{{ categoryOption.name }}</span>
				</template>
				<template #empty>
					{{ $t('EditCategoryComponent.chua_co_danh_muc_duoc_luu') }}
				</template>
				<template #option-empty="{ query }">
					<span class="search-category-empty">
						{{ $t('EditCategoryComponent.khong_tim_thay') }} <q>{{ query }}</q>
					</span>
				</template>
			</USelectMenu> -->
		</div>

		<div class='h-stack action-buttons'>
			<button class='cancel-button' :disabled=isSaving v-on:click="() => {
				close()
			}">
				{{ $t('EditCategoryComponent.thoat') }}
			</button>
			<button class='save-button' :disabled=isSaving v-on:click="() => {
				editCategory();
			}">
				{{ $t('EditCategoryComponent.luu') }}
			</button>
		</div>


	</div>
</template>

<script lang="ts" setup>
import ISO6391 from 'iso-639-1';
import axios, { HttpStatusCode } from 'axios';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, nonAccentVietnamese, showTranslateProductName } from '~/assets/AppConst';
import no_image from '~/assets/image/no-image.webp'
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import exifr from "exifr";
import { Buffer } from "buffer";

var router = useRouter();
var route = useRoute();
var props = defineProps({
	categoryData: "" as any,
	dataCategories: {},
	shop_data: null
})

const { t, locale } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.EditCategoryComponent')
})
var emit = defineEmits(['close']);
var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var imageService = new ImageService();
var shopService = new ShopService();
var categoryService = new CategoryService();

var categoryData = ref(props.categoryData ? props.categoryData : null);
var nameErr = ref("");
var parent_category_obj = ref<any>(null)
var parentIdErr = ref("");
var dataCategories = ref((props.dataCategories ? props.dataCategories : []) as any);
var isSaving = ref(false);

var shopLanguage = ref<string[]>([])
var selectLanguage = ref(null as any);

var banner = ref("");
var bannerFileName = ref(null as any);
var orientationBanner = ref(0);
var isBannerChange = ref(false);
onMounted(() => {
	if (!dataCategories.value?.length) {
		getListCategory()
	}

	shopLanguage.value = props.shop_data.language.at(0) != '[' ? props.shop_data.language : JSON.parse(props.shop_data.language);
	if (shopLanguage.value.indexOf(appConst.defaultLanguage) == -1) {
		shopLanguage.value.splice(0, 0, appConst.defaultLanguage);
	}
	if (shopLanguage.value.indexOf('en') == -1) {
		shopLanguage.value.splice(shopLanguage.value.indexOf(appConst.defaultLanguage) + 1, 0, 'en');
	}

	selectLanguage.value = shopLanguage.value[shopLanguage.value.indexOf(locale.value)] ?? shopLanguage.value[0] ?? appConst.defaultLanguage;
	console.log(categoryData.value,);

	let indexParentId = dataCategories.value.findIndex(function (e: any) {
		return e.id == categoryData.value?.parent_id;
	})

	if (indexParentId != -1) {

	}

	parent_category_obj.value = JSON.parse(JSON.stringify(categoryData.value?.parent_category));

	if (categoryData.value?.profile_picture) {
		convertBannerImageToBase64(domainImage + categoryData.value?.profile_picture);
	}
})
function getListCategory() {
	categoryService.getCategoryByShopId(categoryData.value.shop_id).then(res => {
		dataCategories.value = JSON.parse(JSON.stringify(res.body.data))
	})
}
function editCategory() {
	isSaving.value = true;
	categoryService.editCategory({ id: categoryData.value.id, name: categoryData.value.name, parent_id: parent_category_obj.value?.id ?? null, translation: categoryData.value.translation }).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			if (isBannerChange.value) {
				try {
					if (banner.value && banner.value.length) {
						let indexFirstName = categoryData.value?.translation?.findIndex(function (el: any) {
							return el.name?.length > 0
						})
						let banner$ = {
							path: banner.value,
							object_type: appConst.object_type.category,
							image_type: 'banner',
							title: categoryData.value?.translation[indexFirstName]?.name ?? categoryData.value?.name,
							description: categoryData.value?.translation[indexFirstName]?.name ?? categoryData.value?.name,
							index: 0,
							orientation: orientationBanner.value,
							parent_id: res.body.data.id,
						}
						await imageService.insertImage(banner$).then(res => {
							if (res.status == HttpStatusCode.Ok) { }
							else {
								toast.error(t('EditCategoryComponent.luu_anh_that_bai'));
							}
							// isEditingShop.value = false;
						});
					}
				}
				finally {
					toast.success(t('EditCategoryComponent.cap_nhat_danh_muc_thanh_cong'));
					setTimeout(() => {
						close(true);
					}, 1500)
				}
			}
			else {
				toast.success(t('EditCategoryComponent.cap_nhat_danh_muc_thanh_cong'));
				// setTimeout(() => {
					close(true);
				// }, 1500)
			}
		}
		else {
			toast.error(t('EditCategoryComponent.cap_nhat_danh_muc_that_bai'));
			isSaving.value = false;
		}

	}).catch(err => {
		console.log(err);
		toast.error(t('EditCategoryComponent.cap_nhat_danh_muc_that_bai'));
		isSaving.value = false;
	})
}

function close(updated = false) {
	emit('close', updated)
}

function getNameTranslate(language: string) {
	let indexTranslate = categoryData.value.translation?.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? categoryData.value.translation?.[indexTranslate].name : '';
}

function nameCategoryChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.name = text;
	// 	if (!productData.value.name || !productData.value.name.length) {
	// 		nameErr.value = 'Vui lòng nhập tên Tiếng Việt của sản phẩm';
	// 	} else {
	// 		nameErr.value = '';
	// 	}
	// }

	let indexTranslate = categoryData.value.translation?.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		categoryData.value.translation[indexTranslate].name = text;
	}
	else {
		categoryData.value.translation?.push({
			language_code: translation,
			name: text
		})
	}
}
function validateName() {
	let indexVietnamese = categoryData.value?.translation?.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && categoryData.value?.translation?.[indexVietnamese]?.name?.length > 0) {
		nameErr.value = "";
		categoryData.value.name = categoryData.value?.translation?.[indexVietnamese].name;
		return true
	}

	let indexFirstName = categoryData.value?.translation?.findIndex(function (el: any) {
		return el.name?.length > 0
	})
	if (indexFirstName != -1) {
		nameErr.value = "";
		categoryData.value.name = categoryData.value?.translation?.[indexFirstName].name;
		return true
	}
	nameErr.value = t('EditCategoryComponent.vui_long_nhap_ten_danh_muc');
	return false;
}

async function fileChangeBanner(fileInput: any) {
	if (fileInput.target.files.length) {
		if (fileInput.target.files[0].size > appConst.image_size.max) {
			let imgErr = t('EditCategoryComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
			toast.error(imgErr);
		}
		else {
			bannerFileName.value = fileInput.target.files;

			const reader = new FileReader();
			reader.onload = async (e: any) => {
				let imageFile = e.target;
				const image = new Image();

				image.src = e.target.result;
				banner.value = e.target.result;
				isBannerChange.value = true;
				let orientationExif;
				if (fileInput.target.files[0].type != 'image/webp') {
					orientationExif = await exifr.orientation(image) || 0;
				}
				else orientationExif = 0;
				orientationBanner.value = orientationExif ? orientationExif : 0;

				bannerFileName.value = fileInput.target.files[0].name;
			};
			await reader.readAsDataURL(fileInput.target.files[0]);
		}
	}
}

function convertBannerImageToBase64(imgUrl: string) {
	axios
		.get(imgUrl, {
			responseType: "arraybuffer",
		})
		.then((response) => {
			const buffer = Buffer.from(response.data, "base64").toString("base64");
			banner.value = "data:image/webp" + ";base64," + buffer;
		})
		.catch((ex) => {
			console.error(ex);
		});
}

</script>

<style lang="scss" src="./EditCategoryStyles.scss"></style>