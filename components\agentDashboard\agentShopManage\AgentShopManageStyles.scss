.agent-shop-manage-container {
  width: 100%;
  min-height: 100%;
  height: fit-content !important;
  flex: 1 1;
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: auto;

  // > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;

  //   & > .header-left {
  //     margin-right: auto;
  //   }
  // }

  & > .agent-shop-manage-content {
    padding: 10px;
    background: white;
    gap: 10px;
    display: flex;
    flex-direction: column;
    flex: 1;
    // overflow: auto;
    position: relative;

    & > .empty-shop-list, > .search-shop-empty {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding-top: 75px;
      gap: 15px;

      & > img {
        width: 200px;
        height: 200px;
        object-fit: contain;
        position: relative;
      }

      & > span {
        font-size: 1.4em;
        text-align: center;
      }

      & > button {
        color: white;
        font-size: 1.2em;
        padding: 5px 20px;
        background: var(--primary-color-1);
        border-radius: 10px;
        width: fit-content;
      }
    }

    & > .agent-shop-manage-list{
      box-shadow: none;
      gap: 5px;

      & > .item-shop-accordion{
        height: fit-content;
        gap: 5px;
        border-top-width: 0px !important;
        padding: 10px 0;

        & > .v-expansion-panel__shadow{
          display: none;
        }
        & > .item-shop-info:hover{
          background: transparent;
        }
        & > .item-shop-info{
          display: flex;
          padding: 5px;
          width: 100%;
          animation: none !important;

          & > .item-shop-logo{
            width: 100px;
            height: 100px;
            margin-right: 10px;
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            box-shadow: 0px 4px 8px rgb(0,0,0,.1);
            object-fit: cover;
            overflow: hidden;

            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            & > .logo-origin-container{
              transform: scale(calc(100/var(--scale-origin)));
            }
          }

          & > .item-shop-detail{
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
            align-items: flex-start;
            text-align: left;
            font-size: 1.2em;

            & > .name{
              color: var(--primary-color-1);
              font-weight: 600;
              width: 100%;
            }

            & > .address{
              font-size: .9em;
              color: var(--color-text-note);
              font-weight: 600;
              width: 100%;
            }

            & > .owner{
              display: flex;
              gap: 5px;
              align-items: center;
              color: var(--primary-color-1);
              font-size: .9em;
              font-weight: 600;
              
            }            & > .waiting-confirm{
              display: flex;
              gap: 5px;
              align-items: center;
              color: color-mix(in srgb, #0073c5 80%, transparent);
              font-size: 13px;
              font-weight: 700;
              font-style: italic;
            }

            & > .shop-status{
              display: inline-flex;
              align-items: center;
              gap: 5px;
              font-size: 12px;
              font-weight: 700;
              padding: 4px 12px;
              border-radius: 20px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              position: relative;
              overflow: hidden;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: inherit;
                border-radius: inherit;
                opacity: 0.1;
                z-index: -1;
              }

              & > span {
                position: relative;
                z-index: 1;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
              }

              // Open shop styles (when is_open = true && enable = true)
              &.open {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: 2px solid #10b981;
                animation: pulse-green 2s infinite;

                &::after {
                  content: '●';
                  position: absolute;
                  right: 8px;
                  top: 50%;
                  transform: translateY(-50%);
                  color: #d1fae5;
                  font-size: 8px;
                  animation: blink-green 1.5s infinite;
                }

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
                }
              }

              // Closed shop styles (when is_open = false || enable = false)
              &.closed {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: 2px solid #ef4444;

                &::after {
                  content: '●';
                  position: absolute;
                  right: 8px;
                  top: 50%;
                  transform: translateY(-50%);
                  color: #fecaca;
                  font-size: 8px;
                }

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
                }
              }

              // Animation for open status
              @keyframes pulse-green {
                0%, 100% {
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(16, 185, 129, 0.7);
                }
                50% {
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 4px rgba(16, 185, 129, 0);
                }
              }

              @keyframes blink-green {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
              }

              // Responsive design
              @media (max-width: 768px) {
                font-size: 11px;
                padding: 3px 10px;
              }
            }
            
            & > .disabled{
              color: white;
              background: linear-gradient(to right, var(--primary-color-2) 50%, transparent);
              font-weight: 700;
              font-style: italic;
              font-size: 15px;
              border-radius: 2em;
              padding: 2px 40px 2px 10px;
            }

            & > .owner-tag{
              color: var(--primary-color-2);
              font-weight: 700;
              font-style: italic;
              font-size: 17px;
            }
          }
        }

        & .item-shop-action{
          display: flex;
          justify-content: space-between;

          & > div{
            display: flex;
            justify-content: space-between;
            padding: 5px 0 0 0;
          }
          & button:first-child, a:first-child{
            border-radius: 5px 0 0 5px ;
          }
          & button:last-child, a:last-child{
            border-radius: 0 5px 5px  0;
          }
          & button, a{
            flex: 1;
            color: white;
            padding: 5px 0;
            font-size: 1.2em;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            cursor: pointer;

            & > span{
              white-space: nowrap;
              font-size: 13px;
            }
          }          
          & .view{
            background: var(--primary-color-1);
          }
          & .duplicate{
            background: #337bff;
          }
          & .edit{
            background: var(--color-text-note);
          }
          & .config{
            background: #28a745;
          }
          & .delete{
            background: var(--primary-color-2);
          }
        }
        
      }
    }

    & > .search-bar-container {
      align-items: center;
      position: relative;
      z-index: 99;
      margin-bottom: 5px;
      align-items: center;
      width: 100%;
      color: var(--primary-color-1);
      background-color: #f5f6fa;
      
    }
    & .search-input-container {
      width: 100%;
      background-color: #f5f6fa;
      border-radius: 0 !important;
      border-color: transparent;
      outline: none;
      height: 40px;
      font-size: var(--font-size);
    }
  
    & .search-button {
      display: flex;
      align-items: center;
      background: #f5f6fa;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 10px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & > .loading{
      width: 100%;
      text-align: center;
    }
  }
  & > .agent-create-shop-button{
    padding: 10px 0;
    background: var(--primary-color-1);
    color: white;
    display: flex;
    justify-content: center;
    gap: 10px;
    align-items: center;
    font-weight: bold;
    font-size: 1.1em;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

.agent-delete-shop-container {
  max-height: 95dvh;
    // height: 95dvh;
    width: 500px;
    // max-height: 90%;
    max-width: 95%;
    // min-height: 40dvh;
    border-radius: 10px;
    padding: 10px;
    background-color: white;

  & .agent-delete-shop-content {
    font-size: 20px;
    flex: 1;
    text-align: center;
  }

  & .agent-delete-shop-title {
    font-size: 1em;
    font-weight: bold;
    display: flex;
    flex-direction: column;
    flex: 1;
   
    & > span{
      color: var(--primary-color-1);
    }
  }

  & .agent-delete-shop-message {
    font-size: 1em;
    text-align: center;
  }

  & .agent-delete-shop-message > .shop-name {
    color: var(--primary-color-2);
    font-weight: bold;
  }

  & .accept-disable-button{
    color: white;
    width: 40%;
    max-width: 200px;
    height: 35px;
    border: thin solid var(--primary-color-2);
    background-color: var(--primary-color-2);
    border-radius: 2em;
    font-size: 17px;
    font-weight: 500;
  }

  & .reject-disable-button{
    color: #545454;
    width: 40%;
    max-width: 200px;
    height: 35px;
    border: thin solid #545454;
    background-color: white;
    border-radius: 2em;
    font-size: 17px;
    font-weight: 500;
  }
}
