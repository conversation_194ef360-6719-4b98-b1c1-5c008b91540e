<template>
	<div class="suggest-list content-list" id="hang-tuyen">
		<div class="stack-content-title">
			<span class="section-title">
				<Icon name="gis:map-favorite"></Icon>
				<span>{{ $t('HomeV2Component.hang_tuyen_que_minh') }}</span>
			</span>
			<nuxt-link :to="{
				path: appRoute.AroundComponent,
				query: {
					filter: JSON.stringify({
						...props.filter_data,
						is_suggest: true
					})
				}
			}" class="view-all">
				{{ $t('HomeV2Component.xem_tat_ca') }}
				<Icon name="ic:round-chevron-right"></Icon>
			</nuxt-link>
		</div>
		<div class="stack-content-list">

			<!-- <Swiper v-if="listSuggest?.length" class="my-carousel stack-carousel suggest-carousel"
                                :modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
                                :space-between="10" :loop="false" :effect="'creative'" :navigation="true" @init="(e) => {
                                    suggest_el = e
                                }" :freeMode=false :autoplay="false" key="suggest-carousel" ref="suggest_el">
                                <SwiperSlide class="item-stack-slide" v-for="item of listSuggest"
                                    :key="'suggest_' + item.id">
                                    <div class="icon-hang-tuyen">
                                        <span class="icon-1">hàng</span>
                                        <span class="icon-2">tuyển</span>
                                    </div>
                                    <nuxt-link
                                        :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                        class="item-stack" :title="showTranslateProductName(item)">
                                        <img loading="lazy" :src="item && item.profile_picture
                                            ? domainImage + item.profile_picture
                                            : icon_for_product
                                            " :placeholder="icon_for_product" />
                                        <div class="shop-stack-content">
                                            <AvatarComponent class="shop-logo" :imgTitle="item?.shop?.name"
                                                :imgStyle="item?.shop?.logo?.style" :imgSrc="item?.shop?.logo?.path?.length
                                                    ? (domainImage + item?.shop?.logo?.path)
                                                    : ''" :width="40" :height="40" />
                                            <span class="shop-name">
                                                <span>
                                                    {{ item?.shop?.name }}
                                                </span>
                                            </span>
                                        </div>
                                        <div class="item-stack-content">
                                            <span class="name">{{ showTranslateProductName(item) }}</span>

                                            <span class="price">
                                                <em class="off"
                                                    v-if="(item.price_off != null && item.price_off < item.price)">{{
                                                        (parseFloat(item.price) == 0 || item.price == null)
                                                            ? $t('HomeV2Component.gia_lien_he')
                                                            : formatCurrency(parseFloat(item.price), item.shop ?
                                                                item.shop.currency
                                                                : item.currency)
                                                    }}</em>
                                                {{
                                                    (item.price_off != null && item.price_off < item.price) ?
                                                        formatCurrency(parseFloat(item.price_off), item.shop ?
                                                            item.shop.currency : item.currency) : (parseFloat(item.price) == 0 ||
                                                                item.price == null) ? $t('HomeV2Component.gia_lien_he') :
                                                            formatCurrency(parseFloat(item.price), item.shop ?
                                                                item.shop.currency : item.currency) }} </span>
                                        </div>

                                    </nuxt-link>
                                </SwiperSlide>
                            </Swiper> -->
			<div class="stack-content-list-container" v-if="listSuggest?.length">
				<div class="item-stack-slide" v-for="item of listSuggest" :key="'suggest_list_' + item.id"
					:id="'suggest_list_' + item.id">
					<div class="icon-hang-tuyen">
						<span class="icon-1">hàng</span>
						<span class="icon-2">tuyển</span>
					</div>
					<nuxt-link :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
						class="item-stack" :title="showTranslateProductName(item)">
						<img loading="lazy" :src="item && item.profile_picture
							? domainImage + item.profile_picture
							: icon_for_product
							" :placeholder="icon_for_product" />
						<div class="shop-stack-content">
							<AvatarComponent class="shop-logo" :imgTitle="item?.shop?.name"
								:imgStyle="item?.shop?.logo?.style" :imgSrc="item?.shop?.logo?.path?.length
									? (domainImage + item?.shop?.logo?.path)
									: ''" :width="40" :height="40" />
							<span class="shop-name">
								<span>
									{{ item?.shop?.name }}
								</span>
							</span>
						</div>
						<div class="item-stack-content">
							<span class="name">{{ showTranslateProductName(item) }}</span>

							<span class="price">
								<em class="off" v-if="(item.price_off != null && item.price_off < item.price)">{{
									(parseFloat(item.price) == 0 || item.price == null)
										? $t('HomeV2Component.gia_lien_he')
										: formatCurrency(parseFloat(item.price), item.shop ?
											item.shop.currency
											: item.currency)
								}}</em>
								{{
									(item.price_off != null && item.price_off < item.price) ?
										formatCurrency(parseFloat(item.price_off), item.shop ? item.shop.currency :
											item.currency) : (parseFloat(item.price) == 0 || item.price == null) ?
											$t('HomeV2Component.gia_lien_he') : formatCurrency(parseFloat(item.price), item.shop
												? item.shop.currency : item.currency) }} </span>
						</div>

					</nuxt-link>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" src="./SuggestListSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();
const emit = defineEmits(['business_type_click'])
const props = defineProps({
	filter_data: null,
	enable_load: null
})

var suggest_el = useState<any>(() => { return null });
var listSuggest = useState<any>('dashboard_suggest', () => { return [] });
var loadingSuggest = ref(false);

var publicService = new PublicService();

watch(() => [props.enable_load], () => {
	if (props.enable_load) {
		initSuggest()
	}
})

onBeforeMount(() => {

})
onUnmounted(async () => {
});
onMounted(async () => {
	// initSuggest()
});

function initSuggest(){
	let body = {
        section: "suggest", //suggest,sale_off,best_around,hot_deal
        latitude_user: props.filter_data.latitude_user,
        longitude_user: props.filter_data.longitude_user
    }
	if (!listSuggest.value?.length) {
        loadingSuggest.value = true;
        publicService.dashboard({ ...body, section: 'sale_off' }).then(res => {
            if (res.status == HttpStatusCode.Ok) {
                listSuggest.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
            }

            loadingSuggest.value = false;

        });
    }
}
</script>
