<template>
    <div class="public-container">
        <div class='my-order-detail-container'>
            <SubHeaderV2Component :title="$t('AppRouteTitle.MyOrderDetailComponent')">

            </SubHeaderV2Component>
            <p class="loading" v-show="isRefreshing">
                {{ $t('MyOrderDetailComponent.dang_tai') }}
            </p>

            <div class='v-stack' v-if="!isRefreshing && (order && order.id)">
                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="basil:invoice-outline"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.ma_don') }}: {{
                                    order && order.short_code
                                        ? order.short_code
                                        : $t('MyOrderDetailComponent.chua_co_ma')
                                }}
                            </span>
                        </div>
                        <button class="action copy" v-on:click="() => {
                            toast.success($t('MyOrderDetailComponent.da_sao_chep_ma_don'));
                            copyToClipboard(order.short_code)
                        }">
                            {{ $t('MyOrderDetailComponent.sao_chep') }}
                        </button>
                    </div>
                </div>

                <div class="v-stack main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="solar:pen-new-round-outline"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.trang_thai') }}:
                                {{ $t('MyOrderDetailComponent.' +
                                    appConst.order_status[
                                        parseInt(order.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key =>
                                                appConst.order_status[key].value == parseInt(order.status)) as string
                                            : "taken"
                                    ].nameKey
                                ) }}
                            </span>
                        </div>
                        <button class="action"
                            v-if="parseInt(order.status.toString()) >= appConst.order_status.confirmed.value"
                            v-on:click="() => {
                                showFullProcess = !showFullProcess
                            }">
                            {{ $t('MyOrderDetailComponent.mo_rong') }}
                        </button>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.cancel.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.cancel.value">
                            <span class="status-name">{{ $t('MyOrderDetailComponent.tu_choi_don') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.taken.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.taken.value">
                            <span class="status-name">{{ $t('MyOrderDetailComponent.giao_hang_thanh_cong') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.confirmed.value || showFullProcess"
                            v-if="parseInt(order.status.toString()) == appConst.order_status.confirmed.value">
                            <span class="status-name">{{ $t('MyOrderDetailComponent.xac_nhan') }}</span>
                            <span>
                                {{ moment(order.updated_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                        <div class="content-detail"
                            v-show="parseInt(order.status.toString()) == appConst.order_status.waiting.value || showFullProcess">
                            <span class="status-name">{{ $t('MyOrderDetailComponent.dat_hang') }}</span>
                            <span>
                                {{ moment(order.created_at).format("HH:mm, DD/MM/YYYY") }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:stack-hexagon-outline"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.anh_don_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="h-stack primary-content images-content" v-if="order.images?.length">
                        <div class="selected-image" v-for="(itemImg, index) in order.images">
                            <img :src="itemImg.path" class="handle-drag" v-on:click="() => {
                                indexImageActive = index;
                                showImageViewerModal = true;
                            }" />
                        </div>
                    </div>
                    <div class="h-stack primary-content images-content none-images" v-else>
                        <em>
                            {{ $t('MyOrderDetailComponent.cua_hang_chua_cung_cap') }}
                        </em>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:location-away-outline-rounded"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.nguoi_nhan') }}
                            </span>
                        </div>
                        <div class="h-stack action">
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`"
                                :target="webInApp ? '_blank' : ''" class="call-customer">
                                <Icon name="material-symbols:call"></Icon>
                                {{ $t('MyOrderDetailComponent.goi_dien') }}
                            </nuxt-link>
                        </div>
                    </div>
                    <div class="h-stack primary-content">
                        <img loading="lazy" class='content-detail user-avatar' :src="order && order.customer && order.customer.profile_picture
                            ? ((appConst.provider_img_domain.some(e => order.customer?.profile_picture?.includes(e))) ? order.customer?.profile_picture : (domainImage + order.customer?.profile_picture))
                            : non_avatar" :placeholder="non_avatar" alt="" />
                        <div class="content-detail">
                            <div class=" customer-name" v-on:click="()=>{
                                showOrderReceiverInfoModal = true;
                            }">
                                <span>{{ order.customer_name }}</span>
                                <button class="open-info">
                                    ({{ $t('MyOrderDetailComponent.xem_chi_tiet') }})
                                </button>
                            </div>
                            <nuxt-link :to="`tel:${validPhone(order.customer_phone)}`"
                                :target="webInApp ? '_blank' : ''" class="content-detail customer-phone">
                                <span>{{ order.customer_phone }}</span>
                            </nuxt-link>
                            <div class="customer-address" v-on:click="()=>{
                                showOrderReceiverInfoModal = true;
                            }">
                                <span>{{ order.address }}</span>
                            </div>
                            <div class="customer-delivery-time" v-if="order?.delivery_time?.length">
                                <span>{{ $t('MyOrderDetailComponent.muon_nhan_hang_luc') }}: <em>{{
                                    moment(order.delivery_time).format("DD/MM/YYYY - HH:mm") }}</em></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:storefront-outline"></Icon>
                            <span>
                                {{ order.shops.name }}
                            </span>
                        </div>
                        <div class="action">
                            <nuxt-link :to="directionToShop()" target="_blank" class="call-customer">
                                <Icon name="el:map-marker"></Icon>
                                {{ $t('MyOrderDetailComponent.chi_duong') }}
                            </nuxt-link>
                            <nuxt-link :to="`https://zalo.me/${validPhone(order.shops.phone)}`" target="_blank"
                                class="call-customer">
                                <!-- Icon name="material-symbols:call"></Icon> -->
                                <img loading="lazy" :src='logo_zalo' :placeholder="logo_zalo" />
                                {{ $t('MyOrderDetailComponent.zalo') }}
                            </nuxt-link>
                            <nuxt-link :to="`tel:${validPhone(order.shops.phone)}`" :target="webInApp ? '_blank' : ''"
                                class="call-customer">
                                <Icon name="material-symbols:call"></Icon>
                                {{ $t('MyOrderDetailComponent.goi_dien') }}
                            </nuxt-link>
                            <button class="call-customer" v-if="order.customer_id" v-on:click="() => {
                                chatToShop()
                            }">
                                <Icon name="fluent:chat-24-regular"></Icon>
                                {{ $t('MyOrderDetailComponent.nhan_tin') }}
                            </button>
                        </div>
                    </div>
                    <div class="v-stack order-items-container">
                        <div v-for="(itemOrder) in order.items" class='h-stack item-order-container'
                            :key="itemOrder.id">
                            <div class='v-stack item-order-avatar'>
                                <img loading="lazy"
                                    :src="itemOrder.profile_picture ? (domainImage + itemOrder.profile_picture) : icon_for_product"
                                    :placeholder="icon_for_product" alt="" />
                            </div>
                            <div class='v-stack item-order-detail'>
                                <span class='item-order-name'>
                                    {{
                                        itemOrder.parent_id ? (`${showTranslateProductName(itemOrder.parent_product)} -
                                    `) : '' }}{{
                                        showTranslateProductName(itemOrder) }}
                                </span>

                                <div class='h-stack item-order-quantity-price'>
                                    <span>
                                        x{{ parseFloat(itemOrder.pivot.quantity).toString().replaceAll('.', ',') }}
                                    </span>
                                    |
                                    <span class="price">
                                        {{ (itemOrder.pivot.price_off != null && itemOrder.pivot.price_off <
                                            itemOrder.price) ? formatCurrency(parseFloat(itemOrder.pivot.price_off),
                                                order.shops.currency) : (parseFloat(itemOrder.price) == 0 ||
                                                    itemOrder.price == null) ? $t('MyOrderDetailComponent.gia_lien_he') :
                                            formatCurrency(parseFloat(itemOrder.price), order.shops.currency) }} <em
                                            class="off"
                                            v-if="(itemOrder.pivot.price_off != null && itemOrder.pivot.price_off < itemOrder.price)">
                                            {{ formatCurrency(itemOrder.price ? parseFloat(itemOrder.price) : 0,
                                                order.shops.currency) }}
                                            </em>
                                    </span>
                                </div>
                                <span class='item-order-note'>
                                    {{ itemOrder.pivot.notes }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class='main-stack v-stack' v-if="order.notes?.length">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="material-symbols:description-outline"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.ghi_chu_don_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <p class='content-detail'>{{ order.notes }}</p>
                    </div>
                </div>

                <div class="main-stack" v-if="false">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="carbon:wallet"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.hinh_thuc_thanh_toan') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ $t('MyOrderDetailComponent.thanh_toan_khi_nhan_hang') }}</span>
                        </div>
                    </div>
                </div>

                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="bi:basket2-fill"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.hinh_thuc_nhan_hang') }}
                            </span>
                        </div>
                    </div>
                    <div class="v-stack primary-content">
                        <div class="content-detail">
                            <span>{{ order.delivery_type ? $t('MyOrderDetailComponent.tu_toi_lay') :
                                $t('MyOrderDetailComponent.toi_can_giao_tan_noi') }}</span>
                        </div>
                    </div>
                </div>
                <div class="main-stack">
                    <div class='h-stack first-content'>
                        <div class='h-stack title-main-stack'>
                            <Icon name="hugeicons:truck-delivery"></Icon>
                            <span>
                                {{ $t('MyOrderDetailComponent.thong_tin_giao_hang') }}
                            </span>
                        </div>
                    </div>

                    <div class="h-stack primary-content delivery-content" v-if="order.delivery_info">
                        <div class="label-content">
                            {{ $t('MyOrderDetailComponent.chi_tiet_don_giao') }}
                        </div>
                        <div class="delivery-partner-info">
                            <img class="partner-logo" :src="order?.delivery_partner?.information?.logo"
                                v-if="order?.delivery_partner?.information?.logo"
                                :alt="`logo ${order?.delivery_partner?.name}`">
                            <span class="partner-name" v-else>
                                {{ order?.delivery_partner?.name }}
                            </span>
                        </div>
                        <div class="routes">
                            <div class="label">
                                {{ $t('MyShopOrderDetailComponent.lo_trinh') }}:
                            </div>
                            <div class="places-info">
                                <div class="place">
                                    <img :src="start_location_icon" class="icon-left" />
                                    <div class="place-content">
                                        <span>{{ order?.delivery_info?.name_from }} - {{
                                            order?.delivery_info?.phone_from }}</span>
                                        <em>{{ order?.delivery_info?.address_from }}</em>
                                    </div>
                                </div>
                                <div class="distance">

                                    <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon>
                                </div>
                                <div class="place">
                                    <img :src="destination_location_icon" class="icon-left" />
                                    <div class="place-content">
                                        <span>{{ order?.delivery_info?.name_to }} - {{
                                            validPhone(order?.delivery_info?.phone_to)
                                        }}</span>
                                        <em>{{ order?.delivery_info?.address_to }}</em>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-detail delivery-detail">
                            <!-- <div class="content">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.cod') }}:
                                </div>
                                <span class="data">
                                    {{ formatCurrency(order?.delivery_info?.cod_price || 0) }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.thoi_gian_lay_hang') }}:
                                </div>
                                <span class="data">
                                    {{ order?.delivery_info?.pickup_time ?
                                        moment(order?.delivery_info?.pickup_time).format("DD/MM/YYYY HH:mm") :
                                        $t('MyOrderDetailComponent.bay_gio') }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyShopOrderDetailComponent.loai_dich_vu_giao') }}:
                                </div>
                                <span class="data">
                                    <span v-if="order.delivery_info.service">{{ order.delivery_info.service?.name }} -
                                    </span> {{ order.delivery_info.distance }} km - {{
                                        formatCurrency(order.delivery_price || 0)
                                    }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.giao_trong') }}:
                                </div>
                                <span class="data" v-if="order?.delivery_info?.duration">
                                    {{ order?.delivery_info?.duration.toFixed(0) }} {{ $t('MyOrderDetailComponent.phut')
                                    }}
                                </span>
                                <span class="data" v-else>{{ $t('MyOrderDetailComponent.chua_chon') }}</span>
                            </div> -->
                            <div class="content" v-if="order?.delivery_info?.package_info">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.thong_tin_goi_hang') }}:
                                </div>
                                <span class="data">
                                    <span v-if="order?.delivery_info?.package_info?.product_type?.length">{{
                                        order?.delivery_info?.package_info?.product_type }}</span>
                                    <span v-if="order?.delivery_info?.package_info?.weight?.length">{{
                                        $t(`MyOrderDetailComponent.${order?.delivery_info?.package_info?.weight}`)
                                    }}</span>
                                    <span v-if="order?.delivery_info?.package_info.size?.length">{{
                                        order?.delivery_info?.package_info.size }}</span>
                                </span>
                            </div>
                            <div class="content" v-if="order?.delivery_info?.special_require?.length">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.yeu_cau_dac_biet') }}:
                                </div>
                                <span class="data">
                                    <span class="special-item"
                                        v-for="itemSpecial in order?.delivery_info?.special_require">
                                        {{ $t(`MyOrderDetailComponent.${itemSpecial.key}`) }}
                                    </span>

                                </span>
                            </div>
                            <div class="content" v-if="order?.delivery_info?.notes?.length">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.ghi_chu_cho_tai_xe') }}:
                                </div>
                                <span class="data">
                                    {{ order?.delivery_info?.notes }}
                                </span>
                            </div>
                            <div class="content">
                                <div class="label">
                                    {{ $t('MyOrderDetailComponent.trang_thai') }}:
                                </div>
                                <span class="data" :class="{
                                    'rejected': order?.delivery_info?.status == appConst.delivery_status.cancelled,
                                    'successfully': order?.delivery_info?.status == appConst.delivery_status.delivered,
                                }">
                                    {{
                                        $t(`MyOrderDetailComponent.trang_thai_don_giao_${order?.delivery_info?.status}`)
                                    }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="h-stack primary-content delivery-content"
                        v-if="order.delivery_info && (order.delivery_partner?.name?.toLowerCase().includes('remagan') || !order.delivery_partner)">
                        <div class="label-content">
                            {{ $t(`MyOrderDetailComponent.tai_xe`) }}
                        </div>
                        <div class="h-stack content-detail" v-if="order?.delivery_info?.driver">
                            <img loading="lazy" class='content-detail user-avatar' :src="order?.delivery_info?.driver?.profile_picture?.length
                                ? (domainImage + order?.delivery_info?.driver?.profile_picture)
                                : non_avatar" :placeholder="non_avatar" alt="" />
                            <div class="user-detail">
                                <div class="customer-name">
                                    <span>{{ order?.delivery_info?.driver?.name }}</span>
                                </div>
                                <nuxt-link :to="`tel:${validPhone(order?.delivery_info?.driver?.phone)}`"
                                    :target="webInApp ? '_blank' : ''" class="content-detail customer-phone">
                                    <span>{{ order?.delivery_info?.driver?.phone }}</span>
                                </nuxt-link>
                                <!-- <div class="status"
                                    :class="{ 'rejected': order?.delivery_info?.status == appConst.delivery_status.cancelled }">
                                    <span>{{
                                        $t(`MyOrderDetailComponent.trang_thai_giao_hang_${order?.delivery_info?.status}`)
                                    }}</span>
                                </div> -->
                            </div>
                            <button class="driver-location"
                                v-if="order?.delivery_info?.status < appConst.delivery_status.delivered" v-on:click="() => {
                                    showDriverLocationModal = true
                                }">{{ $t(`MyOrderDetailComponent.vi_tri`) }}</button>
                        </div>
                        <div class="h-stack content-detail" v-else>
                            <img loading="lazy" class='content-detail user-avatar' :src="non_avatar"
                                :placeholder="non_avatar" alt="" />
                            <div class="user-detail">
                                <div class=" customer-name">
                                    <span>{{ $t(`MyOrderDetailComponent.chua_chon_shipper`) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-stack primary-content delivery-content"
                        v-if="order.delivery_info?.shared_link?.length && !(order.delivery_partner?.name?.toLowerCase().includes('remagan'))">
                        
                        <nuxt-link class="driver-location-ahamove" target="_blank" :to="order.delivery_info?.shared_link">{{ $t(`MyOrderDetailComponent.vi_tri_shipper`) }}</nuxt-link>
                    </div>
                    <div class="h-stack primary-content delivery-content" v-if="!order.delivery_info">
                        <em>{{ $t('MyOrderDetailComponent.chua_tao_don_giao') }}</em>
                    </div>
                </div>
                <div class='main-stack'>
                    <div class='h-stack payment-detail'>
                        <span class='label'>
                            {{ $t('MyOrderDetailComponent.tam_tinh') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.total_amount || 0) }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail'>
                        <span class='label'>
                            {{ $t('MyOrderDetailComponent.giam_gia') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.discount_amount || 0) }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail' v-if="parseFloat(order.delivery_price)">
                        <span class='label'>
                            {{ $t('MyOrderDetailComponent.phi_van_chuyen') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.delivery_price_estimate || 0) }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail' v-if="Math.abs(order.delivery_discount)">
                        <span class='label'>
                            {{ $t('MyShopOrderDetailComponent.giam_phi_van_chuyen') }}
                        </span>
                        <span class='data'>
                            {{ order.delivery_discount ? formatNumber(order.delivery_discount || 0) :
                                $t('MyShopOrderDetailComponent.chua_xac_dinh') }}
                        </span>
                    </div>
                    <div class='h-stack payment-detail total'>
                        <span class='label'>
                            {{ $t('MyOrderDetailComponent.thanh_tien') }}
                        </span>
                        <span class='data'>
                            {{ formatNumber(order.grand_total || 0) }}
                        </span>
                        <!-- <span class="note" v-if="!parseFloat(order.delivery_price)">
                            {{ $t('MyOrderDetailComponent.chua_bao_gom_phi_van_chuyen') }}
                        </span> -->
                    </div>
                </div>

                <VueFinalModal class="my-modal-container" content-class="confirm-cancel-order-modal" :overlay-behavior="'persist'"
                    v-model="showModalConfirmCancelOrder" v-on:closed="() => {
                        showModalConfirmCancelOrder = false
                    }" contentTransition="vfm-slide-up">
                    <div class='v-stack cancel-order-content'>
                        <span class='cancel-order-title'>
                            {{ $t('MyOrderDetailComponent.tu_choi_nhan_don_hang') }}
                        </span>
                        <span class='cancel-order-message'>
                            {{ $t('MyOrderDetailComponent.huy_don_hang') }}
                            <span class='order-code'>
                                {{
                                    order && order.short_code
                                        ? order.short_code
                                        : ""
                                }}
                            </span>
                        </span>
                    </div>
                    <div class='h-stack confirm-modal-buttons'>
                        <button class='reject-button' :disabled="isUpdating" v-on:click="() => {
                            showModalConfirmCancelOrder = false
                        }">
                            {{ $t('MyOrderDetailComponent.khong') }}
                        </button>
                        <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
                            updateStatusOrder(appConst.order_status.cancel.value);
                            showModalConfirmCancelOrder = false;
                        }">
                            {{ $t('MyOrderDetailComponent.co') }}
                        </button>
                    </div>
                </VueFinalModal>

            </div>

            <div class='not-existing-order' v-else-if="!isRefreshing">
                <img loading="lazy" class="empty-image" :src='none_result' :placeholder="none_result" />

                <p class="empty-text">
                    {{ $t('MyOrderDetailComponent.khong_tim_thay_don_hang') }}
                </p>
                <button class='accept-button' v-on:click="() => {
                    router.push({ path: appRoute.ManageOrdersComponent, hash: '#waiting' })
                }">
                    {{ $t('MyOrderDetailComponent.xem_don_khac') }}
                </button>
            </div>

        </div>
        <ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal" :listObject="order.images.map((e: any) => {
            return {
                ...e,
                isBase64: true
            }
        })" :indexActive="indexImageActive" v-on:close="(e: any) => {
            showImageViewerModal = false
        }"></ImageViewerComponent>

        <v-overlay v-model="showDriverLocationModal" :z-index="100" :absolute="false" :close-on-back="true" contained
            key="show_chat_detail" class="driver-location-overlay-container"
            content-class='driver-location-modal-container' no-click-animation v-on:click:outside="() => {
                showDriverLocationModal = false;
            }">
            <div class="driver-location-container">
                <HeaderComponent :title="$t('MyOrderDetailComponent.vi_tri_shipper')">
                    <template v-slot:header_left></template>
                    <template v-slot:header_right>
                        <button class="close" v-on:click="() => {
                            showDriverLocationModal = false
                        }">
                            <Icon name="clarity:times-line" size="25"></Icon>
                        </button>
                    </template>
                </HeaderComponent>
                <div class="delivery-detail-info" id="delivery_detail_container">
                    <div class="delivery-info">
                        <span class="delivery-code"> #{{ order?.delivery_info?.short_code }} </span>
                        <span class="delivery-status" :class="{
                            'pending': order?.delivery_info?.status == appConst.delivery_status.pending,
                            'confirmed': order?.delivery_info?.status == appConst.delivery_status.confirmed,
                            'cancel': order?.delivery_info?.status == appConst.delivery_status.cancelled,
                            'delivered': order?.delivery_info?.status == appConst.delivery_status.delivered
                        }">{{ $t(`DeliveryDetailComponent.trang_thai_giao_hang_${order?.delivery_info?.status}`)
                            }}</span>
                        <div class="places-info">
                            <div class="place">
                                <img :src="start_location_icon" class="icon-left" />
                                <div class="place-content">
                                    <span>{{ order?.delivery_info?.name_from }} - {{ order?.delivery_info?.phone_from
                                        }}</span>
                                    <em>{{ order?.delivery_info?.address_from }}</em>
                                </div>
                            </div>
                            <div class="distance">

                                <Icon name="material-symbols:arrow-cool-down-rounded" class="icon-from-to"></Icon> <em
                                    class="distance">{{
                                        order?.delivery_info?.distance }} km</em>
                            </div>
                            <div class="place">
                                <img :src="destination_location_icon" class="icon-left" />
                                <div class="place-content">
                                    <span>{{ order?.delivery_info?.name_to }} - {{
                                        validPhone(order?.delivery_info?.phone_to)
                                        }}</span>
                                    <em>{{ order?.delivery_info?.address_to }}</em>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="map-container" id="driver_map_container">
                    <client-only>
                        <LMap id="driver_leaflet_map" v-on:ready="(e: any) => {
                            console.log('map ready')
                            leafletMap = e;
                            leafletMap.setView([driver_latitude ?? appConst.defaultCoordinate.latitude, driver_longitude ?? appConst.defaultCoordinate.longitude], 17);
                            leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                            initDriverLeafletMap();
                        }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }"
                            :world-copy-jump="true" :use-global-leaflet="true">
                            <LControlZoom position="bottomright"></LControlZoom>

                            <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
                                name="GoogleMap">
                            </LTileLayer>
                        </LMap>
                    </client-only>
                </div>

            </div>
        </v-overlay>

        <v-overlay v-model="showChatToShop" :z-index="100" :absolute="false" :close-on-back="true" contained
            key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
            no-click-animation v-on:click:outside="() => {
                showChatToShop = false;
            }">
            <!-- <ChatDetailComponent v-if="showChatToShop" :mode="member_type.user" :receiver_id="order.value?.shop?.id"
				:chat_info="chatToShopInfo" :receiver_type="true" v-on:close="() => {
					showChatToShop = false;
				}"></ChatDetailComponent> -->

            <ChatDetailComponent v-if="showChatToShop" :receiver_id="order?.shops?.id" :chat_info="chatToShopInfo"
                :receiver_type="true" :order_link="order?.short_code ?? order?.id" :order_detail="order"
                :mode="member_type.user" v-on:close="() => {
                    showChatToShop = false;
                }"></ChatDetailComponent>
        </v-overlay>

        <OrderReceiverInfoComponent v-if="showOrderReceiverInfoModal"
            v-on:close="()=>{
                showOrderReceiverInfoModal = false;
            }"
            :init_data="{
                name: order.customer_name,
                phone: order.customer_phone,
                address: order.address,
                latitude: order.customer_latitude,
                longitude: order.customer_longitude,
                images: isString(order.extra_data) ? JSON.parse(order.extra_data)?.location?.images : order.extra_data?.location?.images,
                note: isString(order.extra_data) ? JSON.parse(order.extra_data)?.location?.note : order.extra_data?.location?.note
            }"
        ></OrderReceiverInfoComponent>
    </div>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_product from '../../assets/image/icon-for-product.png';
import none_result from "~/assets/image/none-result.webp";
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { appConst, domainImage, formatCurrency, formatNumber, showTranslateProductDescription, showTranslateProductName, validPhone } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { PublicService } from '~/services/publicService/publicService';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
import driver_marker_location_icon from "~/assets/image/driver-marker.png"
import start_location_icon from "~/assets/image/start-marker-2.png";
import destination_location_icon from "~/assets/image/destination-marker-2.png";
import { DeliveryService } from '~/services/orderService/deliveryService';
import { channel_type, member_type, type ChannelDTO } from '~/components/chatManage/ChatDTO';

const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    shopData: {},
    updated: null
})
var nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.MyOrderDetailComponent')
});

var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var publicService = new PublicService();
var shopService = new ShopService();
var mqttService = new MqttService();
var deliveryService = new DeliveryService();
var searchOrderTimeout: any;

var loadMoreTimeOut: any;
var searchTimeout: any;

var showModalConfirmCancelOrder = ref(false);
var orderId = ref((route.params.order_id || null) as any);
var order = ref();
var isRefreshing = ref(true);
var isUpdating = ref(false);
var showEditOrderModal = ref(false);
var updated = ref((route.params && route.params.updated) ? route.params.updated : false);
var webInApp = ref(null as any);

var showFullProcess = ref(false);
var indexImageActive = ref(0);
var showImageViewerModal = ref(false);

var showDriverLocationModal = ref(false);
var driver_latitude = ref<any>();
var driver_longitude = ref<any>();
var driver_orient_alpha = ref<any>();

var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var destinationMarkerLeaflet: L.Marker;
var startMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);

let my_latitude = ref(null as any);
let my_longitude = ref(null as any);

let latitude_start = ref(null as any);
let longitude_start = ref(null as any);

let latitude_destination = ref(null as any);
let longitude_destination = ref(null as any);
let control: any;
var lastPublishMoving = ref<any>(null);

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();

var profileInfo = ref<any>(null);

var showOrderReceiverInfoModal = ref(false);
onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
    isRefreshing.value = true;
    profileInfo.value = await authService.checkAuth();
    await init().then(() => {
        mqttService.subscribe(appConst.mqtt_topic.order.replaceAll(':order_id', order.value.id ?? orderId.value), (mess) => {

            let message = mess.mess;
            if (message.type == 'delivery_update' || message.type == 'delivery_completed' || message.type == 'order_update') {
                init().then(() => {
                    setControlRouteMachine();
                });
            }
            if (message.type == 'driver_moving') {
                driver_latitude.value = message.driver_latitude;
                driver_longitude.value = message.driver_longitude;
                driver_orient_alpha.value = message.driver_orient_alpha ?? 0;
                if (!lastPublishMoving.value || (Date.now() - lastPublishMoving.value > 3000)) {
                    lastPublishMoving.value = Date.now();
                    if (control?.getWaypoints()?.length > 0) {
                        console.log('co control')
                        let currentWaypoint = control.getWaypoints();
                        currentWaypoint[0].latLng = new nuxtApp.$L.LatLng(driver_latitude.value, driver_longitude.value);
                        control.setWaypoints(currentWaypoint);
                        control.route();
                    }
                    else {
                        console.log('ko co control')
                        setControlRouteMachine();
                    }
                }

                if (localeMarkerLeaflet) {
                    localeMarkerLeaflet.setLatLng([driver_latitude.value, driver_longitude.value]);
                    localeMarkerLeaflet.setRotationAngle(driver_orient_alpha.value);
                }

            }
        })
        isRefreshing.value = false;
    });

});

onUnmounted(() => {
    mqttService.unsubscribe(appConst.mqtt_topic.order.replaceAll(':order_id', order.value?.id ?? orderId.value))
})
async function init() {
    return new Promise(async (resolve) => {
        await getDetailOrder(orderId.value).then(async (res) => {
            if (res) {
                await getOrderDelivery().then(() => {
                    updated.value = props ? props.updated : false
                    resolve(true);
                })
            }
            else {
                updated.value = props ? props.updated : false
                resolve(true);
            }
        });
    })
}
function getDetailOrder(id: string) {
    return new Promise((resolve) => {
        publicService.detailOrder(id).then(async res => {
            if (res.status == HttpStatusCode.Ok) {
                order.value = JSON.parse(JSON.stringify(res.body.data));
                resolve(order.value);

            }
            else {
                toast.error(t('MyOrderDetailComponent.don_hang_khong_ton_tai'));
                resolve(null);
                // close();
            }
        })
    })

}
function getOrderDelivery() {
    return new Promise((resolve) => {
        if (order.value.delivery?.id) {
            order.value.delivery_info = JSON.parse(JSON.stringify(order.value.delivery));
            order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
            order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

            latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
            longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

            latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
            longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

            resolve(true);
        }
        else if (order.value?.delivery_partner) {
            let body = {
                partner: order.value?.delivery_partner?.name?.toLowerCase().includes('remagan') ? 'remagan' : order.value?.delivery_partner?.name?.toLowerCase(),
                shop_id: order.value?.shop_id ?? order.value?.shops?.id,
                order_id: order.value?.id ?? orderId.value,
                delivery_partner_id: order.value?.delivery_partner_id
            }
            deliveryService.detailV2ByDeliveryId(body).then((res) => {
                if (res.status == HttpStatusCode.Ok) {
                    order.value.delivery_info = JSON.parse(JSON.stringify(res.body.data));
                    order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
                    order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

                    latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
                    longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

                    latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
                    longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

                    resolve(true);
                }
                else {
                    resolve(false);
                }
            })
        }
        else {
            deliveryService.detailByOrderId(orderId.value).then((res) => {
                if (res.status == HttpStatusCode.Ok) {
                    order.value.delivery_info = JSON.parse(JSON.stringify(res.body.data));
                    order.value.delivery_info.package_info = typeof order.value.delivery_info.package_info == 'string' ? JSON.parse(order.value.delivery_info.package_info) : order.value.delivery_info.package_info;
                    order.value.delivery_info.special_require = typeof order.value.delivery_info.special_require == 'string' ? JSON.parse(order.value.delivery_info.special_require) : order.value.delivery_info.special_require;

                    latitude_start.value = order.value?.delivery_info?.latitude_from ?? order.value?.shops?.latitude;
                    longitude_start.value = order.value?.delivery_info?.longitude_from ?? order.value?.shops?.longitude;

                    latitude_destination.value = order.value?.delivery_info?.latitude_to ?? order.value?.customer_latitude ?? order.value?.customer?.latitude;
                    longitude_destination.value = order.value?.delivery_info?.longitude_to ?? order.value?.customer_longitude ?? order.value?.customer?.longitude;

                    resolve(true);
                }
                else {
                    resolve(false);
                }
            })
        }
    })

}
function close() {
    router.back()
    // backHandler();
}

function paymentStatusStyle(status: number) {
    switch (status) {
        case appConst.order_status.waiting.value:
            return "cancel";
        case appConst.order_status.confirmed.value:
            return "cancel";
        // case appConst.order_status.ready.value:
        //     return "cancel";
        case appConst.order_status.taken.value:
            return 'taken';
        case appConst.order_status.return.value:
            return 'return';
        case appConst.order_status.cancel.value:
            return "cancel";
        default:
            return 'taken';
    }
}


function copyToClipboard(text: string) {
    if (!webInApp.value) {
        window.navigator.clipboard.writeText(text);
    }
    else {
        nuxtApp.$emit(appConst.event_key.send_request_to_app, {
            action: appConst.webToAppAction.copyToClipboard,
            data: text
        })
    }
}

async function updateStatusOrder(newStatus: number) {

    isUpdating.value = true;

    let orderTmp = JSON.parse(JSON.stringify(order.value));
    orderTmp.status = newStatus != 3 ? newStatus : 4;
    orderTmp.items = [];
    await order.value.items.forEach((e: any) => {
        orderTmp.items.push(e.pivot)
    })

    orderService.updateOrder(orderTmp).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            updated.value = true;
            init();
        }
        else if (res.status == HttpStatusCode.Unauthorized) {
            toast.error(t('MyOrderDetailComponent.ban_khong_phai_chu_cua_hang'));
            isUpdating.value = false;
        }
        else {
            toast.error(t('MyOrderDetailComponent.co_loi_xay_ra'));
            isUpdating.value = false;
        }
    }).catch(err => {
        toast.error(t('MyOrderDetailComponent.co_loi_xay_ra'));
        isUpdating.value = false;
    })
}

async function initDriverLeafletMap() {

    // leafletMap.setView([
    //   my_latitude.value,
    //   my_longitude.value
    // ], 17);
    // fitToDirection();

    await addDestinationMarker();
    await addStartMarker();
    await addDriverLocationMarker();
    setTimeout(async () => {
        await setControlRouteMachine();
    }, 1000);
}
function addStartMarker() {
    startMarkerLeaflet = nuxtApp.$L.marker([latitude_start.value, longitude_start.value], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: start_location_icon,
            iconSize: [30, 45],
            className: appConst.markerCustom.defaultIcon.class,
        }),
    });
    startMarkerLeaflet.addTo(leafletMap);
}
function addDestinationMarker() {
    destinationMarkerLeaflet = nuxtApp.$L.marker([latitude_destination.value, longitude_destination.value], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: destination_location_icon,
            iconSize: [30, 45],
            className: appConst.markerCustom.defaultIcon.class,
        }),
    });
    destinationMarkerLeaflet.addTo(leafletMap);
}
function addDriverLocationMarker() {
    let iconDriver = new nuxtApp.$L.Icon({
        iconUrl: driver_marker_location_icon,
        iconSize: appConst.markerCustom.driverIcon.size,
        className: appConst.markerCustom.driverIcon.class,
    });
    localeMarkerLeaflet = nuxtApp.$L.marker([driver_latitude.value ?? appConst.defaultCoordinate.latitude, driver_longitude.value ?? appConst.defaultCoordinate.longitude], {
        icon: iconDriver,
        rotationOrigin: appConst.markerCustom.driverIcon.rotatePosition,
        rotationAngle: driver_orient_alpha.value
    });
    localeMarkerLeaflet.addTo(leafletMap);
}
async function setControlRouteMachine() {
    let plan: any;
    if (order.value?.delivery_info?.status == appConst.delivery_status.pending || order.value?.delivery_info?.status == appConst.delivery_status.delivered) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(latitude_start.value, longitude_start.value),
            nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
        ], {
            createMarker: () => (false),
        })
    }
    else if (order.value?.delivery_info?.status == appConst.delivery_status.confirmed) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
            nuxtApp.$L.latLng(latitude_start.value, longitude_start.value)
        ], {
            createMarker: () => (false),
        })
    }
    else if (order.value?.delivery_info?.status == appConst.delivery_status.picked_up) {
        plan = new nuxtApp.$L.Routing.Plan([
            nuxtApp.$L.latLng(driver_latitude.value, driver_longitude.value),
            nuxtApp.$L.latLng(latitude_destination.value, longitude_destination.value)
        ], {
            createMarker: () => (false),
        })
    }
    if (control) {
        control.remove();
        control = null;
        // control.setPlan(plan)
    }
    setTimeout(() => {
        control = new nuxtApp.$L.Routing.Control({
            waypointMode: 'connect',
            router: nuxtApp.$L.Routing.osrmv1({
                serviceUrl: appConst.urlOSRMv1,
                requestParameters: {
                    overview: 'full',
                    annotations: true,
                    steps: true,
                    alternatives: 2,
                },
                profile: 'bike',
                useHints: false,
            }),
            fitSelectedRoutes: 'smart',
            plan: plan,
            autoRoute: false,
            routeWhileDragging: false,
            lineOptions: {
                missingRouteTolerance: 10,
                extendToWaypoints: true,
                addWaypoints: false,
                styles: [{
                    color: 'var(--primary-color-2)',
                    weight: 5,
                }]
            },
            altLineOptions: {
                missingRouteTolerance: 10,
                extendToWaypoints: false,
                addWaypoints: false,
                styles: [{
                    opacity: .8,
                    color: '#545454',
                    weight: 4,
                    dashArray: '10, 10'
                }]
            },
            useZoomParameter: true,
            showAlternatives: true,

        });

        control.on('routingerror', () => {
            // noneRoute.value = true;
            control.remove()
        })
        control.on('routesfound', () => {
            control?.addTo(leafletMap);
        })

        control.route();
    }, 500);
}

function directionToShop() {
    // await getUserLocation();
    let textToDirections = `https://www.google.com/maps/dir/?api=1&origin=${latitude_destination.value},${longitude_destination.value}&destination=${latitude_start.value},${longitude_start.value}&trabelmode=bicycling`;
    return textToDirections ?? "";
}

function chatToShop() {
    if (profileInfo.value?.id) {
        chatToShopInfo.value = {
            members: [{
                member_id: order.value?.shops?.id,
                member: JSON.parse(JSON.stringify(order.value?.shops)),
                member_type: member_type.shop
            }],
            name: null,
            type: channel_type.user,
            avatar: order.value?.shops?.logo
        };
        showChatToShop.value = true;
    }
    else {
        nuxtApp.$emit(appConst.event_key.require_login, {
            redirect_url: route.fullPath,
            back_on_close: false
        })
    }
}
</script>

<style lang="scss" src="./MyOrderDetailStyles.scss"></style>