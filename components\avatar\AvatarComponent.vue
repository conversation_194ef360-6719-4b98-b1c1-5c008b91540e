<template>
	<div class="avatar-container" :style="{
		'width': props.width ? `${props.width}px !important` : '100%',
		'height': props.height ? `${props.height}px !important` : '100%',
		'min-width': props.width ? `${props.width}px !important` : '100%'
	}">
		<img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt="shop logo" title="lỗi ảnh"
			v-if="!props.imgSrc?.length" />
		<div class="logo-origin-container" :style="{
			'--width-view': `${props.width}`
		}" :class="{ 'none-style': !props.imgStyle?.length }" v-else>
			<img :src="props.imgSrc?.length ? props.imgSrc : shop_logo" :placeholder="shop_logo" loading="lazy" v-on:click="() => {
				imgClick();
			}" :style="{
				transform: props.imgStyle?.length ? props.imgStyle
					: 'none'
			}" :alt="props.imgTitle ?? 'Logo'" />
		</div>
	</div>

</template>

<script lang="ts" setup>
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";

const props = defineProps({
	imgTitle: null,
	imgSrc: null,
	imgStyle: null,
	width:null,
	height:null,
})
const emit = defineEmits(['img_click']);

watch(()=>[props],()=>{
})

onMounted(() => {
})

function imgClick() {
	emit('img_click');
}
</script>

<style lang="scss" src="./AvatarStyles.scss"></style>