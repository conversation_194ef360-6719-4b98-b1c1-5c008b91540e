<template>
	<div class="advertisments">

		<Swiper class="my-carousel my-adv-carousel" :modules="[SwiperAutoplay, SwiperPagination]" :slides-per-view="1"
			:loop="true" :effect="'creative'" :pagination="{
				horizontalClass: 'my-adv-carousel-pagination',
			}" :autoplay="true" key="advertisment-carousel" @init="(e) => {
				adver_el = e
			}" ref="adver_el">
			<SwiperSlide class="item-stack-slide" :key="'advertisment_1'">
				<img loading="lazy" :src="banner_1" alt="" />
			</SwiperSlide>
			<SwiperSlide class="item-stack-slide" :key="'advertisment_2'">
				<img loading="lazy" :src="banner_2" alt="" />
			</SwiperSlide>
			<SwiperSlide class="item-stack-slide" :key="'advertisment_3'">
				<img loading="lazy" :src="banner_3" alt="" />
			</SwiperSlide>
			<!-- <SwiperSlide 
		class="item-stack-slide" :key="'advertisment_1'">
		<img loading="lazy" :src="banner_4" alt="" />
	</SwiperSlide>
	<SwiperSlide 
		class="item-stack-slide" :key="'advertisment_1'">
		<img loading="lazy" :src="banner_5" alt="" />
	</SwiperSlide>
	<SwiperSlide 
		class="item-stack-slide" :key="'advertisment_1'">
		<img loading="lazy" :src="banner_6" alt="" />
	</SwiperSlide> -->
		</Swiper>

	</div>

</template>

<style lang="scss" src="./AdvertismentSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import banner_1 from "~/assets/imageV2/Banner_T11_P1(2).jpg";
import banner_2 from "~/assets/imageV2/Banner_T11_P1(3).jpg";
import banner_3 from "~/assets/imageV2/Banner_T11_P1(4).png";
import banner_4 from "~/assets/imageV2/banner-4.jpeg";
import banner_5 from "~/assets/imageV2/banner-5.jpeg";
import banner_6 from "~/assets/imageV2/banner-6.jpeg";
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();

const props = defineProps({
	filter_data: null,
	enable_load: null
})

var adver_el = useState<any>(() => { return null });

onBeforeMount(() => {
})
onUnmounted(async () => {
});
onMounted(async () => {

});

</script>
