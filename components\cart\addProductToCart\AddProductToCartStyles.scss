.product-select-overlay-container {
  overflow: hidden;
  z-index: 1001 !important;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .product-selected-container {
    background: #f5f6fa;
    position: absolute;
    bottom: 0;
    // left: 0;
    left: 50%;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    max-width: var(--max-width-content-view-1024);
    transform: translateX(-50%);

    & > .product-selected-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      text-align: center;
      position: relative;
      padding: 10px 10px 20px 10px;

      & > .close {
        width: fit-content;
        align-self: flex-end;
        cursor: pointer;
        position: absolute;
      }

      & > img {
        width: auto;
        max-width: 100%;
        height: 200px;
        margin: auto;
        overflow: hidden;
        border-radius: 10px;
        object-fit: cover;
      }

      & > .name {
        color: #626262;
        font-size: 1.1em;
        font-weight: 600;
        margin-top: 10px;
      }
      & > .price {
        color: var(--primary-color-2);
        font-size: 1.3em;
        font-weight: bold;
      }
      & > .origin-price {
        text-align: center;
        color: #626262;
        font-size: 1em;
        text-decoration: line-through;
      }

      & > .current-in-cart{
        font-weight: 600;
        color: #545454;
        font-style: 12px;

        & > em{
          color: var(--primary-color-2);
          font-weight: 800;
          font-style: normal;
          font-size: 17px;
        }
      }

      & > .actions {
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        & > .quantity-actions {
          display: flex;
          width: 45%;
          justify-content: space-evenly;

          & > .quantity-button {
            color: #626262;
            font-size: 35px;
            display: flex;
          }

          & > span {
            display: flex;
            align-items: center;
            font-size: 1.6em;
            color: var(--primary-color-2);
            font-weight: bold;
          }
        }

        & > .add-to-cart-action {
          
          border: thin solid var(--primary-color-2);
          color: var(--primary-color-2);
          border-radius: 10px;
          padding: 5px 15px;
          width: 55%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-weight: bold;

          & > .total-price {
            font-size: 1.3em;
          }

          & > .add-action {
            font-size: 0.9em;
            
          }
        }
      }
    }
  }
}