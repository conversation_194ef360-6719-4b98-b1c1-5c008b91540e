import environment from "~/assets/environment/environment";

export default defineEventHandler((event) => {
    const secret = event.req.headers['x-firebase-config-secret'];
    if (secret !== environment.firebaseConfigSecret) {
        return {
            statusCode: 403,
            body: { error: 'Forbidden' }
        };
    }

    return {
        apiKey: environment.firebaseConfig.apiKey,
        authDomain: environment.firebaseConfig.authDomain,
        projectId: environment.firebaseConfig.projectId,
        storageBucket: environment.firebaseConfig.storageBucket,
        messagingSenderId: environment.firebaseConfig.messagingSenderId,
        appId: environment.firebaseConfig.appId,
        measurementId: environment.firebaseConfig.measurementId
    };
});