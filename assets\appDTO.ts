import { appConst } from "./AppConst";

export type CartDto = {
  product_id: string;
  product: any;
  shop_id: string;
  shop: any;
  quantity: number;
  price: number | null;
  notes: string;
};

export type OrderDto = {
  address: string;
  province_id: number;
  district_id: number;
  ward_id: number;
  customer_id: string;
  customer_name: string;
  customer_phone: string;
  price: number | null;
  price_off: number | null;
  delivery_type: boolean;
  delivery_price: number | null;
  payment_method: any;
  short_code: string;
  notes: string;
  items: any[];
  shop_id: any;
  latitude: any;
  longitude: any;
  delivery_distance: any;
  delivery_time: any;
  delivery: any;
  delivery_partner:any;
  delivery_partner_id: any;
  delivery_price_estimate: any;
  extra_data: any
};

export enum InteractionObjectType {
  like = 1,
  follow = 2,
  read_message = 3 // object_b = user, object_a = channel
}

export var filter_sort = {
  gia_thap_den_cao: 1,
  gia_cao_den_thap: 2,
  moi_nhat: 3,
  // san_pham_ban_chay: 4,
  // san_pham_khuyen_mai: 5,
  gan_nhat: 6
} as any

export enum delivery_payment_method_enum {
  cash = "cash",
  cash_by_recipient = "cash_by_recipient"
}

export enum increase_view_obj_type{
  shop = 'shop',
  product = 'product',
  video = 'video'
}