<template>
	<VueFinalModal class="my-modal-container" :keep-overlay="false" :overlay="false" :hide-overlay="false" :overlay-behavior="'persist'"
        :modal-id="'shop_waiting_confirm'" 
		:hide-overlay-on-blur="false"
        content-class="v-stack shop-waiting-confirm-container" :click-to-close="false" v-model="showWaitingConfirmModal"
        contentTransition="vfm-fade" v-on:closed="() => {
            close()
        }">
		<div class="shop-waiting-confirm-content-container">
			<div class="img-container">
				<img loading='lazy' :src="shop_waiting_confirm" alt="">
				<div class="loader"></div>
			</div>
			
			<span>{{ $t('ShopWaitingConfirmComponent.thong_bao_cho_duyet') }}</span>
			<div class="footer-actions">
				<!-- <button class="close-button" v-on:click="close()">
					{{ $t('RequireLoginComponent.de_sau') }}
				</button> -->
				<button class="close-button" v-on:click="() => {
					// goToLogin()
					close(true);
				}">
					{{ $t('ShopWaitingConfirmComponent.dong') }}
				</button>
			</div>
		</div>
	</VueFinalModal>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import { AuthService } from "~/services/authService/authService";
import shop_waiting_confirm from '~/assets/imageV2/waiting-confirm-shop-2.png'

const route = useRoute();
const authService = new AuthService();
const router = useRouter();
const emit = defineEmits(['close']);
const props = defineProps({
    title: null,
})

var showWaitingConfirmModal = ref(false)
onMounted(async () => {
	showWaitingConfirmModal.value = true;
})

function close(value?: any) {
    emit('close', value);
	showWaitingConfirmModal.value = false;
}
</script>

<style lang="scss" src="./ShopWaitingConfirmStyles.scss"></style>