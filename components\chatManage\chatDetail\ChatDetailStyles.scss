.chat-detail-overlay-container {
  overflow: hidden;
  position: absolute !important;
  z-index: 10000 !important;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .chat-detail-modal-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    // animation: slide-up 0.5s ease;

    & .select-product-overlay-container {
      z-index: 10000 !important;
      overflow: hidden;

      .select-product-content-ontainer {
        flex: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        font-size: 1rem;
      }

      & .select-product-container {
        background: white;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        max-width: var(--max-width-content-view-1024);
        width: 100%;
        height: 100%;
        max-height: 95dvh;
        // height: fit-content;
        // min-height: fit-content;
        display: flex;
        flex-direction: column;
        overflow: auto;
        border-radius: 15px 15px 0 0;
        // animation: slide-up 0.5s ease;

        & > .select-product-header {
          display: flex;
          justify-content: center;
          position: relative;
          color: #545454;
          align-items: center;
          padding: 10px;
          font-weight: 700;

          & > button {
            position: absolute;
            right: 10px;
            font-size: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        & > .select-product-body {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 0;
          overflow: auto;

          & > .search-product {
            position: sticky;
            top: 0;
            padding: 0 10px;
            z-index: 10;
            background-color: white;
            padding-bottom: 10px;
            & > .search-input-group {
              display: flex;
              background: var(--secondary-color-2);
              border-radius: 5px;
              padding: 0 5px;
              flex: 1;
              height: 35px;

              & > button {
                color: var(--primary-color-1);
                width: 35px;
                height: 35px;
                font-size: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
              }

              & > button.category-button {
                font-size: 22px;
              }

              & > input {
                background: var(--secondary-color-2);
                height: 35px;
                outline: none;
                font-size: 13px;
                font-weight: bold;
                flex: 1;
              }
            }
          }
        }

        & .category-products-container {
          background: #f5f6fa;
          padding-top: 10px;
          border-radius: 0;
          display: flex;
          flex-direction: column;
          // gap: 10px;
          // box-shadow: 0 0 15px rgb(0, 0, 0, 0.1);
          width: 100%;
          // border-bottom: thin solid #efefef;

          &:last-child {
            padding-bottom: 125px;
          }

          & > .category-title {
            font-size: 13px;
            font-weight: 600;
            color: #a8a7a7;
            padding: 0 10px;
          }

          & > .category-products-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 7px;
            padding: 5px;

            @media screen and (max-width: 500px) {
              & > .product-item-container-grid {
                --width-item: calc(
                  (100vw - 3px - 7px - 5px * 2) / 2
                ) !important;
              }
            }
            @media screen and (min-width: 501px) and (max-width: 1024px) {
              & > .product-item-container-grid {
                --width-item: calc(
                  (100dvw - 3px - 7px * 3 - 5px * 2) / 3
                ) !important;
              }
            }
            @media screen and (min-width: 1025px) {
              & > .product-item-container-grid {
                --width-item: calc(
                  (1024px - 3px - 7px * 3 - 5px * 2) / 4
                ) !important;
              }
            }

            & > .product-item-container-grid {
              cursor: pointer;
              padding: 5px;
              border: thin solid transparent;
              height: auto;
              overflow: hidden;
              flex-direction: column;
              position: relative;
              -webkit-user-drag: none;
              // border: thin solid #D9D9D9;
              background: white;
              border-radius: 10px;
              width: var(--width-item);
              min-width: var(--width-item);
              max-width: var(--width-item);
              justify-content: space-evenly;
              display: block;

              & > .product-item {
                flex: 1;
                width: 100%;

                & > img {
                  flex: 1;
                  width: calc(var(--width-item) - 12px);
                  height: calc(var(--width-item) - 12px);
                  min-height: calc(var(--width-item) - 12px);
                  border-radius: 7px;
                  object-fit: cover;
                }
                & > .product-item-content {
                  display: flex;
                  flex-direction: column;
                  // height: 75px;
                  // min-height: 75px;
                  padding: 5px 0 0;
                  text-align: left;
                  font-size: 1em;
                  gap: 1px;
                  flex: 1;

                  & > .name {
                    --font-size: 13px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-weight: bold;
                    color: var(--primary-color-1);
                    font-size: var(--font-size);
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    line-height: 1.1;
                    height: calc(var(--font-size) * 1.1 * 2);
                    -webkit-box-orient: vertical;
                  }
                  & > .sold-like-amount {
                    font-size: 13px;
                    color: #a8a7a7;
                    font-weight: 400;
                  }
                  & > .h-stack {
                    flex: 1;
                  }

                  & .price {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-weight: bold;
                    color: var(--primary-color-2);
                    font-size: 17px;
                    display: flex;
                    flex-direction: column;
                    line-height: 1.1;
                    margin-top: auto;
                    font-weight: 800;
                    padding-top: 10px;

                    & > .off {
                      color: #6c6c6c;
                      text-decoration: line-through;
                      font-style: normal;
                      font-size: 13px;
                      font-weight: 400;
                    }
                  }

                  & .add-to-cart {
                    color: var(--primary-color-2);
                    margin-left: auto;
                    margin-top: auto;
                    width: 40px;
                    min-width: 40px;
                    height: 40px;
                    padding: 5px;
                    display: flex;
                    font-size: 30px;
                    align-items: flex-end;
                    justify-content: flex-end;
                  }
                }
              }
            }
            & > .product-item-container-grid:first {
              margin-left: 0;
            }
            & > .product-item-container-grid:last-child {
              margin-right: auto;
            }

            & .product-item-container-grid:hover,
            .product-item-container-grid:active,
            .product-item-container-grid:focus,
            .product-item-container-grid:target {
              border: thin solid var(--primary-color-1);
              border-radius: 10px;
              background: #ebfeff;
              box-shadow: 0 7px 10px -7px #868686;
            }
          }
        }
      }
    }

    & .select-order-overlay-container {
      z-index: 10000 !important;
      overflow: hidden;

      .select-order-content-ontainer {
        flex: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        font-size: 1rem;
      }

      & .select-order-container {
        background: white;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        max-width: var(--max-width-content-view-1024);
        width: 100%;
        height: 100%;
        max-height: 95dvh;
        // height: fit-content;
        // min-height: fit-content;
        display: flex;
        flex-direction: column;
        overflow: auto;
        border-radius: 15px 15px 0 0;
        animation: slide-up 0.5s ease;

        & > .select-order-header {
          display: flex;
          justify-content: center;
          position: relative;
          color: #545454;
          align-items: center;
          padding: 10px;
          font-weight: 700;

          & > button {
            position: absolute;
            right: 10px;
            font-size: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        & > .select-order-body {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 0;
          overflow: auto;

          & > .search-order {
            position: sticky;
            top: 0;
            padding: 0 10px;
            z-index: 10;
            background-color: white;
            padding-bottom: 10px;
            & > .search-input-group {
              display: flex;
              background: var(--secondary-color-2);
              border-radius: 5px;
              padding: 0 5px;
              flex: 1;
              height: 35px;

              & > button {
                color: var(--primary-color-1);
                width: 35px;
                height: 35px;
                font-size: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
              }

              & > button.category-button {
                font-size: 22px;
              }

              & > input {
                background: var(--secondary-color-2);
                height: 35px;
                outline: none;
                font-size: 13px;
                font-weight: bold;
                flex: 1;
              }
            }
          }

          & > .list-order-container {
            padding: 10px;
            background: #f5f6fa;
            display: flex;
            flex-direction: column;
            flex: 1;
          }

          & .item-order {
            padding: 10px;
            margin-top: 10px;
            align-items: center;
            width: 100%;
            gap: 5px;
            background-color: white;
            // border: thin solid #ccc;
            border-radius: 5px;
            font-size: 15px;

            & .order-button-container {
              padding: 0;
              align-items: flex-start;
              background: white;
              // color: var(--primary-color-2);
              gap: 5px;
              font-size: 1em;
              width: 100%;
              height: 100%;
              border: none;
              animation: none;

              & > .v-stack {
                width: 100%;

                & > .h-stack {
                  flex: 1;
                  justify-content: space-between;
                  width: 100%;
                }

                & > .h-stack.order-detail-advanced {
                  justify-content: flex-start;
                }
                & .is-new {
                  font-size: 10px;
                  text-transform: uppercase;
                  color: white;
                  padding: 5px 10px;
                  margin: 0 10px 0 auto;
                  background: #1a95ff;
                  border-radius: 5px;
                  align-items: center;
                  display: flex;
                }
                & > .shop-name {
                  color: var(--primary-color-2);
                  font-weight: bold;
                  justify-content: flex-start;
                  gap: 5px;
                }

                & .order-status {
                  padding: 5px;
                  border-radius: 5px;
                  justify-content: center;
                  line-height: 1;

                  &.waiting {
                    color: rgb(164, 171, 182);
                    background-color: rgba(164, 171, 182, 0.25);
                  }

                  &.confirmed {
                    color: rgb(230, 149, 0);
                    background-color: rgba(230, 149, 0, 0.25);
                  }

                  &.ready {
                    color: rgb(45, 204, 255);
                    background-color: rgba(45, 204, 255, 0.25);
                  }

                  &.taken {
                    color: var(--primary-color-1);
                    background-color: var(--secondary-color-2);
                  }

                  &.cancel {
                    color: rgb(255, 56, 56);
                    background-color: rgba(255, 56, 56, 0.25);
                  }
                }

                & .customer-name {
                  font-weight: 600;
                  color: var(--primary-color-1);
                }

                & .customer-phone {
                  font-weight: 600;
                }

                & .short-code {
                  margin-left: auto;
                  font-size: 1.1em;
                }

                & .title {
                  color: var(--color-text-note);
                }

                & .total {
                  font-weight: 600;
                  color: var(--primary-color-2);
                }
              }
            }
          }
        }
      }
    }

    & .selected-message-overlay-container {
      z-index: 10000 !important;
      overflow: hidden;

      & .selected-message-container {
        position: absolute;
        bottom: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        font-size: 1rem;
        padding: 30px 10px;
        width: 100%;
        background-color: #f4f4f4;
        border-radius: 10px 10px 0 0;
        animation: slide-up 0.5s ease;

        & > .close-selected-modal{
          position: absolute;
          top: 0;
          right: 0;
          font-size: 20px;
          padding: 7px 10px;
          display: flex;
        }

        & > .message-container {
          display: flex;
          flex-direction: column;
          gap: 5px;
          margin-top: 5px;

          & > .message-group {
            display: flex;
            flex-direction: row;
            gap: 5px;
            & > .sender-avatar {
              width: 32px;
              height: 32px;
              border: 2px solid white;
              box-shadow: 0 0 4px 0 rgb(0, 0, 0, 0.25);
              border-radius: 50%;
              align-self: flex-start;

              & > img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
              }
            }

            & > .message-content-group {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              max-width: 100%;
              gap: 5px;
              flex: 1;
              position: relative;
              & > .message {
                padding: 5px 10px;
                border-radius: 3px;
                max-width: 70%;
                background-color: white;
                white-space: break-spaces;
                word-break: break-word;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                font-weight: 400;
                column-gap: 5px;
                color: black;
                font-size: 15px;
                align-items: flex-end;
                line-height: 1.25;
                // user-select: none;

                & > .time {
                  font-size: 10px;
                  color: #545454;
                  font-weight: 400;
                  line-height: 1.25;
                  margin-left: auto;
                  margin-top: auto;
                  margin-bottom: 1px;
                  align-self: baseline;
                  user-select: none;
                }
              }
              & > .time {
                font-size: 10px;
                color: #545454;
                font-weight: 400;
                line-height: 1.25;
                align-self: baseline;
                user-select: none;
                flex: 1;
                text-align: center;
                margin: 5px auto;
              }
              & > .message.show-origin {
                border: thin solid var(--primary-color-1);
                border-radius: 5px;
                background: white;
              }
              & > .message.link {
                word-break: break-all;
                color: rgb(0 107 179);
              }

              & > .message.product {
                display: flex;
                flex-direction: column;
                background-color: transparent !important;
                max-width: 100% !important;
                gap: 5px;
                padding: 0;
                min-width: unset;
                width: 100%;
                margin-top: 10px;
                & > .product-detail {
                  display: flex;
                  flex: 1;
                  width: 100%;
                  padding: 7px;
                  gap: 7px;
                  max-width: 100% !important;
                  align-items: center;
                  justify-content: flex-start;
                  border-radius: 3px !important;
                  background-color: white !important;
                  border: 2px solid var(--primary-color-2);

                  & > img {
                    width: 75px;
                    height: 75px;
                    object-fit: cover;
                    border-radius: 0;
                    padding: 0;
                    margin-bottom: auto;
                  }
                  & > .product-info {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    gap: 5px;
                    flex: 1;
                    & > .product-name {
                      font-size: 14px;
                      font-weight: 800;
                      line-height: 20px;

                      color: var(--primary-color-1);
                    }

                    & > .price-info {
                      display: flex;
                      color: var(--primary-color-2);
                      font-size: 20px;
                      font-weight: 900;

                      & > .product-price {
                        display: flex;
                        flex-direction: column;
                        line-height: 1.1;
                      }

                      & em.off {
                        font-weight: 400;
                        font-size: 13px;
                        color: #545454;
                        font-style: normal;
                        text-decoration: line-through;
                      }
                    }
                  }
                }
              }
              & > .message.order {
                display: flex;
                flex-direction: column;
                background-color: transparent !important;
                max-width: 100% !important;
                gap: 5px;
                padding: 0;
                min-width: unset;
                width: 100%;
                margin-top: 10px;
                & > .order-info {
                  display: flex;
                  flex-direction: column;
                  font-size: 15px;
                  color: #545454;
                  flex: 1;
                  line-height: normal;
                  width: 100%;
                  padding: 7px;
                  max-width: 100% !important;
                  align-items: flex-start;
                  justify-content: flex-start;
                  border-radius: 3px !important;
                  background-color: white !important;
                  border: 2px solid var(--primary-color-2);

                  & .order-code {
                    font-weight: 700;
                    color: var(--primary-color-2);
                  }
                  & .order-at {
                    font-style: italic;
                    font-size: 13px;
                    margin-left: 10px;
                  }
                  & .order-customer-name {
                    color: var(--primary-color-1);
                    font-weight: 700;

                    & > em {
                      font-size: 13px;
                      font-style: italic;
                      font-weight: 500;
                      color: #545454;
                    }
                  }
                  & .order-address {
                    font-style: italic;
                    font-weight: 600;
                  }
                  & .order-total {
                    font-size: 20px;
                    font-weight: 700;
                    color: var(--primary-color-2);
                  }
                }
              }

              & > .message.image {
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                gap: 5px;
                padding: 7px;

                & > .images-container {
                  --item-max-width: 100px;
                  --gap: 7px;
                  --item-max-height: 100px;
                  width: fit-content;
                  height: fit-content;
                  display: flex;
                  flex-direction: column;
                  // flex-wrap: wrap;
                  gap: var(--gap);
                  justify-content: center;

                  & .image-item {
                    max-width: var(--item-max-width);
                    max-height: 100%;
                    flex: 1 1 var(--item-max-width);
                    cursor: pointer;
                    display: flex;
                    justify-content: center;

                    & > img {
                      max-width: 100%;
                      max-height: var(--item-max-height);
                      width: auto;
                      height: auto;
                      object-fit: contain;
                      border-radius: 3px;
                    }
                  }
                }

                & > .images-container.multi {
                  display: flex;
                  flex-wrap: wrap;
                  flex-direction: row;

                  & .image-item {
                    // width: auto;
                    // height: auto;
                    // max-width: calc(var(--item-max-width) - var(--gap));
                    // max-height: calc(var(--item-max-width) - var(--gap));;
                    height: fit-content;
                    width: calc((100% - var(--gap)) / 4);
                    max-width: calc((100% - var(--gap)) / 4);
                    // object-fit: contain;

                    & > img {
                      max-width: 100%;
                      max-height: 100%;
                      width: 100%;
                      aspect-ratio: 1;
                      height: auto;
                      object-fit: cover;
                      border-radius: 3px;
                    }
                  }
                }

                & > .time {
                  font-size: 10px;
                  color: #545454;
                  font-weight: 400;
                  line-height: 1.25;
                  margin-left: auto;
                  margin-top: 5px;
                  margin-bottom: 1px;
                  align-self: baseline;
                  user-select: none;
                }
              }
            }

            & > .date-group {
              width: 100%;
              text-align: center;
            }
          }

          & > .date-group {
            text-align: center;
            // font-style: italic;
            font-size: 12px;
            font-weight: 400;
            color: #a8a7a7;
            // background: var(--primary-color-1);
            border-radius: 2em;
            width: fit-content;
            padding: 2px 15px;
            align-self: center;
          }
        }

        & > .message-container.me {
          & > .message-group {
            flex-direction: row-reverse;
            & > .message-content-group{
              align-items: flex-end;
            }
            & .message {
              max-width: 80%;
              border-radius: 3px;
              background-color: color-mix(in srgb, var(--primary-color-1) 20%, transparent);
              cursor: pointer;
            }

            & .message.product {
              // max-width: 90% !important;

              & > .product-detail {
                border-color: var(--primary-color-1);
              }
            }
            & .message.order {
              // max-width: 90% !important;

              & > .order-info {
                border-color: var(--primary-color-1);
              }
            }

            & .message.image {
              & .image-item:last-child {
                display: flex;
                justify-content: flex-end;
              }
            }
          }
        }

        & .advanced-actions {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          margin-top: 10px;
          gap: 15px;
          border-radius: 5px;
          background: transparent;
          z-index: 1000;
          align-self: center;
          width: 100%;

          & > button.action {
            min-width: 60px;
            height: 60px;
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 3px;
            justify-content: center;
            align-items: center;
            color: #545454;
            background-color: white;
            box-shadow: 0 0 3px rgb(0, 0, 0, 0.2);
            border-radius: 7px;
            font-size: 13px;
            flex: 1;

            & > svg {
              font-size: 25px;
            }
          }
        }
      }
    }

    & .empty-list {
      height: 100%;
      flex: 1;
      justify-content: center;
      align-items: center;
      gap: 10px;
      padding: 15px;
      color: #686868;

      & > img {
        margin: 10px auto;
        justify-content: center;
        border-radius: 50%;
        height: 250px;
        width: 250px;
        object-fit: contain;
      }

      & > span {
        font-size: 1.5em;
        text-align: center;
      }
      & > span.refresh {
        color: var(--primary-color-2);
        font-weight: 700;
        cursor: pointer;
      }
    }
    & .load-more {
      position: absolute;
      bottom: 0;
      left: 0;
      padding-bottom: 15px;
      width: 100%;
      text-align: center;
      background: white;
    }
  }
}
.chat-detail-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f4f4f4;
  position: relative;
  font-size: 17px;
  overflow: auto;
  z-index: 1;
  height: 100%;
  border-radius: inherit;
  width: 100%;
  max-width: var(--max-width-content-view-1024);
  margin: 0 auto;

  & > .chat-header {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    max-width: inherit;

    & > .sub-title-v2-header {
      top: unset;
    }

    & > .language-select {
      background: white;
      display: flex;
      justify-content: space-between;
      color: #545454;
      padding: 5px 10px;
      z-index: 100;
    }

    & .header-left,
    .header-right {
      flex: 0;
    }
    & .header-middle {
      justify-content: flex-start;
      padding: 0 10px;
      align-items: center;
      text-align: left;
      flex: 1;
      display: flex;
    }
    & .chat-info {
      display: flex;
      gap: 7px;
      flex: 1;
      overflow: hidden;
      padding-right: 10px;

      & > .avatar-chat {
        width: 45px;
        height: 45px;
        border-radius: 5px;
        overflow: hidden;
        position: relative;

        & .single {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 5px;
          box-shadow: inset 0 0 0 2px white;
          & > .avatar-container {
            border-radius: 5px;
          }
          & > .shop-logo {
            position: relative;
            border: 0;
            border-radius: 5px;
            align-self: flex-start;
            box-shadow: none;
          }
        }

        & .online-dot {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: var(--linear-color-1);
          box-shadow: 0 0 0px 1px white;
          position: absolute;
          bottom: 5px;
          right: 5px;
          // animation: l1 2s infinite;

          @keyframes l1 {
            100% {
              box-shadow: 0 0 0 10px #0f00;
            }
          }
        }
      }
      & > .avatar-chat.group {
        display: flex;
        flex-wrap: wrap;
        gap: 1px;
        width: 70px;
        height: 70px;
        padding: 10px;

        & > .multiple {
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;

          & > .single {
            width: calc(100% / 2);
            height: calc(100% / 2);
            margin-left: 5px;
          }
          & > .single.odd {
            margin-left: -5px;
          }
          & > .single:not(.odd):not(:first-child) {
            margin-top: -5px;
          }

          & > .count {
            background: #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
            margin: -5px 0 0 -5px;
            z-index: 100;
            color: #8f8f8f;
          }
        }
      }

      & > .content-chat {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        flex: 1;
        overflow: hidden;
        // user-select: none;

        & > .name {
          font-size: 15px;
          font-weight: 700;
          text-align: left;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
          overflow: hidden;
        }

        & > .others-info {
          display: flex;
          justify-content: flex-start;
          & > .online-status,
          > .member-amout {
            display: flex;
            font-size: 13px;
            align-items: flex-start;
            justify-content: center;
            font-weight: 500;
            color: #9dff73;
            gap: 5px;
            line-height: normal;
            white-space: nowrap;
            text-align: left;

            & > svg {
              font-size: 15px;
            }
          }
        }
      }
    }
  }
  & > #head_of_list_message {
    text-align: center;
    font-size: 15px;
    display: flex;
    justify-content: center;

    & > .viewed-all {
      color: #686868;
      font-style: italic;
      font-size: 12px;
      padding: 10px;
    }
  }
  & > .chat-detail-content-container {
    display: flex;
    flex-direction: column-reverse;
    flex: 1;
    background-color: #f9f9f9;
    position: relative;
    font-size: 15px;
    padding: 10px 10px 10px 10px;

    & > .message-container {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-top: 5px;

      & > .message-group {
        display: flex;
        flex-direction: row;
        gap: 5px;
        & > .sender-avatar {
          width: 32px;
          height: 32px;
          border: 2px solid white;
          box-shadow: 0 0 4px 0 rgb(0, 0, 0, 0.25);
          border-radius: 50%;
          align-self: flex-start;

          & > img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        & > .sender-avatar.hide {
          opacity: 0;
        }
        & > .sender-avatar.product-message {
          opacity: 1 !important;
          margin-bottom: auto;
          margin-top: 10px;
        }

        & > .message-content-group {
          display: flex;
          flex-direction: column;
          max-width: 100%;
          flex: 1;
          align-items: flex-start;
          max-width: 100%;
          gap: 5px;
          position: relative;
          & > .message {
            padding: 5px 10px;
            border-radius: 3px;
            max-width: 70%;
            background-color: white;
            white-space: break-spaces;
            word-break: break-word;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            font-weight: 400;
            column-gap: 5px;
            color: black;
            font-size: 15px;
            align-items: flex-end;
            line-height: 1.25;
            // user-select: none;

            & > .time {
              font-size: 10px;
              color: #545454;
              font-weight: 400;
              line-height: 1.25;
              margin-left: auto;
              margin-top: auto;
              margin-bottom: 1px;
              align-self: baseline;
              user-select: none;
            }
          }
          & > .time {
            font-size: 10px;
            color: #545454;
            font-weight: 400;
            line-height: 1.25;
            align-self: baseline;
            user-select: none;
            flex: 1;
            text-align: center;
            margin: 5px auto;
          }
          & > .message.show-origin {
            border: thin solid var(--primary-color-1);
            border-radius: 5px;
            background: white;
          }
          & > .message.link {
            word-break: break-all;
            color: rgb(0 107 179);
          }

          & > .message.product {
            display: flex;
            flex-direction: column;
            background-color: transparent !important;
            max-width: 100% !important;
            gap: 5px;
            padding: 0;
            min-width: unset;
            width: 100%;
            margin-top: 10px;
            & > .product-detail {
              display: flex;
              flex: 1;
              width: 100%;
              padding: 7px;
              gap: 7px;
              max-width: 100% !important;
              align-items: center;
              justify-content: flex-start;
              border-radius: 3px !important;
              background-color: white !important;
              border: 2px solid var(--primary-color-2);

              & > img {
                width: 75px;
                height: 75px;
                object-fit: cover;
                border-radius: 0;
                padding: 0;
                margin-bottom: auto;
              }
              & > .product-info {
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 5px;
                flex: 1;
                & > .product-name {
                  font-size: 14px;
                  font-weight: 800;
                  line-height: 20px;

                  color: var(--primary-color-1);
                }

                & > .price-info {
                  display: flex;
                  color: var(--primary-color-2);
                  font-size: 20px;
                  font-weight: 900;

                  & > .product-price {
                    display: flex;
                    flex-direction: column;
                    line-height: 1.1;
                  }

                  & em.off {
                    font-weight: 400;
                    font-size: 13px;
                    color: #545454;
                    font-style: normal;
                    text-decoration: line-through;
                  }
                }
              }
            }
          }
          & > .message.order {
            display: flex;
            flex-direction: column;
            background-color: transparent !important;
            max-width: 100% !important;
            gap: 5px;
            padding: 0;
            min-width: unset;
            width: 100%;
            margin-top: 10px;
            & > .order-info {
              display: flex;
              flex-direction: column;
              font-size: 15px;
              color: #545454;
              flex: 1;
              line-height: normal;
              width: 100%;
              padding: 7px;
              max-width: 100% !important;
              align-items: flex-start;
              justify-content: flex-start;
              border-radius: 3px !important;
              background-color: white !important;
              border: 2px solid var(--primary-color-2);

              & .order-code {
                font-weight: 700;
                color: var(--primary-color-2);
              }
              & .order-at {
                font-style: italic;
                font-size: 13px;
                margin-left: 10px;
              }
              & .order-customer-name {
                color: var(--primary-color-1);
                font-weight: 700;

                & > em {
                  font-size: 13px;
                  font-style: italic;
                  font-weight: 500;
                  color: #545454;
                }
              }
              & .order-address {
                font-style: italic;
                font-weight: 600;
              }
              & .order-total {
                font-size: 20px;
                font-weight: 700;
                color: var(--primary-color-2);
              }
            }
          }

          & > .message.image {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            gap: 5px;
            padding: 7px;

            & > .images-container {
              --item-max-width: 100%;
              --gap: 7px;
              --item-max-height: 400px;
              width: fit-content;
              height: fit-content;
              display: flex;
              flex-direction: column;
              // flex-wrap: wrap;
              gap: var(--gap);
              justify-content: center;

              & .image-item {
                max-width: var(--item-max-width);
                max-height: 100%;
                flex: 1 1 var(--item-max-width);
                cursor: pointer;
                display: flex;
                justify-content: center;

                & > img {
                  max-width: 100%;
                  max-height: var(--item-max-height);
                  width: auto;
                  height: auto;
                  object-fit: contain;
                  border-radius: 3px;
                }
              }
            }

            & > .images-container.multi {
              display: flex;
              flex-wrap: wrap;
              flex-direction: row;

              & .image-item {
                // width: auto;
                // height: auto;
                // max-width: calc(var(--item-max-width) - var(--gap));
                // max-height: calc(var(--item-max-width) - var(--gap));;
                height: fit-content;
                width: calc((100% - var(--gap)) / 2);
                max-width: calc((100% - var(--gap)) / 2);
                // object-fit: contain;

                & > img {
                  max-width: 100%;
                  max-height: 100%;
                  width: 100%;
                  aspect-ratio: 1;
                  height: auto;
                  object-fit: cover;
                  border-radius: 3px;
                }
              }
            }

            & > .time {
              font-size: 10px;
              color: #545454;
              font-weight: 400;
              line-height: 1.25;
              margin-left: auto;
              margin-top: 5px;
              margin-bottom: 1px;
              align-self: baseline;
              user-select: none;
            }
          }

          & > .message.retract {
            font-weight: 400;
            font-style: italic;
            color: #6c6c6c;
          }

          & > .send-error {
            color: var(--primary-color-2);
            min-width: unset;
            font-size: 12px;
          }

          & > .translate-info {
            font-size: 12px;
            color: #686868;
            margin-bottom: 5px;

            & > .show-origin-message-button {
              background: white;
              padding: 0 10px;
              border-radius: 2em;
              margin-left: 7px;
              box-shadow: 0 0 1px 0px #ccc;
            }
          }
        }

        & > .date-group {
          width: 100%;
          text-align: center;
        }
      }

      & > .date-group {
        text-align: center;
        // font-style: italic;
        font-size: 12px;
        font-weight: 400;
        color: #a8a7a7;
        // background: var(--primary-color-1);
        border-radius: 2em;
        width: fit-content;
        padding: 2px 15px;
        align-self: center;
      }
    }

    & > .message-container.me {
      & > .message-group {
        flex-direction: row-reverse;
        & > .message-content-group {
          align-items: flex-end;

          & > .advanced-actions {
            left: unset;
            right: 5px;
          }
        }
        & .message {
          max-width: 80%;
          border-radius: 3px;
          background-color: color-mix(
            in srgb,
            var(--primary-color-1) 20%,
            transparent
          );
          cursor: pointer;
        }

        & .message.product {
          // max-width: 90% !important;

          & > .product-detail {
            border-color: var(--primary-color-1);
          }
        }
        & .message.order {
          // max-width: 90% !important;

          & > .order-info {
            border-color: var(--primary-color-1);
          }
        }

        & .message.image {
          & .image-item:last-child {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }

    & > .message-container.last-message {
      margin-bottom: 10px;
    }
  }

  & > .chat-detail-content-container.empty {
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    max-width: var(--max-width-content-view-1024);
    margin: 0 auto;

    & > img {
      width: 200px;
    }

    & > span {
      color: black;
      font-size: 13px;
      font-weight: 400;
      text-align: center;
    }
    & > span + a {
      color: var(--primary-color-2);
      font-size: 13px;
      font-weight: 700;
      text-align: center;
    }
  }

  & > .chat-container {
    display: flex;
    flex-direction: column;
    position: sticky;
    bottom: 0;
    z-index: 10;
    margin-top: -50px;
    background: white;
    border-top: thin solid #ececec;
    width: 100%;
    max-width: var(--max-width-content-view-1024);
    margin: 0 auto;

    & > .chat-input {
      padding: 5px;
      display: flex;
      gap: 5px;
      // background: var(--primary-color-1);
      position: relative;
      border-top: thin solid #ececec;
      border-bottom: thin solid #ececec;

      & > .chat-input-group {
        flex: 1;
        border-radius: 2em;
        padding: 0;
        background: white;
        display: flex;

        & > .select-emoji-button {
          color: #a8a7a7;
          background: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          font-weight: 600;
          height: 40px;
          width: 40px;
          align-self: center;

          & > svg {
            font-size: 30px;
          }

          & input {
            opacity: 0;
            width: 40px;
            height: 40px;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: 2;
          }

          & input::file-selector-button {
            cursor: pointer;
          }
        }
        & > textarea,
        .input-chat {
          flex: 1;
          font-size: 17px;
          outline: none;
          display: flex;
          align-items: center;
          resize: none;
          overflow: auto;
          background: #f4f4f4;
          border-radius: 3px;
          padding: 0 0 0 15px;
          margin-right: 10px;

          & > textarea {
            flex: 1;
            outline: none;
            resize: none;
            background-color: transparent !important;
          }
        }

        & > .send-button {
          width: 40px;
          height: 40px;
          margin: 3px 0;
          display: flex;
          justify-content: center;
          align-items: center;
          align-self: center;
          color: var(--primary-color-1);
          font-size: 30px;
          border-radius: 50%;
        }
        & > .media-message-button {
          width: 40px;
          height: 40px;
          margin: 3px 0;
          display: flex;
          justify-content: center;
          align-items: center;
          align-self: center;
          color: #a8a7a7;
          font-size: 30px;
          border-radius: 50%;
        }
        & > .send-button:hover,
        > .send-button:active {
          background: #f4f4f4;
        }

        & > .image-info-action {
          display: flex;
          gap: 15px;
          flex: 1;
          font-size: 13px;
          line-height: 1;
          justify-content: center;
          align-items: center;
          align-self: center;

          & > .image-count {
            font-weight: 400;
            color: #545454;
            font-size: 15px;

            & > em {
              font-weight: 700;
              color: var(--primary-color-2);
              font-style: normal;
            }
          }

          & > button {
            color: var(--primary-color-2);
            font-weight: 700;
          }
        }
      }

      & > .typing-label {
        position: absolute;
        bottom: 100%;
        left: 0;
        background: var(--secondary-color-2);
        color: #686868;
        font-style: italic;
        border-top-right-radius: 2em;
        font-size: 13px;
        padding: 0 15px;
        width: -moz-fit-content;
        width: fit-content;
        min-width: 150px;
        display: flex;
        align-items: flex-end;
        gap: 5px;

        & > svg {
          height: 100%;
          margin-bottom: 1px;
        }
      }
    }

    & > .chat-media-preview {
      display: flex;
      justify-content: flex-start;
      // padding: 5px;
      border-bottom: 2px solid white;

      & > .image-message {
        display: flex;
        flex-direction: column;
        gap: 5px;
        padding: 10px 10px 0;
        justify-content: flex-start;
        align-items: flex-start;
        overflow: auto;
        height: fit-content;
        flex: 1;

        & .img-preview {
          width: 150px;
          min-width: 150px;
          height: 150px;
          object-fit: cover;
          border-radius: 10px;
          position: relative;

          & > img {
            width: 100%;
            height: 100%;
            border-radius: inherit;
            object-fit: cover;
            border: 1px solid #ddd;
            user-select: none;
            pointer-events: none;
          }
          & > .delete-image-button {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 25px;
            height: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            border-radius: 50%;
            background: rgb(0, 0, 0, 0.2);
          }
        }
      }

      & > .link-product-order-message {
        padding: 7px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        flex: 1;
        position: relative;

        & > .link-product-order-header {
          display: flex;
          padding: 0 3px;
          color: #545454;
          font-weight: 700;
          font-size: 15px;
          justify-content: space-between;

          & > button {
            color: #cccbcb;
            font-size: 15px;
          }
        }

        & > .link-product-order-detail {
          display: flex;
          gap: 5px;
          padding: 7px;
          border: thin solid #f8f6f6;
          border-radius: 3px;

          & > img {
            width: 75px;
            height: 75px;
            object-fit: cover;
          }
          & > .product-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 5px;
            & > .product-name {
              font-size: 14px;
              font-weight: 800;
              line-height: 20px;

              color: var(--primary-color-1);
            }

            & > .price-info {
              display: flex;
              color: var(--primary-color-2);
              font-size: 20px;
              font-weight: 900;

              & > .product-price {
                display: flex;
                flex-direction: column;
                line-height: 1.1;
              }

              & em.off {
                font-weight: 400;
                font-size: 13px;
                color: #545454;
                font-style: normal;
                text-decoration: line-through;
              }
            }
          }

          & > .send-product-order-message {
            color: white;
            background: var(--primary-color-2);
            font-weight: 700;
            font-size: 15px;
            padding: 7px 15px;
            height: fit-content;
            border-radius: 3px;
            align-self: flex-end;
            margin-left: auto;
          }

          & > .order-info {
            display: flex;
            flex-direction: column;
            font-size: 15px;
            color: #545454;
            flex: 1;
            line-height: normal;

            & .order-code {
              font-weight: 700;
              color: var(--primary-color-2);
            }
            & .order-at {
              font-style: italic;
              font-size: 13px;
              margin-left: 10px;
            }
            & .order-customer-name {
              color: var(--primary-color-1);
              font-weight: 700;

              & > em {
                font-size: 13px;
                font-style: italic;
                font-weight: 500;
                color: #545454;
              }
            }
            & .order-address {
              font-style: italic;
              font-weight: 600;
            }
            & .order-total {
              font-size: 20px;
              font-weight: 700;
              color: var(--primary-color-2);
            }
          }
        }
      }
    }

    & > .emoji-select-container {
      height: 300px;
      max-height: 50dvh;

      & > .chat-emoji-picker {
        width: 100%;
        height: 100%;
        box-shadow: none;

        & > .v3-header {
          padding: 5px 10px;
        }
        & > .v3-footer {
          display: none;
        }

        & button {
          width: 35px;
          height: 35px;
          min-width: 35px;
          max-width: 35px;
          flex: none !important;
        }
      }
    }

    & > .media-select-container {
      padding: 15px 0;
      display: flex;

      & > .media-options {
        display: flex;
        flex: 1;
        justify-content: space-around;

        & > .media-option {
          color: #a8a7a7;
          font-size: 15px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          gap: 5px;
          font-weight: 400;
          cursor: pointer;

          & > div {
            background-color: #ececec;
            border-radius: 5px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            width: 50px;
            height: 50px;
            font-size: 30px;
          }

          & > input {
            display: none;
          }
        }
        & > .media-option.disabled {
          opacity: 0.5;
        }
      }
    }
  }
}
.language-dropdown-container {
  & .v-list {
    overflow: visible !important;
    width: fit-content;
  }
  & .v-list-item__content {
    width: 100%;
  }
  & .v-list-item--one-line {
    width: 100%;
    overflow: visible;
    max-width: unset;
  }
  & .lang-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 17px;
    gap: 5px;
    font-weight: 600;
    width: fit-content;
    overflow: visible;

    & svg {
      width: 35px;
    }
  }
}
.select-language-button {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #a8a7a7;
  font-size: 13px;

  & svg {
    width: 18px;
  }
}
.default-lang-icon {
  background-color: #f28b14;
  color: white;
  width: 35px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-size: 15px;
}

.translate-message-container {
  z-index: 10000;

  & .edit-translate-message {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    background: white;
    border-radius: 10px;

    & .edit-translate-message-input {
      border-radius: 3px;
      border: thin solid var(--primary-color-1);
      padding: 7px;
      background: white;
      display: flex;
      justify-content: center;
      align-items: center;

      & > textarea {
        outline: none;
        flex: 1;
        resize: none;
      }
    }

    & .edit-actions {
      display: flex;
      gap: 10px;

      & > button {
        padding: 10px;
        border-radius: 5px;
        min-width: 150px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        font-size: 15px;
        font-weight: 500;
      }

      & > button.save-edit {
        color: #545454;
        background: white;
      }
      & > button.close-edit {
        color: white;
        background: var(--primary-color-1);
      }
    }
  }
}
