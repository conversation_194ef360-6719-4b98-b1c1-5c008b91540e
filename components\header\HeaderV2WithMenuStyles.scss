.title-v2-with-menu-header {
  // font-size: 1.6em;
  --text-color: var(--primary-color-1);
  padding: 0 15px !important;
  margin: 0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1001;
  background: white;
  width: 100%;
  max-width: var(--max-width-view);
  height: 55px;
  max-height: 55px;
  // border-bottom: thin solid rgba(0, 0, 0, .5);
  transition: all 0.3s ease;
  // overflow: hidden;

  // @media screen and (max-width:1024px) {
  //   // font-size: 1.4em;
  // }
  &.hide-mobile {
    @media (max-width: 768px) {
      display: none !important;
    }
  }
  &.hide-desktop {
    // top: -55px;
    height: 0;
    overflow: hidden;
    & div {
      height: 0;
      opacity: 0;
    }
  }

  .language-dropdown-container {
    min-width: 120px;
    padding: 8px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: white;

    .lang-options-container {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .language-option {
        padding: 8px 12px;
        border-radius: 4px;
        text-align: left;
        transition: all 0.2s ease;
        background: transparent;
        border: none;
        cursor: pointer;
        font-size: 14px;
        
        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }

        &.active-language {
          color: var(--primary-color-2);
          background: rgba(var(--primary-color-2-rgb), 0.1);
          font-weight: 500;
        }
      }
    }
  }

  & h3 {
    margin: 0;
  }

  & .header-left {
    display: flex;
    justify-content: left;
    gap: 5px;
    height: 100%;
    max-height: inherit;
    width: fit-content;
    overflow: visible;
    // flex: 1;
    padding: 10px 0;
    transition: all .3s .5s ease-in-out;
    // margin-right: auto;

    &>a {
      overflow: visible;
      display: flex;
      white-space: nowrap;
    }

    & img {
      // height: 100%;
      object-fit: contain;
      height: 35px;
    }

    &>button.back-button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      height: 35px;
      min-width: fit-content;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      font-size: 30px;
      margin-right: 10px;
      color: var(--text-color);
      transition: all .3s cubic-bezier(0.075, 0.82, 0.165, 1);
    }

    & > .header-menu-button{
      flex: 1;
      border: none;
      // background: white;
      color: var(--color-text-note);
      font-size: 1em;
      height: 35px;
      // height: 55px;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      position: relative;
      padding: 5px 0;
      font-weight: bold;
      overflow: visible;
      display: flex;
      flex-direction: row;
      margin-right: 10px;
      // transition: all .3s cubic-bezier(0.075, 0.82, 0.165, 1);

      & svg{
        width: 30px;
        height: 30px;
        min-height: 30px;
        margin-right: 5px;
      }

      &.active{
        color: var(--text-color);
      }
    }

    & .custom-logo {
      
      width: 40px;
      height: 40px;
      min-height: 40px;
      margin-top: -2.5px;

      & > .shop-logo{
        box-shadow: 0 0 5px rgb(0, 0, 0, .2);
        // border-radius: 5px;
      }
    }
  }

  & .header-right {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    padding-right: 5px;
    height: 100%;
    width: fit-content;
    max-height: inherit;
    overflow: visible;
    padding: 10px 0;
    // transition: all .3s .5s ease-in-out;

    &>a {
      overflow: hidden;
      display: flex;
      justify-content: flex-end;

      white-space: nowrap;
    }

    & .open-shop-button {
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
      color: var(--text-color);
      gap: 5px;

      &>.label-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: right;
        font-size: 15px;
        line-height: 15px;

        &>.primary {
          text-transform: uppercase;
          font-weight: 700;
          line-height: 15px;
        }

        &>.secondary {
          color: #545454;
          font-weight: 700;
          font-size: 13px;
        }
      }

      &>svg {
        height: 100%;
        min-width: 40px;
      }
    }

    & .notification {
      width: 35px;
      min-width: 35px;
      color: var(--text-color);
      height: 35px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      position: relative;
      

      &>.notify-badge {
        color: var(--text-color);
        font-size: 30px;
        font-weight: 700;

        & .v-badge__badge {
          font-weight: 700;
          color: white;
        }
      }

      &>.count {
        position: absolute;
        top: 3px;
        right: -2px;
        padding: 0 5px;
        border-radius: 2em;
        background-color: #cc313a;
        color: white;
        font-size: 12px;
      }


    }

    & .notification-dropdown-container {
      max-width: 400px;
      min-width: 400px;
      max-height: 500px;
      overflow: auto;
      // padding: 10px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 0 5px rgb(0, 0, 0, 30%);
      display: flex;
      flex-direction: column;
      z-index: 1001;

      & .noti-label {
        font-size: 20px;
        font-weight: 700;
        color: #545454;
        top: 0;
        position: sticky;
        background: inherit;
        padding: 5px;
        z-index: 10;
        text-align: center;
        height: 40px;
        min-height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: thin solid #f5f5f5;

        &>.refresh-noti {
          position: absolute;
          right: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text-color);
          animation: none;
          width: 40px;
          height: 40px;
        }
      }
    }

    & .cart-overview-dropdown-container {
      max-width: 400px;
      min-width: 400px;
      max-height: 500px;
      overflow: auto;
      // padding: 10px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 0 5px rgb(0, 0, 0, 30%);
      display: flex;
      flex-direction: column;
      z-index: 1001;

      @media screen and (max-width: 1024px) {
        display: none;
        
      }
    }

    .cart-button {
      display: flex;
      padding: 0;
      width: 35px;
      height: 35px;
      min-width: unset;
      font-size: 25px;
      color: var(--text-color);
      margin-right: 5px;

      &>.v-btn__content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        &>a,
        img, div {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }

        & svg {
          width: 100%;
          height: 100%;
          font-size: 35px;
        }

        & .notify-badge {
          color: var(--text-color);
          font-size: 30px;
          font-weight: 700;
          position: absolute;
          // bottom: calc(100% - 5px);
          // left: calc(100% - 5px);


          & .v-badge__badge {
            font-weight: 700;
            color: white;
          }
        }

        & .cart-badge {
          position: absolute;
          // bottom: calc(100% - 5px);
          // left: calc(100% - 5px);
          font-size: 30px;
          font-weight: 700;
          right: 5px;
          top: 5px;

          & .v-badge__badge {
            font-weight: 700;
            color: white;
          }
        }
      }
    }

    .menu-button {
      display: flex;
      padding: 0;
      width: 35px;
      height: 35px;
      min-width: unset;
      font-size: 25px;
      color: var(--text-color);
      margin-right: 5px;

      @media screen and (max-width: 1024px) {
        // display: none;
      }

      &>.v-btn__content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        &>a,
        img {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }

        & svg {
          width: 100%;
          height: 100%;
          font-size: 35px;
        }

        & .notify-badge {
          color: var(--text-color);
          font-size: 30px;
          font-weight: 700;
          position: absolute;
          bottom: calc(100% - 5px);
          left: calc(100% - 5px);


          & .v-badge__badge {
            font-weight: 700;
            color: white;
          }
        }

        & .menu-badge {
          position: absolute;
          bottom: calc(100% - 5px);
          left: calc(100% - 5px);
          font-size: 30px;
          font-weight: 700;

          & .v-badge__badge {
            font-weight: 700;
            color: white;
          }
        }
      }
    }

    & .menu-side-bar-button {
      display: flex;
      padding: 5px;
      width: 35px;
      height: 35px;
      min-width: unset;
      font-size: 35px;
      color: var(--text-color);

      @media screen and (min-width: 1025px) {
        display: none;
      }

      & .menu-badge {
        position: absolute;
        bottom: calc(100% - 5px);
        left: calc(100% - 5px);
        font-size: 30px;
        font-weight: 700;

        & .v-badge__badge {
          font-weight: 700;
          color: white;
        }
      }
    }
  }

  .header-middle {
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    white-space: nowrap;

    display: flex;
    justify-content: space-evenly;
    align-items: center;
    // box-hadow: 0px 0px 7px -1px rgb(0, 0, 0, 0.2);
    z-index: 1000;
    margin-top: auto;
    max-width: var(--max-width-content-view-720);
    width: 100%;

    // max-height: 50px;
    .footer-button {
      flex: 1;
      border: none;
      background: white;
      color: var(--color-text-note);
      font-size: 1em;
      height: 55px;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      position: relative;
      padding: 5px 0;
      font-weight: bold;
      overflow: visible;

      &>.middle-button {
        width: 60px;
        height: 60px;
        min-height: 60px;
        margin-top: -10px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0px -4px 4px 0 rgb(0, 0, 0, 5%);
        padding: 7px;

        &>div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(to top, var(--text-color), var(--linear-color-1));
          border-radius: inherit;
          color: white;
          border: 3px solid white;
          box-shadow: 0 4px 8px 0px rgb(0, 0, 0, 10%);
          font-size: 25px;
        }
      }

      >img {
        width: 2em;
        height: 2em;
        filter: grayscale(1);
      }

      &>em {
        border-radius: 2em;
        color: white;
        background: var(--primary-color-2);
        min-width: 15px;
        height: 15px;
        padding: 0 2px;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-style: normal;
        position: absolute;
        top: 3px;
        left: 50%;
        line-height: 1;
        font-weight: 600;

        &>span {
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .footer-button>svg {
      font-size: 25px;
    }

    .footer-button>span {
      font-size: 13px;
      display: flex;
      font-weight: 800;
    }

    .footer-button.active {
      /* background-color: var(--primary-color-1); */
      color: var(--text-color) !important;
      box-shadow: none !important;

      &>img {
        filter: grayscale(0);
      }
    }

    @keyframes shakeCart {
      25% {
        transform: translateX(6px);
      }

      50% {
        transform: translateX(-4px);
      }

      75% {
        transform: translateX(2px);
      }

      100% {
        transform: translateX(0);
      }
    }

    .shake {
      animation: shakeCart 0.4s ease-in-out forwards;
    }

    &>a {
      width: 100%;
      text-align: center;
      text-decoration: none;
      display: flex;
    }


    &.mobile {
      display: none;
    }

    @media screen and (max-width: 1024px) {
      &.mobile {
        display: flex;
        height: 100%;
        padding: 10px 0;
      }

      &>a {
        justify-content: center;
        height: 100%;
      }

      & img {
        height: 100%;
        object-fit: contain;
      }

    }
  }
}

.title-v2-with-menu-header.hide-mobile {
  // position: absolute;
  // top: 0;
  // top: -100%;
  @media screen and (max-width: 1024px) {
    height: 0;
    padding: 0;
    // opacity: 0;
    overflow: hidden;  

    & div {
      height: 0;
      opacity: 0;
    }
  }
  // & .header-left{
  //   display: none;
  // }
}

// .title-v2-with-menu-header.hide-desktop {
//   position: absolute;
//   top: 0;
//   top: -100%;

//   @media screen and (min-width: 1025px) {
//     height: 0;
//     padding: 0;
//     // opacity: 0;
//     overflow: hidden;  
//     & div {
//       opacity: 0;
//       height: 0;
//     }
//   }
//   // & .header-left{
//   //   display: none;
//   // }
// }

.title-v2-with-menu-header.custom-header-for-temp-1{
  // background-image: url("~/assets/imageV2/shop-bg.jpg");
  // background-size: cover;
  // background-position: center;
  --text-color: var(--temp-color-2);
  background: var(--temp-color-1);
  // overflow: hidden;
}