<template>
    <div class="public-container">
        <div class="about-container">
            <!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="()=>{
					close();
				}">
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.AboutComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
            <SubHeaderV2Component :title="$t('AppRouteTitle.AboutComponent')">
                <template v-slot:header_right>

                </template>
            </SubHeaderV2Component>
            <div class="about-content">
                <div class="about-us">
                    <div class="content-title">
                        <span>
                            {{ $t('AboutComponent.ve_chung_toi') }}
                        </span>
                    </div>
                    <div class="content">
                        <span class="name">
                            {{ name }}
                        </span>
                        <span>
                            <strong>{{ $t('AboutComponent.dia_chi') }}: </strong> {{ address }}
                        </span>
                        <span>
                            <strong>{{ $t('AboutComponent.so_dien_thoai') }}: </strong> {{ phone }}
                        </span>
                        <span>
                            <strong>{{ $t('AboutComponent.email') }}: </strong> {{ email }}
                        </span>
                    </div>
                </div>

                <div class="quick-link">
                    <div class="content-title">
                        <span>
                            {{ $t('AboutComponent.lien_ket_nhanh') }}
                        </span>
                    </div>
                    <div class="content">
                        <!-- <nuxt-link>{{ $t('AboutComponent.gioi_thieu') }}</nuxt-link> -->
                        <!-- <nuxt-link>{{ $t('AboutComponent.tin_tuc') }}</nuxt-link>
                        <nuxt-link>{{ $t('AboutComponent.lien_he') }}</nuxt-link> -->
                        <nuxt-link :to="appRoute.PolicyComponent">{{ $t('AboutComponent.chinh_sach') }}</nuxt-link>
                        <nuxt-link :to="appRoute.SupportComponent">{{ $t('AboutComponent.ho_tro') }}</nuxt-link>
                    </div>
                </div>

                <div class='v-stack socials'>
                    <div class='content-title'>
                        <span>
                            {{ $t("ProfileComponent.mang_xa_hoi") }}
                        </span>
                    </div>

                    <div class='h-stack social-detail-options'>
                        <nuxt-link class="other-option social-option" target="_blank"
                            :to="environment.appConfig.app_facebook" :title="$t('ProfileComponent.fan_page')">
                            <Icon name="logos:facebook" class="social-icon"></Icon>
                        </nuxt-link>
                        <nuxt-link class="other-option social-option" target="_blank"
                            :to="environment.appConfig.app_zalo_oa" :title="$t('ProfileComponent.nhan_tin_qua_zalo')">
                            <img :src="logo_zalo" loading="lazy" class="social-icon">
                        </nuxt-link>
                        <nuxt-link class="other-option social-option" target="_blank"
                            :to="environment.appConfig.app_tiktok" :title="$t('ProfileComponent.kenh_tiktok')">
                            <Icon name="logos:tiktok-icon" class="social-icon"></Icon>
                        </nuxt-link>
                    </div>

                    <div class='content-title'>
                        <span>
                            {{ $t("ProfileComponent.tai_ung_dung") }}
                        </span>
                    </div>
                    <div class='h-stack other-detail-options download-app-detail-options' v-if="!webInApp">
                        <nuxt-link class="other-option download-app-option" target="_blank"
                            :to="environment.appConfig.app_ch_play_download"
                            :title="$t('ProfileComponent.tai_qua_ch_play')">
                            <Icon name="logos:google-play-icon"></Icon>
                            <div class="v-stack">
                                <em class="android">Get it on</em>
                                <span>Google Play</span>
                            </div>
                        </nuxt-link>
                        <nuxt-link class="other-option download-app-option" target="_blank"
                            :to="environment.appConfig.app_app_store_download"
                            :title="$t('ProfileComponent.tai_qua_app_store')">
                            <Icon name="mdi:apple"></Icon>
                            <div class="v-stack">
                                <em>Download on the</em>
                                <span>App Store</span>
                            </div>
                        </nuxt-link>
                    </div>
                </div>
            </div>

        </div>
    </div>

</template>
<style lang="scss" src="./AboutStyles.scss" scoped></style>
<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import environment from '~/assets/environment/environment';
import logo_zalo from "~/assets/image/Logo-Zalo-Arc.webp";

const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.AboutComponent')
});
let router = useRouter();

let name = ref("");
let address = ref("");
let phone = ref("");
let email = ref("");

var webInApp = ref(null as any);
onMounted(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    name.value = environment.appConfig.app_company_name;
    address.value = environment.appConfig.app_company_address;
    phone.value = environment.appConfig.app_company_phone;
    email.value = environment.appConfig.app_company_email;
})

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
</script>