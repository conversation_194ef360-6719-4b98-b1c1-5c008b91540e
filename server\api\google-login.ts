import { OAuth2Client } from "google-auth-library";
import environment from "~/assets/environment/environment";

export default defineEventHandler(async (event) => {
  
  const body = await readBody(event);
  const token = body.token;
  const user = await verify(token);
  return user;
});

async function verify(token: string) {
  const client = new OAuth2Client(environment.GoogleIDApp, environment.secretKeyGoogleLogin);
  const ticket = await client.getTokenInfo(token);
  // const payload = ticket.();
  return ticket;
}
