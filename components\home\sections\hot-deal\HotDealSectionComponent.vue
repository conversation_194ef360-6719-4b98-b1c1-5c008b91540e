<template>
	<div class="hot-deal content-list">
		<div class="stack-content-title">
			<span class="section-title">
				<Icon name="pajamas:duo-chat"></Icon>
				{{ $t('HomeV2Component.tin_don_gan_day') }}
			</span>
			<nuxt-link :to="'#'" class="view-all" v-if="false">
				{{ $t('HomeV2Component.xem_tat_ca') }}
				<Icon name="ic:round-chevron-right"></Icon>
			</nuxt-link>
		</div>
		<div class="stack-content-list">
			<Swiper v-if="listVideo?.length" class="my-carousel stack-carousel best-around-carousel"
				:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
				:slides-per-group-auto="true" :simulateTouch="true" :loop="false" :effect="'creative'"
				:navigation="true" :freeMode=false :autoplay="false" :spaceBetween="10" @init="(e) => {
					list_video_el = e
				}" key="shop-around-carousel" ref="list_video_el">
				<SwiperSlide class="item-stack-slide best-around-stack" v-for="item of listVideo"
					:key="'best_around_' + item.id">
					<nuxt-link :dragable="false" :to="'#'" class="item-stack" :title="item.file_name"
						v-if="item.file_type == 'video'">
						<div class="item-stack-header">
							<span>
								<Icon name="solar:eye-scan-bold"></Icon>
								{{ showView(item.view_total || 0) }}
							</span>
						</div>

						<div class='primary-content' v-on:click="(e: any) => {
							e.stopPropagation();
							e.preventDefault();
						}">
							<client-only v-if="item.file_type == 'video'">
								<VideoHLSPlayerComponent :src="item.file_path"
									:video_id="item.id"
									:thumbnail="item.thumbnail ? (domainImage + item.thumbnail?.path) : null"
									:height="'100%'" :controls="false" v-on:clickOnPlaying="(e) => {
										// router.push(appRoute.DetailShopComponent + '/' + item.slug)
									}">
								</VideoHLSPlayerComponent>
							</client-only>

							<img v-else :src="item.file_path" />
						</div>
						<div class="item-stack-footer">
							<span class="video-name">{{ item.file_name }}</span>
							<!-- <span class="shop-address">{{ item.address }}</span> -->
						</div>
					</nuxt-link>
					<client-only>
						<div class="item-stack tiktok-item" v-if="item.file_type == 'tiktok'">
							<blockquote class="tiktok-embed" :cite="item.file_path" :data-video-id="item.extra_data?.id"
								style="max-width: 400px;min-width: 175px;">
								<section>
									<a target="_blank" :title="`@${item.extra_data?.channel_name}`"
										:href="`${item.extra_data?.channel}`">@{{
											item.extra_data?.channel_name }}
									</a>
									<p>{{ item.description }}
									</p>
								</section>
							</blockquote>
						</div>

					</client-only>
				</SwiperSlide>
			</Swiper>
			<div v-else class="none-content-list">
				<!-- {{ loadingHotDeal ? '' : $t('HomeV2Component.chua_co_uu_dai') }} -->
			</div>
			<v-overlay v-model="loadingVideos" :z-index="100" :absolute="false" contained
				content-class='spinner-container' persistent scrim="#fff" key="loading_hot_deal" no-click-animation>
				<Icon name="eos-icons:loading"></Icon>
			</v-overlay>
		</div>
	</div>

</template>

<style lang="scss" src="./HotDealSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();

const props = defineProps({
	filter_data: null
})
var publicService = new PublicService();

var list_video_el = useState<any>(() => { return null });
var listVideo = useState<any>('dashboard_video', () => { return [] });
var loadingVideos = ref(false);

var listHotDeal = useState('dashboard_hot_deal' as any);
var loadingHotDeal = ref(false);

onUnmounted(async () => {
});
onMounted(async () => {
	if (!listVideo.value?.length) {
        loadingVideos.value = true;
        publicService.listVideo().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                listVideo.value = res.body.data ? JSON.parse(JSON.stringify(res.body.data)) : []
            }

            loadingVideos.value = false;
            loadTikTokScript();
            // console.log('loadTikTokScript');
        });
    }

	// publicService.dashboard({...body, section: 'hot_deal'}).then(res => {
    //     if (res.status == HttpStatusCode.Ok) {
    //         listHotDeal.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
    //     }

    //     loadingHotDeal.value = false;
    // });
});

const loadTikTokScript = () => {
    if (!document.querySelector("script[src='https://www.tiktok.com/embed.js']")) {
        const script = document.createElement('script');
        script.src = 'https://www.tiktok.com/embed.js';
        script.async = true;
        document.body.appendChild(script);
    }
};

function showView(amount: number) {
    if (amount > Math.pow(1000, 3)) return `${(amount / Math.pow(1000, 3)).toFixed(amount % Math.pow(1000, 3) != 0 ? 1 : 0)} B`;
    if (amount > Math.pow(1000, 2)) return `${(amount / Math.pow(1000, 2)).toFixed(amount % Math.pow(1000, 2) != 0 ? 1 : 0)} M`;
    if (amount > Math.pow(1000, 1)) return `${(amount / Math.pow(1000, 1)).toFixed(amount % Math.pow(1000, 1) != 0 ? 1 : 0)} K`;
    return amount
}

</script>
