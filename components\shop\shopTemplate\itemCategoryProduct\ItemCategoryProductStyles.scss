.category-products-container-1 {
  // background: #f5f6fa;
  margin-top: 50px;
  border-radius: 10px 10px 0 0;
  display: flex;
  flex-direction: column;
  padding: 5px;
  // gap: 10px;
  // box-shadow: 0 0 15px rgb(0, 0, 0, 0.1);
  width: 100%;
  border-radius: 13px;
  background: var(--temp-color-4);
  // background: white;
  position: relative;
  transition: opacity 0.5s ease, transform 0.5s ease;
  // border-bottom: thin solid #efefef;

  &.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  &.animate-out {
    opacity: 0;
    transform: translateY(50px);
  }

  &::before {
    content: "";
    position: absolute;
    width: calc(100% - 25px);
    height: 10px;
    border-radius: 10px 10px 0 0;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-50%);
    left: 50%;
    bottom: 100%;
    z-index: 0;
  }

  &::after {
    content: "";
    position: absolute;
    width: calc(100% - 25px);
    height: 10px;
    border-radius: 0 0 10px 10px;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-50%);
    left: 50%;
    top: 100%;
    z-index: 0;
  }

  &>.category-title {
    // font-size: 13px;
    // color: white;
    // text-transform: uppercase;
    // width: fit-content;
    // border-radius: 7px;
    // line-height: normal;
    // font-weight: 700;
    // display: flex;
    // align-items: center;
    // padding: 5px 10px;
    // background: var(--temp-color-5);
    // margin: auto;
    flex: 1;
    border-radius: 5px;
    min-height: 150px;
    // background-image: url("~/assets/imageV2/shop-bg.jpg");
    display: flex;
    position: relative;
    margin: 0px 0px 10px;
    position: relative;
    box-shadow: rgba(26, 47, 51, 0.11) 0px 5px 10px 0px;
    overflow: hidden;

    &::before {
      content: "";
      background-image: var(--category-image);
      background-size: cover;
      background-position: center;
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      z-index: 1;
      filter: var(--category-filter);
      // filter: contrast(.1);
    }

    &>span {
      // font-size: 25px;
      font-size: calc(25px + var(--font-resize));
      color: white;
      text-transform: uppercase;
      width: fit-content;
      border-radius: 7px;
      line-height: normal;
      font-weight: 700;
      display: flex;
      align-items: center;
      padding: 5px 10px;
      // background: var(--temp-color-5);
      margin: auto;
      display: flex;
      flex-direction: column;
      margin: auto;
      z-index: 3;

      &::after {
        content: "";
        display: block;
        width: 100%;
        height: 5px;
        background-color: var(--temp-color-5);
        margin-top: 5px;
        border-radius: 2em;
      }

      &>em {
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        color: #cecece;
      }
    }
  }

  &>.category-products-list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    // justify-content: center;
    gap: 7px;
    padding: 5px 0;
    margin-top: 15px !important;
  }

  & > .container{
    margin: auto;
    width: 100%
  }

  @media screen and (min-width: 576px) {
    .container {
      max-width: 540px;
    }
  }
  @media screen and (min-width: 768px) {
    .container {
      max-width: 720px;
    }
  }
  @media screen and (min-width: 992px) {
    .container {
      max-width: 960px;
    }
  }
  @media screen and (min-width: 1200px) {
    .container {
      max-width: 1140px;
    }
  }
}
