<script setup lang="ts">
import { appConst, domainImage, formatCurrency, showTranslateProductName, validPhone, formatNumberToShort } from '~/assets/AppConst';

import hot_sale from "~/assets/imageV2/hot-sale.svg";
import discount_tag from "~/assets/image/sale_off_badge_background.png";
import shop_banner_temp from "~/assets/imageV2/logo-landscape-1024.jpg";
import shop_bg from "~/assets/imageV2/shop-bg.jpg";
import non_avatar from '~/assets/image/non-avatar.jpg';

import { condition_relationship, type delivery_fee_config } from '~/components/myShop/shopConfig/ShopConfigDTO';
import { AuthService } from '~/services/authService/authService';

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n()

const authService = new AuthService();

var props = defineProps({
	mode: null,
	shop_data: null,
	css_data: null,
	banner: null,
	background: null
})

var userInfo = ref();

const tempPromotions: delivery_fee_config[] = [{
	id: 1,
	name: 'ten_khuyen_mai',
	description: 'mo_ta_khuyen_mai',
	type: 'percent',
	value: 50,
	max_value: 25000,
	enabled: true,
	conditions: [],
	relationship_condition: condition_relationship.and
}];

const dataShopTempProducts: any[] = [
	{
		id: "product_1",
		name: "sản phẩm 1",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	},
	{
		id: "product_2",
		name: "sản phẩm 2",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	},
	{
		id: "product_3",
		name: "sản phẩm 3",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
		categories: [
			{
				name: "111 sửa",
				translation: [
					{
						object_type: 4,
						language_code: "vi",
						name: "111 sửa",
						description: "111 sửa"
					},
					{
						object_type: 4,
						language_code: "en",
						name: "111 fix",
						description: "111 fix"
					},
					{
						object_type: 4,
						language_code: "zh",
						name: "111 修",
						description: "111 修"
					},
					{
						object_type: 4,
						language_code: "ko",
						name: "111 수정",
						description: "111 수정"
					}
				]
			}
		]
	},
	{
		id: "product_4",
		name: "sản phẩm 4",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	}
]

const dataShopTempCategories: any[] = [
	{
		id: "category_1",
		name: "danh mục 1",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 1",
				description: "danh mục 1"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 1",
				description: "Category 1"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 1",
				description: "카테고리 1"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 1",
				description: "Категория 1"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_2",
		name: "danh mục 2",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 2",
				description: "danh mục 2"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 2",
				description: "Category 2"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 2",
				description: "카테고리 2"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 2",
				description: "Категория 2"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_3",
		name: "danh mục 3",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 3",
				description: "danh mục 3"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 3",
				description: "Category 3"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 3",
				description: "카테고리 3"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 3",
				description: "Категория 3"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_4",
		name: "danh mục 4",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 4",
				description: "danh mục 4"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 4",
				description: "Category 4"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 4",
				description: "카테고리 4"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 4",
				description: "Категория 4"
			}
		],
		products: dataShopTempProducts
	}
]

var showGrid = ref(false)
var open_time_list = ref([] as any);
var showSearchBar = ref(false);

watch(() => [
	props.css_data?.color_2
], () => {
	nuxtApp.$emit('change_header_for_shop_temp_1', {
		_color_2: props.css_data?.color_2
	});
})

onUnmounted(async () => {
});

onMounted(async () => {
	userInfo = await authService.checkAuth()

	console.log(props);
	open_time_list.value = formatOpeningHours(JSON.parse(props.shop_data?.open_hours));
	// useServerCache({
	// 	key: `shop_space_${shopDetails.value.id}`,
	// 	duration: 60 * 60, // Cache for 1 hour
	// 	condition: () => !!shopDetails.value.name // Cache only if productData.name is available
	// });
	nuxtApp.$emit('change_header_for_shop_temp_1', {
		_color_2: props.css_data?.color_2
	});
});
onBeforeMount(async () => { });
function formatOpeningHours(hoursObj: any) {
	function formatHours(hours: any) {
		return hours.map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`).join(' & ');
	}
	const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

	const result = [];
	if (hoursObj) {
		let currentGroup: any = [];
		let currentHours = hoursObj[days[0]];

		for (let i = 0; i < days.length; i++) {
			const day = days[i];
			const hours = hoursObj[day];

			if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
				currentGroup.push(t(`DayInWeek.${day}`));
			} else {
				if (currentGroup.length > 1) {
					// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				} else {
					// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				}
				currentGroup = [t(`DayInWeek.${day}`)];
				currentHours = hours;
			}
		}
		// Xử lý nhóm cuối cùng
		if (currentGroup.length > 1) {
			// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		} else {
			// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		}
	}

	return result;

}

const containerScrolling = (e: any) => {

	var stickyElement = document.getElementById('categories_container') as HTMLElement;

	console.log(stickyElement.getBoundingClientRect().top)

	if (stickyElement.getBoundingClientRect().top <= 110) {
		stickyElement.classList.add('sticky-padding');
	} else {
		stickyElement.classList.remove('sticky-padding');
	}
}
</script>
<template>
<HeaderV2WithMenuComponent class="overview-header">
			<!-- <template v-slot:header_left>
				<button class="back-button">
					<Icon name="solar:round-alt-arrow-left-linear"></Icon>
				</button>
				<div class="custom-logo">
					<AvatarComponent class="shop-logo" :imgTitle="props.shop_data?.name"
						:imgStyle="props.shop_data?.logo?.style" :imgSrc="props.shop_data?.logo?.path?.length
							? (domainImage + props.shop_data?.logo?.path)
							: ''" :width="40" :height="40" />
				</div>
				<nuxt-link class="home-logo header-menu-button" :to="'#'">
					<Icon name="solar:home-smile-bold"></Icon>
					{{ $t("SideBarComponent.trang_chu") }}
					
				</nuxt-link>
				<nuxt-link :to="'#'" class='header-menu-button'>
					<Icon name="solar:point-on-map-bold" />
					<span>
						{{ $t("FooterV2Component.gan_day") }}
					</span>

				</nuxt-link>
			</template> -->
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<v-btn class="notification menu-button" flat rounded v-bind="props"
					:title="$t('NotificationsComponent.thong_bao')">
					<Icon name="ic:sharp-circle-notifications"></Icon>
				</v-btn>
				<v-btn class='menu-button' :variant="'text'" rounded :title="$t('FooterV2Component.chat')">
					<nuxt-link :to="'#'" class='footer-button'>
						<Icon name="mynaui:chat-dots" />
					</nuxt-link>
				</v-btn>

				<v-btn class='notification cart-button' :variant="'text'" id="cart_btn"
					:title="$t('FooterV2Component.gio_hang')">
					<Icon name="solar:cart-3-linear"></Icon>
				</v-btn>


				<v-btn class='menu-button' :variant="'text'" rounded :title="$t('FooterV2Component.toi')">
					<nuxt-link :to="'#'">
						<img loading="lazy" v-if="userInfo?.id" :src="userInfo && userInfo.profile_picture
							? ((appConst.provider_img_domain.some(e => userInfo?.profile_picture?.includes(e))) ? userInfo.profile_picture : domainImage + userInfo.profile_picture)
							: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" />
						<Icon name="solar:user-circle-linear" v-else />
					</nuxt-link>
				</v-btn>

				<v-btn class="menu-side-bar-button" :variant="'text'">
					<Icon name="famicons:menu"></Icon>
				</v-btn>
			</template>
		</HeaderV2WithMenuComponent>
	<div class="public-container" id="shop_temp_container_1_overview" :style="{
		fontFamily: `${props.css_data.font}, ` + 'Nunito, Montserrat, Mulish, Roboto, sans-serif',
		'--temp-color-1': props.css_data?.color_1 ?? 'var(--primary-color-1)',
		'--temp-color-2': props.css_data?.color_2 ?? 'var(--primary-color-2)',
		'--temp-color-3': props.css_data?.color_3 ?? '#ffffff',
		'--temp-color-4': props.css_data?.color_4 ?? 'var(--secondary-color-3)',
		'--temp-color-5': props.css_data?.color_5 ?? '#d19019',
		'--font-resize': `${props.css_data?.font_resize ?? 0}px`,
		'--back-image': `url(${props.background ? props.background : shop_bg})`
	}" v-on:scroll="containerScrolling">
		
		<!-- </div> -->
		<div class="shop-temp-1-overview-container">
			<v-parallax class="banner-delay-box" :src="props.banner ? props.banner : props.shop_data?.banner?.path
				? `${domainImage + props.shop_data.banner.path}`
				: `${shop_banner_temp}`"></v-parallax>

			<div class="shop-v2-content-container" id='shop-container' v-if="props.shop_data?.id">
				<div class='v-stack shop-content-container'>
					<div class="categories-container" id="categories_container">
						<div class="logo-in-categories-carousel">
							<AvatarComponent class="shop-logo" :imgTitle="props.shop_data?.name"
								:imgStyle="props.shop_data?.logo?.style" :imgSrc="props.shop_data?.logo?.path?.length
									? (domainImage + props.shop_data?.logo?.path)
									: ''" :width="50" :height="50" />
						</div>


						<Swiper class="my-carousel categories-carousel" :modules="[SwiperFreeMode, SwiperNavigation]"
							:navigation="true" :freeMode="true" :centered-slides="true" :centered-slides-bounds="true"
							:slides-per-view="'auto'" :slides-per-group-auto="true" :loop="false" :spaceBetween="30"
							:effect="'creative'" :autoplay="false" key="category-carousel">
							<SwiperSlide class="category-item-slide"
								v-for="(itemCategory, indexTab) of dataShopTempCategories" :class="{
									'active': indexTab == 0
								}" :value="itemCategory.id" :key="itemCategory.id" :id="'tab_' + itemCategory.id">
								<div class="tab-title">
									<span class='name'>
										{{ showTranslateProductName(itemCategory) }}
									</span>
								</div>
							</SwiperSlide>
						</Swiper>
						<div class="search-input-group-in-cs" :class="{
							'hide': !showSearchBar
						}">
							<div class="search-input-group">
								<v-btn class="search-button" variant="text">
									<Icon :name="'solar:rounded-magnifer-outline'"></Icon>
								</v-btn>
								<input type="search" name='search-text'
									:placeholder="props.shop_data?.name ?? $t('UpdateViewTemplateComponent.tim_san_pham')"
									autoComplete='off' :maxlength="appConst.max_text_short" v-on:input="($event: any) => {
										// searchProduct()
									}" />

							</div>
						</div>


						<div class="search-in-categories-carousel">
							<v-btn class="search-in-cs-button" variant="text" v-on:click="() => {
								showSearchBar = !showSearchBar
							}">
								<Icon
									:name="showSearchBar ? 'icon-park-outline:to-right' : 'solar:rounded-magnifer-outline'">
								</Icon>
							</v-btn>
						</div>
					</div>

					<div class="shop-detail-container">
						<AvatarComponent class="shop-logo" :imgTitle="props.shop_data?.name"
							:imgStyle="props.shop_data?.logo?.style" :imgSrc="props.shop_data?.logo?.path?.length
								? (domainImage + props.shop_data?.logo?.path)
								: ''" :width="100" :height="100" />
						<div class="shop-detail-content">
							<div class="name-share">
								<nuxt-link :to="'#'" class="name">
									<span>{{ props.shop_data?.name }}</span>
									<!-- <Icon name="solar:alt-arrow-right-linear"></Icon> -->
								</nuxt-link>

							</div>

							<span class="follows">
								<em>{{ props.shop_data?.follows ?? 0 }}</em> {{
									$t('UpdateViewTemplateComponent.nguoi_theo_doi') }}
							</span>

							<span class="is-open" v-if="props.shop_data?.settings?.general" :class="{
								'closed': !props.shop_data?.settings?.general?.is_open?.value
							}">
								<em v-if="props.shop_data?.settings?.general?.is_open.value">
									{{ $t('UpdateViewTemplateComponent.dang_mo_cua') }}
								</em>
								<em v-else class="close">
									<span :class="{
										'has-reason': props.shop_data?.settings?.general?.is_open?.reason && (
											props.shop_data?.settings?.general?.is_open?.reason?.[locale]?.length
											|| props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
										)
									}">{{ $t('UpdateViewTemplateComponent.da_dong_cua') }}</span>

									<span class="reason" v-if="props.shop_data?.settings?.general?.is_open?.reason && (
										props.shop_data?.settings?.general?.is_open?.reason?.[locale]?.length
										|| props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
									)">{{
										props.shop_data?.settings?.general?.is_open?.reason?.[locale]
										??
										props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]
									}}</span>
								</em>
							</span>
							<span class="is-open" v-else>
								<em>{{ $t('UpdateViewTemplateComponent.dang_mo_cua') }}</em>
							</span>

							<div class="actions">
								<v-btn class="action-button direction" variant="text"
									:title="$t('UpdateViewTemplateComponent.chi_duong')">
									<Icon name="solar:point-on-map-bold"></Icon>
									{{ $t('UpdateViewTemplateComponent.chi_duong') }}
								</v-btn>

								<v-btn class="action-button follow" :variant="'outlined'"
									:title="$t('UpdateViewTemplateComponent.theo_doi')">
									<Icon :name="'solar:add-circle-bold'">
									</Icon>
									<span>{{ $t('UpdateViewTemplateComponent.theo_doi') }}</span>
									<!-- <span v-else>{{ $t('UpdateViewTemplateComponent.da_theo_doi') }}</span> -->

								</v-btn>

								<v-btn class="action-button share" variant="tonal">
									<Icon name="solar:share-linear"></Icon>
									<span>{{ $t('UpdateViewTemplateComponent.chia_se') }}</span>
								</v-btn>
							</div>


						</div>
					</div>
					<div class="slogan" v-if="props.shop_data.settings?.general?.message?.greeting?.[locale]">
						<Icon name="game-icons:beveled-star"></Icon>
						<span class="text">{{ props.shop_data.settings?.general?.message?.greeting?.[locale] }}</span>
						<Icon name="game-icons:beveled-star"></Icon>
					</div>

					<div class="advanced-detail">
						<div class="shop-detail-open-time" v-if="open_time_list.length > 0">
							<div class="label">
								<span>{{ $t('UpdateViewTemplateComponent.gio_mo_cua') }}</span>
								<Icon name="solar:clock-square-linear"></Icon>
							</div>
							<div class="line"></div>
							<div class="list-open-time">
								<div class="item-open-time" v-for="item in open_time_list">
									<span class="days">{{ item.days }}</span>
									<span class="times">{{ item.times }}</span>
								</div>
							</div>
						</div>

						<div class="shop-detail-notification"
							v-if="props.shop_data?.settings?.general?.message?.notification?.[locale]">
							<div class="label">
								<Icon name="fxemoji:loudspeaker"></Icon>
								<span>{{ $t('UpdateViewTemplateComponent.thong_bao') }}</span>
							</div>
							<div class="line"></div>
							<div class="notification-content"
								v-html="props.shop_data?.settings?.general?.message?.notification?.[locale]">
							</div>
						</div>

						<div class="shop-detail-delivery-promotion"
							v-if="props.shop_data?.settings?.order?.delivery?.length > 0">
							<div class="label">
								<Icon name="hugeicons:discount-tag-02"></Icon>
								<span>{{ $t('UpdateViewTemplateComponent.khuyen_mai') }}</span>
							</div>
							<div class="line"></div>
							<div class="list-delivery-promotion">
								<Swiper class="my-carousel delivery-promotion-carousel" :modules="[SwiperFreeMode]"
									:navigation="false" :freeMode="true" :slides-per-view="'auto'"
									:slides-per-group-auto="true" :loop="false" :spaceBetween="10" :effect="'creative'"
									:autoplay="false" key="category-carousel">
									<SwiperSlide class="delivery-promotion-item-slide"
										v-for="(itemPromotion, indexPromotion) in tempPromotions"
										:value="`${itemPromotion.id}_${indexPromotion}`"
										:key="`${itemPromotion.id}_${indexPromotion}`"
										:id="`item_promotion_${itemPromotion.id}_${indexPromotion}`">
										<div class="value-tag">
											<span>-{{ itemPromotion?.type == 'percent' ? `${itemPromotion.value}%` :
												formatNumberToShort(itemPromotion.value) }}</span>
											<img :src="discount_tag" loading="lazy">
										</div>
										<div class="promotion-detail">
											<span class="name">{{
												$t(`UpdateViewTemplateComponent.${itemPromotion.name}`)
											}}</span>
											<span class="description">{{
												$t(`UpdateViewTemplateComponent.${itemPromotion.description}`)
											}}</span>
											<span class="max-value" v-if="itemPromotion.max_value">{{
												$t('UpdateViewTemplateComponent.toi_da') }} <em>{{
													formatCurrency(itemPromotion.max_value
														?? 0) }}</em></span>
											<button class="condition-action">
												{{ $t('UpdateViewTemplateComponent.xem_chi_tiet') }}
											</button>
										</div>

									</SwiperSlide>
								</Swiper>
							</div>
						</div>
					</div>


					<div class="shop-products-container sale-off" v-show="dataShopTempProducts?.length">
						<div class="title-stack uppercase">
							<span>{{ $t('UpdateViewTemplateComponent.giam_gia') }}</span>
						</div>
						<!-- <div class="line"></div> -->
						<div class="products-container">
							<div class="container">
								<Swiper class="my-carousel stack-carousel product-carousel"
									:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="2"
									:breakpoints="{
										576: {
											slidesPerView: 2
										},
										768: {
											slidesPerView: 3
										},
										992: {
											slidesPerView: 4
										},
										1200: {
											slidesPerView: 4
										}
									}" :centered-slides="false" :centered-slides-bounds="true" :space-between="10" :loop="false"
									:effect="'creative'" :navigation="true" :freeMode=false :autoplay="false"
									key="sale-off-carousel">
									<SwiperSlide class="product-item-container-grid-carousel"
										v-for="(itemProduct, indexSaleOff) of dataShopTempProducts"
										:key="'sale_off_' + itemProduct.id">
										<ItemProductGridComponent class="swiper-slide"
											:product-data="JSON.parse(JSON.stringify(itemProduct))"
											:shop-data="JSON.parse(JSON.stringify(props.shop_data))"
											:product-index="indexSaleOff" :animation="true" :view_mode="'grid'"
											:preview_mode="true">
											<template v-slot:top_left_tag>
												<img :src="hot_sale" class="top-left-tag" />
											</template>
										</ItemProductGridComponent>
									</SwiperSlide>
								</Swiper>
							</div>

						</div>
					</div>

					<div class="shop-products-container sale-off" v-show="dataShopTempProducts?.length">
						<div class="title-stack uppercase">
							<span>{{ $t('UpdateViewTemplateComponent.noi_bat') }}</span>
						</div>
						<!-- <div class="line"></div> -->
						<div class="products-container">
							<div class="container">
								<Swiper class="my-carousel stack-carousel product-carousel"
									:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="2"
									:breakpoints="{
										576: {
											slidesPerView: 2
										},
										768: {
											slidesPerView: 3
										},
										992: {
											slidesPerView: 4
										},
										1200: {
											slidesPerView: 4
										}
									}" :centered-slides="false" :centered-slides-bounds="true" :space-between="10" :loop="false"
									:effect="'creative'" :navigation="true" :freeMode=false :autoplay="false"
									key="sale-off-carousel">
									<SwiperSlide class="product-item-container-grid-carousel"
										v-for="(itemProduct, indexSaleOff) of dataShopTempProducts"
										:key="'sale_off_' + itemProduct.id">
										<ItemProductGridComponent class="swiper-slide"
											:product-data="JSON.parse(JSON.stringify(itemProduct))"
											:shop-data="JSON.parse(JSON.stringify(props.shop_data))"
											:product-index="indexSaleOff" :animation="true" :view_mode="'grid'"
											:preview_mode="true">
											<template v-slot:top_left_tag>
												<img :src="hot_sale" class="top-left-tag" />
											</template>
										</ItemProductGridComponent>
									</SwiperSlide>
								</Swiper>
							</div>

						</div>
					</div>

					<div class="shop-products-container" v-if="dataShopTempCategories?.length">


						<!-- <div class="line"></div> -->
						<!-- <div class="grid-list-view">
							<span>
								{{ $t('UpdateViewTemplateComponent.che_do_hien_thi') }}
							</span>
							<div class="type-view-buttons">
								<button class="type-view-button list" :class="{ 'active': !showGrid }"
									v-on:click="() => { showGrid = false; }">
									<Icon name="solar:server-minimalistic-linear"></Icon>
								</button>
								<button class="type-view-button grid" :class="{ 'active': showGrid }"
									v-on:click="() => { showGrid = true; }">
									<Icon name="solar:widget-linear"></Icon>
								</button>
							</div>
						</div> -->
						<div class="products-container">

							<ItemCategoryProductComponent class="category-products-container"
								v-for="(itemCategory, indexCategory) in dataShopTempCategories"
								:view_mode="showGrid ? 'grid' : 'list'"
								:category-data="JSON.parse(JSON.stringify(itemCategory))"
								:shop-data="JSON.parse(JSON.stringify(props.shop_data))" :category-index="indexCategory"
								:key="`category_products_${itemCategory.id || 'all'}`"
								:id="`category_products_${itemCategory.id}`" :preview_mode=true>
								<template v-slot:top_header>
									<div class="grid-list-view">
										<!-- <span>
											{{ $t('ShopComponent.che_do_hien_thi') }}
										</span> -->
										<div class="type-view-buttons">
											<button class="type-view-button list" :class="{ 'active': !showGrid }"
												v-on:click="() => { showGrid = false; }">
												<Icon name="solar:server-minimalistic-linear"></Icon>
											</button>
											<button class="type-view-button grid" :class="{ 'active': showGrid }"
												v-on:click="() => { showGrid = true; }">
												<Icon name="solar:widget-linear"></Icon>
											</button>
										</div>
									</div>
								</template>
							</ItemCategoryProductComponent>
						</div>
						<!-- <v-overlay v-model="loadMore" :z-index="100" :absolute="false" contained
							content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
							<Icon name="eos-icons:loading"></Icon>
						</v-overlay> -->
					</div>

					<div class="shop-footer">
						<v-btn class="call-to-shop" varian="elevated">
							<nuxt-link :to="`#`" :target="'_blank'"
								:title="$t('UpdateViewTemplateComponent.goi_dien_cho_shop')">
								<Icon name="solar:phone-calling-bold"></Icon>
								<span>{{ $t('UpdateViewTemplateComponent.goi_dien_cho_shop') }}</span>
							</nuxt-link>
						</v-btn>

						<v-btn class="chat-to-shop" variant="elevated">
							<Icon name="solar:chat-round-dots-bold"></Icon>
							<span>{{ $t('UpdateViewTemplateComponent.chat_voi_shop') }}</span>
						</v-btn>
					</div>
				</div>

			</div>
		</div>
		<span class="shop-actions-speed-dial">
			<v-speed-dial location="left center" transition="none" contained>
				<template v-slot:activator="{ props: activatorProps }">
					<v-btn v-bind="activatorProps" class="shop-actions-button">
						<Icon name="mdi:phone-message"></Icon>
					</v-btn>
				</template>
				<v-btn class="call-to-shop" :title="$t('UpdateViewTemplateComponent.goi_dien_cho_shop')">
					<nuxt-link :to="'#'" :title="$t('UpdateViewTemplateComponent.goi_dien_cho_shop')">
						<Icon name="solar:phone-calling-bold"></Icon>
						<!-- <span>{{ $t('UpdateViewTemplateComponent.goi_dien_cho_shop') }}</span> -->
					</nuxt-link>
				</v-btn>

				<v-btn class="chat-to-shop" :title="$t('UpdateViewTemplateComponent.chat_voi_shop')">
					<Icon name="solar:chat-round-dots-bold"></Icon>
					<!-- <span>{{ $t('UpdateViewTemplateComponent.chat_voi_shop') }}</span> -->
				</v-btn>
			</v-speed-dial>
		</span>
	</div>

</template>

<style lang="scss" src="./ShopTemp1OverviewStyles.scss"></style>
