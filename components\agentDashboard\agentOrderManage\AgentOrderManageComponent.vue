<template>
    <div class="public-container" v-on:scroll="listOrderScroll(tabIndex != 'all' ? tabIndex : null)">
        <div class='agent-order-manage-container'>
            <SubHeaderV2Component :title="$t('AppRouteTitle.AgentOrderManageComponent')">

                <template v-slot:header_right>
                    <button class="filter-button" :class="{ 'active': checkFilterActive() }" v-on:click="async () => {
                        showFilterMenu = !showFilterMenu
                    }">
                        <Icon name="ph:funnel" />
                    </button>
                    <button v-on:click="async () => {
                        refreshListOrder()
                    }">
                        <Icon name="lets-icons:refresh" />
                    </button>
                </template>
            </SubHeaderV2Component>
            <div class="sticky-header" v-if="!noneShop">
                <div class="search-bar-container">
                    <div class='h-stack search-bar'>
                        <Icon name="ion:search" size="20" v-show="!searchOrderLoading" />
                        <Icon name="eos-icons:loading" size="20" v-show="searchOrderLoading" />

                        <input type="search" :value="search_text"
                            :maxlength="appConst.max_text_short"
                            :placeholder="$t('AgentOrderManageComponent.tim_theo_ma_don_hang')" v-on:input="async ($event: any) => {
                                search_text = $event.target.value;
                                searchOrders();
                            }" />
                    </div>
                </div>
                <v-tabs v-model="tabIndex" hide-slider class="orders-tab">
                    <v-tab class="order-tab-title" :class="tabIndex == 'all' ? 'active' : ''" value="all" key="all"
                        v-on:click="async () => {
                            if (tabIndex == 'all') {
                                await getListOrder();
                            }
                            router.replace({ hash: '' });
                        }">
                        <div class="tab-title">
                            {{ $t('AgentOrderManageComponent.tat_ca') }}
                            <!-- <em>{{ getStatusCount() }}</em> -->
                        </div>
                    </v-tab>
                    <v-tab v-for="(key, indexTab) in Object.keys(appConst.order_status)" class="order-tab-title"
                        v-on:click="async () => {
                            if (tabIndex == key) {
                                await getListOrder(appConst.order_status[key].value);
                            }
                            router.replace({ hash: '#' + key });
                        }" :class="tabIndex == key ? 'active' : ''" :value="key" :key="key" :id="'tab_' + key">
                        <div class="tab-title">
                            {{ $t('AgentOrderManageComponent.' + appConst.order_status[key].nameKey) }}
                            <!-- <em>{{ getStatusCount(appConst.order_status[key].value) }}</em> -->
                        </div>
                    </v-tab>
                </v-tabs>
            </div>
            <div class='v-stack agent-order-manage-view' v-if="!noneShop">


                <v-window v-model="tabIndex" class="tab-content-container">
                    <v-window-item value="all" id="tab_content_all">
                        <div v-for="(itemOrder, indexSelected) in listOrder['all'] " :key="'all_' + itemOrder.id"
                            class='v-stack item-order' v-if="listOrder['all'] && listOrder['all'].length > 0">
                            <!-- <div class="order-status">
                            <span>
                                {{
                                    appConst.order_status[
                                        parseInt(itemOrder.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key =>
                                                appConst.order_status[key].value ==
                                                parseInt(itemOrder.status)) as string
                                            : "taken"
                                    ].name
                                }}
                            </span>
                            <span class="is-new" v-if="checkNewOrder(itemOrder)">
                                new
                            </span>
                        </div>
                        <div class="order-item" v-for="(product) in itemOrder.items">
                            <div class="product-avatar">
                                <img loading="lazy" :src="product.profile_picture ? (domainImage + product.profile_picture) : icon_for_product" :placeholder="icon_for_product" alt="" />
                            </div>
                            <div class="product-detail">
                                <span class="name">{{ product.parent_id ? (product.parent_product?.name + " - ") :
                                    "" }}{{ product.name }}</span>

                                <div class="quantity-price">
                                    <span>Số lượng: {{ parseFloat(product.pivot.quantity) }}</span>
                                    |
                                    <span>
                                    {{
										(product.pivot.price_off != null && parseFloat(product.pivot.price_off) < parseFloat(product.pivot.price)) ?
											formatCurrency(parseFloat(product.pivot.price_off),  itemOrder.shops.currency) :
											(parseFloat(product.pivot.price) == 0 || product.pivot.price == null)
												? 'Giá liên hệ' : formatCurrency(parseFloat(product.pivot.price),
													 itemOrder.shops.currency) }} 
                                        <em class="off"
										v-if="(product.pivot.price_off != null && parseFloat(product.pivot.price_off) < parseFloat(product.price))">
										{{
											formatCurrency(product.price ? parseFloat(product.price) : 0,
                                            itemOrder.shops.currency)
										}}</em>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="order-actions">
                            <button 
                                class='reject-button btn-50' 
                                :disabled="isUpdating || itemOrder.isUpdating" 
                                v-on:click="async () => { router.push(appRoute.AgentOrderDetailComponent.replace(':id', itemOrder.id)) }">Chi tiết</button>
                            <button
                                v-if="parseInt(itemOrder.status.toString()) <= appConst.order_status.waiting.value"
                                class='accept-button accept-button-2 btn-50' :disabled="isUpdating || itemOrder.isUpdating" v-on:click="async () => {
                                    selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                    itemOrder.isUpdating = true;
                                    updateStatusOrder(selectedOrder, appConst.order_status.confirmed.value)
                                }"
                            >
                                Nhận đơn
                            </button>
                            <button 
                                v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                class='accept-button accept-button-2 btn-50' 
                                :disabled="isUpdating || itemOrder.isUpdating" 
                                v-on:click="async () => {
                                    selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                    itemOrder.isUpdating = true;
                                    updateStatusOrder(selectedOrder, appConst.order_status.taken.value);
                            }">
                                Gửi đi
                            </button>
                            <button 
                                v-if="parseInt(itemOrder.status.toString()) < appConst.order_status.taken.value"
                                class='accept-button btn-100' 
                                v-on:click="async () => {
                                    callToBuyer(itemOrder);
                                }"
                            >
                                Gọi cho người mua
                            </button>
                        </div> -->
                            <button class='order-button-container' v-on:click="() => {
                                { router.push(appRoute.AgentOrderDetailComponent.replace(':id', itemOrder.id)) }
                            }">
                                <div class='v-stack'>
                                    <div class='h-stack'>
                                        <span class='customer-name'>
                                            {{ itemOrder.customer_name }}
                                        </span>
                                        <span class="is-new"
                                            v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
                                            {{ $t('AgentOrderManageComponent.moi') }}
                                        </span>
                                        <span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
                                            : ' taken')">
                                            {{
                                                $t('AgentOrderManageComponent.' +
                                                    appConst.order_status[
                                                        parseInt(itemOrder.status.toString()) != 3
                                                            ? Object.keys(appConst.order_status).find(key =>
                                                                appConst.order_status[key].value ==
                                                                parseInt(itemOrder.status)) as string
                                                            : "taken"
                                                    ].nameKey
                                                )
                                            }}
                                        </span>
                                    </div>
                                    <div class='h-stack order-detail-advanced'>
                                        <span class='ordered-at'>
                                            {{
                                                moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
                                            }}
                                        </span>
                                        <span class='short-code'>
                                            [{{ itemOrder.short_code }}]
                                        </span>
                                    </div>
                                    <div class='h-stack'>
                                        <span>
                                            {{ itemOrder.address }}
                                        </span>
                                        <span class='customer-phone'>
                                            {{ itemOrder.customer_phone }}
                                        </span>
                                    </div>
                                    <div class='h-stack shop-name'>
                                        <Icon name="material-symbols:storefront-outline"></Icon>
                                        <span>
                                            {{ itemOrder.shops.name }}
                                        </span>
                                    </div>
                                    <div class='h-stack'>
                                        <span class='title'>{{ $t('AgentOrderManageComponent.tong_cong') }}</span>
                                        <span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
                                            itemOrder.shops.currency)
                                            }}</span>
                                    </div>
                                </div>
                            </button>

                            <div class='h-stack order-action'
                                v-if="parseInt(itemOrder.status.toString()) <= appConst.order_status.waiting.value">
                                <button class='reject-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                        // itemOrder.deleting = true;
                                        showModalConfirmCancelOrder = true
                                    }">
                                    <span v-if="!itemOrder.deleting">
                                        {{ $t('AgentOrderManageComponent.huy_bo') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                                <button class='accept-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                        itemOrder.isUpdating = true;
                                        updateStatusOrder(selectedOrder, appConst.order_status.confirmed.value)
                                    }">
                                    <span v-if="!itemOrder.isUpdating">
                                        {{ $t('AgentOrderManageComponent.xac_nhan') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                            </div>

                            <div v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                class='h-stack order-action'>
                                <button class='reject-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                        // itemOrder.deleting = true
                                        showModalConfirmCancelOrder = true;
                                    }">
                                    <span v-if="!itemOrder.deleting">
                                        {{ $t('AgentOrderManageComponent.huy_bo') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                                <button class='accept-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                        itemOrder.isUpdating = true;
                                        updateStatusOrder(itemOrder, appConst.order_status.taken.value);
                                    }">
                                    <span v-if="!itemOrder.isUpdating">
                                        {{ $t('AgentOrderManageComponent.da_giao') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                            </div>
                        </div>
                        <div class='v-stack empty-list' v-else>
                            <img loading="lazy" :src="list_empty" :placeholder="list_empty"
                                :alt="$t('AgentOrderManageComponent.danh_sach_trong')" />
                            <span>
                                {{ $t('AgentOrderManageComponent.khong_tim_thay_don_hang_nao') }}
                            </span>
                            <span class='refresh' v-on:click="() => {
                                getListOrder();
                            }">
                                {{ $t('AgentOrderManageComponent.lam_moi') }}
                            </span>
                        </div>
                        <span class='load-more' v-if="loadMore || isRefreshing">
                            {{ $t('AgentOrderManageComponent.dang_tai') }}
                        </span>
                        <div id="last_of_list_all"></div>
                    </v-window-item>

                    <v-window-item :id="'tab_content_' + key"
                        v-for="(key, indexTab) in Object.keys(appConst.order_status)" :value="key">
                        <div v-for="(itemOrder, indexSelected) in listOrder[key] " :key="key + '_' + itemOrder.id"
                            class='v-stack item-order' v-if="listOrder[key] && listOrder[key].length > 0">
                            <nuxt-link :to="appRoute.AgentOrderDetailComponent.replace(':id', itemOrder.id)"
                                class='order-button-container'>
                                <div class='v-stack'>
                                    <div class='h-stack'>
                                        <span class='customer-name'>
                                            {{ itemOrder.customer_name }}
                                        </span>
                                        <span class="is-new"
                                            v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
                                            {{ $t('AgentOrderManageComponent.moi') }}
                                        </span>
                                        <span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
                                            : ' taken')">
                                            {{
                                                $t('AgentOrderManageComponent.' +
                                                    appConst.order_status[
                                                        parseInt(itemOrder.status.toString()) != 3
                                                            ? Object.keys(appConst.order_status).find(key =>
                                                                appConst.order_status[key].value ==
                                                                parseInt(itemOrder.status)) as string
                                                            : "taken"
                                                    ].nameKey
                                                )
                                            }}
                                        </span>
                                    </div>
                                    <div class='h-stack order-detail-advanced'>
                                        <span class='ordered-at'>
                                            {{
                                                moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
                                            }}
                                        </span>
                                        <span class='short-code'>
                                            [{{ itemOrder.short_code }}]
                                        </span>
                                    </div>
                                    <div class='h-stack'>
                                        <span>
                                            {{ itemOrder.address }}
                                        </span>
                                        <span class='customer-phone'>
                                            {{ itemOrder.customer_phone }}
                                        </span>
                                    </div>
                                    <div class='h-stack shop-name'>
                                        <Icon name="material-symbols:storefront-outline"></Icon>
                                        <span>
                                            {{ itemOrder.shops.name }}
                                        </span>
                                    </div>
                                    <div class='h-stack'>
                                        <span class='title'>{{ $t('AgentOrderManageComponent.tong_cong') }}</span>
                                        <span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
                                            itemOrder.shops?.currency)
                                            }}</span>
                                    </div>
                                </div>

                            </nuxt-link>
                            <div class='h-stack order-action'
                                v-if="parseInt(itemOrder.status.toString()) == appConst.order_status.waiting.value">
                                <button class='reject-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                        // itemOrder.deleting = true
                                        showModalConfirmCancelOrder = true
                                    }">
                                    <span v-if="!itemOrder.deleting">
                                        {{ $t('AgentOrderManageComponent.huy_bo') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                                <button class='accept-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        itemOrder.isUpdating = true;
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                        updateStatusOrder(itemOrder, appConst.order_status.confirmed.value)
                                    }">
                                    <span v-if="!itemOrder.isUpdating">
                                        {{ $t('AgentOrderManageComponent.xac_nhan') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>

                            </div>
                            <div v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                class='h-stack order-action'>
                                <button class='reject-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder));
                                        // itemOrder.deleting = true;
                                        showModalConfirmCancelOrder = true;
                                    }">
                                    <span v-if="!itemOrder.deleting">
                                        {{ $t('AgentOrderManageComponent.huy_bo') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>
                                <button class='accept-button'
                                    :disabled="isUpdating || itemOrder.isUpdating || itemOrder.deleting" v-on:click="async () => {
                                        selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                        itemOrder.isUpdating = true;
                                        updateStatusOrder(itemOrder, appConst.order_status.taken.value);
                                    }">
                                    <span v-if="!itemOrder.isUpdating">
                                        {{ $t('AgentOrderManageComponent.da_giao') }}
                                    </span>
                                    <Icon name="eos-icons:loading" size="20" v-else />
                                </button>

                            </div>
                            <!-- <div class="order-status">
                            <span>
                                {{
                                    appConst.order_status[
                                        parseInt(itemOrder.status.toString()) != 3
                                            ? Object.keys(appConst.order_status).find(key =>
                                                appConst.order_status[key].value ==
                                                parseInt(itemOrder.status)) as string
                                            : "taken"
                                    ].name
                                }}
                            </span>
                            <span class="is-new" v-if="checkNewOrder(itemOrder)">
                                new
                            </span>
                        </div>
                        <div class="order-item" v-for="(product) in itemOrder.items">
                            <div class="product-avatar">
                                <img loading="lazy"
                                    :src="product.profile_picture ? (domainImage + product.profile_picture) : icon_for_product"
                                    :placeholder="icon_for_product" alt="" />
                            </div>
                            <div class="product-detail">
                                <span class="name">{{ product.parent_id ? (product.parent_product?.name + " - ") :
                                    "" }}{{ product.name }}</span>

                                <div class="quantity-price">
                                    <span>Số lượng: {{ parseFloat(product.pivot.quantity) }}</span>
                                    |
                                    <span>
                                        {{
                                            (product.pivot.price_off != null && parseFloat(product.pivot.price_off) <
                                                parseFloat(product.pivot.price)) ?
                                                formatCurrency(parseFloat(product.pivot.price_off),
                                                    itemOrder.shops.currency) : (parseFloat(product.pivot.price) == 0 ||
                                            product.pivot.price==null) ? 'Giá liên hệ' :
                                            formatCurrency(parseFloat(product.pivot.price), itemOrder.shops.currency) }}
                                            <em class="off"
                                            v-if="(product.pivot.price_off != null && parseFloat(product.pivot.price_off) < parseFloat(product.price))">
                                            {{
                                                formatCurrency(product.price ? parseFloat(product.price) : 0,
                                            itemOrder.shops.currency)
                                            }}</em>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="order-actions">
                            <button class='reject-button btn-50' :disabled="isUpdating || itemOrder.isUpdating"
                                v-on:click="async () => { router.push(appRoute.AgentOrderDetailComponent.replace(':id', itemOrder.id)) }">Chi
                                tiết</button>
                            <button v-if="parseInt(itemOrder.status.toString()) <= appConst.order_status.waiting.value"
                                class='accept-button accept-button-2 btn-50'
                                :disabled="isUpdating || itemOrder.isUpdating" v-on:click="async () => {
                                    selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                    itemOrder.isUpdating = true;
                                    updateStatusOrder(selectedOrder, appConst.order_status.confirmed.value)
                                }">
                                Nhận đơn
                            </button>
                            <button
                                v-else-if="parseInt(itemOrder.status.toString()) == appConst.order_status.confirmed.value"
                                class='accept-button accept-button-2 btn-50'
                                :disabled="isUpdating || itemOrder.isUpdating" v-on:click="async () => {
                                    selectedOrder = JSON.parse(JSON.stringify(itemOrder))
                                    itemOrder.isUpdating = true;
                                    updateStatusOrder(selectedOrder, appConst.order_status.taken.value);
                                }">
                                Gửi đi
                            </button>
                            <button v-if="parseInt(itemOrder.status.toString()) < appConst.order_status.taken.value"
                                class='accept-button btn-100' v-on:click="async () => {
                                    callToBuyer(itemOrder);
                                }">
                                Gọi cho người mua
                            </button>
                        </div> -->
                        </div>
                        <div class='v-stack empty-list' v-else>
                            <img loading="lazy" :src="list_empty" :placeholder="list_empty"
                                :alt="$t('AgentOrderManageComponent.danh_sach_trong')" />
                            <span>
                                {{ $t('AgentOrderManageComponent.khong_tim_thay_don_hang_nao') }}
                            </span>
                            <span class='refresh' v-on:click="() => {
                                getListOrder(appConst.order_status[key].value);
                            }">
                                {{ $t('AgentOrderManageComponent.lam_moi') }}
                            </span>
                        </div>
                        <span class='load-more' v-if="loadMore || isRefreshing">
                            {{ $t('AgentOrderManageComponent.dang_tai') }}
                        </span>

                        <div :id="'last_of_list_' + key"></div>
                    </v-window-item>

                </v-window>
            </div>
            <!-- <div class="v-stack agent-order-manage-view" v-else>
                <div class='register-shop-content-button'>
                    <img loading="lazy" :src="none_shop" :placeholder="none_shop"
                        :alt="$t('AgentOrderManageComponent.chua_dang_ky_cua_hang')" />
                    <span>
                        {{ $t('AgentOrderManageComponent.ban_chua_co_cua_hang') }}
                    </span>
                    <nuxt-link :to="appRoute.RegisterShopComponent">
                        <button>
                            {{ $t('AgentOrderManageComponent.dang_ky_ngay') }}
                        </button>
                    </nuxt-link>
                </div>
            </div> -->
            <NoneMyShopComponent v-else :show_header="false"
                :title="shopData ? shopData.name : $t('AppRouteTitle.AgentShopDetailDashboardComponent')"
                :mode="'agent'" :message="t('AgentOrderManageComponent.ban_khong_co_quyen_truy_cap')">
            </NoneMyShopComponent>
            <VueFinalModal class="my-modal-container" content-class="v-stack confirm-cancel-order-modal" :overlay-behavior="'persist'"
                v-model="showModalConfirmCancelOrder" v-on:closed="() => {
                    showModalConfirmCancelOrder = false;
                    selectedOrder = null;
                }" contentTransition="vfm-slide-up">
                <div class='v-stack cancel-order-content'>
                    <span class='cancel-order-title'>
                        {{ $t('AgentOrderManageComponent.tu_choi_nhan_don_hang') }}
                    </span>

                    <span class='cancel-order-message'>
                        {{ $t('AgentOrderManageComponent.huy_don_hang') }}
                        <span class='order-code'>
                            {{
                                selectedOrder && selectedOrder.short_code
                                    ? selectedOrder.short_code
                                    : ""
                            }}
                        </span>
                    </span>
                </div>
                <div class='h-stack confirm-modal-buttons'>
                    <button class='reject-button' :disabled="isUpdating" v-on:click="() => {
                        showModalConfirmCancelOrder = false
                    }">
                        {{ $t('AgentOrderManageComponent.khong') }}
                    </button>
                    <button class='accept-button' :disabled="isUpdating" v-on:click="() => {
                        updateStatusOrder(selectedOrder, appConst.order_status.cancel.value);
                        showModalConfirmCancelOrder = false;
                        selectedOrder = null;
                    }">
                        {{ $t('AgentOrderManageComponent.co') }}
                    </button>
                </div>
            </VueFinalModal>

        </div>
        <v-overlay v-model="showFilterMenu" :z-index="100" :absolute="false" contained key="show_filter_menu"
            class="filter-menu-overlay-container" content-class='filter-menu-container' no-click-animation
            v-on:click:outside="() => {
                showFilterMenu = false;
                shop_id_temp = shop_id;
                filterTimeTemp = filterTime;
            }">

            <div class="filter-menu-content">
                <div class="filter-title">
                    {{ $t('AgentOrderManageComponent.loc_don_hang') }}
                </div>
                <USelectMenu class="dropdown-select-container shop-select" searchable
                    :searchable-placeholder="$t('AgentOrderManageComponent.tim_cua_hang')" :search-attributes="['name']"
                    variant="none" :options="listShopManaged" :placeholder="$t('AgentOrderManageComponent.hang_muc')"
                    v-model="shop_id_temp" v-on:change="(newValue: any) => { }" value-attribute="id"
                    option-attribute="name">
                    <template #label>
                        <span class="truncate">{{ shop_id_temp
                            ? (listShopManaged[listShopManaged.findIndex((e: any) => {
                                return e.id == shop_id_temp
                            })].name)
                            : $t('AgentOrderManageComponent.tat_ca_cua_hang') }}</span>
                    </template>
                    <template #option="{ option: shopOption }">
                        <span class="truncate">{{ shopOption.name }}</span>
                    </template>
                    <template #empty>
                        {{ $t('AgentOrderManageComponent.chua_co_cua_hang_duoc_uy_quyen') }}
                    </template>
                    <template #option-empty="{ query }">
                        <span class="search-shop-empty">
                            {{ $t('AgentOrderManageComponent.khong_tim_thay') }} <q>{{ query }}</q>
                        </span>
                    </template>
                </USelectMenu>
                <div class="time-filter">
                    <div class="item-time-filter" :class="{ 'active': filterTimeTemp == item.value }"
                        v-for="item of dataFilterTime" v-on:click="() => {
                            filterTimeTemp = item.value
                        }">
                        <Icon name="ic:sharp-radio-button-checked" v-if="filterTimeTemp == item.value"></Icon>
                        <Icon name="ic:sharp-radio-button-unchecked" v-else></Icon>
                        <span>{{ $t('AgentOrderManageComponent.' + item.nameKey) }}</span>
                    </div>
                    <div class="h-stack select-date" v-if="filterTimeTemp == 5">
                        <input name="start-day" class="input-custom" id="start-day" type="date" :value="start_date"
                            :placeholder="$t('AgentOrderManageComponent.chua_cung_cap')"
                            :max="moment(end_date ? end_date : new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                                start_date = event.target.value;
                            }" />

                        <Icon name="material-symbols:arrow-forward-rounded"></Icon>

                        <input name="end-day" class="input-custom" id="end-day" type="date" :value="end_date"
                            :placeholder="$t('AgentOrderManageComponent.chua_cung_cap')"
                            :min="moment(start_date ? start_date : new Date()).format('YYYY-MM-DD')"
                            :max="moment(new Date()).format('YYYY-MM-DD')" v-on:input="(event: any) => {
                                end_date = event.target.value;
                            }" />
                    </div>
                    <div class="h-stack footer">
                        <button class="close" v-on:click="() => {
                            showFilterMenu = false;
                            shop_id_temp = shop_id;
                            filterTimeTemp = filterTime;
                        }">
                            {{ $t('AgentOrderManageComponent.dong') }}
                        </button>
                        <button class="apply" v-on:click="() => {
                            showFilterMenu = false;
                            shop_id = shop_id_temp;
                            if (filterTimeTemp != filterTime || filterTimeTemp == 5) {
                                filterTime = filterTimeTemp;
                                switch (filterTime as any) {
                                    case 0: case null: {
                                        start_date = null;
                                        end_date = null;
                                        break;
                                    }
                                    case 1: {
                                        start_date = moment(new Date()).format(appConst.formatDate.toSave);
                                        end_date = moment(new Date()).format(appConst.formatDate.toSave)
                                        break;
                                    }
                                    case 2: {
                                        start_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave);
                                        end_date = moment().subtract(1, 'day').format(appConst.formatDate.toSave)
                                        break;
                                    }
                                    case 3: {
                                        start_date = moment().startOf('month').format(appConst.formatDate.toSave);
                                        end_date = moment().endOf('month').format(appConst.formatDate.toSave)
                                        break;
                                    }
                                    case 4: {
                                        start_date = moment().add(-1, 'months').startOf('month').format(appConst.formatDate.toSave);
                                        end_date = moment().add(-1, 'months').endOf('month').format(appConst.formatDate.toSave);
                                        break;
                                    }
                                    case 5: {
                                        start_date = start_date ? moment(start_date).format(appConst.formatDate.toSave) : null;
                                        end_date = end_date ? moment(end_date).format(appConst.formatDate.toSave) : null;
                                        break;
                                    }
                                    default: break;
                                }
                            }
                            init();
                        }">
                            {{ $t('AgentOrderManageComponent.ap_dung') }}
                        </button>
                    </div>
                </div>
            </div>
        </v-overlay>
    </div>

</template>
<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
const { t } = useI18n();
var emit = defineEmits(['close']);
var props = defineProps({
    shopData: {} as any,
    tabIndex: null,
    shop_id: null
})
var nuxtApp = useNuxtApp();
useSeoMeta({
    title: t('AppRouteTitle.AgentOrderManageComponent')
});
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { appConst, formatCurrency, domainImage } from '~/assets/AppConst';
import { appRoute, appRouteTitle } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { AgentService } from '~/services/agentService/agentService';

import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg";
import { toast } from 'vue3-toastify';
import { HttpStatusCode } from 'axios';
import { MqttService } from '~/services/mqttService/mqttService';
var authService = new AuthService();
var userService = new UserService();
var orderService = new OrderService();
var shopService = new ShopService();
var agentService = new AgentService();
var mqttSerivce = new MqttService();
var searchOrderTimeout: any;

var loadMoreTimeOut: any;
var searchTimeout: any;
var dataFilterTime = [
    {
        value: 0 || null,
        name: "Toàn thời gian",
        nameKey: "toan_thoi_gian",
    },
    {
        value: 1,
        name: "Hôm nay",
        nameKey: "hom_nay",
    },
    {
        value: 2,
        name: "Hôm qua",
        nameKey: "hom_qua",
    },
    {
        value: 3,
        name: "Tháng này",
        nameKey: "thang_nay",
    },
    {
        value: 4,
        name: "Tháng trước",
        nameKey: "thang_truoc",
    },
    {
        value: 5,
        name: "Thời gian khác",
        nameKey: "thoi_gian_khac",
    },
];

var refreshTimeout: any;

var showModalConfirmCancelOrder = ref(false);
var selectedOrder = ref(null as any);
var search_text = ref("")
var tabIndex = ref();
var searchOrderLoading = ref(false);
var shop_id = ref((router.options.history.state && router.options.history.state.shop_id ? router.options.history.state.shop_id : null) as any);
var shop_id_temp = ref((router.options.history.state && router.options.history.state.shop_id ? router.options.history.state.shop_id : null) as any);
var listOrder = ref({} as any);
var countOrderByStatus = ref([] as any[]);
var isRefreshing = ref(false);
var showSearchBar = ref(false);
var showFilterMenu = ref(false);
var filterTime = ref(null);
var filterTimeTemp = ref(null as any);
var start_date = ref(null as any);
var end_date = ref(null as any);
var loadMore = ref(false);
var isUpdating = ref(false);
var searchFocus = ref(false);
var noneShop = ref(false);
var offset = ref(0);
var listShopManaged = ref([] as any[]);

onMounted(() => {
    setTimeout(async () => {
        await getListShopManage();
        await init();
        tabIndex.value = route.hash ? route.hash.replaceAll('#', '') : 'all'
    }, 0);
})

onUnmounted(() => {
    // unsubscripeMqttToAllAgentShop();
})

async function init() {
    getListOrder(route.hash ? appConst.order_status[route.hash.replaceAll('#', '')].value : null);
    // Object.keys(appConst.order_status).map(async (key) => {
    //     setTimeout(() => {
    //         getListOrder(appConst.order_status[key].value);
    //     }, 500);
    // });
}
function close() {
    router.back();
}

function getListShopManage() {
    isRefreshing.value = true;
    agentService.listShopManage("", 1000, offset.value).then(res => {
        listShopManaged.value = [{
            id: null,
            name: "Tất cả cửa hàng"
        }, ...JSON.parse(JSON.stringify(res.body.data))];

        isRefreshing.value = false;

        // subscribeMqttToAllAgentShop();
    })
}

function subscribeMqttToAllAgentShop() {
    listShopManaged.value.forEach(e => {
        mqttSerivce.subscribe(appConst.mqtt_topic.shop.replaceAll(':shop_id', e.id), (mes) => {
            if (mes.mess?.url == shop_id.value) {
                getListOrder();
            }
        })
    })
}

function unsubscripeMqttToAllAgentShop() {
    listShopManaged.value.forEach(e => {
        mqttSerivce.unsubscribe(appConst.mqtt_topic.shop.replaceAll(':shop_id', e.id));
    })
}

function getListOrder(status?: number | null) {
    isRefreshing.value = true;

    agentService.orderByAgentId(shop_id.value, 0, 20, status, search_text.value, start_date.value, end_date.value).then(async res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
            let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
            statusName = statusName ? statusName : "all";
            document.getElementById('tab_content_' + statusName)?.scrollTo({
                behavior: 'smooth',
                top: 0
            });
            let list = JSON.parse(JSON.stringify(listOrder.value));

            list[statusName] = JSON.parse(JSON.stringify(res.body.data.result));
            let index = countOrderByStatus.value.findIndex(function (e: any) {
                return e.status == status;
            })
            if (index != -1) {
                countOrderByStatus.value[index].count = res.body.data.count
            }
            else {
                countOrderByStatus.value.push({
                    status: status,
                    count: res.body.data.count
                })
            }
            isRefreshing.value = false;
            listOrder.value = JSON.parse(JSON.stringify(list));
        }
        else if (res.status == HttpStatusCode.Unauthorized) {
            // router.push(appRoute.LoginComponent)
        }
    })
}

function getMoreOrderInAList(offset = 0, limit = 20, status?: number | null) {
    let index = countOrderByStatus.value.findIndex(function (e: any) {
        return e.status == (status ? status : undefined);
    });
    if (offset < countOrderByStatus.value[index].count) {
        clearTimeout(loadMoreTimeOut);

        loadMoreTimeOut = setTimeout(() => {
            loadMore.value = true;
            let statusName = Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == status)?.toString();
            agentService.orderByAgentId(shop_id.value, offset, limit, status).then(async res => {

                if (res.status && res.status == HttpStatusCode.Ok) {

                    statusName = statusName ? statusName : "all";
                    let list = JSON.parse(JSON.stringify(listOrder.value));
                    list[statusName] = [...list[statusName], ...res.body.data.result];

                    loadMore.value = false;
                    listOrder.value = JSON.parse(JSON.stringify(list));
                }

            })

        }, 500)
    }

}

// getCountOrderByStatus() {
//     orderService.countOrderByStatus(shopData.id).then(async res => {
//         await setState({
//             countOrderByStatus: res.body.data
//         })
//     })
// }

function getStatusCount(status?: number | null) {
    let index = countOrderByStatus.value.findIndex(function (e: any) {
        return e.status == status
    })

    if (index != -1) {
        if (countOrderByStatus.value[index].count < 100) return countOrderByStatus.value[index].count;
        if (countOrderByStatus.value[index].count >= 100) return '99+';
    }
    return 0;
}
var isCloseToBottom = (layoutMeasurement: any, contentOffset: any, contentSize: any) => {
    const paddingToBottom = 20;
    return layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom;
};

async function updateStatusOrder(orderItem: any, newStatus: number) {
    isUpdating.value = true;

    let order = JSON.parse(JSON.stringify(orderItem));
    order.status = newStatus != 3 ? newStatus : 4;
    order.items = [];
    await orderItem.items.forEach((e: any) => {
        order.items.push(e.pivot)
    })

    agentService.agentUpdateOrder(order).then(res => {
        if (res.status == HttpStatusCode.Ok) {
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;

        }
        else if (res.status == HttpStatusCode.BadRequest) {
            toast.warn(t('AgentOrderManageComponent.ban_khong_co_quyen_truy_cap'));
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;
        }
        else {
            // toast.warn("Cập nhật thất bại. Vui lòng thử lại sau");
            isUpdating.value = false;
            orderItem.isUpdating = false;
            selectedOrder.value = null;
        }
        init();
    }).catch(err => {
        // toast.error("Cập nhật thất bại. Vui lòng thử lại sau");
        isUpdating.value = false;
        orderItem.isUpdating = false;
        selectedOrder.value = null;
    })
}

function searchOrders() {
    searchFocus.value = true;
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        init()
    }, 100)
}

function listOrderScroll(key: string | null = null) {

    let el = document.getElementById('last_of_list_' + (key ? key : 'all'))?.getBoundingClientRect().bottom;
    if (el && (el <= window.innerHeight + 300)) {
        getMoreOrderInAList(listOrder.value[key ? key : "all"].length, 20, key ? appConst.order_status[key].value : null)
    }
}

function checkFilterActive() {
    if (shop_id.value?.length > 0) return true;
    if (start_date.value || end_date.value) return true;
    return false;
}

function refreshListOrder() {
    clearTimeout(refreshTimeout);
    refreshTimeout = setTimeout(() => {
        init()
    }, 1000);
}

function checkNewOrder(order: any) {
    var orderCreatedAt = moment(order.created_at).valueOf();
    var currentTime = moment().valueOf();
    let count = currentTime - orderCreatedAt;
    if (Math.round(count / (60 * 60 * 24)) < 60)
        return true;
    return false;
}

function callToBuyer(order: any) {
    window.open(`tel:${order.customer_phone}`)
}
</script>

<style lang="scss" src="./AgentOrderManageStyles.scss"></style>