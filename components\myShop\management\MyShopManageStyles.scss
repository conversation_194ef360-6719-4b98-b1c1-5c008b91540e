.my-shop-manage-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--color-background-2);
  position: relative;
  align-items: center;
  justify-content: flex-start ;
  overflow: auto;
  max-width: var(--max-width-content-view-1024) !important;
  width: 100%;
  z-index: 1;

  & > .my-shop-manage-content{
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  & .manage-tab {
    text-transform: none;
    font-weight: 600;
    font-size: 1em;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    min-height: 50px;
    background: white;

    & .manage-tab-title {
      flex: 1;
      text-transform: none;
      // border-bottom: 2px solid transparent;
      color: #79787d;
      font-weight: 600;
      gap: 5px;
      font-size: 1em;
      background-color: white;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        & em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 25px;
          height: 25px;
          font-size: 0.8em;
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 600;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    & .manage-tab-title.active {
      font-weight: 600;
      // border-bottom: 2px solid var(--primary-color-1);
    }
  }
  & .tab-content-container {
    width: 100%;
    flex: 1;
    display: flex;

    & > div {
      width: 100%;
    }

    & div.v-window-item {
      padding: 0;
      overflow: auto;
      flex: 1;
      display: flex;
      flex-direction: column;
      width: 100%;
    }
  }

  & .sticky-header{
    width: 100%;
    position: sticky;
    top: 50px;
    z-index: 10;
    background: white;
  }
}

.access-denied{
  padding: 0;
  justify-content: flex-start !important;
  background: white;

  & > .access-denied-content{
    margin: auto 0;
    display: flex;
    flex-direction: column;
  }
}

