<template>
    <VueFinalModal class="my-modal-container" :click-to-close="false"
        :key="'receiver_info_modal'"
        :modal-id="'receiver_info_modal'"
        :overlay-behavior="'persist'"
        :hide-overlay="false"
        :overlay-class="'receiver-info-overlay'"
        content-class="my-modal-content-container form-modal edit-customer-info-modal" v-model="showModal"
        v-on:closed="() => {

            showModal = false;

        }" contentTransition="vfm-slide-down">
        <div class='v-stack edit-customer-info-container'>
            <SubHeaderV2Component :title="$t('OrderReceiverInfoComponent.thong_tin_nguoi_nhan')">
                <template v-slot:header_left></template>
                <template v-slot:header_right>
                    <button v-on:click="()=>{close()}">
                        <Icon name="clarity:times-line" size="25"></Icon>
                    </button>
                </template>
            </SubHeaderV2Component>
            <div class="edit-customer-info-content">
                
                <div class='h-stack'>
                    <span class='label'>
                        {{ $t('OrderReceiverInfoComponent.ten') }}:
                    </span>
                    <span class="content">{{ props.init_data.name }}</span>
                </div>
                <div class='h-stack'>
                    <span class='label'>
                        {{ $t('OrderReceiverInfoComponent.so_dien_thoai') }}:
                    </span>
                    <nuxt-link :to="`tel:${props.init_data.phone}`" class="content">{{ props.init_data.phone }}</nuxt-link>
                </div>

                <div class='v-stack'>
                    <div class="h-stack">
                        <span class='label'>
                            {{ $t('OrderReceiverInfoComponent.vi_tri_dia_chi') }}:
                        </span>
                    </div>

                    <div class="map-container">
                        <client-only>
                            <LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
                                leafletMap = e;
                                leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

                                initLeafletMap();
                            }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true"
                                :use-global-leaflet="true">
                                <LControlZoom position="bottomright"></LControlZoom>
                                <span class="current-location-leaflet"
                                    :title="$t('Map.vi_tri_mac_dinh')" v-on:click="() => {
                                        gotoCurrentLocationLeaflet();
                                    }
                                        ">
                                    <Icon name="line-md:my-location-loop" class="my-location-icon" />
                                </span>
                                <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
                                    data-placement="right"
                                    :title="$t('Map.nhan_de_chuyen_loai_map')" v-bind:style="{
                                        backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                                    }" v-on:click="(event: any) => {
                                        if (event.isTrusted) {
                                            if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.roadmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.hyprid;
                                                mapTypeTitle = $t('Map.ve_tinh');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_sateline;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.hyprid
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.streetmap;
                                                mapTypeTitle = $t('Map.co_dien');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_streetmap;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.streetmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.roadmap;
                                                mapTypeTitle = $t('Map.ve_tinh_va_nhan');
                                                mapType = 'roadmap';
                                                buttonMapTileBackgound = map_sateline;
                                            }
                                        } else event.preventDefault();
                                    }
                                        ">
                                    <span>{{ mapTypeTitle }}</span>
                                </div>
                                <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                    :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
                                    name="GoogleMap">
                                </LTileLayer>
                                
                            </LMap>
                        </client-only>
                    </div>

                    <span class='content'>{{ props.init_data.address }}</span>

                </div>

                <div class="v-stack image" v-if="props.init_data?.images?.length">
                    <span class="label">{{ t('OrderReceiverInfoComponent.anh') }}</span>
                    <div class="image-list">
                        <div class="selected-image" v-for="(itemImamge, index) in props.init_data.images" v-on:click="() => {
                                indexImageActive = index;
                                showImageViewerModal = true;
                            }">
                            <img :src="itemImamge?.id ? (domainImage + itemImamge.path) : itemImamge.src" />
                        </div>
                    </div>
                </div>

                <div class='v-stack' v-if="props.init_data?.note?.length">
                    <span class='label'>
                        {{ $t('OrderReceiverInfoComponent.ghi_chu_cho_dia_chi') }}
                    </span>
                    <span class="content">
                        {{ props.init_data.note }}
                    </span>
                </div>
            </div>
        </div>

        <ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal" :listObject="props.init_data.images.map((e:any) => {
            return {
                ...e,
                isBase64: e.id ? false: true,
                path: e.id ? e.path : e.src 
            }
        })" :indexActive="indexImageActive" v-on:close="(e: any) => {
            showImageViewerModal = false
        }"></ImageViewerComponent>
    </VueFinalModal>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import { appConst, appDataStartup, domainImage, formatCurrency, showTranslateProductName, showTranslateProductDescription, validPhone } from "~/assets/AppConst";
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import { PlaceService } from "~/services/placeService/placeService";
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import exifr from "exifr";
import { toast } from 'vue3-toastify';
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue';

var placeService = new PlaceService();

const props = defineProps({
    init_data: null,
})
const emit = defineEmits(['close']);
const nuxtApp = useNuxtApp();

var router = useRouter();
var route = useRoute();
const { t, locale } = useI18n();

var showModal = ref(false);
var user_latitude = useState<any>('user_latitude', () => { return });
var user_longitude = useState<any>('user_longitude', () => { return });

var leafletMap: L.Map;
var mapType = ref("roadmap");
var localeMarkerLeaflet: L.Marker;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('Map.ve_tinh_va_nhan'));

var buttonMapTileBackgound = ref(map_sateline);
var showImageViewerModal = ref(false);
var indexImageActive = ref(0);
onUnmounted(() => {
    nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})

onBeforeMount(async () => {

    nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
        console.log('moving', coor);
        user_latitude.value = coor.latitude;
        user_longitude.value = coor.longitude;
    });
    // let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

    // dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];
});
onMounted(() => {
    console.log(props);
    showModal.value = true;
})

function close() {
    emit('close')
}

async function initLeafletMap() {
    // markersCluster = new nuxtApp.$L.MarkerClusterGroup({
    // 	maxClusterRadius: 5,
    // 	iconCreateFunction: (cluster) => createClusterElement(cluster),
    // }).addTo(leafletMap);
    console.log("init map")
    await setCurrentLocationLeaflet();
    // (leafletMap as any)["gestureHandling"].enable();
}
async function setCurrentLocationLeaflet() {
    leafletMap.setView([props.init_data?.latitude ?? appConst.defaultCoordinate.latitude, props.init_data?.longitude ?? appConst.defaultCoordinate.longitude], 17);
    setLocationLeafletMarker();
}

function setLocationLeafletMarker(){
    if (localeMarkerLeaflet) {
        localeMarkerLeaflet.remove();
    }
    localeMarkerLeaflet = nuxtApp.$L.marker([props.init_data.latitude, props.init_data.longitude], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: marker_location_icon,
            iconSize: appConst.markerCustom.defaultIcon.size,
            className: appConst.markerCustom.defaultIcon.class,
        }),
        rotationOrigin: 'center 75%'
    });

    localeMarkerLeaflet.addTo(leafletMap);
}

async function gotoCurrentLocationLeaflet(event?: Event) {
    if (!event || event.isTrusted == true) {

        leafletMap.flyTo(
            [props.init_data.latitude, props.init_data.longitude],
            17, {
            duration: 1.5
        }
        );
    }
}
</script>

<style lang="scss" src="./OrderReceiverInfoStyles.scss"></style>