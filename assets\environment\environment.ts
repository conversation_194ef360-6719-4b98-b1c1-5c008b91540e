export const environment = {
    production: false,
    domain: "dev.remagan.com",
    DEVTOOLS: 1,
    domainImage: "https://s3-dev.nomnomland.net/remagan.uploads/",
    baseLogoUrl: "https://s3.remagan.com/pro.remagan.uploads/static/remagan-logo.png",
    api_url: "https://api-dev.remagan.com",
    ZaloIDApp: "1735716509130650710",
    GoogleIDApp: '925447132698-nt40eaoj6f9lsj2ti02gl3s2k65ralgc.apps.googleusercontent.com',
    AppleClientId: 'com.remagan',
    AppleClientSecretKey: 'XX5ZLJ83RY',
    appConfig: {
        app_name: "Rẻ mà Gần",
        app_short_name: "<PERSON>ma<PERSON>",
        app_company_name: "CÔNG TY TNHH REMAGAN",
        app_company_address: "111/24 H<PERSON><PERSON> Vươ<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
        app_company_phone: "0879555799",
        app_company_email: "<EMAIL>",
        app_facebook: "https://www.facebook.com/remagan.vn",
        app_zalo_oa: "https://zalo.me/remagan",
        app_zalo_oa_backup: "https://zalo.me/4254915150131453419",
        app_tiktok: 'https://www.tiktok.com/@remagan_vn',
        app_ch_play_download: "https://play.google.com/store/apps/details?id=com.remagan&hl=vi",
        app_app_store_download: "https://apps.apple.com/us/app/remagan-v%E1%BB%ABa-r%E1%BA%BB-v%E1%BB%ABa-g%E1%BA%A7n/id6504094872?platform=iphone"
    },
    GoogleTagsId: null,
    callbackUrlZalo: "https://dev.remagan.com/Oauth-zalo",
    secretKeyZaloLogin: "2sW0cVMmY884p12M0DeG",
    secretKeyGoogleLogin: "925447132698-nt40eaoj6f9lsj2ti02gl3s2k65ralgc.apps.googleusercontent.com",
    stateZalo: "remagan.com",
    secret_key_password: "31323334363738394041724b7276736f::654f31507345445a65703459614f334e",
    firebaseConfig: {
        apiKey: "AIzaSyCCQfNoOYiK07VVDB5Fi9QlQmuHPIt_Bl4",
        authDomain: "remagan-76005.firebaseapp.com",
        projectId: "remagan-76005",
        storageBucket: "remagan-76005.appspot.com",
        messagingSenderId: "854888323388",
        appId: "1:854888323388:web:813098c67f0cb385a9a639",
        measurementId: "G-T84NYTCPVM"
    },
    firebaseConfigSecret: "54mlKkg3jGUMYtGeiFTKphtA7ASx7UxC",
    zaloConfig: {
        appIDZaloLogin: '1735716509130650710',
        // callbackUrlZalo: "https://dev.remagan.com/Oauth-zalo",
        callbackUrlZalo: "http://localhost:3000/Oauth-zalo",
        secretKeyZaloLogin: "2sW0cVMmY884p12M0DeG",
        stateZalo: 'remagan.com'
    },
    mqtt: {
        url: `wss://mqtt.remagan.com:8443/mqtt`,
        username: 'nomnom-admin',
        password: 'FD8bCJeb6gs7OQvD0IaY8jwLg'
    },
    redis: {
        host: "*************",
        port: 6378,
        password: "98we3y54fdfgG4y1I5xd",
        prefix: 'client_dev',
        db: 0
    }
}

export default environment;