<template>
	<v-overlay v-model="showSideBar" location="right" :z-index="1001" v-on:click:outside="() => {
		close()
	}" key="show_side_bar" class="side-bar-overlay-container" persistent content-class='side-bar-container'
		:close-on-content-click="true" no-click-animation v-on:update:model-value="() => {
			console.log('click content', showSideBar);
			if (!showSideBar) {
				close()
			}
		}">

		<div class="side-bar-content-container">

			<div class="user-info">
				<nuxt-link :to="appRoute.ProfileComponent">
					<img loading="lazy" :src="userProfile && userProfile.profile_picture
						? ((appConst.provider_img_domain.some(e => userProfile?.profile_picture?.includes(e))) ? userProfile.profile_picture : domainImage + userProfile.profile_picture)
						: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" />
				</nuxt-link>
				<div class="user-info-buttons" v-if="userProfile?.id">
					<v-btn class="side-bar-btn" variant="tonal" :color="'var(--primary-color-1)'">
						<nuxt-link :to="appRoute.ProfileComponent">
							<Icon name="solar:user-circle-linear" size="20px"></Icon>
							{{ $t("SideBarComponent.trang_ca_nhan") }}
						</nuxt-link>
					</v-btn>
					<v-btn class="side-bar-btn" variant="tonal" :color="'var(--primary-color-2)'" v-on:click="() => {
						logout()
						}">
						<Icon name="material-symbols:logout" size="20px"></Icon>
						{{ $t("SideBarComponent.dang_xuat") }}
					</v-btn>
				</div>
				<div class="user-info-buttons" v-else>
					<v-btn class="side-bar-btn" variant="tonal" :color="'var(--primary-color-1)'"
						v-if="!userProfile?.id">
						<nuxt-link :to="appRoute.LoginComponent">
							<Icon name="material-symbols:login" size="20px"></Icon>
							{{ $t("SideBarComponent.dang_nhap") }}
						</nuxt-link>
					</v-btn>
					<v-btn class="side-bar-btn" variant="tonal" :color="'var(--primary-color-2)'"
						v-if="!userProfile?.id">
						<nuxt-link :to="appRoute.SignupComponent">
							<Icon name="streamline:user-add-plus-solid" size="20px"></Icon>
							{{ $t("SideBarComponent.dang_ky") }}
						</nuxt-link>
					</v-btn>
				</div>
			</div>

			<div class="side-bar-content">
				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.HomeComponent }">
					<nuxt-link :to="appRoute.HomeComponent" class="side-bar-item-content">
						<Icon name="solar:home-smile-bold" size="25px"></Icon>
						{{ $t("SideBarComponent.trang_chu") }}
					</nuxt-link>
				</v-btn>

				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.ProfileComponent }">
					<nuxt-link :to="appRoute.ProfileComponent" class="side-bar-item-content">
						<Icon name="solar:user-circle-linear" size="25px"></Icon>
						{{ $t("SideBarComponent.trang_ca_nhan") }}
					</nuxt-link>
				</v-btn>

				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.AroundComponent }">
					<nuxt-link :to="appRoute.AroundComponent" class="side-bar-item-content">
						<Icon name="solar:point-on-map-bold" size="25px" />
						{{ $t("SideBarComponent.gan_day") }}
					</nuxt-link>
				</v-btn>

				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.CartComponent }">
					<nuxt-link :to="appRoute.CartComponent" class="side-bar-item-content">
						<Icon name="solar:cart-3-linear" size="25px" />
						{{ $t("SideBarComponent.gio_hang") }}
					</nuxt-link>
				</v-btn>

				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.MyShopComponent }">
					<nuxt-link :to="appRoute.MyShopComponent" class="side-bar-item-content">
						<Icon name="famicons:storefront" size="25px" />
						{{ $t("SideBarComponent.cua_hang_cua_toi") }}
					</nuxt-link>
				</v-btn>

				<div class="side-bar-item language-options">
					<div class="side-bar-item-content">
						<Icon name="bi:translate" size="25px" />
						<div>
							<button v-on:click="() => {
								changeLanguage(lang)
							}" class="language-option" v-for="(lang, index) in langOptions"
								:class="{ 'active': locale == lang.code, 'first': index == 0 }">
								{{ ISO6391.getNativeName(lang.code) }}
							</button>
						</div>
					</div>
				</div>

				<v-btn variant="text" class="side-bar-item"
					:class="{ 'active': checkActive() == appRoute.ChatManageComponent }">
					<nuxt-link :to="appRoute.ChatManageComponent" class="side-bar-item-content">
						<Icon name="mynaui:chat-dots" size="25px" />
						{{ $t("SideBarComponent.chat") }}
					</nuxt-link>
					<v-badge v-if="props.unreadMessageCount" inline
						:content="props.unreadMessageCount < 10 ? props.unreadMessageCount : '9+'"
						class="side-bar-badge" :color="'var(--primary-color-2)'">
					</v-badge>
				</v-btn>

				<v-btn variant="text" class="side-bar-item" v-if="userProfile?.id"
					:class="{ 'active': checkActive() == appRoute.ChatManageComponent }" v-on:click="($event: Event) => {
						$event.stopPropagation();
						showNotificationModal = true
					}">
					<nuxt-link class="side-bar-item-content">
						<Icon name="ic:sharp-circle-notifications" size="25px"></Icon>
						{{ $t("SideBarComponent.thong_bao") }}
					</nuxt-link>

					<v-badge v-if="props.unreadCount" :content="props.unreadCount < 10 ? props.unreadCount : '9+'"
						class="side-bar-badge" inline :color="'var(--primary-color-2)'"></v-badge>
				</v-btn>
				<div class='h-stack other-detail-options download-app-detail-options' v-if="!webInApp">
					<nuxt-link class="other-option download-app-option" target="_blank"
						:to="environment.appConfig.app_ch_play_download"
						:title="$t('ProfileComponent.tai_qua_ch_play')">
						<Icon name="logos:google-play-icon"></Icon>
						<div class="v-stack">
							<em class="android">Get it on</em>
							<span>Google Play</span>
						</div>
					</nuxt-link>
					<nuxt-link class="other-option download-app-option" target="_blank"
						:to="environment.appConfig.app_app_store_download"
						:title="$t('ProfileComponent.tai_qua_app_store')">
						<Icon name="mdi:apple"></Icon>
						<div class="v-stack">
							<em>Download on the</em>
							<span>App Store</span>
						</div>
					</nuxt-link>
				</div>
				<span class="version" v-on:click="() => {
					reloadNuxtApp()
				}">
					{{ $t("ProfileComponent.phien_ban") }}
					<!-- Phiên bản  -->
					{{ webInApp ? webInApp.appInstalledInfo : nuxtApp.$config.public.appVersion }}
				</span>
			</div>
			
			<v-overlay v-model="showNotificationModal" location="bottom" :z-index="1101" v-on:click:outside="() => {
				showNotificationModal = false
			}" key="show_product_select" class="notifications-overlay-container" persistent
				content-class='notifications-content-container' no-click-animation>
				<div class="noti-label">{{ $t('NotificationsComponent.thong_bao') }}
					<button class="refresh-noti" :disabled="refreshing || loadingMore" v-on:click="(e: any) => {
						e.stopPropagation();
						refreshListNotifications();
					}">
						<Icon :name="refreshing ? 'eos-icons:loading' : 'material-symbols:refresh'"></Icon>
					</button>
					<button class="close-noti" v-on:click="() => {
						showNotificationModal = false
					}">
						<Icon name="clarity:times-line" size="25"></Icon>
					</button>
				</div>
				<NotificationsComponent :notificationData="JSON.parse(JSON.stringify(notificationData))" :count="count"
					:loadingMore="loadingMore" :goUp="goToTopListNoti" :unread_count="props.unreadCount"
					v-on:unread_change="(e: any) => {
						handleUnreadChange(e)
					}" v-on:load_more_noti="async () => {
						if (notificationData.length < count) {
							loadingMore = true;
							await getMoreNotifications();
							loadingMore = false;
						}
					}"></NotificationsComponent>
			</v-overlay>
			
		</div>

	</v-overlay>
</template>

<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";
import non_avatar from '~/assets/image/non-avatar.jpg';

import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import { ShopService } from "~/services/shopService/shopService";
import ResetCartComponent from "~/components/resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "~/components/confirm18Age/Confirm18AgeComponent.vue";
import { AuthService } from "~/services/authService/authService";
import { HttpStatusCode } from "axios";
import { UserService } from "~/services/userService/userService";
import { toast } from "vue3-toastify";
import environment from "~/assets/environment/environment";

const { t, locale, locales, setLocale } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.CartComponent')
});
const emit = defineEmits(['close']);
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

const props = defineProps({
	unreadMessageCount: null,
	unreadCount: null
});

var authService = new AuthService();
var userService = new UserService();

var showSideBar = ref(false);

var userProfile = ref();

var webInApp = ref(null as any);

var showNotificationModal = useState('show_noti_modal', () => { return false });
var notificationData = useState<any>('user_notification', () => { return [] });
var unreadCount = useState('user_notify_unread', () => { return 0 });
var count = ref(0);
var loadingMore = ref(false);
var refreshing = ref(false);
var goToTopListNoti = ref(0);

const availableLocales = computed(() => {
	return locales.value.filter(i => i.code)
})
var langOptions = ref([] as any);

onMounted(async () => {
	showSideBar.value = true;

	langOptions.value = availableLocales.value.map(el => {
		return el
	})

	userProfile.value = await authService.checkAuth();
})

import { useLogout } from '~/composables/useLogout';
const { logout, logingOut } = useLogout();
// ...existing code...
// Replace the local logout with the composable's logout


function close() {
	showSideBar.value = false
	emit('close');
}

function checkActive() {
	if (route.path.includes(appRoute.AroundComponent)) return appRoute.AroundComponent;
	if (route.path.includes(appRoute.HomeComponent)) return appRoute.HomeComponent;
	if (route.path.includes(appRoute.ProfileComponent)) return appRoute.ProfileComponent;
	if (route.path.includes(appRoute.CartComponent)) return appRoute.CartComponent;
	if (route.path.includes(appRoute.SearchComponent)) return appRoute.SearchComponent;
	if (route.path.includes(appRoute.ChatManageComponent)) return appRoute.ChatManageComponent;
	if (route.path.includes(appRoute.MyShopComponent)) return appRoute.MyShopComponent;
	return '';
}

const getListNotifications = () => {
	return new Promise((resolve) => {
		if (userProfile.value?.id) {
			userService.listNotify(0, notificationData.value?.length > 20 ? notificationData.value?.length : 20).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					notificationData.value = JSON.parse(JSON.stringify(res.body.data));
					count.value = res.body.count;
					unreadCount.value = res.body.unread;
					resolve(notificationData.value)
				}
				else {
					notificationData.value = [];
					count.value = 0;
					unreadCount.value = 0
					resolve(null);
				}
			}).catch(() => {
				notificationData.value = [];
				count.value = 0;
				unreadCount.value = 0
				resolve(null);
			})
		}
		else {
			notificationData.value = [];
			count.value = 0;
			unreadCount.value = 0
			resolve(null);
		}

	})
}

async function refreshListNotifications() {
	refreshing.value = true;
	await getListNotifications();
	goToTopListNoti.value = goToTopListNoti.value + 1;
	refreshing.value = false;

}

function getMoreNotifications() {
	return new Promise((resolve) => {
		userService.listNotify(notificationData.value?.length).then((res) => {
			if (res.status == HttpStatusCode.Ok) {
				notificationData.value = [...notificationData.value, ...res.body.data]
				count.value = res.body.count;
				unreadCount.value = res.body.unread;
				resolve(notificationData.value)
			}
		})
	})
}

function handleUnreadChange(data: any) {
	getListNotifications()
	// if (data.item == 'all') {
	//   getListNotifications()
	// }
	// else {
	//   let indexChange = notificationData.value.findIndex(function (e: any) {
	//     return e.id == data.item?.id
	//   })

	//   if (indexChange != -1) {
	//     notificationData.value[indexChange] = { ...data.item };
	//   }
	//   unreadCount.value = data.unread_count;
	//   count.value = data.count;
	// }

}

function changeLanguage(lang = langOptions.value[0]) {
	// locale.value = lang.code;
	setLocale(lang.code)
	if (userProfile.value?.id) {
		userService.switchLanguage(lang.code);
	}
	localStorage.setItem(appConst.storageKey.language, lang.code);
	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.setLanguage,
		data: lang.code
	})
}

</script>

<style lang="scss" src="./SideBarStyles.scss"></style>