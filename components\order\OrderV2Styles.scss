.order-v2-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-view);
  margin: auto;
  display: flex;
  flex-direction: column;
  background: #f4f4f4;
  font-size: 17px;
  overflow: auto;

  & .leaflet-map-order-container {
    height: 0 !important;
    max-height: 0 !important;
    overflow: hidden;
    display: none;
  }

  & .order-header {
    line-height: 1.2;

    & .header-middle {
      line-height: 1.2;

      &>h3 {
        font-size: 17px;
      }

      & em {
        font-size: 13px;
        font-weight: 400;
        color: #c3ffa9;
        font-style: normal;
      }
    }
  }

  /* gap: 10px; */
  /* padding: 0 10px; */

  // > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   background: white;
  // }

  &>.order-content-container {
    display: flex;
    flex-direction: column;
    background: #f4f4f4;
    font-size: 17px;
    padding: 10px;
    // overflow: auto;

    &>.receiver-info {
      display: flex;
      gap: 5px;
      background: white;
      margin-bottom: 10px;
      padding: 10px;
      align-items: flex-start;
      font-size: 1rem;
      cursor: pointer;
      border-radius: 10px;

      &>svg {
        color: var(--primary-color-1);
        font-size: 20px;
        margin-top: 2px;
      }

      &>.user-info {
        display: flex;
        flex-direction: column;
        color: #19191b;
        font-weight: 700;
        font-size: 15px;
        flex: 1;

        &>.address {
          color: #545454;
          font-weight: 400;
          font-size: 13px;
        }

        &>.address.error,
        .name-phone>.error {
          color: var(--primary-color-2);
        }
      }

      &>.select-receiver {
        display: flex;
        margin-left: auto;
        color: #7b7b7b;
        align-self: flex-start;
        margin-top: 5px;
        align-items: center;
        height: 100%;
      }
    }

    &>.cart-selected-items {
      display: flex;
      // flex-direction: column;
      flex-direction: row;
      flex-wrap: wrap;
      padding: 10px;
      background: white;
      flex: 1;
      margin-bottom: 10px;
      border-radius: 10px;

      &>.cart-item-shop {
        display: flex;
        flex-direction: column;
        gap: 5px;
        align-items: center;
        width: 100%;
        padding-bottom: 10px;
        border-bottom: thin solid #f3f3f3;

        &>.shop-info {
          display: flex;
          gap: 5px;
          flex-wrap: wrap;
          align-items: center;
          width: 100%;

          &>svg {
            color: var(--primary-color-1);
            font-size: 20px;
          }

          & a {
            font-size: 15px;
            color: black;
            font-weight: 700;
          }

          & > .chat-to-shop{
            color: var(--primary-color-1);
            height: 30px;
            padding: 5px;
            font-weight: 700;
            text-transform: none;
            margin-left: auto;
          }
        }
        &>.closed {
          font-size: 13px;
          font-style: italic;
          color: var(--primary-color-2);
          line-height: 1;
          flex: 1;
          text-align: right;
          align-self: center;
          width: 100%;
          font-weight: 700;
          display: flex;
          align-items: center;
        }
      }

      

      &>.cart-item-container {
        --cart-item-width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 10px;
        color: black;
        gap: 10px;
        position: relative;
        width: var(--cart-item-width);

        // @media screen and (min-width: 1001px) {
        //   --cart-item-width: calc(50%);
        // }

        &>.checkbox {
          margin-top: 35px;
          transform: translateY(-50%);
          animation: none !important;

          &>svg.checked {
            color: var(--primary-color-1);
          }

          &>svg {
            color: #e8e7e7;
            font-size: 25px;
          }
        }

        &>img {
          width: 70px;
          height: 70px;
          aspect-ratio: 1;
          border-radius: 5px;
          object-fit: cover;
          background: var(--color-background-2);
        }

        &>.item-cart-detail {
          align-items: flex-start;
          flex: 1;
          overflow: hidden;

          &>.item-cart-product-name {
            font-weight: 500;
            color: black;
            line-height: normal;
            display: -webkit-box;
            line-clamp: 1;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 15px;
          }

          &>.product-price {
            color: black;
            font-weight: 700;
            font-size: 17px;
            display: flex;
            flex-direction: column;
            line-height: 20px;
            margin-top: 25px;
            width: 100%;

            &>em {
              color: #a8a7a7;
              text-decoration: line-through;
              font-weight: 400;
              font-size: 13px;
              line-height: 15px;
              min-height: 15px;
              font-style: normal;
              opacity: 0;
              max-width: 55%;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            &>em.show {
              opacity: 1;
            }
          }

          &>.item-cart-quantity {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            margin-top: -10px;

            &>span {
              margin: 0 10px;
            }

            &>.quantity-button {
              color: black;
              border: none;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 30px;
              height: 30px;
              padding: 0;
              font-size: 17px;
              background: #f3f3f3;
              border-radius: 0;
            }

            &>.quantity-button:disabled {
              opacity: 0.5;
            }

            &>.delete-item-cart {
              margin-left: 5px;
              color: var(--primary-color-2);
              // align-self: flex-end;
            }

            .price-input {
              // border: thin solid var(--color-text-note);
              border-width: 0 0 1px 0;
              margin: 0;
              width: 40px;
              height: 30px;
              text-align: center;
              font-weight: 600;
              outline: none;
              display: flex;
              font-size: 15px;
              background: #fbfbfb;
            }
          }

          &>.note-input {
            width: 100%;
            outline: none;
            background: #f4f4f4;
            border-radius: 3px;
            margin-top: 5px;
            padding: 5px;
            font-size: 13px;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            color: #545454;

            & p {
              word-break: break-word;
            }

            & svg {
              margin-top: 2px;
              margin-right: 4px;
              font-size: 15px;
              min-width: 15px;
            }
          }
        }

        &:not(:last-child)::after {
          content: "";
          height: 1px;
          width: calc(100%);
          background: #f3f3f3;
          position: absolute;
          bottom: 0;
        }
      }

      &>.selected-total {
        font-size: 13px;
        color: black;
        font-weight: 400;
        text-align: right;
        margin-top: 10px;
        width: 100%;

        &>em {
          font-weight: 700;
          font-style: normal;
        }
      }
    }

    &>.payment-method,
    >.note-container {
      display: flex;
      padding: 10px;
      margin-bottom: 10px;
      background: white;
      align-items: flex-start;
      border-radius: 10px;

      &>.icon-label {
        font-size: 20px;
        color: var(--primary-color-1);
        padding-top: 0;
      }

      &>.content-container {
        padding-left: 5px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        &>.label {
          font-size: 15px;
          color: black;
          font-weight: 700;
        }

        &>.content {
          color: #545454;
          font-size: 14px;
          width: 100%;

          &>.content-item {
            display: flex;
            gap: 3px;
            margin: 0 auto 0 0;
            justify-self: stretch;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            color: #2e2d30;

            &>svg {
              color: #e8e7e7;
              font-size: 20px;
            }
          }

          &>.content-item.active {
            &>svg {
              color: var(--primary-color-1);
              font-size: 20px;
            }
          }
        }

        &>.note-order-input-container {
          width: 100%;
          background-color: var(--color-background-2);
          border-radius: 5px;
          margin-top: 5px;

          &>.note-order-input {
            padding: 5px 10px;
            width: 100%;
            outline: none;
            resize: none;
            font-size: 15px;
          }
        }

        & .delivery-price {
          font-size: 13px;
          font-style: normal;
          margin-top: 5px;
          font-weight: 400;
          color: var(--primary-color-1);
        }

        & .delivery-partners-content-container {
          display: flex;
          flex-direction: column;
          margin-top: 15px;
          flex: 1;
          width: 100%;

          & .delivery-partners-content {
            color: #545454;
            font-size: 14px;
            width: 100%;

            &>.partner-logo {
              height: 25px;
              margin-top: 5px;
              margin-right: 10px;
            }
          }

          & .change-partner {
            font-size: 13px;
            font-style: italic;
            margin-left: 10px;
            margin-top: 5px;
            font-weight: 700;
            color: var(--primary-color-1);
          }

          & .service-type {
            margin-top: 10px;

            &>em {
              font-weight: 400;
              color: var(--primary-color-1);
              margin-left: 10px;
            }
          }

          & .list-options-delivery-price {
            display: flex;
            flex: 1;
            justify-content: space-evenly;
            flex-wrap: wrap;
            width: 100%;

            & .delivery-price-option {
              display: flex;
              gap: 3px;
              margin: 5px auto 0 0;
              justify-self: stretch;
              font-size: 14px;
              font-weight: 600;
              cursor: pointer;
              color: #2e2d30;
              animation: none;
              text-align: left;

              &:disabled {
                opacity: .6;
              }

              &>svg {
                color: #e8e7e7;
                font-size: 20px;
              }

              & em {
                color: var(--primary-color-1);
                font-weight: 700;
              }
            }

            &>.delivery-price-option.selected>svg {
              color: var(--primary-color-1);
            }
          }

          & .non-delivery-partners {
            font-size: 15px;
            color: var(--primary-color-2);
            font-weight: 600;
          }
        }
      }
    }

    &>.payment-info-container {
      padding: 10px 10px 0;
      display: flex;
      flex-direction: column;
      background-color: white;
      font-size: 17px;
      border-radius: 10px;
      // margin-bottom: 10px;

      &>.label {
        font-size: 15px;
        color: black;
        font-weight: 700;
        margin-bottom: 5px;
      }

      &>.payment-info-content {
        justify-content: space-between;
        align-items: flex-start;
        display: flex;
        font-weight: 500;
        font-size: 14px;
        color: #545454;
        margin-bottom: 3px;
      }

      &>.payment-info-content>.value {
        font-size: 15px;
        color: #2e2d30;
        font-weight: 500;
      }

      &>.payment-info-content.off>.value {
        font-size: 15px;
        color: var(--primary-color-2);
      }

      &>.payment-info-content.total-left {
        // border-bottom: thin solid #88888a;
        color: black;
        padding: 7px 0 5px 0;
        border-top: thin solid #f3f3f3;
        margin-top: 5px;

        &>.value {
          font-weight: bold;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          font-size: 1.1em;

          &>.note {
            font-size: 1rem;
          }
        }
      }
    }
  }

  &>.order-privacy {
    display: flex;
    flex-direction: column;
    background: transparent;
    color: #545454;
    font-size: 13px;
    text-align: center;
    padding: 0 0 10px 0;

    &>a {
      color: var(--primary-color-1);
      font-weight: 700;
    }
  }

  &>.order-footer {
    position: sticky;
    bottom: 0;
    left: 0;
    padding: 0 10px;
    background-color: white;
    font-size: 17px;

    &>.order-footer-content {
      box-shadow: 0 -4px 4px 0 rgb(0, 0, 0, 5%);
      padding: 10px 0 15px 0;
      align-items: center;
      justify-content: space-between;
      color: white;
      font-size: 1em;
      margin-top: auto;
      font-weight: 500;
      display: flex;
      flex-direction: column;

      &>.total {
        color: #575757;
        font-size: 1em;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 10px;

        &>span {
          font-size: 15px;
          color: black;
          font-weight: 700;

          &>em {
            font-weight: 400;
            font-style: normal;
          }
        }

        &>.price {
          color: var(--primary-color-2);
          font-weight: 800;
          font-size: 17px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          &>span {
            width: 100%;
            color: #545454;
            font-size: 13px;
            font-weight: 700;

            &>em {
              color: var(--primary-color-1);
              font-style: normal;
            }
          }
        }

        &>.none-price {
          color: var(--primary-color-1);
          font-weight: 500;
        }
      }

      &>button {
        width: 100%;
        height: 100%;
        margin-left: auto;
        border-radius: 5px;
        background-color: var(--primary-color-1);
        padding: 10px;
        color: white;
        font-weight: 800;
        border: none;
        gap: 5px;
        font-size: 1em;
        display: flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
      }
    }
  }

  .empty-cart-avt {
    margin: 0 10px;
    justify-content: center;
    width: 250px;
    height: 250px;
    object-fit: contain;
  }

  .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding-top: 30px;
    flex: 1;
    background: white;
  }

  .empty-cart>.text {
    color: var(--color-text-black);
    font-weight: bold;
    font-size: 1.2em;
  }

  .empty-cart>.action {
    color: var(--color-text-note);
    font-style: italic;
    font-size: 1em;
    border: none;
    background: transparent;
  }
}

.saved-address-modal-container {
  width: 500px !important;
  max-width: 95% !important;
  height: 800px !important;
  max-height: 95% !important;
  padding: 0 !important;
}

.complete-order-container {
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.complete-order-container>.complete-order-image {
  margin: 10px auto;
  justify-content: center;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: contain;
}

.complete-order-container>.complete-order-message {
  font-size: 1.3em;
  font-weight: 500;
  text-align: center;
}

.complete-order-container>.complete-order-action {
  font-size: 1.3em;
  color: var(--primary-color-1);
  cursor: pointer;
  font-weight: 500;
  text-align: center;
  background: transparent;
  border: none;
  outline: none;
}