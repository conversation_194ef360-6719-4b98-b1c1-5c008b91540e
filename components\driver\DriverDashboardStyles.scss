.driver-dashboard-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  // background-color: white;
  position: relative;
  overflow: auto;
  // background: url("~/assets/image_08_04_2024/driver-background.jpg"), linear-gradient(to bottom, white, #f5f6fa) ;
  background-size: 100% 300px;
  background-repeat: no-repeat;
  font-size: calc(var(--font-size) * 1.3);
  background-color: #f5f6fa;

  &>.driver-dashboard-content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    &>.primary-driver-info {
      display: flex;
      padding: 10px;
      border-radius: 10px;
      box-shadow: 0 0 4px rgb(0, 0, 0, 20%);
      position: absolute;
      top: 15px;
      right: 15px;
      max-width: 300px;
      z-index: 1;
      background: white;
      gap: 5px;
      font-size: 17px;
      flex-wrap: wrap;
      opacity: .95;

      & .user-avatar {
        width: 75px;
        min-width: 75px;
        height: 75px;
        border: 3px solid white;
        object-fit: cover;
        border-radius: 50%;
        cursor: pointer;
      }

      & .none-avatar {
        /* flex: 1; */
        margin: 10px;
        justify-content: center;
        width: 75px;
        height: 75px;
        object-fit: contain;
        border-radius: 50%;
        cursor: pointer;
      }

      &>.driver-info {
        display: flex;
        flex-direction: column;
        max-width: calc(100% - 80px);

        & .name {
          font-weight: bold;
          font-size: 20px;
          color: var(--primary-color-1);
          display: flex;

          
        }
        & .gender-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          font-weight: 600;
          font-size: 20px;
          margin-left: 5px;

          &.male {
            color: #02aae8;
            margin-bottom: 3px;
          }

          &.female {
            color: #ef5890;
            margin-top: 3px;
          }

          &.other {
            color: #9c7ccf;
          }
        }
        & .user-info {
          font-size: 15px;
          color: #595e63;
          font-weight: 600;
        }

        & .other-info {
          font-size: 15px;
          color: #818086;
          font-weight: 500;
        }

      }

      &>.action-button {
        width: 100%;
        min-width: 100%;
        color: #595e63;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        white-space: balance;
        text-overflow: ellipsis;
        flex: 1;
        display: flex;
        padding: 10px;
        background: #f5f6fa;
        border-radius: 10px;

        &>svg {
          width: 25px;
          min-width: 25px;
        }

        &>span {
          overflow: hidden;
          text-overflow: ellipsis;
          width: auto;
        }

        & .online-dot {
          width: 15px;
          height: 15px;
          min-width: 15px;
          margin-right: 5px;
          border-radius: 50%;
          background: green;
          box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.5);
          animation: l1 2s infinite;
        }

        @keyframes l1 {
          100% {
            box-shadow: 0 0 0 20px #0f00
          }
        }

        & .online-dot.off {
          background: #818086;
          animation: none;
        }
      }
    }

    &>.driver-dashboard-actions {
      display: flex;
      flex-direction: column;
      gap: 5px;
      min-height: auto;
      background: #f5f6fa;

      &>.driver-dashboard-actions {
        display: flex;
        gap: 5px;
        background: #f5f6fa;
        border-radius: 10px;
        padding: 10px 5px;

        &>.delivery-history-option {
          display: flex;
          color: #595e63;
          width: 100%;
          align-items: center;
          justify-content: flex-start;
          gap: 5px;
          border-radius: 10px;
          padding: 10px;
          position: relative;
          background: #ededed;

          &>.option-icon {
            width: 30px;
            height: 30px;
            border-radius: 10px;
            margin: 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #0d80aa;
          }

          &>.option-label {
            white-space: nowrap;
            text-align: left;
            // display: flex;
            color: #28262c;
            font-size: 17px;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;

            &>em {
              font-size: 13px;
            }
          }

          &>.checked-icon {
            position: absolute;
            top: 5px;
            right: 5px;
          }

          &>.icon-right {
            margin-left: auto;
            color: #0d80aa;
            font-size: 25px;
          }
        }
      }
    }
  }

  & .map-container {
    // display: flex;
    width: 100%;
    flex: 1;
    min-height: 50%;
    display: flex;
    height: 100%;

    position: relative;

    &>#leaflet_map {
      // flex: 1;
      // align-self: unsafe;
      // height: 100%;
      // // min-height: 450px;
      // outline: none;
      min-height: inherit;
      position: absolute;
      z-index: 1;
      font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system,
        Tahoma, "Segoe UI";
      outline: none;
    }

    & .current-location-leaflet {
      bottom: 175px;
    }

    & .map-type-btn {
      bottom: 60px !important;
    }

    & .leaflet-bottom {
      bottom: 50px;
    }

    & .user-location {
      width: 100%;
      height: 100%;
      color: var(--primary-color-2);
    }
  }
}