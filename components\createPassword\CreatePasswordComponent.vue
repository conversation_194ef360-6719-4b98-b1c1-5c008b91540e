<template>
  <VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false" :overlay-behavior="'persist'"
    content-class="v-stack create-password-modal" v-model="showCreatePasswordModal" v-on:closed="() => {
      showCreatePasswordModal = false
    }" contentTransition="vfm-fade">
    <div class="create-password-container">
      <HeaderComponent :title="props.title ?? t('AppRouteTitle.CreatePasswordComponent')">
        <template v-slot:header_left></template>
      </HeaderComponent>
      <div class="create-password-content">
        <span class="label-content">{{ t('CreatePasswordComponent.tao_mat_khau_cho') }} <em> {{ props.userData?.name }}
          </em></span>
        <div class='h-stack'>
          <span class='label'>
            {{ $t('LoginComponent.email_ten_dang_nhap') }}: 
          </span>

          <input name='email' maxLength=255 class='content-input' :value="userData.user_name" readonly />
        </div>
        <div class='password-content'>
          <div class='content-input-group'>
            <input :title="$t('CreatePasswordComponent.mat_khau')" name="password" maxLength=255 class='content-input'
              :placeholder="$t('CreatePasswordComponent.nhap_mat_khau')" :type="passwordShow ? 'text' : 'password'"
              v-model="password" :disabled="isSaving" v-on:input="($event: any) => {
                password = $event.currentTarget.value;
                passwordValidation();
              }" v-on:blur="() => { passwordValidation(); }" />
            <button v-on:click="() => { passwordShow = !passwordShow }">
              <Icon name="mage:eye-closed" v-if="!passwordShow" />
              <Icon name="mage:eye" v-if="passwordShow" />
            </button>
          </div>
          <span class="error-message" :class="{ 'success': passwordErr1 == false }">
            <Icon name="ph:dot-outline-fill" v-if="passwordErr1"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('CreatePasswordComponent.phai_chua_so_chu_cai_ky_tu_dac_biet') }}&nbsp;
            <v-tooltip location="bottom" open-on-click
              :text="`${$t('CreatePasswordComponent.ky_tu_dac_biet')}: !@#$%^&*()_+-=[]{};:|,.<>?\\`">
              <template v-slot:activator="{ props }">
                <span v-bind="props">
                  <Icon name="bi:question-circle"></Icon>
                </span>
              </template>
            </v-tooltip>
          </span>
          <span class="error-message" :class="{ 'success': passwordErr2 == false }">
            <Icon name="ph:dot-outline-fill" v-if="passwordErr2"></Icon>
            <Icon name="material-symbols:check-small-rounded" v-else></Icon>
            {{ $t('CreatePasswordComponent.phai_tu_6_20_ky_tu') }}
          </span>
        </div>
      </div>
      <div class="create-password-footer">
        <button class='reject-button' v-on:click="() => {
          close()
        }">
          {{ $t('CreatePasswordComponent.dong') }}
        </button>
        <button class='accept-button' v-on:click="() => {
          submit()
        }">
          {{ $t('CreatePasswordComponent.tao') }}
        </button>
      </div>
    </div>
  </VueFinalModal>


</template>

<style lang="scss" src="./CreatePasswordStyles.scss"></style>

<script setup lang="ts">

import { PublicService } from "~/services/publicService/publicService";
import { VueFinalModal } from "vue-final-modal";
import { AuthService } from "~/services/authService/authService";
import { UserService } from "~/services/userService/userService";
import { toast } from "vue3-toastify";
import { appConst, encryptPassword } from "~/assets/AppConst";
import environment from "~/assets/environment/environment";
import { HttpStatusCode } from "axios";
// import passwordGenerator from 'password-generator';

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const props = defineProps({
  userData: null,
  title: null,
  showModal: null
})
const emit = defineEmits(['close', 'submit']);

let checkEmailTimeOut: any = null;
let resendTimeInterval: any = null;

let authService = new AuthService();
let userService = new UserService();
let publicService = new PublicService();

var showCreatePasswordModal = ref(props.showModal)
var passwordShow = ref(true);
var password = ref("");
var isSaving = ref(false);
var passwordErr1 = ref(true);
var passwordErr2 = ref(true);

onUnmounted(async () => { });
onUpdated(() => {
  

})
onMounted(async () => {
  password.value = passwordGenerator(12);
  if (window.crypto) {
    passwordValidation();
  }

});
function passwordGenerator(length = 12) {
  var charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{};:|,.<>?\\"
  const array = new Uint8Array(length);
  window.crypto.getRandomValues(array);
  return Array.from(array, byte => charset[byte % charset.length]).join('');
}

function passwordValidation() {
  let re = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};:\\|,.<>\/?]).{1,}$/;
  if (!re.test(password.value)) {
    passwordErr1.value = true;
  }
  else {
    passwordErr1.value = false;
  }
  if (password.value?.length < 6 || password.value?.length > 20) {
    passwordErr2.value = true;
  }
  else {
    passwordErr2.value = false;
  }

}

async function submit() {
  isSaving.value = true;
  passwordShow.value = false;
  passwordValidation();
  if (passwordErr1.value || passwordErr2.value) {
    hightlightError();
    isSaving.value = false;
    passwordShow.value = true;
  }
  else {
    userService.updatePasswordClient(await encryptPassword(password.value)).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        toast.success(t('CreatePasswordComponent.tao_mat_khau_thanh_cong'));
        close(true);
        nuxtApp.$emit(appConst.event_key.user_info_change);
        isSaving.value = false;
      }
      else {
        toast.error(res.body?.message ?? t('CreatePasswordComponent.tao_mat_khau_that_bai'));
        hightlightError();
        isSaving.value = false;
        passwordShow.value = true;
      }
    })
  }
}
function hightlightError() {
  let els = document.getElementsByClassName("error-message");
  Array.prototype.forEach.call(els, function (el) {
    el.classList.add("hight-light");
    setTimeout(() => {
      el.classList.remove("hight-light");
    }, 1000);
  });
}
function close(submit = false) {
  // showCreatePasswordModal.value = false;
  if (submit) {
    emit('submit', true)
  }
  else {
    emit('close')
  }
}

</script>
