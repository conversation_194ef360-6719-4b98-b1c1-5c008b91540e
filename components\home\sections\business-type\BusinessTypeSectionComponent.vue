<template>
	<div class="business-type-list content-list">
		<div class="stack-content-business-type">
			<Swiper v-if="dataBusinessType?.length" class="my-carousel stack-carousel business-type-carousel"
				:modules="[SwiperNavigation, SwiperFreeMode, SwiperGrid]" :grid="{
					rows: 2,
					fill: 'row'
				}" :slides-per-view="'auto'" :loop="false" :effect="'creative'" :navigation="false" :freeMode=false
				:space-between="10" :centered-slides="false" :centered-slides-bounds="true" key="business-type-carousel"
				ref="business_type_el">
				<SwiperSlide class="item-stack-slide business-type-slide" v-for="item of dataBusinessType"
					:key="'sale_off_' + item.id">
					<nuxt-link class="item-stack business-type-item" :to="{
						path: appRoute.AroundComponent,
						query: {
							filter: JSON.stringify({
								...props.filter_data,
								// category_ids: [id],
								business_type_id: item.id
							})
						}
					}" :title="item.name">
						<div class="business-type-logo">
							<img :src="domainImage + item.thumbnail" alt="" v-if="item.thumbnail" loading="lazy">
							<Icon name="garden:box-3d-fill-16" v-else></Icon>
						</div>

						<span class="business-type-name">
							{{ item.name }}
						</span>
					</nuxt-link>
				</SwiperSlide>
			</Swiper>
		</div>
	</div>

</template>

<style lang="scss" src="./BusinessTypeSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();
const emit = defineEmits(['business_type_click'])
const props = defineProps({
	filter_data: null,
	enable_load: null
})

var business_type_el = useState<any>(() => { return null });
var dataBusinessType = ref<any>(appDataStartup.listBusinessType);

onBeforeMount(() => {
	nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
		dataBusinessType.value = appDataStartup.listBusinessType;
	});
})
onUnmounted(async () => {
});
onMounted(async () => {

});

</script>
