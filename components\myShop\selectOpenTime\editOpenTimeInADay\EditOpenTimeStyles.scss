.edit-open-time-container {
  max-height: 95dvh;
  height: fit-content;
  min-height: 700px;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  overflow: hidden;
  background-color: white;
  position: relative;

  & > .edit-open-time-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-bottom: 10px;
    padding-bottom: 10px;
    overflow: auto;

    & > .days-content {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      padding: 10px;
      flex-wrap: wrap;

      & > .day-select {
        width: 50px;
        height: 50px;
        border-radius: 2em;
        border: thin solid #ddd;
        color: #1a73e8;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      & > .day-select.active {
        color: var(--primary-color-1);
        background: rgb(255 133 156 / 10%);
        border-color: transparent;
        font-weight: bold;
      }
    }

    & > .times-content {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: center;

      & > .time-inputs {
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
        align-items: center;
        width: 100%;

        & > .v-stack {
          gap: 5px;
          width: calc((100% - 60px) / 2);

          & > .input-time {
            background: #f5f6fa;
            border: thin solid transparent;
            border-radius: 10px;
            outline: none;
            display: flex;
            height: 40px;
            justify-content: center;
            align-items: center;

            & input {
              background: #f5f6fa;
              border: none;
            }

            // & [class*="menu_wrap"] {
            //   box-shadow: 0 0 10px rgb(0, 0, 0, 0.3);
            //   border-radius: 20px;
            //   overflow: hidden;
            // }
          }
        }
        & > button {
          width: 40px;
          height: 40px;
          margin-top: 25px;
          border-radius: 50%;
          background: #f5f6fa;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      & .none-time {
        margin: 15px;
        font-style: italic;
        color: var(--color-text-note);
      }

      & > .add-time {
        padding: 10px 15px;
        border-radius: 10px;
        border: thin solid #ddd;
        color: #1a73e8;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
      }
    }
  }
  & > .action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;
    user-select: none;

    & > button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }
    & > .cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }
    & > .save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    & > .cancel-button:disabled {
      // color: var(--color-text-note);
      // border-color: var(--color-text-note);
      opacity: 0.5;
    }
    & > .save-button:disabled {
      // background: #ccc;
      // color: var(--primary-color-1);
      // border-color: #ccc;
      opacity: 0.5;
    }
  }
}
