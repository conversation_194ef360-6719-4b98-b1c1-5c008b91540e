<template>
	<div class="public-container">
		<div class='delete-account-container'>
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					close();
				}">
					<Icon name="lucide:chevron-left"/>
				</button>
			</div>
			<h3>{{ appRouteTitle.DeleteAccountComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.DeleteAccountComponent')">
			</SubHeaderV2Component>
			<div class='v-stack delete-account-content'>
				<div class="v-stack delete-account" v-if="step == 1">
					<img loading="lazy" :src='delete_account' :placeholder="delete_account"
						class="empty-delete-account-avt" />
					<button class="action delete-button accept-button" v-on:click="() => {
						step = 2;
					}">
						{{ $t('DeleteAccountComponent.xoa_tai_khoan') }}
					</button>
				</div>
				<div class="v-stack delete-account" v-else-if="step == 2">
					<img loading="lazy" :src='delete_account' :placeholder="delete_account"
						class="empty-delete-account-avt" />
					<span class="delete-account-notice">{{
						$t('DeleteAccountComponent.xoa_tai_khoan_cua_hang_va_san_pham')
						}}</span>
					
					<div class="actions-buttons">
						<button class="action reject-button delete-button" v-on:click="() => {
							close()
						}">
							{{ $t('DeleteAccountComponent.quay_lai') }}
						</button>

						<button class="action accept-button delete-button" v-on:click="() => {
							deleteAccount()
						}">
							{{ $t('DeleteAccountComponent.chac_chan') }}
						</button>
					</div>
					<em class="note">
						{{
						$t('DeleteAccountComponent.xoa_tai_khoan_ghi_chu')
						}}
					</em>
				</div>

			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { AuthService } from "~/services/authService/authService";
import { toast } from "vue3-toastify";

import delete_account from "~/assets/image/delete_account.jpg";
import { HttpStatusCode } from "axios";

const { t } = useI18n();
const nuxtApp = useNuxtApp();
useSeoMeta({
	title: t('AppRouteTitle.DeleteAccountComponent')
});
var router = useRouter();
var route = useRoute();
var refreshing = ref(false);
var isUpdating = ref(false);
var step = ref(1);

var authService = new AuthService();

onBeforeMount(async () => {
	document.title = t('AppRouteTitle.DeleteAccountComponent')
})

function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

function deleteAccount() {
	authService.deleteAccount().then(res => {
		if (res.status == HttpStatusCode.Ok) {
			toast.success(t('DeleteAccountComponent.xoa_thanh_cong'));
			nuxtApp.$emit(appConst.event_key.logout);
			setTimeout(() => {
				router.push(appRoute.HomeComponent)
			}, 2000);
		}
		else {
			toast.error(res.body?.message ?? t('DeleteAccountComponent.xoa_that_bai'));
		}
	}).catch(err => {
		toast.error(t('DeleteAccountComponent.xoa_that_bai'));
	})
}

</script>

<style lang="scss" src="./DeleteAccountStyles.scss"></style>