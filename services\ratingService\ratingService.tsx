import axios from "axios";

import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";


export class RatingService extends BaseHTTPService {
    detailRating(id: string){
        let url = appConst.apiURL.ratingDetail.replaceAll(':id', id);
        return this.https('GET', url);
    }

    createRating(body:any){
        return this.https('POST', appConst.apiURL.createRating, body);
    }

    updateRating(body:any){
        return this.https('POST', appConst.apiURL.updateRating, body);
    }

    deleteRating(id:string){
        let body = {
            id: id
        }
        return this.https('POST', appConst.apiURL.deleteRating, body);
    }

    listRatingByObject(object_id:string, offset = 0, limit = 25){
        let body = {
            object_id: object_id,
            limit: limit,
            offset: offset
        }
        return this.https('POST', appConst.apiURL.listRatingByObject, body);
    }

    calcObjectRating(object_id:string){
        return this.https('GET', appConst.apiURL.calcObjectRating.replaceAll(':object_id', object_id));
    }

    myRatingToObjectId(object_id:string){
        return this.https('GET', appConst.apiURL.myRatingToObject.replaceAll(':object_id', object_id));
    }
}