.notification-item-ref{
    min-height: 75px;
}
.notification-item-container{
    display: flex;
    gap: 5px;
    padding: 10px;
    background: white;
    border-radius: 10px;
    width: 100%;
    position: relative;
    // background: color-mix(in srgb, var(--primary-color-1) 5%, transparent);

    & > .noti-avt{
        width: 50px;
        height: 50px;
        min-width: 50px;
        border-radius: 50%;
        object-fit: cover;
    }

    & > .noti-detail{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        text-align: left;
        line-height: 1.1;

        & > .noti-title{
            font-weight: 600;
            color: #868686;
        }

        & > .noti-content{
            margin-top: 5px;
            font-weight: 500;
            font-size: 13px;
            color: #545454;
            display: -webkit-box;
            --webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        & > .noti-at{
            font-size: 12px;
            font-weight: 600;
            color: #343434;
            font-style: normal;
            margin-top: 5px;
        }
    }

    & > .options-button{
        font-size: 25px;
        color: #868686;
        height: 30px;
        width: 30px;
        margin: auto;
        cursor: pointer;
    }

    & > .new{
        position: absolute;
        top: 5px;
        right: 5px;
    }

    &.unread{
        background: color-mix(in srgb, var(--primary-color-1) 5%, transparent);

        & .noti-title{
            font-weight: 700;
            color: var(--primary-color-1);
        }
    }
}

.divider{
    margin: 5px 0;
}