<template>
	<!-- <div class="public-container" id="chat_detail_container">

	</div> -->
	<div class="chat-detail-container" v-on:scroll="(e) => { listScroll(e); }" ref="chatScrollContainer" :style="{
		paddingTop: `${chatHeader?.getBoundingClientRect().height}px`,
	}" v-if="!isLoading">
		<div class="chat-header" ref="chatHeader">
			<SubHeaderV2Component>
				<template v-slot:header_left>
					<button class="back-button" v-on:click="() => {
						close();
					}">
						<Icon name="solar:round-alt-arrow-left-linear"></Icon>
					</button>
				</template>
				<template v-slot:header_middle>
					<div class="chat-info">
						<div class="avatar-chat" v-if="chatInfo?.type == channel_type.user">
							<img v-if="chatInfo?.members?.[0]?.member_type == member_type.user" :src="chatInfo?.members?.[0]?.member?.profile_picture
								? (appConst.provider_img_domain.some(e => chatInfo?.members?.[0]?.member?.profile_picture?.includes(e))
									? chatInfo?.members?.[0]?.member?.profile_picture
									: (domainImage + chatInfo?.members?.[0]?.member?.profile_picture)
								)
								: non_avatar" class="single" />
							<div class="single" v-if="chatInfo?.members?.[0]?.member_type == member_type.shop">
								<AvatarComponent :imgTitle="chatInfo?.members?.[0]?.member?.name"
									:imgStyle="chatInfo?.members?.[0]?.member?.logo?.style" :imgSrc="chatInfo?.members?.[0]?.member?.logo?.path?.length
										? (domainImage + chatInfo?.members?.[0]?.member?.logo?.path)
										: ''" :width="45" :height="45" />
							</div>
							<div class="online-dot" v-if="chatInfo?.members?.[0]?.member?.user_status"></div>
						</div>
						<div v-else class='avatar-chat group'>
							<div class="multiple">
								<img :id="`${chatInfo?.id}_${chatInfo?.members?.length}_${indexMember}`"
									:hidden="indexMember > 2" v-for="(member, indexMember) in chatInfo?.members" :src="chatInfo?.members?.[0]?.member?.profile_picture
										? ((appConst.provider_img_domain.some(e => member?.member?.profile_picture.includes(e)))
											? member?.member?.profile_picture
											: (domainImage + member?.member?.profile_picture)
										)
										: non_avatar" class="single" :class="{ 'odd': indexMember % 2 != 0 }" :style="{
											zIndex: (chatInfo?.members?.length ?? 0) - indexMember
										}" />
								<div class="count single" v-show="(chatInfo?.members?.length ?? 0) > 3">
									{{ chatInfo?.members?.length }}
								</div>
							</div>

						</div>

						<div class="content-chat">
							<span class="name">{{ chatInfo?.type == channel_type.group ? chatInfo?.name :
								chatInfo?.members?.[0]?.member?.name }}</span>
							<div class="others-info">

								<div class="online-status" v-if="chatInfo?.type == channel_type.user">

									<span
										v-if="chatInfo?.members?.[0]?.member_type == member_type.user && chatInfo?.members?.[0]?.member?.user_status">{{
											$t('ChatDetailComponent.dang_online') }}</span>
									<span v-else-if="chatInfo?.members?.[0]?.member_type == member_type.shop">
										{{ $t('ChatDetailComponent.thuong_tra_loi_trong_vong_30_phut') }}
									</span>
									<!-- <span v-else-if="checkTimeOnline(chatInfo?.members?.[0]?.member?.last_online) != null">{{
									$t('ChatDetailComponent.online_x_truoc', {
										time:
											checkTimeOnline(chatData.last_online)
									}) }}</span> -->
								</div>
								<div class="member-amout" v-if="chatInfo?.type == channel_type.group">
									<Icon name="tdesign:member"></Icon>
									<span>{{ $t('ChatDetailComponent.x_thanh_vien', {
										amount: chatInfo?.members?.length
									})
									}}</span>
								</div>
							</div>
						</div>
					</div>
				</template>
				<template v-slot:header_right>
					<nuxt-link :to="appRoute.HomeComponent" class="right-button">
						<Icon name="solar:shop-2-bold"></Icon>
					</nuxt-link>
					<button class="right-button">
						<Icon name="solar:menu-dots-bold"></Icon>
					</button>
				</template>
			</SubHeaderV2Component>
			<div class="language-select">
				<span>{{ $t('ChatDetailComponent.ban_dang_su_dung_ngon_ngu') }}</span>
				<button class="button-mode select-language-button" dark v-bind="props">
					{{ $t('ChatDetailComponent.thay_doi') }}
					<Icon :name="appConst.flag_icons[selectedLanguage]" v-if="appConst.flag_icons[selectedLanguage]">
					</Icon>
					<span class="default-lang-icon" v-else>{{ selectedLanguage }}</span>

					<v-menu class="language-dropdown-container" location="bottom right" activator="parent" :style="{
						zIndex: 10000
					}">

						<v-list>
							<v-list-item :key="'language_' + lang.code" v-for="(lang, index) in langOptions"
								:active="selectedLanguage == lang.code" activeClass="active" v-on:click="() => {
									selectedLanguage = lang.code;
								}">
								<v-list-item-title class="lang-item" v-on:click="() => {
									changeLanguage(lang.code)
								}">
									<Icon :name="appConst.flag_icons[lang.code]" v-if="appConst.flag_icons[lang.code]">
									</Icon>
									<span class="default-lang-icon" v-else>{{ lang.code }}</span>
									{{ ISO6391.getNativeName(lang.code) }}
								</v-list-item-title>
							</v-list-item>
						</v-list>
					</v-menu>
				</button>

			</div>
		</div>

		<div id="head_of_list_message" ref="headOfListMessage">
			<span v-if="isLoadingMore">{{ $t('ChatDetailComponent.dang_tai') }}</span>
			<!-- <span v-else-if="listMessage.length && viewAllMess" class="viewed-all">{{ $t('ChatDetailComponent.da_xem_het') }}</span> -->
		</div>
		<div class="chat-detail-content-container" v-if="listMessage.length" id="chat_detail_content_container">

			<div class="message-container" :class="{
				'me': mess.member_id == senderId,
				'last-message': mess.member_id != listMessage[indexMess - 1]?.member_id,
				'head-message': mess.member_id != listMessage[indexMess + 1]?.member_id
			}" v-for="(mess, indexMess) in listMessage" :id="`message_${mess.id}_${indexMess}`"
				:key="`message_${mess.id}_${indexMess}`">
				<div class="date-group"
					v-if="checkShowDateSend(mess.created_at, listMessage[indexMess + 1]?.created_at) && mess.status != 2">
					{{ moment(mess.created_at, 'YYYY-MM-DD HH:mm:ss').format('DD.MM.YYYY') }}
				</div>
				<div class="message-group" v-if="mess.status != 2">
					<div class="sender-avatar" :class="{
						'hide': mess.member_id == listMessage[indexMess + 1]?.member_id && !checkShowDateSend(mess.created_at, listMessage[indexMess + 1]?.created_at),
						'product-message': mess.content?.link_product && mess.content?.product_detail
					}" v-if="mess.member_id != senderId">
						<img v-if="chatInfo?.members?.[0]?.member_type == member_type.user" :src="chatInfo?.members?.[0]?.member?.profile_picture
							? (appConst.provider_img_domain.some(e => chatInfo?.members?.[0]?.member?.profile_picture?.includes(e))
								? chatInfo?.members?.[0]?.member?.profile_picture
								: (domainImage + chatInfo?.members?.[0]?.member?.profile_picture)
							)
							: non_avatar" />
						<div class="single" v-if="chatInfo?.members?.[0]?.member_type == member_type.shop">
							<AvatarComponent :imgTitle="chatInfo?.members?.[0]?.member?.name"
								:imgStyle="chatInfo?.members?.[0]?.member?.logo?.style" :imgSrc="chatInfo?.members?.[0]?.member?.logo?.path?.length
									? (domainImage + chatInfo?.members?.[0]?.member?.logo?.path)
									: ''" :width="30" :height="30" />
						</div>
					</div>
					<div class="message-content-group" v-if="mess.status != 3">
						<nuxt-link :to="`${appRoute.ProductComponent}/${mess.content?.link_product}`" v-on:contextmenu="(e: Event) => {
							e.preventDefault();
							// mess.show_actions = true;
							blurInputMessage()
							selectedMessage = JSON.parse(JSON.stringify(mess));

							showSelectMessageModal = true;
						}" v-longpress="(e: any) => {
							e.preventDefault();
							blurInputMessage()
							// mess.show_actions = true;
							selectedMessage = JSON.parse(JSON.stringify(mess));

							showSelectMessageModal = true;
						}" class="message product" tabindex="0" v-if="mess.content?.link_product && mess.content?.product_detail">
							<div class="product-detail">
								<img v-on:error="(event: any) => {
									event.target.src = icon_for_broken_image;
								}" :src="mess.content?.product_detail?.profile_picture?.length ? (domainImage + mess.content?.product_detail?.profile_picture) : icon_for_product"
									:placeholder="icon_for_product"
									:alt="mess.content?.product_detail ? showTranslateProductName(mess.content?.product_detail) : $t('ChatDetailComponent.khong_co_avatar')" />
								<div class="product-info">
									<span class="product-name">{{ showTranslateProductName(mess.content?.product_detail)
									}}</span>
									<div class="price-info">
										<span class='product-price'>
											<em class="off"
												v-if="(mess.content?.product_detail?.price_off != null && parseFloat(mess.content?.product_detail?.price_off) < parseFloat(mess.content?.product_detail?.price))">
												{{
													(parseFloat(mess.content?.product_detail?.price) == 0 ||
														mess.content?.product_detail?.price ==
														null)
														? $t('ChatDetailComponent.gia_lien_he')
														: formatCurrency(parseFloat(mess.content?.product_detail?.price),
															mess.content?.product_detail?.shop?.currency)
												}}</em>{{ (mess.content?.product_detail?.price_off != null &&
													parseFloat(mess.content?.product_detail?.price_off)
													< parseFloat(mess.content?.product_detail?.price)) ?
													formatCurrency(parseFloat(mess.content?.product_detail?.price_off),
														mess.content?.product_detail?.shop?.currency) :
													(parseFloat(mess.content?.product_detail?.price) == 0 ||
														mess.content?.product_detail?.price == null) ?
														$t('ChatDetailComponent.gia_lien_he') :
														formatCurrency(parseFloat(mess.content?.product_detail?.price),
															mess.content?.product_detail?.shop?.currency) }} </span>
									</div>

								</div>
							</div>
						</nuxt-link>
						<nuxt-link :to="props.mode == member_type.user
							? `${appRoute.MyOrderDetailComponent.replace(':order_id', mess.content?.order_detail.short_code ?? mess.content?.link_order)}`
							: props.mode == member_type.user ? `${appRoute.MyShopOrderDetailComponent.replace(':id', mess.content?.order_detail.id ?? mess.content?.link_order)}`
								: `${appRoute.AgentOrderDetailComponent.replace(':id', mess.content?.order_detail.id ?? mess.content?.link_order)}`
							" v-on:contextmenu="(e: Event) => {
								e.preventDefault();
								// mess.show_actions = true;
								blurInputMessage()
								selectedMessage = JSON.parse(JSON.stringify(mess));

								showSelectMessageModal = true;
							}" v-longpress="(e: any) => {
								e.preventDefault();
								// mess.show_actions = true;
								blurInputMessage()
								selectedMessage = JSON.parse(JSON.stringify(mess));

								showSelectMessageModal = true;
							}" tabindex="0" class="message order" v-if="mess.content?.link_order && mess.content?.order_detail">
							<div class="order-info">
								<div class="h-stack">
									<span class="order-code">[{{ mess.content?.order_detail?.short_code }}]</span>
									<span class="order-at">
										{{ moment(mess.content?.order_detail?.created_at).format('HH:mm DD/MM/YYYY') }}
									</span>
								</div>
								<span class="order-customer-name"><em>{{ $t('ChatDetailComponent.nguoi_nhan') }}:</em>
									{{
										mess.content?.order_detail?.customer_name }}</span>
								<span class="order-address">{{ mess.content?.order_detail?.address }}</span>
								<span class="order-total">
									{{
										formatCurrency(mess.content?.order_detail?.grand_total || 0,
											mess.content?.order_detail?.shops?.currency)
									}}
								</span>
							</div>

							<!-- <span class="time"
								v-if="mess.content.link_order && mess.content?.order_detail && !mess.content.text">
								{{ showTimeMessage(mess.created_at) }}
							</span> -->
						</nuxt-link>
						<span class="time" v-if="!mess.content?.text">
							{{ showTimeMessage(mess.created_at) }}
						</span>
						<div class="message image" v-if="mess.content?.image?.length > 0" v-on:contextmenu="(e: Event) => {
							if (mess.member_id == senderId) {
								e.preventDefault();
								// mess.show_actions = true;
								blurInputMessage()
								selectedMessage = JSON.parse(JSON.stringify(mess));

								showSelectMessageModal = true;
							}
						}" v-longpress="(e: any) => {
							if (mess.member_id == senderId) {
								e.preventDefault();
								// mess.show_actions = true;
								blurInputMessage()
								selectedMessage = JSON.parse(JSON.stringify(mess));

								showSelectMessageModal = true;
							}
						}">
							<div class="images-container" :class="{
								'multi': mess.content?.image?.length >= 3,
							}">
								<div class="image-item" v-for="(itemImg, indexImg) in mess.content?.image" v-on:click="() => {
									listObjectViewer = mess.content?.image;
									indexActive = indexImg;
									showImageViewerModal = true;
								}">
									<img :src="domainImage + itemImg.path" alt="" loading="lazy">
								</div>
							</div>
							<span class="time"
								v-if="checkShowTimeMessage(mess, listMessage[indexMess + 1], listMessage[indexMess - 1])">
								{{ showTimeMessage(mess.created_at) }}
							</span>
						</div>
						<span class="message" :class="{
							'show-origin': mess.show_origin_mess || mess.show_actions
						}" v-if="mess.content?.text" v-on:contextmenu="(e: Event) => {
							e.preventDefault();
							// mess.show_actions = true;
							blurInputMessage()
							selectedMessage = JSON.parse(JSON.stringify(mess));

							showSelectMessageModal = true;
						}" v-longpress="(e: any) => {
							e.preventDefault();
							blurInputMessage()
							// mess.show_actions = true;
							selectedMessage = JSON.parse(JSON.stringify(mess));

							showSelectMessageModal = true;
						}">
							<span v-if="mess.member_id != senderId">
								{{ mess.show_origin_mess ? mess.content?.text : (mess.content?.translate?.[locale] ??
									mess.content?.text) }}
							</span>
							<span v-if="mess.member_id == senderId">
								{{ mess.content?.text }}
							</span>
							<span class="time"
								v-if="checkShowTimeMessage(mess, listMessage[indexMess + 1], listMessage[indexMess - 1])">
								{{ showTimeMessage(mess.created_at) }}
							</span>

						</span>
						<div class="translate-info me" v-if="mess.member_id == senderId
							&& mess.content?.text
							&& mess?.content?.translate
							&& Object.keys(mess?.content?.translate)?.at(1) != undefined
							&& mess.content?.original_language != Object.keys(mess?.content?.translate)?.at(1)">
							<em
								v-html="$t('ChatDetailComponent.da_duoc_dich_sang', { lang_name: ':lang' }).replaceAll(':lang', $t(`LocalizedLanguageName.${Object.keys(mess?.content?.translate)?.at(1)}`))">
							</em>
							<button class="show-origin-message-button" v-on:click="() => {
								// mess.show_translate_version
								selectedMessage = mess;
								showTranslateMessageModal = true;

							}">
								{{ $t('ChatDetailComponent.xem_ban_dich') }}
							</button>
						</div>
						<div class="translate-info" v-if="mess.member_id != senderId
							&& mess.content?.text
							&& mess?.content?.translate
							&& Object.keys(mess?.content?.translate)?.at(1) != undefined
							&& mess.content?.original_language != Object.keys(mess?.content?.translate)?.at(1)">
							<em
								v-html="$t('ChatDetailComponent.duoc_dich_tu', { lang_name: ':lang' }).replaceAll(':lang', $t(`LocalizedLanguageName.${mess.content?.original_language ?? Object.keys(mess?.content?.translate)?.at(0)}`))">
							</em>
							<button class="show-origin-message-button" v-on:click="() => {
								mess.show_origin_mess = !mess.show_origin_mess
							}">
								{{ mess.show_origin_mess ? $t('ChatDetailComponent.xem_ban_dich') :
									$t('ChatDetailComponent.xem_ban_goc') }}
							</button>
						</div>
						<em class="send-error" v-if="mess.send_err">{{ $t('ChatDetailComponent.gui_loi') }}</em>
					</div>
					<div class="message-content-group" v-else-if="mess.status == 3">
						<span class="message retract">
							<span>
								{{ $t('ChatDetailComponent.tin_nhan_da_duoc_thu_hoi') }}
							</span>
							<span class="time">
								{{ showTimeMessage(mess.created_at) }}
							</span>

						</span>
					</div>
				</div>
			</div>
		</div>
		<div class="chat-detail-content-container empty" v-else>
			<!-- <img :src="start_chat_icon" /> -->
			<span>{{ t('ChatDetailComponent.notice_1') }}</span>
			<nuxt-link :to="appRoute.PolicyComponent">{{ t('ChatDetailComponent.notice_2') }}</nuxt-link>
		</div>

		<div class="chat-container">
			<div class="chat-media-preview"
				v-if="currentImgs.length || currentLinkProduct?.length || currentLinkOrder?.length">
				<div class="image-message" v-if="currentImgs.length">

					<Swiper class="my-carousel stack-carousel" :modules="[SwiperFreeMode]" :slides-per-view="'auto'"
						:space-between="10" :loop="false" :effect="'creative'" :freeMode="true"
						key="images-message-preview-carousel">
						<SwiperSlide class="img-preview" v-for="(itemImg, index) of currentImgs"
							:key="'image_preview_' + index">
							<img :src="itemImg.path" />
							<button class="delete-image-button" :disabled="mediaUploading" v-on:click="() => {
								currentImgs.splice(index, 1);
							}">
								<Icon name="pajamas:close"></Icon>
							</button>
						</SwiperSlide>
					</Swiper>
				</div>
				<div class="link-product-order-message" v-if="currentLinkProduct?.length">
					<div class="link-product-order-header">
						<span>
							{{ $t('ChatDetailComponent.hoi_ve_san_pham_nay') }}
						</span>
						<button class="delete-link-product" v-on:click="() => {
							currentLinkProduct = null;
							currentProductDetail = null;
						}">
							<Icon name="solar:close-square-linear"></Icon>
						</button>
					</div>
					<div class="link-product-order-detail" v-if="currentProductDetail?.id">
						<img v-on:error="(event: any) => {
							event.target.src = icon_for_broken_image;
						}" :src="currentProductDetail?.profile_picture?.length ? (domainImage + currentProductDetail.profile_picture) : icon_for_product"
							:placeholder="icon_for_product"
							:alt="currentProductDetail ? showTranslateProductName(currentProductDetail) : $t('ChatDetailComponent.khong_co_avatar')" />
						<div class="product-info">
							<span class="product-name">{{ showTranslateProductName(currentProductDetail) }}</span>
							<div class="price-info">
								<span class='product-price'>
									<em class="off"
										v-if="(currentProductDetail.price_off != null && currentProductDetail.price_off < currentProductDetail.price)">
										{{
											(parseFloat(currentProductDetail.price) == 0 || currentProductDetail.price ==
												null)
												? $t('ChatDetailComponent.gia_lien_he')
												: formatCurrency(parseFloat(currentProductDetail.price),
													currentProductDetail.shop?.currency)
										}}</em>
									{{
										(currentProductDetail.price_off != null && currentProductDetail.price_off <
											currentProductDetail.price) ?
											formatCurrency(parseFloat(currentProductDetail.price_off),
												currentProductDetail.shop?.currency) :
											(parseFloat(currentProductDetail.price) == 0 || currentProductDetail.price == null)
												? $t('ChatDetailComponent.gia_lien_he') :
												formatCurrency(parseFloat(currentProductDetail.price),
													currentProductDetail.shop?.currency) }} </span>
							</div>

						</div>
						<button class="send-product-order-message" v-on:click="() => {
							sendProductMessage()
						}">
							{{ $t('ChatDetailComponent.gui') }}
						</button>
					</div>
				</div>
				<div class="link-product-order-message" v-if="currentLinkOrder?.length">
					<div class="link-product-order-header">
						<span>
							{{ $t('ChatDetailComponent.hoi_ve_don_hang_nay') }}
						</span>
						<button class="delete-link-product" v-on:click="() => {
							currentLinkOrder = null;
							currentOrderDetail = null;
						}">
							<Icon name="solar:close-square-linear"></Icon>
						</button>
					</div>
					<div class="link-product-order-detail" v-if="currentOrderDetail?.id">
						<div class="order-info">
							<div class="h-stack">
								<span class="order-code">[{{ currentOrderDetail?.short_code }}]</span>
								<span class="order-at">
									{{ moment(currentOrderDetail.created_at).format("HH:mm DD/MM/YYYY") }}
								</span>
							</div>
							<span class="order-customer-name"><em>{{ $t('ChatDetailComponent.nguoi_nhan') }}:</em> {{
								currentOrderDetail?.customer_name }}</span>
							<span class="order-address">{{ currentOrderDetail?.address }}</span>
							<span class="order-total">
								{{
									formatCurrency(currentOrderDetail.grand_total || 0, currentOrderDetail.shops?.currency)
								}}
							</span>
						</div>

						<button class="send-product-order-message" v-on:click="() => {
							sendOrderMessage()
						}">
							{{ $t('ChatDetailComponent.gui') }}
						</button>
					</div>
				</div>
			</div>

			<div class="chat-input">
				<span class="typing-label" v-if="opponentTyping">{{ chatInfo?.members?.[0].member?.name }} {{
					$t('ChatDetailComponent.dang_soan_tin') }}
					<Icon name="svg-spinners:3-dots-scale"></Icon>
				</span>

				<div class="chat-input-group chat-text" v-if="!showMediaSelect">
					<button class="select-emoji-button" v-if="false" v-on:click="() => {
						showEmojiSelect = !showEmojiSelect;
						showMediaSelect = false;
					}">
						<Icon name="solar:emoji-funny-circle-linear"></Icon>
					</button>
					<UTextarea class="input-chat" autoresize :maxrows="5" :rows="1"
						:placeholder="$t('ChatDetailComponent.nhap_tin_nhan')" v-model="currentMes" v-on:input="(e: any) => {
							// checkFirstLink(e.target.value)
							publishTyping()
						}" v-on:change="(e: any) => {
							// checkFirstLink(e.target.value)
						}" v-on:keypress="(event: any) => {
							if (event.key === 'Enter' && !event.shiftKey) {
								event.preventDefault();
								sendMessage()
								currentMes = '';
							}
						}" v-on:focus="(e: any) => {
							if (e) {
								showEmojiSelect = false;
								showMediaSelect = false;
							}

						}" id="input_message"></UTextarea>
					<button class="media-message-button" v-on:click="() => {
						showMediaSelect = !showMediaSelect;
						showEmojiSelect = false;
					}">
						<Icon name="solar:add-circle-linear" v-if="!showMediaSelect"></Icon>
						<Icon name="solar:close-circle-bold" v-else></Icon>
					</button>
					<button class="send-button" v-on:click="() => {
						sendMessage()

					}">
						<Icon name="solar:plain-bold"></Icon>
					</button>
				</div>
				<div class="chat-input-group chat-media"
					v-else-if="!(currentImgs.length || currentVideos.length || currentLinkProduct || currentProductDetail?.id || currentLinkOrder || currentOrderDetail?.id)">
					<div class="image-info-action">
						<span class="image-count">{{ $t('ChatDetailComponent.tin_nhan_da_phuong_tien') }}</span>
					</div>
					<button class="media-message-button" v-on:click="() => {
						showMediaSelect = false;
					}">
						<Icon name="solar:close-circle-bold"></Icon>
					</button>
				</div>
				<div class="chat-input-group chat-image" v-else-if="currentImgs.length">
					<span class="mr-auto" v-if="mediaUploading">{{ $t('ChatDetailComponent.dang_tai_anh_len')
					}}</span>
					<div class="image-info-action" v-if="!mediaUploading">
						<span class="image-count" v-if="currentImgs.length"
							v-html="$t('ChatDetailComponent.da_chon_x_anh', { count: ':count' }).replaceAll(':count', `<em>${currentImgs?.length}</em>`)"></span>
						<span class="image-count" v-else>{{ $t('ChatDetailComponent.chua_chon') }}</span>
						<button :disabled="mediaUploading" v-if="currentImgs.length" v-on:click="() => {
							currentImgs = []
						}">{{
							$t('ChatDetailComponent.xoa_het')
						}}</button>
					</div>
					<button class="media-message-button" v-on:click="() => {
						showMediaSelect = !showMediaSelect;
						if (showMediaSelect == false) {
							currentImgs = [];
						}
					}">
						<Icon name="solar:add-circle-linear" v-if="!showMediaSelect"></Icon>
						<Icon name="solar:close-circle-bold" v-else></Icon>
					</button>
					<button class="send-button" :disabled="mediaUploading || !currentImgs.length"
						v-if="currentImgs.length && !(currentLinkProduct || currentProductDetail?.id || currentLinkOrder || currentOrderDetail?.id)"
						v-on:click="() => {
							sendImagesMessage()
						}">
						<Icon name="solar:plain-bold"></Icon>
					</button>
				</div>
				<div class="chat-input-group chat-tag-product"
					v-else-if="currentLinkProduct && currentProductDetail?.id">
					<div class="image-info-action" v-if="!currentLinkProduct">
						<span class="image-count">{{ $t('ChatDetailComponent.chua_chon') }}</span>
					</div>
				</div>

			</div>
			<div class="emoji-select-container" v-show="showEmojiSelect">
				<client-only>
					<NuxtEmojiPicker class="chat-emoji-picker" :native="true" :hide-search="true" :hide-footer="true"
						:disabled-groups="['flags']" :display-recent="true" :disable-sticky-group-names="true"
						:disable-skin-tones="true" @select="(emoji: any) => {
							currentMes += emoji.i;
							// focusInputMessage();
						}" />
				</client-only>

			</div>
			<div class="media-select-container" v-show="showMediaSelect">
				<div class="media-options">
					<label class="media-option" :class="{
						'disabled': currentLinkProduct || currentProductDetail?.id || currentLinkOrder || currentOrderDetail?.id || currentVideos?.length
					}" v-on:click="(e) => {
						if (currentLinkProduct || currentProductDetail?.id || currentLinkOrder || currentOrderDetail?.id || currentVideos?.length) e.preventDefault()
					}">
						<div>
							<Icon name="solar:gallery-minimalistic-bold"></Icon>
						</div>
						<span>{{ $t('ChatDetailComponent.anh') }}</span>
						<input type="file" accept="image/*" :multiple="true" v-on:change="($event: any) => {
							fileImageChangeInput($event);
						}" ref="imageFilesName" />
					</label>
					<label class="media-option" v-if="false" :class="{
						'disabled': currentLinkProduct || currentProductDetail?.id || currentLinkOrder || currentOrderDetail?.id || currentImgs?.length
					}" v-on:click="(e) => {
						if (currentLinkProduct || currentProductDetail.id || currentLinkOrder || currentOrderDetail || currentImgs?.length) e.preventDefault()
					}">
						<div>
							<Icon name="solar:video-library-bold"></Icon>
						</div>
						<span>{{ $t('ChatDetailComponent.video') }}</span>
						<input type="file" accept="video/*" :multiple="true" v-on:change="($event: any) => {
							fileVideoChangeInput($event);
						}" ref="imageFilesName" />
					</label>
					<button class="media-option" v-on:click="() => {
						showSelectOrderModal = true;
					}" :disabled="currentImgs.length || currentVideos.length || currentLinkProduct || currentProductDetail?.id">
						<div>
							<Icon name="solar:bill-list-bold"></Icon>
						</div>
						<span>{{ $t('ChatDetailComponent.don_hang') }}</span>
					</button>
					<button class="media-option" v-on:click="() => {
						showSelectProductModal = true;
					}" :disabled="currentImgs.length || currentVideos.length || currentLinkOrder || currentOrderDetail?.id">
						<div>
							<Icon name="solar:cart-3-bold"></Icon>
						</div>
						<span>{{ $t('ChatDetailComponent.san_pham') }}</span>
					</button>
				</div>

			</div>
		</div>

		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>

		<VueFinalModal class="my-modal-container translate-message-container" content-class="v-stack"
			:click-to-close="true" v-model="showTranslateMessageModal" contentTransition="vfm-fade" v-on:closed="() => {
				showTranslateMessageModal = false
			}">
			<div class="edit-translate-message">
				<UTextarea class="input-chat edit-translate-message-input" autoresize :maxrows="5" :rows="5"
					:placeholder="$t('ChatDetailComponent.nhap_tin_nhan')"
					:model-value="(selectedMessage.content?.translate[Object.keys(selectedMessage.content?.translate).at(1) as any])"
					v-on:input="(e: any) => {
						selectedMessage.content.translate[Object.keys(selectedMessage.content?.translate).at(1) as any] = e.target.value;
					}" v-on:change="(e: any) => {
						// checkFirstLink(e.target.value)
					}" id="input_edit_translate_message"></UTextarea>
				<div class="edit-actions">
					<button class="save-edit" :disabled="isUpdating" v-on:click="() => updateSelectedMessage()">
						<Icon name="eos-icons:loading" v-if="isUpdating"></Icon>
						{{ $t('ChatDetailComponent.cap_nhat_ban_dich') }}
					</button>
					<button class="close-edit" :disabled="isUpdating" v-on:click="() => {
						showTranslateMessageModal = false;
						selectedMessage = null
					}">
						{{ $t('ChatDetailComponent.dong') }}
					</button>
				</div>
			</div>
		</VueFinalModal>


	</div>

	<div class="chat-detail-container" v-else>
		<div class="chat-header">
			<v-skeleton-loader type="list-item-avatar-two-line" class="chat-info"></v-skeleton-loader>
		</div>
	</div>
	<v-overlay v-model="showSelectProductModal" location="bottom" contained :absolute="true" :z-index="10000"
		key="select_product" class="select-product-overlay-container" persistent
		content-class='select-product-container' no-click-animation v-on:click:outside="() => {
			showSelectProductModal = false
		}">
		<div class="select-product-header">
			{{ $t('ChatDetailComponent.chon_san_pham') }}
			<button v-on:click="() => {
				showSelectProductModal = false
			}">
				<Icon name="solar:close-circle-outline"></Icon>
			</button>
		</div>
		<div class="select-product-body">
			<div class="search-product">
				<div class="search-input-group">
					<button class="search-button">
						<Icon name="solar:rounded-magnifer-outline"></Icon>
					</button>
					<input type="search" name='search-text' :placeholder="$t('ChatDetailComponent.tim_san_pham')"
						:maxlength="appConst.max_text_short" autoComplete='off' :value="search_text" v-on:input="($event: any) => {
							search_text = $event.target.value;
							searchingProduct = true;
							searchProduct()
						}" />
					<button class="clear-button" v-show="search_text?.length > 0" v-on:click="() => {
						search_text = '';
						searchingProduct = true;
						searchProduct()
					}">
						<Icon name="iconamoon:sign-times" size="25" />
					</button>

				</div>
			</div>

			<div class="list-product-container">
				<div class="category-products-container"
					v-for="(itemCategory, indexCategory) in dataShopProductCategories"
					v-if="dataShopProductCategories && dataShopProductCategories.length > 0"
					:key="`category_products_${itemCategory.id || 'all'}`" :id="`category_products_${itemCategory.id}`">
					<div class="category-title" :id="`category_products_title_${itemCategory.id}`">
						{{ showTranslateProductName(itemCategory) }} ({{ itemCategory.products.length }})
					</div>
					<div class="category-products-list">
						<div class="product-item-container-grid" v-on:click="() => {
							handleSelectProduct(itemProduct)
						}" :key="`category_${itemCategory.id || 'all'}_product_item_${itemProduct.id}`"
							v-for="(itemProduct, indexSelected) in itemCategory.products">
							<div class="product-item">
								<img loading="lazy" :src="itemProduct && itemProduct.profile_picture
									? domainImage + itemProduct.profile_picture
									: icon_for_product" :placeholder="icon_for_product" v-on:error="(e: any) => {
										e.target.src = icon_for_broken_image
									}" />
								<div class="product-item-content">
									<span class="name">{{ showTranslateProductName(itemProduct) }}</span>
									<div class="h-stack">
										<span class="price">
											<em class="off"
												v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
												{{
													(parseFloat(itemProduct.price) == 0 || itemProduct.price ==
														null)
														? $t('ChatDetailComponent.gia_lien_he')
														: formatCurrency(parseFloat(itemProduct.price),
															itemProduct.shop
																?
																itemProduct.shop.currency
																: itemProduct.currency)
												}}
											</em>
											{{
												(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)
													? formatCurrency(parseFloat(itemProduct.price_off), itemProduct.shop ?
														itemProduct.shop.currency : itemProduct.currency) :
													(parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
														$t('ChatDetailComponent.gia_lien_he') :
														formatCurrency(parseFloat(itemProduct.price), itemProduct.shop ?
															itemProduct.shop.currency : itemProduct.currency) }} </span>
									</div>
								</div>
							</div>

						</div>
					</div>


				</div>
				<div class='v-stack empty-list' v-else>
					<img loading="lazy" :src="list_empty" :placeholder="list_empty"
						:alt="$t('ChatDetailComponent.danh_sach_trong')" />
					<span>
						{{ $t('ChatDetailComponent.khong_tim_thay_san_pham_nao') }}
					</span>
				</div>
			</div>
		</div>
	</v-overlay>

	<v-overlay v-model="showSelectOrderModal" location="bottom" contained :absolute="true" :z-index="10000"
		key="select_order" class="select-order-overlay-container" persistent content-class='select-order-container'
		no-click-animation v-on:click:outside="() => {
			showSelectOrderModal = false
		}">
		<div class="select-order-header">
			{{ $t('ChatDetailComponent.chon_don_hang_da_dat') }}
			<button v-on:click="() => {
				showSelectOrderModal = false
			}">
				<Icon name="solar:close-circle-outline"></Icon>
			</button>
		</div>
		<div class="select-order-body" v-on:scroll="() => { listOrderScroll() }">

			<div class="search-order">
				<div class="search-input-group">
					<button class="search-button">
						<Icon name="solar:rounded-magnifer-outline"></Icon>
					</button>
					<input type="search" name='search-text' :placeholder="$t('ChatDetailComponent.tim_don_hang')"
						:maxlength="appConst.max_text_short" autoComplete='off' :value="search_order_text" v-on:input="($event: any) => {
							search_order_text = $event.target.value;
							searchingOrder = true;
							searchOrder(search_order_text)
						}" />
					<button class="clear-button" v-show="search_order_text?.length > 0" v-on:click="() => {
						search_order_text = '';
						searchingOrder = true;
						searchOrder(search_order_text)
					}">
						<Icon name="iconamoon:sign-times" size="25" />
					</button>

				</div>
			</div>
			<div class="list-order-container">
				<div v-for="(itemOrder) in dataOrderPlaced" :key="'placed_order_' + itemOrder.id"
					class='v-stack item-order' v-if="dataOrderPlaced && dataOrderPlaced.length > 0">
					<button v-on:click="() => {
						handleSelectOrder(itemOrder)
					}" class='order-button-container'>
						<div class='v-stack'>
							<div class='h-stack'>
								<span class='customer-name'>
									{{ itemOrder.customer_name }}
								</span>
								<span class="is-new"
									v-if="checkNewOrder(itemOrder) && itemOrder.status == appConst.order_status.waiting.value">
									{{ $t('ChatDetailComponent.moi') }}
								</span>
								<span :class="'order-status ' + (parseInt(itemOrder.status.toString()) != 3
									? Object.keys(appConst.order_status).find(key => appConst.order_status[key].value == parseInt(itemOrder.status)) as string
									: ' taken')">
									{{
										$t('ChatDetailComponent.' +
											appConst.order_status[
												parseInt(itemOrder.status.toString()) != 3
													? Object.keys(appConst.order_status).find(key =>
														appConst.order_status[key].value ==
														parseInt(itemOrder.status)) as string
													: "taken"
											].nameKey
										)
									}}
								</span>
							</div>
							<div class='h-stack order-detail-advanced'>
								<span class='ordered-at'>
									{{
										moment(itemOrder.created_at).format("HH:mm DD/MM/YYYY")
									}}
								</span>
								<span class='short-code'>
									[{{ itemOrder.short_code }}]
								</span>
							</div>
							<div class='h-stack'>
								<span>
									{{ itemOrder?.address }}
								</span>
								<span class='customer-phone'>
									{{ itemOrder?.customer_phone }}
								</span>
							</div>
							<div class='h-stack shop-name'>
								<Icon name="material-symbols:storefront-outline"></Icon>
								<span>
									{{ itemOrder?.shops?.name }}
								</span>
							</div>
							<div class='h-stack'>
								<span class='title'>{{ $t('ChatDetailComponent.tong_cong') }}</span>
								<span class='total'>{{ formatCurrency(itemOrder.grand_total || 0,
									itemOrder.shops?.currency)
								}}</span>
							</div>
						</div>
					</button>

				</div>
				<div class='v-stack empty-list' v-else>
					<img loading="lazy" :src="list_empty" :placeholder="list_empty"
						:alt="$t('ChatDetailComponent.danh_sach_trong')" />
					<span>
						{{ $t('ChatDetailComponent.khong_tim_thay_don_hang_nao') }}
					</span>
					<span class='refresh' v-on:click="() => {
						searchOrder(search_order_text);
					}
					">
						{{ $t('ChatDetailComponent.lam_moi') }}
					</span>
				</div>
			</div>

			<span class='load-more' v-if="loadMoreOrder">
				{{ $t('ChatDetailComponent.dang_tai') }}
			</span>
			<div id="last_of_list_all"></div>
		</div>
	</v-overlay>

	<v-overlay v-model="showSelectMessageModal" location="bottom" contained :absolute="true" :z-index="10000"
		key="select_message" class="selected-message-overlay-container" persistent
		content-class='selected-message-container' no-click-animation v-on:click:outside="() => {
			showSelectMessageModal = false;
			selectedMessage = null;
		}">
		<button class="close-selected-modal" v-on:click="() => {
			showSelectMessageModal = false;
			selectedMessage = null;
		}">
			<Icon name="clarity:times-line"></Icon>
		</button>
		<div class="message-container" v-if="selectedMessage?.id" :class="{
			'me': selectedMessage.member_id == senderId,
		}">
			<div class="message-group" v-if="selectedMessage.status != 2">
				<div class="sender-avatar" v-if="selectedMessage.member_id != senderId">
					<img v-if="chatInfo?.members?.[0]?.member_type == member_type.user" :src="chatInfo?.members?.[0]?.member?.profile_picture
						? (appConst.provider_img_domain.some(e => chatInfo?.members?.[0]?.member?.profile_picture?.includes(e))
							? chatInfo?.members?.[0]?.member?.profile_picture
							: (domainImage + chatInfo?.members?.[0]?.member?.profile_picture)
						)
						: non_avatar" />
					<div class="single" v-if="chatInfo?.members?.[0]?.member_type == member_type.shop">
						<AvatarComponent :imgTitle="chatInfo?.members?.[0]?.member?.name"
							:imgStyle="chatInfo?.members?.[0]?.member?.logo?.style" :imgSrc="chatInfo?.members?.[0]?.member?.logo?.path?.length
								? (domainImage + chatInfo?.members?.[0]?.member?.logo?.path)
								: ''" :width="30" :height="30" />
					</div>
				</div>
				<div class="message-content-group" v-if="selectedMessage.status != 3">
					<div class="message product"
						v-if="selectedMessage.content?.link_product && selectedMessage.content?.product_detail">
						<div class="product-detail">
							<img v-on:error="(event: any) => {
								event.target.src = icon_for_broken_image;
							}" :src="selectedMessage.content?.product_detail?.profile_picture?.length ? (domainImage + selectedMessage.content?.product_detail?.profile_picture) : icon_for_product"
								:placeholder="icon_for_product"
								:alt="selectedMessage.content?.product_detail ? showTranslateProductName(selectedMessage.content?.product_detail) : $t('ChatDetailComponent.khong_co_avatar')" />
							<div class="product-info">
								<span class="product-name">{{
									showTranslateProductName(selectedMessage.content?.product_detail)
								}}</span>
								<div class="price-info">
									<span class='product-price'>
										<em class="off"
											v-if="(selectedMessage.content?.product_detail?.price_off != null && parseFloat(selectedMessage.content?.product_detail?.price_off) < parseFloat(selectedMessage.content?.product_detail?.price))">
											{{
												(parseFloat(selectedMessage.content?.product_detail?.price) == 0 ||
													selectedMessage.content?.product_detail?.price ==
													null)
													? $t('ChatDetailComponent.gia_lien_he')
													: formatCurrency(parseFloat(selectedMessage.content?.product_detail?.price),
														selectedMessage.content?.product_detail?.shop?.currency)
											}}</em>{{ (selectedMessage.content?.product_detail?.price_off != null &&
												parseFloat(selectedMessage.content?.product_detail?.price_off)
												< parseFloat(selectedMessage.content?.product_detail?.price)) ?
												formatCurrency(parseFloat(selectedMessage.content?.product_detail?.price_off),
													selectedMessage.content?.product_detail?.shop?.currency) :
												(parseFloat(selectedMessage.content?.product_detail?.price) == 0 ||
													selectedMessage.content?.product_detail?.price == null) ?
													$t('ChatDetailComponent.gia_lien_he') :
													formatCurrency(parseFloat(selectedMessage.content?.product_detail?.price),
														selectedMessage.content?.product_detail?.shop?.currency) }} </span>
								</div>

							</div>
						</div>
					</div>
					<div class="message order"
						v-if="selectedMessage.content?.link_order && selectedMessage.content?.order_detail">
						<div class="order-info">
							<div class="h-stack">
								<span class="order-code">[{{ selectedMessage.content?.order_detail?.short_code
								}}]</span>
								<span class="order-at">
									{{
										moment(selectedMessage.content?.order_detail?.created_at).format('HH:mm DD/MM/YYYY')
									}}
								</span>
							</div>
							<span class="order-customer-name"><em>{{ $t('ChatDetailComponent.nguoi_nhan') }}:</em>
								{{
									selectedMessage.content?.order_detail?.customer_name }}</span>
							<span class="order-address">{{ selectedMessage.content?.order_detail?.address }}</span>
							<span class="order-total">
								{{
									formatCurrency(selectedMessage.content?.order_detail?.grand_total || 0,
										selectedMessage.content?.order_detail?.shops?.currency)
								}}
							</span>
						</div>
						<span class="time" v-if="!selectedMessage.content?.text">
							{{ showTimeMessage(selectedMessage.created_at) }}
						</span>
					</div>

					<div class="message image" v-if="selectedMessage.content?.image?.length > 0">
						<div class="images-container multi">
							<div class="image-item" v-for="(itemImg, indexImg) in selectedMessage.content?.image"
								v-on:click="() => {
									listObjectViewer = selectedMessage.content?.image;
									indexActive = indexImg;
									showImageViewerModal = true;
								}">
								<img :src="domainImage + itemImg.path" alt="" loading="lazy">
							</div>
						</div>
						<span class="time">
							{{ showTimeMessage(selectedMessage.created_at) }}
						</span>
					</div>
					<span class="message" :class="{
						'show-origin': selectedMessage.show_origin_mess
					}" v-if="selectedMessage.content?.text">
						<span v-if="selectedMessage.member_id != senderId">
							{{ selectedMessage.show_origin_selectedMessage ? selectedMessage.content?.text :
								(selectedMessage.content?.translate?.[locale] ??
									selectedMessage.content?.text) }}
						</span>
						<span v-if="selectedMessage.member_id == senderId">
							{{ selectedMessage.content?.text }}
						</span>
						<span class="time">
							{{ showTimeMessage(selectedMessage.created_at) }}
						</span>

					</span>
				</div>
			</div>
			<div class="advanced-actions">
				<button class="action"
					v-if="selectedMessage.content?.link_product || selectedMessage.content?.link_order" v-on:click="() => {
						console.log('click')
						if (selectedMessage.content?.link_product && !selectedMessage.content?.link_order) {
							copyToClipboard(`${domain}${appRoute.ProductComponent}/${selectedMessage.content?.link_product}`)
						}
						else if (!selectedMessage.content?.link_product && selectedMessage.content?.link_order) {
							copyToClipboard(
								`${domain}
								${props.mode == member_type.user
									? appRoute.MyOrderDetailComponent.replace(':order_id', selectedMessage.content?.order_detail.short_code ?? selectedMessage.content?.link_order)
									: props.mode == member_type.shop ? appRoute.MyShopOrderDetailComponent.replace(':id', selectedMessage.content?.order_detail.id ?? selectedMessage.content?.link_order)
										: appRoute.AgentOrderDetailComponent.replace(':id', selectedMessage.content?.order_detail.id ?? selectedMessage.content?.link_order)
								}`
							)
						}
					}">
					<Icon name="solar:copy-linear"></Icon>
					{{ $t('ChatDetailComponent.sao_chep_lien_ket') }}
				</button>
				<button class="action" v-if="selectedMessage.content?.text" v-on:click="() => {
					copyToClipboard(selectedMessage.content?.text)
				}">
					<Icon name="solar:copy-linear"></Icon>
					{{ $t('ChatDetailComponent.sao_chep') }}
				</button>
				<button class="action" v-if="selectedMessage.member_id == senderId" v-on:click="() => {
					retractMessage(selectedMessage, 3)
				}">
					<Icon name="solar:chat-square-arrow-linear"></Icon>
					{{ $t('ChatDetailComponent.thu_hoi') }}
				</button>
				<button class="action" v-if="selectedMessage.member_id == senderId" v-on:click="() => {
					retractMessage(selectedMessage, 2)
				}">
					<Icon name="solar:trash-bin-minimalistic-linear"></Icon>
					{{ $t('ChatDetailComponent.an_phia_toi') }}
				</button>
			</div>
		</div>

	</v-overlay>
</template>

<script lang="ts" setup>
import ISO6391 from "iso-639-1";
import non_avatar from "~/assets/image/non-avatar.jpg";
import start_chat_icon from "~/assets/imageV2/start-chat-icon.png";
import { appConst, appDataStartup, domain, domainImage, formatCurrency, nonAccentVietnamese, showTranslateProductDescription, showTranslateProductName } from "~/assets/AppConst";
import { InteractionObjectType, type CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import moment from "moment";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import { channel_type, member_type, type ChannelDTO, type MessageDTO, type NotificationDTO } from '../ChatDTO'
import { UserService } from "~/services/userService/userService";
import { content } from "#build/nuxtui-tailwind.config.cjs";
import { ShopService } from "~/services/shopService/shopService";
import { MqttService } from "~/services/mqttService/mqttService";
import icon_for_broken_image from '~/assets/image/image-broken.jpg';
import list_empty from "~/assets/image/list-empty-2.jpg";
import { InteractionService } from "~/services/interactionService/interactionService";
import { ImageService } from "~/services/imageService/imageService";
import { OrderService } from "~/services/orderService/orderService";
import { AgentService } from "~/services/agentService/agentService";

const props = defineProps({
	receiver_id: null,
	receiver_type: null, // user: 1, channel: 0, null, undefined, default
	chat_info: null,
	mode: null,
	firstMessage: null,
	product_link: null,
	product_detail: null,
	order_link: null,
	order_detail: null
})
const nuxtApp = useNuxtApp();
const { locale, t, setLocale, locales } = useI18n();
var router = useRouter();
var route = useRoute();
var emit = defineEmits(['accept', 'close', 'reject', 'readAll']);

var shop_id = ref(((route.query && route.query.shop_id) ? route.query.shop_id : "") as any);
var show = ref(true);
var chatService = new ChatService();
var userService = new UserService();
var shopService = new ShopService();
var agentService = new AgentService();
var orderService = new OrderService();
var mqttService = new MqttService();
var imageService = new ImageService();
var interactionService = new InteractionService();

var chatInfo = ref<ChannelDTO | null>(null);
var listMessage = ref<any>([])
var senderId = ref<any>(null);
var receiver_id = ref(props.receiver_id);
var receiver_type = ref(props.receiver_type);

var currentMes = ref<string>(props.firstMessage ? props.firstMessage : "");
var currentImgs = ref<any>([]);
var currentVideos = ref<any>([]);
var currentLink = ref<any>([]);
var currentLinkMeta = ref<any>(null)
var currentLinkProduct = ref<any>(props.product_link);
var currentProductDetail = ref<any>(props.product_detail);
var currentLinkOrder = ref<any>(props.order_link);
var currentOrderDetail = ref<any>(props.order_detail)

var imageFilesName = ref(null as any);
var checkLinkTimeout: any;
var isLoading = ref(true);
var viewAllMess = ref(false);
var isLoadingMore = ref(false);
var preChatScrollHeight = ref(0);

var chatScrollContainer = ref<HTMLElement | null>(null);
var headOfListMessage = ref<HTMLElement | null>(null);
var chatHeader = ref<HTMLElement | null>(null);

var setTypeEndTimeout: any;
var opponentTyping = ref(false);

var selectedLanguage = ref<any>(null);
var langOptions = ref([] as any);
const availableLocales = computed(() => {
	return locales.value.filter(i => i.code)
})

var showEmojiSelect = ref(false);
var showMediaSelect = ref(false);
var mediaUploading = ref(false);

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);

var showTranslateMessageModal = ref(false);
var showSelectProductModal = ref(false);
var showSelectOrderModal = ref(false);
var selectedMessage = ref<any>(null);
var showSelectMessageModal = ref(false);
var isUpdating = ref(false);

var dataShopProductCategories = ref<any>([]);
var dataShopProductCategoriesBackup = ref<any>([]);
var dataOrderPlaced = ref<any>([]);
var dataOrderPlacedCount = ref(0);

var searchProductTimeout: any;
var search_text = ref("");
var search_order_text = ref("");
var searchingProduct = ref(false);
var searchingOrder = ref(false);
var searchOrderTimeout: any;

var loadMoreOrder = ref(false);
var webInApp = ref();

var channelNotExist = ref(false);
var loadMoreTimeout: any;
onMounted(async () => {

	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	langOptions.value = availableLocales.value.map(el => {
		return el
	})

	isLoading.value = true;
	selectedLanguage.value = locale.value;

	chatInfo.value = JSON.parse(JSON.stringify(props.chat_info));

	if (props.mode == member_type.user) {
		await getUserInfo().then(async (e: any) => {
			senderId.value = e.id;
			// changeLanguage(e.language?.[0] ?? e.language)
			locale.value
			if (receiver_type.value == true) {
				await getChannel()
			}
			listenMessage();
			let indexShopMember = chatInfo.value?.members.findIndex(function (e) {
				return e.member_type == member_type.shop
			});
			if (indexShopMember != -1) {
				getShopProduct(chatInfo.value?.members.at(indexShopMember ?? 0)?.member_id);
				getListOrderPlaced(senderId.value, chatInfo.value?.members.at(indexShopMember ?? 0)?.member_id);
			}

			if (senderId.value) {
				getChatListMessage().then((result) => {
					listMessage.value = JSON.parse(JSON.stringify(result));
					isLoading.value = false;

					scrollToNewestMessage();
					setTimeout(() => {
						setReadAllMessage()
					}, 1000);
				});
			}
			else {
				nuxtApp.$emit(appConst.event_key.require_login, {
					redirect_url: route.fullPath,
					back_on_close: true
				})
				// router.push({
				// 	path: appRoute.LoginComponent,
				// 	query: {
				// 		redirect: JSON.stringify(route.fullPath)
				// 	}
				// });
			}
		});
	}
	else if (props.mode == member_type.shop) {
		await getMyShopInfo().then(async (e: any) => {
			senderId.value = e.id;
			getShopProduct(e.id);
			let indexUserMember = chatInfo.value?.members.findIndex(function (e) {
				return e.member_type == member_type.user
			});
			if (indexUserMember != -1) {
				getListOrderPlaced(chatInfo.value?.members.at(indexUserMember ?? 0)?.member_id, e.id)
			}

			if (receiver_type.value == true) {
				await getChannel()
			}
			listenMessage();
			if (senderId.value) {
				getChatListMessage().then((result) => {
					listMessage.value = JSON.parse(JSON.stringify(result));
					isLoading.value = false;

					scrollToNewestMessage();
					setTimeout(() => {
						setReadAllMessage()
					}, 1000);
				});
			}
			else {
				router.push({
					path: appRoute.LoginComponent,
					query: {
						redirect: JSON.stringify(route.fullPath)
					}
				});
			}
		});
	}
	else if (props.mode == member_type.agent) {
		await getShopDetail().then(async (e: any) => {
			senderId.value = e.id;
			getShopProduct(e.id);
			let indexUserMember = chatInfo.value?.members.findIndex(function (e) {
				return e.member_type == member_type.user
			});
			if (indexUserMember != -1) {
				getListOrderPlaced(chatInfo.value?.members.at(indexUserMember ?? 0)?.member_id, e.id)
			}

			if (receiver_type.value == true) {
				await getChannel()
			}
			listenMessage();
			if (senderId.value) {
				getChatListMessage().then((result) => {
					listMessage.value = JSON.parse(JSON.stringify(result));
					isLoading.value = false;

					scrollToNewestMessage();
					setTimeout(() => {
						setReadAllMessage()
					}, 1000);
				});
			}
			else {
				router.push({
					path: appRoute.LoginComponent,
					query: {
						redirect: JSON.stringify(route.fullPath)
					}
				});
			}
		});
	}
})

onUnmounted(() => {
	nuxtApp.$unsubscribe(`${appConst.event_key.new_chat_message}_${receiver_id.value}`);
	unsubscribeMessage();
	setTimeout(() => {
		setReadAllMessage()
	}, 300);

})
function changeLanguage(lang = 'vi') {
	locale.value = lang;
	// setLocale(lang)
	localStorage.setItem(appConst.storageKey.language, lang);
	selectedLanguage.value = lang;

	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.setLanguage,
		data: lang
	})
}
async function getUserInfo() {
	return new Promise((resolve) => {
		return userService.profileInfo().then(res => {
			if (res.status == HttpStatusCode.Ok) {
				resolve(res.data);
			}
			resolve([]);
		})
	})
}

async function getMyShopInfo() {
	return new Promise((resolve) => {
		return shopService.myShop().then(res => {
			if (res.status == HttpStatusCode.Ok && res?.body?.data) {
				resolve(res.body.data);
			}
			resolve([]);
		})
	})
}

function getShopDetail() {
	return new Promise((resolve) => {
		return agentService.agentShopDetail(shop_id.value).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				resolve(res.body.data);
			}
			resolve([]);
		})
	})
}

function getShopProduct(shop_id: any) {
	if (shop_id) {
		shopService.getProductsWithCategory(shop_id).then(res => {
			dataShopProductCategories.value = JSON.parse(JSON.stringify(res.body.data.categories));

			dataShopProductCategoriesBackup.value = JSON.parse(JSON.stringify(res.body.data.categories));
		}).catch(err => {
			dataShopProductCategories.value = [];
			dataShopProductCategoriesBackup.value = [];
		})
	}
}

function getListOrderPlaced(customer_id: any, shop_id: any, offset?: any, limit?: any, status?: any, search_text?: any) {
	if (customer_id && shop_id) {
		orderService.orderByCustomerIdAndShopId(
			customer_id,
			shop_id,
			offset ?? 0,
			limit ?? 50,
			status ?? null,
			search_text ?? search_order_text.value
		).then((res) => {

			dataOrderPlaced.value = JSON.parse(JSON.stringify(res.body.data.result));
			dataOrderPlacedCount.value = res.body.data.count;
		}).catch(() => {
			toast.error(t('ChatDetailComponent.loi_khi_lay_danh_sach_don_hang'))
			dataOrderPlaced.value = [];
			dataOrderPlacedCount.value = 0
		})
	}
}

function checkNewOrder(order: any) {
	var orderCreatedAt = moment(order.created_at).valueOf();
	var currentTime = moment().valueOf();
	let count = currentTime - orderCreatedAt;
	if (Math.round(count / (60 * 60 * 24)) < 60)
		return true;
	return false;
}

function getChannel() {
	return new Promise((resolve) => {
		chatService.getChannelByMemberIds(
			senderId.value,
			props.mode == member_type.user ? member_type.user : member_type.shop,
			receiver_id.value
		).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				receiver_id.value = res.body?.data?.id;
				receiver_type.value = false;
				let chatInfoTemp = JSON.parse(JSON.stringify(res.body?.data));
				chatInfoTemp.members = chatInfoTemp.members.filter((e: any) => e.member_id != senderId.value);

				chatInfo.value = chatInfoTemp;
			}
			else if (res.status == HttpStatusCode.NotFound) {
				channelNotExist.value = true;
			}
			resolve(res)
		})
	})
}

function getChatListMessage() {
	return new Promise((resolve) => {
		chatService.listMessage(receiver_id.value, locale.value, undefined, undefined, senderId.value, props.mode == member_type.user ? member_type.user : member_type.shop).then((res) => {
			if (res.status == HttpStatusCode.Ok) {
				if (res.body?.data?.count) {
					listMessage.value = [
						...listMessage.value,
						...res.body?.data?.result
					]
					if (!res.body?.data?.result?.length) {
						viewAllMess.value = true;
					}
				}
				else {
					viewAllMess.value = true;
				}
				resolve(res.body?.data?.result ?? [])
			}
			else {
				resolve([])
			}
		}).catch(() => {
			resolve([])
		})
	})

}

function loadMoreMessage() {
	return new Promise((resolve) => {
		chatService.listMessage(receiver_id.value, locale.value, listMessage.value.length, undefined, senderId.value, props.mode == member_type.user ? member_type.user : member_type.shop).then((res) => {
			if (res.status == HttpStatusCode.Ok) {
				if (res.body?.data?.count) {
					listMessage.value = [
						...listMessage.value,
						...res.body?.data?.result
					]
					if (!res.body?.data?.result?.length) {
						viewAllMess.value = true;
					}
				}
				else {
					viewAllMess.value = true;
				}
				resolve(res.body?.data?.result ?? [])
			}
			else {
				resolve([])
			}
		}).catch(() => {
			resolve([])
		})
	})
}

function showTimeMessage(time: any) {
	if (time) {
		let timeTemp = moment(time, 'YYYY-MM-DD HH:mm:ss');
		return timeTemp.format('HH:mm')
	}
	return null;
}

function checkTimeOnline(time: any) {
	let now = moment();
	let timeTemp = moment(time, 'YYYY-MM-DD HH:mm:ss');

	let diffInDay = now.diff(timeTemp, 'seconds');
	if (diffInDay < 60) return `${t('ChatDetailComponent.vua_xong')}`;
	if (diffInDay < 60 * 60) return `${Math.round(diffInDay / 60)} ${t('ChatDetailComponent.phut_truoc')}`;
	if (diffInDay < 60 * 60 * 24) return `${Math.round(diffInDay / (60 * 60))} ${t('ChatDetailComponent.gio_truoc')}`;

	let diffInWeek = now.diff(timeTemp, 'days');
	if (diffInWeek == 1) return `${t('ChatDetailComponent.hom_qua')}`;
	if (diffInWeek <= 7) return `${diffInWeek} ${t('ChatDetailComponent.ngay_truoc')}`;

	return null
}

async function sendMessage(mode?: any) {
	let currentText = currentMes.value?.trim() ?? null;

	currentMes.value = '';
	let receiver = {
		id: props.receiver_id,
		type: props.chat_info?.members?.[0].member_type
	}
	let contentMess = {
		text: currentText as any,
		translate: {
			[selectedLanguage.value ?? locale.value]: currentText
		} as any,
	}
	if (currentText.length) {
		var newMess: MessageDTO = {
			id: 'new',
			channel_id: !receiver_type.value ? receiver_id.value : null,
			member_id: senderId.value,
			content: contentMess,
			member_type: props.mode == member_type.user ? member_type.user : member_type.shop,
			send_err: false
		};

		listMessage.value.splice(0, 0, newMess);
		chatService.sendMessage(
			senderId.value,
			receiver_type.value ? receiver : null,
			!receiver_type.value ? receiver_id.value : null,
			props.mode == member_type.user ? member_type.user : member_type.shop,
			contentMess,
			locale.value
		).then((res) => {
			currentMes.value = "";
			if (res.status == HttpStatusCode.Ok || res.status == HttpStatusCode.Created) {
				receiver_id.value = res.body.channel_id;
				receiver_type.value = null;
				nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`);
				if (channelNotExist.value == true) {
					listenMessage()
					channelNotExist.value = false;
				}
			}
			else {
				newMess.send_err = true
			}
			publishTypeEnd();
		}).catch(() => {
			newMess.send_err = true;
			publishTypeEnd();
		})

		scrollToNewestMessage()

		if (showEmojiSelect.value || showMediaSelect.value) {
			showEmojiSelect.value = false;
			showMediaSelect.value = false;
		}
		else {
			focusInputMessage()
		}
	}

}

function sendProductMessage() {
	let receiver = {
		id: props.receiver_id,
		type: props.chat_info?.members?.[0].member_type
	}
	let contentMess = {
		text: null,
		link_product: currentLinkProduct.value as any,
		product_detail: {
			profile_picture: currentProductDetail.value?.profile_picture ?? null,
			name: currentProductDetail.value?.name,
			translation: currentProductDetail.value?.translation,
			price: currentProductDetail.value?.price,
			price_off: currentProductDetail.value?.price_off,
			shop: {
				id: currentProductDetail.value?.shop?.id,
				name: currentProductDetail.value?.shop?.name,
				address: currentProductDetail.value?.shop?.address,
				slug: currentProductDetail.value?.shop?.slug,
				currency: currentProductDetail.value?.shop?.currency
			}
		} as any
	}

	var newMess: MessageDTO = {
		id: 'new',
		channel_id: !receiver_type.value ? receiver_id.value : null,
		member_id: senderId.value,
		content: contentMess,
		member_type: props.mode == member_type.user ? member_type.user : member_type.shop,
		send_err: false
	};

	if (currentLinkProduct.value && currentProductDetail.value?.id) {
		listMessage.value.splice(0, 0, newMess);

		chatService.sendMessage(
			senderId.value,
			receiver_type.value ? receiver : null,
			!receiver_type.value ? receiver_id.value : null,
			props.mode == member_type.user ? member_type.user : member_type.shop,
			contentMess,
			locale.value
		).then((res) => {
			currentLinkProduct.value = null;
			currentProductDetail.value = null;

			if (res.status == HttpStatusCode.Ok || res.status == HttpStatusCode.Created) {
				receiver_id.value = res.body.channel_id;
				receiver_type.value = null;
				nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`)
			}
			else {
				newMess.send_err = true
			}
			publishTypeEnd();
		}).catch(() => {
			newMess.send_err = true;
			publishTypeEnd();
		})
		scrollToNewestMessage()

		if (showEmojiSelect.value || showMediaSelect.value) {
			showEmojiSelect.value = false;
			showMediaSelect.value = false;
		}
		else {
			focusInputMessage()
		}
	}
}

function sendOrderMessage() {
	{
		let receiver = {
			id: props.receiver_id,
			type: props.chat_info?.members?.[0].member_type
		}
		let contentMess = {
			text: null,
			link_order: currentLinkOrder.value as any,
			order_detail: {
				id: currentOrderDetail.value?.id,
				short_code: currentOrderDetail.value?.short_code,
				customer_name: currentOrderDetail.value?.customer_name,
				customer_id: currentOrderDetail.value?.customer_id,
				address: currentOrderDetail.value?.address,
				grand_total: currentOrderDetail.value?.grand_total,
				created_at: currentOrderDetail.value?.created_at,
				shop: {
					id: currentOrderDetail.value?.shop?.id,
					name: currentOrderDetail.value?.shop?.name,
					address: currentOrderDetail.value?.shop?.address,
					slug: currentOrderDetail.value?.shop?.slug,
					currency: currentOrderDetail.value?.shop?.currency
				}
			} as any
		}

		var newMess: MessageDTO = {
			id: 'new',
			channel_id: !receiver_type.value ? receiver_id.value : null,
			member_id: senderId.value,
			content: contentMess,
			member_type: props.mode == member_type.user ? member_type.user : member_type.shop,
			send_err: false
		};

		if (currentLinkOrder.value && currentOrderDetail.value?.id) {
			listMessage.value.splice(0, 0, newMess);

			chatService.sendMessage(
				senderId.value,
				receiver_type.value ? receiver : null,
				!receiver_type.value ? receiver_id.value : null,
				props.mode == member_type.user ? member_type.user : member_type.shop,
				contentMess,
				locale.value
			).then((res) => {
				currentLinkOrder.value = null;
				currentOrderDetail.value = null;

				if (res.status == HttpStatusCode.Ok || res.status == HttpStatusCode.Created) {
					receiver_id.value = res.body.channel_id;
					receiver_type.value = null;
					nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`)
				}
				else {
					newMess.send_err = true
				}
				publishTypeEnd();
			}).catch(() => {
				newMess.send_err = true;
				publishTypeEnd();
			})
			scrollToNewestMessage()

			if (showEmojiSelect.value || showMediaSelect.value) {
				showEmojiSelect.value = false;
				showMediaSelect.value = false;
			}
			else {
				focusInputMessage()
			}
		}
	}
}

async function sendImagesMessage() {
	let imgMessData: any = [];

	if (currentImgs.value.length) {
		mediaUploading.value = true;
		await Promise.all(
			currentImgs.value.map(async (img: any, index: any) => {
				await handleSaveImage(img, index).then((e: any) => {

					if (e.status == HttpStatusCode.Ok) {
						imgMessData.push(e?.body?.data?.images)
					}
				})
			})
		)
		mediaUploading.value = false;
		nextTick()
	}

	if (imgMessData.length) {
		currentMes.value = '';
		let receiver = {
			id: props.receiver_id,
			type: props.chat_info?.members?.[0].member_type
		}
		let contentMess = {
			text: null,
			image: imgMessData,
		}

		var newMess: MessageDTO = {
			id: 'new',
			channel_id: !receiver_type.value ? receiver_id.value : null,
			member_id: senderId.value,
			content: contentMess,
			member_type: props.mode == member_type.user ? member_type.user : member_type.shop,
			send_err: false
		};
		listMessage.value.splice(0, 0, newMess);

		chatService.sendMessage(
			senderId.value,
			receiver_type.value ? receiver : null,
			!receiver_type.value ? receiver_id.value : null,
			props.mode == member_type.user ? member_type.user : member_type.shop,
			contentMess,
			locale.value
		).then((res) => {
			currentImgs.value = [];

			if (res.status == HttpStatusCode.Ok || res.status == HttpStatusCode.Created) {
				receiver_id.value = res.body.channel_id;
				receiver_type.value = null;
				nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`)
			}
			else {
				newMess.send_err = true
			}
			publishTypeEnd();
		}).catch(() => {
			newMess.send_err = true;
			publishTypeEnd();
		})

		scrollToNewestMessage()

		if (showEmojiSelect.value || showMediaSelect.value) {
			showEmojiSelect.value = false;
			showMediaSelect.value = false;
		}
		else {
			focusInputMessage()
		}
	}

}

async function handleSaveImage(imgMess: any, index: any) {

	return new Promise(async (resolve) => {
		let img = {
			index: index,
			title: imgMess.title ?? `anh_${index}`,
			parent_id: receiver_id.value,
			object_type: appConst.object_type.channel,
			path: imgMess.path,
			image_type: imgMess.image_type ? imgMess.image_type : null,
			description: imgMess.desciption,
			orientation: imgMess.orientation,
			isEdit: imgMess.isEdit,
		}
		let imgRes = await imageService.insertImage(img);
		resolve(imgRes)
	})
}

function scrollToNewestMessage() {
	setTimeout(() => {
		chatScrollContainer.value?.scrollTo({
			top: chatScrollContainer.value?.scrollHeight
		})
	}, 100);
}

async function fileImageChangeInput(fileInput: any, childProduct?: any) {
	if (fileInput.target.files.length) {
		imageFilesName.value = fileInput.target.files;
		for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
			if (fileInput.target.files[i].size > appConst.image_size.max) {
				let imgErr = t('ChatDetailComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
				toast.error(imgErr);
			}
			else {
				const reader = new FileReader();
				reader.onload = async (e: any) => {
					const image = new Image();
					image.src = e.target.result;
					let orientationExif;
					if (fileInput.target.files[i].type != 'image/webp') {
						orientationExif = await exifr.orientation(image) || 0;
					}
					else orientationExif = 0;
					// orientation.value = orientationExif ? orientationExif : 0;
					let newImg = {
						isEdit: true,
						object_type: appConst.object_type.message,
						orientation: orientationExif,
						description: null,
						enable: true,
						parent_id: null,
						path: image.src,
						title: fileInput.target.files[i].name,
					}
					currentImgs.value.push(newImg);
				}
				await reader.readAsDataURL(fileInput.target.files[i]);
			}
		}
		imageFilesName.value = "";
	}
}
async function fileVideoChangeInput(fileInput: any, childProduct?: any) {
	if (fileInput.target.files.length) {

	}
}

function checkFirstLink(text: string) {
	clearTimeout(checkLinkTimeout)
	checkLinkTimeout = setTimeout(async () => {
		let url = "";

		const urlPattern = /\b(www\.[^\s]+|[^\s]+\.[^\s]{2,})/g; // This pattern looks for URLs starting with http or https
		const match = text.match(urlPattern);

		if (match) {
			currentLink.value = match
			let indexHTTP = match[0].indexOf('https://');
			if (indexHTTP == -1) {
				url = "https://".concat(text);
			}
			else url = text;
			fetch(url).then(async (fetchMeta) => {
				const html = await fetchMeta.text();
				const parser = new DOMParser();
				const doc = parser.parseFromString(html, 'text/html');
				const title = doc.querySelector('title')?.textContent ?? doc.querySelector('meta[property="og:title"]')?.getAttribute('content');
				const description = doc.querySelector('meta[name="description"]')?.getAttribute('content') ?? doc.querySelector('meta[property="og:description"]')?.getAttribute('content');
				const ogImage = doc.querySelector('meta[property="og:image"]')?.getAttribute('content');

				currentLinkMeta.value = {
					title: title,
					description: description,
					image: ogImage
				}
			}).catch(() => {
				currentLinkMeta.value = null;
			});

		}
		else {
			currentLinkMeta.value = null;
		}
	}, 500);

}
async function listScroll(event: any) {

	const currentScrollHeight = chatScrollContainer.value?.scrollHeight ?? 0;
	if ((headOfListMessage?.value?.getBoundingClientRect()?.bottom ?? 0) >= (chatHeader.value?.getBoundingClientRect()?.bottom ?? 0) - 300 && !viewAllMess.value) {
		clearTimeout(loadMoreTimeout)
		isLoadingMore.value = true;
		loadMoreTimeout = setTimeout(async () => {
			await loadMoreMessage();
			isLoadingMore.value = false;

			requestAnimationFrame(() => {
				if (chatScrollContainer.value) {
					const newScrollHeight = chatScrollContainer.value.scrollHeight;
					chatScrollContainer.value.scrollTop += newScrollHeight - currentScrollHeight;
				}
			});
		}, 200);



	}
	// document.getElementById('input_message')?.blur();
}

function checkShowDateSend(timeCur: any, timePre: any) {
	if (!timePre) {
		return true
	}
	let curTime = moment(timeCur);
	let preTime = moment(timePre);

	let diffDay = curTime.get('d') - preTime.get('d');

	return diffDay >= 1 ? true : false;
}

function checkShowTimeMessage(curMess: any, preMess: any, nextMess: any) {
	// console.log(curMess, preMess, nextMess)
	if (curMess.content?.link_product && curMess.content?.product_detail && curMess.status != 2) return true;
	if (curMess.content?.image?.length) return true;
	var diffMinuteToShow = 5;  // phút
	if (!nextMess) return true;

	if (curMess?.member_id != nextMess?.member_id) return true;
	let curTime = moment(curMess?.created_at);
	let nextTime = moment(nextMess?.created_at);

	let diffMinutes = nextTime.diff(curTime, 'minutes');
	if (diffMinutes >= diffMinuteToShow) return true;


	return false
}

function close() {
	emit('close')
}

function publishTyping() {
	let mess: NotificationDTO = {
		mess: {
			type: 'typing',
			url: receiver_id.value,
		},
		topic: appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value),
		user_id: senderId.value
	}
	mqttService.publish(appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value), JSON.stringify(mess))
	clearTimeout(setTypeEndTimeout)

	setTypeEndTimeout = setTimeout(() => {
		publishTypeEnd()
	}, 3000);
}

function publishTypeEnd() {
	let mess: NotificationDTO = {
		mess: {
			type: 'type_end',
			url: receiver_id.value
		},
		topic: appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value),
		user_id: senderId.value
	}
	mqttService.publish(appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value), JSON.stringify(mess))
}

function listenMessage() {
	console.log("lắng nghe message")
	mqttService.subscribe(appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value), (message: any) => {
		switch (message.mess.type) {
			case 'typing':
				if (message?.user_id == chatInfo.value?.members?.[0]?.member_id) {
					opponentTyping.value = true;
				}
				break;
			case 'type_end':
				if (message?.user_id == chatInfo.value?.members?.[0]?.member_id) {
					opponentTyping.value = false;
				}
				break
			default:
				console.log("detail có tin nhắn mới");
				getChatListMessage().then((result) => {
					listMessage.value = JSON.parse(JSON.stringify(result));
					isLoading.value = false;
					scrollToNewestMessage()

				});
		}
	});
}
function unsubscribeMessage() {
	mqttService.unsubscribe(appConst.mqtt_topic.chat.replaceAll(':channel_id', receiver_id.value))
}

function setReadAllMessage() {
	console.log("set read all")
	return new Promise((resolve) => {
		interactionService.createOrDelete({
			interaction_type: InteractionObjectType.read_message,
			object_id_from: receiver_id.value,
			object_type_from: appConst.object_type.channel as any,
			object_id_to: senderId.value,
			object_type_to: (props.mode == member_type.user ? appConst.object_type.user : appConst.object_type.shop) as any,
		}).then(() => {
			// emit('readAll')
			nuxtApp.$emit(appConst.event_key.check_unread_message)
			resolve(true);
		});
	})
}

function updateSelectedMessage() {
	isUpdating.value = true;
	chatService.updateMessage(selectedMessage.value.id, senderId.value, selectedMessage.value.content, selectedLanguage.value ?? locale.value).then((res) => {
		if (res.status == HttpStatusCode.Ok || res.status == HttpStatusCode.Created) {
			nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`);
			selectedMessage.value = null;
			showTranslateMessageModal.value = false;
		}
		else {
			showTranslateMessageModal.value = false;
		}
		isUpdating.value = false;
	})
}

function searchProduct() {
	clearTimeout(searchProductTimeout);

	searchProductTimeout = setTimeout(async () => {
		// getShopProduct(0, 20);
		await searchProductInCurrentList()
	}, 1000)
}

function searchProductInCurrentList() {
	dataShopProductCategories.value = [];
	var arrTemp = JSON.parse(JSON.stringify(dataShopProductCategoriesBackup.value));
	var res: any = [];
	if (search_text.value?.length) {
		arrTemp.forEach((element: any) => {
			if (isArray(element.products)) {
				element.products = element.products?.filter((el: any) => {
					return nonAccentVietnamese(showTranslateProductName(el) ?? '').includes(nonAccentVietnamese(search_text.value))
						|| nonAccentVietnamese(showTranslateProductDescription(el) ?? '').includes(nonAccentVietnamese(search_text.value))
				});
			}
			else {
				element.products = [];
			}
			res.push(element)
		});
	}
	else {
		res = arrTemp;
	}
	dataShopProductCategories.value = JSON.parse(JSON.stringify(res))
	return
}
function handleSelectProduct(product: any) {
	currentLinkProduct.value = product.slug ?? product.id;
	currentProductDetail.value = product;
	search_text.value = "";
	showSelectProductModal.value = false
}

function searchOrder(searchText$: string) {
	clearTimeout(searchOrderTimeout);
	searchOrderTimeout = setTimeout(() => {
		let customerId;
		let shopId;
		if (props.mode == member_type.user) {
			let indexShopMember = chatInfo.value?.members.findIndex(function (e) {
				return e.member_type == member_type.shop
			});
			if (indexShopMember != -1) {
				customerId = senderId.value;
				shopId = chatInfo.value?.members.at(indexShopMember ?? 0)?.member_id;
			}
			else {
				return
			}
		}
		else {
			let indexUserMember = chatInfo.value?.members.findIndex(function (e) {
				return e.member_type == member_type.user
			});
			if (indexUserMember != -1) {
				shopId = senderId.value;
				customerId = chatInfo.value?.members.at(indexUserMember ?? 0)?.member_id;
			}
			else {
				return
			}
		}
		getListOrderPlaced(customerId, shopId, 0, 50, null, searchText$);
	}, 1000);
}
function getMoreOrderInAList(offset = 0, limit = 50) {
	if (offset < dataOrderPlacedCount.value) {

		clearTimeout(searchOrderTimeout);
		searchOrderTimeout = setTimeout(() => {
			let customerId;
			let shopId;
			loadMoreOrder.value = true;
			if (props.mode == member_type.user) {
				let indexShopMember = chatInfo.value?.members.findIndex(function (e) {
					return e.member_type == member_type.shop
				});
				if (indexShopMember != -1) {
					customerId = senderId.value;
					shopId = chatInfo.value?.members.at(indexShopMember ?? 0)?.member_id;
				}
				else {
					return
				}
			}
			else {
				let indexUserMember = chatInfo.value?.members.findIndex(function (e) {
					return e.member_type == member_type.user
				});
				if (indexUserMember != -1) {
					shopId = senderId.value;
					customerId = chatInfo.value?.members.at(indexUserMember ?? 0)?.member_id;
				}
				else {
					return
				}
			}
			if (customerId && shopId) {
				orderService.orderByCustomerIdAndShopId(
					customerId,
					shopId,
					offset ?? 0,
					limit ?? 50,
					null,
					search_order_text.value
				).then((res) => {
					dataOrderPlaced.value = [
						...dataOrderPlaced.value,
						...JSON.parse(JSON.stringify(res.body.data.result))
					];
					loadMoreOrder.value = false;
				}).catch(() => {
					loadMoreOrder.value = false;
					toast.error(t('ChatDetailComponent.loi_khi_lay_danh_sach_don_hang'))
				})
			}
			else {
				loadMoreOrder.value = false;
			}
			return
		}, 1000);
	}

}
function listOrderScroll() {
	let el = document.getElementById('last_of_list_all')?.getBoundingClientRect().bottom;
	if (el && (el <= window.innerHeight + 10)) {
		getMoreOrderInAList(dataOrderPlaced.value.length, 50)
	}
}

function handleSelectOrder(orderSelected: any) {
	currentLinkOrder.value = orderSelected.short_code ?? orderSelected.id;
	currentOrderDetail.value = orderSelected;
	search_order_text.value = "";
	showSelectOrderModal.value = false
}

function copyToClipboard(text: any) {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(text);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: text
		})
	}
	toast.info(t('ChatDetailComponent.da_sao_chep_lien_ket'), {
		hideProgressBar: true,
	})
}

function retractMessage(message: any, type = 2) {
	chatService.retractMessage(message.id, senderId.value, type).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			message.status = type;
			selectedMessage.value = null;
			showSelectMessageModal.value = false;
			nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${receiver_id.value}`)
		}
		else if (res.status == HttpStatusCode.Forbidden) {
			if (type == 3) {
				toast.error(t('ChatDetailComponent.khong_the_thu_hoi_sau_24h'))
			}
			else toast.error(t('ChatDetailComponent.khong_the_an_sau_24h'))
		}
		else {
			if (type == 3) {
				toast.error(t('ChatDetailComponent.thu_hoi_that_bai'))
			}
			else toast.error(t('ChatDetailComponent.an_that_bai'))
		}
	}).catch(() => {
		if (type == 3) {
			toast.error(t('ChatDetailComponent.thu_hoi_that_bai'))
		}
		else toast.error(t('ChatDetailComponent.an_that_bai'))
	})
}
function focusInputMessage() {
	document.getElementById('input_message')?.focus();
}
function blurInputMessage() {
	document.getElementById('input_message')?.blur();
}
</script>

<style lang="scss" src="./ChatDetailStyles.scss"></style>