<template>
	<div class="public-container">
		<div class="products-category-container">
			<!-- <div class='title-header'>
			<div class="header-left">
			</div>
			<h3>{{ categoryData ? categoryData.name : appRouteTitle.ProductsCategoryComponent }}</h3>
			<div class="header-right">
				<button class="back-button" v-on:click="() => {
				close()
			}">
					<Icon name="iconamoon:sign-times-circle-light" />
				</button>
			</div>
		</div> -->
			<SubHeaderV2Component
				:title="categoryData ? showTranslateProductName(categoryData) : $t('AppRouteTitle.ProductsCategoryComponent')">
				<template v-slot:header_left>
					<button class="back-button" v-on:click="() => {
						close()
					}">
						<Icon name="solar:round-alt-arrow-left-linear"></Icon>
					</button>
				</template>
				<template v-slot:header_right>
					<button class="edit-button" v-on:click="() => {
						showEditCategoryModal = true;
					}" :disabled="showEditCategoryModal">
						<Icon name="ri:edit-line" />
					</button>
					<button class="delete-button" v-on:click="() => {
						showDeleteCategoryModal = true;
					}" :disabled="showDeleteCategoryModal">
						<Icon name="material-symbols:delete-outline-rounded" />
					</button>
				</template>
			</SubHeaderV2Component>
			<!-- <div class="products-category-content" v-if="isEditing && categoryData?.id">
			<div class="h-stack search-bar-container">
				<button class="search-button" :disabled="searchProductLoading" v-on:click="getSearchProductResult()">
					<Icon name="ion:search" size="20" v-show="!searchProductLoading" />
					<Icon name="eos-icons:loading" size="20" v-show="searchProductLoading" />
				</button>

				<input type="search" autocomplete="off" class="search-input-container" :value="search" v-on:click="() => {
					openFormAddProduct();
				}" id="search_input" placeholder="Thêm sản phẩm của bạn..." v-on:input="async ($event: any) => {
					search = $event.target.value;
					// searchOrders();

					getSearchProductResult();
				}
					" />
			</div>

			<div class="list-products-editing">
				<div class="list-selected">
					<div class="selected-empty" v-if="!listProductSelected || !listProductSelected.length">
						<img loading="lazy" :src="list_empty" :placeholder="list_empty" alt="" />
						<span>Chưa chọn sản phẩm</span>
					</div>
					<div v-else class="selected-list">
						<div v-for="(itemProduct, index) of listProductSelected" class="item-product"
							:class="{ 'odd': index % 2 }">
							<img loading="lazy" :src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length)
								? (domainImage + itemProduct.profile_picture)
								: icon_for_product" :placeholder="icon_for_product" alt="" />
							<div class="item-product-content">
								<span class="name">
									{{ itemProduct.name }}
								</span>
								<span class="price">
									{{ formatCurrency(itemProduct.price, shopData.currency) }}
								</span>
							</div>
							<button class="unselected-button" v-on:click="() => {
								let arrSuggest = listProductSuggest;
								let indexSuggest = arrSuggest.findIndex(function (e: any) {
									return e.id == itemProduct.id
								})
								if (indexSuggest != -1) {
									arrSuggest[indexSuggest].selected = false;
								}


								let arrSelect = listProductSelected;
								let indexSelect = arrSelect.findIndex(function (e: any) {
									return e.id == itemProduct.id
								})
								arrSelect.splice(indexSelect, 1);

								listProductSelected = arrSelect;
								listProductSuggest = arrSuggest;
							}">
								<img loading="lazy" :src="remove_icon" :placeholder="remove_icon" />
							</button>
						</div>
					</div>
					<div class="footer">
						<button class="cancel" v-on:click="() => {
							isEditing = false;
							listProductSuggest = [];
							listProductSelected = JSON.parse(JSON.stringify(listProduct));
						}">
							Thoát
						</button>
						<button class="save" :disabled="!listProductSelected || !listProductSelected.length || isSaving"
							v-on:click="() => {
								saveProducts()
							}">
							Lưu
						</button>
					</div>
				</div>
				<div class="list-suggest" v-show="searchFocus">
					<div class="suggest-empty" v-if="!(listProductSuggest && listProductSuggest.length)">
						<img loading="lazy" :src="none_result" :placeholder="none_result" alt="" />
						<span>Không tìm thấy sản phẩm</span>
					</div>
					<div v-if="(listProductSuggest && listProductSuggest.length)" class="suggest-list" id="suggest_list"
						v-on:scroll="(e) => {
							listSuggestScroll(e);
						}">
						<div v-for="(itemProduct, index) of listProductSuggest" class="item-product"
							:class="{ 'odd': index % 2 }">
							<img loading="lazy"
								:src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ? (domainImage + itemProduct.profile_picture) : icon_for_product"
								:placeholder="icon_for_product" alt="" />
							<div class="item-product-content">
								<span class="name">
									{{ itemProduct.name }}
								</span>
								<span class="price">
									{{ formatCurrency(itemProduct.price, shopData.currency) }}
								</span>
							</div>
							<button class="select-button" v-on:click="() => {
								itemProduct.selected = !itemProduct.selected;
								let arrSuggest = listProductSuggest;
								arrSuggest[index].selected = itemProduct.selected;

								let arrSelected = listProductSelected;
								let indexSelected = arrSelected.findIndex(function (e: any) {
									return e.id == itemProduct.id;
								})
								if (indexSelected != -1 && !itemProduct.selected) {
									arrSelected.splice(indexSelected, 1)
								}
								else if (indexSelected == -1 && itemProduct.selected) {
									itemProduct.system_price = itemProduct.price;
									arrSelected.push(itemProduct)
								}

								listProductSuggest = arrSuggest;
								listProductSelected = arrSelected
							}">
								<Icon name="mdi:checkbox-marked-circle-outline" v-if="itemProduct.selected"></Icon>
								<Icon name="mdi:checkbox-blank-circle-outline" v-if="!itemProduct.selected"></Icon>
							</button>
						</div>
						<div id="last_of_list_suggest"></div>
						<span class="showed-all"
							v-if="listProductSuggest && listProductSuggest.length && (listProductSuggest.length >= listProductSuggestCount || searchSuggestAll)">
							Đã hiển thị toàn bộ kết quả
						</span>
						<div class="loading-more" v-if="searchProductLoading">Loading...</div>
					</div>
					<div class="footer">
						<span>Đã chọn: {{ listProductSuggest.filter((e: any) => { return e.selected == true }).length
							}}</span>
						<button class="cancel"
							v-if="listProductSuggest.filter((e: any) => { return e.selected == true }).length"
							v-on:click="() => {
								let arr = listProductSuggest;
								let arrSelect = listProductSelected;
								arr.forEach((item: any) => {
									if (item.selected) {
										item.selected = false;
										let indexSelect = arrSelect.findIndex(function (e: any) {
											return e.id == item.id
										});
										if (indexSelect != -1) {
											arrSelect.splice(indexSelect, 1)
										}
									}

								});
								listProductSuggest = arr;
								listProductSelected = arrSelect;
							}">
							Bỏ chọn
						</button>
						<button class="save" :disabled="isSaving" v-on:click="() => {
							closeFormAddProduct();
						}">
							Xong
						</button>
					</div>
				</div>
			</div>
		</div> -->
			<div class="products-category-content" v-if="!isEditing && categoryData?.id">
				<!-- <button class="update-list-button" v-on:click="() => {
				isEditing = true
			}">{{ $t('ProductsCategoryComponent.cap_nhat_danh_sach') }}</button> -->
				<div class="empty-products" v-if="!listProduct || !listProduct.length">
					<img loading="lazy" :src="list_empty" :placeholder="list_empty" alt="" />
					<span>{{ $t('ProductsCategoryComponent.chua_co_san_pham_nao') }}</span>
				</div>
				<div v-else class="list-products">
					<div v-for="(itemProduct, index) of listProduct" class="item-product" :class="{ 'odd': index % 2 == 0}"
						v-on:click="() => {
							router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
						}">
						<img loading="lazy"
							:src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ? (domainImage + itemProduct.profile_picture) : icon_for_product"
							:placeholder="icon_for_product" alt="" />
						<div class="item-product-content">
							<span class="name">
								{{ showTranslateProductName(itemProduct) }}
							</span>
							<span class="price">
								{{ formatCurrency(itemProduct.price, shopData.currency) }}
							</span>
						</div>
					</div>
				</div>
				<div class="footer">
					<button class="cancel" v-on:click="() => {
						isEditing = true;
						openFormDeleteProduct()
					}">
						{{ $t('ProductsCategoryComponent.xoa_san_pham') }}
					</button>
					<button class="save" :disabled="isSaving" v-on:click="() => {
						isEditing = true
						openFormAddProduct();
					}">
						{{ $t('ProductsCategoryComponent.them_san_pham') }}
					</button>
				</div>

			</div>
			<div class="products-category-content" v-if="isEditing && categoryData?.id && showAddProductForm">
				<div class="h-stack search-bar-container">
					<button class="search-button" :disabled="searchProductLoading"
						v-on:click="getSearchProductResult()">
						<Icon name="ion:search" size="20" v-show="!searchProductLoading" />
						<Icon name="eos-icons:loading" size="20" v-show="searchProductLoading" />
					</button>

					<input type="search" autocomplete="off" class="search-input-container" :value="search"
					:maxlength="appConst.max_text_short"
						id="search_input" :placeholder="$t('ProductsCategoryComponent.them_san_pham_cua_ban')"
						v-on:input="async ($event: any) => {
							search = $event.target.value;
							getSearchProductResult();
						}" />
				</div>
				<div class="list-products-editing">
					<div class="list-suggest">
						<div class="suggest-empty" v-if="!(listProductSuggest && listProductSuggest.length)">
							<img loading="lazy" :src="none_result" :placeholder="none_result" alt="" />
							<span>{{ $t('ProductsCategoryComponent.khong_tim_thay_san_pham') }}</span>
						</div>
						<div v-if="(listProductSuggest && listProductSuggest.length)" class="suggest-list"
							id="suggest_list" v-on:scroll="(e) => {
								listSuggestScroll(e);
							}">
							<div v-for="(itemProduct, index) of listProductSuggest" class="item-product" v-on:click="() => {
								if (!itemProduct.existing) {
									itemProduct.selected = !itemProduct.selected;
									let arrSuggest = listProductSuggest;
									arrSuggest[index].selected = itemProduct.selected;

									let arrSelected = listProductSelected;
									let indexSelected = arrSelected.findIndex(function (e: any) {
										return e.id == itemProduct.id;
									})
									if (indexSelected != -1 && !itemProduct.selected) {
										arrSelected.splice(indexSelected, 1)
									}
									else if (indexSelected == -1 && itemProduct.selected) {
										itemProduct.system_price = itemProduct.price;
										arrSelected.push(itemProduct)
									}

									listProductSuggest = arrSuggest;
									listProductSelected = arrSelected;
								}
							}" :class="{ 'odd': index % 2 == 0 }">
								<img loading="lazy"
									:src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ? (domainImage + itemProduct.profile_picture) : icon_for_product"
									:placeholder="icon_for_product" alt="" />
								<div class="item-product-content">
									<span class="name">
										{{ showTranslateProductName(itemProduct) }}
									</span>
									<span class="price">
										{{ formatCurrency(itemProduct.price, shopData.currency) }}
									</span>
								</div>
								<button class="select-button" :disabled="itemProduct.existing">
									<Icon name="mdi:checkbox-marked-circle-outline" v-if="itemProduct.selected"></Icon>
									<Icon name="mdi:checkbox-blank-circle-outline" v-if="!itemProduct.selected"></Icon>
								</button>
							</div>
							<div id="last_of_list_suggest"></div>
							<span class="showed-all"
								v-if="listProductSuggest && listProductSuggest.length && (listProductSuggest.length >= listProductSuggestCount || searchSuggestAll)">
								{{ $t('ProductsCategoryComponent.da_hien_thi_toan_bo_ket_qua') }}
							</span>
							<div class="loading-more" v-if="searchProductLoading">{{
								$t('ProductsCategoryComponent.dang_tai') }}
							</div>
						</div>
						<div class="footer add-product-to-list">
							<span>{{ $t('ProductsCategoryComponent.da_chon') }}:
								{{ listProductSuggest.filter((e: any) => !e.existing && e.selected).length }}</span>
							<button class="go-back" v-on:click="() => {
								clearListProductSelected();
								closeFormAddProduct();
							}">
								{{ $t('ProductsCategoryComponent.quay_lai') }}
							</button>
							<button class="cancel"
								v-if="listProductSuggest.filter((e: any) => !e.existing && e.selected).length"
								v-on:click="() => {
									clearListProductSelected();
								}">
								{{ $t('ProductsCategoryComponent.bo_chon') }}
							</button>
							<button class="save"
								:disabled="isSaving || !listProductSuggest.filter((e: any) => !e.existing && e.selected).length"
								v-on:click="() => {
									saveProducts();
								}">
								{{ $t('ProductsCategoryComponent.luu') }}
							</button>
						</div>
					</div>
				</div>
			</div>

			<div class="products-category-content" v-if="isEditing && categoryData?.id && showDeleteProductForm">
				<span class="delete-notice">{{ $t('ProductsCategoryComponent.chon_san_pham_xoa_khoi_danh_muc') }}</span>
				<div class="list-products-editing">
					<div class="list-suggest">
						<div class="suggest-empty" v-if="!(listProductDelete && listProductDelete.length)">
							<img loading="lazy" :src="none_result" :placeholder="none_result" alt="" />
							<span>{{ $t('ProductsCategoryComponent.chua_co_san_pham') }}</span>
						</div>
						<div v-if="(listProductDelete && listProductDelete.length)" class="suggest-list"
							id="delete_list">
							<div v-for="(itemProduct, index) of listProductDelete" class="item-product" v-on:click="() => {
								itemProduct.delete = !itemProduct.delete;
							}" :class="{ 'odd': index % 2 == 0 }">
								<img loading="lazy"
									:src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ? (domainImage + itemProduct.profile_picture) : icon_for_product"
									:placeholder="icon_for_product" alt="" />
								<div class="item-product-content">
									<span class="name">
										{{ showTranslateProductName(itemProduct) }}
									</span>
									<span class="price">
										{{ formatCurrency(itemProduct.price, shopData.currency) }}
									</span>
								</div>
								<button class="select-button">
									<Icon name="mdi:checkbox-marked-circle-outline" v-if="itemProduct.delete"></Icon>
									<Icon name="mdi:checkbox-blank-circle-outline" v-if="!itemProduct.delete"></Icon>
								</button>
							</div>
						</div>
						<div class="footer">
							<button class="go-back" v-on:click="() => {
								closeFormDeleteProduct()
							}">
								{{ $t('ProductsCategoryComponent.quay_lai') }}
							</button>
							<button class="save"
								:disabled="isSaving || !listProductDelete.filter((e: any) => e.delete).length"
								v-on:click="() => {
									deleteProducts()
								}">
								{{ $t('ProductsCategoryComponent.xoa') }}
							</button>
						</div>
					</div>
				</div>
			</div>


			<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
				v-model="showEditCategoryModal" v-on:closed="() => {
					showEditCategoryModal = false
				}" contentTransition="vfm-fade">
				<EditCategoryComponent :categoryData="JSON.parse(JSON.stringify(categoryData))" 
					:shop_data="JSON.parse(JSON.stringify(shopData))"
					v-on:close="async (e: any) => {
						showEditCategoryModal = false;
						if (e) {
							await getCategoryDetail();
							useNuxtApp().$emit('refresh_category_manage', true)
						}
					}" />
			</VueFinalModal>
			<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container" :overlay-behavior="'persist'"
				v-model="showDeleteCategoryModal" v-on:closed="() => { 
					showDeleteCategoryModal = false
				}" contentTransition="vfm-fade">
				<div class='v-stack delete-category-content'>
					<span class='delete-category-title'>
						{{ $t('ProductsCategoryComponent.xoa_danh_muc') }}
					</span>

					<span class='delete-category-message'>
						{{ $t('ProductsCategoryComponent.ban_chac_chan_muon_xoa_danh_muc') }}
						<span class='category-name'>
							{{ categoryData ? categoryData.name : "" }}
						</span>?
					</span>
				</div>
				<div class='h-stack confirm-modal-buttons'>
					<button class='reject-button' :disabled="isSaving" v-on:click="() => {
						showDeleteCategoryModal = false;
					}">
						{{ $t('ProductsCategoryComponent.khong') }}
					</button>
					<button class='accept-button' :disabled="isSaving" v-on:click="() => {
						deleteCategory();
						showDeleteCategoryModal = false;
					}">
						{{ $t('ProductsCategoryComponent.co') }}
					</button>
				</div>
			</VueFinalModal>

		</div>
	</div>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '~/assets/image/icon-for-product.png';
import none_result from "~/assets/image/none-result.webp"
import list_empty from "~/assets/image/list-empty-2.jpg"
import remove_icon from "~/assets/image/remove_icon.png"
import { toast } from 'vue3-toastify';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { appConst, domainImage, formatCurrency, showTranslateProductName } from '~/assets/AppConst';
import { HttpStatusCode } from 'axios';
import { AgentService } from '~/services/agentService/agentService';
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue';


var emit = defineEmits(["close"]);

const nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();
var props = defineProps({
	shopData: {},
	categoryData: null,
	mode: null,
})
const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.ProductsCategoryComponent')
})
let authService = new AuthService();
let userService = new UserService();
let productService = new ProductService();
let imageService = new ImageService();
let shopService = new ShopService();
let agentService = new AgentService();
let categoryService = new CategoryService();
let searchProductTimeout: any;

var shop_id = ref<any>(route.params.id ? route.params.id : null)
var category_id = ref((route.params.category_id ? route.params.category_id : null) as any)
var shopData = ref(router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)) : null);
var categoryData = ref(router.options.history.state.categoryData ? JSON.parse(JSON.stringify(router.options.history.state.categoryData)) : null);
var shopProducts = ref([] as any[]);
var listProduct = ref(router.options.history.state.categoryData ? JSON.parse(JSON.stringify(router.options.history.state.categoryData)).products : []);
var listProductSuggest = ref([] as any[]);
var listProductDelete = ref([] as any[]);
var listProductSuggestCount = ref(0);
var listProductSelected = ref(router.options.history.state.categoryData ? JSON.parse(JSON.stringify(router.options.history.state.categoryData)).products : []);

var search = ref("");
var searchFocus = ref(router.options.history.state.searchFocus ? router.options.history.state.searchFocus : false);
var showAddProductForm = ref(false);
var showDeleteProductForm = ref(false);
var isSaving = ref(false);
var isEditing = ref(router.options.history.state.isEditing ? router.options.history.state.isEditing : false);
var updated = ref(false);
var searchSuggestAll = ref(false);
var searchProductLoading = ref(true);

var showEditCategoryModal = ref(false);
var showDeleteCategoryModal = ref(false);
onMounted(async () => {
	shopData.value = router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)) : null;
	if (props.mode != 'agent') {
		await getMyShop();
	}
	else {
		await getShopDetail()
	}
	if (category_id.value) {
		await getCategoryDetail();
	}

	await getSearchProductResult();
	useSeoMeta({
		title: categoryData.value ? categoryData.value.name : t('AppRouteTitle.ProductsCategoryComponent')
	});
	if (router.options.history.state.showAddProductForm == true) {
		openFormAddProduct();
	}
	// if (searchFocus.value == true) openFormAddProduct();
})
function getMyShop() {
	shopService.myShop().then(res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {

			shopData.value = res.body.data;
		}
		else {

			shopData.value = null;
			toast.error(t('ProductsCategoryComponent.ban_chua_dang_ky_cua_hang'));
			setTimeout(() => {
				close()
			}, 1000);
		}
		return
	}).catch((err) => {
		toast.error(t('ProductsCategoryComponent.ban_chua_dang_ky_cua_hang'));
		setTimeout(() => {
			close()
		}, 1000);
	})
}

async function getShopDetail() {
	let shopData$ = await agentService.agentShopDetail(shop_id.value)
	if (shopData$.status == HttpStatusCode.Ok) {
		shopData.value = shopData$.body.data;
	}
	else if (shopData$.status == 401) {
		shopData.value = null;
		toast.error(t('ProductsCategoryComponent.cua_hang_khong_ton_tai'));
		setTimeout(() => {
			close()
		}, 1000);
	}
	return;
}
function close() {
	// emit('close', updated.value)
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
	if (updated.value == true) {
		nuxtApp.$emit('refresh_category_manage', true)
	}
}

function getSearchProductResult() {
	searchProductLoading.value = true
	clearTimeout(searchProductTimeout);
	searchProductTimeout = setTimeout(() => {

		shopService.searchProductsInShop(
			search.value.length ? search.value : "",
			shopData.value?.id ?? shop_id.value,
			[],
			20,
			null,
			null,
			null,
			props.mode == 'agent' ? false : true
		).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				let listSuggest: any[] = res.body.data.result;
				listProductSuggest.value = listSuggest;
				listProductSuggestCount.value = res.body.data.count;
				searchProductLoading.value = false;
				setOptionMyShopProduct();
				document.getElementById("suggest_list")?.scrollTo({ top: 0 });
				searchSuggestAll.value = false;
			}
		}).catch(() => {
			searchProductLoading.value = false;
		})
	}, 1000);

}

function saveProducts() {
	isSaving.value = true;
	let body = {
		category_id: categoryData.value.id,
		product_ids: listProductSelected.value.map((item: any) => item.id)
	}
	categoryService.updateListProduct(body).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			isEditing.value = false;
			updated.value = true;
			closeFormAddProduct();
			getCategoryDetail();
		}
		else {
			toast.error(t('ProductsCategoryComponent.cap_nhat_that_bai'));
		}
		isSaving.value = false;
	}).catch(err => {
		isSaving.value = false;
		toast.error(t('ProductsCategoryComponent.cap_nhat_that_bai'));
	})
}

function getCategoryDetail() {
	categoryService.detailCategory(categoryData.value?.id ? categoryData.value?.id : category_id.value).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			categoryData.value = JSON.parse(JSON.stringify(res.body.data))
			listProduct.value = JSON.parse(JSON.stringify(res.body.data.products));
			listProductSelected.value = JSON.parse(JSON.stringify(res.body.data.products));
		}
	})
}


function getMoreFilterProducts() {
	clearTimeout(searchProductTimeout)
	searchProductTimeout = setTimeout(() => {
		if (listProductSuggest.value.length < listProductSuggestCount.value &&
			!searchSuggestAll.value) {
			searchProductLoading.value = true;
			shopService.searchProductsInShop(
				search.value ? search.value : "",
				shopData.value.id,
				[],
				20,
				listProductSuggest.value.length,
				null,
				null,
				false
			).then(async res => {
				if (res.status && res.status == HttpStatusCode.Ok) {

					let listTemp = res.body.data.result;
					if (listTemp.length) {
						listProductSuggest.value = [...listProductSuggest.value, ...listTemp];
						searchProductLoading.value = false;
						setOptionMyShopProduct();
					}
					else {
						searchSuggestAll.value = true;
					}
				}
				else {
					searchProductLoading.value = false;
				}
			}).catch(() => {
				searchProductLoading.value = false;
			})
		}
	}, 1000);

}

function setOptionMyShopProduct() {
	let listTemp = JSON.parse(JSON.stringify(listProductSuggest.value));
	listTemp.forEach((item: any) => {
		item.selected = false;
		item.existing = false;

		let indexSelected = listProductSelected.value.findIndex(function (e: any) {
			return e.id == item.id
		});
		if (indexSelected != -1) {
			item.selected = true;
		}

		let indexExisting = listProduct.value.findIndex(function (e: any) {
			return e.id == item.id
		});
		if (indexExisting != -1) {
			item.existing = true;
		}
	});

	listProductSuggest.value = listTemp;
}


function listSuggestScroll(event: any) {
	let el = document
		.getElementById("last_of_list_suggest")
		?.getBoundingClientRect().bottom;
	if (el && el <= window.innerHeight + 10) {
		if (listProductSuggest.value.length < listProductSuggestCount.value) {
			getMoreFilterProducts();
		}
	}
}
function openFormAddProduct() {
	let listSuggest: any[] = listProductSuggest.value;
	searchProductLoading.value = false;
	listProductSuggest.value = JSON.parse(JSON.stringify(listSuggest));
	showAddProductForm.value = true;
	setOptionMyShopProduct()
}
function closeFormAddProduct() {
	// document.getElementById('search_input')?.blur();
	showAddProductForm.value = false;
	isEditing.value = false;
}

function openFormDeleteProduct() {
	showDeleteProductForm.value = true;
	let listToDelete = JSON.parse(JSON.stringify(listProduct.value));
	listToDelete.forEach((element: any) => {
		element.delete = false
	});

	listProductDelete.value = JSON.parse(JSON.stringify(listToDelete))
}
function closeFormDeleteProduct() {
	// document.getElementById('search_input')?.blur();
	showDeleteProductForm.value = false;
	isEditing.value = false;
}
function deleteProducts() {
	let listDelete = listProductDelete.value.filter((e) => {
		return !e.delete;
	});

	isSaving.value = true;
	let body = {
		category_id: categoryData.value.id,
		product_ids: listDelete.map((item: any) => item.id)
	}
	categoryService.updateListProduct(body).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			closeFormDeleteProduct();
			updated.value = true;
			getCategoryDetail();
		}
		else {
			toast.error(t('ProductsCategoryComponent.cap_nhat_that_bai'));
		}
		isSaving.value = false;
	}).catch(err => {
		isSaving.value = false;
		toast.error(t('ProductsCategoryComponent.cap_nhat_that_bai'));
	})
}
function clearListProductSelected() {
	let arr = listProductSuggest.value;
	let arrSelect = listProductSelected.value;
	arr.forEach((item: any) => {
		if (item.selected && !item.existing) {
			item.selected = false;
			let indexSelect = arrSelect.findIndex(function (e: any) {
				return e.id == item.id
			});
			if (indexSelect != -1) {
				arrSelect.splice(indexSelect, 1)
			}
		}

	});
	listProductSuggest.value = arr;
	listProductSelected.value = arrSelect;
}

function deleteCategory() {
	categoryService.removeCategory(categoryData.value.id).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			// nuxtApp.$emit('refresh_category_manage')
			updated.value = true;
			close();
		}
	})
}
</script>

<style lang="scss" src="./ProductsCategoryStyles.scss"></style>