<template>
  <div class='title-header'>
    <div class="header-left">
      <slot name="header_left" v-if="$slots.header_left"></slot>
      <button v-else class="back-button" v-on:click="() => {
        router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
      }">
        <Icon name="lucide:chevron-left" />
      </button>
    </div>
    <h3>{{ props.title }}</h3>
    <div class="header-right">
      <slot name="header_right" v-if="$slots.header_right"></slot>
    </div>
  </div>
</template>

<style lang="scss" src="./HeaderStyles.scss"></style>

<script setup lang="ts">
import { appRoute } from '~/assets/appRoute';

const props = defineProps({
  title: null,
})
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

</script>
