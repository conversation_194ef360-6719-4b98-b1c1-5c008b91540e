// plugins/longpress.js
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive("click-outside", {
    mounted(el: any, binding: { value: Function }) {
      el.clickOutsideHandler = (event: Event) => {
        if (!(el === event.target || el.contains(event.target))) {
          binding.value(event); // Call the provided function
        }
      };
      document.addEventListener('click', el.clickOutsideHandler);
      document.addEventListener('touchstart', el.clickOutsideHandler);
    },
    // unmounted(el: any) {
    //   document.removeEventListener('click', el.clickOutsideHandler);
    // },
  });
});
