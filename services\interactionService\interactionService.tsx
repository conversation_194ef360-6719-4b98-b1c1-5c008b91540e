
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";
import { increase_view_obj_type, type InteractionObjectType } from "~/assets/appDTO";
import moment from 'moment';

export class InteractionService extends BaseHTTPService {
  createOrDelete({object_type_from=null, object_id_from = null, object_type_to = null, object_id_to = null, interaction_type = 1}) {
    let body = {
      object_type_a: object_type_from,
      object_id_a: object_id_from,
      object_type_b: object_type_to,
      object_id_b: object_id_to,
      interaction_type: interaction_type
    }
    return this.https('POST', appConst.apiURL.interactionCreateOrDelete, body, null, true);
  }

  getInterctions(){
    return this.https('GET', appConst.apiURL.interactionList);
  }

  checkInterctions(object_b_id: string, interaction_type: InteractionObjectType){
    let body = {
      object_id_b: object_b_id,
      interaction_type: interaction_type 
    }
    return this.https('POST', appConst.apiURL.interactionCheck, body, null, true);
  }

  shouldIncreaseViewCount(key: string): boolean {
    const lastViewTime = localStorage.getItem(key);
    const currentTime = new Date().getTime();
  
    if (!lastViewTime || moment.duration(moment(currentTime).diff(moment(parseInt(lastViewTime)))).asHours() > 1) {
      localStorage.setItem(key, currentTime.toString());
      return true;
    }
  
    return false;
  }

  increaseView(object_id: string, object_type = increase_view_obj_type.product){
    let body = {
      object_id: object_id,
      object_type: object_type 
    }
    return this.https('POST', appConst.apiURL.interactionIncrementView, body, null, false);
  }
}