.otp-confirm-container {
    gap: 10px;
    padding: 15px;

    & .title{
        font-size: 1.5em;
        font-weight: 500;
        text-align: center;

        & > span{
            color: var(--primary-color-1);
            font-weight: 600;
        }
    }

    & .otp-input{
        color: var(--primary-color-1);
    }

    & .re-send{
        text-align: center;

        & > button {
            color: var(--primary-color-1);
            font-weight: 600;
        }
    }

    & .form-actions {
        position: sticky;
        bottom: 1px;
        padding: 10px 0 0;
        background: white;
        justify-content: space-evenly;

        &>button {
            border-radius: 2em;
            width: 35%;
            padding: 5px 20px;
            border: none;
        }

        &>.cancel-button {
            color: var(--color-button-special);
            border: thin solid var(--color-button-special);
            background: white;
        }

        &>.save-button {
            color: white;
            border: thin solid var(--primary-color-1);
            background: var(--primary-color-1);
        }
    }
}
