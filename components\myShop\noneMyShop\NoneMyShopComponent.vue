<template>
	<div class='none-my-shop-container'>
		<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ appRouteTitle.MyShopComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
		<SubHeaderV2Component :title="props.title ?? $t('AppRouteTitle.MyShopComponent')" v-if="props.show_header">
			<template v-slot:header_right>
				<slot name="header_right" v-if="$slots.header_right"></slot>
			</template>
		</SubHeaderV2Component>
		<div class='none-my-shop-content'>
			<img loading="lazy" :src='none_shop' :placeholder="none_shop"
				:alt="$t('MyShopComponent.chua_dang_ky_cua_hang')" />

			<span v-if="props.mode != 'agent'">
				{{ props.message ?? $t('MyShopComponent.ban_chua_co_cua_hang') }}
			</span>

			<span v-else>
				{{ props.message ?? $t('UnauthorizedComponent.ban_khong_co_quyen_truy_cap') }}
			</span>

			<nuxt-link v-if="props.mode != 'agent'" :to="{
				path: appRoute.RegisterShopComponent
			}">
				<button>
					{{ $t('MyShopComponent.dang_ky_ngay') }}
				</button>
			</nuxt-link>
		</div>

	</div>
</template>

<script lang="ts" setup>
import appRoute from "~/assets/appRoute";
import none_shop from "~/assets/image/none-shop.jpg";
import { AuthService } from "~/services/authService/authService";

const route = useRoute();
const authService = new AuthService();
var props = defineProps({
	show_header: null,
	title: null,
	mode: null,
	message: null,
	header_right: null
})

var profileData = ref<any>(null);
onMounted(async () => {
	profileData.value = await authService.checkAuth();
})
</script>

<style lang="scss" src="./NoneMyShopStyles.scss"></style>