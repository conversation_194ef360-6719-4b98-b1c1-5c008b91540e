.manage-category-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: auto;

  // & > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .list-category-container {
    display: flex;
    flex: 1;
    overflow: auto;
    padding: 0 10px;
    z-index: 2;

    & .v-expansion-panels {
      padding: 10px 0;
    }

    & .drag-drop-container {
      width: 100%;
    }

    & .item-category-accordion:active{
      transform: scale(.95);
      transition: transform .2s;
      transition-delay: .5s;
    }
    & .item-category-accordion {
      padding: 5px 10px;
      border: none;
      border-bottom: thin solid #ccc;
      border-radius: 0;
      margin: 5px 0;

      & .v-expansion-panel-title--active > .v-expansion-panel-title__overlay,
      .v-expansion-panel-title[aria-haspopup="menu"][aria-expanded="true"]
        > .v-expansion-panel-title__overlay {
        opacity: calc(0.2 * var(--v-theme-overlay-multiplier));
      }

      & .v-expansion-panel-title:hover > .v-expansion-panel-title__overlay{
        opacity: calc(0.2 * var(--v-theme-overlay-multiplier));
      }

      & .accordion-header {
        font-weight: 500;
        font-size: 1em;
        padding: 5px;
        gap: 10px;
        outline: none !important;
        box-shadow: none;
        background: white;
        animation: none !important;

        & > span {
          color: var(--primary-color-1);
          font-style: italic;
        }

        & .stt {
          width: fit-content;
          background: var(--color-button-special);
          color: white !important;
          padding: 1px 10px;
          border-radius: 2em;
          font-style: normal !important;
          line-height: normal;
        }
      }
      & [class*="v-expansion-panel-text"] {
        gap: 5px;
        flex: 1;
        padding: 0;
        & > .h-stack {
          justify-content: space-between;
        }
        & button {
          border: none;
          border-radius: 3px;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          width: 33%;
          font-weight: 500;
          font-size: 0.85em;
          padding: 5px;

          & > span {
            font-size: 1.2em;
            display: flex;
          }
        }
        & button.view-product {
          background: var(--primary-color-1);
        }
        & button.edit {
          background: var(--color-text-note);
        }
        & button.delete {
          background: var(--color-button-special);
        }
      }
    }
  }

  & .loading {
    flex: 1;
    text-align: center;
  }

  & > .add-category-button {
    color: white;
    background-color: var(--primary-color-1);
    gap: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    font-size: 1.2em;
    font-weight: 600;
    padding: 10px 0;
  }

  & .categories-list-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 10px;

    & > img {
      width: 200px;
      height: 200px;
      align-items: center;
      object-fit: contain;
      position: relative;
      border-radius: 50%;
    }

    & span {
      font-size: 1.5em;
      font-weight: 500;
    }
  }
}

.delete-category-content {
  font-size: 1.3em;
  text-align: center;
}

.delete-category-title {
  font-size: 1em;
  font-weight: bold;
}

.delete-category-message {
  font-size: 1em;
  text-align: center;
}

.delete-category-message > .category-name {
  color: var(--primary-color-2);
  font-weight: 600;
}

.category-content-container{
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}