<template>
  <div class='sub-title-v2-header' id="sub_header_remagan">
    <div class="header-left">
      <slot name="header_left" v-if="$slots.header_left"></slot>
      <!-- <button class="back-button" v-else v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="solar:round-alt-arrow-left-linear"></Icon>
				</button> -->
    </div>
    <div class="header-middle">
      <slot name="header_middle" v-if="$slots.header_middle"></slot>
      <h3 v-else-if="props.title?.length">{{ props.title }}</h3>
    </div>
    
    <div class="header-right">
      <slot name="header_right" v-if="$slots.header_right"></slot>
    </div>
  </div>
</template>

<style lang="scss" src="./SubHeaderV2Styles.scss"></style>

<script setup lang="ts">
import logo_v2 from "~/assets/imageV2/remagan-logo-v2.png";

import { appRoute } from '~/assets/appRoute';
import { appConst } from "~/assets/AppConst";

const props = defineProps({
  title: null,
  header_level_2_id: null,
  last_element_id: null
})
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const { t } = useI18n();

</script>
