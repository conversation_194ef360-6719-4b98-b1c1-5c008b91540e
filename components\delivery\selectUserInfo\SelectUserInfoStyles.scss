.select-user-info-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .select-user-info-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    height: auto;
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    & > .title-header {
      border-radius: inherit;
    }

    & > .select-user-info-content-container {
      flex: 1;
      padding: 0 10px;
      width: 100%;
      height: auto;
      overflow: auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      & > .select-user-saved {
        color: var(--primary-color-1);
        font-size: 15px;
        margin-top: 15px;
        font-weight: 600;
        margin-left: auto;
      }
      & > .v-stack {
        margin-bottom: 5px;
        & .label {
          color: #2e2d30;
          font-weight: bold;
          font-size: 15px;
        }
        & > .input-order {
          background-color: #f5f6fa;
          height: 45px;
          padding: 10px 10px 10px 30px;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;
        }

        & > .search-address-container{
          background: #f5f6fa;
          & > .input-search-address{
            background: #f5f6fa;
          }
        }
      }

      & .map-container {
        width: 100%;
        flex: 1;
        min-height: 250px;
        height: 250px;
        border-radius: 10px;
    
        & .leaflet-container {
          border-radius: 10px;
          height: 100%;
          min-height: inherit !important;
    
          & .marker-location {
            width: 30px;
            height: 40px;
            position: absolute;
            bottom: 50%;
            left: 50%;
            object-fit: contain;
            transform: translateX(-50%);
            z-index: 450;
    
            & > img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
    }

    & > .footer-actions{
      padding: 5px 10px;
      width: 100%;

      & > .submit-button{
        padding: 5px;
        background-color: var(--primary-color-1);
        color: white;
        width: 100%;
        border-radius: 7px;
      }
    }
  }
}
