<script setup lang="ts">
import { appConst, domainImage, formatCurrency, showTranslateProductName, validPhone, formatNumberToShort } from '~/assets/AppConst';

import hot_sale from "~/assets/imageV2/hot-sale.svg";
import discount_tag from "~/assets/image/sale_off_badge_background.png";
import shop_banner_temp from "~/assets/imageV2/logo-landscape-1024.jpg";
import list_empty from "~/assets/image/list-empty-2.jpg"
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'

import { condition_relationship, type delivery_fee_config } from '~/components/myShop/shopConfig/ShopConfigDTO';

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n()
var props = defineProps({
	mode: null,
	shop_data: null,
	css_data: null
})

const tempPromotions: delivery_fee_config[] = [{
	id: 1,
	name: 'ten_khuyen_mai',
	description: 'mo_ta_khuyen_mai',
	type: 'percent',
	value: 50,
	max_value: 25000,
	enabled: true,
	conditions: [],
	relationship_condition: condition_relationship.and
}];

const dataShopTempProducts: any[] = [
	{
		id: "product_1",
		name: "sản phẩm 1",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	},
	{
		id: "product_2",
		name: "sản phẩm 2",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	},
	{
		id: "product_3",
		name: "sản phẩm 3",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
		categories: [
			{
				name: "111 sửa",
				translation: [
					{
						object_type: 4,
						language_code: "vi",
						name: "111 sửa",
						description: "111 sửa"
					},
					{
						object_type: 4,
						language_code: "en",
						name: "111 fix",
						description: "111 fix"
					},
					{
						object_type: 4,
						language_code: "zh",
						name: "111 修",
						description: "111 修"
					},
					{
						object_type: 4,
						language_code: "ko",
						name: "111 수정",
						description: "111 수정"
					}
				]
			}
		]
	},
	{
		id: "product_4",
		name: "sản phẩm 4",
		is_main: true,
		brand_id: null,
		profile_picture: null,
		notes: null,
		price: 15000,
		price_off: 10000,
		enable: true,
		type: 3,
		is_feature: true,
		stock: 998,
		sold_count: 0,
		is_suggest: false,
		views: 0,
		likes: 0,
		follows: 0,
		ratings: 0,
		extra_code: null,
		slug: '#',
	}
]

const dataShopTempCategories: any[] = [
	{
		id: "category_1",
		name: "danh mục 1",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 1",
				description: "danh mục 1"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 1",
				description: "Category 1"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 1",
				description: "카테고리 1"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 1",
				description: "Категория 1"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_2",
		name: "danh mục 2",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 2",
				description: "danh mục 2"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 2",
				description: "Category 2"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 2",
				description: "카테고리 2"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 2",
				description: "Категория 2"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_3",
		name: "danh mục 3",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 3",
				description: "danh mục 3"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 3",
				description: "Category 3"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 3",
				description: "카테고리 3"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 3",
				description: "Категория 3"
			}
		],
		products: dataShopTempProducts
	},
	{
		id: "category_4",
		name: "danh mục 4",
		translation: [
			{
				object_type: 4,
				language_code: "vi",
				name: "danh mục 4",
				description: "danh mục 4"
			},
			{
				object_type: 4,
				language_code: "en",
				name: "Category 4",
				description: "Category 4"
			},
			{
				object_type: 4,
				language_code: "ko",
				name: "카테고리 4",
				description: "카테고리 4"
			},
			{
				object_type: 4,
				language_code: "ru",
				name: "Категория 4",
				description: "Категория 4"
			}
		],
		products: dataShopTempProducts
	}
]

var showGrid = ref(false)
var open_time_list = ref([] as any);
var showSearchBar = ref(false);

onUnmounted(async () => {
});

onMounted(async () => {

	console.log(props);
	open_time_list.value = formatOpeningHours(JSON.parse(props.shop_data?.open_hours));
	// useServerCache({
	// 	key: `shop_space_${shopDetails.value.id}`,
	// 	duration: 60 * 60, // Cache for 1 hour
	// 	condition: () => !!shopDetails.value.name // Cache only if productData.name is available
	// });
});
onBeforeMount(async () => { });
function formatOpeningHours(hoursObj: any) {
	function formatHours(hours: any) {
		return hours.map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`).join(' & ');
	}
	const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

	const result = [];
	if (hoursObj) {
		let currentGroup: any = [];
		let currentHours = hoursObj[days[0]];

		for (let i = 0; i < days.length; i++) {
			const day = days[i];
			const hours = hoursObj[day];

			if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
				currentGroup.push(t(`DayInWeek.${day}`));
			} else {
				if (currentGroup.length > 1) {
					// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				} else {
					// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				}
				currentGroup = [t(`DayInWeek.${day}`)];
				currentHours = hours;
			}
		}
		// Xử lý nhóm cuối cùng
		if (currentGroup.length > 1) {
			// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		} else {
			// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		}
	}

	return result;

}

const containerScrolling = (e: any) => {

	var stickyElement = document.getElementById('categories_container') as HTMLElement;

	console.log(stickyElement.getBoundingClientRect().top)

	if (stickyElement.getBoundingClientRect().top <= 60) {
		stickyElement.classList.add('sticky-padding');
	} else {
		stickyElement.classList.remove('sticky-padding');
	}
}
</script>
<template>
	<div class="public-container" id="shop_temp_container_default_overview" v-on:scroll="containerScrolling">
		<div class="shop-v2-default-container">
			<div class="search-input-container" id="search_input_container">
				<div class="search-input-group">
					<button class="search-button">
						<Icon name="solar:rounded-magnifer-outline"></Icon>
					</button>
					<input type="search" name='search-text'
						:placeholder="props.shop_data?.name ?? $t('ShopComponent.tim_san_pham')" autoComplete='off'
						:maxlength="appConst.max_text_short" />

				</div>
				<!-- <nuxt-link :to="appRoute.CartComponent" class="cart-button">
					<Icon name=solar:cart-3-bold></Icon>
					<div class="count" v-if="cartData?.length > 0">{{ cartData.length < 10 ? cartData.length : "9+"
							}}</div>
				</nuxt-link> -->
			</div>

			<div class="shop-v2-default-content-container" id='shop-container' v-if="props.shop_data?.id">
				<div class='v-stack shop-content-container'>
					<ShopBannerComponent id="shop_banner"
						:imgSrc="(props.shop_data && props.shop_data?.banner) ? (domainImage + props.shop_data?.banner?.path) : null"
						:imgStyle="props.shop_data?.banner?.style"></ShopBannerComponent>

					<div class="shop-detail-container">
						<AvatarComponent class="shop-logo" :imgTitle="props.shop_data?.name"
							:imgStyle="props.shop_data?.logo?.style" :imgSrc="props.shop_data?.logo?.path?.length
								? (domainImage + props.shop_data?.logo?.path)
								: ''" :width="75" :height="75" />
						<div class="shop-detail-content">
							<div class="name-share">
								<nuxt-link :to="'#'" class="name">
									<span>{{ props.shop_data?.name }}</span>
									<Icon name="solar:alt-arrow-right-linear"></Icon>
								</nuxt-link>
								<button class="share">
									<Icon name="solar:share-linear"></Icon>
								</button>
							</div>
							<span class="follows">
								<em>{{ props.shop_data?.follows ?? 0 }}</em> {{ $t('ShopComponent.nguoi_theo_doi') }}
							</span>

							<span class="is-open" v-if="props.shop_data?.settings?.general" :class="{
								'closed': !props.shop_data?.settings?.general?.is_open?.value
							}">
								<em v-if="props.shop_data?.settings?.general?.is_open.value">
									{{ $t('ShopComponent.dang_mo_cua') }}
								</em>
								<em v-else class="close">
									<span :class="{
										'has-reason': props.shop_data?.settings?.general?.is_open?.reason && (
											props.shop_data?.settings?.general?.is_open?.reason?.[locale]?.length
											|| props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
										)
									}">{{ $t('ShopComponent.da_dong_cua') }}</span>

									<span class="reason" v-if="props.shop_data?.settings?.general?.is_open?.reason && (
										props.shop_data?.settings?.general?.is_open?.reason?.[locale]?.length
										|| props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
									)">{{
										props.shop_data?.settings?.general?.is_open?.reason?.[locale]
										??
										props.shop_data?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]
										}}</span>
								</em>
							</span>
							<span class="is-open" v-else>
								<em>{{ $t('ShopComponent.dang_mo_cua') }}</em>
							</span>

							<div class="actions">
								<!-- <nuxt-link class="action-button call" :to="`tel:${validPhone(shopData?.phone)}`"
									:target="'_blank'" :title="$t('ShopComponent.goi')" v-on:click="() => {
										callToShop()
									}">
									<Icon name="solar:phone-rounded-bold"></Icon>
									{{ $t('ShopComponent.goi') }}
								</nuxt-link> -->
								<button class="action-button direction" :title="$t('ShopComponent.chi_duong')">
									<Icon name="solar:point-on-map-bold"></Icon>
									{{ $t('ShopComponent.chi_duong') }}
								</button>

								<button class="action-button follow" :title="$t('ShopComponent.theo_doi')">
									<Icon name="solar:add-circle-bold"></Icon>
									<span>{{ $t('ShopComponent.theo_doi') }}</span>

								</button>
							</div>
						</div>
					</div>
					<div class="slogan" v-if="props.shop_data?.settings?.general?.message?.greeting?.[locale]">
						<span class="text">{{ props.shop_data?.settings?.general?.message?.greeting?.[locale] }}</span>
					</div>
					<div class="shop-detail-open-time" v-if="open_time_list.length > 0">
						<div class="label">
							<span>{{ $t('ShopComponent.gio_mo_cua') }}</span>
							<Icon name="solar:clock-square-linear"></Icon>
						</div>
						<div class="line"></div>
						<div class="list-open-time">
							<div class="item-open-time" v-for="item in open_time_list">
								<span class="days">{{ item.days }}</span>
								<span class="times">{{ item.times }}</span>
							</div>
						</div>
					</div>

					<div class="shop-detail-notification"
						v-if="props.shop_data.settings?.general?.message?.notification?.[locale]">
						<div class="label">
							<Icon name="fxemoji:loudspeaker"></Icon>
							<span>{{ $t('ShopComponent.thong_bao') }}</span>
						</div>
						<div class="line"></div>
						<div class="notification-content"
							v-html="props.shop_data.settings?.general?.message?.notification?.[locale]">
						</div>
					</div>

					<div class="shop-detail-delivery-promotion"
						v-if="props.shop_data?.settings?.order?.delivery?.length > 0">
						<div class="label">
							<Icon name="hugeicons:discount-tag-02"></Icon>
							<span>{{ $t('ShopComponent.khuyen_mai') }}</span>
						</div>
						<div class="line"></div>
						<div class="list-delivery-promotion">
							<Swiper class="my-carousel delivery-promotion-carousel" :modules="[SwiperFreeMode]"
								:navigation="false" :freeMode="true" :slides-per-view="'auto'"
								:slides-per-group-auto="true" :loop="false" :spaceBetween="10" :effect="'creative'"
								:autoplay="false" key="category-carousel">
								<SwiperSlide class="delivery-promotion-item-slide"
									v-for="(itemPromotion, indexPromotion) in tempPromotions"
									:value="`${itemPromotion.id}_${indexPromotion}`"
									:key="`${itemPromotion.id}_${indexPromotion}`"
									:id="`item_promotion_${itemPromotion.id}_${indexPromotion}`">
									<div class="value-tag">
										<span>-{{ itemPromotion?.type == 'percent' ? `${itemPromotion.value}%` :
											formatNumberToShort(itemPromotion.value) }}</span>
										<img :src="discount_tag" loading="lazy">
									</div>
									<div class="promotion-detail">
										<span class="name">{{
											$t(`UpdateViewTemplateComponent.${itemPromotion.name}`)
										}}</span>
										<span class="description">{{
											$t(`UpdateViewTemplateComponent.${itemPromotion.description}`)
										}}</span>
										<span class="max-value" v-if="itemPromotion.max_value">{{
											$t('UpdateViewTemplateComponent.toi_da') }} <em>{{
												formatCurrency(itemPromotion.max_value
												?? 0) }}</em></span>
										<button class="condition-action">
											{{ $t('UpdateViewTemplateComponent.xem_chi_tiet') }}
										</button>
									</div>

								</SwiperSlide>
							</Swiper>
						</div>
					</div>

					<div class="shop-products-container" v-if="dataShopTempProducts?.length">
						<div class="title-stack uppercase">
							<span>{{ $t('ShopComponent.san_pham_hot') }}</span>
						</div>
						<div class="line"></div>
						<div class="products-container">
							<Swiper v-if="dataShopTempProducts?.length"
								class="my-carousel stack-carousel product-carousel"
								:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
								:space-between="10" :loop="false" :effect="'creative'" :navigation="true"
								:freeMode=false :autoplay="false" key="sale-off-carousel">
								<SwiperSlide class="product-item-container-grid"
									v-for="itemProduct of dataShopTempProducts" :key="'sale_off_' + itemProduct.id">
									<img :src="hot_sale" class="top-left-tag" />
									<nuxt-link class="product-item" :to="'#'"
										:title="showTranslateProductName(itemProduct)">

										<img loading="lazy" :src="itemProduct && itemProduct.profile_picture
											? domainImage + itemProduct.profile_picture
											: icon_for_product" :placeholder="icon_for_product" />
										<div class="product-item-content">
											<span class="name">{{ showTranslateProductName(itemProduct) }}</span>
											<div class="h-stack price-add-to-cart"
												v-on:click.stop="(e) => { e.preventDefault() }">
												<span class="price">
													<em class="off"
														v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">{{
															(parseFloat(itemProduct.price) == 0 || itemProduct.price ==
																null)
																? $t('ShopComponent.gia_lien_he')
																: formatCurrency(parseFloat(itemProduct.price), itemProduct.shop
																	?
																	itemProduct.shop.currency
																	: props.shop_data?.currency)
														}}</em>
													{{
														(itemProduct.price_off != null && itemProduct.price_off <
															itemProduct.price) ?
															formatCurrency(parseFloat(itemProduct.price_off),
																itemProduct.shop ? itemProduct.shop.currency :
																	props.shop_data?.currency) : (parseFloat(itemProduct.price) == 0
																		|| itemProduct.price == null) ? $t('ShopComponent.gia_lien_he') :
																formatCurrency(parseFloat(itemProduct.price), itemProduct.shop ?
																	itemProduct.shop.currency : props.shop_data?.currency) }}
														</span>
														<button class="add-to-cart">
															<Icon name="solar:cart-plus-linear"></Icon>
														</button>
											</div>
										</div>
									</nuxt-link>
								</SwiperSlide>
							</Swiper>
						</div>
					</div>

					<div class="shop-products-container" v-if="dataShopTempCategories?.length">

						<div class="categories-container" id="categories_container">
							<Swiper class="my-carousel categories-carousel"
								:modules="[SwiperFreeMode, SwiperNavigation]" :navigation="true" :freeMode="true"
								:centered-slides="true" :centered-slides-bounds="true" :slides-per-view="'auto'"
								:slides-per-group-auto="true" :loop="false" :spaceBetween="30" :effect="'creative'"
								:autoplay="false" key="category-carousel">
								<SwiperSlide class="category-item-slide"
									v-for="(itemCategory, indexTab) of dataShopTempCategories" :class="{
										'active': indexTab == 0
									}" :value="itemCategory.id" :key="itemCategory.id" :id="'tab_' + itemCategory.id">
									<div class="tab-title">
										<span class='name'>
											{{ showTranslateProductName(itemCategory) }}
										</span>
									</div>
								</SwiperSlide>
							</Swiper>
						</div>
						<div class="line"></div>
						<div class="grid-list-view">
							<span>
								{{ $t('ShopComponent.sap_xep_danh_sach_san_pham') }}
							</span>
							<div class="type-view-buttons">
								<button class="type-view-button list" :class="{ 'active': !showGrid }"
									v-on:click="() => { showGrid = false; }">
									<Icon name="solar:server-minimalistic-linear"></Icon>
								</button>
								<button class="type-view-button grid" :class="{ 'active': showGrid }"
									v-on:click="() => { showGrid = true; }">
									<Icon name="solar:widget-linear"></Icon>
								</button>
							</div>
						</div>
						<div class="products-container">
							<div class="category-products-container"
								v-for="(itemCategory, indexCategory) in dataShopTempCategories"
								:key="`category_products_${itemCategory.id || 'all'}`"
								:id="`category_products_${itemCategory.id}`">
								<div class="category-title" :id="`category_products_title_${itemCategory.id}`">
									<span>{{ showTranslateProductName(itemCategory) }} ({{ itemCategory.products.length
										}})</span>
								</div>
								<div class="category-products-list">
									<div :class="{
										'product-item-container-grid': showGrid,
										'product-item-container': !showGrid,
									}" :key="`category_${itemCategory.id || 'all'}_product_item_${itemProduct.id}`"
										v-for="(itemProduct, indexSelected) in itemCategory.products">
										<nuxt-link class="product-item" :to="'#'"
											:title="showTranslateProductName(itemProduct)">

											<img loading="lazy" :src="itemProduct && itemProduct.profile_picture
												? domainImage + itemProduct.profile_picture
												: icon_for_product" :placeholder="icon_for_product" v-on:error="(e: any) => {
													e.target.src = icon_for_broken_image
												}" />
											<div class="product-item-content">
												<span class="name">{{ showTranslateProductName(itemProduct) }}</span>
												<span class="sold-like-amount">
													<span v-if="itemProduct.sold_count">
														{{ $t('AroundComponent.luot_ban', {
															count: itemProduct.sold_count ??
																0
														}) }}
													</span>
													<span v-if="itemProduct.sold_count && itemProduct.likes">
														&nbsp;|&nbsp;
													</span>
													<span v-if="itemProduct.likes">
														{{ $t('AroundComponent.luot_thich', {
															count: itemProduct.likes ??
																0
														}) }}
													</span>

												</span>
												<div class="h-stack price-add-to-cart"
													v-on:click.stop="(e) => { e.preventDefault() }">
													<span class="price">
														<em class="off"
															v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
															{{
																(parseFloat(itemProduct.price) == 0 || itemProduct.price ==
																	null)
																	? $t('ShopComponent.gia_lien_he')
																	: formatCurrency(parseFloat(itemProduct.price),
																		itemProduct.shop
																			?
																			itemProduct.shop.currency
																			: props.shop_data?.currency)
															}}
														</em>
														{{
															(itemProduct.price_off != null && itemProduct.price_off <
																itemProduct.price) ?
																formatCurrency(parseFloat(itemProduct.price_off),
																	itemProduct.shop ? itemProduct.shop.currency :
																	props.shop_data?.currency) : (parseFloat(itemProduct.price) == 0 ||
																			itemProduct.price == null) ? $t('ShopComponent.gia_lien_he') :
																	formatCurrency(parseFloat(itemProduct.price),
																		itemProduct.shop ? itemProduct.shop.currency :
																		props.shop_data?.currency) }} </span>
															<button class="add-to-cart">
																<Icon name="solar:cart-plus-linear"></Icon>
															</button>
												</div>
											</div>
										</nuxt-link>

									</div>
								</div>


							</div>
						</div>
						<!-- <v-overlay v-model="loadMore" :z-index="100" :absolute="false" contained
							content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
							<Icon name="eos-icons:loading"></Icon>
						</v-overlay> -->
					</div>

					<div class="shop-footer">
						<nuxt-link :to="'#'"
							:title="$t('ShopComponent.goi_dien_cho_shop')" class="call-to-shop">
							<Icon name="solar:phone-calling-bold"></Icon>
							<span>{{ $t('ShopComponent.goi_dien_cho_shop') }}</span>
						</nuxt-link>
						<button class="chat-to-shop">
							<Icon name="solar:chat-round-dots-bold"></Icon>
							<span>{{ $t('ShopComponent.chat_voi_shop') }}</span>
						</button>
					</div>
				</div>

			</div>
			<div id='last_of_list'></div>
			<!-- <client-only>
				<button ref="chat_to_shop_drag_button" class="chat-to-shop-button"
					:style="{ top: `${position.y}px`, left: `${position.x}px` }">
					<Icon name="solar:chat-round-dots-bold"></Icon>
				</button>
			</client-only> -->

		</div>
	</div>

</template>

<style lang="scss" src="./ShopTempDefaultOverviewStyles.scss"></style>
