<template>
  <div class="public-container" :class="{ 'order-mode-container': props.mode == 'order' || props.mode == 'select' }">
    <div class="saved-address-container">
      <!-- <div class='title-header'>
      <div class="header-left">
        <button class="back-button" v-on:click="() => {
          router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
        }">
          <Icon name="lucide:chevron-left" />
        </button>
      </div>
      <h3>{{ appRouteTitle.SavedAddressComponent }}</h3>
      <div class="header-right"></div>
    </div> -->
      <SubHeaderV2Component :title="$t('AppRouteTitle.SavedAddressComponent')">
        <template v-slot:header_right v-if="props.mode == 'order' || props.mode == 'select'">
          <button v-on:click="() => {
            console.log(isUpdated, userInfoTemp);
            // return
            if(!isUpdated){
              emit('close');
            }
            else{
              emit('selectedUser', JSON.parse(JSON.stringify(userInfoTemp)))
            }
          }">
            <Icon name="iconamoon:sign-times-duotone" />
          </button>
        </template>
        <template v-slot:header_left v-if="props.mode == 'order' || props.mode == 'select'"></template>
      </SubHeaderV2Component>

      <div class="saved-user-list" v-if="dataSavedUsers && dataSavedUsers.length" v-show="isRefreshing == false">

        <div v-for="(itemUser, index) in dataSavedUsers" class="item-saved-user" :key="itemUser.id + '_' + index">
          <div class="saved-user-info" v-on:click="() => {
            if (props.mode == 'order' || props.mode == 'select') {
              emit('selectedUser', JSON.parse(JSON.stringify(itemUser)))
            }
            else {
              selectedIndex = index;
              userInfoTemp = JSON.parse(JSON.stringify(itemUser));
              firstLoad = true
              showModalInfo = true;
            }

          }">
            <div class="name-phone">
              <span class="name">
                {{ itemUser.name }}
              </span>
              <span class="phone">
                {{ itemUser.phone }}
              </span>
            </div>
            <div class="address">
              <span class="address-type" v-show="itemUser.title?.length" :class="{
                'home': itemUser.title == t('SavedAddressComponent.nha_rieng'),
                'work': itemUser.title == t('SavedAddressComponent.cong_ty'),
                'others': itemUser.title != t('SavedAddressComponent.nha_rieng') && itemUser.title != $t('SavedAddressComponent.cong_ty'),
              }">{{ itemUser.title }}</span>
              {{ itemUser.address }}
            </div>
            <div class="h-stack">
              <span class="default" v-if="itemUser.is_default == true">
                <Icon name="mingcute:flag-3-fill"></Icon>
                {{ $t('SavedAddressComponent.mac_dinh') }}
              </span>
              <span class="selected" v-if="
                (props.mode == 'order' || props.mode == 'select')
                && props.selectedObject?.name == itemUser.name
                && props.selectedObject?.phone == itemUser.phone
                && props.selectedObject?.address == itemUser.address
                && props.selectedObject?.latitude == itemUser.latitude
                && props.selectedObject?.longitude == itemUser.longitude
                && deepCompareJsons({
                  location: {
                    note: props.selectedObject?.note,
                    images: props.selectedObject?.images,
                  }
                }, {
                  location: {
                    note: itemUser?.note,
                    images: itemUser?.images,
                  }
                })
              ">
                <Icon name="mingcute:flag-1-fill"></Icon>
                {{ $t('SavedAddressComponent.dang_chon') }}

              </span>
            </div>

          </div>
          <div class="saved-user-actions">
            <v-menu class="bootstrap-dropdown-container" location="bottom right">
              <template v-slot:activator="{ props }">
                <button dark v-bind="props">
                  <Icon name="ic:outline-more-vert"></Icon>
                </button>
              </template>

              <v-list class="saved-user-actions-list">
                <v-list-item key="un_set_default" v-if="itemUser.is_default" class="saved-user-dropdown-item"
                  v-on:click="() => {
                    unsetDefault()
                  }">
                  <v-list-item-title>{{ $t('SavedAddressComponent.huy_dat_mac_dinh') }}</v-list-item-title>
                </v-list-item>
                <v-list-item key="set_default" v-else class="saved-user-dropdown-item" v-on:click="() => {
                  setDefault(index)
                }">
                  <v-list-item-title>{{ $t('SavedAddressComponent.dat_mac_dinh') }}</v-list-item-title>
                </v-list-item>

                <v-list-item key="edit" class="saved-user-dropdown-item" v-on:click="() => {
                  selectedIndex = index;
                  userInfoTemp = JSON.parse(JSON.stringify(itemUser));
                  firstLoad = true
                  showModalInfo = true;
                }">
                  <v-list-item-title>{{ $t('SavedAddressComponent.chinh_sua') }}</v-list-item-title>
                </v-list-item>
                <v-list-item key="delete" class="saved-user-dropdown-item" v-on:click="() => {
                  deleteSavedInfo(index);
                }">
                  <v-list-item-title>{{ $t('SavedAddressComponent.xoa') }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>

          </div>
        </div>

      </div>
      
      <div class="none-user-list" v-if="!(dataSavedUsers?.length)" v-show="isRefreshing == false">
        <img loading="lazy" :src="none_save_user" :placeholder="none_save_user" alt="" />
        <span>
          {{ $t('SavedAddressComponent.chua_co_dia_chi_nao_duoc_luu') }}
        </span>
      </div>

      <div class="footer" v-show="isRefreshing == false">
        <button v-on:click="() => {
          userInfoTemp = {
            id: null,
            name: '',
            address: '',
            phone: '',
            nameErr: '',
            phoneErr: '',
            addressErr: '',
            note: null,
            latitude: '',
            longitude: '',
            title: t('SavedAddressComponent.nha_rieng'),
            titleErr: '',
            province_id: null,
            district_id: null,
            ward_id: null,
            is_default: false,
            images: []
          }
          selectedIndex = null;
          firstLoad = false;
          showModalInfo = true;
        }">{{ $t('SavedAddressComponent.them_dia_chi_moi') }}</button>
        <div class="use-temporary-address" v-if="props.mode == 'order'">
          <button v-on:click="() => {
            emit('enterTempAddres')
          }">
            {{ $t('SavedAddressComponent.chon_tu_ban_do') }}
          </button>

        </div>
      </div>

      <v-overlay v-model="isRefreshing" :z-index="100" :absolute="false" contained content-class='spinner-container'
        persistent scrim="#fff" key="loading" no-click-animation>
        <Icon name="eos-icons:loading"></Icon>
      </v-overlay>

      <AddOrUpdateSavedAddressComponent :selectedObject="JSON.parse(JSON.stringify(userInfoTemp))" v-if="showModalInfo" v-on:close="async ($event) => {
        console.log($event);
        if ($event) {
          await getMyListAddress();
          showModalInfo = false;
          selectedIndex = null;
          isSaving = false;
          isUpdated = true;
        }
        else {
          userInfoTemp = {
            id: null,
            name: '',
            address: '',
            phone: '',
            nameErr: '',
            phoneErr: '',
            addressErr: '',
            latitude: '',
            longitude: '',
            title: '',
            titleErr: '',
            province_id: null,
            district_id: null,
            ward_id: null,
            is_default: false,
            note: null,
            images: []
          }
          selectedIndex = null;
          showModalInfo = false;
        }
      }"></AddOrUpdateSavedAddressComponent>
    </div>
  </div>

</template>

<script lang="ts" setup>
import icon_for_product from "~/assets/image/icon-for-product.png";
import none_save_user from "~/assets/image_08_05_2024/none-save-user.jpg";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import marker_location_icon from "~/assets/image/marker-location.png";
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

import {
  appConst,
  appDataStartup,
  baseLogoUrl,
  deepCompareJsons,
  domain,
  domainImage,
  formatCurrency,
  formatNumber,
  validPhone,
  zaloConfig
} from "~/assets/AppConst";
import appRoute from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import non_avatar from "~/assets/image/non-avatar.jpg";
import { PublicService } from "~/services/publicService/publicService";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import type { CartDto } from "~/assets/appDTO";
import { VueFinalModal } from "vue-final-modal";
import { UserAddressService } from "~/services/userAddressService/userAddressService";
import { HttpStatusCode } from "axios";

const router = useRouter();
const route = useRoute();
const nuxtApp = useNuxtApp();

const { t } = useI18n()
var props = defineProps({
  mode: null,
  selectedObject: null
})

const emit = defineEmits([
  'close', 'enterTempAddres', 'selectedUser'
])
useSeoMeta({
  title: t('AppRouteTitle.SavedAddressComponent')
});

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var userAddressService = new UserAddressService();
var imageService = new ImageService();
var publicService = new PublicService();

var dataSavedUsers = ref<any[]>([]);

var selectedIndex = ref<any>(null);
var showModalInfo = ref(false);
var firstLoad = ref(true)
var userInfoTemp = useState(() => {
  return {
    id: null,
    name: "",
    address: "",
    phone: "",
    nameErr: "",
    phoneErr: "",
    addressErr: "",
    note:null as any,
    latitude: null as any,
    longitude: null as any,
    province_id: null,
    district_id: null,
    ward_id: null,
    title: t('SavedAddressComponent.nha_rieng'),
    titleErr: "",
    is_default: false,
    images: []
  }
});

var leafletMap: L.Map;
var leafletMapAddInfo: L.Map;
var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('SavedAddressComponent.ve_tinh_nhan'));
var mapType = ref("roadmap");
var buttonMapTileBackgound = ref(map_sateline);

var webInApp = ref<any>(null);
var searchAddressTimeout: any;

var isSaving = ref(false);
var isRefreshing = ref<any>(null);


var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });
// watch(() => [user_latitude.value, user_longitude?.value], () => {
//   userInfoTemp.value.latitude = user_latitude?.value;
//   userInfoTemp.value.longitude = user_longitude?.value;
// });

var changeAddressOnMoving = ref(true);

var coordinatesText = ref("");
var isUpdated = ref(false)
watch(() => [userInfoTemp.value.latitude, userInfoTemp.value.longitude], () => {
  coordinatesText.value = `${userInfoTemp.value.latitude}, ${userInfoTemp.value.longitude}`
})
onUnmounted(() => {
  nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})
onBeforeMount(async () => {
  
  nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
    console.log('moving', coor);
    user_latitude.value = coor.latitude;
    user_longitude.value = coor.longitude;
  });

  
})

onMounted(async () => {
  // let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

  // dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];
  isRefreshing.value = true;
  let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
  webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
  await loadData();
  isRefreshing.value = false;
});
async function getProfileInfo() {

  return await userService.profileInfo();
}
const loadData = async () => {
  

  let authCheck = await authService.checkAuth();
  if (authCheck) {
    await getMyListAddress();
  }
  else {
    localStorage.removeItem(appConst.storageKey.token);
    localStorage.removeItem(appConst.storageKey.userInfo);
    sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
    // router.push(appRoute.LoginComponent);
    nuxtApp.$emit(appConst.event_key.require_login, {
      redirect_url: route.path,
      back_on_close: true
    })
    isRefreshing.value = false;
  }
}
var onRefresh = () => {
  loadData();
};
function getMyListAddress() {
  return new Promise((resolve)=>{
    userAddressService.myListAddress().then(res => {
    if (res.status == HttpStatusCode.Ok) {
      dataSavedUsers.value = JSON.parse(JSON.stringify(res?.body?.data));
    }
    isRefreshing.value = false;
    resolve(dataSavedUsers.value)
  }).catch(()=>{
    isRefreshing.value = false;
    resolve([])
  })
  })
  
}
function setDefault(index: number) {
  let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
    return e.is_default == true
  });

  if (index != indexDefault) {
    userAddressService.setDefault(dataSavedUsers.value[index].id).then(res => {
      if (res.status == HttpStatusCode.Ok) {
        if (indexDefault != -1) {
          dataSavedUsers.value[indexDefault].is_default = false;
        }

        dataSavedUsers.value[index].is_default = true;
      }
      else {
        toast.error(t('SavedAddressComponent.dat_mac_dinh_that_bai'))
      }
    });
    // localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(dataSavedUsers.value));
  }
}
function unsetDefault() {
  let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
    return e.is_default == true
  });
  dataSavedUsers.value[indexDefault].is_default = false;
  userAddressService.update(dataSavedUsers.value[indexDefault]).then(() => {
    getMyListAddress();
  })
  // localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(dataSavedUsers.value));
}
function deleteSavedInfo(index: number) {

  userAddressService.delete(dataSavedUsers.value[index].id).then(() => {
    getMyListAddress();
  })
  // localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(dataSavedUsers.value));
}

</script>

<style lang="scss" src="./SavedAddressStyles.scss"></style>
