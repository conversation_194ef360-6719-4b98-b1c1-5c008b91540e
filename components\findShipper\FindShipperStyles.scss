@media screen and (max-width: 500px) {
  .find-shipper-container {
    & .shipper-list-container {
      overflow: hidden !important;
    }

    & .shipper-list-content-container {
      padding-top: 35px !important;
    }

    & .shipper-item-container {
      max-width: 100% !important;
    }

    & .shipper-list-absolute {
      position: absolute;
      top: 0;
      left: 100%;

      & .close-expand-button {
        display: none !important;
      }

      & .expand-button {
        display: flex !important;
      }
    }

    & .shipper-list-absolute.expanded {
      left: unset;
      right: 0;
      width: 100% !important;
      height: 100% !important;
      max-width: unset;
      // padding: 10px !important;

      & .close-expand-button {
        display: flex !important;
      }

      & .expand-button {
        display: none !important;
      }
    }


  }
}

.find-shipper-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  .find-shipper-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    max-height: 100%;
    height: 95dvh;
    display: flex;
    flex-direction: column;

    &>.sub-title-v2-header {
      border-radius: inherit;
      text-transform: none;

      & h3{
        text-transform: none;
      }
    }

    &>.find-shipper-content-container {
      display: flex;
      width: 100%;
      height: 100%;
      padding: 10px;
      overflow: auto;

      &>.shipper-list-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px 1%;
        width: 100%;
        height: 100%;
        justify-content: center;
        position: relative;

        & .leaflet-map-container {
          position: relative;
          height: 100%;
          min-height: 500px;
          flex: 1;
          z-index: 1;

          & #leaflet_map_find_shipper {
            // flex: 1;
            // align-self: unsafe;
            // height: 100%;
            // // min-height: 450px;
            // outline: none;
            position: absolute;
            border-radius: 10px;
            width: 100%;
            height: 100%;
            z-index: 1;
            font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system,
              Tahoma, "Segoe UI";
          }
        }



        & .button-group {
          position: absolute;
          bottom: 50px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          z-index: 100;
          border-radius: 10px;
          box-shadow: 0 0 0 0 rgb(0, 0, 0, 30%);
          animation: l1 1.5s infinite;

          @keyframes l1 {
            100% {
              box-shadow: 0 0 0 10px;
            }
          }

          & .find-area {
            background: var(--primary-color-1);
            color: white;
            font-weight: 800;
            border-radius: 10px 0 0 10px;
            padding: 5px 15px;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
            height: 45px;
            white-space: nowrap;
          }

          & .radius-select {
            background: white;
            color: var(--primary-color-1);
            white-space: nowrap;
            padding: 5px;
            margin-top: 0 !important;
            border-radius: 0 10px 10px 0;
            height: 45px;
            align-items: center;
            width: max-content;

            & .v-field__input {
              justify-content: center;
            }

            & .v-input__control {
              padding: 0;
              min-height: 35px !important;

              & .v-field__field {
                align-items: center;

                & input {
                  align-self: center;
                  margin: auto;
                  text-align: center;
                  background: inherit;
                }
              }

              & .v-select__selection {
                align-self: center;
                font-weight: 700;
              }

              & .radius-label {
                font-size: 13px;
                color: #545454;
              }
            }
          }
        }


        &>.shipper-list-absolute {
          // position: absolute;
          // top: 0;
          // left: 0;
          // padding: 5px;
          // width: 150px;
          width: calc(40%);
          height: 100%;
          transition: all 0.3s ease-in-out;
          // filter: drop-shadow(0px 0px 7px rgb(0, 0, 0, 0.1));
          z-index: 2;

          & .shipper-list-content-container {
            border-radius: 10px;
            width: 100%;
            max-height: 100%;
            min-height: 100%;
            position: relative;
            overflow: auto;
            z-index: 10;
            padding: 0 10px;
            background: white;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;
            // flex-wrap: wrap;
            // gap: 5px;

            &>.shipper-item-container {
              display: flex;
              // flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              gap: 2px;
              // flex: 1;
              min-width: 100%;
              border-radius: 10px;
              background: #f5f6fa;
              font-size: 13px;
              cursor: pointer;
              transition: all 0.3s ease-in-out;
              height: fit-content;
              position: relative;
              width: 100%;
              max-width: 100%;
              margin-top: 10px;
              padding: 5px;

              & .shipper-avt {
                width: 40px;
                min-width: 40px;
                height: 40px;
                // margin-top: -60px;
                object-fit: cover;
                border-radius: 50%;
                box-shadow: 0 0 0px 2px white;
                transition: all 0.3s ease-in-out;
              }

              &>.shipper-info {
                display: flex;
                flex-direction: column;
                gap: 5px;
                flex: 1;
                min-width: 50%;
                align-items: flex-start;
                padding-left: 10px;

                & .name {
                  font-weight: bold;
                  color: #28262c;
                  justify-content: flex-start;
                  transition: all 0.3s ease-in-out;
                  white-space: normal;
                }

                & .online-status {
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  gap: 5px;
                  font-style: italic;
                  font-size: 13px;
                  color: #545454;
                  font-weight: 700;

                  &.online {
                    color: var(--primary-color-1);
                    font-weight: 700;
                  }

                  &.offline {
                    color: #b2b2b2;
                  }

                  & .online-dot {
                    width: 13px;
                    height: 13px;
                    min-width: 13px;
                    margin-right: 3px;
                    border-radius: 50%;
                    background: green;
                    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.5);
                    animation: l1 2s infinite;

                    @keyframes l1 {
                      100% {
                        box-shadow: 0 0 0 10px #0f00;
                      }
                    }
                  }

                  & .online-dot.off {
                    background: #818086;
                    animation: none;
                  }


                }

                & .info {
                  display: flex;
                  align-items: flex-start;
                  width: 100%;
                  white-space: nowrap;
                  font-size: 13px;
                  transition: all 0.3s ease-in-out;
                  gap: 10px;

                  &>.rating {
                    display: flex;
                    font-weight: 600;
                    color: #28262c;
                    justify-content: center;
                    align-items: center;
                    line-height: normal;
                    gap: 2px;
                    font-size: 14px;

                    &>svg {
                      color: #ffc107;
                      font-size: 20px;
                    }
                  }

                  &>.distance {
                    display: flex;
                    font-weight: 600;
                    color: #28262c;
                    justify-content: center;
                    align-items: center;
                    line-height: normal;
                    gap: 2px;
                    font-size: 14px;

                    &>svg {
                      font-size: 20px;
                      color: #f86f64;
                    }
                  }

                  &>.gender {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 2px;
                    font-weight: 600;
                    font-size: 14px;
  
                    &>.male {
                      color: #02aae8;
                      margin-bottom: 3px;
                    }
  
                    &>.female {
                      color: #ef5890;
                      margin-top: 3px;
                    }
  
                    &>.other {
                      color: #9c7ccf;
                    }
                  }
                }

                
              }

              &>.actions {
                display: flex;
                flex-direction: column;
                gap: 10px;
                width: auto;
                max-width: 50%;
                justify-content: space-between;
                padding: 10px 35px 0 10px;


                &>.phone {
                  color: #5f5e66;
                  background: white;
                  font-size: 15px;
                  height: 30px;
                  flex: 1;
                  padding: 2px 7px;
                  border-radius: 2em;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  gap: 5px;
                  font-weight: 600;
                  border: thin solid #ecebf3;
                  white-space: nowrap;

                  & > svg{
                    min-width: 15px;
                  }

                  &.disabled{
                    background: #f5f6fa;
                    user-select: none;
                    cursor: default;
                  }
                }

                &>.select-shipper {
                  color: white;
                  background: var(--primary-color-1);
                  font-size: 15px;
                  height: 30px;
                  padding: 2px 7px;
                  flex: 1;
                  border-radius: 2em;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  gap: 5px;
                  font-weight: 600;
                  white-space: nowrap;
                }

                &>.add-wishlist-button:disabled {
                  opacity: 1;
                }

                // &>.add-wishlist-button:not(:disabled):hover {
                //   background-color: rgb(0, 0, 0, 0.1);
                // }

                &>.add-wishlist-button {
                  position: absolute;
                  right: 5px;
                  top: 5px;
                  // background-color: rgb(0, 0, 0, 0.1);
                  border-radius: 50%;
                  // padding: 3px 10px;
                  width: 30px;
                  height: 30px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: #5f5e66;
                  font-size: 15px;
                  font-weight: 700;
                  background: var(--secondary-color-4);

                  & svg {
                    font-size: 22px;
                  }
                }

                &>.add-wishlist-button.active {
                  color: #ff002fe3;
                }
              }
            }

            &>.shipper-item-container.active,
            >.shipper-item-container:hover {
              filter: drop-shadow(0 0 5px rgb(0, 0, 0, 0.5));
            }
          }

          &>.shipper-list-content-container.shipper-none-list {
            display: flex;
            flex-direction: column !important;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
            gap: 10px;
            flex: 1;

            &>img {
              height: 150px;
              object-fit: contain;
            }

            &>span {
              color: var(--color-text-note);
              font-weight: 600;
              font-style: italic;
            }
          }

          &>.expand-button {
            display: none;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 50px;
            right: calc(100% - 6px);
            background: white;
            width: 50px;
            height: 50px;
            font-size: 32px;
            border-radius: 10px 0 0 10px;
            z-index: 10;
            color: var(--primary-color-1);
            border: 2px solid var(--primary-color-1);
            box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
            animation: l1 1.5s infinite;
          }

          &>.close-expand-button {
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ddd;
            width: 40px;
            height: 40px;
            font-size: 40px;
            border-radius: 50%;
            z-index: 100;
            color: var(--primary-color-1);
            display: none;
          }
        }

        &>.shipper-list-absolute.expanded {
          // width: calc(100% - 50px);
          width: calc(40%);

          // & .shipper-item-container {
          //   min-width: 100%;
          //   width: 100%;
          //   max-width: 100%;
          //   margin-top: 10px;
          //   padding: 5px;
          // }

          &>.shipper-list-content-container.shipper-none-list {
            &>img {
              max-width: 400px;
              max-height: 400px;
            }

            &>span {
              max-width: 400px;
            }
          }
        }
      }

      &>.shipper-none-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
}