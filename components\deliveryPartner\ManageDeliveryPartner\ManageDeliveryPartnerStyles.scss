.manage-delivery-partner-container{
  display: flex;
  flex-direction: column;
  flex: 1;
  // background-color: white;
  position: relative;
  padding: 0 0 50px 0 !important;
  overflow: auto;
  // background-image: url('~/assets/image_03_04_2024/profie-background.jpg');
  background-size: 100%;
  font-size: 15px;
  background-color: var(--color-background-2);
  & .header-middle{
    flex: calc(100% - 100px);
  }
  & .header-right{
    flex: 1;
  }
  & .delivery-partner-manage-header{
    display: flex;
    flex-direction: column;
    gap: 0;
    font-weight: 700;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 5px;
    flex: 1;
    max-width: 100%;
    overflow: hidden;
    line-height: normal;

    & > h3{
      font-size: 17px;
    }

    & > span{
      font-size: 15px;
      color: #92ff63;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
  }

  & > .manage-delivery-partner-content{
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    gap: 10px;

    & > .delivery-partner-item{
      // flex: 1;
      flex-basis: calc(50% - 5px);
      padding: 10px 20px;
      border-radius: 10px;
      border: thin solid transparent;
      background: #e4e4e4;
      display: flex;
      flex-direction: column;
      min-height: 100px;
      box-shadow: 2px 2px 4px rgb(0,0,0,.2);

      @media screen and (max-width: 720px) {
        flex-basis: 100%;
      }
      &.connected{
        box-shadow: none;
        background: white;
        border: thin solid var(--primary-color-1);
        & .connection-tag{
          color: var(--primary-color-1);
        }
      }
      &.disconnected{

        & .parter-logo{
          filter: grayscale(1);
        }
        & .connection-tag{
          color: var(--primary-color-2);
        }
      }

      &.not-connected{
        & .parter-logo{
          filter: grayscale(1);
        }
        
        & .connection-tag{
          color: var(--primary-color-2);
        }
      }

      & > .name-connection-info{
        display: flex;
        gap: 5px;
        justify-content: flex-start;
        align-items: center;

        & > .parter-logo{
          height: 25px;
          width: auto;
          object-fit: contain;
        }
        & > .partner-name{
          font-size: 20px;
          font-weight: 700;
          color: var(--primary-color-1);
        }

        & > .connection-tag{
          padding: 0px 10px;
          border: thin solid;
          border-radius: 2em;
          font-size: 11px;
          display: flex;
          align-items: center;
          justify-content: center;
          align-self: flex-start;
          line-height: normal;
          margin-left: 5px;
        }
        & > .default-flag{
          padding: 0px 10px;
          border: thin solid;
          border-radius: 2em;
          font-size: 11px;
          display: flex;
          align-items: center;
          justify-content: center;
          align-self: flex-start;
          line-height: normal;
          margin-left: 5px;
          color: #0099e0;
        }
        & > .setting-button{
          margin-left: auto;
          font-size: 25px;
          color: #545454;
          display: flex;
          align-self: flex-start;
          transition: all .3s ease;
          animation: none;

          &:hover{
            transform: rotate(45deg);
          }
        }
      }

      & > .secondary-info{
        color: #545454;
        margin: 10px 0;
      }

      & > .actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: auto;

        & > .show-info{
          margin-right: auto;
          justify-self: flex-start;
          font-size: 13px;
          color: var(--primary-color-1);
        }

        & > .connect-button{
          padding: 3px 10px;
          border-radius: 3px;
          color: white;
          font-weight: 600;
          background: var(--primary-color-1);
        }

        & > .disconnect-button{
          padding: 3px 10px;
          border-radius: 3px;
          background-color: transparent;
          font-weight: 600;
          border: thin solid;
          color: var(--primary-color-2);
        }
      }
    }
  }
}
.settings-button{

  & .disconnect-button{
    color: var(--primary-color-2)
  }
}