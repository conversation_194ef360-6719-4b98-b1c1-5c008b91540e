.share-link-to-supplier-container {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  max-height: 90%;
  max-width: 95% !important;
  padding: 0;
  // min-height: 40dvh;
  border-radius: 10px;
  background-color: white;
  overflow: auto;

  &>.title-header {
    background: white;
  }

  &>.share-link-to-supplier-content {
    display: flex;
    flex-direction: column;
    padding: 0 15px 15px;

    & .share-link-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 5px;
      padding: 5px 10px;
      border-radius: 10px;
      background: var(--secondary-color-3);

      &>.supplier-name {
        color: rgb(0, 179, 255);
        height: 25px;
        align-self: flex-start;
      }

      &>.link-copy {
        display: flex;
        gap: 5px;
        align-items: flex-start;

        &>span {
          font-size: 13px;
          color: #545454;
          font-weight: 600;
        }

        &>button {
          text-transform: none;
          color: var(--primary-color-1);
          height: 30px;
        }
      }
    }
  }
}