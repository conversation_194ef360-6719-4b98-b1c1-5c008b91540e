.title-v2-header {
  // font-size: 1.6em;
  // padding: 10px 15px;
  margin: 0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: -100%;
  z-index: 1000;
  background: white;
  width: 100%;
  max-width: var(--max-width-view);
  height: 55px;
  max-height: 55px;
  border-bottom: 0;
  transition: all .3s cubic-bezier(0.075, 0.82, 0.165, 1);

  @media screen and (min-width: 1025px) {
    display: none;
  }

  & h3 {
    margin: 0;
  }

  & .header-left {
    display: flex;
    justify-content: left;
    gap: 5px;
    height: 100%;
    max-height: inherit;
    width: fit-content;
    overflow: hidden;
    flex: 1;
    padding: 10px 0 10px 15px;
    // margin-right: auto;

    &>a {
      overflow: hidden;
      display: flex;
      white-space: nowrap;
    }

    & img {
      height: 100%;
      object-fit: contain;
    }
  }

  & .header-right {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    padding-right: 5px;
    height: 100%;
    width: fit-content;
    max-height: inherit;
    overflow: hidden;
    padding: 10px 15px 10px 0;

    &>a {
      overflow: hidden;
      display: flex;
      justify-content: flex-end;

      white-space: nowrap;
    }

    & .open-shop-button {
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
      color: var(--primary-color-1);
      gap: 5px;

      &>.label-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: right;
        font-size: 15px;
        line-height: 15px;

        &>.primary {
          text-transform: uppercase;
          font-weight: 700;
          line-height: 15px;
        }

        &>.secondary {
          color: #545454;
          font-weight: 700;
          font-size: 13px;
        }
      }

      &>svg {
        height: 100%;
        min-width: 40px;
      }
    }

    & .notification {
      width: 35px;
      min-width: 35px;
      color: var(--primary-color-1);
      height: 35px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      position: relative;

      &>.notify-badge {
        color: var(--primary-color-1);
        font-size: 30px;
        font-weight: 700;

        & .v-badge__badge {
          font-weight: 700;
          color: white;
        }
      }

      &>svg {
        color: var(--primary-color-1);
        font-size: 30px;
      }

      &>.count {
        position: absolute;
        top: 3px;
        right: -2px;
        padding: 0 5px;
        border-radius: 2em;
        background-color: #cc313a;
        color: white;
        font-size: 12px;
      }
    }
  }
}

.title-v2-header.sticky {
  // position: sticky;
  top: 0;
}