<template>
    <div class="public-container" v-on:scroll="(e: any) => { listScroll(e); }" id="around_public_container">

        <div class="around-container">
            <div class="around-sticky-header h-fit" ref="sticky_header">
                <SubHeaderV2Component :class="{
                    'searching': filterData.search_text?.length && !searchFocus
                }">
                    <template v-slot:header_left v-if="searchFocus">
                        <button class="back-close-search-button" v-on:click="() => {
                            searchFocus = false
                        }">
                            <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                        </button>
                    </template>
                    <template v-slot:header_middle>
                        <h3 v-if="!filterData?.search_text?.length || searchFocus">{{
                            $t('AppRouteTitle.AroundComponent')
                        }}</h3>
                        <div v-else class="h-stack searching-container" v-on:click="() => {
                            focusSearch()
                        }">
                            <div class="searching-text">
                                {{ filterData.search_text }}
                            </div>
                        </div>
                    </template>
                    <template v-slot:header_right v-if="!searchFocus">
                        <button class="search-button" v-if="!refreshing" v-on:click="() => {
                            focusSearch()
                        }">
                            <Icon name="solar:rounded-magnifer-outline"></Icon>
                        </button>
                        <button class="search-button" v-else>
                            <Icon name="eos-icons:loading"></Icon>
                        </button>
                    </template>
                </SubHeaderV2Component>
                <div class="categories-header">
                    <Swiper class="my-carousel categories-carousel" :modules="[SwiperFreeMode]" :navigation="false"
                        :freeMode="true" :slides-per-view="'auto'" :slides-per-group-auto="true" :loop="false"
                        :spaceBetween="15" :effect="'creative'" :autoplay="false" key="category-carousel" @init="(e: any) => {
                            // categoryCarousel = e;
                            if (filterData.business_type_id) {
                                let index = dataBusinessType.findIndex(function (e: any) {
                                    return e.id == filterData.business_type_id
                                });
                                e.slideTo(index + 1)
                            }
                        }">
                        <SwiperSlide class="category-item-slide" v-on:click="async () => {
                            // filterCategory = itemCategory.id;
                            setBusinessTypeFilter(null);
                        }" :class="!filterData.business_type_id ? 'active' : ''" :value="'all'" :key="'all'"
                            :id="'tab_all'">
                            <div class="tab-title">
                                <span class='name'>
                                    {{ $t('AroundComponent.tat_ca') }}
                                </span>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide class="category-item-slide"
                            v-for="(itemBusinessType, indexTab) of dataBusinessType" v-on:click="async () => {
                                // filterCategory = itemBusinessType.id;
                                // setCategoryFilter(itemBusinessType.id);
                                setBusinessTypeFilter(itemBusinessType.id);
                            }" :class="filterData.business_type_id == itemBusinessType.id ? 'active' : ''"
                            :value="itemBusinessType.id" :key="itemBusinessType.id" :id="'tab_' + itemBusinessType.id">
                            <div class="tab-title">
                                <span class='name'>
                                    {{ itemBusinessType.name }}
                                </span>
                            </div>
                        </SwiperSlide>
                    </Swiper>
                </div>
                <div class="filter-options-header">
                    <button class="short-filter-button" v-on:click="() => {
                        showFilterSortSelect = true;
                    }">
                        <Icon name="solar:filter-linear"></Icon>
                    </button>
                    <Swiper class="my-carousel filter-options-carousel" :modules="[SwiperFreeMode]" :navigation="false"
                        :freeMode="true" :slides-per-view="'auto'" :slides-per-group-auto="true" :loop="false"
                        :spaceBetween="5" :effect="'creative'" :autoplay="false" key="filter-options-carousel" @init="(e: any) => {
                            // categoryCarousel = e;
                            filterCarousel = e
                        }">
                        <SwiperSlide class="filter-option-slide" v-on:click="async () => {
                            // filterData.is_sale_off = !filterData.is_sale_off;
                            // filter()
                            // filterData.sortBy = filter_sort.gan_nhat;
                            filter();
                        }" :class="'active'" :value="'sort'" :key="'sort'" :id="'tab_sort'">
                            <div class="tab-title">
                                <span class='name'>
                                    {{$t(`AroundComponent.${Object.keys(filter_sort).filter(e => {
                                        return filter_sort[e] == filterData.sortBy
                                    })}`)}}
                                </span>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide class="filter-option-slide" v-on:click="async () => {
                            // filterCategory = itemCategory.id;
                            // setFilterFlashSale(null);
                            filterData.is_suggest = !filterData.is_suggest;
                            filter();
                        }" :class="filterData.is_suggest ? 'active' : ''" :value="'hang_tuyen'" :key="'hang_tuyen'"
                            :id="'tab_hang_tuyen'">
                            <div class="tab-title">
                                <span class='name'>
                                    {{ $t('AroundComponent.hang_tuyen') }}
                                </span>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide class="filter-option-slide" v-on:click="async () => {
                            // filterCategory = itemCategory.id;
                            // setFilterFlashSale(null);
                            filterData.is_sale_off = !filterData.is_sale_off;
                            filter();
                        }" :class="filterData.is_sale_off ? 'active' : ''" :value="'flash_sale'" :key="'flash_sale'"
                            :id="'tab_flash_sale'">
                            <div class="tab-title">
                                <!-- <Icon name="streamline:flash-1-solid"></Icon> -->
                                <Icon name="mingcute:sale-fill"></Icon>
                                <span class='name'>
                                    {{ $t('AroundComponent.giam_gia') }}
                                </span>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide class="filter-option-slide" v-if="false" v-on:click="async () => {
                            // filterCategory = itemCategory.id;
                            // setFilterFlashSale(null);
                            showCategorySelect = true;
                        }" :class="filterData.category_ids?.length > 0 ? 'active' : ''" :value="'category'"
                            :key="'category'" :id="'tab_category'">
                            <div class="tab-title">
                                <span class='name'>
                                    {{ $t('AroundComponent.danh_muc') }} <em v-if="filterData.category_ids?.length">({{
                                        filterData.category_ids?.length }})</em>
                                </span>
                            </div>
                        </SwiperSlide>

                    </Swiper>
                </div>
            </div>
            <div class="around-content-container" :class="{ 'show-search': searchFocus }">


                <div class="filter-result-container" :class="{ 'show-map': !isShowList }" id="map_container">

                    <client-only>
                        <div class="leaflet-map-absolute" :style="{
                            maxHeight: `calc(100dvh - ${sticky_header?.getBoundingClientRect().height}px)`
                        }">
                            <div class="leaflet-map-relative">
                                <LMap id="leaflet_map" :draggable="!showSelectedShop" v-on:ready="(e: any) => {
                                    console.log('map ready')
                                    leafletMap = e;
                                    leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
                                    initLeafletMap();
                                }" v-on:update:bounds="(bounds: any) => {
                                    mapBoundChanges(
                                        bounds.getSouth(),
                                        bounds.getWest(),
                                        bounds.getNorth(),
                                        bounds.getEast()
                                    );
                                }" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }"
                                    :world-copy-jump="true" :use-global-leaflet="true">
                                    <!-- <LControlZoom position="bottomright"></LControlZoom> -->
                                    <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                        :use-global-leaflet="true" :max-zoom="appConst.leafletMapTileOption.maxZoom"
                                        :min-zoom="5" layer-type="base" :name="$t('AroundComponent.google_map')" />

                                    <LControl position="bottomright">
                                        <div class="custom-contol-bottom">
                                            <span class="current-location-v2-leaflet"
                                                :title="$t('AroundComponent.vi_tri_cua_ban')" v-on:click="(e: any) => {
                                                    gotoCurrentLocationLeaflet();
                                                }">
                                                <Icon name="line-md:my-location-loop" class="my-location-icon" />
                                            </span>
                                            <span class="show-list-button"
                                                :title="$t('AroundComponent.hien_thi_danh_sach')" v-on:click="(e: any) => {
                                                    showList();
                                                }">
                                                <Icon name="solar:server-minimalistic-bold" class="my-location-icon" />
                                            </span>
                                        </div>

                                    </LControl>
                                </LMap>
                                <button class="filter-current-center" v-on:click="() => {
                                    handelFilterInArea()
                                }">
                                    {{ $t('AroundComponent.tim_kiem_khu_vuc_nay') }}
                                    <Icon v-if="refreshing" name="eos-icons:loading" size="20"></Icon>
                                    
                                </button>
                                <div class="filter-product-on-map-carousel" v-show="filterResultMap?.length > 0">
                                    <Swiper class="my-carousel stack-carousel" :lazy="true" :centeredSlides="true"
                                        :lazyPreloadPrevNext="1" :centeredSlidesBounds="false"
                                        :modules="[SwiperAutoplay, SwiperFreeMode, SwiperVirtual]" :virtual="{
                                            addSlidesBefore: 0,
                                            addSlidesAfter: 0
                                        }" :slides-per-view="1.1" :loop="false" :space-between="10"
                                        :effect="'creative'" :freeMode=false
                                        :initialSlide="indexFilterCarouselShowing ?? 0" :autoplay="false"
                                        key="filter-map-carousel" @init="(e: any) => {
                                            filterMapCarousel = e;
                                            // showingSlideFilter()
                                        }" v-on:slide-change="(e: any) => {
                                            unsetClassShowCenterMarker()
                                            indexFilterCarouselShowing = e.activeIndex;
                                            mapFlyTo(filterResultMap[indexFilterCarouselShowing]);
                                            setClassShowCenterMarker();
                                        }" @slidesUpdated="() => {
                                            if (filterResultMap?.length && filterMapCarousel) {
                                                filterMapCarousel.slideTo(0)
                                            }
                                        }">
                                        <SwiperSlide class="item-stack-slide shop-carousel-item-slide"
                                            v-for="(item, indexItem) of filterResultMap" :virtual-index="indexItem"
                                            :key="`filter_map_carousel_${item.id}_${indexItem}`">
                                            <div class="filter-detail-slide"
                                                v-if="indexItem >= indexFilterCarouselShowing - 1 && indexItem <= indexFilterCarouselShowing + 1"
                                                v-on:click="() => {
                                                    // selectedShop = JSON.parse(JSON.stringify(item));
                                                    showProductsInShopSlide = !showProductsInShopSlide;
                                                    // showSelectedShop = true;
                                                    filterMapCarousel.slideTo(indexItem)
                                                }">
                                                <div class="close-bar" v-show="indexFilterCarouselShowing == indexItem">
                                                    <Icon name="line-md:chevron-down" v-if="showProductsInShopSlide">
                                                    </Icon>
                                                    <Icon name="line-md:chevron-up" v-else></Icon>
                                                </div>

                                                <div class="shop-item-detail">
                                                    <AvatarComponent class="shop-item-logo" :imgTitle="item.name"
                                                        :imgStyle="item.logo?.style" :imgSrc="item.logo
                                                            ? (domainImage + item.logo.path)
                                                            : item.banner
                                                                ? (domainImage + item.banner.path)
                                                                : ''
                                                            " :width="50" :height="50" v-on:img_click="() => {
                                                                router.push(appRoute.DetailShopComponent + '/' + (item.slug ? item.slug : item.id))
                                                            }" v-on:click="($e: Event) => { $e.stopPropagation() }" />
                                                    <div class="shop-item-info">
                                                        <div class="shop-item-name">
                                                            <nuxt-link :title="item.name" :prefetch="false"
                                                                :to="appRoute.DetailShopComponent + '/' + (item.slug ? item.slug : item.id)"
                                                                v-on:click="(($e: Event) => { $e.stopPropagation() })">{{
                                                                    item.name
                                                                }}</nuxt-link>
                                                        </div>
                                                        <span :title="item.address" class="shop-item-address">{{
                                                            item.address
                                                            }}</span>

                                                        <div class="shop-item-business-rating">
                                                            <span class="business-name" v-if="item.business_types?.id">
                                                                {{ item.business_types?.name }}
                                                            </span>
                                                            <div class="rating" v-if="item.ratings">
                                                                <Icon name="solar:star-bold"></Icon> {{
                                                                    item.ratings ?? 0
                                                                }}<span>/{{
                                                                    appConst.defaultMaxRate }}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="products"
                                                    :class="{ 'show': showProductsInShopSlide && indexFilterCarouselShowing == indexItem }"
                                                    v-if="item.products && item.products.length && indexItem >= indexFilterCarouselShowing - 1 && indexItem <= indexFilterCarouselShowing + 1"
                                                    v-on:click="($e) => { $e.stopPropagation() }">
                                                    <Swiper class="stack-carousel products-slide-inside-shop-carousel"
                                                        :nested="true"
                                                        :modules="[SwiperAutoplay, SwiperFreeMode, SwiperPagination, SwiperVirtual]"
                                                        :freeMode="false" :pagination="{
                                                            horizontalClass: 'selected-shop-products-carousel-pagination',
                                                        }" :slides-per-view="'auto'" :loop="false" :effect="'creative'"
                                                        :navigation="false" :spaceBetween="10" :autoplay="false"
                                                        :key="`product_in_shop_carousel_${indexItem}`">
                                                        <SwiperSlide class="item-stack-slide"
                                                            v-for="(itemProduct, indexProduct) of item.products"
                                                            :virtual-index="indexProduct" :key="'suggest_' + item.id">
                                                            <nuxt-link :prefetch="false"
                                                                :to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
                                                                class="item-stack" v-on:click="($event: Event) => {
                                                                    $event.stopPropagation()
                                                                }">

                                                                <img loading="lazy"
                                                                    :src="itemProduct?.profile_picture?.length ? (domainImage + itemProduct?.profile_picture) : icon_for_product"
                                                                    :placeholder="icon_for_product"
                                                                    :alt="showTranslateProductName(itemProduct)"
                                                                    :title="showTranslateProductName(itemProduct)" />
                                                                <div class="item-stack-content">
                                                                    <nuxt-link :prefetch="false"
                                                                        :to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
                                                                        :title="showTranslateProductName(itemProduct)">
                                                                        <span class="name">{{
                                                                            showTranslateProductName(itemProduct)
                                                                            }}</span>
                                                                    </nuxt-link>

                                                                    <span class="sold-like-amount">
                                                                        <span v-if="itemProduct.sold_count">
                                                                            {{ $t('AroundComponent.luot_ban', {
                                                                                count: itemProduct.sold_count ??
                                                                                    0
                                                                            }) }}
                                                                        </span>
                                                                        <span
                                                                            v-if="itemProduct.sold_count && itemProduct.likes">
                                                                            &nbsp;|&nbsp;
                                                                        </span>
                                                                        <span v-if="itemProduct.likes">
                                                                            {{ $t('AroundComponent.luot_thich', {
                                                                                count: itemProduct.likes ??
                                                                                    0
                                                                            }) }}
                                                                        </span>

                                                                    </span>
                                                                    <div class="price-actions">
                                                                        <div class="price-container">
                                                                            <em class="origin-price" :class="{
                                                                                'hide': !(itemProduct.price_off != null && parseFloat(itemProduct.price_off) < parseFloat(itemProduct.price))
                                                                            }">
                                                                                {{
                                                                                    (parseFloat(itemProduct.price) == 0 ||
                                                                                        parseFloat(itemProduct.price)
                                                                                        == null)
                                                                                        ? $t('AroundComponent.gia_lien_he')
                                                                                        :
                                                                                        formatCurrency(parseFloat(itemProduct.price),
                                                                                            itemProduct.shop ?
                                                                                                itemProduct.shop.currency :
                                                                                                item.currency)
                                                                                }}
                                                                            </em>
                                                                            <span class="price">
                                                                                {{
                                                                                    (itemProduct.price_off != null &&
                                                                                        itemProduct.price_off <
                                                                                        itemProduct.price) ?
                                                                                        formatCurrency(parseFloat(itemProduct.price_off),
                                                                                            itemProduct.shop ?
                                                                                                itemProduct.shop.currency :
                                                                                                item.currency) :
                                                                                        (parseFloat(itemProduct.price) == 0 ||
                                                                                            itemProduct.price == null) ?
                                                                                            $t('AroundComponent.gia_lien_he') :
                                                                                            formatCurrency(parseFloat(itemProduct.price),
                                                                                                itemProduct.shop ?
                                                                                                    itemProduct.shop.currency :
                                                                                                    item.currency) }} </span>

                                                                        </div>
                                                                        <button class="add-to-cart" v-on:click="async () => {
                                                                            selectedProduct = JSON.parse(JSON.stringify(itemProduct));
                                                                            selectedProduct.shop = JSON.parse(JSON.stringify(item));
                                                                            showSelectedProduct = true;
                                                                        }"
                                                                            v-on:click.stop="(e) => { e.preventDefault() }">
                                                                            <Icon name="solar:cart-plus-linear"></Icon>
                                                                        </button>
                                                                    </div>

                                                                </div>
                                                            </nuxt-link>
                                                        </SwiperSlide>
                                                    </Swiper>
                                                </div>
                                            </div>

                                        </SwiperSlide>
                                    </Swiper>
                                </div>
                            </div>
                        </div>

                        <v-overlay v-model="showSelectedShop" :z-index="1000" location="bottom" :width="'100%'"
                            content-class='shop-selected-container' persistent contained v-on:click:outside="() => {
                                showSelectedShop = false;
                            }">
                            <div class="shop-selected-content">
                                <div class="shop-selected-detail">
                                    <AvatarComponent class="shop-seleted-logo" :imgTitle="selectedShop.name"
                                        :imgStyle="selectedShop.logo?.style" :imgSrc="selectedShop.logo
                                            ? (domainImage + selectedShop.logo.path)
                                            : selectedShop.banner
                                                ? (domainImage + selectedShop.banner.path)
                                                : ''
                                            " :width="75" :height="75" v-on:img_click="() => {
                                                router.push(appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id))
                                            }" />
                                    <div class="shop-selected-info">
                                        <div class="shop-selected-name">
                                            <nuxt-link :title="selectedShop.name" :prefetch="false"
                                                :to="appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id)">{{
                                                    selectedShop.name }}</nuxt-link>
                                            <button class="close" v-on:click="(e) => {
                                                e.stopPropagation();
                                                showSelectedShop = false;
                                            }">
                                                <Icon name="solar:close-circle-linear" size="25"></Icon>
                                            </button>
                                        </div>
                                        <nuxt-link :title="selectedShop.address" :prefetch="false"
                                            :to="appRoute.DetailShopComponent + '/' + (selectedShop.slug ? selectedShop.slug : selectedShop.id)"
                                            class="shop-selected-address">{{ selectedShop.address }}</nuxt-link>

                                        <div class="shop-selected-business-rating">
                                            <span class="business-name" v-if="selectedShop.business_types?.id">
                                                {{ selectedShop.business_types?.name }}
                                            </span>
                                            <div class="rating" v-if="selectedShop.ratings">
                                                <Icon name="solar:star-bold"></Icon> {{
                                                    selectedShop.ratings ?? 0
                                                }}<span>/{{
                                                    appConst.defaultMaxRate }}</span>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="products" v-if="selectedShop.products && selectedShop.products.length">
                                    <Swiper class="my-carousel stack-carousel suggest-carousel"
                                        :modules="[SwiperAutoplay, SwiperFreeMode, SwiperPagination]" :freeMode="true"
                                        :pagination="{
                                            horizontalClass: 'selected-shop-products-carousel-pagination',
                                        }" :slides-per-view="'auto'" :loop="false" :effect="'creative'"
                                        :navigation="false" :spaceBetween="10" :autoplay="false" key="suggest-carousel">
                                        <SwiperSlide class="item-stack-slide" v-for="item of selectedShop.products"
                                            :key="'suggest_' + item.id">
                                            <nuxt-link :prefetch="false"
                                                :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                                class="item-stack">

                                                <img loading="lazy"
                                                    :src="item?.profile_picture?.length ? (domainImage + item?.profile_picture) : icon_for_product"
                                                    :placeholder="icon_for_product"
                                                    :alt="showTranslateProductName(item)"
                                                    :title="showTranslateProductName(item)" />
                                                <div class="item-stack-content">
                                                    <nuxt-link :prefetch="false"
                                                        :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
                                                        :title="showTranslateProductName(item)">
                                                        <span class="name">{{ showTranslateProductName(item)
                                                        }}</span>
                                                    </nuxt-link>
                                                    <!-- <span class="sold-like-amount" v-if="item.sold_count || item.likes">
                                                        {{ $t('ShopComponent.luot_ban', {
                                                            count: item.sold_count ??
                                                                0
                                                        }) }}
                                                        &nbsp;|&nbsp;
                                                        {{ $t('ShopComponent.luot_thich', {
                                                            count: item.likes ??
                                                                0
                                                        }) }}
                                                    </span> -->
                                                    <span class="sold-like-amount">
                                                        <span v-if="item.sold_count">
                                                            {{ $t('AroundComponent.luot_ban', {
                                                                count: item.sold_count ??
                                                                    0
                                                            }) }}
                                                        </span>
                                                        <span v-if="item.sold_count && item.likes">
                                                            &nbsp;|&nbsp;
                                                        </span>
                                                        <span v-if="item.likes">
                                                            {{ $t('AroundComponent.luot_thich', {
                                                                count: item.likes ??
                                                                    0
                                                            }) }}
                                                        </span>

                                                    </span>
                                                    <div class="price-actions">
                                                        <div class="price-container">
                                                            <em class="origin-price" :class="{
                                                                'hide': !(item.price_off != null && parseFloat(item.price_off) < parseFloat(item.price))
                                                            }">
                                                                {{
                                                                    (parseFloat(item.price) == 0 ||
                                                                        parseFloat(item.price)
                                                                        == null)
                                                                        ? $t('AroundComponent.gia_lien_he')
                                                                        : formatCurrency(parseFloat(item.price),
                                                                            item.shop ?
                                                                                item.shop.currency : selectedShop.currency)
                                                                }}
                                                            </em>
                                                            <span class="price">
                                                                {{
                                                                    (item.price_off != null && item.price_off < item.price)
                                                                        ? formatCurrency(parseFloat(item.price_off),
                                                                            item.shop ? item.shop.currency :
                                                                                selectedShop.currency) : (parseFloat(item.price) == 0
                                                                                    || item.price == null) ?
                                                                            $t('AroundComponent.gia_lien_he') :
                                                                            formatCurrency(parseFloat(item.price), item.shop ?
                                                                                item.shop.currency : selectedShop.currency) }}
                                                                    </span>

                                                        </div>
                                                        <button class="add-to-cart" v-on:click="async () => {
                                                            selectedProduct = JSON.parse(JSON.stringify(item));
                                                            selectedProduct.shop = JSON.parse(JSON.stringify(selectedShop));
                                                            showSelectedProduct = true;
                                                        }" v-on:click.stop="(e) => { e.preventDefault() }">
                                                            <Icon name="solar:cart-plus-linear"></Icon>
                                                        </button>
                                                    </div>

                                                </div>
                                            </nuxt-link>
                                        </SwiperSlide>
                                    </Swiper>
                                </div>
                            </div>
                        </v-overlay>
                    </client-only>

                    <div class="search-result" id="search-result" :class="{ 'show': searchFocus }" v-on:scroll="(e: any) => {
                        listSuggestScroll(e);
                    }">
                        <div class="search-input-container">
                            <div class="h-stack search-container">
                                <button class="search-button" :disabled="refreshing || searchProductLoading" v-on:click="() => {
                                    searchFocus = false;
                                    filterData = {
                                        ...filterData,
                                        search_text: filterData.search,
                                    };
                                    filter();
                                    blurSearch();
                                }">
                                    <Icon name="solar:rounded-magnifer-outline" size="25"
                                        v-show="!(refreshing || searchProductLoading)" />
                                    <Icon name="eos-icons:loading" size="25"
                                        v-show="refreshing || searchProductLoading" />
                                </button>

                                <input type="search" name="search-text" :autofocus="searchFocus"
                                    :maxlength="appConst.max_text_short" :placeholder="$t('AroundComponent.tim_kiem')"
                                    class="search-input-content" v-on:click="() => {
                                        searchFocus = true;
                                    }" :value="filterData.search" v-on:input="(e: any) => {
                                        filterData = {
                                            ...filterData,
                                            search: e.target.value
                                        };
                                        getSearchProductResult();
                                    }" autoComplete="off" v-on:keypress.enter="() => {
                                        searchFocus = false;
                                        filterData = {
                                            ...filterData,
                                            search_text: filterData.search,
                                        };
                                        filter();
                                        blurSearch();
                                    }" id="search_input" x-webkit-speech />
                                <button class="clear-button" v-show="filterData.search && filterData.search.length"
                                    v-on:click="() => {
                                        filterData = {
                                            ...filterData,
                                            search: ''
                                        }
                                        searchProductSuggest = [];
                                        focusSearch()
                                        // filter()
                                    }">
                                    <Icon name="iconamoon:sign-times" size="25" />
                                </button>

                            </div>

                            <span class="search-nearby-notice">
                                <Icon name="solar:plain-linear"></Icon>
                                {{ $t('AroundComponent.gan_toi') }}
                            </span>
                            <button class="search-submit" :disabled="refreshing" v-on:click="() => {
                                searchFocus = false;
                                filterData = {
                                    ...filterData,
                                    search_text: filterData.search,
                                };
                                filter();
                                blurSearch();
                            }">
                                <!-- <Icon name="eos-icons:loading" size="25" v-show="refreshing || searchProductLoading" /> -->
                                {{ $t('AroundComponent.kham_pha') }}
                            </button>
                        </div>
                        <div class="history-search" v-if="searchRecent?.length">
                            <span>{{ $t('HomeComponent.lich_su_tim_kiem') }}
                                <button v-on:click="() => {
                                    showClearHistorySearchModal = true
                                    // clearSearchHistory()
                                }">
                                    <Icon name="iconamoon:close-circle-1-light"></Icon>
                                </button>
                            </span>
                            <div class="list-history-search">
                                <button v-for="(itemSearch, indexTab) in searchRecent" v-on:click="() => {
                                    filterData = {
                                        ...filterData,
                                        search: itemSearch,
                                        search_text: itemSearch
                                    };
                                    filter();
                                    blurSearch();
                                }">
                                    {{ itemSearch }}
                                </button>
                            </div>
                        </div>
                        <div class="suggest-search" v-if="searchTextSuggest && searchTextSuggest.length">
                            <span>{{ $t('AroundComponent.tim_kiem_them') }}</span>

                            <div class="list-suggest-search">
                                <button v-for="(itemSearch, indexTab) in searchTextSuggest" v-on:click="() => {
                                    filterData = {
                                        ...filterData,
                                        search: itemSearch,
                                        search_text: itemSearch
                                    };
                                    filter();
                                    blurSearch();
                                }">
                                    {{ itemSearch }}
                                </button>
                            </div>
                        </div>
                        <div class="categories-and-list-suggest">
                            <div class="list-search-suggest-container">
                                <div class="list-search-suggest" v-if="shopRecent.length">
                                    <span>{{ $t('AroundComponent.cua_hang_da_xem') }}</span>
                                    <div class="search-placeholder" v-if="(!shopRecent || !shopRecent.length)">
                                        <img loading="lazy" :src="none_result" :placeholder="none_result" />
                                    </div>
                                    <nuxt-link :prefetch="false"
                                        :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)"
                                        v-if="shopRecent && shopRecent.length" v-for="itemRecent of shopRecent"
                                        class="recent-item-container" :title="showTranslateProductName(itemRecent)">
                                        <AvatarComponent class="shop-recent-logo" :imgTitle="itemRecent.name"
                                            :imgStyle="itemRecent.logo?.style" :imgSrc="itemRecent.logo
                                                ? (domainImage + itemRecent.logo.path)
                                                : itemRecent.banner
                                                    ? (domainImage + itemRecent.banner.path)
                                                    : ''
                                                " :width="100" :height="100" />

                                        <div class="recent-item-content">
                                            <div class="shop-selected-name">
                                                <nuxt-link :title="itemRecent.name" :prefetch="false"
                                                    :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)">{{
                                                        itemRecent.name }}</nuxt-link>
                                            </div>
                                            <nuxt-link :title="itemRecent.address" :prefetch="false"
                                                :to="appRoute.DetailShopComponent + '/' + (itemRecent.slug ? itemRecent.slug : itemRecent.id)"
                                                class="shop-selected-address">{{ itemRecent.address }}</nuxt-link>

                                            <div class="shop-selected-business-rating">
                                                <span class="business-name" v-if="itemRecent.business_types?.id">
                                                    {{ itemRecent.business_types?.name }}
                                                </span>
                                                <div class="rating" v-if="itemRecent.ratings">
                                                    <Icon name="solar:star-bold"></Icon> {{
                                                        itemRecent.ratings ?? 0
                                                    }}<span>/{{
                                                        appConst.defaultMaxRate }}</span>
                                                </div>
                                            </div>
                                        </div>


                                    </nuxt-link>

                                    <div class="loading-more" v-if="searchProductLoadingMore == true">
                                        {{ $t('AroundComponent.loading') }}
                                    </div>
                                    <div id="last_of_list_suggest"></div>
                                </div>
                                <div class="list-search-suggest" v-if="productRecent.length">
                                    <span>{{ $t('AroundComponent.san_pham_da_xem') }}</span>
                                    <div class="search-placeholder" v-if="(!productRecent || !productRecent.length)">
                                        <img loading="lazy" :src="none_result" :placeholder="none_result" />
                                    </div>
                                    <nuxt-link :prefetch="false"
                                        :to="appRoute.ProductComponent + '/' + (itemRecent.slug?.length ? itemRecent.slug : itemRecent.id)"
                                        v-if="productRecent && productRecent.length" v-for="itemRecent of productRecent"
                                        class="recent-item-container" :title="showTranslateProductName(itemRecent)">
                                        <img loading="lazy"
                                            :src="itemRecent?.profile_picture?.length ? (domainImage + itemRecent?.profile_picture) : icon_for_product"
                                            :placeholder="icon_for_product"
                                            :alt="showTranslateProductName(itemRecent)" />

                                        <div class="recent-item-content">
                                            <span class="name">{{ showTranslateProductName(itemRecent) }}</span>
                                            <span class="sold-like-amount">
                                                <span v-if="itemRecent.sold_count">
                                                    {{ $t('ShopComponent.luot_ban', {
                                                        count: itemRecent.sold_count ??
                                                            0
                                                    }) }}
                                                </span>
                                                <span v-if="itemRecent.sold_count && itemRecent.likes">
                                                    &nbsp;|&nbsp;
                                                </span>
                                                <span v-if="itemRecent.likes">
                                                    {{ $t('ShopComponent.luot_thich', {
                                                        count: itemRecent.likes ??
                                                            0
                                                    }) }}
                                                </span>

                                            </span>

                                            <div class="price-actions">
                                                <div class="price-container">
                                                    <em class="origin-price" :class="{
                                                        'hide': !(itemRecent.price_off != null && parseFloat(itemRecent.price_off) < parseFloat(itemRecent.price))
                                                    }">
                                                        {{
                                                            (parseFloat(itemRecent.price) == 0 ||
                                                                parseFloat(itemRecent.price)
                                                                == null)
                                                                ? $t('AroundComponent.gia_lien_he')
                                                                : formatCurrency(parseFloat(itemRecent.price),
                                                                    itemRecent.shop ?
                                                                        itemRecent.shop.currency : selectedShop.currency)
                                                        }}
                                                    </em>
                                                    <span class="price">
                                                        {{
                                                            (itemRecent.price_off != null && itemRecent.price_off <
                                                                itemRecent.price) ?
                                                                formatCurrency(parseFloat(itemRecent.price_off),
                                                                    itemRecent.shop ? itemRecent.shop.currency :
                                                                        itemRecent.currency) : (parseFloat(itemRecent.price) == 0 ||
                                                                            itemRecent.price == null) ? $t('AroundComponent.gia_lien_he')
                                                                    : formatCurrency(parseFloat(itemRecent.price),
                                                                        itemRecent.shop ? itemRecent.shop.currency :
                                                                            selectedShop.currency) }} </span>

                                                </div>
                                                <button class="add-to-cart" v-on:click="async () => {
                                                    selectedProduct = JSON.parse(JSON.stringify(itemRecent));
                                                    selectedProduct.shop = JSON.parse(JSON.stringify(selectedShop));
                                                    showSelectedProduct = true;
                                                }" v-on:click.stop="(e) => { e.preventDefault() }">
                                                    <Icon name="solar:cart-plus-linear"></Icon>
                                                </button>
                                            </div>
                                        </div>
                                    </nuxt-link>

                                    <div class="loading-more" v-if="searchProductLoadingMore == true">
                                        {{ $t('AroundComponent.loading') }}
                                    </div>
                                    <div id="last_of_list_suggest"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="list-result" :class="{ 'show': isShowList && !searchFocus }" id="list-result">
                        <div class="empty-search"
                            v-if="!(isFirstLoad || refreshing || (filterResultList && filterResultList.length))">
                            <img loading="lazy" class="empty-image" :src="none_result_1" :placeholder="none_result_1" />
                            <p class="empty-text">{{ $t('AroundComponent.khong_tim_thay') }}</p>
                            <span class="go-map-and-zoom-out" v-on:click="() => {
                                showMap()
                            }">{{ $t('AroundComponent.chuyen_qua_ban_do') }}</span>
                        </div>

                        <!-- <div v-if="filterResultList.length" v-for="(itemResult, indexItem) in filterResultList"
                            :key="itemResult?.id" :id="`list_result_${indexItem.toString()}`"
                            class="v-stack result-item-container" :title="itemResult?.name">
                            <ListAroundItemComponent :itemData="JSON.parse(JSON.stringify(itemResult))"
                                :itemIndex="indexItem" v-on:item_click="(itemProduct) => {
                                    selectedProduct = JSON.parse(JSON.stringify(itemProduct));
                                    showSelectedProduct = true;
                                }"></ListAroundItemComponent>
                        </div> -->
                        <v-virtual-scroll :items="filterResultList" v-if="filterResultList.length"
                            class="filter-map-virtual-scroll">
                            <template v-slot:default="{ item, index }" :title="(item as any)?.name"
                                :key="(item as any)?.id" :id="`list_result_${(item as any)?.id.toString()}`">
                                <ListAroundItemComponent :itemData="JSON.parse(JSON.stringify(item))" :itemIndex="index"
                                    v-on:item_click="(itemProduct) => {
                                        selectedProduct = JSON.parse(JSON.stringify(itemProduct));
                                        showSelectedProduct = true;
                                    }"></ListAroundItemComponent>
                            </template>
                        </v-virtual-scroll>
                        <div class="loading-more" v-if="loadingMore == true">
                            {{ $t('AroundComponent.loading') }}
                        </div>
                        <span class="showed-all"
                            v-if="filterResultList && filterResultList.length && (filterResultList.length >= filterResultListCount)">
                            {{ $t('AroundComponent.da_hien_thi_toan_bo') }}
                        </span>

                        <div id="last_of_list"></div>
                        <button v-if="isShowList" v-on:click="() => {
                            showList()
                        }" class="show-map-button">
                            <Icon name="solar:map-outline"></Icon>
                            {{ $t('AroundComponent.ban_do') }}
                        </button>
                    </div>
                    <!-- <v-overlay v-model="refreshing" location="bottom" :z-index="100" :absolute="false" :contained="true"
                        class="loadint-container" content-class='spinner-container' persistent scrim="#fff"
                        key="loading" no-click-animation>
                        <Icon name="eos-icons:loading"></Icon>
                    </v-overlay> -->
                </div>
            </div>
            <!-- <VueFinalModal class="my-modal-container" content-class="v-stack my-modal-content-container"
                :click-to-close="true" v-model="showClearHistorySearchModal" contentTransition="vfm-fade" v-on:closed="() => {
                    showClearHistorySearchModal = false
                }">
                <div class="clear-search-history-container">
                    <span>
                        {{ $t('HomeComponent.xoa_lich_su_tim_kiem') }}
                    </span>

                    <div class="clear-actions">
                        
                        <button class="close" v-on:click="() => {
                            showClearHistorySearchModal = false;
                        }">{{ $t('HomeComponent.dong') }}</button>
                        <button class="accept" v-on:click="() => {
                            clearSearchHistory()
                        }">{{ $t('HomeComponent.xoa_lich_su') }}</button>
                    </div>
                </div>
            </VueFinalModal> -->
            <ClearSearchHistoryComponent v-if="showClearHistorySearchModal" v-on:close="() => {
                showClearHistorySearchModal = false
            }" v-on:submit="() => {
                clearSearchHistory()
            }"></ClearSearchHistoryComponent>
        </div>
        <AddProductToCartComponent v-if="showSelectedProduct" v-on:close="() => {
            showSelectedProduct = false
        }" :selectedProduct="selectedProduct">
        </AddProductToCartComponent>

        <div class="auto-click" id="auto-click" v-on:click="(e) => {
        }"></div>
        <CustomSelectComponent v-if="showCategorySelect" :_key="'select_category_filter'" :list_item="dataCategory"
            :field_value="'id'" :field_title="'name'" :multiple="true" :title="`${t('AroundComponent.danh_muc')}`"
            :class="'my-custom-select'" :searchable="false" :model_value="filterData.category_ids" v-on:close="() => {
                showCategorySelect = false;
            }" v-on:model:update="(e) => {
                setCategoriesFilter(e);
            }">
            <template v-slot:title_icon_left>
                <Icon name="solar:hamburger-menu-linear"></Icon>
            </template>
        </CustomSelectComponent>
        <CustomSelectComponent v-if="showFilterSortSelect" :_key="'select_category_filter'" :list_item="dataFilterSort"
            :field_value="'id'" :field_title="'name'" :multiple="false" :title="`${t('AroundComponent.sap_xep')}`"
            :class="'my-custom-select sort-custom-select'" :searchable="false" :model_value="filterData.sortBy"
            v-on:close="() => {
                showFilterSortSelect = false;
            }" v-on:model:update="(e) => {
                filterData.sortBy = e;
                filter()
                // setCategoriesFilter(e);
            }">
            <template v-slot:title_icon_left>
                <Icon name="solar:hamburger-menu-linear"></Icon>
            </template>
            <template v-slot:render_item_option="{ item }">
                <span class="sort-select-item">{{ item.name }}</span>
            </template>
        </CustomSelectComponent>
    </div>

</template>
<style lang="scss" src="./AroundV2Styles.scss"></style>
<script lang="ts" setup>
import logo from "~/assets/image_13_3_2024/logo.png"
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import shop_banner from "~/assets/image/remagan-banner-19_1.png";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import icon_for_product from "~/assets/image/icon-for-product.png";
import marker_location_icon from "~/assets/image/marker-location.png";
import blue_marker_location_icon from "~/assets/image/blue-dot-location.png";
import none_result from "~/assets/image/none-result-2.webp";
import none_result_1 from "~/assets/image/none-result.webp";

import { LMap, LTileLayer, LControlZoom, LControl } from '@vue-leaflet/vue-leaflet';
import { appRoute } from '~/assets/appRoute';
import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName, showTranslateProductDescription, deepCompareJsons } from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";
import { PlaceService } from "~/services/placeService/placeService";
import { toast } from "vue3-toastify";
import type { MarkerClusterGroup } from "leaflet";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "../confirm18Age/Confirm18AgeComponent.vue";
import { filter_sort, type CartDto } from "~/assets/appDTO";
import { documentId } from "firebase/firestore";
import AddProductToCartComponent from "../cart/addProductToCart/AddProductToCartComponent.vue";
import AvatarComponent from "../avatar/AvatarComponent.vue";
import { HttpStatusCode } from "axios";
import { VueFinalModal } from "vue-final-modal";



const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const device = useDevice();

useHead({
    title: t('AppRouteTitle.AroundComponent'),
    meta: [
        {
            name: "title",
            content: t('AppRouteTitle.AroundComponent'),
        },
    ],
});
useSeoMeta({
    title: t('AppRouteTitle.AroundComponent'),
    ogTitle: t('AppRouteTitle.AroundComponent'),
    description: t('AppRouteTitle.AroundComponent'),
    ogDescription: t('AppRouteTitle.AroundComponent'),
    ogImage: baseLogoUrl,
    ogImageHeight: 400,
    ogImageWidth: 720,
    ogUrl: "https://remagan.com",
    ogType: "website",
});

var dataCategory = ref(appDataStartup.listCategory) as any;
var dataBusinessType = ref(appDataStartup.listBusinessType) as any;
var dataFilterSort = Object.keys(filter_sort).map((key) => {
    return {
        id: filter_sort[key],
        key: key,
        name: t(`AroundComponent.${key}`)
    }
})

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

var filterCarousel = ref<any>(null);
var address = ref("");
var isFirstLoad = ref(true);
var refreshing = ref(false);
var loadingMore = ref(false);
var loadMoreTimeout: any;
var selectedShop = ref(null as any);
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref(false);
var searchSuggestAll = ref(false);
var searchFocus = ref(route.query?.searchFocus == 'true' ? true : false);
var isShowList = ref(route.query?.isShowList == 'true' ? true : false)
var productRecent = ref([] as any[]);
var shopRecent = ref([] as any[]);
var searchRecent = ref([] as any[]);
var searchTextSuggest = ref([] as any[]);
var cartData = ref();
var markerLeaflets = ref([] as any);
var filterResultMap = useState<any>('around_filter_result_map', () => { return [] });
var filterResultList = useState<any>('around_filter_result_list', () => { return [] });
var filterResultListCount = ref(0);
var filterData = useState('around_filter_data', () => {
    return {
        search: "",
        search_text: "",
        latitude_user: user_latitude.value,
        longitude_user: user_longitude.value,
        latitude: null,
        longitude: null,
        zoom: 15,
        // latitude_s: "",
        // latitude_b: "",
        // longitude_s: "",
        // longitude_b: "",
        limit: 20,
        offset: 0,
        category_ids: [] as any,
        sortBy: filter_sort.gan_nhat,
        filter_type: 1,
        map_data: true,
        is_suggest: false,
        is_sale_off: false,
        business_type_id: null,
        child_num: 5,
    }
});
var isSearchNew = ref(false);
var buttonMapTileBackgound = ref(map_sateline);
var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: MarkerClusterGroup;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('AroundComponent.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var latitude = ref();
var longitude = ref();

var searchProductTimeout: any;
var searchProductSuggest = ref();
var searchProductSuggestCount = ref();

var publicService = new PublicService();
var placeService = new PlaceService();

var showSelectedShop = ref(false);
var showProductsInShopSlide = useState(() => { return false });

var selectedProduct = ref(null as any);
var showSelectedProduct = ref(false);
var webInApp = ref(null as any);

var rotate = ref(0 as any);
var filterMapCarousel = ref();
var indexFilterCarouselShowing = useState(() => { return 0 })
var showClearHistorySearchModal = ref(false);
var showCategorySelect = ref(false);
var showFilterSortSelect = ref(false);

var sticky_header = ref<HTMLElement | null>();

var aroundScrollPosition = useState('around_scroll_position', () => { return 0 });

watch(() => [user_latitude.value, user_longitude?.value], (newValue, oldValue) => {

    if (localeMarkerLeaflet) {
        localeMarkerLeaflet.setLatLng([user_latitude?.value, user_longitude?.value])
    }
    else if (leafletMap) {
        setLocationLeafletMarker(user_latitude?.value, user_longitude?.value);
    }
    filterData.value = {
        ...filterData.value,
        latitude_user: user_latitude?.value,
        longitude_user: user_longitude?.value
    }
    console.log(isFirstLoad.value)
    if (isFirstLoad.value) {

        filterData.value = {
            ...filterData.value,
            latitude: filterData.value.latitude ?? user_latitude?.value,
            longitude: filterData.value.longitude ?? user_longitude?.value
        }
        // if (leafletMap) {
        //     setCurrentViewLeaflet()
        // }
    }
})

onUnmounted(() => {
    let storageState = {
        filterData: filterData.value,
        isShowList: isShowList.value,
        searchProductSuggest: searchProductSuggest.value,
        searchProductSuggestCount: searchProductSuggestCount.value,
        searchTextSuggest: searchTextSuggest.value,
        selectedShop: selectedShop.value,
        showSelectedShop: showSelectedShop.value,
    }
    sessionStorage.setItem(appConst.storageKey.stateRestore.AroundComponent, JSON.stringify(storageState));
    nuxtApp.$unsubscribe('refresh_around');
    nuxtApp.$unsubscribe(appConst.event_key.user_moving);
    nuxtApp.$unsubscribe(appConst.event_key.app_data_loaded);
    nuxtApp.$unsubscribe(appConst.event_key.home_click);
    nuxtApp.$unsubscribe(appConst.event_key.search_focus);
})
onBeforeMount(async () => {
    let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
    webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

    productRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : []
    shopRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.shopRecent) as string) : []
    searchRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) : [];
    window.addEventListener('beforeunload', async () => {
        refresh()
    });
    let cartData$ = JSON.parse(
        localStorage.getItem(appConst.storageKey.cart) as string
    );
    cartData.value = cartData$;

    nuxtApp.$listen('refresh_around', async () => {
        refresh()
    });
    nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
        dataCategory.value = appDataStartup.listCategory;
        dataBusinessType.value = appDataStartup.listBusinessType;
    });

    nuxtApp.$listen(appConst.event_key.search_focus, () => {
        focusSearch()
    });
    nuxtApp.$listen(appConst.event_key.home_click, () => {
        isShowList.value = !isShowList.value;
        router.replace({
            query: {
                ...route.query,
                isShowList: JSON.stringify(isShowList.value)
            }
        })
    });

    nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
        user_latitude.value = coor.latitude;
        user_longitude.value = coor.longitude;
    });
    let queryFilter = route.query?.filter ? route.query.filter : null;
    let queryIsShowList = route.query?.isShowList ? route.query?.isShowList : null;

    let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.AroundComponent) as string);
    if (queryFilter) {
        filterData.value = JSON.parse(route.query.filter as string);
        router.replace({
            query: {
                ...route.query,
                filter: JSON.stringify(filterData.value)
            }
        })
    }

    if (queryIsShowList) {
        isShowList.value = queryIsShowList == 'true' ? true : false;
        router.replace({
            query: {
                ...route.query,
                isShowList: isShowList.value.toString()
            }
        })
    }

    if (!queryFilter && store) {
        // filterData.value = store.filterData;
        // router.replace({
        //     query: {
        //         ...route.query,
        //         filter: JSON.stringify(filterData.value),
        //     }
        // })
        searchProductSuggest.value = store.searchProductSuggest;
        searchProductSuggestCount.value = store.searchProductSuggestCount;
        searchTextSuggest.value = store.searchTextSuggest;
    }
    if (store) {
        selectedShop.value = store.selectedShop;
        showSelectedShop.value = store.showSelectedShop;
    }


})
onMounted(async () => {

    // --------------------- sẽ mở lại sau -----------------------
    // if (isiOS()) {
    //     nuxtApp.$listen(appConst.event_key.device_rotated, (e: any) => {
    //         rotate.value = e;
    //         if (localeMarkerLeaflet && e !== null) {
    //             let icon = new nuxtApp.$L.Icon({
    //                 iconUrl: blue_marker_location_icon,
    //                 iconSize: appConst.markerCustom.rotatedIcon.size,
    //                 className: appConst.markerCustom.rotatedIcon.class,
    //             })
    //             localeMarkerLeaflet.setIcon(icon);
    //             localeMarkerLeaflet.setRotationAngle(rotate.value);
    //         }
    //     });
    // } else {
    //     ondeviceorientationabsolute = (e) => {
    //         console.log("device orientation: ", e);
    //         rotate.value = e.alpha;
    //         if (localeMarkerLeaflet && e.alpha !== null) {
    //             let icon = new nuxtApp.$L.Icon({
    //                 iconUrl: blue_marker_location_icon,
    //                 iconSize: appConst.markerCustom.rotatedIcon.size,
    //                 className: appConst.markerCustom.rotatedIcon.class,
    //             })
    //             localeMarkerLeaflet.setIcon(icon);
    //             localeMarkerLeaflet.setRotationAngle(-rotate.value);
    //         }
    //     }
    // }

    // let userLocation = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);

    // if (userLocation && userLocation.latitude_user && userLocation.longitude_user) {
    //     filterData.value = {
    //         ...filterData.value,
    //         latitude_user: userLocation.latitude_user,
    //         longitude_user: userLocation.longitude_user
    //     }
    // }
    setTimeout(() => {
        document.getElementById("around_public_container")?.scrollBy({ top: aroundScrollPosition.value });
    }, 200);
})



function refresh() {
    sessionStorage.removeItem(appConst.storageKey.stateRestore.AroundComponent);
}
function isiOS() {
    var Apple = device.isApple;
    var Mac = device.isMacOS;
    var iOS = device.isIos;
    return (Apple || Mac || iOS);
}
function handelFilterInArea(){
    refreshing.value = true;
    setTimeout(() => {
        filter();
        refreshing.value = false;
    }, 1500);
}
function filter(offset = 0, limit = 20) {
    refreshing.value = true;
    filterData.value.map_data = false;
    filterData.value.filter_type = 1;
    // filterData.value.sortBy = 6;

    publicService
        .filter(filterData.value)
        .then((res) => {
            if (res.status && res.status == HttpStatusCode.Ok) {
                filterResultMap.value = res.body.data.resultMap;
                refreshing.value = false;
                filterResultList.value = res.body.data.resultMap;
                filterResultListCount.value = res.body.data.count;

                router.replace({
                    query: {
                        ...route.query,
                        filter: JSON.stringify(filterData.value)
                    }
                })
                if (leafletMap) showProductsOnMap();
                saveSearchRecent()
                filterMapCarousel.value.slideTo(0)
            }
        })
        .catch((err) => {
            console.error("lỗi list: ", err);
            refreshing.value = false;
        });
}
async function initLeafletMap() {
    console.log("init leaflet map", user_latitude.value, user_longitude.value)
    markersCluster = new nuxtApp.$L.MarkerClusterGroup({
        maxClusterRadius: 30,
        disableClusteringAtZoom: 13,
        iconCreateFunction: (cluster) => createClusterElement(cluster),
    });
    leafletMap.addLayer(markersCluster);

    setTimeout(async () => {
        await setCurrentViewLeaflet();

        setLocationLeafletMarker(user_latitude?.value ?? appConst.defaultCoordinate.latitude, user_longitude?.value ?? appConst.defaultCoordinate.longitude);
    }, 500);
}

function createClusterElement(cluster: any) {
    return nuxtApp.$L.divIcon({
        html: '<div class="my-cluster-icon red-shadow-icon">'
            + cluster.getChildCount()
            + '</div>'
    });
}
async function mapBoundChanges(lat_s: any, lng_s: any, lat_b: any, lng_b: any) {
    let filterParams = JSON.parse(JSON.stringify(filterData.value));
    latitude.value = leafletMap.getCenter().lat;
    longitude.value = leafletMap.getCenter().lng;
    console.log('map bound change', latitude.value, longitude.value)
    filterParams = {
        ...filterParams,
        // latitude_s: lat_s,
        // latitude_b: lat_b,
        // longitude_s: lng_s,
        // longitude_b: lng_b,
        latitude: leafletMap.getCenter().lat,
        longitude: leafletMap.getCenter().lng,
        zoom: leafletMap.getZoom()
    };
    router.replace({
        query: {
            ...route.query,
            filter: JSON.stringify(filterParams)
        }
    })
    filterData.value = JSON.parse(JSON.stringify(filterParams));
    if (user_latitude.value != null && user_longitude.value != null) {
        if (isFirstLoad.value == true) {
            if (!filterResultList.value || !filterResultList.value.length || !filterResultMap.value || !filterResultMap.value.length || !deepCompareJsons(filterData.value, JSON.parse(route.query.filter as string ?? null))) {
                filter();
            }
            else {
                showProductsOnMap();
            }
        }
        else {
            // filter();
        }
        isFirstLoad.value = false;
    }

}
function resetMarkersLeaflet() {
    if (markerLeaflets.value.length) {
        markerLeaflets.value = [];
        markersCluster.clearLayers();
    }
}
async function showProductsOnMap() {
    let listPropertyOnMap = JSON.parse(JSON.stringify(filterResultMap.value));

    await resetMarkersLeaflet();
    listPropertyOnMap.forEach(async (element: any, index: number) => {

        let markerMap = nuxtApp.$L.marker([element.latitude, element.longitude], {
            icon: nuxtApp.$L.divIcon({
                className: `img-icon`,
                html: `
                <div class='shop-logo-container' key='marker_${index}_${element.id}' id='marker_${index}_${element.id}'> 
                    <div class='shop-logo'>
                        ${element.logo
                        ? `<div class='logo-origin-container'>
                                <img 
                                    src='${element.logo ? (domainImage + element.logo.path) : element.banner ? (domainImage + element.banner.path) : shop_logo}'
                                    style='
                                        transform: ${element.logo?.style?.length ? element.logo.style : element.banner?.style?.length ? element.banner.style : 'scale(1) translate(0)'};
                                        ${element.logo?.style?.length ? '' : 'height: 100%'}
                                    '
                                />
                            </div>`
                        : `<img
                                [hidden]=${!!element.logo}
                                src='${shop_logo}'
                                />`
                    } 
                    
                    </div>
                </div>
                <div class='after'></div>
                `,
                iconUrl: (element.banner ? domainImage + element.banner : shop_logo),
                iconSize: [50, 50],
            }),
        });


        let markerMapZoom13 = nuxtApp.$L.marker([element.latitude, element.longitude], {
            icon: nuxtApp.$L.divIcon({
                className: "red-shadow-icon",
                iconUrl: (element.banner ? domainImage + element.banner : shop_banner),
                iconAnchor: [7, 7],
                iconSize: [15, 15],
            }),
        });

        // if (leafletMap.getZoom() >= 13) {
        //     markerLeaflets.value.push(markerMap);
        //     if (leafletMap.getZoom() >= 15) {
        //         markerMap.bindTooltip(element.name, {
        //             direction: 'bottom',
        //             permanent: true,
        //             interactive: true,
        //             className: "tooltip-leaflet-own",
        //         });
        //     }
        // }
        // else {
        //     markerLeaflets.value.push(markerMapZoom13);
        // }
        markerLeaflets.value.push(markerMap);
        // if (leafletMap.getZoom() >= 15) {
        //     markerMap.bindTooltip(element.name, {
        //         direction: 'bottom',
        //         permanent: true,
        //         interactive: true,
        //         className: "tooltip-leaflet-own",
        //     });
        // }

        let iconHoverLeaflet = nuxtApp.$L.divIcon({
            className: "violet-shadow-icon",
            iconAnchor: [7, 7],
            iconSize: [14, 14],
        });

        markerLeaflets.value[index].addEventListener("click", async () => {

            selectedShop.value = listPropertyOnMap[index];
            // showSelectedShop.value = true;
            if (filterMapCarousel.value) {
                filterMapCarousel.value.slideTo(index)
            }

        });

    });
    markersCluster.addLayers(markerLeaflets.value);
    setClassShowCenterMarker()
}
function unsetClassShowCenterMarker() {
    if (indexFilterCarouselShowing.value != null) {
        // leafletMap.removeLayer(markerLeaflets.value[indexFilterCarouselShowing.value])
        // markersCluster.addLayer(markerLeaflets.value[indexFilterCarouselShowing.value]);
        // if(!markersCluster.hasLayer(markerLeaflets.value[indexFilterCarouselShowing.value])){
        //     markersCluster.addLayer(markerLeaflets.value[indexFilterCarouselShowing.value]);
        //     leafletMap.removeLayer(markerLeaflets.value[indexFilterCarouselShowing.value])
        // }
        markerLeaflets.value[indexFilterCarouselShowing.value]?._icon?.classList.remove('show-center');
        markerLeaflets.value[indexFilterCarouselShowing.value]?.closeTooltip();
    }
}
function setClassShowCenterMarker() {
    // ${indexFilterCarouselShowing.value == index ? 'show-center': ''}
    setTimeout(() => {
        if (indexFilterCarouselShowing.value != null) {
            // markersCluster.removeLayer(markerLeaflets.value[indexFilterCarouselShowing.value]);
            // leafletMap.addLayer(markerLeaflets.value[indexFilterCarouselShowing.value])
            // if(markersCluster.hasLayer(markerLeaflets.value[indexFilterCarouselShowing.value])){
            //     markersCluster.removeLayer(markerLeaflets.value[indexFilterCarouselShowing.value]);
            //     leafletMap.addLayer(markerLeaflets.value[indexFilterCarouselShowing.value])
            // }
            markerLeaflets.value[indexFilterCarouselShowing.value]?._icon?.classList.add('show-center');
            markerLeaflets.value[indexFilterCarouselShowing.value]?.bindTooltip(filterResultMap.value?.[indexFilterCarouselShowing.value].name, {
                direction: 'bottom',
                permanent: true,
                interactive: true,
                className: "tooltip-leaflet-own show-center",
            });
        }
    }, 300);



}
function mapFlyTo(shopItem: any) {
    if (leafletMap) {
        leafletMap.flyTo([shopItem?.latitude, shopItem?.longitude], 15, {
            duration: .2
        })
    }
}
async function setCurrentViewLeaflet() {
    console.log('set map view', filterData.value?.latitude, filterData.value?.longitude)
    if (filterData.value?.latitude && filterData.value?.longitude) {
        leafletMap.setView([filterData.value?.latitude, filterData.value?.longitude], filterData.value?.zoom ?? 15)
    }
    else if (filterData.value?.latitude_user && filterData.value?.longitude_user) {
        leafletMap.setView([filterData.value.latitude_user, filterData.value.longitude_user], filterData.value?.zoom ?? 15);
    } else if (user_latitude?.value != null && user_longitude?.value != null) {
        leafletMap.setView([user_latitude?.value, user_longitude?.value], filterData.value.zoom ?? 15);
    }
    else {
        leafletMap.setView([appConst.defaultCoordinate.latitude, appConst.defaultCoordinate.longitude], filterData.value.zoom ?? 15);
    }
    // if ("geolocation" in navigator) {    
    // navigator.geolocation.getCurrentPosition(
    //     async (position) => {
    //         filterData.value.latitude_user = position.coords.latitude;
    //         filterData.value.longitude_user = position.coords.longitude;

    //         latitude.value = position.coords.latitude;
    //         longitude.value = position.coords.longitude;
    //         // if (
    //         //     filterData.value.latitude_s &&
    //         //     filterData.value.latitude_b &&
    //         //     filterData.value.longitude_s &&
    //         //     filterData.value.longitude_b
    //         // ) {
    //         //     let bound = new nuxtApp.$L.LatLngBounds(
    //         //         [
    //         //             parseFloat(filterData.value.latitude_b),
    //         //             parseFloat(filterData.value.longitude_b),
    //         //         ],
    //         //         [
    //         //             parseFloat(filterData.value.latitude_s),
    //         //             parseFloat(filterData.value.longitude_s),
    //         //         ]
    //         //     );

    //         //     leafletMap.fitBounds(bound);
    //         // } else {
    //         leafletMap.setView([filterData.value?.latitude_user ?? position.coords.latitude, filterData.value?.longitude_user ?? position.coords.longitude], filterData.value?.zoom ?? 15);
    //         // }

    //         await setLocationLeafletMarker(filterData.value.latitude_user, filterData.value.longitude_user);
    //     },
    //     async (error) => {
    //         filterData.value.latitude_user = appConst.defaultCoordinate.latitude;
    //         filterData.value.longitude_user = appConst.defaultCoordinate.longitude;
    //         latitude.value = appConst.defaultCoordinate.latitude;
    //         longitude.value = appConst.defaultCoordinate.longitude;

    //         // if (
    //         //     filterData.value.latitude_s &&
    //         //     filterData.value.latitude_b &&
    //         //     filterData.value.longitude_s &&
    //         //     filterData.value.longitude_b
    //         // ) {
    //         //     let bound = new nuxtApp.$L.LatLngBounds(
    //         //         [
    //         //             parseFloat(filterData.value.latitude_b),
    //         //             parseFloat(filterData.value.longitude_b),
    //         //         ],
    //         //         [
    //         //             parseFloat(filterData.value.latitude_s),
    //         //             parseFloat(filterData.value.longitude_s),
    //         //         ]
    //         //     );
    //         //     leafletMap.fitBounds(bound);
    //         // } else {
    //         leafletMap.setView([latitude.value, longitude.value], filterData.value.zoom ?? 15);
    //         // }
    //         await setLocationLeafletMarker(latitude.value, longitude.value);
    //     },
    //     {
    //         enableHighAccuracy: false, // Use less accurate but faster methods
    //         timeout: 5000, // Set a timeout (in milliseconds)

    //     }
    // );
    // }
}

function getSearchProductResult() {
    searchProductLoading.value = true;
    clearTimeout(searchProductTimeout);
    searchProductTimeout = setTimeout(() => {
        publicService
            .searchSuggest(filterData.value.search, undefined, undefined, user_latitude.value, user_longitude.value)
            .then((res) => {

                if (res.status == HttpStatusCode.Ok) {
                    searchProductSuggest.value = res.body.data.products;
                    searchProductSuggestCount.value = res.body.data.count;
                    searchTextSuggest.value = res.body.data.strings;
                    searchProductLoading.value = false;
                    document.getElementById("search-result")?.scrollTo({ top: 0 });
                    searchSuggestAll.value = false;
                }
            })
            .catch(() => {
                searchProductLoading.value = false;
            });
    }, 1000);
}

function loadMoreProductSearching() {
    if (
        searchProductSuggest.value.length < searchProductSuggestCount.value &&
        !searchSuggestAll.value && !searchProductLoadingMore.value
    ) {
        searchProductLoadingMore.value = true;
        clearTimeout(searchProductTimeout);
        searchProductTimeout = setTimeout(() => {
            publicService
                .searchSuggest(
                    filterData.value.search,
                    searchProductSuggest.value.length,
                    25,
                    filterData.value.latitude_user, filterData.value.longitude_user
                )
                .then((res) => {
                    if (res.status == HttpStatusCode.Ok) {
                        if (res.body.data && res.body.data.result.length > 0) {
                            searchProductSuggest.value = [
                                ...searchProductSuggest.value,
                                ...res.body.data.products,
                            ];
                        } else {
                            searchSuggestAll.value = true;
                        }
                        searchProductLoadingMore.value = false;
                    }
                })
                .catch(() => {
                    searchProductLoadingMore.value = false;
                });
        }, 1000);
    }
}

function setLocationLeafletMarker(lat: number, lng: number) {
    if (localeMarkerLeaflet) {
        localeMarkerLeaflet.remove();
    }

    localeMarkerLeaflet = nuxtApp.$L.marker([lat, lng], {
        icon: new nuxtApp.$L.Icon({
            iconUrl: marker_location_icon,
            iconSize: appConst.markerCustom.defaultIcon.size,
            className: appConst.markerCustom.defaultIcon.class,
        }),
        rotationOrigin: 'center 75%'
    });
    localeMarkerLeaflet.addTo(leafletMap);
    // latitude.value = lat;
    // longitude.value = lng;
    // filterData.value.latitude_user = lat;
    // filterData.value.longitude_user = lng;
    // getUserAddress();

    localeMarkerLeaflet.addEventListener("click", (event: L.LeafletEvent) => {
    });
}
async function gotoCurrentLocationLeaflet(event?: Event) {
    if (!event || event.isTrusted == true) {
        leafletMap.flyTo([user_latitude.value, user_longitude.value], 17);
        // if ("geolocation" in navigator) {
        //     navigator.geolocation.getCurrentPosition(
        //         (position) => {
        //             console.log(position)
        //             leafletMap.flyTo(
        //                 [position.coords.latitude, position.coords.longitude],
        //                 17
        //             );
        //             latitude.value = position.coords.latitude;
        //             longitude.value = position.coords.longitude;

        //         },
        //         (error) => {
        //             console.log(error)
        //             toast.warning(t('AroundComponent.chua_cung_cap_vi_tri'), {
        //                 autoClose: 1000,
        //                 hideProgressBar: true,
        //             });
        //             latitude.value = appConst.defaultCoordinate.latitude;
        //             longitude.value = appConst.defaultCoordinate.longitude;
        //             leafletMap.flyTo([latitude.value, longitude.value], 17);

        //             setLocationLeafletMarker(latitude.value, longitude.value);
        //         },
        //     );
        // }

    }
}

function getUserAddress() {
    setProvinceDistrictByLocation();
}
async function setProvinceDistrictByLocation() {
    placeService
        .myGeocoderByLatLngToAddress(latitude.value, longitude.value)
        .then((res: any) => {
            if (res.body.data && res.body.data.length) {
                address.value = res.body.data[0].address
                    ? res.body.data[0].address
                    : "";
            }
        });
}
function checkCategoryFilter(id: any) {
    if (
        filterData.value.category_ids &&
        filterData.value.category_ids.indexOf(id) != -1
    )
        return true;
    return false;
}
function setCategoryFilter(id: any) {
    if (!id) {
        filterData.value = {
            ...filterData.value,
            category_ids: [],
        };
        filter()
        return;
    }
    let arr: any = filterData.value.category_ids || [];
    let index = arr.indexOf(id);
    if (index == -1) {
        filterData.value = {
            ...filterData.value,
            category_ids: [id],
        };
    } else {
        filterData.value = {
            ...filterData.value,
            category_ids: [],
        };
    }
    // filterData.value = {
    //     ...filterData.value,
    //     category_ids: arr,
    // };
    filter()
}

function setCategoriesFilter(ids: any[]) {
    filterData.value.category_ids = [...ids];
    filter();
}

function setBusinessTypeFilter(id: any) {
    if (!id) {
        filterData.value = {
            ...filterData.value,
            business_type_id: null,
        };
        filter()
        return;
    }
    if (filterData.value.business_type_id == id) {
        filterData.value = {
            ...filterData.value,
            business_type_id: null,
        };
    } else {
        filterData.value = {
            ...filterData.value,
            business_type_id: id,
        };
    }
    // filterData.value = {
    //     ...filterData.value,
    //     category_ids: arr,
    // };
    filter()
}
function listScroll(event: any) {
    let el = document
        .getElementById("last_of_list")
        ?.getBoundingClientRect().bottom ?? 0;
    let elContainer = document.getElementById('around_public_container')?.getBoundingClientRect().bottom ?? 0;
    if (el <= elContainer) {
        loadMoreFilterResult()
    }

    let ep = document
        .getElementById("around_public_container")
        ?.scrollTop;
    aroundScrollPosition.value = ep as number;
}
function loadMoreFilterResult() {
    if (filterResultList.value.length < filterResultMap.value.length) {
        loadingMore.value = true;
        clearTimeout(loadMoreTimeout)
        loadMoreTimeout = setTimeout(() => {

            let listOnMap = JSON.parse(JSON.stringify(filterResultMap.value));
            filterResultList.value = [
                ...filterResultList.value,
                ...listOnMap.slice(filterResultList.value.length, filterResultList.value.length + 20)
            ]
            loadingMore.value = false;
        }, 1000);
    }

}
function listSuggestScroll(event: any) {
    let el = document
        .getElementById("last_of_list_suggest")
        ?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 300) {
        if (searchProductSuggest.value?.length < searchProductSuggestCount.value) {
            loadMoreProductSearching();
        }
    }
}
function focusSearch() {
    searchFocus.value = true;
    setTimeout(() => {
        document.getElementById('search_input')?.focus();
    }, 500);

}
function blurSearch() {
    searchFocus.value = false;
    document.getElementById('search_input')?.blur();
}

function showList() {
    nuxtApp.$emit(appConst.event_key.home_click);
    showingSlideFilter();
}
function showMap() {
    isShowList.value = false;
    leafletMap.setZoom(leafletMap.getZoom() - 1);
}
function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
function getPercentSaleOff(product: any) {
    if (product.price_off && product.price) return -Math.round(((product.price - product.price_off) / product.price) * 100 || 0);
    return 0
}

function saveSearchRecent() {
    if (filterData.value?.search_text?.length) {

        let searchRecent$ = JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.searchRecent) as string) : [];
        let index = searchRecent$.indexOf(filterData.value.search_text);
        if (index == -1) {
            searchRecent$ = [
                filterData.value.search_text,
                ...searchRecent$,
            ].slice(0, 10);
            searchRecent.value = searchRecent$;

            localStorage.setItem(appConst.storageKey.searchRecent, JSON.stringify(searchRecent$));
        }
    }
}

function clearSearchHistory() {
    localStorage.removeItem(appConst.storageKey.searchRecent);
    searchRecent.value = [];
    showClearHistorySearchModal.value = false
}

function showingSlideFilter() {
    setTimeout(() => {
        filterMapCarousel.value.slideTo(indexFilterCarouselShowing.value ?? 0);
        if (!aroundScrollPosition.value) {
            document.getElementById(`filter_item_${indexFilterCarouselShowing.value}`)?.scrollIntoView({
                block: 'center'
            })
        }

    }, 200);
}

</script>