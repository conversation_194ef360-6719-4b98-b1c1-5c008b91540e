.my-shop-order-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--color-background-2);
  position: relative;
  align-items: center;
  justify-content: center;
  overflow: auto;
  width: 100%;
  max-width: var(--max-width-content-view-1024) !important;

  // & > .title-header {
  //   // font-size: 1.6em;
  //   padding: 5px 0;
  //   margin: 0;
  //   text-align: center;
  //   width: 100%;
  //   border-bottom: thin solid #ccc;
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;

  //   & h3 {
  //     margin: 0;
  //   }

  //   & .header-left {
  //     display: flex;
  //     padding-left: 5px;
  //     justify-content: left;
  //     gap: 5px;
  //     flex: 1;
  //     // margin-right: auto;

  //     & > button {
  //       background: var(--color-background-2);
  //       border-radius: 50%;
  //       padding: 0;
  //       width: 40px;
  //       height: 40px;
  //       display: flex;
  //       align-items: center;
  //       justify-content: center;
  //       border: none;
  //     }
  //   }

  & > .sub-title-v2-header{
    padding: 0 10px;
    height: 50px;
  }

  & .header-right {
    //     display: flex;
    //     justify-content: flex-end;
    //     gap: 5px;
    //     flex: 1;
    //     padding-right: 5px;

    //     & > button {
    //       background: var(--color-background-2);
    //       border-radius: 50%;
    //       padding: 0;
    //       width: 40px;
    //       height: 40px;
    //       display: flex;
    //       align-items: center;
    //       justify-content: center;
    //       border: none;
    //     }
    //   }
    flex: none !important;
    & > .filter-button.active {
      color: #92ff63;
    }
  }
  & .header-left{
    flex: none !important;
  }

  & .header-middle{
    flex: 1;
  }
  & .order-manage-shop-name{
    display: flex;
    flex-direction: column;
    gap: 0;
    font-weight: 700;
    align-items: flex-start;
    padding-left: 5px;
    flex: 1;
    overflow: hidden;
    line-height: normal;
    flex: 1;

    & > h3{
      font-size: 17px;
    }

    & > span{
      font-size: 15px;
      color: #92ff63;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: left;
    }
  }

  & > .my-shop-order-view {
    flex: 1;
    width: 100%;
    overflow: auto;

    & .tab-content-container {
      width: 100%;
      flex: 1;
      display: flex;

      & div[class*="v-window"] {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-bottom: 50px;
      }

      & div.v-window-item {
        padding: 0 5px;
        overflow: auto;
      }
    }
    & .empty-list {
      height: 100%;
      flex: 1;
      justify-content: center;
      align-items: center;
      gap: 10px;

      & > img {
        margin: 10px auto;
        justify-content: center;
        border-radius: 50%;
        height: 250px;
        width: 250px;
        object-fit: contain;
      }

      & > span {
        font-size: 1.5em;
        color: var(--color-text-note);
      }
    }

    & .tab-content {
      flex: 1;
      padding: 5px 0;
      overflow: auto;

      & > .tab-pane {
        height: 100%;
        padding: 5px 10px;
        overflow: auto;
      }
    }

    & .item-order {
      padding: 10px;
      margin-top: 10px;
      align-items: center;
      width: 100%;
      gap: 5px;
      background-color: white;
      border: thin solid #ccc;
      border-radius: 5px;

      & .order-button-container {
        padding: 0;
        align-items: flex-start;
        background: white;
        // color: var(--primary-color-1);
        gap: 5px;
        font-size: 1em;
        width: 100%;
        height: 100%;
        border: none;

        & > .v-stack {
          width: 100%;

          & > .h-stack {
            flex: 1;
            justify-content: space-between;
            width: 100%;
            text-align: left;
            align-items: flex-start;
            gap: 10px;
          }

          & > .h-stack.order-detail-advanced {
            justify-content: flex-start;
          }
          & .is-new {
            font-size: 10px;
            text-transform: uppercase;
            color: white;
            padding: 5px 10px;
            margin: 0 10px 0 auto;
            background: #1a95ff;
            border-radius: 5px;
            align-items: center;
            display: flex;
          }
          // & .order-status {
          //   padding: 5px;
          //   border-radius: 5px;
          //   justify-content: center;

          //   &.waiting {
          //     color: rgb(164, 171, 182);
          //     background-color: rgba(164, 171, 182, 0.25);
          //   }

          //   &.confirmed {
          //     color: rgb(230, 149, 0);
          //     background-color: rgba(230, 149, 0, 0.25);
          //   }

          //   &.ready {
          //     color: rgb(45, 204, 255);
          //     background-color: rgba(45, 204, 255, 0.25);
          //   }

          //   &.taken {
          //     color: #23490d;
          //     background-color: rgba(86, 240, 0, 0.25);
          //   }

          //   &.cancel {
          //     color: rgb(255, 56, 56);
          //     background-color: rgba(255, 56, 56, 0.25);
          //   }
          // }

          & .customer-name {
            font-weight: 600;
            color: var(--primary-color-1);
          }

          & .customer-phone {
            font-weight: 600;
          }

          & .customer-address > a {
            color: var(--primary-color-1);
            font-style: italic;
            font-weight: 700;
          }

          & .short-code {
            margin-left: auto;
            font-size: 1.1em;
          }
          & .delivery-info {
            display: flex;
            gap: 5px;

            & > .driver-info {
              display: flex;
              gap: 5px;
              align-items: center;

              & > img {
                width: 40px;
                min-width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
                align-self: baseline;
              }
            }

            & > .none {
              margin-left: auto;
              font-size: 15px;
              color: #79787d;
              font-style: italic;
            }
          }

          & .delivery-partner-info{
            display: flex;
            gap: 5px;
            align-items: flex-end;
            line-height: normal;

            & > img{
              height: 25px;
              width: auto;
              object-fit: contain;
            }
            & > .none {
              margin-left: auto;
              font-size: 15px;
              color: #79787d;
              font-style: italic;
            }
          }
          & .title {
            color: var(--color-text-note);
            white-space: nowrap;
          }
          & .total {
            font-weight: 600;
            color: var(--primary-color-1);
          }
        }
      }
    }

    & .order-action {
      padding: 5px;
      justify-content: center;
      gap: 10px;
      width: 100%;

      & button {
        width: 45%;
        height: 40px;
        border-radius: 10px;
        border: thin solid rgba(164, 171, 182, 0.25);
      }

      & button.reject-button {
        color: var(--color-text-black);
        background-color: rgba(164, 171, 182, 0.25);
      }

      & button.accept-button {
        color: var(--primary-color-1);
        color: #fff;
      }
    }

    // & .item-order {
    //   margin-top: 10px;
    //   padding: 0 10px;
    //   background-color: white;

    //   & > .order-status {
    //     border-bottom: 1.4px solid #dedde3;
    //     padding: 10px 0;
    //     display: flex;
    //     justify-content: space-between;
    //     font-weight: bold;
    //     color: #848389;

    //     & > .is-new {
    //       font-size: 0.55em;
    //       text-transform: uppercase;
    //       color: white;
    //       padding: 0 5px;
    //       background: #1a95ff;
    //       border-radius: 5px;
    //       align-items: center;
    //       display: flex;
    //     }
    //   }

    //   & > .order-item {
    //     display: flex;
    //     padding: 10px 0;
    //     gap: 15px;

    //     & > .product-avatar {
    //       width: 100px;
    //       height: 100px;
    //       border-radius: 10px;
    //       background-color: var(--color-background-2);
    //       & > img {
    //         width: 100%;
    //         height: 100%;
    //         border-radius: inherit;
    //         object-fit: contain;
    //       }
    //     }

    //     & > .product-detail {
    //       display: flex;
    //       flex-direction: column;
    //       padding: 5px 0;
    //       gap: 5px;
    //       flex: 1;

    //       & > .name {
    //         font-weight: bold;
    //         display: -webkit-box;
    //         -webkit-box-orient: vertical;
    //         -webkit-line-clamp: 2;
    //         overflow: hidden;
    //         line-height: normal;
    //       }

    //       & > .quantity-price {
    //         color: #848389;
    //         display: flex;
    //         justify-content: flex-start;
    //         align-items: center;
    //         gap: 10px;
    //         font-weight: 500;

    //         & .off {
    //           font-size: 0.85em;
    //           text-decoration: line-through;
    //           font-style: normal;
    //         }
    //       }
    //     }

    //   }
    //   & > .order-actions {
    //     display: flex;
    //     flex-wrap: wrap;
    //     gap: 15px 10px;
    //     font-size: 15px;
    //     justify-content: space-around;
    //     padding: 0 0 15px;
    //     font-size: 15px;
    //     margin-top: 5px;

    //     & button.btn-50 {
    //       flex: .5;
    //       height: 40px;
    //       border-radius: 5px;
    //       color: var(--primary-color-1);
    //       border: thin solid var(--primary-color-1);
    //     }
    //     & button.btn-100{
    //       width: 100%;
    //     }
    //     & button.reject-button {
    //       background: white;
    //     }

    //     & button.accept-button-2 {
    //       background: #1a95ff;
    //       border: thin solid #1a95ff;
    //       color: white;
    //     }
    //   }
    // }

    & .refresh {
      color: var(--primary-color-1) !important;
      font-weight: 600;
      cursor: pointer;
    }

    & .load-more {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      text-align: center;
      background: white;
    }
  }

  & .sticky-header {
    width: 100%;
    position: sticky;
    top: 50px;
    z-index: 1;
  }
  & .search-bar-container {
    width: 100%;
    padding: 10px 15px;
    color: var(--color-text-note);
    font-weight: 500;
    background-color: #f5f6fa;
    & > .search-bar {
      background-color: white;
      gap: 5px;
      padding: 5px 10px;
      border-radius: 5px;

      & > input {
        margin-top: 0;
        border: none;
        display: flex;
        flex: 1;
        outline: none;
      }
    }
  }
  & .orders-tab {
    text-transform: none;
    font-weight: 600;
    font-size: 1em;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    min-height: 50px;
    background: white;

    & .order-tab-title {
      text-transform: none;
      border-bottom: 2px solid transparent;
      color: #79787d;
      font-weight: 600;
      gap: 5px;
      font-size: 1em;
      background-color: white;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        // & > span {
        //   width: 25px;
        //   height: 25px;
        //   background: var(--color-button-special);
        //   color: white;
        //   border-radius: 50%;
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   font-weight: 500;

        //   & > span {
        //     font-size: 0.8em;
        //   }
        // }

        & em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 25px;
          height: 25px;
          font-size: 0.8em;
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 600;

          & > span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    & .order-tab-title.active {
      font-weight: 600;
      border-bottom: 2px solid var(--primary-color-1);
    }
  }
  & .create-order-button {
    color: white;
    background-color: var(--primary-color-1);
    gap: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid white;
    box-shadow: 0 0 20px rgb(0, 0, 0, 10%);
    font-size: 1.2em;
    font-weight: 600;
    padding: 10px 20px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    border-radius: 2em;
    cursor: pointer;
    z-index: 2;
  }
}

.cancel-order-content {
  font-size: 1.3em;
  text-align: center;
}

.cancel-order-title {
  font-size: 1em;
  font-weight: bold;
}

.cancel-order-message {
  font-size: 1em;
  text-align: center;
}

.cancel-order-message > .order-code {
  color: var(--primary-color-2);
  font-weight: bold;
}

.v-slide-group__next,
.v-slide-group__prev {
  color: var(--primary-color-1);
}

.register-shop-content-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background: white;

  & > img {
    width: 300px;
    height: 300px;
    align-items: center;
    object-fit: contain;
    position: relative;
  }

  & span {
    font-size: 20px;
    font-weight: 500;
  }

  & button {
    background-color: var(--primary-color-1);
    border-radius: 2em;
    padding: 3px 25px;
    color: white;
    font-size: 20px;
    border: none;
  }

  
}
.confirm-cancel-order-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.filter-menu-overlay-container {
  overflow: hidden;
  max-width: 100% !important;
  z-index: 1001 !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .filter-menu-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    max-width: var(--max-width-content-view-1024);
    transform: translateX(-50%);

    & > .filter-menu-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      text-align: center;
      padding: 10px 10px 20px 10px;

      & > .filter-title {
        font-size: 1.3em;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color-1);
        position: relative;

        & > button {
          position: absolute;
          right: 0;
          color: var(--color-text-black);
          display: flex;
          align-items: center;
          top: 50%;
          font-size: 1.5em;
          transform: translateY(-50%);
          animation: none;
        }
      }

      & > .time-filter {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 10px;
        font-size: 1.3em;
        gap: 10px;

        & > .item-time-filter {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        & > .item-time-filter.active {
          color: var(--primary-color-1);
        }

        & > .select-date {
          width: 100%;
          justify-content: space-evenly;
        }

        & > .footer {
          display: flex;
          justify-content: space-evenly;
          width: 100%;

          & > button {
            width: 35%;
            padding: 5px 10px;
            display: flex;
            border-radius: 10px;
            white-space: nowrap;
            justify-content: center;
            align-items: center;
            color: var(--primary-color-1);
            border: thin solid var(--primary-color-1);
          }

          & > button.apply {
            background-color: var(--primary-color-1);
            color: white;
          }
        }
      }
    }
  }
}
