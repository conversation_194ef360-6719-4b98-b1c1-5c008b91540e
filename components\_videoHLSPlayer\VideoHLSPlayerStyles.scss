.video-hls-player-container{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    & > .video-error-content {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        & > img{
            width: 50% !important;
            height: 100% !important;
            object-fit: contain !important;
        }
        
        & > button.reload{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    & > .video-thumbnail{
        height: 100%;
        width: 100%;
        object-fit: cover;
        position: relative;

        & > img.thumb {
            height: 100%;
            object-fit: cover;
            max-height: unset;
        }

        
    }
    & > svg.play-icon{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        color: white;
    }
}