<template>
	<div class="content-list shop-interacted-container" v-if="listShopInteracted?.length > 0">
		<div class="stack-content-title">
			<span class="section-title">
				<Icon name="carbon:location-heart"></Icon>
				{{ $t('HomeV2Component.cua_hang_yeu_thich') }}
			</span>
		</div>
		<div class="stack-content-list">

			<!-- <Swiper v-if="listShopInteracted?.length && !loadingShopInteraction"
                                class="my-carousel stack-carousel"
                                :modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
                                :loop="false" :effect="'creative'" :navigation="true" :freeMode=false :autoplay="false"
                                @init="(e) => {
                                    shop_around_el = e
                                }" :space-between="10" key="shop-around-carousel" ref="shop_around_el">
                                <SwiperSlide class="item-stack-slide" v-for="item of listShopInteracted"
                                    :key="'shop_interaction_' + item.id" :id="'shop_interaction_' + item.id">
                                    <nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
                                        :title="showTranslateProductName(item)">
                                        <AvatarComponent :imgTitle="showTranslateProductName(item)"
                                            :imgStyle="item.logo?.style" class="shop-logo-stack" :imgSrc="item.logo
                                                ? (domainImage + item.logo.path)
                                                : item.banner
                                                    ? (domainImage + item.banner.path)
                                                    : ''
                                                " :width="getShopItemWidth('shop_interaction_' + item.id)"
                                            :height="getShopItemWidth('shop_interaction_' + item.id)" v-on:img_click="() => {
                                                router.push(appRoute.DetailShopComponent + '/' + item.slug)
                                            }" />
                                        <span class="distance" v-if="item.distance">
                                            {{ parseFloat(item.distance) > 1000 ? (parseFloat(item.distance) /
                                                1000).toFixed(1) :
                                                (parseFloat(item.distance)).toFixed(0) }}
                                            <em>{{ parseFloat(item.distance) > 1000 ? 'km' : 'm' }}</em> </span>
                                        <div class="item-stack-content">
                                            <span class="shop-name">{{ showTranslateProductName(item) }}</span>
                                            <span class="shop-address">{{ item.address }}</span>
                                        </div>
                                    </nuxt-link>
                                </SwiperSlide>
                            </Swiper> -->
			<div class="stack-content-list-container" v-if="listShopInteracted?.length && !loadingShopInteraction">
				<div class="item-stack-slide" v-for="item of listShopInteracted" :key="'shop_interaction_' + item.id">
					<nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
						:title="showTranslateProductName(item)" :id="'shop_interaction_' + item.id">
						<AvatarComponent :imgTitle="showTranslateProductName(item)" :imgStyle="item.logo?.style"
							class="shop-logo-stack" :imgSrc="item.logo
								? (domainImage + item.logo.path)
								: item.banner
									? (domainImage + item.banner.path)
									: ''
								" :width="getShopItemWidth('shop_interaction_' + item.id)"
							:height="getShopItemWidth('shop_interaction_' + item.id)" v-on:img_click="() => {
								router.push(appRoute.DetailShopComponent + '/' + item.slug)
							}" />
						<span class="distance" v-if="item.distance">
							{{ parseFloat(item.distance) > 1000 ? (parseFloat(item.distance) /
								1000).toFixed(1) :
								(parseFloat(item.distance)).toFixed(0) }}
							<em>{{ parseFloat(item.distance) > 1000 ? 'km' : 'm' }}</em> </span>
						<div class="item-stack-content">
							<span class="shop-name">{{ showTranslateProductName(item) }}</span>
							<span class="shop-address">{{ item.address }}</span>
						</div>
					</nuxt-link>
				</div>
			</div>
			<div v-else class="none-content-list">
				<!-- {{ loadingShopInteraction ? '' : '' }} -->
			</div>
			<v-overlay v-model="loadingShopInteraction" :z-index="100" :absolute="false" contained
				content-class='spinner-container' persistent scrim="#fff" key="loading_best_around" no-click-animation>
				<Icon name="eos-icons:loading"></Icon>
			</v-overlay>
		</div>
	</div>
</template>

<style lang="scss" src="./MyInteractionShopSectionStyles.scss"></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { filter_sort, type CartDto } from "~/assets/appDTO";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();

const props = defineProps({
	enable_load: null
})

var publicService = new PublicService();

var listShopInteracted = useState<any>('dashboard_shop_interaction', () => { return [] });
var loadingShopInteraction = ref(false);
var limit = ref(20);
var offset = ref(0);

watch(() => [props.enable_load], () => {
	if (props.enable_load) {
		initListShopInteraction()
	}
})
onBeforeMount(() => {

})
onUnmounted(async () => {
});
onMounted(async () => {
	initListShopInteraction();
});

function initListShopInteraction() {
	return new Promise((resolve) => {
		let body = {
			type: appConst.interactionType.follow,
			limit: limit.value,
			offset: offset.value
		}

		publicService.listShopInteracted({
			...body
		}).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				listShopInteracted.value = res.body?.result ? JSON.parse(JSON.stringify(res.body?.result)) : []
			}
			else{
				listShopInteracted.value = [];
			}
			resolve(listShopInteracted.value)
		}).catch(()=>{
			listShopInteracted.value = [];
			resolve(listShopInteracted.value)
		})
	})
}

function getShopItemWidth(elId: any) {
	return document.getElementById(elId)?.getBoundingClientRect().width ?? 150;
}

</script>
