<template>
    <div class="search-public-container">
        <div class="location-and-search scale-down">
            <div class="h-stack location-text">
                <!-- <span><PERSON><PERSON><PERSON> <PERSON>ng đến</span>
                <div class="address">
                    <span class="location-value" :title="address.length ? address : 'Không có địa chỉ'">
                        {{ address.length ? address : "Không có địa chỉ" }}
                    </span>
                </div> -->
                <img loading="lazy" :src="logo" :placeholder="logo" alt="logo" />
            </div>
            <div class="h-stack search-bar-container">
                <button v-show="searchFocus" v-on:click="() => {
                    searchFocus = false;
                    // if (!filterData.search && !filterData.search.length) {
                    // 	filter();
                    // }
                    if (filterData.search != filterData.search_text) {
                        filterData.search = filterData.search_text
                    }
                }">
                    <Icon name="bi:chevron-left" size="25"></Icon>
                </button>
                <div class="h-stack search-input-content-container">
                    <button class="search-button">
                        <Icon name="iconoir:search" size="25" v-show="!(refreshing || searchProductLoading)" />
                        <Icon name="eos-icons:loading" size="25" v-show="refreshing || searchProductLoading" />
                    </button>

                    <input type="search" name="search-text" placeholder="Tìm kiếm..." class="search-input-container"
                    :maxlength="appConst.max_text_short"
                        v-on:click="() => {
                            searchFocus = true;
                        }" :value="filterData.search" v-on:input="(e: any) => {
                            filterData = {
                                ...filterData,
                                search: e.target.value
                            };
                            getSearchProductResult();
                        }" autoComplete="off" v-on:keypress.enter="() => {
                            searchFocus = false;
                            filterData = {
                                ...filterData,
                                search_text: filterData.search,
                            };
                            filter();
                            blurSearch();
                        }" id="search_input" />
                    <button class="clear-button" v-show="filterData.search && filterData.search.length" v-on:click="() => {
                        filterData = {
                            ...filterData,
                            search: ''
                        }
                        // searchProductSuggest = [];
                        focusSearch()
                        // filter()
                    }">
                        <Icon name="iconamoon:sign-times" size="25" />
                    </button>
                    <!-- <button class="voice">
                        <Icon name="carbon:microphone-filled"></Icon>
                    </button> -->
                </div>
                <button class="cart-in-search" v-show="searchFocus" v-on:click="() => {
                    router.push(appRoute.CartComponent);
                }">
                    <Icon name="bi:basket2-fill" size="25"></Icon>
                    <em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length : "10+" }} </em>
                </button>
            </div>

        </div>

        <div class="filter-result-container">
            <div class="search-result" id="search-result" :class="{ 'show': searchFocus }" v-on:scroll="(e: any) => {
                listSuggestScroll(e);
            }">
                <div class="history-search" v-if="searchTextSuggest && searchTextSuggest.length">
                    <span>Gợi ý:</span>

                    <div class="list-history-search">
                        <!-- <button v-for="(itemSearch, indexTab) in searchHistory" v-on:click="() => {
                        filterData = {
                            ...filterData,
                            search: itemSearch
                        };
                        getSearchProductResult();
                    }">
                            {{ itemSearch }}
                        </button> -->
                        <button v-for="(itemSearch, indexTab) in searchTextSuggest" v-on:click="() => {
                            filterData = {
                                ...filterData,
                                search: itemSearch
                            };
                            getSearchProductResult();
                        }">
                            {{ itemSearch }}
                        </button>
                    </div>

                </div>
                <div class="categories-and-list-suggest">
                    <div class="categories" v-if="false">
                        <button class="item-category" v-for="itemCategory in dataCategory"
                            :class="{ 'active': checkCategoryFilter(itemCategory.id) }" v-on:click="async ($event: any) => {
                                setCategoryFilter(itemCategory.id);
                                // filter();
                            }">
                            <img loading="lazy" :src="domainImage + itemCategory.profile_picture" alt=""
                                v-if="itemCategory.profile_picture" />
                            <Icon name="carbon:data-categorical" v-else></Icon>
                            <span>{{ itemCategory.name }}</span>
                        </button>
                    </div>
                    <div class="list-search-suggest-container">
                        <div class="list-search-suggest">
                            <div class="empty-search"
                                v-if="filterData.search && filterData.search.length && (!searchProductSuggest || !searchProductSuggest.length) && !searchProductLoading">
                                <img loading="lazy" class="empty-image" :src="none_result" :placeholder="none_result" />
                                <p class="empty-text">Không tìm thấy kết quả phù hợp</p>
                            </div>
                            <div class="search-placeholder"
                                v-if="!(filterData.search && filterData.search.length) && (!searchProductSuggest || !searchProductSuggest.length)">
                                <img loading="lazy" :src="none_result_2" :placeholder="none_result_2" />
                            </div>
                            <nuxt-link :to="appRoute.ProductComponent + '/' + (itemSuggest.slug?.length ? itemSuggest.slug : itemSuggest.id)"
                                v-if="searchProductSuggest && searchProductSuggest.length"
                                v-for="itemSuggest of searchProductSuggest" class="search-result-item-container"
                                :title="itemSuggest.name">
                                <img loading="lazy" :src="itemSuggest &&
                                    itemSuggest.profile_picture &&
                                    itemSuggest.profile_picture.length
                                    ? domainImage + itemSuggest.profile_picture
                                    : icon_for_product" :placeholder="icon_for_product" :alt="itemSuggest.name" />

                                <div class="items-suggest-content">
                                    <span class="name">{{ itemSuggest.name }}</span>
                                    <span class="shop-name" v-show="itemSuggest.shop">{{ itemSuggest.shop?.name
                                        }}</span>
                                    <span class="price">
                                        {{
                                            (itemSuggest.price_off != null && itemSuggest.price_off < itemSuggest.price) ?
                                                formatCurrency(parseFloat(itemSuggest.price_off), itemSuggest.shop ?
                                                    itemSuggest.shop.currency : itemSuggest.currency) :
                                                (parseFloat(itemSuggest.price) == 0 || itemSuggest.price == null)
                                                    ? 'Giá liên hệ' : formatCurrency(parseFloat(itemSuggest.price),
                                                        itemSuggest.shop ? itemSuggest.shop.currency : itemSuggest.currency) }}
                                            </span>
                                            <span class="origin-price"
                                                v-if="(itemSuggest.price_off != null && itemSuggest.price_off < itemSuggest.price)">
                                                {{
                                                    (parseFloat(itemSuggest.price) == 0 || itemSuggest.price ==
                                                        null)
                                                        ? 'Giá liên hệ'
                                                        : formatCurrency(parseFloat(itemSuggest.price), itemSuggest.shop ?
                                                            itemSuggest.shop.currency : itemSuggest.currency)
                                                }}
                                            </span>
                                            <button class="add-to-cart">
                                                Thêm vào giỏ
                                            </button>
                                </div>

                            </nuxt-link>

                            <div class="loading-more" v-if="searchProductLoadingMore == true">
                                Loading...
                            </div>
                            <div id="last_of_list_suggest"></div>
                            <span class="showed-all"
                                v-if="searchProductSuggest && searchProductSuggest.length && (searchProductSuggest.length >= searchProductSuggestCount || searchSuggestAll)">
                                Đã hiển thị toàn bộ kết quả
                            </span>
                        </div>
                    </div>
                </div>



            </div>

            <div class="list-result" :class="{ 'show-search': searchFocus }" id="list-result" v-on:scroll="(e: any) => {
                listScroll(e);
            }">
                <div class="empty-search" v-if="!(filterResultList && filterResultList.length)">
                    <img loading="lazy" class="empty-image" :src="none_result" :placeholder="none_result" />
                    <p class="empty-text">Không tìm thấy kết quả phù hợp</p>
                </div>
                <div v-if="filterResultList && filterResultList.length" v-for="itemResult of filterResultList"
                    :key="itemResult.id" class="v-stack result-item-container">
                    <div class="v-stack product-content">
                        <div class="h-stack product-detail">
                            <nuxt-link :to="appRoute.ProductComponent + '/' + (itemResult.slug?.length ? itemResult.slug : itemResult.id)" :title="itemResult.name">
                                <img loading="lazy" :src="itemResult && itemResult.profile_picture
                                    ? domainImage + itemResult.profile_picture
                                    : icon_for_product
                                    " :placeholder="icon_for_product" :alt="itemResult.name"
                                    :title="itemResult.name" />
                                <div class="v-stack product-info">
                                    <p class="product-name">{{ itemResult.name }}</p>
                                    <div class="h-stack">
                                        <div class="v-stack">
                                            <p class="product-price">
                                                {{
                                                    (itemResult.price_off != null && itemResult.price_off <
                                                        itemResult.price) ? formatCurrency(parseFloat(itemResult.price_off),
                                                            itemResult.shop ? itemResult.shop.currency : itemResult.currency) :
                                                        (parseFloat(itemResult.price) == 0 || itemResult.price == null)
                                                            ? 'Giá liên hệ' : formatCurrency(parseFloat(itemResult.price),
                                                                itemResult.shop ? itemResult.shop.currency : itemResult.currency) }}
                                                    </p>
                                                    <p class="product-origin-price"
                                                        v-if="(itemResult.price_off != null && itemResult.price_off < itemResult.price)">
                                                        {{ (parseFloat(itemResult.price) == 0 ||
                                                            itemResult.price == null)
                                                            ? 'Giá liên hệ'
                                                            : formatCurrency(parseFloat(itemResult.price), itemResult.shop ?
                                                                itemResult.shop.currency : itemResult.currency) }}
                                                    </p>
                                        </div>
                                        <button class="add-to-cart" v-on:click="async () => {
                                            selectedProduct = itemResult;
                                            showProductSelected = true;
                                        }" v-on:click.stop="(e) => {
                                            e.preventDefault()
                                        }">
                                            Thêm vào giỏ
                                        </button>
                                    </div>

                                </div>
                            </nuxt-link>

                        </div>
                        <div class="h-stack shop-detail">
                            <nuxt-link
                                :to="appRoute.DetailShopComponent + '/' + (itemResult.shop.slug ? itemResult.shop.slug : itemResult.shop.id)"
                                :title="itemResult.shop.name">
                                <span class="shop-name">
                                    <Icon name="emojione-v1:shopping-bags"></Icon>
                                    {{ itemResult.shop.name }}
                                </span>
                                <span class="shop-mark" v-if="itemResult.shop.mark">{{ itemResult.shop.mark }}</span>
                                <span class="shop-distance">khoảng cách: {{ itemResult.distance }}</span>
                            </nuxt-link>

                        </div>
                    </div>
                </div>
                <div id="last_of_list"></div>
                <div class="loading-more" v-if="loadingMore == true">
                    Loading...
                </div>
                <span class="showed-all"
                    v-if="filterResultList && filterResultList.length && (filterResultList.length >= filterResultListCount)">
                    Đã hiển thị toàn bộ kết quả
                </span>

                <v-overlay v-model="refreshing" :z-index="100" :absolute="false" contained
                    content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
                    <Icon name="eos-icons:loading"></Icon>
                </v-overlay>
            </div>


        </div>


    </div>
    <AddProductToCartComponent v-if="showProductSelected" v-on:close="() => {
        showProductSelected = false
    }" :selectedProduct="selectedProduct">
    </AddProductToCartComponent>
</template>
<style lang="scss" src="./SearchStyles.scss"></style>

<script lang="ts" setup>
import { VueFinalModal } from "vue-final-modal";
import logo from "~/assets/image_13_3_2024/logo.png"
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import shop_banner from "~/assets/image/remagan-banner-19_1.png";
import icon_for_product from "~/assets/image/icon-for-product.png";
import marker_location_icon from "~/assets/image/marker-location.png";
import none_result from "~/assets/image/none-result.webp";
import none_result_2 from "~/assets/image/none-result-2.webp"
import { appRoute } from '~/assets/appRoute';
import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber } from "~/assets/AppConst";
import { PublicService } from "~/services/publicService/publicService";
import { PlaceService } from "~/services/placeService/placeService";
import { toast } from "vue3-toastify";
import type { CartDto } from "~/assets/appDTO";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "../confirm18Age/Confirm18AgeComponent.vue";
import { HttpStatusCode } from "axios";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
useHead({
    title: t('AppRouteTitle.SearchComponent'),
    meta: [
        {
            name: "title",
            content: t('AppRouteTitle.SearchComponent'),
        },
    ],
});
useSeoMeta({
    title: t('AppRouteTitle.SearchComponent'),
    ogTitle: t('AppRouteTitle.SearchComponent'),
    description: t('AppRouteTitle.SearchComponent'),
    ogDescription: t('AppRouteTitle.SearchComponent'),
    ogImage: baseLogoUrl,
    ogImageHeight: 400,
    ogImageWidth: 720,
    ogUrl: "https://remagan.com",
    ogType: "website",
});

var dataCategory = ref(appDataStartup.listCategory) as any;

var address = ref("");
var refreshing = ref(false);
var selectedShop = ref();
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref(false);
var searchSuggestAll = ref(false);
var searchFocus = ref(true);
var searchHistory = ref([] as any[]);
var cartData = ref();
var filterData = ref({
    search: "",
    search_text: "",
    latitude_user: 0,
    longitude_user: 0,
    latitude_s: "",
    latitude_b: "",
    longitude_s: "",
    longitude_b: "",
    limit: 20,
    offset: 0,
    category_ids: [] as any,
    sortBy: 1,
    filter_type: 2,
    map_data: true
});
var justFilter = ref(true);
var isFirstLoad = ref(true);
var filterResultList = ref();
var filterResultListCount = ref(0);
var latitude = ref();
var longitude = ref();

var loadMore: any;
var loadingMore = ref(false);
var searchProductTimeout: any;
var searchProductSuggest = ref();
var searchProductSuggestCount = ref();
var searchTextSuggest = ref([] as any[]);
var showProductSelected = ref(false);
var selectedProduct = ref();

var publicService = new PublicService();
var placeService = new PlaceService();
onUnmounted(() => {
    let storageState = {
        filterData: filterData.value,
        searchProductSuggest: searchProductSuggest.value,
        searchProductSuggestCount: searchProductSuggestCount.value,
        filterResultList: filterResultList.value,
        filterResultListCount: filterResultListCount.value,
        searchTextSuggest: searchTextSuggest.value
    }
    sessionStorage.setItem(appConst.storageKey.stateRestore.SearchComponent, JSON.stringify(storageState));
    // localStorage.setItem(appConst.storageKey.searchHistory, JSON.stringify(searchHistory.value));
})
onMounted(async () => {
    window.addEventListener('beforeunload', async () => {
        refresh()
    });
    let queryFilter = route.query?.filter ? route.query.filter : null;
    let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.SearchComponent) as string);
    if (queryFilter) {
        filterData.value = JSON.parse(route.query.filter as string);
    }
    else if (store) {
        filterData.value = store.filterData;
    }
    if (store) {
        searchProductSuggest.value = store.searchProductSuggest;
        searchProductSuggestCount.value = store.searchProductSuggestCount;
        filterResultList.value = store.filterResultList;
        filterResultListCount.value = store.filterResultListCount;
        searchTextSuggest.value = store.searchTextSuggest;
        if (store.searchFocus) {
            focusSearch()
        }
        else blurSearch()
    }
    // searchHistory.value = JSON.parse(localStorage.getItem(appConst.storageKey.searchHistory) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.searchHistory) as string) : []
    if (!(searchProductSuggest.value && searchProductSuggest.value.length)) getSearchProductResult()
    nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
        dataCategory.value = appDataStartup.listCategory;
    })
    try {
        // let userLocation = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);

        // // if (nuxtApp.$messaging) {
        // // 	let token = await getToken(nuxtApp.$messaging);
        // // 	console.log(token);
        // // }
        // if (userLocation && userLocation.latitude_user && userLocation.longitude_user) {
        //     filterData.value = {
        //         ...filterData.value,
        //         latitude_user: userLocation.latitude_user,
        //         longitude_user: userLocation.longitude_user
        //     }
        //     getUserAddress();
        // }
        // else {
        await getCurrentLocationOfUser();
        // }
    }
    finally {
        if (!(filterResultList.value && filterResultList.value.length)) filter();
    }
    if (justFilter.value) {
        document
            .getElementById("list-result")
            ?.scrollTo({ top: 0, behavior: "smooth" });
        justFilter.value = false;
    }
    // document.title = appRouteTitle.HomeComponent;
    let cartData$ = JSON.parse(
        localStorage.getItem(appConst.storageKey.cart) as string
    );

    cartData.value = cartData$ ? cartData$ : [];
    focusSearch();
    // if (nuxtApp.$messaging) {
    // 	let token = await getToken(nuxtApp.$messaging);
    // 	console.log(token);
    // }


})

function refresh() {
    sessionStorage.removeItem(appConst.storageKey.stateRestore.SearchComponent);
}

function filter(offset = 0, limit = 20) {
    refreshing.value = true;
    filterData.value.map_data = true;
    filterData.value.offset = offset;
    filterData.value.filter_type = 2;

    router.replace({
        query: {
            ...route.query,
            filter: JSON.stringify(filterData.value)
        }
    })
    if (filterData.value.search && filterData.value.search.length) {
        publicService
            .filter(filterData.value)
            .then((res) => {
                if (res.status && res.status == HttpStatusCode.Ok) {
                    refreshing.value = false;
                    isFirstLoad.value = false;
                    justFilter.value = true;
                    filterResultList.value = res.body.data.result;
                    filterResultListCount.value = res.body.data.count;
                    document
                        .getElementById("list-result")
                        ?.scrollTo({ top: 0, behavior: "smooth" });
                }
            })
            .catch((err) => {
                console.error("lỗi list: ", err);
                refreshing.value = false;
                isFirstLoad.value = false;
            });
    }
    else {
        refreshing.value = false;
        isFirstLoad.value = false;
        justFilter.value = true;
    }

}
function getCurrentLocationOfUser() {
    if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                filterData.value = {
                    ...filterData.value,
                    latitude_user: position.coords.latitude,
                    longitude_user: position.coords.longitude
                }
                getUserAddress();
                return;
            },
            (error) => {
                filterData.value = {
                    ...filterData.value,
                    latitude_user: appConst.defaultCoordinate.latitude,
                    longitude_user: appConst.defaultCoordinate.longitude
                }
                getUserAddress();
                return;
            },
            {
                enableHighAccuracy: false, // Use less accurate but faster methods
                timeout: 5000, // Set a timeout (in milliseconds)
                
            }
        );
    }
}
function getSearchProductResult() {
    searchProductLoading.value = true;
    clearTimeout(searchProductTimeout);
    if (filterData.value.search && filterData.value.search.length) {
        searchProductTimeout = setTimeout(() => {
            publicService
                .searchSuggest(filterData.value.search, undefined, undefined, filterData.value.latitude_user, filterData.value.longitude_user)
                .then((res) => {
                    if (filterData.value.search && filterData.value.search.length) {
                        let indexHistory = searchHistory.value.indexOf(filterData.value.search);
                        if (indexHistory == -1) {
                            searchHistory.value = [
                                filterData.value.search,
                                ...searchHistory.value,
                            ].slice(0, 10);
                        }
                    }

                    if (res.status == HttpStatusCode.Ok) {
                        searchProductSuggest.value = res.body.data.products;
                        searchProductSuggestCount.value = res.body.data.count;
                        searchTextSuggest.value = res.body.data.strings;
                        searchProductLoading.value = false;
                        document.getElementById("search-result")?.scrollTo({ top: 0 });
                        searchSuggestAll.value = false;
                    }
                })
                .catch(() => {
                    searchProductLoading.value = false;
                });
        }, 1000);
    } else {
        searchProductLoading.value = false;
    }
}

function loadMoreProductSearching() {
    if (
        searchProductSuggest.value.length < searchProductSuggestCount.value &&
        !searchSuggestAll.value && !searchProductLoadingMore.value
    ) {
        searchProductLoadingMore.value = true;
        clearTimeout(searchProductTimeout);
        searchProductTimeout = setTimeout(() => {
            publicService
                .searchSuggest(
                    filterData.value.search,
                    searchProductSuggest.value.length,
                    25,
                    filterData.value.latitude_user, filterData.value.longitude_user
                )
                .then((res) => {
                    if (res.status == HttpStatusCode.Ok) {
                        if (res.body.data && res.body.data.result.length > 0) {
                            searchProductSuggest.value = [
                                ...searchProductSuggest.value,
                                ...res.body.data.products,
                            ];
                        } else {
                            searchSuggestAll.value = true;
                        }
                        searchProductLoadingMore.value = false;
                    }
                })
                .catch(() => {
                    searchProductLoadingMore.value = false;
                });
        }, 1000);
    }
}

function loadMoreFilterResult() {
    if (filterResultList.value.length < filterResultListCount.value && !loadingMore.value) {
        clearTimeout(loadMore);
        // this.setState({ loadMore: true });
        loadingMore.value = true;
        filterData.value = {
            ...filterData.value,
            offset: filterResultList.value.length
        }

        loadMore = setTimeout(() => {

            publicService
                .filter(filterData.value)
                .then((res) => {
                    if (res.status && res.status == HttpStatusCode.Ok) {
                        filterResultList.value = [
                            ...filterResultList.value,
                            ...res.body.data.result
                        ];
                    }
                    loadingMore.value = false;
                })
                .catch((err) => {
                    loadingMore.value = false;
                    console.error("lỗi list: ", err);
                });
        }, 500);
    }
}

function getUserAddress() {
    placeService
        .myGeocoderByLatLngToAddress(filterData.value.latitude_user, filterData.value.longitude_user)
        .then((res: any) => {
            if (res.body.data && res.body.data.length) {
                address.value = res.body.data[0].address
                    ? res.body.data[0].address
                    : "";
            }
        });
}
function checkCategoryFilter(id: any) {
    if (
        filterData.value.category_ids &&
        filterData.value.category_ids.indexOf(id) != -1
    )
        return true;
    return false;
}
function setCategoryFilter(id: any) {
    let arr: any = filterData.value.category_ids || [];
    let index = arr.indexOf(id);
    if (index == -1) {
        arr.push(id);
    } else {
        arr.splice(index, 1);
    }
    filterData.value = {
        ...filterData.value,
        category_ids: arr,
    };
}
function listScroll(event: any) {
    let el = document
        .getElementById("last_of_list")
        ?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 10) {
        loadMoreFilterResult()
    }
}
function listSuggestScroll(event: any) {
    let el = document
        .getElementById("last_of_list_suggest")
        ?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 10) {
        if (searchProductSuggest.value.length < searchProductSuggestCount.value) {
            loadMoreProductSearching();
        }
    }
}
function focusSearch() {
    document.getElementById('search_input')?.focus();
    searchFocus.value = true;
}
function blurSearch() {
    document.getElementById('search_input')?.blur();
    searchFocus.value = false;
}
function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}
</script>