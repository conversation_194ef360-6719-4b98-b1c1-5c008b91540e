import axios from "axios";
import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";

export class StockService extends BaseHTTPService {
    stockImportCancelToken: any;
    stockExportCancelToken: any;
    stockWasteCancelToken: any;
    stockHistoryCancelToken: any;
    stockDailySummaryCancelToken: any;
    stockMyShopsSummaryCancelToken: any;

    /**
     * Record stock import/purchase
     * @param body - Import data including product_id, quantity, unit, purchase_price, supplier_info, notes, receipt_image
     */
    stockImport(body: any) {
        if (typeof (this.stockImportCancelToken) != typeof undefined) {
            this.stockImportCancelToken.cancel()
        }
        this.stockImportCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockImport;
        return this.https('POST', url, body, this.stockImportCancelToken.token, true);
    }

    /**
     * Record stock export/sale
     * @param body - Export data including product_id, quantity, sale_price, order_id (optional), notes
     */
    stockExport(body: any) {
        if (typeof (this.stockExportCancelToken) != typeof undefined) {
            this.stockExportCancelToken.cancel()
        }
        this.stockExportCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockExport;
        return this.https('POST', url, body, this.stockExportCancelToken.token, true);
    }

    /**
     * Record stock waste/loss
     * @param body - Waste data including product_id, quantity, reason, photo, notes
     */
    stockWaste(body: any) {
        if (typeof (this.stockWasteCancelToken) != typeof undefined) {
            this.stockWasteCancelToken.cancel()
        }
        this.stockWasteCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockWaste;
        return this.https('POST', url, body, this.stockWasteCancelToken.token, true);
    }

    /**
     * Get stock history for a specific product
     * @param productId - Product ID to get history for
     * @param offset - Pagination offset
     * @param limit - Pagination limit
     */
    getStockHistory(productId: string, offset = 0, limit = 20) {
        if (typeof (this.stockHistoryCancelToken) != typeof undefined) {
            this.stockHistoryCancelToken.cancel()
        }
        this.stockHistoryCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockHistory + '/' + productId + '?offset=' + offset + '&limit=' + limit;
        return this.https('GET', url, null, this.stockHistoryCancelToken.token, true);
    }

    /**
     * Get daily stock summary report
     * @param body - Filter data including date_from, date_to, shop_id (optional)
     */
    getDailySummary(body: any) {
        if (typeof (this.stockDailySummaryCancelToken) != typeof undefined) {
            this.stockDailySummaryCancelToken.cancel()
        }
        this.stockDailySummaryCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockDailySummary;
        return this.https('POST', url, body, this.stockDailySummaryCancelToken.token, true);
    }

    /**
     * Get user's shops stock overview summary
     */
    getMyShopsSummary() {
        if (typeof (this.stockMyShopsSummaryCancelToken) != typeof undefined) {
            this.stockMyShopsSummaryCancelToken.cancel()
        }
        this.stockMyShopsSummaryCancelToken = axios.CancelToken.source();
        
        let url = appConst.apiURL.stockMyShopsSummary;
        return this.https('GET', url, null, this.stockMyShopsSummaryCancelToken.token, true);
    }

    /**
     * Get current stock levels for shop products
     * @param shopId - Shop ID to get stock for
     * @param offset - Pagination offset
     * @param limit - Pagination limit
     */
    getCurrentStock(shopId: string, offset = 0, limit = 20) {
        let body = {
            shop_id: shopId,
            offset: offset,
            limit: limit
        }
        
        let url = appConst.apiURL.getProductsByShopId;
        return this.https('POST', url, body, null, true);
    }

    /**
     * Search products for stock operations
     * @param shopId - Shop ID to search products in
     * @param searchText - Search term
     * @param offset - Pagination offset
     * @param limit - Pagination limit
     */
    searchProducts(shopId: string, searchText: string, offset = 0, limit = 20) {
        let body = {
            shop_id: shopId,
            search: searchText,
            offset: offset,
            limit: limit
        }
        
        let url = appConst.apiURL.getProductsByShopId;
        return this.https('POST', url, body, null, true);
    }
}
