<template>
  <div class='title-v2-header' id="header_remagan">
    <div class="header-left">
      <slot name="header_left" v-if="$slots.header_left"></slot>
      <nuxt-link :to="appRoute.HomeComponent" v-else v-on:click="() => {
        if (route.path.includes(appRoute.HomeComponent)) {
          nuxtApp.$emit(appConst.event_key.refresh_home)
        }
      }">
        <img class="logo-header" :src="logo_v2" alt="" />
      </nuxt-link>
    </div>
    <h3 v-if="props.title?.length">{{ props.title }}</h3>
    <div class="header-right">
      <slot name="header_right" v-if="$slots.header_right"></slot>
      <slot v-else>
        <nuxt-link
          :to="userInfo?.role_id == appConst.role_enum.agent ? appRoute.ManageShopsComponent : appRoute.MyShopComponent">
          <button class="open-shop-button">
            <!-- <div class="label-button">
              <span class="primary">{{ t('HeaderV2Component.mo_shop') }}</span>
              <span class="secondary">
                {{ t('HeaderV2Component.mien_phi') }}
              </span>
            </div> -->
            <Icon name="solar:shop-2-bold"></Icon>
          </button>

        </nuxt-link>
        <button class="notification" v-on:click="() => {
          showNotificationModal = true
        }">
          <v-badge :content="unreadCount" class="notify-badge" :color="'var(--primary-color-2)'">
            <Icon name="ic:sharp-circle-notifications"></Icon>
          </v-badge>
        </button>
      </slot>
    </div>

    <v-overlay v-model="showNotificationModal" location="bottom" :z-index="1101" v-on:click:outside="() => {
      showNotificationModal = false
    }" key="show_product_select" class="notifications-overlay-container" persistent
      content-class='notifications-content-container' no-click-animation>
      <div class="noti-label">{{ $t('NotificationsComponent.thong_bao') }}
        <button class="refresh-noti" :disabled="refreshing || loadingMore" v-on:click="(e:any) => {
          e.stopPropagation();
          refreshListNotifications();
        }">
          <Icon :name="refreshing ? 'eos-icons:loading' : 'material-symbols:refresh'"></Icon>
        </button>
        <button class="close-noti" v-on:click="() => {
          showNotificationModal = false
        }">
          <Icon name="clarity:times-line" size="25"></Icon>
        </button>
      </div>
      <NotificationsComponent :notificationData="JSON.parse(JSON.stringify(notificationData))" :count="count" :loadingMore="loadingMore" :goUp="goToTopListNoti"
        :unread_count="unreadCount" v-on:unread_change="(e: any) => {
          handleUnreadChange(e)
        }" v-on:load_more_noti="async () => {
          if (notificationData.length < count) {
            loadingMore = true;
            await getMoreNotifications();
            loadingMore = false;
          }
        }"></NotificationsComponent>
    </v-overlay>
  </div>
</template>

<style lang="scss" src="./HeaderV2Styles.scss"></style>

<script setup lang="ts">
import logo_v2 from "~/assets/imageV2/remagan-logo-v2.png";

import { appRoute } from '~/assets/appRoute';
import { appConst } from "~/assets/AppConst";
import { AuthService } from "~/services/authService/authService";
import { UserService } from "~/services/userService/userService";
import { HttpStatusCode } from "axios";

const props = defineProps({
  title: null,
  header_level_2_id: null,
  last_element_id: null
})
const emit = defineEmits([
  'refresh'
])

var authService = new AuthService();
var userService = new UserService();

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

const { t } = useI18n();

let lastScrollTop = ref(0);
var windowInnerWidth = ref();

var userInfo = ref();

var showNotificationModal = useState('show_noti_modal', () => { return false });
var notificationData = useState<any>('user_notification', () => { return [] });
var unreadCount = useState('user_notify_unread', () => { return 0 });
var count = ref(0);
var loadingMore = ref(false);
var refreshing = ref(false);
var goToTopListNoti = ref(0);
onBeforeMount(async () => {
  userInfo.value = await authService.checkAuth();
  nuxtApp.$listen(appConst.event_key.scrolling, (e) => {
    showHideHeader(e);
  })

  nuxtApp.$listen(appConst.event_key.check_user_notification, getListNotifications);
  getListNotifications();

  nuxtApp.$listen(appConst.event_key.login, async () => {
    userInfo.value = await authService.checkAuth();
    getListNotifications();
  })
  nuxtApp.$listen(appConst.event_key.logout, async () => {
    userInfo.value = await authService.checkAuth();
    getListNotifications();
  })
})

onBeforeUnmount(() => {
  nuxtApp.$unsubscribe(appConst.event_key.scrolling)
  nuxtApp.$unsubscribe(appConst.event_key.check_user_notification, getListNotifications);
  nuxtApp.$unsubscribe(appConst.event_key.login);
  nuxtApp.$unsubscribe(appConst.event_key.logout);
})

onMounted(() => {
  window.addEventListener('resize', () => {
    windowInnerWidth.value = window.innerWidth;
  })
  windowInnerWidth.value = window.innerWidth;
})

function showHideHeader(event: any) {
  let el = document.getElementById(props.last_element_id)?.getBoundingClientRect().bottom;
  let scrollTop = el || 0;

  if (scrollTop > lastScrollTop.value && checkWindowWidthMax(1024)) {
    console.log('Scrolling down');
    document.getElementById(props.header_level_2_id)?.classList.add('top-55');
    document.getElementById('header_remagan')?.classList.add('sticky');
  }

  else {
    console.log('Scrolling up');

    document.getElementById(props.header_level_2_id)?.classList.remove('top-55');
    document.getElementById('header_remagan')?.classList.remove('sticky');
  }

  lastScrollTop.value = Math.max(scrollTop, 0);
}
function checkWindowWidthMax(width: any) {
  if (windowInnerWidth.value <= width) return true;
  return false;
}

const getListNotifications = () => {
  return new Promise((resolve) => {
    if (userInfo.value?.id) {
      userService.listNotify(0, notificationData.value?.length > 20 ? notificationData.value?.length : 20).then((res) => {
        if (res.status == HttpStatusCode.Ok) {
          notificationData.value = JSON.parse(JSON.stringify(res.body.data));
          count.value = res.body.count;
          unreadCount.value = res.body.unread;
          resolve(notificationData.value)
        }
        else {
          notificationData.value = [];
          count.value = 0;
          unreadCount.value = 0
          resolve(null);
        }
      }).catch(() => {
        notificationData.value = [];
        count.value = 0;
        unreadCount.value = 0
        resolve(null);
      })
    }
    else {
      notificationData.value = [];
      count.value = 0;
      unreadCount.value = 0
      resolve(null);
    }

  })
}

async function refreshListNotifications(){
  refreshing.value = true;
  await getListNotifications();
  goToTopListNoti.value = goToTopListNoti.value + 1;
  refreshing.value = false;
  
}

function getMoreNotifications() {
  return new Promise((resolve) => {
    userService.listNotify(notificationData.value?.length).then((res) => {
      if (res.status == HttpStatusCode.Ok) {
        notificationData.value = [...notificationData.value, ...res.body.data]
        count.value = res.body.count;
        unreadCount.value = res.body.unread;
        resolve(notificationData.value)
      }
    })
  })
}

function handleUnreadChange(data: any) {
  getListNotifications()
  // if (data.item == 'all') {
  //   getListNotifications()
  // }
  // else {
  //   let indexChange = notificationData.value.findIndex(function (e: any) {
  //     return e.id == data.item?.id
  //   })

  //   if (indexChange != -1) {
  //     notificationData.value[indexChange] = { ...data.item };
  //   }
  //   unreadCount.value = data.unread_count;
  //   count.value = data.count;
  // }

}
</script>
