.around-container {
  // width: 100%;
  position: relative;
  height: 100%;
  flex: 1;
  display: flex;
  /* min-height: inherit; */
  border-radius: 10px 10px 0 0;
  max-width: var(--max-width-content-view-1024) !important;
  background: white;
  // margin: 0;
  // overflow: auto;
  padding: 0 !important;
  // display: flex;
  flex-direction: column;
  // position: relative;
  z-index: 1;

  &>.around-sticky-header {
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    z-index: 1000;

    // position: fixed;
    // top: 0;
    // left: 0;
    // z-index: 100;
    // width: 100%;
    &>.sub-title-v2-header {
      text-transform: uppercase;
      position: relative;
    }

    &>.sub-title-v2-header.searching {
      & .header-middle {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 40px;
        padding: 0 5px;
        width: 100%;

        & .searching-container {
          color: #545454;
          background: white;
          padding: 0 10px;
          display: flex;
          height: 100%;
          border-radius: 5px;
          width: 100%;
          text-transform: none;
          cursor: pointer;

          &>.searching-text {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            font-size: 15px;
          }
        }
      }
    }

    &>.categories-header {
      background: white;
      display: flex;
      align-items: center;
      padding: 5px 0;
      border-bottom: thin solid #f4f4f4;
      user-select: none;

      &>.categories-carousel {
        padding: 0 10px;

        & .category-item-slide {
          width: fit-content !important;
          padding: 1px;
          color: #545454;
          font-size: 15px;
          font-weight: 400;
          cursor: pointer;
        }

        & .category-item-slide.active {
          // color: var(--primary-color-2);
          color: black;
          text-decoration: underline;
        }
      }
    }

    &>.filter-options-header {
      background: white;
      display: flex;
      align-items: center;
      padding: 0px 10px 0 0;
      border-bottom: thin solid #f4f4f4;
      user-select: none;

      &>.short-filter-button {
        color: var(--primary-color-1);
        display: flex;
        align-items: center;
        height: 30px;
        padding: 0 15px;
        align-items: center;
        justify-content: center;
      }

      &>.filter-options-carousel {
        padding: 10px 10px 10px 0;

        & .filter-option-slide {
          width: fit-content !important;
          color: black;
          font-size: 15px;
          font-weight: 400;
          cursor: pointer;
          padding: 2px 10px;
          border: thin solid #d8d8d8;
          border-radius: 2em;
          font-size: 14px;

          &>.tab-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;

            & em {
              font-style: normal;
            }

            & svg {
              color: var(--primary-color-1);
              font-size: 20px;
            }
          }
        }

        & .filter-option-slide.active {
          background: var(--primary-color-1);
          color: white;

          & svg {
            color: white;
          }
        }
      }
    }
  }

  &>.around-content-container {
    height: 100%;
    min-height: 100%;
    flex: 1;
    background: white;
    margin: 0;
    // overflow: auto;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  &>.around-content-container.show-search {
    overflow: hidden;
  }

  // display: flex;
  // flex-direction: column;
  // flex: 1;
  // background-color: white;
  // position: relative;
  // padding: 10px;
  // overflow: auto;
  // font-size: calc(var(--font-size) * 1.3);
  // background-color: var(--color-background-2);

  & .location-and-search {
    width: 100%;
    background-size: 100%;
    // background-image: url("~/assets/image_13_3_2024/patter.jpg");
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 18px;
    transition: min-height 0.5s ease;

    .background-image {
      width: 200%;
      aspect-ratio: 1 / 1;
      position: absolute;
      background-position: 50% 50%;
      top: -150%;
      background-image: url("~/assets/image/imgpsh_fullsize_anim.jpg");
      animation: fullrotate 5s infinite ease-out;
      background-size: 100%;

      @keyframes fullrotate {
        0% {
          -webkit-transform: rotate(0);
          transform: rotate(0);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    }

    .location-text {
      padding: 5px 20px;
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      text-align: left;
      width: 100%;
      color: white;

      &>img {
        margin: auto;
        height: 40px;
        filter: drop-shadow(0 0 1px white);
      }

      & .address {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }

    & .search-bar-container {
      position: relative;
      width: calc(100% - 80px);
      top: unset;
      transform: none;
      border-radius: 2em;
      padding: 0 5px 10px;
      width: 100%;

      &>button {
        width: 40px;
        height: 100%;
        color: white;
        font-size: 1em;
        position: relative;
      }

      &>.cart-in-search {
        animation: none;

        &>em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 15px;
          height: 15px;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          position: absolute;
          bottom: 0px;
          right: 0px;
          line-height: 1;
          box-shadow: 0 0 0px 1px white;
          font-weight: 500;

          &>span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        padding: 0 5px;
        box-shadow: 1px 1px 3px -1px;
        background: white;
        overflow: hidden;
        margin-left: auto;
      }

      & .search-input-container {
        height: 40px;
        border-color: transparent;
        outline: none;
        width: 100%;
      }
    }

    & .search-bar-container.search-focus {
      & .search-container {
        margin: unset;
      }
    }

    & .search-input-container {
      width: 100%;
      height: 50px;
      // background-color: #f5f6fa;
      // border-radius: 0 5px 5px 0 !important;
      border-color: transparent;
      outline: none;
    }

    & .search-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .clear-button {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .voice {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
      font-size: 1.25em;
    }
  }

  .location-value {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
  }

  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 40px;
    padding: 0 10px;
    aspect-ratio: 1;
    background: var(--primary-color-1);
    border-radius: 5px;
    color: white;
    border: none;
    font-size: 1.3em;
  }

  .filter-result-container.show-map {
    overflow: hidden;

    &>.leaflet-map-absolute {
      z-index: 100;
    }
  }

  .filter-result-container {
    display: flex;
    width: 100%;
    // height: 100%;
    flex: 1;
    position: relative;

    &>.leaflet-map-absolute {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      flex: 1;
      z-index: 1;
      width: 100%;
      height: 100%;

      &>.leaflet-map-relative {
        position: relative;
        display: flex;
        flex: 1;

        &>#leaflet_map {
          // flex: 1;
          // align-self: unsafe;
          // height: 100%;
          // // min-height: 450px;
          // outline: none;
          z-index: 1;
          font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system,
            Tahoma, "Segoe UI";
          position: absolute;

          & .custom-contol-bottom {
            margin-bottom: 125px;
          }
        }

        & .filter-current-center {
          position: absolute;
          top: 10px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1;
          color: white;
          font-weight: 700;
          padding: 5px 15px;
          border-radius: 2em;
          background: linear-gradient(to right,
              var(--primary-color-1),
              var(--linear-color-1));
          animation: none !important;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
        }

        & .filter-product-on-map-carousel {
          position: absolute;
          bottom: 5px;
          width: 100%;
          // padding: 10px 0;

          &>.my-carousel {
            padding: 10px 0;
          }

          & .filter-detail-slide {
            --line-height: 1.25;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 10px;
            padding: 25px 10px 10px;
            box-shadow: 0 0 4px rgb(0 0 0 / 25%);
            user-select: none;
            line-height: var(--line-height);

            &>.close-bar {
              position: absolute;
              top: 0;
              left: 50%;
              width: 30px;
              height: 30px;
              color: #595959;
              transform: translate(-50%, -10px);
              font-size: 25px;
              background: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 0 10px rgb(0, 0, 0, .2);
            }

            &>.item-name {
              --font-size: 15px;
              font-weight: 700;
              color: var(--primary-color-1);
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: var(--font-size);
              white-space: nowrap;
              min-height: calc(var(--font-size) * var(--line-height));
              line-height: 1;
              margin-top: 3px;
              max-width: fit-content;
              width: auto;
            }

            &>.item-address {
              --font-size: 13px;
              --line-height: 1.5;
              font-weight: 400;
              color: #8c8c8c;
              font-size: var(--font-size);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              min-height: calc(var(--font-size) * var(--line-height));
              margin-top: 5px;
              max-width: fit-content;
              width: auto;
            }

            &>.item-business-rating {
              display: flex;
              justify-content: space-between;
              align-items: flex-end;
              min-height: 25px;
              margin-bottom: 5px;

              &>.business-name {
                color: #545454;
                font-weight: 700;
                font-size: var(--font-size);
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &>.rating {
                --font-size: 17px;
                font-weight: 500;
                color: #545454;
                font-size: var(--font-size);
                display: flex;
                align-items: flex-end;
                margin-left: auto;
                min-width: fit-content;

                &>svg {
                  color: #ffe500;
                  font-size: 25px;
                  margin-right: 5px;
                }
              }
            }

            &>.shop-item-detail {
              display: flex;
              gap: 10px;
              margin-bottom: 10px;

              &>.shop-item-logo {
                box-shadow: 0 0 5px rgb(0, 0, 0, 20%);
                background: white;
                border-radius: 10px;
                cursor: pointer;

                &>.logo-origin-container {
                  transform: scale(calc(75 / var(--scale-origin)));
                }

                &>img {
                  min-width: 100%;
                  min-height: 100%;
                  object-fit: cover;
                }
              }

              &>.shop-item-info {
                display: flex;
                flex-direction: column;
                flex: 1;
                align-items: flex-start;
                overflow: hidden;


                &>.shop-item-name {
                  --font-size: 15px;
                  font-weight: 700;
                  color: var(--primary-color-1);
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-size: var(--font-size);
                  white-space: nowrap;
                  min-height: calc(var(--font-size) * var(--line-height));
                  display: flex;
                  align-items: center;
                  width: 100%;
                  width: auto;
                  max-width: fit-content;

                  &>a {
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  &>span {
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-align: left;
                  }

                  &>button.close {
                    color: #a8a7a7;
                    font-size: 20px;
                    margin-left: auto;
                    display: flex;
                  }
                }

                &>.shop-item-address {
                  --font-size: 13px;
                  --line-height: 1.5;
                  font-weight: 400;
                  color: #8c8c8c;
                  font-size: var(--font-size);
                  overflow: hidden;
                  width: 100%;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  min-height: calc(var(--font-size) * var(--line-height));
                  margin-top: 5px;
                  text-align: left;
                  width: auto;
                  max-width: 100%;
                }

                &>.shop-item-business-rating {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;
                  min-height: calc(var(--font-size) * var(--line-height));
                  width: 100%;
                  text-align: left;
                  height: 25px;

                  &>.business-name {
                    color: #545454;
                    font-weight: 700;
                    font-size: var(--font-size);
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  &>.rating {
                    --font-size: 17px;
                    font-weight: 500;
                    color: #545454;
                    font-size: var(--font-size);
                    display: flex;
                    align-items: flex-end;
                    margin-left: auto;
                    min-width: fit-content;

                    &>svg {
                      color: #ffe500;
                      font-size: 25px;
                      margin-right: 5px;
                    }
                  }
                }
              }
            }

            & .products {
              padding: 0 0;
              height: auto;
              max-height: 0;
              overflow: hidden;
              width: 100%;
              display: block;
              transition: height .5s ease-in-out;
              transition: max-height .5s ease-in-out;

              &.show {
                max-height: 100dvh;
                // margin-top: 10px;
              }

              &>.products-slide-inside-shop-carousel {
                display: flex;
                flex-direction: column;
                height: 100%;
                padding-right: 2px;
                display: flex;
                flex-direction: column;

                & .item-stack {
                  width: 100%;
                  height: 100%;
                  border-radius: 10px;
                  overflow: hidden;
                  display: flex;
                  flex-direction: column;
                  background-color: #ffffff;
                  border: thin solid #d8d8d8;

                  &>img {
                    flex: 1;
                    height: var(--item-stack-slide-width);
                    width: var(--item-stack-slide-width);
                    min-height: var(--item-stack-slide-width);
                    max-height: var(--item-stack-slide-width);
                    background: var(--color-background-2);
                    object-fit: cover;
                  }

                  &>.item-stack-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    padding: 10px;
                    text-align: left;
                    font-size: 15px;

                    &>a {
                      width: 100%;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    }

                    & .name {
                      font-weight: bold;
                      color: var(--primary-color-1);
                    }

                    & .sold-like-amount {
                      color: #a8a7a7;
                      font-size: 11px;
                      line-height: 13px;
                      min-height: 13px;
                    }

                    & .price-actions {
                      display: flex;
                      align-items: flex-end;
                      justify-content: space-between;
                      width: 100%;

                      & .price-container {
                        display: flex;
                        gap: 0;
                        flex-direction: column;
                        justify-content: flex-start;
                        text-align: left;
                        align-items: flex-start;
                        line-height: 1;
                      }

                      & .price {
                        white-space: nowrap;
                        font-weight: 900;
                        width: fit-content;
                        color: var(--primary-color-2);
                        font-size: 15px;
                      }

                      & .origin-price {
                        white-space: nowrap;
                        overflow: hidden;
                        text-decoration: line-through;
                        text-overflow: ellipsis;
                        font-weight: 400;
                        font-size: 13px;
                        min-height: calc(13px * 1);
                        color: #545454;
                        font-style: normal;
                        overflow: hidden;
                      }

                      & .origin-price.hide {
                        opacity: 0;
                      }

                      &>.add-to-cart {
                        color: var(--primary-color-2);
                        margin-left: auto;
                        margin-top: auto;
                        width: 25px;
                        min-width: 25px;
                        height: 25px;
                        display: flex;
                        font-size: 25px;
                        align-items: center;
                        justify-content: center;
                      }
                    }
                  }
                }

                & .selected-shop-products-carousel-pagination {
                  position: relative;
                  top: 0;
                  width: fit-content;
                  padding: 10px 0 0;
                  align-items: center;
                  display: flex;
                  justify-content: center;
                  width: 100%;

                  &>.swiper-pagination-bullet {
                    background: #a8a7a7;
                    width: 7px;
                    height: 7px;
                    opacity: 1;
                    border-radius: 2em;
                    margin: 0 2px;
                  }

                  &>.swiper-pagination-bullet[class*="active"] {
                    background: var(--primary-color-1);
                    width: 20px;
                  }
                }
              }

              & .stack-carousel {
                flex: 1;

                & .item-stack-slide {
                  // margin-right: 10px;
                  // padding: 0 5px !important;

                  --item-stack-slide-width: 150px;
                  width: var(--item-stack-slide-width);
                  padding: 0;
                  position: relative;
                  // border-radius: 0;
                  border: none;

                  @media screen and (min-width: 1321px) {
                    --item-stack-slide-width: 250px;
                  }

                  @media screen and (max-width: 1320px) and (min-width: 1200px) {
                    --item-stack-slide-width: 200px;
                  }

                  @media screen and (max-width: 1200px) and (min-width: 721px) {
                    --item-stack-slide-width: 175px;
                  }
                }

                & .item-stack-slide:last-child {
                  padding-right: 0;
                }
              }
            }

          }

          & .shop-carousel-item-slide {
            align-self: flex-end;
            position: relative;
          }
        }
      }
    }

    .search-result.show,
    .list-result.show {
      display: flex;
    }

    .list-result {
      z-index: 2;
      gap: 10px;
      padding: 0;
      flex-direction: column;
      // height: fit-content;
      // overflow: auto;
      flex: 1;
      width: 100%;
      min-height: 100%;
      display: none;
      background: #f4f4f4;
      padding: 10px 0 50px;
      // position: absolute;
      // top: 0;
      // left: 0;

      &>.empty-search,
      .search-placeholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        margin: auto;
      }

      & .search-placeholder>img {
        width: 200px;
        height: 200px;
        object-fit: cover;
      }

      & .showed-all {
        color: #626262;
        font-style: italic;
        width: 100%;
        text-align: center;
        padding: 20px 0;
      }

      & .filter-map-virtual-scroll {
        padding: 0 10px;
        & .v-virtual-scroll__item{
          margin-top: 10px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgb(0 0 0 / 20%);
        }
      } 
    }
  }

  .search-result {
    z-index: 1001;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: #f4f4f4;
    padding: 10px;

    &>.search-input-container {
      display: flex;
      flex-direction: column;
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 0 4px 0 rgb(0, 0, 0, 0.1);
      background: white;

      &>.search-container {
        border-radius: 5px;
        width: calc(100%);
        padding: 5px;
        border: thin solid #d9d9d9;
        background: white;
        overflow: hidden;
        display: flex;
        align-items: center;
        font-size: 15px;
        gap: 5px;

        &>.search-button,
        >.clear-button {
          color: #8c8c8c;
          font-size: 20px;
          display: flex;
        }

        &>input {
          width: 100%;
          outline: none;
        }
      }

      &>.search-nearby-notice {
        display: flex;
        gap: 5px;
        color: #545454;
        font-weight: 500;
        font-size: 13px;
        align-items: center;
        margin: 7px 0 0;

        &>svg {
          font-size: 15px;
        }
      }

      &>.search-submit {
        background: var(--primary-color-1);
        color: white;
        font-weight: 700;
        font-size: 15px;
        width: 100%;
        border-radius: 5px;
        margin-top: 15px;
        padding: 7px 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;

        &>svg {
          font-size: 20px !important;
        }
      }
    }

    &>.suggest-search {
      // padding: 10px;
      // background: white;
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-weight: 600;
      border-radius: 5px;

      &>span {
        font-size: 15px;
      }

      &>.list-suggest-search {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        font-size: 15px;

        &>button {
          // border: thin solid #b2b2b2;
          padding: 3px 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2em;
          color: #545454;
        }
      }
    }

    &>.history-search {
      // padding: 10px;
      // background: white;
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-weight: 600;
      border-radius: 5px;

      &>span {
        display: flex;
        font-size: 15px;
        align-items: center;
        justify-content: space-between;

        &>button {
          font-size: 20px;
          display: flex;
        }
      }

      &>.list-history-search {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        font-size: 15px;

        &>button {
          border: thin solid #b2b2b2;
          padding: 5px 10px;
          border-radius: 2em;
          color: #545454;
          background: white;
        }
      }
    }

    &>.categories-and-list-suggest {
      display: flex;
      // overflow: auto;
      position: relative;
      flex: 1;

      &>.categories {
        width: 15%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        background: #f4f4f4;
        height: fit-content;

        &>.item-category.active {
          border-right: 2px solid #ed1b24;
        }

        &>.item-category.active::after {
          content: "";
          width: 13px;
          height: 13px;
          background: #ed1b24;
          position: absolute;
          // border-radius: 5px 0;
          top: 50%;
          right: 0;
          transform: translate(50%, -50%) rotate(45deg);
        }

        &>.item-category {
          width: 100%;
          display: flex;
          flex-direction: column;
          background-color: white;
          padding: 10px 10px 5px;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: #626262;
          position: relative;
          overflow: hidden;

          &>img {
            width: 50px;
            height: 50px;
            aspect-ratio: 1;
            border-radius: 50%;
            background-color: #646464;
          }

          &>svg {
            width: 50px;
            height: 50px;
          }

          &>span {
            font-size: 0.8em;
          }
        }
      }

      &>.list-search-suggest-container {
        background: transparent;
        flex: 1;
        height: fit-content;
        min-height: 100%;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        width: 100%;

        &>.list-search-suggest {
          padding: 0;
          gap: 10px;
          // flex: 1;
          height: -moz-fit-content;
          height: fit-content;
          min-height: 100%;
          display: flex;
          flex-wrap: wrap;
          font-weight: 600;
          margin: 7px 0 0;
          width: 100%;

          &>span {
            width: 100%;
            color: #545454;
            text-align: center;
            font-size: 15px;
            text-transform: uppercase;
          }

          &>.recent-item-container {
            --item-recent-width: 100%;
            display: flex;
            width: var(--item-recent-width);
            align-self: normal;
            align-items: flex-start;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            border: thin solid transparent;
            color: #626262;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);

            &>img {
              width: 100px;
              height: 100px;
              object-fit: cover;
            }

            &>.recent-item-content {
              padding: 10px;
              display: flex;
              flex: 1;
              flex-direction: column;
              gap: 5px;
              justify-content: flex-end;
              line-height: normal;
              overflow: hidden;

              &>.name {
                color: var(--primary-color-1);
                text-align: left;
                font-weight: 700;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              &>.shop-name {
                display: flex;
                width: 100%;
                gap: 5px;
                align-items: baseline;
                overflow: hidden;
                font-weight: bold;
                font-style: italic;
                color: #399901;
                text-align: center;
                font-size: 0.9em;
                justify-content: center;
              }

              & .sold-like-amount {
                color: #a8a7a7;
                font-size: 11px;
                line-height: 13px;
                min-height: 13px;
              }

              & .price-actions {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;

                & .price-container {
                  display: flex;
                  gap: 0;
                  flex-direction: column;
                  justify-content: flex-start;
                  text-align: left;
                  align-items: flex-start;
                  line-height: 1;
                }

                & .price {
                  white-space: nowrap;
                  font-weight: 900;
                  width: fit-content;
                  color: var(--primary-color-2);
                  font-size: 15px;
                }

                & .origin-price {
                  white-space: nowrap;
                  overflow: hidden;
                  text-decoration: line-through;
                  text-overflow: ellipsis;
                  font-weight: 400;
                  font-size: 13px;
                  min-height: calc(13px * 1);
                  color: #545454;
                  font-style: normal;
                  overflow: hidden;
                }

                & .origin-price.hide {
                  opacity: 0;
                }

                &>.add-to-cart {
                  color: var(--primary-color-2);
                  margin-left: auto;
                  margin-top: auto;
                  width: 25px;
                  min-width: 25px;
                  height: 25px;
                  display: flex;
                  font-size: 25px;
                  align-items: center;
                  justify-content: center;
                }
              }

              &>.shop-selected-name {
                --font-size: 15px;
                font-weight: 700;
                color: var(--primary-color-1);
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: var(--font-size);
                white-space: nowrap;
                min-height: calc(var(--font-size) * var(--line-height));
                display: flex;
                align-items: center;
                width: 100%;

                &>span {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  text-align: left;
                }

                &>button.close {
                  color: #a8a7a7;
                  font-size: 20px;
                  margin-left: auto;
                  display: flex;
                }
              }

              &>.shop-selected-address {
                --font-size: 13px;
                --line-height: 1.5;
                font-weight: 400;
                color: #8c8c8c;
                font-size: var(--font-size);
                overflow: hidden;
                width: 100%;
                text-overflow: ellipsis;
                white-space: nowrap;
                min-height: calc(var(--font-size) * var(--line-height));
                margin-top: 5px;
                text-align: left;
              }

              &>.shop-selected-business-rating {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                min-height: calc(var(--font-size) * var(--line-height));
                width: 100%;
                text-align: left;

                &>.business-name {
                  color: #545454;
                  font-weight: 700;
                  font-size: var(--font-size);
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                &>.rating {
                  --font-size: 17px;
                  font-weight: 500;
                  color: #545454;
                  font-size: var(--font-size);
                  display: flex;
                  align-items: flex-end;
                  margin-left: auto;
                  min-width: fit-content;

                  &>svg {
                    color: #ffe500;
                    font-size: 25px;
                    margin-right: 5px;
                  }
                }
              }
            }

            &>.shop-recent-logo {
              box-shadow: none;
              background: white;
              border-radius: 5px 0 0 5px;
              cursor: pointer;

              &>.logo-origin-container {
                transform: scale(calc(100 / var(--scale-origin)));
              }

              &>img {
                min-width: 100%;
                min-height: 100%;
                object-fit: cover;
              }
            }

            // @media screen and (min-width: 1321px) {
            //   --item-recent-width: calc(50% - 5px);
            // }
            // @media screen and (max-width: 1320px) and (min-width: 1200px) {
            //   --item-recent-width: calc(50% - 5px);
            // }
            // @media screen and (max-width: 1200px) and (min-width: 721px) {
            //   --item-recent-width: calc(50% - 5px);
            // }
          }

          &>.recent-item-container:hover {
            background-color: #ebfeff;
            border: thin solid var(--primary-color-1);
          }

          &>.empty-search,
          .search-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
          }

          & .search-placeholder>img {
            width: 200px;
            height: 200px;
            object-fit: cover;
          }
        }

        & .showed-all {
          color: #626262;
          font-style: italic;
          width: 100%;
          text-align: center;
          padding: 20px 0;
        }
      }
    }

    &>.empty-search,
    .search-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }

  .btn-leaflet {
    position: absolute;
    z-index: 1000;
  }

  .btn-leaflet-left {
    left: 10px;
  }

  .btn-leaflet-right {
    right: 10px;
  }

  #fullScreen_map {
    background: white;
    z-index: 1000;
    position: absolute;
    bottom: 210px;
    color: black;
    display: flex;
    right: 10px;
    width: fit-content;
    min-width: 34px;
    padding: 9px;
    margin: 0 3px;
    font-size: 1.5em;
    cursor: pointer;
    border-radius: 3px;
  }

  .map-type-btn {
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    margin: 5px;
    width: fit-content;
    white-space: nowrap;
    height: 60px;
    min-width: 6em;
    border: 3px solid white;
    border-radius: 15px;
    cursor: pointer;
    bottom: 10px !important;
    right: 55px;
    left: auto !important;
    color: white;
  }

  .map-type-btn>span {
    margin-top: auto;
    width: 100%;
    height: 40%;
    display: flex;
    padding: 0 5px;
    justify-content: center;
    align-items: self-end;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    background-image: linear-gradient(to top,
        rgb(0, 0, 0, 80%),
        rgb(255, 255, 255, 0));
  }

  .current-location-v2-leaflet {
    // position: absolute;
    // bottom: 175px;
    // right: 12px;
    background: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 0 5px rgb(0 0 0 / 20%);
    margin-bottom: 10px;

    & svg {
      font-size: 25px;
      color: #545454;
    }
  }

  .leaflet-marker-location {
    margin-left: -15px !important;
    margin-top: -40px !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    z-index: 10000 !important;
  }

  // .leaflet-control-zoom {
  //   height: 100px !important;
  //   width: 40px !important;
  //   border-radius: 2em !important;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   flex-direction: column;
  // }

  // .leaflet-control-zoom > .leaflet-control-zoom-in {
  //   width: 100% !important;
  //   height: 50% !important;
  //   border-radius: inherit !important;
  //   border-bottom-right-radius: 0 !important;
  //   border-bottom-left-radius: 0 !important;
  //   align-items: center;
  //   justify-content: center;
  //   display: flex;
  // }

  // .leaflet-control-zoom > .leaflet-control-zoom-out {
  //   width: 100% !important;
  //   height: 50% !important;
  //   border-radius: inherit !important;
  //   border-top-right-radius: 0 !important;
  //   border-top-left-radius: 0 !important;
  //   align-items: center;
  //   justify-content: center;
  //   display: flex;
  // }

  .show-list-button {
    // position: absolute;
    // bottom: 125px;
    // right: 12px;
    background: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 0 5px rgb(0 0 0 / 20%);
    margin-bottom: 10px;

    & svg {
      font-size: 25px;
      color: #1c274c;
    }
  }

  .detail-range-container {
    width: calc(100% - 20px);
    height: 100px;
    max-width: 500px;
    cursor: pointer;
    z-index: 1 !important;
    position: relative;
    margin: 10px 0 0 10px;
  }

  .detail-range-container.detail-range-leaflet {
    z-index: 1000 !important;
    margin: 5px;
    width: calc(100% - 10px);
    background: white;
    height: auto;
  }

  .detail-range-container>div {
    margin-right: 2em;
    background: white;
    z-index: 2;
  }

  .detail-range-container>.close {
    position: absolute;
    top: 5px;
    right: 5px;
    background: transparent;
    font-size: 2em;
    line-height: 1;
    color: #2f3640;
    z-index: 3;
  }

  .detail-range-container .detail-range {
    background: white;
    width: 100%;
    height: 100%;
    padding: 5px;
    margin: 0;
    display: flex;
    font-size: 15px;
    z-index: 1 !important;
    transition: all 1s ease-in-out;
  }

  .detail-range-container .detail-range>img {
    object-fit: cover;
    width: 30%;
    height: 95px;
  }

  .detail-range-container .detail-range .detail-range-content {
    width: 70%;
    padding: 0 5px;
    display: flex;
    flex-direction: column;
  }

  .detail-range-container .detail-range .detail-range-content .name,
  .detail-range-container .detail-range .detail-range-content .address {
    width: 100%;
  }

  .detail-range-container .detail-range .detail-range-content .name>strong>span {
    font-size: 1.3em;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .detail-range-container .detail-range .detail-range-content .address>span {
    font-size: 1.2em;
  }

  .detail-range-container .detail-range .detail-range-content .price {
    width: 100%;
    color: #274abb;
    font-size: 1.3em;
  }

  .detail-range-container .detail-range .detail-range-content>.type {
    height: fit-content;
  }

  .detail-range-container .detail-range .detail-range-content>.type>span {
    background: #f5f6fa;
    border-radius: 3px;
    color: #007dff;
    padding: 5px 15px;
  }

  .detail-range-map {
    background: white;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 5px;
    line-height: normal;
    margin: 0;
    padding: 0;
    display: flex;
    font-size: 15px;
    z-index: 1 !important;
    overflow: hidden;
    font-size: 0.85em;
    transition: all 1s ease-in-out;

    & img {
      object-fit: cover;
      height: 50%;
    }

    & .detail-range-content {
      height: 50%;
    }

    & span,
    strong {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      line-clamp: 2;
      overflow: hidden;
    }
  }

  .loading-more {
    background-color: transparent;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .list-map-buttons {
    position: absolute;
    left: 100%;
    top: 0;
  }

  .list-map-button {
    background: white;
    border: thin solid var(--primary-color-1);
    border-radius: 5px;
    padding: 10px;
    margin-left: 5px;
    color: var(--primary-color-1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3em;
  }

  .list-map-button:hover {
    background: var(--primary-color-1);
    color: white;
  }

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  & .shop-selected-container {
    background: linear-gradient(to bottom right, #d0ffbc, #ffffff 35%);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fit-content;
    border-radius: 10px 10px 0 0;
    animation: slide-up 0.5s ease;
  }

  & .shop-selected-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 5px;
    position: relative;
    padding: 0;

    &>.shop-selected-detail {
      display: flex;
      gap: 10px;
      padding: 10px;
      border-bottom: thin solid color-mix(in srgb, var(--primary-color-1) 10%, transparent);

      &>.shop-seleted-logo {
        box-shadow: 0 0 10px rgb(0, 0, 0, 20%);
        background: white;
        border-radius: 10px;
        cursor: pointer;

        &>.logo-origin-container {
          transform: scale(calc(75 / var(--scale-origin)));
        }

        &>img {
          min-width: 100%;
          min-height: 100%;
          object-fit: cover;
        }
      }

      &>.shop-selected-info {
        display: flex;
        flex-direction: column;
        flex: 1;
        align-items: flex-start;
        overflow: hidden;

        &>.shop-selected-name {
          --font-size: 15px;
          font-weight: 700;
          color: var(--primary-color-1);
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: var(--font-size);
          white-space: nowrap;
          min-height: calc(var(--font-size) * var(--line-height));
          display: flex;
          align-items: center;
          width: 100%;

          &>a {
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &>span {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }

          &>button.close {
            color: #a8a7a7;
            font-size: 20px;
            margin-left: auto;
            display: flex;
          }
        }

        &>.shop-selected-address {
          --font-size: 13px;
          --line-height: 1.5;
          font-weight: 400;
          color: #8c8c8c;
          font-size: var(--font-size);
          overflow: hidden;
          width: 100%;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-height: calc(var(--font-size) * var(--line-height));
          margin-top: 5px;
          text-align: left;
        }

        &>.shop-selected-business-rating {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          min-height: calc(var(--font-size) * var(--line-height));
          width: 100%;
          text-align: left;

          &>.business-name {
            color: #545454;
            font-weight: 700;
            font-size: var(--font-size);
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &>.rating {
            --font-size: 17px;
            font-weight: 500;
            color: #545454;
            font-size: var(--font-size);
            display: flex;
            align-items: flex-end;
            margin-left: auto;
            min-width: fit-content;

            &>svg {
              color: #ffe500;
              font-size: 25px;
              margin-right: 5px;
            }
          }
        }
      }
    }

    &>.products {
      width: 100%;
      padding: 10px 0;
    }

    & .my-carousel {
      width: 100%;
      height: 100%;
      padding: 0 10px;
      display: flex;
      flex-direction: column;

      & img {
        width: 100%;
        height: 100%;
        min-height: 150px;
        max-height: 150px;
        object-fit: contain;
      }

      & .selected-shop-products-carousel-pagination {
        position: relative;
        top: 0;
        width: fit-content;
        padding: 10px;
        align-items: center;
        display: flex;
        justify-content: center;
        width: 100%;

        &>.swiper-pagination-bullet {
          background: #a8a7a7;
          width: 7px;
          height: 7px;
          opacity: 1;
          border-radius: 2em;
          margin: 0 2px;
        }

        &>.swiper-pagination-bullet[class*="active"] {
          background: var(--primary-color-1);
          width: 20px;
        }
      }
    }

    & .stack-carousel {
      flex: 1;

      & .item-stack-slide {
        // margin-right: 10px;
        // padding: 0 5px !important;

        --item-stack-slide-width: 150px;
        width: var(--item-stack-slide-width);
        padding: 0;
        position: relative;
        // border-radius: 0;
        border: none;

        @media screen and (min-width: 1321px) {
          --item-stack-slide-width: 250px;
        }

        @media screen and (max-width: 1320px) and (min-width: 1200px) {
          --item-stack-slide-width: 200px;
        }

        @media screen and (max-width: 1200px) and (min-width: 721px) {
          --item-stack-slide-width: 175px;
        }
      }

      & .item-stack-slide:last-child {
        padding-right: 0;
      }
    }

    & .item-stack {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      border: thin solid #d8d8d8;

      &>img {
        flex: 1;
        height: var(--item-stack-slide-width);
        width: var(--item-stack-slide-width);
        min-height: var(--item-stack-slide-width);
        max-height: var(--item-stack-slide-width);
        background: var(--color-background-2);
        object-fit: cover;
      }

      &>.item-stack-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px;
        text-align: left;
        font-size: 15px;

        &>a {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & .name {
          font-weight: bold;
          color: var(--primary-color-1);
        }

        & .sold-like-amount {
          color: #a8a7a7;
          font-size: 11px;
          line-height: 13px;
          min-height: 13px;
        }

        & .price-actions {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          width: 100%;

          & .price-container {
            display: flex;
            gap: 0;
            flex-direction: column;
            justify-content: flex-start;
            text-align: left;
            align-items: flex-start;
            line-height: 1;
          }

          & .price {
            white-space: nowrap;
            font-weight: 900;
            width: fit-content;
            color: var(--primary-color-2);
            font-size: 15px;
          }

          & .origin-price {
            white-space: nowrap;
            overflow: hidden;
            text-decoration: line-through;
            text-overflow: ellipsis;
            font-weight: 400;
            font-size: 13px;
            min-height: calc(13px * 1);
            color: #545454;
            font-style: normal;
            overflow: hidden;
          }

          & .origin-price.hide {
            opacity: 0;
          }

          &>.add-to-cart {
            color: var(--primary-color-2);
            margin-left: auto;
            margin-top: auto;
            width: 25px;
            min-width: 25px;
            height: 25px;
            display: flex;
            font-size: 25px;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  & .my-carousel {
    width: 100%;
    height: 100%;
    padding: 10px;

    & img {
      width: 100%;
      height: 100%;
      min-height: 150px;
      max-height: 150px;
      object-fit: contain;
    }
  }

  & .stack-carousel {
    flex: 1;

    & .item-stack-slide {
      // margin-right: 10px;
      // padding: 0 5px !important;
      height: 100%;
      cursor: pointer;
    }

    & .item-stack-slide:last-child {
      padding-right: 0;
    }
  }

  & .show-selected-product-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 20%);
  }

  .go-map-and-zoom-out {
    cursor: pointer;
    margin: 10px 0 0;
    font-weight: bold;
    font-size: 1.1em;
    text-align: center;
    color: var(--primary-color-2);
  }

  .leaflet-marker-icon.img-icon {
    align-items: center;
    margin-left: -25px !important;
    // filter: drop-shadow(0px -5px 5px rgba(0, 0, 0, 0.1));
    filter: none;
    z-index: 100 !important;
  }

  .leaflet-marker-icon.img-icon.show-center {
    z-index: 10000 !important;

    &>.shop-logo-container {
      background: var(--primary-color-2);
      filter: drop-shadow(0px -5px 5px rgba(255, 255, 255, 0.4));
      transform: scale(1.2) translateY(-10%);
    }
  }

  .tooltip-leaflet-own {
    z-index: 1;
  }

  .tooltip-leaflet-own.show-center {
    z-index: 10000 !important;
    font-weight: 700;
  }

  .shop-logo-container {
    width: 46px !important;
    height: 46px !important;
    padding: 3px;
    background: linear-gradient(to top,
        var(--primary-color-1),
        var(--linear-color-1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    transition: all 0.3s ease-in-out;
  }

  .shop-logo {
    width: 40px !important;
    height: 40px !important;
    aspect-ratio: 1/1;
    // height: 100% !important;
    object-fit: cover;
    border-radius: 2em;
    background-color: white;
    box-shadow: none;
    z-index: 2;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    // background: white;

    &>.logo-origin-container {
      // background: white;
      transform: scale(calc(40 / var(--scale-origin)));
    }

    &>img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .after {
    width: 30px !important;
    height: 30px !important;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, -75%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
    z-index: 1;
    border-radius: 0 !important;
    clip-path: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);
    background-color: var(--primary-color-2) !important;
  }

  .show-map-button {
    position: fixed;
    bottom: 75px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 5px;
    border-radius: 2em;
    z-index: 100;
    padding: 5px 10px;
    background: linear-gradient(to right,
        var(--primary-color-1),
        var(--linear-color-1));
    box-shadow: 2px 2px 4px 0 rgb(0 0 0 / 25%);
    font-size: 15px;
    color: white;
    font-weight: 700;
    animation: none !important;

    &>svg {
      font-size: 25px;
    }
  }
}

.sort-custom-select {
  & .sort-select-item {
    text-align: center;
  }
}

.empty-container {
  flex: 1;
  padding: 15px;
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-image {
  margin: 0px 10px;
  justify-content: center;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  object-fit: contain;
}

.empty-text {
  margin: 10px 0 0;
  font-size: 1.5em;
  text-align: center;
  color: #545454;
}

.auto-click {
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  display: none;
}