import axios from "axios";
import { appConst } from "../../assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";

export class AgentService extends BaseHTTPService {
    listShopCancelToken:any;
    createShopCancelToken: any;
    listShopManage(search="" ,limit = 10, offset = 0){
        if (typeof (this.listShopCancelToken) != typeof undefined) {
            this.listShopCancelToken.cancel()
        }
        this.listShopCancelToken = axios.CancelToken.source();
        let body = {
            search: search,
            offset: offset,
            limit: limit,
           
        }
        let url = appConst.apiURL.agentListShop
        //  + `?offset=${offset}&limit=${limit}&search=${search}`;
        return this.https('GET', url, body, this.listShopCancelToken.token, true);
    }

    agentShopDetail(detailShopId=null){
        let url = appConst.apiURL.agentShopDetail + '/' + detailShopId;
        return this.https('GET', url);
    }

    ordersByAgent(){
        let url = appConst.apiURL.ordersByAgent;
        return this.https('GET', url);
    }

    ordersByShopId(shopId = null){
        let url = appConst.apiURL.ordersByAgent + '/' + shopId;
        return this.https('GET', url);
    }

    createShopAgent(data: any){
        if (typeof (this.createShopCancelToken) != typeof undefined) {
            this.createShopCancelToken.cancel()
        }
        this.createShopCancelToken = axios.CancelToken.source();
        let body = {
            name: data.name,
            address: data.address,
            business_type_id: data.business_type_id,
            latitude: data.latitude,
            longitude: data.longitude,
            province_id: data.province_id,
            district_id: data.district_id,
            ward_id: data.ward_id,
            phone: data.phone,
            description: data.description,
            language: data.language,
            open_hours: data.open_hours
        }
        let url = appConst.apiURL.agentCreateShop;
        return this.https('POST', url, body, this.createShopCancelToken.token, true);
    }

    editShopAgent(body:any){
        return this.https('POST', appConst.apiURL.agentUpdateShop, body, null, true);
    }

    disableShopAgent(shopId = null){
        let body = {
            id: shopId
        }
        let url = appConst.apiURL.agentDeleteShop;
        return this.https('POST', url, body, null, true);
    }

    duplicateShop(shopId = null){
        let body = {
            shop_id: shopId
        }
        let url = appConst.apiURL.agentDuplicateShop;
        return this.https('POST', url, body, null, true);
    }

    orderByAgentId(shopId:any = 'all', offset = 0, limit = 20, status?:number | null, search_text?:string | null, start_date?: string | null, end_date?: string | null,){
        let body = {
            shop_id : shopId ? shopId : 'all',
            offset: offset,
            limit: limit,
            status: status ? status : null,
            search_text: search_text ? search_text : "",
            start_date: start_date ? start_date : "",
            end_date: end_date ? end_date : ""
        }
        return this.https('POST', appConst.apiURL.agentListOrder, body, null, true);
    }

    agentOrderDetail(orderId: string){
        let url = appConst.apiURL.agentOrderDetail + "/" + orderId;
        return this.https('GET', url);
    }

    agentUpdateOrder(order:any){
        let body = {    
            id: order.id,
            status: order.status,
            short_code: order.short_code,
            notes: order.notes,
            address: order.address,
            province_id: order.province_id,
            district_id: order.district_id,
            ward_id: order.ward_id,
            customer_id: order.customer_id,
            customer_name: order.customer_name,
            customer_phone: order.customer_phone,
            customer_latitude: order.customer_latitude ? order.customer_latitude : order.latitude,
            customer_longitude: order.customer_longitude ? order.customer_longitude : order.longitude,
            price: order.price,
            price_off: order.price_off,
            delivery_type: order.delivery_type,
            delivery_price: order.delivery_price,
            payment_method: order.payment_method,
            shop_id: order.shop_id,
            items: order.items,
            grand_total: order.grand_total,
            discount_amount: order.discount_amount,
            total_amount: order.total_amount,
            image_delete: order.image_delete
        }

        return this.https('POST', appConst.apiURL.agentOrderUpdate, body, null, true);
    }

    addProductFromSystem(body:any){
        let bodyRef = {
            shop_id: body.shop_id,
            list_product: body.list_product.map((el:any)=>{
                return {
                    id: el.id,
                    name: el.name,
                    profile_picture: el.profile_picture,
                    price: el.price,
                    existing: el.existing,
                    selected: el.selected,
                    system_price: el.system_price,
                    stock: el.stock
                }
            })
        }
        return this.https('POST', appConst.apiURL.agentAddProductFromSystem, bodyRef, null, true);
    }

    createPrivateProduct(body:any){
        return this.https('POST', appConst.apiURL.agentCreatePrivateProduct, body, null, true);
    }

    agentUpdateProduct(body:any){
        return this.https('POST', appConst.apiURL.agentUpdateProduct, body, null, true);
    }

    categoryUpdateListProduct(body:any){
        return this.https('POST', appConst.apiURL.agentUpdateListProduct, body, null, true);
    }
}