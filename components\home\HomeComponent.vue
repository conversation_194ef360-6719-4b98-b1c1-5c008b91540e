<template>
	<div class="public-container" v-on:scroll="() => {
			listProductScroll();
		}">
		<div class="home-container" >
			<div class="location-and-search scale-down">
				<div class="background-image"></div>
				<div class="h-stack location-text">
					<img :src="logo" :placeholder="logo" alt="logo" />
				</div>
				<div class="h-stack search-bar-container">
					<button v-show="searchFocus" v-on:click="() => {
						searchFocus = false;
						if (filterData.search != filterData.search_text) {
							filterData.search = filterData.search_text
						}
					}">
						<Icon name="bi:chevron-left" size="25"></Icon>
					</button>
					<div class="h-stack search-container">
						<button class="search-button">
							<Icon name="iconoir:search" size="25" v-show="!(refreshing || searchProductLoading)" />
							<Icon name="eos-icons:loading" size="25" v-show="refreshing || searchProductLoading" />
						</button>

						<input type="search" name="search-text" :placeholder="$t('HomeComponent.tim_kiem_placeholder')"
							class="search-input-container" v-on:click="() => {
								searchFocus = true;
							}" :value="filterData.search" v-on:input="(e: any) => {
							filterData = {
								...filterData,
								search: e.target.value
							};
							getSearchProductResult();
						}" autoComplete="off" v-on:keypress.enter="() => {
							pushToAround(filterData.search)
						}" id="search_input" />
						<button class="clear-button" v-show="filterData.search && filterData.search.length" v-on:click="() => {
							filterData = {
								...filterData,
								search: ''
							}
							searchProductSuggest = [];
							focusSearch()
						}">
							<Icon name="iconamoon:sign-times" size="25" />
						</button>
					</div>
					<button class="cart-in-search" v-show="searchFocus" v-on:click="() => {
						router.push(appRoute.CartComponent);
					}">
						<Icon name="bi:basket2-fill" size="25"></Icon>
						<em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length : "10+" }}
								</em>
					</button>
				</div>

			</div>

			<div class="filter-result-container">
				<div class="search-result" id="search-result" :class="{ 'show': searchFocus }" v-on:scroll="(e: any) => {
					listSuggestScroll(e);
				}">
					<div class="history-search" v-if="searchTextSuggest && searchTextSuggest.length">
						<span>{{ $t('HomeComponent.goi_y') }}</span>

						<div class="list-history-search">
							<button v-for="(itemSearch, indexTab) in searchTextSuggest" v-on:click="() => {
								pushToAround(itemSearch)
							}">
								{{ itemSearch }}
							</button>
						</div>

					</div>
					<div class="categories-and-list-suggest">
						<div class="categories" v-if="false">
							<button class="item-category" v-for="itemCategory in dataCategory"
								:class="{ 'active': checkCategoryFilter(itemCategory.id) }" v-on:click="async ($event: any) => {
									setCategoryFilter(itemCategory.id);
								}">
								<img loading="lazy" :src="domainImage + itemCategory.profile_picture" alt=""
									v-if="itemCategory.profile_picture" />
								<Icon name="carbon:data-categorical" v-else></Icon>
								<span>{{ itemCategory.name }}</span>
							</button>
						</div>
						<div class="list-search-suggest-container">

							<div class="list-search-suggest">
								<span>{{ $t('HomeComponent.xem_gan_day') }}</span>
								<div class="search-placeholder" v-if="(!productRecent || !productRecent.length)">
									<img loading="lazy" :src="none_result" :placeholder="none_result" />
								</div>
								<nuxt-link :to="appRoute.ProductComponent + '/' + (itemRecent.slug?.length ? itemRecent.slug : itemRecent.id)"
									v-if="productRecent && productRecent.length" v-for="itemRecent of productRecent"
									class="search-result-item-container" :title="showTranslateProductName(itemRecent)">
									<img loading="lazy"
										:src="itemRecent?.profile_picture ? domainImage + itemRecent.profile_picture : icon_for_product"
										:placeholder="icon_for_product" :alt="showTranslateProductName(itemRecent)" />

									<div class="items-suggest-content">
										<span class="name">{{ showTranslateProductName(itemRecent) }}</span>
										<span class="shop-name" v-show="itemRecent.shop">{{ itemRecent.shop?.name
											}}</span>
										<span class="price">
											{{
												(itemRecent.price_off != null && itemRecent.price_off < itemRecent.price) ?
													formatCurrency(parseFloat(itemRecent.price_off), itemRecent.shop ?
														itemRecent.shop.currency : itemRecent.currency) :
													(parseFloat(itemRecent.price) == 0 || itemRecent.price == null) ?
														$t('HomeComponent.gia_lien_he') :
														formatCurrency(parseFloat(itemRecent.price), itemRecent.shop ?
															itemRecent.shop.currency : itemRecent.currency) }} </span>
												<span class="origin-price"
													v-if="(itemRecent.price_off != null && itemRecent.price_off < itemRecent.price)">
													{{
														(parseFloat(itemRecent.price) == 0 || itemRecent.price ==
															null)
															? $t('HomeComponent.gia_lien_he')
															: formatCurrency(parseFloat(itemRecent.price), itemRecent.shop ?
																itemRecent.shop.currency : itemRecent.currency)
													}}
												</span>
												<button class="add-to-cart">
													{{ $t('HomeComponent.them_vao_gio') }}
												</button>
									</div>

								</nuxt-link>

								<div class="loading-more" v-if="searchProductLoadingMore == true">
									{{ $t('HomeComponent.loading') }}...
								</div>
								<div id="last_of_list_suggest"></div>
							</div>
						</div>
					</div>
				</div>

				<div class="dashboard" :class="{ 'show-search': searchFocus }" id="dashboard">

					<div class="categories" v-if="false">
						<v-tabs hide-slider class="categories-filter-tab" v-if="dataCategory && dataCategory.length">
							<v-tab v-for="(itemCategory, indexTab) in dataCategory" class="category-item-tab"
								v-on:click="async ($event: any) => {
									setCategoryFilter(itemCategory.id);
								}" :class="checkCategoryFilter(itemCategory.id) ? 'active' : ''" :value="itemCategory.id" :key="itemCategory.id"
								:id="'tab_' + itemCategory.id">
								<div class="tab-title">
									<span class='name'>
										{{ itemCategory.name }}
									</span>
								</div>
							</v-tab>
						</v-tabs>
					</div>
					<div class="advertisments">

						<Swiper class="my-carousel" :modules="[SwiperAutoplay, SwiperPagination]" :slides-per-view="1"
							:loop="true" :effect="'creative'" :pagination="{
								horizontalClass: 'my-adv-carousel-pagination',
							}" :autoplay="false" key="advertisment-carousel">
							<SwiperSlide class="item-stack-slide" v-for="ad of listAdv" :key="'advertisment_' + ad">
								<img loading="lazy" :src="ad" alt="" />
							</SwiperSlide>
						</Swiper>

					</div>

					<div class="suggest-list content-list">
						<div class="stack-content-title">
							<span>
								{{ $t('HomeComponent.de_xuat') }}
							</span>
						</div>
						<div class="stack-content-list">

							<Swiper v-if="listSuggest?.length" class="my-carousel stack-carousel"
								:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="2"
								:loop="true" :effect="'creative'" :navigation="true" :spaceBetween="10" :freeMode="true"
								:autoplay="false" key="suggest-carousel">
								<SwiperSlide class="item-stack-slide" v-for="item of listSuggest"
									:key="'suggest_' + item.id">
									<nuxt-link :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)" class="item-stack"
										:title="showTranslateProductName(item)">
										<span class="distance">{{ item.distance }}</span>
										<img loading="lazy" :src="item && item.profile_picture
											? domainImage + item.profile_picture
											: icon_for_product
											" :placeholder="icon_for_product" />
										<div class="item-stack-content">
											<span class="name">{{ showTranslateProductName(item) }}</span>

											<span class="price">
												{{
													(item.price_off != null && item.price_off < item.price) ?
														formatCurrency(parseFloat(item.price_off), item.shop ?
															item.shop.currency : item.currency) : (parseFloat(item.price) == 0 ||
																item.price == null) ? $t('HomeComponent.gia_lien_he') :
															formatCurrency(parseFloat(item.price), item.shop ?
																item.shop.currency : item.currency) }} </span>
													<span class="origin-price"
														v-if="(item.price_off != null && item.price_off < item.price)">
														{{
															(parseFloat(item.price) == 0 || item.price == null)
																? $t('HomeComponent.gia_lien_he')
																: formatCurrency(parseFloat(item.price), item.shop ?
																	item.shop.currency : item.currency)
														}}
													</span>
										</div>

									</nuxt-link>
								</SwiperSlide>
							</Swiper>
						</div>
					</div>


					<div class="sale-off content-list">
						<div class="stack-content-title">
							<span>
								{{ $t('HomeComponent.giam_gia') }}
								<!-- <nuxt-link>
				{{ $t('HomeComponent.xem_het') }}
			</nuxt-link> -->
							</span>
							<span></span>
						</div>
						<div class="stack-content-list">

							<Swiper v-if="listSaleOff?.length && !loadingSaleOff" class="my-carousel stack-carousel"
								:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="2"
								:loop="true" :effect="'creative'" :navigation="true" :spaceBetween="10" :freeMode="true"
								:autoplay="false" key="sale-off-carousel">
								<SwiperSlide class="item-stack-slide" v-for="item of listSaleOff"
									:key="'sale_off_' + item.id">
									<nuxt-link class="item-stack" :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)"
										:title="showTranslateProductName(item)">
										<img loading="lazy" :src="item && item.profile_picture
											? domainImage + item.profile_picture
											: icon_for_product" :placeholder="icon_for_product" />
										<div class="item-stack-content">
											<span class="name">{{ showTranslateProductName(item) }}</span>
											<span class="price">
												{{
													(item.price_off != null && item.price_off < item.price) ?
														formatCurrency(parseFloat(item.price_off), item.shop ?
															item.shop.currency : item.currency) : (parseFloat(item.price) == 0 ||
																item.price == null) ? $t('HomeComponent.gia_lien_he') :
															formatCurrency(parseFloat(item.price), item.shop ?
																item.shop.currency : item.currency) }} <em class="off"
													v-if="(item.price_off != null && item.price_off < item.price)">{{
														(parseFloat(item.price) == 0 || item.price == null)
															? $t('HomeComponent.gia_lien_he')
															: formatCurrency(parseFloat(item.price), item.shop ?
																item.shop.currency
																: item.currency)
													}}</em>
											</span>
										</div>
									</nuxt-link>
								</SwiperSlide>
							</Swiper>
							<div v-else class="none-content-list">
								<!-- {{ loadingSaleOff ? '' : $t('HomeComponent.chua_co_san_pham_giam_gia') }} -->
							</div>
							<v-overlay v-model="loadingSaleOff" :z-index="100" :absolute="false" contained
								content-class='spinner-container' persistent scrim="#fff" key="loading_sale_off"
								no-click-animation>
								<Icon name="eos-icons:loading"></Icon>
							</v-overlay>
						</div>
					</div>

					<div class="best-around content-list">
						<div class="stack-content-title">
							<span>
								{{ $t('HomeComponent.duoc_ua_chuong_nhat') }}
								<!-- <nuxt-link>
				{{ $t('HomeComponent.xem_het') }}
			</nuxt-link> -->
							</span>
							<span>{{ $t('HomeComponent.cua_hang_duoc_ua_chuong_nhat') }}</span>
						</div>
						<div class="stack-content-list">

							<Swiper v-if="listBestAround?.length && !loadingBestAround"
								class="my-carousel stack-carousel best-around-carousel"
								:modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :freeMode="true"
								:slides-per-view="2" :loop="true" :effect="'creative'" :navigation="true"
								:spaceBetween="10" :autoplay="false" key="best-around-carousel">
								<SwiperSlide class="item-stack-slide" v-for="item of listBestAround"
									:key="'best_around_' + item.id">
									<nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
										:title="showTranslateProductName(item)">
										<!-- <div class="shop-logo">
						<img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt="" v-if="!item.logo"/>
						<div class="logo-origin-container" :class="{'none-style' : !(item.logo?.style?.length)}" v-else>
							<img 
								:src="domainImage + item.logo.path" 
								:style="{
									transform: (item.logo && item.logo.style) ? item.logo.style : 'none'
								}"
								alt=""/>
						</div>
					</div> -->
										<AvatarComponent :imgTitle="showTranslateProductName(item)" :imgStyle="item.logo?.style"
											class="shop-logo" :imgSrc="item.logo
												? (domainImage + item.logo.path)
												: item.banner
													? (domainImage + item.banner.path)
													: ''
												" :width="75" :height="75" v-on:img_click="() => {
												router.push(appRoute.DetailShopComponent + '/' + item.slug)
											}" />
										<div class="item-stack-content">
											<span class="distance">{{ item.distance }}</span>
											<span class="name">{{ showTranslateProductName(item) }}</span>
										</div>
										<div class="go-to-shop">
											{{ $t('HomeComponent.xem_san_pham') }}
											<Icon name="mingcute:arrow-down-circle-fill" width="1.3em" height="1.3em" />
										</div>
									</nuxt-link>
								</SwiperSlide>
							</Swiper>
							<div v-else class="none-content-list">
								<!-- {{ loadingBestAround ? '' : '' }} -->
							</div>
							<v-overlay v-model="loadingBestAround" :z-index="100" :absolute="false" contained
								content-class='spinner-container' persistent scrim="#fff" key="loading_best_around"
								no-click-animation>
								<Icon name="eos-icons:loading"></Icon>
							</v-overlay>
						</div>
					</div>

					<div class="hot-deal content-list">
						<div class="stack-content-title">
							<span>
								{{ $t('HomeComponent.uu_dai_hot') }}
								<!-- <nuxt-link>
				{{ $t('HomeComponent.xem_het') }}
			</nuxt-link> -->
							</span>
							<span>{{ $t('HomeComponent.cac_chuong_trinh_uu_dai') }}</span>
						</div>
						<div class="stack-content-list">

							<Swiper v-if="listHotDeal?.length && !loadingHotDeal"
								class="my-carousel stack-carousel hot-deal-carousel"
								:modules="[SwiperAutoplay, SwiperNavigation, SwiperPagination, SwiperFreeMode]"
								:freeMode="true" :slides-per-view="1" :loop="true" :effect="'creative'"
								:navigation="true" :pagination="{
									horizontalClass: 'hot-deal-carousel-pagination',
								}" :autoplay="false" key="hot-deal-carousel">
								<SwiperSlide class="item-stack-slide" v-for="item of listHotDeal"
									:key="'hot_deal_' + item.id">
									<nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
										:title="showTranslateProductName(item)">
										<img loading="lazy" :src="item && item.banner
											? domainImage + item.banner.path
											: shop_banner" :placeholder="shop_banner" :alt="showTranslateProductName(item)" />
										<div class="item-stack-content">
											<div class="hot-deal-info">
												<span v-if="item.hot_deal_name">{{ item.hot_deal_name }}</span>
												<span v-if="item.hot_deal_start">{{ item.hot_deal_start }} - {{
													item.hot_deal_expire
												}}</span>
											</div>
											<span class="name">{{ showTranslateProductName(item) }}</span>
											<span class="address">{{ item.address }}</span>
										</div>
									</nuxt-link>
								</SwiperSlide>
							</Swiper>
							<div v-else class="none-content-list">
								<!-- {{ loadingHotDeal ? '' : $t('HomeComponent.chua_co_uu_dai') }} -->
							</div>
							<v-overlay v-model="loadingHotDeal" :z-index="100" :absolute="false" contained
								content-class='spinner-container' persistent scrim="#fff" key="loading_hot_deal"
								no-click-animation>
								<Icon name="eos-icons:loading"></Icon>
							</v-overlay>
						</div>
					</div>

					<div class="product content-list">
						<div class="stack-content-title">
							<span>
								{{ $t('HomeComponent.san_pham') }}
								<!-- <nuxt-link>
				{{ $t('HomeComponent.xem_het') }}
			</nuxt-link> -->
							</span>
						</div>
						<div class="stack-content-list">
							<div class="product-stack-content" v-if="listProduct?.length && !loadingProduct">
								<nuxt-link :to="appRoute.ProductComponent + '/' + (item.slug?.length ? item.slug : item.id)" class="item-stack"
									v-for="item of listProduct" :title="showTranslateProductName(item)">
									<span class="distance">{{ item.distance }}</span>
									<img loading="lazy" :src="item && item.profile_picture
										? domainImage + item.profile_picture
										: icon_for_product" :placeholder="icon_for_product" />
									<div class="item-stack-content">
										<span class="name">{{ showTranslateProductName(item) }}</span>
										<span class="price">
											{{
												(item.price_off != null && item.price_off < item.price) ?
													formatCurrency(parseFloat(item.price_off), item.shop ?
														item.shop.currency : item.currency) : (parseFloat(item.price) == 0 ||
															item.price == null) ? $t('HomeComponent.gia_lien_he') :
														formatCurrency(parseFloat(item.price), item.shop ? item.shop.currency :
															item.currency) }} <em class="off"
												v-if="(item.price_off != null && item.price_off < item.price)">{{
													(parseFloat(item.price) == 0 || item.price == null)
														? $t('HomeComponent.gia_lien_he')
														: formatCurrency(parseFloat(item.price), item.shop ? item.shop.currency
															: item.currency)
												}}</em>
										</span>
									</div>
								</nuxt-link>
								<div v-show="loadingMoreProduct" class="loading-more">{{
									$t('HomeComponent.loading_more') }}
								</div>
								<div id="last_of_list_product"></div>
							</div>
							<div v-else class="none-content-list">
								<!-- {{ loadingProduct ? '' : $t('HomeComponent.chua_co_san_pham') }} -->
							</div>
							<v-overlay v-model="loadingProduct" :z-index="100" :absolute="false" contained
								content-class='spinner-container' persistent scrim="#fff" key="loading_hot_deal"
								no-click-animation>
								<Icon name="eos-icons:loading"></Icon>
							</v-overlay>
						</div>
					</div>

				</div>

			</div>

		</div>
	</div>

</template>

<style lang="scss" src="./HomeStyles.scss"></style>

<script setup lang="ts">
import { getToken } from "firebase/messaging";
import { toast } from "vue3-toastify";
import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import logo from "~/assets/image_13_3_2024/logo.png"
import icon_for_product from "~/assets/image/icon-for-product.png";
import shop_banner from "~/assets/image/remagan-banner-19_1.png";
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_result from "~/assets/image/none-result-2.webp";
import { VueFinalModal } from "vue-final-modal";
import _ from "lodash";
import { PublicService } from "~/services/publicService/publicService";
import type { CartDto } from "~/assets/appDTO";
import { HttpStatusCode } from "axios";

const listAdv = [
	// "https://inan2h.vn/wp-content/uploads/2022/12/in-banner-quang-cao-do-an-3.jpg",
	// "https://inan2h.vn/wp-content/uploads/2022/12/in-banner-quang-cao-do-an-7-1.jpg",
	"https://tuongviethoasen.vn/application/media/kien_thuc_nau_an/banner.jpg",
	"https://phuongnamvina.com/img_data/images/top-y-tuong-kinh-doanh-do-an-vat-online-hot-nhat.jpg",
	// "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRIWtZ-zrBiVG1JkfvS3cQqfthgLctIFVqQj2Zww_-axj3pbTiJ-x2G1XlF8zzP71jO8fc&usqp=CAU",
	// "https://inan2h.vn/wp-content/uploads/2022/12/in-banner-quang-cao-do-an-5.jpg",
	"https://inan2h.vn/wp-content/uploads/2022/12/in-banner-quang-cao-do-an-12.jpg"]

var productService = new ProductService();
var publicService = new PublicService();
var placeService = new PlaceService();
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
useHead({
	title: t('AppRouteTitle.HomeComponent'),
	meta: [
		{
			name: "title",
			content: t('AppRouteTitle.HomeComponent'),
		},
	],
});
let metaDescription = `Rẻ mà gần (remagan.com) là ứng dụng mua sắm tiện lợi, giúp bạn dễ dàng tìm kiếm và mua hàng với giá rẻ và gần nhất. Chúng tôi không chỉ hỗ trợ người mua, mà còn là cầu nối cho các doanh nghiệp nhỏ và vừa tiếp cận khách hàng thông qua một phương tiện đơn giản và hiệu quả. 
        Với mục tiêu tối ưu hóa giá cả, Rẻ mà gần (remagan.com) ưu tiên mang đến cho người mua những ưu đãi vô cùng hấp dẫn với chiết khấu siêu cạnh tranh, vượt trội so với các ứng dụng khác. Khám phá sự thuận tiện và giá trị vô song cùng Rẻ mà gần (remagan.com) ngay hôm nay!
        #Remagan re ma gan #MuaSắm #ƯuĐãi #TiệnLợi #re_ma_gan #re #ma #gan`
useSeoMeta({
	title: t('AppRouteTitle.HomeComponent'),
	description: metaDescription,
	ogTitle: t('AppRouteTitle.HomeComponent'),
	ogDescription: metaDescription,
	ogImage: baseLogoUrl,
	ogImageHeight: 400,
	ogImageWidth: 720,
	ogUrl: "https://remagan.com",
	ogType: "website",
});

var dataCategory = ref(appDataStartup.listCategory) as any;
var dataFilterSort = [
	{
		label: "Giá từ thấp đến cao",
		value: 1,
	},
	{
		label: "Giá từ cao đến thấp",
		value: 2,
	},
	{
		label: "Mới nhất",
		value: 3,
	},
	// {
	//     label: "Sản phẩm bán chạy",
	//     value: 4
	// },
	// {
	//     label: "Sản phẩm khuyến mãi",
	//     value: 5
	// }
];
var address = ref("");
var refreshing = ref(true);
var searchProductLoading = ref(false);
var searchProductLoadingMore = ref(false);
var searchSuggestAll = ref(false);
var searchFocus = ref(route.query?.searchFocus == 'true' ? true : false);
var searchHistory = ref([] as any[]);
var searchTextSuggest = ref([] as any[]);
var filterData = ref({
	search: "",
	search_text: "",
	latitude_user: 0,
	longitude_user: 0,
	latitude_s: "",
	latitude_b: "",
	longitude_s: "",
	longitude_b: "",
	limit: 20,
	offset: 0,
	category_ids: [] as any,
	sortBy: 1,
	filter_type: 1,
});
var searchProductSuggest = ref();
var searchProductSuggestCount = ref();
var cartData = ref();

var searchProductTimeout: any;
var loadMore: any;

var loadMoreProductTimeout: any;
var loadingMoreProduct = ref(false);

var listSaleOff = ref([] as any);
var listSuggest = ref([] as any);
var listBestAround = ref([] as any);
var listHotDeal = ref([] as any);
var listProduct = ref([] as any);

var listProductCount = ref(0);

var loadingSuggest = ref(false);
var loadingSaleOff = ref(false);
var loadingHotDeal = ref(false);
var loadingBestAround = ref(false)
var loadingProduct = ref(false);

var onDraging = ref(false);
var productRecent = ref([] as any[]);
var webInApp = ref(null as any);
onUnmounted(async () => {
	let storageState = {
		filterData: filterData.value,
		searchProductSuggest: searchProductSuggest.value,
		searchProductSuggestCount: searchProductSuggestCount.value,
		searchTextSuggest: searchTextSuggest.value
	}
	sessionStorage.setItem(appConst.storageKey.stateRestore.HomeComponent, JSON.stringify(storageState));
});
// watch(() => route.query, (cur, pre) => {

// 	let preObj = !isEmpty(pre) ? JSON.parse(pre.filter as string) : null;
// 	let curObj = !isEmpty(cur) ? JSON.parse(cur.filter as string) : null;
// 	console.log("query listen: ", _.isEqual(preObj, curObj));
// 	if (!_.isEqual(preObj, curObj)) {
// 		filterData.value = curObj;
// 	}
// })

// watch(filterData, (cur, pre) => {

// 	let curObj = !isEmpty(cur) ? JSON.parse(JSON.stringify(cur)) : null;
// 	let preObj = !isEmpty(pre) ? JSON.parse(JSON.stringify(pre)) : null;
// 	console.log("filter listen: ", _.isEqual(preObj, curObj));
// 	let bound = new nuxtApp.$L.LatLngBounds(
// 		[curObj.latitude_s, curObj.longitude_s]
// 		, [curObj.latitude_b, curObj.longitude_b]
// 	);
// 	if (!_.isEqual(preObj, curObj)) {
// 		let curBound = leafletMap ? leafletMap.getBounds() : null;
// 		if (curBound && !curBound.equals(bound)) {
// 			leafletMap.fitBounds(bound, { animate: false, duration: 0 });
// 		}
// 		else{
// 			filter()
// 		}
// 	}
// 	// let curBound = leafletMap.getBounds();
// 	// if (!curBound.equals(bound)) {
// 	// 	leafletMap.fitBounds(bound, { animate: false, duration: 0 });
// 	// }
// })
// watch(() => route.query, (cur, pre) => {
// 	console.log("route query listen ", cur, pre);
// 	filterData.value = JSON.parse(cur.filter as string)
// 	console.log(filterData.value);
// 	let bound = new nuxtApp.$L.LatLngBounds(
// 		[parseFloat(filterData.value.latitude_s), parseFloat(filterData.value.longitude_s)]
// 		, [parseFloat(filterData.value.latitude_b), parseFloat(filterData.value.longitude_b)]
// 	);
// 	leafletMap.flyToBounds(bound);
// })


onMounted(() => {

	console.log("mount");
	let webInAppRef = localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	// nuxtApp.$emit(appConst.event_key.send_request_to_app, { action: appConst.webToAppAction.requestUserLocation });
	nuxtApp.$emit(appConst.event_key.send_request_to_app, { action: appConst.webToAppAction.requestNotification });
	// let queryFilter = route.query?.filter ? route.query.filter : null;
	productRecent.value = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : [];
	let store = JSON.parse(sessionStorage.getItem(appConst.storageKey.stateRestore.HomeComponent) as string);
	// if (queryFilter) {
	// 	filterData.value = JSON.parse(route.query.filter as string);
	// }
	// else if (store) {
	// 	filterData.value = store.filterData;
	// }
	if (store) {
		searchProductSuggest.value = store.searchProductSuggest;
		searchProductSuggestCount.value = store.searchProductSuggestCount;
		searchTextSuggest.value = store.searchTextSuggest
	}
	// filter();
	setTimeout(() => {
		getCurrentLocationOfUser();
	}, 1000);

	nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
		dataCategory.value = appDataStartup.listCategory;
	});
	// nuxtApp.$listen("search_focus", () => {
	// 	focusSearch()
	// });
	// nuxtApp.$listen("home_click", (e: any) => {
	// 	console.log(e);
	// 	isShowList.value = e;
	// 	searchFocus.value = false;
	// 	router.replace({
	// 		query: {
	// 			...route.query,
	// 			isShowList: isShowList.value.toString(),
	// 		}
	// 	})
	// });
	// if (isShowList.value && justFilter.value) {
	// 	document
	// 		.getElementById("list-result")
	// 		?.scrollTo({ top: 0, behavior: "smooth" });
	// 	justFilter.value = false;
	// }
	document.title = t('AppRouteTitle.HomeComponent');
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$;


	// let userLocation = await JSON.parse(localStorage.getItem(appConst.storageKey.userLocation) as string);

	// if (webInApp.value && userLocation && userLocation.latitude_user && userLocation.longitude_user) {
	// 	filterData.value = {
	// 		...filterData.value,
	// 		latitude_user: userLocation.latitude_user,
	// 		longitude_user: userLocation.longitude_user
	// 	}
	// 	getUserAddress();
	// 	initDashboard()
	// }
	// else {


	// }

});

function initDashboard() {
	console.log("load dashboard")
	loadingSuggest.value = true;
	loadingSaleOff.value = true;
	loadingBestAround.value = true;
	loadingHotDeal.value = true;
	loadingProduct.value = true;
	let body = {
		section: "suggest", //suggest,sale_off,best_around,hot_deal
		latitude_user: filterData.value.latitude_user,
		longitude_user: filterData.value.longitude_user
	}
	publicService.dashboard({ ...body, section: 'suggest' }).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listSuggest.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
		}
		loadingSuggest.value = false;
	})


	publicService.dashboard({ ...body, section: 'sale_off' }).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listSaleOff.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
		}
		loadingSaleOff.value = false;
	})


	publicService.dashboard({ ...body, section: 'best_around' }).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listBestAround.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
		}
		loadingBestAround.value = false;
	})


	publicService.dashboard({ ...body, section: 'hot_deal' }).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listHotDeal.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
		}
		loadingHotDeal.value = false;
	})
	publicService.dashboard({ ...body, section: 'suggest', limit: 20 }).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listProduct.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : [];
			listProductCount.value = res.body.data?.count
		}
		loadingProduct.value = false;
	})
	refreshing.value = false;
}

function getCurrentLocationOfUser() {
	if ("geolocation" in navigator) {
		navigator.geolocation.getCurrentPosition(
			async (position) => {
				filterData.value = {
					...filterData.value,
					latitude_user: position.coords.latitude,
					longitude_user: position.coords.longitude
				}
				getUserAddress();
				initDashboard()
			},
			async (error) => {
				filterData.value = {
					...filterData.value,
					latitude_user: appConst.defaultCoordinate.latitude,
					longitude_user: appConst.defaultCoordinate.longitude
				}
				getUserAddress();
				initDashboard()
			},
            {
                enableHighAccuracy: false, // Use less accurate but faster methods
                timeout: 5000, // Set a timeout (in milliseconds)
                
            }
		);
	}
}

function getSearchProductResult() {
	searchProductLoading.value = true;
	clearTimeout(searchProductTimeout);
	// if (filterData.value.search && filterData.value.search.length) {
	searchProductTimeout = setTimeout(() => {
		publicService
			.searchSuggest(filterData.value.search, undefined, undefined, filterData.value.latitude_user, filterData.value.longitude_user)
			.then((res) => {
				if (filterData.value.search && filterData.value.search.length) {
					let indexHistory = searchHistory.value.indexOf(filterData.value.search);
					if (indexHistory == -1) {
						searchHistory.value = [
							filterData.value.search,
							...searchHistory.value,
						].slice(0, 10);
					}
				}

				if (res.status == HttpStatusCode.Ok) {
					searchProductSuggest.value = res.body.data.products;
					searchProductSuggestCount.value = res.body.data.count;
					searchTextSuggest.value = res.body.data.strings;
					searchProductLoading.value = false;
					document.getElementById("search-result")?.scrollTo({ top: 0 });
					searchSuggestAll.value = false;
				}
			})
			.catch(() => {
				searchProductLoading.value = false;
			});
	}, 1000);
	// } else {
	// 	searchProductLoading.value = false;
	// }
}

function loadMoreProductSearching() {
	if (
		searchProductSuggest.value.length < searchProductSuggestCount.value &&
		!searchSuggestAll.value
	) {
		searchProductLoadingMore.value = true;
		clearTimeout(searchProductTimeout);
		searchProductTimeout = setTimeout(() => {
			publicService
				.searchSuggest(
					filterData.value.search,
					searchProductSuggest.value.length,
					25,
					filterData.value.latitude_user, filterData.value.longitude_user
				)
				.then((res) => {
					if (res.status == HttpStatusCode.Ok) {
						if (res.body.data && res.body.data.result.length > 0) {
							searchProductSuggest.value = [
								...searchProductSuggest.value,
								...res.body.data.products,
							];
						} else {
							searchSuggestAll.value = true;
						}
						searchProductLoadingMore.value = false;
					}
				})
				.catch(() => {
					searchProductLoadingMore.value = false;
				});
		}, 1000);
	}
}

function loadMoreProducts() {
	if (
		listProduct.value.length < listProductCount.value
	) {
		loadingMoreProduct.value = true;
		clearTimeout(loadMoreProductTimeout);
		loadMoreProductTimeout = setTimeout(() => {
			let body = {
				section: "suggest", //suggest,sale_off,best_around,hot_deal
				latitude_user: filterData.value.latitude_user,
				longitude_user: filterData.value.longitude_user
			}
			publicService.dashboard({ ...body, section: 'suggest', offset: listProduct.value.length, limit: 20 }).then(res => {
				if (res.status == HttpStatusCode.Ok) {
					listProduct.value = [
						...listProduct.value,
						...(res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : [])
					]
				}
				loadingMoreProduct.value = false;
			})
		}, 1000);
	}
}
function getUserAddress() {
	placeService
		.myGeocoderByLatLngToAddress(filterData.value.latitude_user, filterData.value.longitude_user)
		.then((res: any) => {
			if (res.body.data && res.body.data.length) {
				address.value = res.body.data[0].address
					? res.body.data[0].address
					: "";
			}
		});
}
function listSuggestScroll(event: any) {
	let el = document
		.getElementById("last_of_list_suggest")
		?.getBoundingClientRect().bottom;
	if (el && el <= window.innerHeight + 10) {
		if (searchProductSuggest.value?.length < searchProductSuggestCount.value) {
			loadMoreProductSearching();
		}
	}
}

function checkCategoryFilter(id: any) {
	if (
		filterData.value.category_ids &&
		filterData.value.category_ids.indexOf(id) != -1
	)
		return true;
	return false;
}
function setCategoryFilter(id: any) {
	let arr: any = filterData.value.category_ids || [];
	let index = arr.indexOf(id);
	if (index == -1) {
		arr.push(id);
	} else {
		arr.splice(index, 1);
	}
	filterData.value = {
		...filterData.value,
		category_ids: arr,
	};
}
function focusSearch() {
	document.getElementById('search_input')?.focus();
	searchFocus.value = true;
}
function blurSearch() {
	document.getElementById('search_input')?.blur();
	searchFocus.value = false;
}

function pushToAround(search_text: string) {
	nuxtApp.$emit('refresh_around');
	setTimeout(() => {
		filterData.value = {
			...filterData.value,
			search: search_text,
			search_text: search_text
		};
		// getSearchProductResult();
		router.push({
			path: appRoute.AroundComponent,
			query: {
				filter: JSON.stringify(filterData.value)
			}
		})
	}, 300);

}

function listProductScroll() {
	let el = document.getElementById('last_of_list_product')?.getBoundingClientRect().bottom;
	if (el && (el <= window.innerHeight + 10)) {
		loadMoreProducts()
	}
}
</script>
