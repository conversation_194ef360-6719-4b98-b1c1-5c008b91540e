.cancel-delivery-container {
  min-height: unset !important;
  background-color: white;
  gap: 5px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  padding: 0;
  // min-height: 40dvh;
  border-radius: 10px;
  background-color: white;


  & h2 {
    padding: 0 10px;
    text-align: center;
    color: #545454;
  }

  & .parter-info{
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 0 10px;
    align-items: baseline;

    & img{
      height: 30px;
      margin: auto;
    }
  }
  & .delivery-code{
    padding: 0 10px;
    text-align: center;
    font-style: italic;
    font-weight: 700;
    font-size: 17px;
    color: var(--primary-color-1);
  }
  & .cancel-reason{
    display: flex;
    flex-direction: column;
    padding: 10px;

    & > label{
      margin-bottom: 10px;
      color: #545454;
      font-weight: 600;
    }

    & .cancel-reason-options {
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-height: 300px;
      overflow-y: auto;
      margin-bottom: 15px;
      
      & .reason-option {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;
        
        &:hover {
          background: #e9ecef;
        }
        
        &.selected {
          background: #e3f2fd;
          border-color: var(--primary-color-1);
        }
        
        & input[type="radio"] {
          margin-right: 10px;
          width: 16px;
          height: 16px;
        }
        
        & label {
          cursor: pointer;
          margin: 0;
          flex: 1;
          color: #333;
          font-weight: 500;
        }
      }
    }

    & .additional-notes {
      & label {
        margin-bottom: 5px;
        color: #545454;
        font-weight: 600;
        display: block;
      }

      & textarea {
        background: #f5f6fa;
        border-radius: 10px;
        outline: none;
        padding: 8px 12px;
        resize: none;
        width: 100%;
        border: 1px solid #e0e0e0;
        
        &:focus {
          border-color: var(--primary-color-1);
        }
      }
    }
  }

  & .cancel-delivery-footer{
    display: flex;
    padding: 0 10px 10px;
    justify-content: space-evenly;

    & button{
      border-radius: 2em;
      padding: 5px 15px;
      color: #545454;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 150px;
      font-weight: 700;
      font-size: 17px;
    }

    & button.close{
      background: #f5f6fa;
    }

    & button.cancel{
      background: var(--primary-color-2);
      color: white;
    }
  }
}