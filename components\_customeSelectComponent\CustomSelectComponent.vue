<template>
	<v-overlay v-model="showCustomSelectModal" location="bottom" :absolute="true" :contained="true" :z-index="1001"
		v-on:click:outside="() => {
			close()
		}" key="show_custom_select" class="custom-select-overlay-container" persistent
		content-class='custome-select-container' no-click-animation>
		<div class="title-select">
			<slot name="title_icon_left" v-if="$slots.title_icon_left"></slot>
			<div class="title-select">
				<span>{{ props.title }} <em v-if="props.multiple">({{ selected_options?.length }})</em> </span>
			</div>
			<slot name="title_icon_right" v-if="$slots.title_icon_right"></slot>
			<button class="close-button" v-on:click="() => close()" v-else>
				<Icon name="iconamoon:close-circle-1-light"></Icon>
			</button>
		</div>
		<div class="search-select-options" v-if="props.searchable">
			<div class="search-input-group">
				<Icon name="solar:rounded-magnifer-outline"></Icon>
				<input type="text" v-model="search_text" :maxlength="appConst.max_text_short" :placeholder="$t('CustomSelectComponent.tim_kiem')" v-on:input="(e) => {
					searchOptions()
				}">
			</div>
		</div>
		<div class="select-options-container" ref="optionsContainer" v-on:scroll="(e) => {
			scrollPosition = optionsContainer?.scrollTop ?? 0
		}" v-if="list_item_show.length">
			<button :id="`item_option_${itemOption[props.field_value]}`" class="item-option" :class="{
				'selected':
					itemOption[props.field_value] == selected_option
					|| (selected_options.findIndex((e: any) => {
						return e == itemOption[props.field_value]
					}) != -1)
			}" v-for="itemOption in list_item_show" v-on:click="() => {
				changeValue(itemOption)
			}">
				<slot name="render_item_option" v-if="$slots.render_item_option" :item="itemOption"></slot>
				<span v-else>{{ itemOption[props.field_title] }}</span>

			</button>
		</div>
		<div class="select-options-container none-list" ref="optionsContainer" v-else>
			<span>
				{{ $t('CustomSelectComponent.danh_sach_trong') }}
			</span>
		</div>
		<div class="select-footer">
			<button class="unselect-all" v-if="props.multiple"
				:disabled="!selected_options?.length"
				v-on:click="()=>{
					selected_options = []
				}"
			>
				{{ $t('CustomSelectComponent.bo_chon_tat_ca') }}
			</button>
			<button class="select-multiple-submit" v-if="props.multiple"
				v-on:click="()=>{
					handleModalUpdate();
				}"
			>
				{{ $t('CustomSelectComponent.chon') }}
			</button>
		</div>
	</v-overlay>

</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber, nonAccentVietnamese } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import login_first from '~/assets/imageV2/login-first.png';
definePageMeta({
	keepalive: true
})
const props = defineProps({
	_key: null,
	list_item: null,
	field_value: null,
	field_title: null,
	multiple: null,
	searchable: null,
	item_closeable: null,
	clearable: null,
	title: null,
	select_placeholder: null,
	model_value: null,
});
const my_slot = defineSlots<{
	title_icon_left: any,
}>;
const emit = defineEmits(['close', 'select', 'model:update']);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var list_item_temp = ref(props.list_item ?? []);
var list_item_show = ref(props.list_item ?? []);
var search_text = ref("");
var showCustomSelectModal = ref(false);
var webInApp = ref(null as any);
var selected_option = ref<any>(null);
var selected_options = ref<any[]>([]);

var optionsContainer = ref<HTMLElement | null>();
var scrollPosition = useState(`scroll_select_${props._key}`, () => {
	return 0
});

onMounted(async () => {
	if (props.multiple) {
		selected_options.value = [...props.model_value];
		if (selected_options.value.length) {
			document.getElementById(`item_option_${selected_options.value[0]?.[props.field_value]}`)?.scrollIntoView({
				block: 'center'
			})
		}

	}
	else {
		selected_option.value = props.model_value;
		if (selected_option.value) {
			document.getElementById(`item_option_${selected_option.value}`)?.scrollIntoView({
				block: 'center'
			})
		}
	}
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	showCustomSelectModal.value = true;

	setTimeout(() => {
		optionsContainer.value?.scrollTo({
			top: scrollPosition.value
		})
	}, 300);

});
function close() {
	showCustomSelectModal.value = false
	emit('close')
}

function changeValue(item: any) {
	if (props.multiple) {
		let indexCur = selected_options.value.findIndex(function (e) {
			return e == item[props.field_value]
		})
		if (indexCur != -1) {
			selected_options.value.splice(indexCur, 1);
		}
		else {
			selected_options.value.push(item[props.field_value]);
		}
	}
	else {
		if (selected_option.value == item[props.field_value]) {
			selected_option.value = null;
		}
		else {
			selected_option.value = item[props.field_value];
		}
		handleModalUpdate();
	}
	
}
function handleModalUpdate() {
	if (!props.multiple) {
		emit('model:update', selected_option.value);
		close();
	}
	else {
		emit('model:update', selected_options.value);
		close();
	}
}

function searchOptions() {

	let st = nonAccentVietnamese(search_text.value);
	list_item_show.value = list_item_temp.value.filter((e: any) => {
		return nonAccentVietnamese(e[props.field_title]).includes(st) || nonAccentVietnamese(e[props.field_value].toString()).includes(st)
	});
}
</script>

<style lang="scss" src="./CustomSelectStyles.scss"></style>