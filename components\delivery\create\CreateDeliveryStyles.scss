.create-delivery-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  z-index: 100;

  &::-webkit-scrollbar {
    width: 3px;
    position: absolute;
    padding: 0;
    background: linear-gradient(to bottom, var(--linear-color-1) 54px, transparent 54px);
  }

  &>.create-delivery-content-container {
    display: flex;
    flex-direction: column;
    padding: 15px 0;
    width: 100%;
    flex: 1;
    height: fit-content;
    background: white;

    &>.stack-content {
      padding: 10px;
      background: #f5f6fa;
      border-radius: 10px;
      width: calc(100% - 30px);
      // box-shadow: 0 0 15px rgb(0,0,0,.1);
      margin: 0 auto 25px;

      &>.stack-title {
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: bold;
        color: var(--color-text-black);

        & em {
          font-weight: 600;
          font-size: 0.9em;
          color: var(--color-text-note);
        }
      }

      &>.stack-item-content {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        padding: 0 15px;

        &>.stack-label {
          display: flex;
          flex-direction: column;
          font-weight: 600;
          overflow: hidden;

          &>em {
            font-size: 0.8em;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: var(--color-text-note);

            &>span:not(:first-child) {
              border-left: thin solid;
              margin-left: 5px;
              padding-left: 5px;
            }
          }

          &>em.error {
            color: var(--primary-color-2);
          }
        }

        &>.icon-left {
          font-size: 25px;
          min-width: 25px;
          color: #2d8f7e;
        }

        &>.icon-right {
          margin-left: auto;
          font-size: 25px;
          min-width: 25px;
          color: #818181;
        }

        &>.from-marker {
          color: var(--primary-color-1);
        }

        &>.icon-from-to {
          font-size: 25px;
          color: var(--color-text-note);
        }



        &>.partner-logo {
          height: 25px;
          margin-top: 5px;
          margin-right: 10px;
        }

        &>.driver-info {
          display: flex;
          flex: 1;
          width: 100%;

          &>.driver-content {
            display: flex;
            flex: 1;
            align-items: flex-start;
            gap: 10px;

            & .user-avatar {
              width: 50px;
              height: 50px;
              border-radius: 2em;
              object-fit: cover;
              background-color: var(--color-background-2);

            }

            & .user-detail {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              flex: 1;

              & > .h-stack{
                align-items: flex-start;
              }

              & .user-name {
                font-weight: 500;
                align-self: center;
                margin-right: auto;
              }

              & .user-phone {
                color: #5f5e66;
                background: white;
                font-size: 15px;
                height: 30px;
                flex: 1;
                padding: 2px 10px 2px 7px;
                border-radius: 2em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 5px;
                font-weight: 600;
                border: thin solid #ecebf3;
                white-space: nowrap;
                margin-left: 5px;

                &>svg {
                  min-width: 15px;
                }

                &.disabled {
                  background: #f5f6fa;
                  user-select: none;
                  cursor: default;
                }
              }
            }

            & .select-other-driver {
              align-self: flex-start;
            }
          }


        }

        & .stack-content {
          color: #545454;
          font-size: 14px;
          width: 100%;

          &>.content-item {
            display: flex;
            gap: 3px;
            margin: 0 auto 0 0;
            justify-self: stretch;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            color: #2e2d30;

            &>svg {
              color: #e8e7e7;
              font-size: 20px;
            }
          }

          &>.content-item.active {
            &>svg {
              color: var(--primary-color-1);
              font-size: 20px;
            }
          }
        }
      }

      &>.stack-item-content.selectable {
        cursor: pointer;
        user-select: none;
      }

      &>.stack-item-content.special-item {
        padding: 5px 15px;
        border-radius: 10px;
        background: white;
        width: calc(100% - 30px);
        margin: 10px auto;
        border: thin solid white;

        &>.add-button {
          color: #818181;
        }

        & svg {
          color: #818181;
        }

      }

      &>.stack-item-content.special-item.active {
        // background: linear-gradient(to right, white, var(--primary-color-1));
        // box-shadow: inset 0 0 10px var(--primary-color-1);
        border-color: var(--primary-color-1);


        &>.add-button {
          color: var(--primary-color-1);
        }

        & svg {
          color: var(--primary-color-1);
        }
      }

      &>.stack-item-content.note-content {
        align-items: flex-start;

        &>.stack-label {
          flex: 1;
          gap: 5px;

          & textarea {
            min-height: 100px;
            resize: none;
            background: white;
            flex: 1;
            padding: 5px 15px;
            border-radius: 10px;
            outline: none;
          }

          & input {
            resize: none;
            background: white;
            flex: 1;
            padding: 5px 15px;
            border-radius: 10px;
            outline: none;
          }

          &>.price-input {
            width: 100%;
            max-width: 300px;
          }

        }
      }

      &>.stack-item-content.pending-time-content {
        align-items: flex-start;
        margin-top: 5px;

        &>.stack-label {
          flex: 1;
          gap: 5px;

          &>.input-group-container {
            flex: 1;
            align-items: center;
            max-width: 300px;

            &>input {
              border-radius: 10px 0 0 10px;
              text-align: center;
              height: 35px;
              resize: none;
              background: white;
              flex: 1;
              padding: 5px 15px;
              outline: none;
              width: 150px;
            }

            & .time-unit-select {
              flex: 1;
              background: var(--primary-color-1);
              color: white;
              font-size: 700;
              margin-top: 0 !important;
              border-radius: 0 10px 10px 0;
              height: 35px;
              align-items: center;

              & .v-field__input {
                justify-content: center;
              }

              & .v-input__control {
                padding: 0;
                min-height: 35px !important;

                & .v-field__field {
                  align-items: center;

                  & input {
                    align-self: center;
                    margin: auto;
                    text-align: center;
                    background: inherit;
                  }
                }

                & .v-select__selection {
                  align-self: center;
                  font-weight: 700;
                }
              }
            }
          }
        }

      }

      &>.stack-item-content.delivery-info {
        align-items: flex-start;
      }

      &>.mid-line {
        width: 90%;
        margin: 7px auto;
        height: 1px;
        background: #f1f1f1;
      }
    }

    &>.stack-content.full-width {
      width: 100%;
      border-radius: 0;
    }

    &>.stack-content.driver {
      display: flex;
      flex-direction: column;
      gap: 10px;

      & button {
        color: var(--primary-color-1);
        font-weight: 600;
      }
    }

    &>.estimate-delivery-price {
      display: flex;
      flex-direction: column;

      &>.estimate-price {
        display: block;

        &>em {
          color: var(--primary-color-2);
          font-weight: 700;
          margin-left: 5px;
        }
      }

      &>.estimate-notice {
        display: block;

        &>em {
          color: var(--primary-color-2);
          font-weight: 700;
          margin-right: 5px;
        }

        &>.user-phone {
          color: var(--primary-color-1);
          margin-left: 5px;
        }
      }
    }
  }

  &>.create-delivery-footer {
    position: sticky;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 15px;
    background: white;
    width: 100%;
    border-top: thin solid #ccc;

    &>.total {
      font-size: 17px;
      font-weight: bold;
      justify-content: space-between;
      display: flex;
      align-items: center;

      &>.price {
        font-size: 18px;
        color: var(--primary-color-1);
        margin-left: 5px;
      }
    }

    &>.estimate-delivery-price {
      font-size: 17px;
      font-weight: bold;
      justify-content: space-between;
      display: flex;
      flex-direction: column;
      align-items: center;

      &>.estimate-price {
        font-size: 13px;
        display: flex;
        color: #818181;

        &>em {
          margin-left: 5px;
          font-style: normal;
          color: var(--primary-color-2);
        }
      }

      &>.estimate-notice {
        display: block;
        font-size: 15px;
        text-align: center;

        &>em {
          color: var(--primary-color-2);
          font-weight: 700;
          margin-right: 5px;
        }

        &>.user-phone {
          color: var(--primary-color-1);
          margin-left: 5px;
        }
      }
    }

    & .discount-price {
      font-size: 14px;
      font-weight: 600;
      color: #818181;
      justify-content: space-between;
      display: flex;
      align-items: flex-end;

      &>em {
        font-size: 15px;
        color: var(--primary-color-2);
        margin-left: 5px;
      }
    }

    &>button {
      background: var(--primary-color-1);
      color: white;
      min-width: 100px;
      padding: 5px 15px;
      border-radius: 7px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
      margin-top: 5px;
    }
  }


}

.delivery-partner-custom-select {
  & .delivery-partner-item {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    text-align: center;

    &>img {
      height: 25px;
    }
  }

  & .item-option {
    filter: grayscale(1);
  }

  & .item-option.selected {
    filter: grayscale(0);
    color: white !important;
  }
}

.select-user-info-modal {
  padding: 0 !important;
  overflow: hidden;
  max-height: unset !important;
}

.service-type-overlay-container,
.package-info-overlay-container {
  overflow: hidden;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  &>.service-type-container,
  >.package-info-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    height: fit-content;
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    &>.title-header {
      border-radius: inherit;
    }

    &>.service-type-list {
      display: flex;
      flex-direction: column;
    }

    &>.service-type-list.none-list {
      min-height: 100px;
      align-items: center;
      justify-content: center;
      font-style: italic;
      font-size: 17px;
      color: #818181;
      font-weight: 600;
    }

    & .service-type-item {
      padding: 5px 15px;
      border-radius: 10px;
      background: white;
      width: calc(100% - 30px);
      margin: 10px auto;
      border: thin solid white;
      cursor: pointer;
      display: flex;
      gap: 10px;
      background: #f5f6fa;
      animation: none;

      &>.item-type-label {
        display: flex;
        flex-direction: column;
        font-weight: 600;
        overflow: hidden;
        align-items: flex-start;

        &>em {
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: var(--color-text-note);
        }

        &>em.error {
          color: var(--primary-color-2);

        }
      }


      &>.icon-left {
        font-size: 25px;
        min-width: 25px;
        color: #2d8f7e;
      }

      &>.icon-right {
        margin-left: auto;
        font-size: 25px;
        min-width: 25px;
        color: #818181;
      }
    }

    & .service-type-item.active {
      border-color: var(--primary-color-1);
    }

    & .service-type-item:disabled {
      opacity: .5;
    }

    & .stack-content {
      padding: 10px;
      background: #f5f6fa;
      border-radius: 10px;
      width: calc(100% - 30px);
      // box-shadow: 0 0 15px rgb(0,0,0,.1);
      margin: 10px auto;

      &>.stack-title {
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: bold;
        color: var(--color-text-black);

        & em {
          font-weight: 600;
          font-size: 0.9em;
          color: var(--color-text-note);
        }
      }

      &>.stack-item-content {
        display: flex;
        align-items: center;
        gap: 1%;
        flex-wrap: wrap;
        width: 100%;
        padding: 0 15px;
      }

      & .item-weight {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 49%;
        gap: 5px;
        cursor: pointer;
        font-weight: 600;
      }

      & .item-weight.active {
        font-weight: 600;

        &>svg {
          color: var(--primary-color-1);
        }
      }

      &>.size-info {
        display: flex;
        padding: 10px;

        &>input {
          resize: none;
          background: white;
          flex: 1;
          padding: 5px 15px;
          border-radius: 10px;
          outline: none;
        }
      }

      &>.product-type-info {
        display: flex;
        flex-wrap: wrap;
        padding: 10px;
        gap: 10px;

        &>.item-product-type {
          padding: 3px 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: fit-content;
          background: white;
          border: thin solid #ccc;
          border-radius: 5px;
        }

        &>.item-product-type.active {
          color: var(--primary-color-1);
          border-color: var(--primary-color-1);
          font-weight: bold;
        }
      }
    }

    &>.package-info-footer {
      position: sticky;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 10px 15px;
      background: white;
      width: 100%;
      border-top: thin solid #ccc;
      gap: 15px;

      &>button {
        background: var(--primary-color-1);
        color: white;
        min-width: 100px;
        padding: 5px 15px;
        border-radius: 7px;
      }
    }
  }
}

.empty-partners-container {
  min-height: unset !important;
  background-color: white;
  gap: 5px;
  width: auto !important;
  // max-height: 90%;
  max-width: 95% !important;
  padding: 30px 10px 0;
  // min-height: 40dvh;
  border-radius: 10px;
  background-color: white;


  & h3 {
    padding: 0 10px;
    text-align: center;
    color: var(--primary-color-2);
  }

  & .empty-partner-footer {
    display: flex;
    padding: 10px;
    gap: 10px;
  }

  & button {
    // background: #f5f6fa;
    padding: 5px 15px;
    color: #545454;
    border-radius: 2em;
    min-width: 150px;
    margin: auto;
    font-weight: 700;
    text-transform: none;

    &.close{
      background: #f5f5fa;
    }
  }
}