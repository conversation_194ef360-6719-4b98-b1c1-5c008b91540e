.import-from-file-container {
    min-height: unset !important;
    max-width: 95% !important;
    padding: 0;
    width: fit-content;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;

    &>.import-from-file-content {
        display: flex;
        flex-direction: column;
        padding: 10px 0;

        &> .current-file-name{
            color: var(--primary-color-2);
            font-size: 17px;
            text-align: center;
            margin-bottom: 10px;
            font-weight: 700;
        }

        &>.select-file {
            width: 100%;
            max-width: 300px;
            margin: auto;
            border-radius: 7px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 17px;
            font-weight: 700;
            height: 45px;
            overflow: hidden;
            color: var(--primary-color-1);
            border: thin solid;
            cursor: pointer;

            &>input {
                display: none;
            }
        }

        &>.define-field-select {
            margin: 10px 0;
            &>.field-col {
                padding: 5px;

                &>.field-context {
                    display: flex;
                    flex-direction: column;
                    padding: 5px;
                    background: var(--secondary-color-3);
                    border-radius: 7px;
                    align-items: center;
                    justify-content: center;

                    &>label {
                        color: #545454;
                        font-size: 17px;
                        font-weight: 700;
                    }

                    &>input {
                        background: white;
                        border-radius: 7px;
                        margin: 5px 0;
                        padding: 10px;
                        font-size: 15px;
                        height: 45px;
                        font-weight: 600;
                        border: 1px solid rgb(231, 233, 236);
                        outline: none;
                        text-align: center;
                        width: 100%;

                        &:disabled {
                            background: #f5f6fa;
                        }
                    }
                }
            }

        }

        & > .read-file{
            width: auto;
            padding: 10x;
            min-width: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: auto;
            background: var(--primary-color-1);
            color: white;
            // color: var(--primary-color-1);
            font-weight: 700;
            box-shadow: none;
        }
    }
}