.edit-view-template-overlay-container {
  overflow: hidden;
  position: absolute !important;
  z-index: 10000 !important;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  .edit-view-template-modal-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    // animation: slide-up 0.5s ease;

    & > .template-overview {
      flex: 1;
      width: 100%;
      overflow: hidden auto;
      position: relative;
    }

    & .setting-area-overlay {
      & .v-overlay__scrim {
        opacity: 0;
      }
    }

    & .setting-area {
      width: 500px;
      height: 100%;
      max-width: 90%;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 100;
      background: rgba(255, 255, 255, 0.98);
      border-radius: 0;
      color: #545454;
      padding: 20px 0 0;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      display: flex;
      overflow: auto !important;
      animation: slide-right 0.3s ease-in-out;
      @keyframes slide-right {
        0% {
          right: -100%;
        }

        100% {
          right: 0;
        }
      }

      &>div{
        padding: 5px 30px;
      }

      & > .select-template {
        display: flex;
        flex-direction: column;

        & > .template-select-btn {
          padding: 5px;
          color: var(--primary-color-1);
          text-transform: none;
          font-weight: 700;
          font-size: 15px;

          & em {
            font-size: 17px;
            text-transform: uppercase;
            margin-left: 5px;
            color: #545454;
          }
        }
      }

      & > .select-color-template {
        display: flex;
        flex-direction: column;

        & > .color-template-select-btn {
          padding: 5px;
          color: #868686;
          text-transform: none;
          font-weight: 700;
          font-size: 15px;
          margin-top: 15px;

          & em {
            font-size: 17px;
            text-transform: uppercase;
            margin-left: 5px;
            color: #545454;
          }
        }

        & .color-suggest-item {
          display: flex;
          justify-content: center;
          gap: 5px;
          width: 100%;

          & .v-list-item__content {
            display: flex;
            gap: 5px;
            align-items: center;
            justify-content: center;
          }

          & .color-name {
            color: #545454;
            font-size: 15px;
            font-weight: 600;
            margin-right: 5px;
          }
          & .color-preview {
            width: 20px;
            height: 20px;
            margin: 10px;
            border: 2px solid white;
            box-shadow: 0 0 3px 3px rgba($color: #000, $alpha: 0.05);
            border-radius: 5px;
          }
        }
      }

      & > .select-font-template {
        display: flex;
        align-items: center;
        margin-top: 15px;
        gap: 5px;

        & > .font-select-btn {
          padding: 5px;
          color: #868686;
          text-transform: none;
          font-weight: 700;
          font-size: 15px;
          flex: 1;

          & em {
            font-size: 17px;
            text-transform: uppercase;
            margin-left: 5px;
            color: #545454;
          }
        }

        & .font-template-list {
          // display: flex;
          // justify-content: center;
          // flex-direction: column;
          gap: 5px;
          width: 100%;
          max-height: 400px;
          overflow: auto;

          & .v-list-item__content {
            display: flex;
            gap: 5px;
            align-items: center;
            justify-content: center;
          }
        }

        & > .font-resize{
          display: flex;
          align-items: center;
          gap: 10px;

          & > button{
            padding: 0;
            width: 35px;
            font-size: 25px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: unset;
            border-radius: 50%;
          }

          & > span{
            color: var(--primary-color-1);
            font-size: 20px;
            font-weight: 700;
          }
        }
      }

      & > .select-banner-background-template {
        display: flex;
        margin-top: 15px;
        // flex-direction: column;
        gap: 5px;

        @media screen and (max-width: 1024px) {
          flex-direction: column;
        }

        & > .select-banner {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          gap: 5px;
          flex: 1;
          overflow: hidden;

          & > .label {
            font-size: 13px;
            font-weight: 600;
            color: #545454;
          }

          & > .select-image-input {
            display: flex;
            flex: 1;
            border-radius: 5px;
            height: 150px;
            min-height: 150px;
            max-height: 150px;
            width: 100%;
            position: relative;

            & > img {
              object-fit: cover;
              width: 100%;
              height: 100%;
              border-radius: inherit;
            }

            & > .actions {
              position: absolute;
              background: rgba($color: #000, $alpha: 0.2);
              padding: 5px;
              justify-content: flex-end;
              gap: 5px;
              width: 100%;
              height: 100%;
              display: none;
              border-radius: inherit;

              & > button {
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 7px;
                min-width: unset;

                & svg {
                  font-size: 20px;
                }

                &.change-image {
                  color: var(--primary-color-1);
                }
                &.delete-image {
                  color: var(--primary-color-2);
                }

                & input {
                  display: none;
                }
              }
            }

            &:hover .actions {
              display: flex;
            }
          }

          & > .use-default {
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 3px;
            align-items: center;
            white-space: nowrap;
            margin-bottom: 5px;

            & .my-switches {
              height: 25px;
              overflow: hidden;

              & .v-selection-control__wrapper {
                height: 25px;
                overflow: visible;
              }
            }
          }
        }
      }

      & > .select-colors {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: flex-start;
        margin-top: 10px;
        margin-bottom: 40px;

        & .h-stack {
          padding: 5px;
          gap: 5px;
          flex-direction: row;
          align-items: flex-start;
          // flex-wrap: wrap;
          width: 100%;

          & label {
            width: 50%;
          }

          & .color-select-btn {
            display: flex;
            gap: 5px;
            align-items: center;
            animation: none;
            flex: 1;
            // border: 4px solid white;
            border-radius: 10px;
            background: #eaeaea;
            padding: 5px 10px;
            width: 100%;
            // color: white;
            font-weight: 700;

            & > .color-view {
              width: 50px;
              height: 25px;
              border: 3px solid white;
              border-radius: 7px;
            }
          }
        }
      }
    }

    & .update-view-template-footer {
      display: flex;
      padding: 5px 15px;
      gap: 15px;
      justify-content: flex-end;
      background: rgba($color: white, $alpha: 1);
      z-index: 1;
      position: sticky;
      bottom: 0;
      margin-top: auto;

      & > button {
        min-width: 100px;
        text-transform: none;
        font-size: 15px;
        font-weight: 700;
      }
    }
  }

  & .edit-color-temp-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
