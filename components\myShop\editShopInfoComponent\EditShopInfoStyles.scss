.edit-shop-info-modal {
  min-height: 95dvh !important;
  max-height: 95dvh !important;
  height: 95dvh !important;
  min-height: 88vh;
  max-height: 88vh;
  height: 88vh;
}
.edit-shop-info-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  align-items: center;
  justify-content: center;
  overflow: auto;

  // & > .title-header {
  //   width: 100%;
  //   // font-size: 1.6em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & .edit-shop-info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    overflow: auto;

    & > .edit-shop-content-container {
      --padding-content: 15px;
      width: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: var(--padding-content);
      border-radius: 10px;
      overflow: auto;
      gap: 10px;
      background: #f6f5fb;
      font-size: 16px;

      & > .adv {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-end;
        position: relative;
        font-size: 1.2em;

        & > img {
          width: 100%;
          border-radius: 10px;
        }

        & > span {
          width: 100%;
          height: 100%;
          position: absolute;
          bottom: 0;
          right: 0;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: flex-end;
          color: white;
          text-transform: uppercase;
          background: linear-gradient(to top, rgba(0, 0, 0), transparent);
          border-radius: 10px;
          padding: 10px;
          font-size: 0.8em;

          & > span + span {
            font-size: 1.3em;
          }
        }
      }

      & .shop-content {
        margin-top: 10px;
        font-size: 1em;
        gap: 5px;
        & > .label-input {
          color: #2e2d30;
          font-weight: bold;
        }
        & > .label-input.required::after {
          content: attr(data-required);
          font-style: italic;
          color: #2e2d30;
          font-size: 0.85em;
          font-weight: 500;
          margin-left: 0;
        }
        & > .label-input.optional::after {
          content: attr(data-optional);
          font-style: italic;
          color: #2e2d30;
          font-size: 0.85em;
          font-weight: 500;
        }
        & > .input-shop {
          background-color: white;
          height: 45px;
          padding: 10px 10px 10px 30px;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;
        }

        & > .open-hours.input-shop {
          height: fit-content;
          min-height: 45px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          animation: none;
          text-align: left;

          & svg {
            margin-left: auto;
          }
        }

        & > .text-area-shop.input-shop {
          min-height: 100px;
          height: unset;
        }
        & > .counter {
          text-align: right;
          font-size: 0.9em;
        }
        & > .text-area-shop.input-shop::placeholder {
          font-size: 0.9em;
        }

        & > .error-message {
          font-size: 0.9em;
        }

        & > .business-type-select,
        > .place-select {
          background-color: white;
          height: 45px;
          padding: 10px 10px 10px 30px !important;
          border: none;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          position: relative;

          & button {
            padding: 0 !important;
            display: flex;
            align-items: center;
            width: 100%;
            cursor: pointer !important;
            animation: none;

            & svg {
              font-size: 25px;
              margin-left: auto;
            }
          }
        }

        & > .submit-button {
          width: 100%;
          padding: 10px 0;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          gap: 5px;
          background-color: #1ba9ff;
          border-radius: 5px;
        }

        & .language-select {
          background-color: white;
          // height: 45px;
          // padding: 10px 10px 10px 30px;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;

          & .v-field {
            --v-input-control-height: 100%;
          }

          & .v-input__control {
            padding: 5px 10px 5px 30px;
            height: fit-content;
            min-height: 45px;
          }

          & .v-input__details {
            display: none;
          }

          & .chip-language {
            background: #2e2d30;
            color: white;
            padding: 0px 10px;
            border-radius: 2em;
          }

          & .v-field__append-inner {
            padding: 0;
            align-items: center;
            font-size: 25px;
          }

          & .v-field__clearable {
            padding: 0;
            align-items: center;
          }

          & .v-field__input {
            height: 100%;
            align-items: center;
            padding: 0;
            min-height: 0;
          }
          & .v-field__input input::placeholder {
            // color: #2e2d30;
            opacity: 1;
          }
        }
      }

      & .map-container {
        width: 100%;
        flex: 1;
        min-height: 250px;
        height: 250px;
        border-radius: 10px;

        & .leaflet-container {
          border-radius: 10px;
          min-height: inherit;

          & .marker-location {
            width: 30px;
            height: 40px;
            position: absolute;
            bottom: 50%;
            left: 50%;
            object-fit: contain;
            transform: translateX(-50%);
            z-index: 450;

            & > img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
      & .shop-banner-container {
        --aspect-ratio: 2.2;
        // aspect-ratio: var(--aspect-ratio);
        height: calc((100dvw - var(--padding-content)*2) / var(--aspect-ratio));
        position: relative;
        display: flex;
        justify-content: center;
        width: 100%;
        align-items: center;
        box-shadow: 0 0 10px rgb(0, 0, 0, 15%);
        border-radius: 10px;
        overflow: hidden;
        margin: 15px 0;

        @supports not (aspect-ratio: var(--aspect-ratio)) {
          &::before {
            float: left;
            padding-top: calc(var(--width-view / var(--aspect-ratio)));
            content: "";
          }

          &::after {
            display: block;
            content: "";
            clear: both;
          }
        }

        & > img.selected-banner {
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > img:not([class*="selected"]) {
          object-fit: cover;
          width: 100%;
          height: 250px;
          background-color: var(--color-background-2);
        }

        & > div.select-image {
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;

          & > div {
            position: relative;
            width: 100%;
            font-size: 1.2em;
            background: black;
            color: white;
            height: 100%;
            display: flex;
            border-radius: 10px;
            padding: 7px;
            cursor: pointer;
            opacity: 0.5;

            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: 2;
            }

            & input::file-selector-button {
              cursor: pointer;
            }
          }
        }
      }

      & .cropper-selected-logo{
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 10px;

        & > .cropper{
          max-height: 300px;
        }
        & .selected-logo-background{
          background: transparent;
        }

        & .preview {
          position: relative;
          margin-top: 20px;
        }
        .image-container {
          position: relative;
          overflow: hidden;
        }
        & .stencil-preview {
          position: absolute;
          border: 2px dashed red; /* Visualize stencil area with dashed border */
          pointer-events: none; /* Prevent interaction with the stencil preview */
        }
      }

      & .shop-logo-container {
        position: relative;
        display: flex;
        justify-content: center;
        width: 100%;
        align-items: center;
        box-shadow: 0 0 10px rgb(0, 0, 0, 15%);
        width: 200px;
        height: 200px;
        border-radius: 50%;
        overflow: hidden;
        margin: 15px auto;

        & > img.selected-logo {
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > img:not([class*="selected"]) {
          object-fit: cover;
          width: 200px;
          height: 200px;
          background-color: var(--color-background-2);
        }

        & > div.select-image {
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;

          & > div {
            position: relative;
            width: 100%;
            font-size: 1.2em;
            background: black;
            color: white;
            height: 100%;
            display: flex;
            border-radius: 10px;
            padding: 7px;
            cursor: pointer;
            opacity: 0.5;

            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: 2;
            }

            & input::file-selector-button {
              cursor: pointer;
            }
          }
        }
      }

      & .image-actions {
        display: flex;
        gap: 10px;

        & > button {
          background-color: #efefef;
          color: #595e63;
          border-radius: 10px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          flex: 1;
          font-weight: 600;

          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: 2;
          }

          & input::file-selector-button {
            cursor: pointer;
          }
        }

        & > button:not(:disabled):active,
        > button.active {
          background-color: var(--primary-color-1);
          color: white;
        }
      }

      & .edit-image {
        display: flex;
        flex-direction: column;
        & > .action-edit-image {
          display: flex;
          flex-direction: column;

          & > .edit-image-slider {
            width: 85%;
            margin-left: auto;

            & [class*="fill"] {
              display: none;
            }

            & .v-input__details {
              display: none;
            }
          }
        }
      }
    }
  }

  // & .edit-shop-info-tab-header {
  //   color: var(--primary-color-1);
  //   font-size: 1em;
  //   width: 100%;
  // }
  // & .tab-button.active {
  //   color: var(--primary-color-1);
  //   border-color: var(--primary-color-1);
  // }
  // & .tab-button {
  //   flex: 1;
  //   text-transform: none;
  //   font-weight: 600;
  //   font-size: 1em;
  //   color: var(--color-text-note);
  //   border-bottom: 2px solid transparent;
  // }
  // & .tab-content-container {
  //   width: 100%;
  //   flex: 1;
  //   display: flex;

  //   & div[class*="v-window"] {
  //     flex: 1;
  //     display: flex;
  //     overflow: hidden;
  //   }
  // }
  // & .tab-content {
  //   padding: 0 10px;
  //   margin-bottom: 20px;
  //   width: 100%;
  //   height: 100%;
  //   overflow: auto;

  //   & .shop-banner-container {
  //     position: relative;
  //     display: flex;
  //     justify-content: center;
  //     width: 100%;
  //     align-items: center;

  //     & > img {
  //       margin-bottom: 10px;
  //       border-radius: 10px;
  //       flex: 1 1;
  //       height: 250px;
  //       width: 100%;
  //       object-fit: cover;
  //       background-color: var(--color-background-2);
  //     }

  //     & > div.select-image {
  //       position: absolute;
  //       top: 10px;
  //       right: 10px;
  //       cursor: pointer;

  //       & > div {
  //         position: relative;
  //         width: 100%;
  //         font-size: 1.2em;
  //         background: black;
  //         color: white;
  //         height: 100%;
  //         display: flex;
  //         border-radius: 10px;
  //         padding: 7px;
  //         cursor: pointer;
  //         opacity: .5;

  //         & input {
  //           opacity: 0;
  //           width: 100%;
  //           height: 100%;
  //           position: absolute;
  //           top: 0;
  //           left: 0;
  //           cursor: pointer;
  //           z-index: 2;
  //         }

  //         & input::file-selector-button {
  //           cursor: pointer;
  //         }
  //       }
  //     }
  //   }

  //   & .shop-content {
  //     margin-top: 10px;

  //     & > .checkbox-input-label {
  //       gap: 5px;
  //       display: flex;
  //       cursor: pointer;
  //       user-select: none;
  //       font-weight: 500;
  //       color: var(--primary-color-1);

  //       & span {
  //         color: var(--primary-color-1);
  //       }

  //       & em {
  //         color: var(--primary-color-2);
  //       }
  //     }

  //     & em {
  //       color: var(--primary-color-1);
  //     }
  //   }

  //   & .map-container{
  //       width: 100%;
  //       flex: 1;
  //       border-radius: 10px;
  //       min-height: 250px;

  //       & .leaflet-container{
  //           border-radius: 10px;
  //       }
  //   }
  // }
  // & .search-select-empty {
  //   color: var(--color-text-note);
  // }

  // & .edit-shop-info-actions{
  //   width: 100%;
  //   justify-content: space-evenly;
  //   padding: 5px;
  // }
}
