.filter-result-container .list-result .result-item-container {
    display: flex;
    // border: thin solid transparent;
    border-radius: 10px;
    // margin-bottom: 25px;
    // box-shadow: 2px 4px 8px 0 rgba(0, 0, 0, 0.15);
    gap: 10px;
    background: white;
    color: #2f3640;
    position: relative;

    & a {
        color: unset;
    }

    & .shop-content {
        animation: none !important;
        // box-shadow: 0 0 5px rgb(0 0 0 / 20%);
        border-radius: 10px;
        overflow: hidden;

        &.loading{
            height: max-content;
            min-height: 500px;
        }
    }

    & .shop-content-container {
        display: flex;
        flex-direction: column;

        width: 100%;
        overflow: hidden;
        text-align: left;

        &>.shop-detail {
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 3px;
            flex: 1;
            width: 100%;
            overflow: hidden;

            &>.name-distance {
                display: flex;
                justify-content: space-between;
                line-height: normal;
                align-items: flex-start;
                gap: 5px;

                &>.shop-name {
                    color: var(--primary-color-1);
                    font-weight: 700;
                    font-size: 15px;
                    text-align: left;
                    display: -webkit-box;
                    line-clamp: 2;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                &>.shop-distance {
                    font-size: 15px;
                    color: var(--primary-color-2);
                    font-weight: 700;
                    font-style: normal;
                    display: flex;
                    align-items: center;

                    &>em {
                        font-style: normal;
                    }

                    &>svg {
                        margin-left: 3px;
                        color: var(--primary-color-1);
                        font-size: 17px;
                    }
                }
            }

            &>.shop-address {
                font-weight: 400;
                color: #8c8c8c;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 2px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;

                &>span {
                    text-overflow: ellipsis;
                    overflow: hidden;
                }

                &>svg {
                    min-width: 14px;
                }
            }

            &>.business-type-rating {
                --font-size: 13px;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                min-height: calc(var(--font-size) * var(--line-height));

                &>.business-name {
                    color: #545454;
                    font-weight: 700;
                    font-size: var(--font-size);
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                &>.rating {
                    --font-size: 15px;
                    font-weight: 500;
                    color: #545454;
                    font-size: var(--font-size);
                    display: flex;
                    align-items: flex-end;
                    margin-left: auto;
                    min-width: fit-content;

                    &>svg {
                        color: #ffe500;
                        font-size: 20px;
                        margin-right: 5px;
                    }
                }
            }
        }
    }

    & .shop-content-container.product-content {
        flex-direction: row;
        border: thin solid transparent;
        padding-bottom: 1px;
        border-bottom-color: var(--linear-color-1);

        & .shop-filter-logo {
            border-radius: 5px 0 0 5px;
        }

        & .shop-detail {
            padding: 0 10px;
            gap: 0;
            justify-content: center;

            & .shop-name {
                line-clamp: 1;
                -webkit-line-clamp: 1;
            }
        }

        & .shop-distance {
            font-size: 15px;
            color: var(--primary-color-2);
            font-weight: 700;
            font-style: normal;
            display: flex;
            align-items: center;

            &>em {
                font-style: normal;
            }

            &>svg {
                margin-left: 3px;
                color: var(--primary-color-1);
                font-size: 17px;
            }
        }
    }

    // & .shop-content {
    //   display: flex;
    //   padding: 10px;
    //   border-radius: 10px;
    //   border: thin solid transparent;

    //   & > a {
    //     width: 100%;
    //     gap: 10px;
    //     display: flex;
    //     align-items: flex-start;
    //     justify-content: flex-start;
    //   }
    //   & .shop-banner {
    //     width: 100px;
    //     height: 100px;
    //     border-radius: 50%;
    //     box-shadow: 0 0 1em rgb(0, 0, 0, 0.1);
    //     aspect-ratio: 1;
    //     max-height: 200px;
    //     object-fit: cover;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     overflow: hidden;

    //     & > .logo-origin-container {
    //       transform: scale(calc(100 / var(--scale-origin)));
    //     }

    //     & > img {
    //       width: 100%;
    //       height: 100%;
    //       object-fit: cover;
    //     }
    //   }

    // & .shop-info {
    //   display: flex;
    //   flex-direction: column;
    //   justify-content: flex-start;
    //   align-items: flex-start;
    //   flex: 1;
    //   gap: 10px;
    //   text-align: left;

    //   & > .shop-name {
    //     color: var(--primary-color-1);
    //     font-weight: bold;
    //     display: -webkit-box;
    //     -webkit-box-orient: vertical;
    //     -webkit-line-clamp: 2;
    //     line-clamp: 2;
    //     overflow: hidden;
    //     text-overflow: ellipsis;
    //     font-size: 1.1em;
    //   }

    //   & > .shop-address {
    //     color: var(--color-text-black);
    //     display: -webkit-box;
    //     -webkit-box-orient: vertical;
    //     -webkit-line-clamp: 2;
    //     line-clamp: 2;
    //     overflow: hidden;
    //     text-overflow: ellipsis;
    //     font-size: 0.9em;
    //   }

    //   & > .shop-distance {
    //     display: flex;
    //     align-items: flex-end;
    //     font-style: italic;
    //     font-size: 0.9em;
    //     color: #6f6e6e;
    //     font-weight: 600;
    //     white-space: nowrap;
    //     width: 100%;

    //     & > a {
    //       margin-left: auto;
    //       > button {
    //         padding: 0px 10px;
    //         color: #28282a;
    //         border: thin solid #7d7c82;
    //         border-radius: 5px;
    //         white-space: nowrap;
    //         height: fit-content;
    //       }
    //     }

    //     & > em {
    //       margin-left: 5px;
    //     }
    //   }
    // }
    // }
    // & .shop-content:hover,
    // .shop-content:active {
    //   background: linear-gradient(#fff7f8, #fff7f8) padding-box,
    //     linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
    // }
    & .shop-products-container {
        width: 100%;
        border-bottom: thin dashed color-mix(in srgb, var(--linear-color-1) 50%, transparent);
        padding: 10px;
        align-items: center;

        &>.products-container {
            display: flex;
            width: 100%;
            align-self: normal;
            align-items: flex-start;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            color: #626262;
            border: thin solid transparent;
            align-items: center;
            // box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);

            &>img {
                width: 100px;
                height: 100px;
                object-fit: cover;
            }

            &>.products-content {
                padding: 0 10px;
                display: flex;
                flex: 1;
                flex-direction: column;
                gap: 5px;
                justify-content: flex-end;
                line-height: normal;

                &>.name {
                    color: var(--primary-color-1);
                    text-align: left;
                    font-weight: 700;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                &>.shop-name {
                    display: flex;
                    width: 100%;
                    gap: 5px;
                    align-items: baseline;
                    overflow: hidden;
                    font-weight: bold;
                    font-style: italic;
                    color: #399901;
                    text-align: center;
                    font-size: 0.9em;
                    justify-content: center;
                }

                &>.sold-like-amount {
                    color: #a8a7a7;
                    font-size: 11px;
                    line-height: 13px;
                    min-height: 13px;
                }

                & .price-actions {
                    display: flex;
                    align-items: flex-end;
                    justify-content: space-between;

                    & .price-container {
                        display: flex;
                        gap: 0;
                        flex-direction: column;
                        justify-content: flex-start;
                        text-align: left;
                        align-items: flex-start;
                        line-height: 1;
                    }

                    & .price {
                        white-space: nowrap;
                        font-weight: 900;
                        width: fit-content;
                        color: var(--primary-color-2);
                        font-size: 15px;
                    }

                    & .origin-price {
                        white-space: nowrap;
                        overflow: hidden;
                        text-decoration: line-through;
                        text-overflow: ellipsis;
                        font-weight: 400;
                        font-size: 13px;
                        min-height: calc(13px * 1);
                        color: #545454;
                        font-style: normal;
                        overflow: hidden;
                    }

                    & .origin-price.hide {
                        opacity: 0;
                    }

                    &>.add-to-cart {
                        color: var(--primary-color-2);
                        margin-left: auto;
                        margin-top: auto;
                        width: 25px;
                        min-width: 25px;
                        height: 25px;
                        display: flex;
                        font-size: 25px;
                        align-items: center;
                        justify-content: center;
                    }
                }

                &>.shop-selected-name {
                    --font-size: 15px;
                    font-weight: 700;
                    color: var(--primary-color-1);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: var(--font-size);
                    white-space: nowrap;
                    min-height: calc(var(--font-size) * var(--line-height));
                    display: flex;
                    align-items: center;
                    width: 100%;

                    &>span {
                        width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-align: left;
                    }

                    &>button.close {
                        color: #a8a7a7;
                        font-size: 20px;
                        margin-left: auto;
                        display: flex;
                    }
                }

                &>.shop-selected-address {
                    --font-size: 13px;
                    --line-height: 1.5;
                    font-weight: 400;
                    color: #8c8c8c;
                    font-size: var(--font-size);
                    overflow: hidden;
                    width: 100%;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    min-height: calc(var(--font-size) * var(--line-height));
                    margin-top: 5px;
                    text-align: left;
                }

                &>.shop-selected-business-rating {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    min-height: calc(var(--font-size) * var(--line-height));
                    width: 100%;
                    text-align: left;

                    &>.business-name {
                        color: #545454;
                        font-weight: 700;
                        font-size: var(--font-size);
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &>.rating {
                        --font-size: 17px;
                        font-weight: 500;
                        color: #545454;
                        font-size: var(--font-size);
                        display: flex;
                        align-items: flex-end;
                        margin-left: auto;
                        min-width: fit-content;

                        &>svg {
                            color: #ffe500;
                            font-size: 25px;
                            margin-right: 5px;
                        }
                    }
                }
            }

            &>.shop-recent-logo {
                box-shadow: none;
                background: white;
                border-radius: 5px 0 0 5px;
                cursor: pointer;

                &>.logo-origin-container {
                    transform: scale(calc(100 / var(--scale-origin)));
                }

                &>img {
                    min-width: 100%;
                    min-height: 100%;
                    object-fit: cover;
                }
            }
        }

        &>.products-container:hover {
            background-color: #ebfeff;
            border: thin solid var(--primary-color-1);
        }
    }

    & .shop-products-container:last-child {
        border-bottom: none;
    }
}