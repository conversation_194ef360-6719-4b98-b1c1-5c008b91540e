<template>
    <div class="public-container" v-on:scroll="($event) => listOrderScroll($event)">

        <div class='my-shop-manage-container' v-if="!isErrored">
            <SubHeaderV2Component :title="shopData?.name">
                <template v-slot:header_right v-if="tabIndex == tabList.products.key">
                    <button class="filter-button" v-on:click="async () => {
                        productShowGrid = !productShowGrid
                    }">
                        <Icon name="iconoir:view-grid" v-if="productShowGrid" />
                        <Icon name="iconoir:list" v-else />
                    </button>
                    <v-menu class="bootstrap-dropdown-container" location="bottom right">
                        <template v-slot:activator="{ props }" v>
                            <button class="button-mode button-fitler-sort" dark v-bind="props">
                                <!-- {{ dataFilterSort.filter(e => e.value == productSortBy)[0].title }} -->
                                <Icon name="hugeicons:sorting-01" />
                            </button>
                        </template>
                        <v-list>
                            <v-list-item v-for="(item, index) in dataFilterSort" :key="'sort_' + item.value"
                                class="filter-sort-dropdown-item" :active="productSortBy == item.value"
                                activeClass="active" v-on:click="() => {
                                    productSortBy = item.value;
                                }">
                                <v-list-item-title>{{ $t('MyShopManageComponent.' + item.titleKey)
                                    }}</v-list-item-title>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </template>
            </SubHeaderV2Component>
            <div class="sticky-header">
                <v-tabs v-model="tabIndex" draggable="false" class="manage-tab" color="var(--primary-color-1)">
                    <v-tab v-for="(key, indexTab) in Object.keys(tabList)" class="manage-tab-title"
                        :class="tabIndex == tabList[key].key ? 'active' : ''" :value="tabList[key].key"
                        :key="tabList[key].key" v-on:click="() => {
                            tabIndex = tabList[key].key;
                            changeTab()
                        }">
                        <div class="tab-title">
                            {{ $t('MyShopManageComponent.' + tabList[key].key) }}
                        </div>
                    </v-tab>
                </v-tabs>
            </div>
            <div class="my-shop-manage-content" v-if="shopData?.id">

                <v-window v-model="tabIndex" class="tab-content-container" :disabled="true">
                    <v-window-item :value="tabList.products.key">
                        <ManageProductsComponent :showGrid="productShowGrid" :sortBy="productSortBy" :mode="props.mode"
                            :scroll="productTabScroll" :shopData="shopData" />
                    </v-window-item>
                    <v-window-item :value="tabList.categories.key">
                        <ManageCategoriesComponent :mode="props.mode" :shopData="shopData"
                            :scroll="categoryTabScroll" />
                    </v-window-item>
                </v-window>
            </div>
        </div>
        <NoneMyShopComponent v-else :show_header="true" :title="$t('MyShopManageComponent.tieu_de')"></NoneMyShopComponent>
    </div>


</template>
<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';

import icon_for_product from '../../assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg";
import none_shop from "~/assets/image/none-shop.jpg"
import ManageProductsComponent from './manageProducts/ManageProductsComponent.vue';
import ManageCategoriesComponent from './manageCategories/ManageCategoriesComponent.vue';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';


const router = useRouter();
const route = useRoute();
var emit = defineEmits(['close']);
var props = defineProps({
    shopData: {},
    tabIndex: null,
    mode: null
})
var nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('MyShopManageComponent.tieu_de')
})
var productTabScroll = ref(null as any);
var categoryTabScroll = ref(null as any);
var shopService = new ShopService();
var agentService = new AgentService();

var shopData = ref(null as any)
var tabList = ref({
    products: {
        key: 'products',
        title: 'Sản phẩm'
    },
    categories: {
        key: 'categories',
        title: 'Danh mục'
    }
} as any)

var productShowGrid = ref(false);
var productSortBy = ref(3)

var refreshing = ref(false);
var isErrored = ref(false);
var tabIndex = useState<any>('shop_manage_tab', () => { return null });
var dataFilterSort = [
    {
        title: "Giá từ thấp đến cao",
        titleKey: "gia_tu_thap_den_cao",
        value: 1
    },
    {
        title: "Giá từ cao đến thấp",
        titleKey: "gia_tu_cao_den_thap",
        value: 2
    },
    {
        title: "Mới nhất",
        titleKey: "moi_nhat",
        value: 3
    },
    {
        title: "Xếp theo tên a-z",
        titleKey: "xep_theo_ten_a_z",
        value: 4
    },
    {
        title: "Xếp theo tên z-a",
        titleKey: "xep_theo_ten_z_a",
        value: 5
    },
    {
        title: "Đặc trưng",
        titleKey: "dac_trung",
        value: 6
    }
];
onBeforeUnmount(() => {
    // let state = router.options.history.state
    // delete state.tabIndex;
    // history.replaceState(state, document.title);
})
onMounted(async () => {
    if (props.mode == 'agent') {
        await getShopDetailByAgent()
    }
    else {
        await getMyShop();
    }
    if (router.options.history.state.tabIndex) {
        tabIndex.value = router.options.history.state.tabIndex;
    }

})
onUpdated(() => {
})

function getMyShop() {
    shopService.myShop().then(res => {
        if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {

            shopData.value = res.body.data;
            useSeoMeta({
                title: `${shopData?.value?.name} - ${t(`MyShopManageComponent.${tabList.value[tabIndex.value]?.key}`)}`
            })
            changeTab();
        }
        else {
            shopData.value = null;
            isErrored.value = true
        }
        return
    })
}

function getShopDetailByAgent() {
    agentService.agentShopDetail(route.params.id as any).then(res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
            shopData.value = res.body.data;
            useSeoMeta({
                title: `${shopData?.value?.name} - ${t(`MyShopManageComponent.${tabList.value[tabIndex.value]?.key}`)}`
            })
            changeTab();
        }
        else {
            shopData.value = null;
            isErrored.value = true
        }
        return
    })
}

function changeTab() {
    setTimeout(() => {
        useSeoMeta({
            title: `${shopData.value?.name} - ${t(`MyShopManageComponent.${tabList.value[tabIndex.value]?.key}`)}`
        })
    }, 500);

}

function listOrderScroll(event: any) {
    if (tabIndex.value == tabList.value.products.key) {
        productTabScroll.value = event;
        return;
    }
    else if (tabIndex.value == tabList.value.categories.key) {
        productTabScroll.value = event;
        return;
    }
}
</script>

<style lang="scss" src="./MyShopManageStyles.scss"></style>