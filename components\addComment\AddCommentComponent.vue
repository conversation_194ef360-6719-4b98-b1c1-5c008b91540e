<template>
	<VueFinalModal class="my-modal-container" content-class="v-stack form-modal add-comment-container" :click-to-close="false" :overlay-behavior="'persist'"
		v-model="showModal" contentTransition="vfm-fade" v-on:closed="() => {
			close()
		}">
		<SubHeaderV2Component :title="props.initData?.id ? $t('AddCommentComponent.cap_nhat_danh_gia') : $t('AddCommentComponent.them_danh_gia')">
			<template v-slot:header_left>

			</template>
		</SubHeaderV2Component>
		<div class="add-comment-content-container">
			<div class="rate">
				<NuxtRating clas="rating-custom" :inactive-color="'white'" :border-width="4" :read-only="false"
					:rounded-corners="true" :rating-spacing="5" :rating-step="1" :rating-size="40"
					:rating-value="rating || 0" class="rating" v-on:rating-selected="(e: any) => {
						rating = e;
					}" />
				<span class="rate-description">{{ t(`AddCommentComponent.${appConst.rating_text_key[rating]}`) }}</span>
			</div>
			<div class="comment-content">
				<span class="label">{{ t('AddCommentComponent.binh_luan') }} <em class="text-length">({{ review?.length ?? 0 }}/{{ appConst.max_text_short }})</em></span>
				<textarea :placeholder="$t('AddCommentComponent.noi_dung_danh_gia')" v-model="review" name="review"
				:maxlength="appConst.max_text_short"
					id="review_comment" class='review-input'></textarea>
			</div>
			<div class="image">
				<span class="label">{{ t('AddCommentComponent.anh_dinh_kem') }}</span>
				<div class="image-list">
					<div class="select-image" v-if="images.length < 6">
						<label :disabled="images.length >= 6">
							<Icon name="ion:plus-round"></Icon>
							<input type="file" accept='image/*' :multiple="true" v-on:click="(e) => {
								if (images.length >= 6) {
									e.preventDefault()
								}
							}" v-on:change="($event: any) => {
								fileChangeInput($event)
							}" ref="imageFilesName" />
						</label>
					</div>
					<div class="selected-image" v-for="(itemImamge, index) in images">
						<img :src="itemImamge?.id ? (domainImage + itemImamge.path) : itemImamge.src" />
						<div class="action-overlay">
							<button class="delete-image" v-on:click="() => {
								images.splice(index, 1)
							}">
								<Icon name="bi:trash3-fill"></Icon>
							</button>
						</div>

					</div>
				</div>
			</div>
		</div>
		<div class='h-stack action-buttons'>
			<button class='cancel-button' :disabled="isSaving" v-on:click="() => close()">
				{{ $t('AddCommentComponent.thoat') }}
			</button>
			<button class='save-button' :disabled="isSaving" v-on:click="() => submit()">
				<Icon name="eos-icons:loading" v-if="isSaving"></Icon>
				<span v-else>{{ $t('AddCommentComponent.luu_danh_gia') }}</span>
			</button>
		</div>
		<!-- <LoginFirstComponent v-if="!profileData?.id" v-on:close="()=>{
			close()
		}"></LoginFirstComponent> -->
	</VueFinalModal>
</template>

<script lang="ts" setup>

import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";

import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import { ShopService } from "~/services/shopService/shopService";
import ResetCartComponent from "~/components/resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "~/components/confirm18Age/Confirm18AgeComponent.vue";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { RatingService } from "~/services/ratingService/ratingService";
import { ImageService } from "~/services/imageService/imageService";
import LoginFirstComponent from "../loginFirst/LoginFirstComponent.vue";
import { HttpStatusCode } from "axios";
import { AuthService } from "~/services/authService/authService";

const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.CartComponent')
});
var emit = defineEmits([
	'close', 'submit'
])
const props = defineProps({
	showModal: null,
	object: null,
	object_type: null,
	initData: null,
})
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

var ratingService = new RatingService();
var imageService = new ImageService();
var authService = new AuthService();

var showModal = ref(props.showModal);
var rating = ref(props.initData?.rating ?? 5);
var review = ref(props.initData?.review ?? "");
var images = ref<any>( props.initData?.images ? JSON.parse(JSON.stringify( props.initData?.images)) : []);
var profileData = ref(null as any);
var isSaving = ref(false);

var imageFilesName = ref(null as any);

onUpdated(() => {
	showModal.value = props.showModal;

})
onMounted(async () => {
	profileData.value = await authService.checkAuth();
	console.log(props.initData);
	if (!profileData.value?.id) {
		close()
		nuxtApp.$emit(appConst.event_key.require_login, {
			redirect_url: route.path,
			back_on_close: false
		})
	}
})

function close(value?: any) {
	showModal.value = false;
	emit('close', value);
}

function submit() {
	isSaving.value = true;
	if (props.initData?.id) {
		submitEditRating()
	}
	else {
		submitCreateRating()
	}
}

function submitCreateRating() {
	let body = {
		object_id: props.object.id,
		object_type: props.object_type,
		rating: rating.value,
		review: review.value
	}
	ratingService.createRating(body).then(async res => {
		var addImages = async () => {
			await images.value.forEach(async (imageItem: any, index: number) => {
				let profile_picture_ref = {
					path: imageItem.src,
					object_type: appConst.object_type.comment_rating,
					title: `${review.value.slice(0, 20)}... ${index + 1}`,
					description: `${review.value.slice(0, 20)}... ${index + 1}`,
					index: 0,
					orientation: imageItem.orientation,
					isEdit: false,
					parent_id: res.body.data.id
				}
				await imageService.insertImage(profile_picture_ref);

			});
			return
		};
		if (res.status == HttpStatusCode.Ok) {
			if (images.value.length > 0) {
				try {
					await addImages()
				}
				finally {
					// isSaving.value = false;
					toast.success(t('AddCommentComponent.them_thanh_cong'));
					setTimeout(() => {
						close(true);
					}, 1500);

				}
			}
			else {
				// isSaving.value = false;
				toast.success(t('AddCommentComponent.them_thanh_cong'));
				setTimeout(() => {
					close(true);
				}, 1500);
			}
		}
		else {
			isSaving.value = false;
			toast.error(res.body?.message ?? t('AddCommentComponent.them_that_bai'))
		}
	})
}

async function submitEditRating() {
	let body = {
		id: props.initData?.id,
		rating: rating.value,
		review: review.value,
		image_delete: await handleGetListDeleteImage(),
		images: await handleGetListUpdateImage()
	}
	ratingService.updateRating(body).then(async res => {
		// var addImages = async () => {
		// 	await images.value.forEach(async (imageItem: any, index: number) => {
		// 		let profile_picture_ref = {
		// 			path: imageItem.src,
		// 			object_type: appConst.object_type.comment_rating,
		// 			title: `comment_${index + 1}`,
		// 			description: `comment_${index + 1}`,
		// 			index: 0,
		// 			orientation: imageItem.orientation,
		// 			isEdit: false,
		// 			parent_id: res.body.data.id
		// 		}
		// 		await imageService.insertImage(profile_picture_ref);

		// 	});
		// 	return
		// };
		var handleSaveNewImage = async () => {
			await Promise.all(
				images.value.map(async (img: any, index: any) => {
					let image_comment = {
						path: img.src,
						object_type: appConst.object_type.comment_rating,
						title: `${review.value.slice(0, 20)}...`,
						description: `${review.value.slice(0, 20)}...`,
						index: index,
						orientation: img.orientation,
						isEdit: false,
						parent_id: res.body.data.id,
						is_profile_picture: index == 0 ? true : false
					}

					if (!img.id) {
						await imageService.insertImage(image_comment)
					}
				})
			)
		}
		if (res.status == HttpStatusCode.Ok) {
			if (images.value.length > 0) {
				try {
					// await addImages()
					await handleSaveNewImage();
				}
				finally {
					// isSaving.value = false;
					toast.success(t('AddCommentComponent.them_thanh_cong'));
					setTimeout(() => {
						close(true);
					}, 1500);

				}
			}
			else {
				// isSaving.value = false;
				toast.success(t('AddCommentComponent.them_thanh_cong'));
				setTimeout(() => {
					close(true);
				}, 1500);
			}
		}
		else {
			isSaving.value = false;
			toast.error(res.body?.message ?? t('AddCommentComponent.them_that_bai'))
		}
	})
}
async function fileChangeInput(fileInput: any, childProduct?: any) {
	if (fileInput.target.files.length) {
		if ((fileInput.target.files.length + images.value.length) > appConst.defaultMaxImageAmout) {
			toast.error(t('AddCommentComponent.so_luong_toi_da', { amount: appConst.defaultMaxImageAmout }))
		}
		else {
			imageFilesName.value = fileInput.target.files;
			for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
				if (fileInput.target.files[i].size > appConst.image_size.max) {
					let imgErr = t('AddCommentComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
					// toast.error(imgErr);
				}
				else {
					const reader = new FileReader();
					reader.onload = async (e: any) => {
						const image = new Image();
						image.src = e.target.result;

						let orientationExif;
						let imgTemp = {
							src: "",
							orientation: 0,

						};
						if (fileInput.target.files[i].type != 'image/webp') {
							orientationExif = await exifr.orientation(image) || 0;
						}
						else orientationExif = 0;
						imgTemp.src = image.src;
						imgTemp.orientation = orientationExif ? orientationExif : 0;
						images.value.push(imgTemp);
					}
					await reader.readAsDataURL(fileInput.target.files[i]);
				}
			};
		}
	}
}

function handleGetListDeleteImage() {
	let listDelete: any[] = [];
	props.initData?.images.forEach((img: any, index: number) => {
		let indexInCurrentList = images.value?.findIndex(function (e: any) {
			return e.id == img.id;
		})
		if (indexInCurrentList == -1) {
			listDelete.push({ id: img.id })
		}
	});
	return listDelete;
}

function handleGetListUpdateImage() {
	let listUpdate: any[] = [];
	images.value.forEach((img: any, index: number) => {
		let indexInCurrentList = props.initData?.images?.findIndex(function (e: any) {
			return e.id == img.id;
		})
		if (indexInCurrentList != -1) {
			let updateImg;
			updateImg = {
				id: img.id,
				title: img.title,
				index: indexInCurrentList,
				is_profile_picture: indexInCurrentList == 0 ? true : false
			}
			listUpdate.push(updateImg)
		}
	});
	return listUpdate;
}
</script>

<style lang="scss" src="./AddCommentStyles.scss"></style>