.zalo-oauth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  background-color: white;
  position: relative;
  padding: 10px;
  font-size: 20px;
  background-color: var(--color-background-2);

  & img {
    width: 200px;
  }

  & span {
    font-size: 20px;
  }
}
.user-disabled-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
  position: relative;

  & button {
    margin-left: auto;
    font-size: 25px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
  & img {
    width: 300px;
    object-fit: contain;
    margin: auto;
  }

  & .message {
    font-size: 1.3em;
    // color: var(--primary-color-1);
    text-align: center;

    & a {
      font-weight: 600;
      color: var(--primary-color-1);
    }
  }
}
