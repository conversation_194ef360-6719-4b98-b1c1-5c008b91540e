<template>
    <div class="video-hls-player-container" ref="videoContainer">
        <div class="video-error-content" :title="$t('VideoHLSPlayerComponent.tai_lai')" v-if="isErrorVideo"
            v-on:click="() => { reloadVideo() }">
            <img :src="video_broken" />
        </div>

        <div class="video-thumbnail" v-if="props.thumbnail && !isErrorVideo" v-on:click="() => {
            // initVideo();
            playFullScreen()
        }">
            <img loading="lazy" class="thumb" :src="props.thumbnail" />

        </div>

        <video v-show="!isErrorVideo" ref="video" :poster="props.thumbnail ?? ''" :controls="props.controls == true"
            :muted="mute" :height="height" :preload="'auto'" :dragable="false" @mouseenter="() => {
                // playVideo() 
            }" @mouseleave="() => {
                // pauseVideo() 
            }" @click="(e: any) => {
                // e.preventDefault();
                playFullScreen()
                if (isPlaying == true) {

                    emits('clickOnPlaying')
                }
            }" @error="handleVideoError()" @loadeddata="() => { isLoading = false }">
        </video>
        <Icon class="play-icon" name="solar:play-broken" v-if="!isErrorVideo" v-on:click="() => { playFullScreen() }">
        </Icon>
        <v-overlay v-model="isLoading" :z-index="100" :absolute="false" contained content-class='spinner-container'
            persistent scrim="#fff" key="loading_sale_off" no-click-animation>
            <Icon name="eos-icons:loading"></Icon>
        </v-overlay>
    </div>
</template>

<script setup lang="ts">
import Hls from 'hls.js';
import { increase_view_obj_type } from '~/assets/appDTO';
import video_broken from '~/assets/imageV2/video-broken.webp';
import { InteractionService } from '~/services/interactionService/interactionService';

interface HlsPlayerProps {
    src: string;
    width?: number | string;
    height?: number | string;
    controls: boolean;
    thumbnail?: string | null;
    video_id: string
}

var interactionService = new InteractionService();

const props = defineProps<HlsPlayerProps>();
const emits = defineEmits(['clickOnPlaying']);
const videoContainer = ref<HTMLElement | null>(null)
const video = ref<HTMLVideoElement | null>(null);
const { t } = useI18n()

var mute = ref(true)
var isPlaying = ref(false);
var isErrorVideo = ref(false);
var isLoading = ref(false);
var viewTimeout: any;
onMounted(() => {
    initVideo()
    document.addEventListener('fullscreenchange', handleFullscreenChange)
});

onUnmounted(() => {
    // Clean up event listener when component unmounts
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
const handleFullscreenChange = () => {
    if (document.fullscreenElement === video.value) {
        // Unmute when entering fullscreen
        mute.value = false
        video.value?.play();
        increaseView();
    } else {
        // Mute when exiting fullscreen
        mute.value = true;
        video.value?.pause()
        clearTimeout(viewTimeout);
    }
}

function initVideo() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting && video.value) {
                if (Hls.isSupported()) {
                    const hls = new Hls();
                    hls.loadSource(props.src);
                    hls.attachMedia(video.value);
                    hls.on(Hls.Events.ERROR, (event, data) => {
                        console.log(data);
                        isLoading.value = false;
                        if (data.fatal) {
                            isErrorVideo.value = true;
                            // handleHlsError(data);
                        }
                        else {
                            isErrorVideo.value = false;
                        }
                    });
                }
                else if (video.value.canPlayType('application/vnd.apple.mpegurl')) {
                    // Fallback for Safari
                    video.value.src = props.src;
                }
                isLoading.value = false;
                observer.disconnect()
            }
        })
    }, {
        threshold: 1
    })
    if (videoContainer.value) {
        observer.observe(videoContainer.value)
    }
}

function increaseView(obj_id = props.video_id) {
    if (obj_id) {
        viewTimeout = setTimeout(() => {
            const key = `view_video_${obj_id}`;
            if (interactionService.shouldIncreaseViewCount(key)) {
                console.log('tang luot view')
                interactionService.increaseView(obj_id, increase_view_obj_type.video)
            }
            else {
                console.log('da tang luot view roi')
            }
        }, 3000);
    }
}

function playVideo() {
    setTimeout(() => {
        video.value?.play();
        isPlaying.value = true;
        mute.value = false;
    }, 100);
}
function pauseVideo() {
    setTimeout(() => {
        video.value?.pause();
        isPlaying.value = false;
        mute.value = true;
    }, 100);

}
function togglePlay() {
    if (video.value?.paused) {
        playVideo()
    }
    else {
        pauseVideo()
    }
}

function handleVideoError() {
    isErrorVideo.value = true;
    isLoading.value = false;
}

function reloadVideo() {
    isErrorVideo.value = false;
    isLoading.value = true;
    // initVideo();
    video.value?.load();
    video.value?.play();
}
function playFullScreen() {
    // initVideo();
    video.value?.requestFullscreen();
}
</script>

<style lang="scss" src="./VideoHLSPlayerStyles.scss"></style>