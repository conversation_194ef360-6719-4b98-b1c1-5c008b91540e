.forget-password-container {
  flex: 1;
  display: flex;
  font-size: 1.2em;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  background: white;
  max-width: var(--max-width-content-view-720) !important;
  // > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;

  //   & > .header-left {
  //     margin-right: auto;
  //   }
  // }

  & .forget-password-content {
    padding: 20px 10px 30px 10px;
    
    flex: 1;
    display: flex;
    flex-direction: columns;
    overflow: auto;
  }

  & .otp-input {
    color: var(--primary-color-1);
  }

  & .step-content {
    gap: 10px;
    padding: 20px 10px 0 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: fit-content;
    width: 100%;

    & > .by-email,
    .by-phone {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    & > .get-protocol {
      color: var(--primary-color-1);
      font-weight: 600;
      text-align: right;
    }
    & > img {
      justify-content: center;
      width: 150px;
      height: 150px;
      object-fit: contain;
      margin: 10px auto;
    }

    & .step-title {
      font-size: 1.5em;
      color: var(--primary-color-1);
      text-align: center;
      font-weight: 600;
    }

    & .step-text {
      margin: 10px 0;
      font-size: 1em;
      color: var(--color-text-note);
      font-weight: 600;
      width: 100%;

      & > span {
        color: var(--primary-color-1);
      }
    }

    & .step-action {
      background-color: var(--primary-color-1);
      gap: 10px;
      color: white;
      align-items: center;
      justify-content: center;
      padding: 5px;
      border-radius: 5px;
    }

    & .password-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
    }

    & .password-input-group > input {
      flex: 1;
      overflow: auto;
    }

    & .password-input-group > button {
      background: transparent;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      color: var(--primary-color-1);
    }

    & .password-input-group > button:hover {
      background-color: #ddd;
    }

    .content-input {
      background: transparent;
      border: none;
      outline: none;
      padding: 10px 0;
      border-radius: inherit;
    }
    & .checkbox-input-label {
      gap: 5px;
      display: flex;
      cursor: pointer;
      user-select: none;
      font-weight: 500;
      color: var(--primary-color-1);
      font-size: 20px;     
      align-items: center;
      justify-content: flex-end;   
      margin-left: auto;

      & span {
        font-size: 17px;
        color: var(--primary-color-1);
      }

      & em {
        color: var(--primary-color-1);
        font-size: 14px;
        line-height: normal;
        align-self: center;
      }
    }
    .label {
      color: var(--color-text-black);
      margin: 5px 0;
      font-size: 15px;
    }
    .content-input-group:focus-within {
      border-color: var(--primary-color-1) !important;
    }
    .content-input-group:hover{
      border-color: var(--color-text-black);
    }
    .content-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border: thin solid var(--color-text-note);
      padding-left: 10px;
      border-radius: 5px;

      & > input {
        flex: 1;
        overflow: auto;
      }
      & > input:-webkit-autofill {
        background-color: initial !important;
      }
      & > button {
        background: transparent;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: var(--primary-color-1);
      }

      & > button.nation-code {
        color: var(--color-text-black);
        height: 20px;
        width: fit-content;
        padding-right: 15px;
        margin-right: 15px;
        border-right: thin solid #ddd;
        border-radius: 0;
      }

      & > button.send-code{
        width: fit-content;
        min-width: fit-content;
        white-space: nowrap;
        margin: 0 10px;
        font-weight: 700;
        font-size: 15px;
      }
    }

    & .error-message.success{
      font-style: normal;
      color: green;
    }
    @keyframes high-light {
      50% {
        opacity: 0.0;
      }
    }
    & .error-message.hight-light:not(.success){
      transform-origin: 0 0;
      animation: high-light .5s ease-in-out infinite;
    }
  }

  & .step-2 {
    text-align: center;
    font-size: 1.3em;

    & > .step-note {
      font-size: 1.1rem;

      & > button {
        color: var(--primary-color-1);
        font-weight: 600;
        cursor: pointer;
      }
    }

    & > .use-other-protocol {
      color: var(--primary-color-1);
      font-weight: 600;
      font-size: 17px;
      cursor: pointer;
    }
  }
}
