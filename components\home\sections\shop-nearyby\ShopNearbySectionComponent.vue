<template>
	<div class="content-list shop-nearby-list">
		<div class="stack-content-title">
			<span class="section-title">
				<Icon name="mdi:store-marker-outline"></Icon>
				{{ $t('HomeV2Component.cua_hang_gan_ban') }}
			</span>
			<nuxt-link :to="{
				path: appRoute.AroundComponent,
				query: {
					filter: JSON.stringify({
						...props.filter_data,
						sortBy: filter_sort.gan_nhat
					})
				}
			}" class="view-all">
				{{ $t('HomeV2Component.xem_tat_ca') }}
				<Icon name="ic:round-chevron-right"></Icon>
			</nuxt-link>
		</div>
		<div class="stack-content-list">

			<!-- <Swiper v-if="listShopNearBy?.length && !loadingShopNearby"
                                class="my-carousel stack-carousel"
                                :modules="[SwiperAutoplay, SwiperNavigation, SwiperFreeMode]" :slides-per-view="'auto'"
                                :loop="false" :effect="'creative'" :navigation="true" :freeMode=false :autoplay="false"
                                @init="(e) => {
                                    shop_around_el = e
                                }" :space-between="10" key="shop-around-carousel" ref="shop_around_el">
                                <SwiperSlide class="item-stack-slide" v-for="item of listShopNearBy"
                                    :key="'shop_nearby_' + item.id" :id="'shop_nearby_' + item.id">
                                    <nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
                                        :title="showTranslateProductName(item)">
                                        <AvatarComponent :imgTitle="showTranslateProductName(item)"
                                            :imgStyle="item.logo?.style" class="shop-logo-stack" :imgSrc="item.logo
                                                ? (domainImage + item.logo.path)
                                                : item.banner
                                                    ? (domainImage + item.banner.path)
                                                    : ''
                                                " :width="getShopItemWidth('shop_nearby_' + item.id)"
                                            :height="getShopItemWidth('shop_nearby_' + item.id)" v-on:img_click="() => {
                                                router.push(appRoute.DetailShopComponent + '/' + item.slug)
                                            }" />
                                        <span class="distance" v-if="item.distance">
                                            {{ parseFloat(item.distance) > 1000 ? (parseFloat(item.distance) /
                                                1000).toFixed(1) :
                                                (parseFloat(item.distance)).toFixed(0) }}
                                            <em>{{ parseFloat(item.distance) > 1000 ? 'km' : 'm' }}</em> </span>
                                        <div class="item-stack-content">
                                            <span class="shop-name">{{ showTranslateProductName(item) }}</span>
                                            <span class="shop-address">{{ item.address }}</span>
                                        </div>
                                    </nuxt-link>
                                </SwiperSlide>
                            </Swiper> -->
			<div class="stack-content-list-container" v-if="listShopNearBy?.length && !loadingShopNearby">
				<div class="item-stack-slide" v-for="item of listShopNearBy" :key="'shop_nearby_' + item.id">
					<nuxt-link :to="appRoute.DetailShopComponent + '/' + item.slug" class="item-stack"
						:title="showTranslateProductName(item)" :id="'shop_nearby_' + item.id">
						<AvatarComponent :imgTitle="showTranslateProductName(item)" :imgStyle="item.logo?.style"
							class="shop-logo-stack" :imgSrc="item.logo
								? (domainImage + item.logo.path)
								: item.banner
									? (domainImage + item.banner.path)
									: ''
								" :width="getShopItemWidth('shop_nearby_' + item.id)" :height="getShopItemWidth('shop_nearby_' + item.id)"
							v-on:img_click="() => {
								router.push(appRoute.DetailShopComponent + '/' + item.slug)
							}" />
						<span class="distance" v-if="item.distance">
							{{ parseFloat(item.distance) > 1000 ? (parseFloat(item.distance) /
								1000).toFixed(1) :
								(parseFloat(item.distance)).toFixed(0) }}
							<em>{{ parseFloat(item.distance) > 1000 ? 'km' : 'm' }}</em> </span>
						<div class="item-stack-content">
							<span class="shop-name">{{ showTranslateProductName(item) }}</span>
							<span class="shop-address">{{ item.address }}</span>
						</div>
					</nuxt-link>
				</div>
			</div>
			<div v-else class="none-content-list">
				<!-- {{ loadingShopNearby ? '' : '' }} -->
			</div>
			<v-overlay v-model="loadingShopNearby" :z-index="100" :absolute="false" contained
				content-class='spinner-container' persistent scrim="#fff" key="loading_best_around" no-click-animation>
				<Icon name="eos-icons:loading"></Icon>
			</v-overlay>
		</div>
	</div>
</template>

<style lang="scss" src="./ShopNearbySectionStyles.scss" scoped></style>

<script setup lang="ts">

import { useSSRContext } from "vue";
import { appRoute } from "~/assets/appRoute";
import { inject } from 'vue';
import { subscribe, publish } from "~/services/customEvents";
import { AuthService } from "~/services/authService/authService";
import { PlaceService } from "~/services/placeService/placeService";
import { ProductService } from "~/services/productService/productService";
import { ShopService } from "~/services/shopService/shopService";
import { UserService } from "~/services/userService/userService";
import Vue3Toastify, { toast } from 'vue3-toastify';
import type { query } from "firebase/firestore";
import home_icon from "~/assets/image_13_3_2024/icon_square_favicon_transparent.png";
import wave from '~/assets/image_13_3_2024/wave.png'
import { ChatService } from "~/services/chatService/chatService";
import { HttpStatusCode } from "axios";
import hot_sale from "~/assets/imageV2/hot-sale.svg";
import icon_for_product from "~/assets/image/icon-for-product.png";

import {
	appConst,
	appDataStartup,
	baseLogoUrl,
	domainImage,
	formatCurrency,
	formatNumber,
	showTranslateProductDescription,
	showTranslateProductName
} from "~/assets/AppConst";
import { filter_sort, type CartDto } from "~/assets/appDTO";
import { PublicService } from "~/services/publicService/publicService";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const localePath = useLocalePath();

const props = defineProps({
	filter_data: null,
	enable_load: null
})

var publicService = new PublicService();

var listShopNearBy = useState<any>('dashboard_shop_nearby', () => { return [] });
var loadingShopNearby = ref(false);

watch(() => [props.enable_load], () => {
	if (props.enable_load) {
		initListShopNearby()
	}
})
onBeforeMount(() => {

})
onUnmounted(async () => {
});
onMounted(async () => {
	// initListShopNearby();
});

function initListShopNearby() {
	console.log('init dashboard shop near', listShopNearBy.value?.length);
	if (props.filter_data.latitude_user != null && props.filter_data.longitude_user != null) {
		let body = {
			section: "sale_off", //suggest,sale_off,best_around,hot_deal
			latitude_user: props.filter_data.latitude_user,
			longitude_user: props.filter_data.longitude_user
		}
		if (!listShopNearBy.value?.length) {
			loadingShopNearby.value = true;
			publicService.dashboard({ ...body, section: 'best_around' }).then(res => {
				if (res.status == HttpStatusCode.Ok) {
					listShopNearBy.value = res.body.data?.result ? JSON.parse(JSON.stringify(res.body.data?.result)) : []
				}

				loadingShopNearby.value = false;
			});
		}
	}

}

function getShopItemWidth(elId: any) {
	return document.getElementById(elId)?.getBoundingClientRect().width ?? 150;
}

</script>
