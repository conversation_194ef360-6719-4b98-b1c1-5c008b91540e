.product-item-container-grid {
  --shop-product-item-width: 48%;
  cursor: pointer;
  padding: 5px;
  border: 1px dashed #ccc;
  width: var(--shop-product-item-width);
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  -webkit-user-drag: none;
  // border: thin solid #D9D9D9;
  // background: #f5f6fa;
  // background: color-mix(in srgb, var(--temp-color-4) 10%, white);
  background: var(--temp-color-4);
  // background: #ebfeff;
  border-radius: 10px;
  // transition: all 0.5s ease-in-out;

  @media screen and (min-width: 576px) {
    // max-width: 540px;
    --shop-product-item-width: calc(49%)
  }
  @media screen and (min-width: 768px) {
    // max-width: 720px;
    --shop-product-item-width: calc(32%)
  }
  @media screen and (min-width: 992px) {
    // max-width: 960px;
    --shop-product-item-width: calc(24.4%)
  }
  @media screen and (min-width: 1200px) {
    // max-width: 1140px;
    --shop-product-item-width: calc(24.4%)
  }

  // @media screen and (max-width: 500px) {
  //   // --shop-product-item-width: calc((100vw - 7px * 5 - 5px * 2) / 2) !important;
  // }

  // @media screen and (min-width: 501px) and (max-width: 1024px) {
  //   // --shop-product-item-width: calc((720px - 7px * 5 - 5px * 2) / 3) !important;
  // }

  // @media screen and (min-width: 1025px) and (max-width: 1320px) {
  //   // --shop-product-item-width: calc((1024px - 7px * 12 - 5px * 3) / 4) !important;
  // }

  // @media screen and (min-width: 1321px) {
  //   // --shop-product-item-width: calc((1024px - 7px * 12 - 5px * 3) / 4) !important;
  // }

  & .product-item {
    width: 100%;
    // width: calc(var(--shop-product-item-width) - 20px);
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    -webkit-user-drag: none;

    &>img {
      flex: 1;
      width: 100%;
      background: var(--color-background-2);
      // height: calc(var(--shop-product-item-width) - 20px);
      // max-height: calc(var(--shop-product-item-width) - 20px);
      // min-height: calc(var(--shop-product-item-width) - 20px);
      aspect-ratio: 1;
      object-fit: cover;
      border-radius: 7px;
    }

    &>.product-item-content {
      display: flex;
      flex-direction: column;
      // height: 75px;
      // min-height: 75px;
      padding: 5px 0 0;
      text-align: left;
      // font-size: 1em;
      font-size: calc(1em + var(--font-resize));
      gap: 1px;
      flex: 1;

      &>.name {
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        color: var(--temp-color-1);
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        line-height: normal;
        -webkit-box-orient: vertical;
        text-align: center;
      }

      &>.sold-like-amount {
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        color: #a8a7a7;
        font-weight: 400;
        line-height: calc(13px + var(--font-resize));
        min-height: calc(13px + var(--font-resize));
        margin-top: 3px;
      }

      &>.h-stack.price-add-to-cart {
        // flex: 1;
        background: #ececec;
        border-radius: 10px;
        width: auto;
        width: 100%;
        margin-right: auto;
        margin-top: auto;
        padding: 5px;

      }

      & .price {
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // font-weight: bold;
        // color: var(--temp-color-2);
        // font-size: 17px;
        // display: flex;
        // flex-direction: column;
        // line-height: 1.1;
        // margin-top: auto;
        // font-weight: 800;
        // padding-top: 10px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        color: var(--temp-color-1);
        // font-size: 16px;
        font-size: calc(16px + var(--font-resize));
        display: flex;
        flex-direction: column;
        line-height: 1.1;

        &>.off {
          // color: #6c6c6c;
          color: var(--temp-color-3);
          text-decoration: line-through;
          font-style: normal;
          // font-size: 13px;
          font-size: calc(13px + var(--font-resize));
          font-weight: 400;
        }
      }

      & .add-to-cart {
        // color: var(--temp-color-2);
        color: var(--temp-color-5);
        margin-left: auto;
        margin-top: auto;
        width: 40px;
        min-width: 40px;
        height: 40px;
        padding: 5px;
        display: flex;
        // font-size: 30px;
        font-size: calc(30px + var(--font-resize));
        align-items: flex-end;
        justify-content: flex-end;
      }
    }

    &.list-mode{
      flex-direction: row;
    }
  }

  &>.top-left-tag {
    z-index: 1;
    position: absolute;
    top: 10px;
    left: 10px;
    width: 50px;
    height: 50px !important;
    min-height: unset;
    object-fit: contain;
  }

  &>.top-left-tag.small {
    width: 30px;
    height: 30px !important;
  }
}

.product-item-container-grid:hover,
.product-item-container-grid:active,
.product-item-container-grid:focus,
.product-item-container-grid:target {
  border: thin solid var(--temp-color-5);
  // background: var(--temp-color-3);
  // background: var(--temp-color-4);
  border-radius: 10px;
  background: color-mix(in srgb, var(--temp-color-5) 2.5%, white);
  box-shadow: 0 7px 10px -7px #868686;
}

.product-item-container-list {
  --shop-product-item-width: 50%;
  cursor: pointer;
  // padding: 5px;
  border: 1px dashed #ccc;
  width: var(--shop-product-item-width);
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 5px 0;
  -webkit-user-drag: none;
  // border: thin solid #D9D9D9;
  // background: #f5f6fa;
  // background: color-mix(in srgb, var(--temp-color-4) 10%, white);
  background: var(--temp-color-4);
  // background: #ebfeff;
  border-radius: 5px;
  // transition: all 0.5s ease-in-out;
  // background: var(--temp-color-4);

  @media screen and (max-width: 500px) {
    --shop-product-item-width: 100% !important;
  }

  @media screen and (min-width: 501px) and (max-width: 1024px) {
    --shop-product-item-width: 100% !important;
  }

  @media screen and (min-width: 1025px){
    --shop-product-item-width: calc(50% - 20px) !important;
  }

  & .product-item {
    width: 100%;
    // width: calc(var(--shop-product-item-width) - 20px);
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    position: relative;
    -webkit-user-drag: none;

    &>img {
      width: 120px;
      height: 120px;
      aspect-ratio: 1;
      background: var(--color-background-2);
      // height: calc(var(--shop-product-item-width) - 20px);
      // max-height: calc(var(--shop-product-item-width) - 20px);
      // min-height: calc(var(--shop-product-item-width) - 20px);
      // aspect-ratio: 1;
      object-fit: cover;
      border-radius: 5px 0 0 5px;
    }

    &>.product-item-content {
      display: flex;
      flex-direction: column;
      // height: 75px;
      // min-height: 75px;
      padding: 5px 5px 5px 10px;
      text-align: left;
      // font-size: 1em;
      font-size: calc(1em + var(--font-resize));
      gap: 1px;
      flex: 1;

      &>.name {
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        color: var(--temp-color-1);
        // font-size: 15px;
        font-size: calc(15px + var(--font-resize));
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        line-height: normal;
        -webkit-box-orient: vertical;
        text-align: left;
      }

      &>.sold-like-amount {
        // font-size: 13px;
        font-size: calc(13px + var(--font-resize));
        color: #a8a7a7;
        font-weight: 400;
        line-height: 15px;
        min-height: 15px;
        margin-top: 3px;
      }

      &>.h-stack.price-add-to-cart {
        // flex: 1;
        background: #ececec;
        border-radius: 10px;
        width: auto;
        width: 100%;
        margin-right: auto;
        margin-top: auto;
        padding: 5px;

      }

      & .price {
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // font-weight: bold;
        // color: var(--temp-color-2);
        // font-size: 17px;
        // display: flex;
        // flex-direction: column;
        // line-height: 1.1;
        // margin-top: auto;
        // font-weight: 800;
        // padding-top: 10px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        // color: var(--temp-color-2);
        color: var(--temp-color-1);
        // font-size: 16px;
        font-size: calc(16px + var(--font-resize));
        display: flex;
        flex-direction: column;
        line-height: 1.1;

        &>.off {
          color: var(--temp-color-3);
          text-decoration: line-through;
          font-style: normal;
          // font-size: 13px;
          font-size: calc(13px + var(--font-resize));
          font-weight: 400;
        }
      }

      & .add-to-cart {
        // color: var(--temp-color-2);
        color: var(--temp-color-5);
        margin-left: auto;
        margin-top: auto;
        width: 40px;
        min-width: 40px;
        height: 40px;
        padding: 5px;
        display: flex;
        // font-size: 30px;
        font-size: calc(30px + var(--font-resize));
        align-items: flex-end;
        justify-content: flex-end;
      }
    }

    &.list-mode{
      flex-direction: row;
    }
  }

  &>.top-left-tag {
    z-index: 1;
    position: absolute;
    top: 10px;
    left: 10px;
    width: 50px;
    height: 50px !important;
    min-height: unset;
    object-fit: contain;
  }

  &>.top-left-tag.small {
    width: 30px;
    height: 30px !important;
  }
}

.product-item-container-list:hover,
.product-item-container-list:active,
.product-item-container-list:focus,
.product-item-container-list:target {
  border: thin solid var(--temp-color-5);
  border-radius: 10px;
  background: color-mix(in srgb, var(--temp-color-5) 2.5%, white);
  // background: var(--temp-color-3);
  // background: var(--temp-color-4);
  box-shadow: 0 7px 10px -7px #868686;
}
