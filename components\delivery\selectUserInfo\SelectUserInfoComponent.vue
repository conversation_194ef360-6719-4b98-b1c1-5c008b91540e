<template>
	<v-overlay v-model="showSelectUserInfoModal" location="bottom" contained :z-index="1001" key="show_product_select"
		class="select-user-info-overlay-container" persistent content-class='select-user-info-container'
		no-click-animation>
		<HeaderComponent :title="$props.title ? $props.title : $t('AppRouteTitle.SelectUserInfoComponent')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close" v-on:click="() => {
					close()
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="select-user-info-content-container">
			<button class="select-user-saved" v-if="userInfo" v-on:click="() => {
				showSelectSavedUser = true;
			}">
				{{ $t('SelectUserInfoComponent.chon_tu_so_dia_chi') }}
				<!-- <Icon name="material-symbols:person-pin-circle-outline"></Icon> -->
			</button>
			<div class='v-stack'>
				<span class='label required'>
					{{ $t('SelectUserInfoComponent.ten') }}
				</span>
				<input :title="$t('SelectUserInfoComponent.ten')" name='customer-name' maxLength=255 autoComplete="off"
					class='input-order' :placeholder="$t('SelectUserInfoComponent.ten_placeholder')"
					:value="name || null" v-on:input="($event: any) => {
						name = $event.target.value;
						nameValidation()
					}" v-on:blur="() => {
						nameValidation()
					}" />
				<span class='error-message'>{{ nameErr }}</span>
			</div>
			<div class='v-stack'>
				<span class='label required'>
					{{ $t('SelectUserInfoComponent.so_dien_thoai') }}
				</span>
				<input :title="$t('SelectUserInfoComponent.so_dien_thoai')" name='customer-phone' maxLength=255
					autoComplete="off" class='input-order' type="phone"
					:placeholder="$t('SelectUserInfoComponent.so_dien_thoai_placeholder')" :value="phone || null"
					v-on:input="($event: any) => {
						phone = validPhone($event.target.value);
						phoneValidation();
					}" v-on:blur="() => {
						phoneValidation();
					}" />
				<span class='error-message'>{{ phoneErr }}</span>
			</div>
			<div class='v-stack'>
				<span class='label required'>
					{{ $t('SelectUserInfoComponent.dia_chi') }}
				</span>
				<AddressSearchingInputComponent :address="address" v-on:input="(event: any) => {
					address = event;
					addressValidation()
				}" v-on:select="(obj: any) => {
					selectAddressSuggest(obj)
					addressValidation()
				}" />
				<span class='error-message'>{{ addressErr }}</span>
			</div>
			<div class="map-container">
				<client-only>
					<LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
						leafletMap = e;
						leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
						// leafletMap.setView([appConst.defaultCoordinate.latitude, appConst.defaultCoordinate.longitude], 15)
						initLeafletMap();
					}" :max-zoom="appConst.leafletMapTileOption.maxZoom" v-on:update:center="async (bounds: any) => {
						console.log('center change');
						latitude = leafletMap.getCenter().lat;
						longitude = leafletMap.getCenter().lng;
					}" :onzoomend="() => {
						latitude = leafletMap.getCenter().lat;
						longitude = leafletMap.getCenter().lng;
					}" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true" :use-global-leaflet="true">
						<LControlZoom position="bottomright"></LControlZoom>
						<span class="current-location-leaflet" :title="$t('SelectUserInfoComponent.vi_tri_cua_ban')"
							v-on:click="() => {
								gotoCurrentLocationLeaflet();
							}
								">
							<Icon name="line-md:my-location-loop" class="my-location-icon" />
						</span>
						<div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
							data-placement="right" :title="$t('SelectUserInfoComponent.nhan_de_chuyen_loai_map')"
							v-bind:style="{
								backgroundImage: `url(` + buttonMapTileBackgound + `)`,
							}" v-on:click="(event: any) => {
								if (event.isTrusted) {
									if (
										leafletMapTileUrl ==
										appConst.leafletMapTileUrl.roadmap
									) {
										leafletMapTileUrl =
											appConst.leafletMapTileUrl.hyprid;
										mapTypeTitle = $t('SelectUserInfoComponent.ve_tinh');
										mapType = 'hyprid';
										buttonMapTileBackgound = map_sateline;
									} else if (
										leafletMapTileUrl ==
										appConst.leafletMapTileUrl.hyprid
									) {
										leafletMapTileUrl =
											appConst.leafletMapTileUrl.streetmap;
										mapTypeTitle = $t('SelectUserInfoComponent.co_dien');
										mapType = 'hyprid';
										buttonMapTileBackgound = map_streetmap;
									} else if (
										leafletMapTileUrl ==
										appConst.leafletMapTileUrl.streetmap
									) {
										leafletMapTileUrl =
											appConst.leafletMapTileUrl.roadmap;
										mapTypeTitle = $t('SelectUserInfoComponent.ve_tinh_nhan');
										mapType = 'roadmap';
										buttonMapTileBackgound = map_sateline;
									}
								} else event.preventDefault();
							}
								">
							<span>{{ mapTypeTitle }}</span>
						</div>
						<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
							:max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
							name="GoogleMap">
						</LTileLayer>
						<div class="marker-location">
							<img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon"
								alt="" />
						</div>
					</LMap>
				</client-only>
			</div>
		</div>
		<div class="footer-actions">
			<button class="submit-button" v-on:click="() => {
				submit();
			}">
				{{ $t('SelectUserInfoComponent.luu') }}
			</button>
		</div>
		<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
			content-class="my-modal-content-container saved-address-modal-container" :click-to-close="false"
			v-model="showSelectSavedUser" v-on:closed="() => {
				showSelectSavedUser = false
			}" contentTransition="vfm-slide-up">
			<SavedAddressComponent :mode="'select'" :selectedObject="{
				name: name,
				phone: validPhone(phone),
				address: address,
				latitude: latitude,
				longitude: longitude,
			}" v-on:close="() => {
				showSelectSavedUser = false;
			}" v-on:enterTempAddres="() => {
				showSelectSavedUser = false;
			}" v-on:selectedUser="(selectedObj: any) => {
				showSelectSavedUser = false;
				setDataOnSelected(selectedObj);
			}"></SavedAddressComponent>
		</VueFinalModal>
	</v-overlay>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, validPhone } from '~/assets/AppConst';

import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import no_image from "~/assets/image/no-image.webp";
import { UserAddressService } from '~/services/userAddressService/userAddressService';
import { VueFinalModal } from 'vue-final-modal';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
	title: null,
	initialUserInfo: null
});
const nuxtApp = useNuxtApp();

var userAddressService = new UserAddressService();

var userInfo = ref(null as any);

var leafletMap: L.Map;
var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('SelectUserInfoComponent.ve_tinh_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var buttonMapTileBackgound = ref(map_sateline);

var name = ref("");
var nameErr = ref("");
var phone = ref("" as any);
var phoneErr = ref("");
var address = ref("");
var addressErr = ref("");
var latitude = ref(null as any);
var longitude = ref(null as any);
var province_id = ref(null as any);
var district_id = ref(null as any);
var ward_id = ref(null as any);

var dataSavedUsers = ref([] as any);
var showSelectUserInfoModal = ref(false);
var showSelectSavedUser = ref(false);
var userInfoSelected = ref({
	name: "",
	phone: "",
	address: "",
	latitude: null,
	longitude: null,
	province_id: null,
	district_id: null,
	ward_id: null
} as any)

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

// watch(() => [user_latitude.value, user_longitude?.value], () => {
// 	latitude.value = user_latitude?.value;
// 	longitude.value = user_longitude?.value;
// });

onBeforeMount(() => {
	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;
	});
})

onUnmounted(() => {
	nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})

onMounted(async () => {
	let userInfoCurrent = await localStorage.getItem(appConst.storageKey.userInfo);
	userInfo.value = JSON.parse(userInfoCurrent as string);
	if (userInfo.value) {
		setDataOnSelected(userInfo.value);
	}
	await getMyListAddress();
	name.value = props.initialUserInfo?.name ? props.initialUserInfo.name : "";
	phone.value = props.initialUserInfo?.phone ? validPhone(props.initialUserInfo.phone) : "";
	address.value = props.initialUserInfo?.address ? props.initialUserInfo.address : "";
	latitude.value = props.initialUserInfo?.latitude ? props.initialUserInfo.latitude : "";
	longitude.value = props.initialUserInfo?.longitude ? props.initialUserInfo.longitude : "";

	showSelectUserInfoModal.value = true;
})

function setDataOnSelected(selectedObj: any) {
	name.value = selectedObj ? selectedObj.name : null;
	phone.value = selectedObj ? validPhone(selectedObj.phone) : null;
	address.value = selectedObj ? selectedObj.address : null;
	latitude.value = selectedObj ? selectedObj.latitude : null;
	longitude.value = selectedObj ? selectedObj.longitude : null;
	province_id.value = selectedObj ? selectedObj.province_id : null;
	district_id.value = selectedObj ? selectedObj.district_id : null;
	ward_id.value = selectedObj ? selectedObj.ward_id : null;
	if (leafletMap) {
		leafletMap.flyTo(
			[latitude.value, longitude.value],
			15,
			{
				duration: .5
			}
		);
	}

}
async function getMyListAddress() {
	let res = await userAddressService.myListAddress();
	if (res.status == 200) {
		dataSavedUsers.value = res.body.data;
		let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
			return e.is_default == true;
		})

		if (indexDefault != -1) {
			setDataOnSelected(dataSavedUsers.value[indexDefault]);
		}
		else {
			setDataOnSelected(dataSavedUsers.value[0]);

		}
	}
	else {
		dataSavedUsers.value = [];
	}
}
async function initLeafletMap() {
	// markersCluster = new nuxtApp.$L.MarkerClusterGroup({
	// 	maxClusterRadius: 5,
	// 	iconCreateFunction: (cluster) => createClusterElement(cluster),
	// }).addTo(leafletMap);
	await setCurrentLocationLeaflet();
	// (leafletMap as any)["gestureHandling"].enable();
}

function setCurrentLocationLeaflet() {
	if (latitude.value && longitude.value) {
		leafletMap.setView([latitude.value, longitude.value], 15);
		// setLocationLeafletMarker(latitude.value, longitude.value);
	} else {
		latitude.value = user_latitude.value;
		longitude.value = user_longitude.value;
		leafletMap.setView([latitude.value ?? appConst.defaultCoordinate.latitude, longitude.value ?? appConst.defaultCoordinate.longitude], 15);
	}
	// if ("geolocation" in navigator) {
	// 	navigator.geolocation.getCurrentPosition(
	// 		async (position) => {
	// 			latitude.value = position.coords.latitude;
	// 			longitude.value = position.coords.longitude;
	// 			leafletMap.setView([position.coords.latitude, position.coords.longitude], 15);
	// 			// setLocationLeafletMarker(latitude.value, longitude.value);
	// 		},
	// 		async (error) => {
	// 			latitude.value = appConst.defaultCoordinate.latitude;
	// 			longitude.value = appConst.defaultCoordinate.longitude;
	// 			leafletMap.setView([latitude.value, longitude.value], 15);
	// 			// setLocationLeafletMarker(latitude.value, longitude.value);
	// 		},
	// 		{
	// 			enableHighAccuracy: false, // Use less accurate but faster methods
	// 			timeout: 5000, // Set a timeout (in milliseconds)

	// 		}
	// 	);
	// }
}
async function gotoCurrentLocationLeaflet(event?: Event) {
	if (!event || event.isTrusted == true) {
		// if ("geolocation" in navigator) {
		// 	navigator.geolocation.getCurrentPosition(
		// 		(position) => {
		// 			leafletMap.flyTo(
		// 				[position.coords.latitude, position.coords.longitude],
		// 				17
		// 			);
		// 			latitude.value = position.coords.latitude;
		// 			longitude.value = position.coords.longitude;
		// 		},
		// 		(error) => {
		// 			toast.warning(t('SelectUserInfoComponent.chua_cung_cap_vi_tri'), {
		// 				autoClose: 1000,
		// 				hideProgressBar: true,
		// 			});
		// 			latitude.value = appConst.defaultCoordinate.latitude;
		// 			longitude.value = appConst.defaultCoordinate.longitude;
		// 			leafletMap.flyTo([latitude.value, longitude.value], 17);
		// 		},
		// 		{
		// 			enableHighAccuracy: false, // Use less accurate but faster methods
		// 			timeout: 5000, // Set a timeout (in milliseconds)

		// 		}
		// 	);
		// }
		leafletMap.flyTo(
			[user_latitude.value, user_longitude.value],
			17
		);
		latitude.value = user_latitude.value;
		longitude.value = user_longitude.value;
	}
}
function nameValidation() {
	if (!name.value || !name.value.length) {
		nameErr.value = t('SelectUserInfoComponent.vui_long_nhap_ten_nguoi_nhan')
	}
	else {
		nameErr.value = '';
	}
}

function phoneValidation() {
	let re = appConst.validateValue.phone;
	if (!phone.value || !validPhone(phone.value).length) {
		phoneErr.value = t('SelectUserInfoComponent.vui_long_nhap_sdt')
		return;
	}
	if (!re.test(validPhone(phone.value))) {
		phoneErr.value = t('SelectUserInfoComponent.sdt_khong_dung');
		return;
	}
	else {
		phoneErr.value = '';
	}
}

function addressValidation() {
	if (address.value?.length) {
		addressErr.value = "";
	}
	else addressErr.value = t('SelectUserInfoComponent.vui_long_nhap_dia_chi');
}
function flyToLocation(lat: any, lng: any) {
	if (leafletMap) leafletMap.flyTo([lat, lng], 17, {
		duration: 1
	});
}
function selectAddressSuggest(addressSelectSuggest: any) {

	address.value = addressSelectSuggest.address;
	latitude.value = addressSelectSuggest.latitude;
	longitude.value = addressSelectSuggest.longitude;
	flyToLocation(addressSelectSuggest.latitude, addressSelectSuggest.longitude);
}

function close(value?: any) {
	showSelectUserInfoModal.value = false;
	emit('close', value);
}
function submit() {
	showSelectUserInfoModal.value = false;
	userInfoSelected.value = {
		name: name.value,
		phone: validPhone(phone.value),
		address: address.value,
		latitude: latitude.value,
		longitude: longitude.value,
		province_id: province_id.value,
		district_id: district_id.value,
		ward_id: ward_id.value,
	}
	emit('submit', userInfoSelected.value)
}
</script>

<style lang="scss" src="./SelectUserInfoStyles.scss"></style>