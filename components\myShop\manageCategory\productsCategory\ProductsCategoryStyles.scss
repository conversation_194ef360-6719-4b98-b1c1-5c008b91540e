.products-category-modal {
  padding: 0 !important;
  min-height: 90dvh !important;
  max-height: 90dvh !important;
  min-height: 88vh;
  max-height: 88vh;
}

.products-category-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: hidden;
  padding-bottom: 5px;

  // & > .title-header {
  //   // font-size: 1.3em;

  //   margin: 0;
  //   text-align: center;
  //   border-bottom: thin solid #ccc;
  // }

  & > .products-category-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-top: 5px;
    // overflow: auto;
    padding: 0;

    & .search-bar-container {
      align-items: center;
      position: relative;
      height: 40px;
      min-height: 40px;
      z-index: 99;
      margin-bottom: 5px;
      padding: 0 10px;
    }

    & .search-input-container {
      width: 100%;
      background-color: #f5f6fa;
      border-radius: 0 !important;
      border-color: transparent;
      outline: none;
      height: 100%;
      font-size: var(--font-size);
    }

    & .search-button {
      display: flex;
      align-items: center;
      background: #f5f6fa;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 10px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & > .update-list-button {
      color: white;
      background: var(--primary-color-1);
      padding: 5px;
      border-radius: 5px;
    }

    & > .empty-products {
      flex: 1;
      justify-content: center;
      align-items: center;
      display: flex;
      flex-direction: column;
      font-size: 1.2em;

      & > img {
        width: 250px;
        height: 250px;
        object-fit: contain;
      }
    }

    & > .list-products {
      display: flex;
      flex-direction: column;
      // overflow: auto;
      flex: 1;
      margin-top: 5px;

      & > .item-product {
        display: flex;
        gap: 10px;
        padding: 7px;
        background: #f5f6fa;
        font-size: 17px;
        cursor: pointer;

        & > img {
          height: 100px;
          width: 100px;
          border-radius: 10px;
          object-fit: cover;
        }

        & > .item-product-content {
          display: flex;
          flex-direction: column;
          gap: 5px;
          justify-content: flex-start;
          align-items: flex-start;

          & > .name {
            color: var(--primary-color-1);
            font-weight: bold;
          }

          & > .price {
            color: var(--color-text-note);
          }
        }
      }

      & > .item-product.odd {
        background: white;
      }
    }

    & > .list-products-editing {
      display: flex;
      position: relative;
      flex-direction: column;
      // overflow: auto;
      flex: 1;

      & > .list-selected {
        display: flex;
        flex-direction: column;
        overflow: auto;
        flex: 1;

        & > .selected-empty {
          flex: 1;
          justify-content: center;
          align-items: center;
          display: flex;
          flex-direction: column;
          font-size: 1.2em;

          & > img {
            width: 250px;
            height: 250px;
            object-fit: contain;
          }
        }
        & > .selected-list {
          display: flex;
          flex-direction: column;
          overflow: auto;
          flex: 1;
          & > .item-product {
            display: flex;
            gap: 10px;
            padding: 7px;
            background: var(--color-background-2);
            font-size: 17px;

            & > img {
              height: 100px;
              width: 100px;
              border-radius: 10px;
              object-fit: cover;
            }

            & > .item-product-content {
              display: flex;
              flex-direction: column;
              flex: 1;
              gap: 5px;
              justify-content: flex-start;
              align-items: flex-start;

              & > .name {
                color: var(--primary-color-1);
                font-weight: bold;
              }

              & > .price {
                color: var(--color-text-note);
              }
            }

            & > .unselected-button {
              width: 30px;
              height: 30px;
              align-self: center;

              & > img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }
          }

          & > .item-product.odd {
            background: white;
          }
        }

      }

      & > .list-suggest {
        display: flex;
        flex-direction: column;
        overflow: auto;
        width: 100%;
        height: 100%;
        background: white;
        position: absolute;
        top: 0;
        left: 0;
        & > .suggest-empty {
          flex: 1;
          justify-content: center;
          align-items: center;
          display: flex;
          flex-direction: column;
          font-size: 1.2em;

          & > img {
            width: 250px;
            height: 250px;
            object-fit: contain;
          }
        }
        & > .suggest-list {
          display: flex;
          flex-direction: column;
          overflow: auto;
          flex: 1;
          & > .item-product {
            display: flex;
            gap: 10px;
            padding: 7px;
            background: #f9f9f9;
            font-size: 17px;
            cursor: pointer;

            & > img {
              height: 100px;
              width: 100px;
              border-radius: 10px;
              object-fit: cover;
            }

            & > .item-product-content {
              display: flex;
              flex-direction: column;
              flex: 1;
              gap: 5px;
              justify-content: flex-start;
              align-items: flex-start;

              & > .name {
                color: var(--primary-color-1);
                font-weight: bold;
              }

              & > .price {
                color: var(--color-text-note);
              }
            }

            & > .select-button {
              font-size: 1.5em;
              width: 30px;
              height: 30px;
              display: flex;
              align-self: center;
              color: var(--primary-color-1);
            }
          }

          & > .item-product.odd {
            background: white;
          }

          & .showed-all {
            color: #626262;
            font-style: italic;
            width: 100%;
            text-align: center;
            padding: 20px 0;
          }
          & > .loading-more {
            background: white;
            color: var(--primary-color-1);
            width: 100%;
            text-align: center;
            position: sticky;
            bottom: 0;
            left: 0;
          }
        }
      }
    }

    & .footer {
      margin-top: auto;
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      padding: 10px;
      gap: 10px;
      font-size: 15px;
      position: sticky;
      bottom: 0;
      background: white;

      & > span {
        margin-right: auto;
      }

      & > button {
        border-radius: 7px;
        border: 1px solid var(--primary-color-1);
        color: var(--primary-color-1);
        padding: 7px 10px;
        font-weight: 600;
        font-size: 15px;
        flex: 1;
      }

      & > button.save {
        background: var(--primary-color-1);
        color: white;
        border-color: var(--primary-color-1);
      }

      & > button.go-back{
        color: #515151;
        border: thin solid #515151;
      }
    }

    & .footer.add-product-to-list{
      & > button {
        width: 100px;
        flex: none;
      }
    }
    & > .delete-notice{
      font-size: 15px;
      text-align: center;
      margin-bottom: 5px;
      color: #919191;
    }
  }
}

.category-content-container{
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}