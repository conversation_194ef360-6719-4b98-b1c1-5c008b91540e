{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"start": "nuxt start", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 nuxt build", "dev": "npx opener http://127.0.0.1:3000 && nuxt dev", "dev-https": "nuxi dev --host --https --port 3000", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test:report": "npx cypress run --spec \"cypress/e2e/custom-tests-v1/\" --reporter mochawesome --reporter-options \"reportDir=cypress/results,reportFilename=result\"", "test": "npx cypress run  --spec \"cypress/e2e/custom-tests-v1/\""}, "devDependencies": {"@iconify-json/bi": "^1.2.1", "@iconify-json/bx": "^1.2.1", "@iconify-json/ci": "^1.2.1", "@iconify-json/eos-icons": "^1.2.1", "@iconify-json/gis": "^1.2.1", "@iconify-json/humbleicons": "^1.2.1", "@iconify-json/ic": "^1.2.1", "@iconify-json/ion": "^1.2.1", "@iconify-json/mage": "^1.2.1", "@iconify-json/material-symbols": "^1.2.3", "@iconify-json/pajamas": "^1.2.3", "@nuxt/image": "^1.7.0", "@nuxtjs/device": "^3.1.1", "@nuxtjs/i18n": "^8.3.1", "@stefanobartoletti/nuxt-social-share": "^1.2.0", "@types/crypto-js": "^4.2.2", "@types/leaflet": "^1.9.8", "@types/leaflet-routing-machine": "^3.2.8", "@vueuse/core": "^11.2.0", "@vueuse/nuxt": "^11.2.0", "cross-env": "^7.0.3", "cypress": "^13.6.3", "mochawesome": "^7.1.3", "nuxt": "^3.9.0", "nuxt-icon": "^1.0.0-beta.7", "nuxt3-leaflet": "^1.0.12", "sass": "^1.69.7", "vite-plugin-vuetify": "^2.0.1", "vue": "^3.4.0", "vue-router": "^4.2.5", "vuetify": "^3.4.10"}, "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.2", "@fancyapps/ui": "^5.0.36", "@fingerprintjs/fingerprintjs": "^4.6.1", "@fortawesome/fontawesome-free": "^6.5.1", "@kimyvgy/nuxt-page-cache": "^1.2.8", "@mdi/font": "^7.4.47", "@nuxt/ui": "^2.14.2", "@types/leaflet-rotatedmarker": "^0.2.5", "@types/leaflet.markercluster": "^1.5.4", "@vee-validate/nuxt": "^4.13.2", "@vite-pwa/nuxt": "^0.10.5", "@vuepic/vue-datepicker": "^9.0.3", "@vueuse/components": "^13.0.0", "@vueuse/sound": "^2.0.1", "axios": "^1.6.3", "axios-etag-cache": "^1.4.0", "crypto-js": "^4.2.0", "exif-js": "^2.3.0", "exifr": "^7.1.3", "firebase": "^10.3.1", "google-auth-library": "^9.6.1", "hls.js": "^1.5.17", "iso-639-1": "^3.1.2", "leaflet": "^1.9.4", "leaflet-gesture-handling": "^1.2.2", "leaflet-rotatedmarker": "^0.2.0", "leaflet-routing-machine": "^3.2.12", "leaflet.markercluster": "^1.5.3", "lrm-graphhopper": "^1.3.0", "maska": "^3.1.1", "mitt": "^3.0.1", "moment": "^2.30.1", "mqtt": "^5.10.0", "nuxt-emoji-picker": "^1.1.0", "nuxt-lodash": "^2.5.3", "nuxt-multi-cache": "^3.1.1", "nuxt-rating": "^0.1.5", "nuxt-swiper": "^1.2.2", "nuxt-vue3-google-signin": "^0.0.10", "panzoom": "^9.4.3", "password-generator": "^2.3.2", "vue-advanced-cropper": "^2.8.9", "vue-currency-input": "^3.1.0", "vue-draggable-next": "^2.2.1", "vue-final-modal": "^4.5.3", "vue3-toastify": "^0.2.1", "yup": "^1.4.0"}}