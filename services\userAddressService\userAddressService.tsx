import axios from "axios";

import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "~/assets/AppConst";


export class UserAddressService extends BaseHTTPService {
    myListAddress(){
        let url = appConst.apiURL.myUserAddressList;
        return this.https('GET', url);
    }
    setDefault(id:string){
        let body = {
            id: id
        }
        let url = appConst.apiURL.myUserAddressSetDefault;
        return this.https('POST', url, body);
    }
    create(body:any){
        let url = appConst.apiURL.myUserAddressCreate;
        return this.https('POST', url, body, null, true);
    }
    update(body:any){
        let url = appConst.apiURL.myUserAddressUpdate;
        return this.https('POST', url, body, null, true);
    }
    delete(id:any){
        let body = {
            id: id
        }
        let url = appConst.apiURL.myUserAddressDelete;
        return this.https('POST', url, body, null, true);
    }
}