<template>
	<VueFinalModal class="my-modal-container" content-class="v-stack require-login-popup-container" overlay-class="require-login-popup-overlay" :overlay-behavior="'persist'"
		:click-to-close="false" v-model="showRequireLoginModal" contentTransition="vfm-fade">
		<div class="require-login-content-container">
			<img loading='lazy' :src="login_first" alt="">
			<span>{{ $t('RequireLoginComponent.ban_can_dang_nhap') }}</span>
			<div class="footer-actions">
				<button class="close-button" v-on:click="close()">
					{{ $t('RequireLoginComponent.de_sau') }}
				</button>
				<button class="goto-login-button" v-on:click="() => {
					goToLogin()
				}">
					{{ $t('RequireLoginComponent.dang_nhap') }}
				</button>
			</div>
		</div>
	</VueFinalModal>
</template>

<script setup lang="ts">
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
import login_first from '~/assets/imageV2/login-first.png'
const props = defineProps({
	redirect_to: null,
});
const emit = defineEmits(['close', 'goLogin']);
const video = ref<HTMLVideoElement | null>(null);
const { t } = useI18n()
const nuxtApp = useNuxtApp();
const router = useRouter();

var showRequireLoginModal = ref(false);
var webInApp = ref(null as any);


onMounted(async () => {
	window.addEventListener('popstate', handleBackButton);
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	showRequireLoginModal.value = true;
});

onUnmounted(()=>{
	window.removeEventListener('popstate', handleBackButton);
})
const handleBackButton = () => {
  console.log('Back button clicked');
  close(true)
  // Thực hiện hành động tùy chỉnh ở đây
};
function goToLogin() {
	// Đi tới trang login và kèm theo redirect_link, khi đăng nhập hoàn tất thì chạy về redirect_link
	router.push({
		path: appRoute.LoginComponent,
		query: {
			redirect: JSON.stringify(props.redirect_to)
		}
	})
	emit('goLogin');
	showRequireLoginModal.value = false
}
function close(backClick = false) {
	showRequireLoginModal.value = false;
	emit('close', backClick)
}
</script>

<style lang="scss" src="./RequireLoginStyles.scss"></style>