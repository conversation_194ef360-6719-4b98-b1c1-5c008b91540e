<script setup lang="ts">
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'

import { useI18n } from 'vue-i18n';
import { domainImage, formatCurrency, showTranslateProductName } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
const route = useRoute();
const props = defineProps({
	_key:null,
	productData: null,
	productIndex: null,
	animation: null,
	shopData: null,
	view_mode: null,
	preview_mode: null
});

const emit = defineEmits(['product_click', 'add_to_cart_click']);

var targetItemResult = ref<HTMLElement>()
var isVisible = ref(false);
const observer = ref<IntersectionObserver | null>(null);
onMounted(() => {
	if (process.client) {
		observer.value = new IntersectionObserver(
			([entry]) => {
				// isVisible.value = entry.isIntersecting;
				isVisible.value = true
			},
		), {
			threshold: 1,
		};

		if (targetItemResult.value) {
			observer.value.observe(targetItemResult.value);
		}
	}
	else {
		isVisible.value = true;
	}
});
</script>
<template>
	
		<div 
		:class="`product-item-container product-item-container-${props.view_mode}`"
			ref="targetItemResult"
			:key="`${props.productData.id}`"
			:id="`${props.productData.id}`"
			class=""
		>
			<slot name="top_left_tag" v-if="$slots.top_left_tag"></slot>
			<nuxt-link class="product-item"
				:to="props.preview_mode ? '#' : (appRoute.ProductComponent + '/' + (props.productData?.slug?.length ? props.productData?.slug : props.productData?.id))"
				:title="showTranslateProductName(props.productData)"
			>
				<img loading="lazy" :src="props.productData && props.productData?.profile_picture
					? domainImage + props.productData?.profile_picture
					: icon_for_product" :placeholder="icon_for_product" v-on:error="(e: any) => {
					e.target.src = icon_for_broken_image
				}" />
				<div class="product-item-content">
					<span class="name">{{ showTranslateProductName(props.productData) }}</span>
					<span class="sold-like-amount">
						<span v-if="props.productData?.sold_count">
							{{ $t('AroundComponent.luot_ban', {
								count: props.productData?.sold_count ?? 0
							}) }}
						</span>
						<span v-if="props.productData?.sold_count && props.productData?.likes">
							&nbsp;|&nbsp;
						</span>
						<span v-if="props.productData?.likes">
							{{ $t('AroundComponent.luot_thich', {
								count: props.productData?.likes ?? 0
							}) }}
						</span>
					</span>
					<div class="h-stack price-add-to-cart" v-on:click="async (e) => {
						e.stopPropagation();
						emit('add_to_cart_click');
					}" v-on:click.stop="(e) => { e.preventDefault() }">
						<span class="price">
							<em class="off"
								v-if="(props.productData?.price_off != null && props.productData?.price_off < props.productData?.price)">
								{{
									(parseFloat(props.productData?.price) == 0 || props.productData?.price == null)
										? $t('ShopComponent.gia_lien_he')
										: formatCurrency(parseFloat(props.productData?.price),
											props.productData?.shop
												? props.productData?.shop.currency
												: props.shopData?.currency)
								}}
							</em>
							{{
								(props.productData?.price_off != null && props.productData?.price_off < props.productData?.price)
									? formatCurrency(parseFloat(props.productData?.price_off),
										props.productData?.shop ? props.productData?.shop.currency : shopData.currency)
									: (parseFloat(props.productData?.price) == 0 || props.productData?.price == null)
										? $t('ShopComponent.gia_lien_he')
										: formatCurrency(parseFloat(props.productData?.price),
											props.productData?.shop ? props.productData?.shop.currency : props.shopData?.currency)
							}}
						</span>
						<button class="add-to-cart">
							<Icon name="solar:cart-plus-linear"></Icon>
						</button>
					</div>
				</div>
			</nuxt-link>
		</div>

</template>

<style lang="scss">
@import url('./ItemProductGridStyles.scss');
</style>
