<script setup lang="ts">

import { appConst, appDataStartup, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, showTranslateProductName, showTranslateProductDescription } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import type { CartDto } from '~/assets/appDTO';
import { AuthService } from '~/services/authService/authService';
import { ProductService } from '~/services/productService/productService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '~/assets/image/icon-for-product.png';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import non_avatar from '~/assets/image/non-avatar.jpg';
import { toast } from "vue3-toastify";
import ResetCartComponent from "../resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "../confirm18Age/Confirm18AgeComponent.vue";
import VueCountdown from '@chenfengyuan/vue-countdown';
import moment from "moment";
import { ShopService } from "~/services/shopService/shopService";
import { RatingService } from "~/services/ratingService/ratingService";
import ImageViewerComponent from "../imageViewer/ImageViewerComponent.vue";
import environment from "~/assets/environment/environment";
import { HttpStatusCode } from "axios";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const dataId = route.params.slug;
const { t } = useI18n();
var productGet: any = await useLazyFetch(appConst.apiURL.productDetail + dataId, {
	key: `product_detail_${dataId}`,
	headers: {
		Origin: `https://${environment.domain}`
	},
	cache: 'default',
	getCachedData: (key) => {
		// Check if the data is already cached in the Nuxt payload
		// if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
		// 	console.log("nuxt payload",nuxtApp.payload.data[key]);
		// 	return nuxtApp.payload.data[key]
		// }

		// Check if the data is already cached in the static data
		if (nuxtApp.static.data[key]) {
			return nuxtApp.static.data[key]
		}

		return null
	}
});
var productDetails: any;
var productData = ref();

if (productGet?.data?.value?.status == HttpStatusCode.Ok) {
	productDetails = await JSON.parse(JSON.stringify(productGet.data.value.body.data));
	if (productDetails?.enable) {
		productData.value = productDetails;
		let metaName = await showTranslateProductName(productData.value);
		let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
		useServerSeoMeta({
			title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
			ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
		})
	}
}


var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var ratingService = new RatingService();

var userProfile = ref(null as any);
var refreshing = ref(true);

var productID = ref((route.params?.slug ? route.params.slug : null) as string);
var selectedChildProduct = ref(null as any);
var userInfo = ref();
var shopData = ref();
var quantityRef = ref(0);
var cartData = ref([] as any[]);
var loadMore = ref(false);
var isErrored = ref(false);
var isAuth = ref(false);
var isUpdating = ref(false);
// var showModalConfirmResetCart = ref(false);
// var showModalConfirm18Age = ref(false);
var isShowFullNote = ref(false);
var showFullButton = ref(false);
var showAddToCartModal = ref(false);
var showAddCommentModal = ref(false);

var loadMoreTimeOut: any;
var webInApp = ref(null as any);

var notesToCartRef = ref("");
var showNoteInCart = ref(false);
var similarProducts = ref([] as any);
var slidersPerViewSimilar = ref(2);

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);
// var expireTime = ref(moment('2024-05-24 9:00:00'));
// var currentTime = ref(moment());
onActivated(async () => {

	// await getDetailProduct().then(async () => {
	// 	let cartDataLocal = await localStorage.getItem(appConst.storageKey.cart);
	// 	cartData.value = cartDataLocal ? JSON.parse(cartDataLocal as string) : []
	// 	let index = await cartData.value.findIndex((res: any) => {
	// 		return res.product_id == productData.value.id
	// 	});
	// 	quantity = index != -1 ? cartData.value[index].quantity : 0
	// });
})
onBeforeMount(async () => {
})
onMounted(async () => {
	refreshing.value = true;
	nuxtApp.$listen(appConst.event_key.cart_change, (e) => {
		getCartNumber();
	})
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	if (productData.value) {
		let metaName = await showTranslateProductName(productData.value);
		let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
		useSeoMeta({
			title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
			description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
			ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
		})
		if (productData.value.children_products.length) {
			productData.value.children_products.forEach((item: any) => {
				item.shop = JSON.parse(JSON.stringify(productData.value.shop));

			})

		}
		getCartNumber();
		checkNoteShowFullable();
		getSimilarProducts();
		getProductRating()
		productData.value.images = productData.value?.images.sort((a: any, b: any) => a.index - b.index);
		refreshing.value = false;
	}
	else {
		await getDetailProduct().then(async (res) => {
			if (res) {
				if (productData.value.children_products.length) {
					productData.value.children_products.forEach((item: any) => {
						item.shop = JSON.parse(JSON.stringify(productData.value.shop))
					})
				}
				getCartNumber();
				checkNoteShowFullable();
				getSimilarProducts();
				getProductRating();
				productData.value.images = productData.value?.images.sort((a: any, b: any) => a.index - b.index);
			}
			refreshing.value = false;
		});
	}


	loadScript();
});

onUnmounted(async () => {
	if (productData.value?.id) {
		let productRecent = JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) ? JSON.parse(localStorage.getItem(appConst.storageKey.productRecent) as string) : [];
		let index = productRecent.findIndex((e: any) => {
			return e.id == productData.value?.id;
		})
		if (index != -1) {
			productRecent.splice(index, 1)
		}
		productRecent = [
			productData.value,
			...productRecent,
		].slice(0, 10);

		localStorage.setItem(appConst.storageKey.productRecent, JSON.stringify(productRecent));
	}

});

async function getDetailProduct() {
	console.log("lấy thông tin")
	refreshing.value = true;
	return new Promise(async (resolve) => {
		await productService.detailProduct(productID.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				let product$ = JSON.parse(JSON.stringify(res.body.data));
				// console.log(product$);
				if (product$.enable) {
					productData.value = product$;
					refreshing.value = false
					let metaName = await showTranslateProductName(productData.value);
					let metaDescription = showTranslateProductDescription(productData.value)?.length > 65 ? showTranslateProductDescription(productData.value).slice(0, 65).concat('...') : showTranslateProductDescription(productData.value)
					useSeoMeta({
						title: productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						ogTitle: () => productData.value.shop ? `${metaName} | ${productData.value.shop?.name} | Rẻ mà gần` : `${metaName} | Rẻ mà gần`,
						description: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogDescription: () => metaDescription && metaDescription.length ? `${metaName} | ${metaDescription} | remagan.com` : `${metaName} | remagan.com`,
						ogUrl: () => domain + route.fullPath,
						ogImage: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl,
						ogImageUrl: () => productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
					});
					resolve(productData.value)
				}
				else {
					productData.value = null;
					refreshing.value = false
					isErrored.value = true;
					resolve(null)
				}
			}
			else {
				productData.value = null;
				refreshing.value = false
				isErrored.value = true;
				resolve(null)
			}
		}).catch(err => {
			productData.value = null;
			isErrored.value = true;
			refreshing.value = false
			resolve(null)
		})
	})
}
async function getCartNumber() {
	let cartDataLocal = await localStorage.getItem(appConst.storageKey.cart);

	cartData.value = cartDataLocal ? JSON.parse(cartDataLocal) : [];
	let index = await cartData.value.findIndex((res: any) => {
		return res.product_id == productData.value.id
	});
	quantityRef.value = index != -1 ? cartData.value[index].quantity : 0;
}
function getSimilarProducts() {
	return productService.relatedProducts(productData.value.id).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			similarProducts.value = JSON.parse(JSON.stringify(res.body.data.result));
		}
		else {
			similarProducts.value = [];
		}
	}).catch((err => {
		similarProducts.value = [];
	}));
}

function isCloseToBottom(layoutMeasurement: any, contentOffset: any, contentSize: any) {
	const paddingToBottom = 20;
	return layoutMeasurement.height + contentOffset.y >=
		contentSize.height - paddingToBottom;
};

function close() {
	router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

function selectChildProduct(childProduct: any) {
	if (!selectedChildProduct.value || selectedChildProduct.value.id != childProduct.id) {
		selectedChildProduct.value = childProduct;
		let cartTemp = JSON.parse(JSON.stringify(cartData.value));
		let index = cartTemp.findIndex((e: any) => {
			return e.product_id == childProduct.id
		});
		if (index != -1) {
			quantityRef.value = cartTemp[index].quantity
		}
		else {
			quantityRef.value = 0
		}
	}
	else {
		selectedChildProduct.value = null;
		let cartTemp = JSON.parse(JSON.stringify(cartData.value));
		let index = cartTemp.findIndex((e: any) => {
			return e.product_id == productData.value.id
		});
		if (index != -1) {
			quantityRef.value = cartTemp[index].quantity;
		}
		else {
			quantityRef.value = 0;
		}
	}
}

function copyToClipboard(text: string) {
	if (!webInApp.value) {
		window.navigator.clipboard.writeText(text);
	}
	else {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: text
		})
	}
	toast.info(t('ProductComponent.da_sao_chep_lien_ket'), {
		hideProgressBar: true,
		autoClose: 1000
	})
}
function loadScript() {
	const script = document.createElement('script');
	script.type = 'text/javascript';
	script.src = 'https://sp.zalo.me/plugins/sdk.js';
	script.async = false;
	script.defer = true;
	script.id = "zalo_share";
	document.body.appendChild(script);
}
function shareToZalo() {
	document?.getElementById('share_zalo_button')?.click();
}
function shareToApp() {
	let titleShop = productData.value.shop && productData.value.shop.id
		? productData.value.shop.name + " - "
		: "";
	let messageShare = {
		message: titleShop + showTranslateProductName(productData.value) + "\n",
		url: domain + appRoute.ProductComponent + "/" + (productData.value.slug?.length ? productData.value.slug : productData.value.id),
		filename: productData.value.profile_picture ? domainImage + productData.value.profile_picture : baseLogoUrl
	}

	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.share,
		data: messageShare
	})
}
function shareToFacebook() {
	let url = domain + appRoute.ProductComponent + "/" + (productData.value.slug?.length ? productData.value.slug : productData.value.id);
	let hrefLinkShare = "https://www.facebook.com/sharer/sharer.php?u=" + url;
	var x = (window.outerWidth) / 2 - 350;
	var y = (window.outerHeight) / 2 - 300;
	var responsive = "width=700,height=600,left=" + x + ",top=" + y
	window.open(hrefLinkShare, 'name', responsive)
}

function getPercentSaleOff(product: any) {
	if (product.price_off && product.price) return -Math.ceil(((product.price - product.price_off) / product.price) * 100 || 0);
	return 0
}

function checkNoteShowFullable() {
	setTimeout(() => {
		let heighContent = document?.getElementById("product_note_content")?.getBoundingClientRect()?.height || 0;
		let heightParent = document?.getElementById("product_note_container")?.getBoundingClientRect()?.height || 0;
		if (heighContent > 200 && heighContent >= heightParent - 25) {
			showFullButton.value = true;
		}
		else showFullButton.value = false;
	}, 1000);

}

function getSlideHeight(id: any) {
	return document.getElementById(id)?.getBoundingClientRect().width;
}

async function getProductRating() {
	await ratingService.calcObjectRating(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.ratings = res.body.data
		}

	})
	await ratingService.listRatingByObject(productData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.comment_count = res.body.data.count;
			productData.value.comments = res.body.data.result;
		}

	})
}
async function loadmoreRating() {
	await ratingService.listRatingByObject(productData.value.id, productData.value.comments.length, 20).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			productData.value.comments = [...productData.value.comments, ...res.body.data.result];
		}

	})
}

</script>

<template>
	<div class="public-container">
		<div class='v-stack loading-skeleton' v-if="refreshing">
			<v-skeleton-loader height=300></v-skeleton-loader>
			<v-skeleton-loader type="article"></v-skeleton-loader>
		</div>
		<div class='v-stack product-container' v-else-if="productData && productData.id && !refreshing">
			<div class="title-header">
				<div class="header-left">
					<button :title="$t('ProductComponent.quay_ve')" class="back-button" v-on:click="() => {
						router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
					}">
						<Icon name="lucide:chevron-left" />
					</button>
				</div>

				<div class="header-right">
					<button :title="$t('ProductComponent.toi_gio_hang')" class="cart-in-search" v-on:click="() => {
						router.push(appRoute.CartComponent);
					}">
						<Icon name="bi:basket2-fill" size="25"></Icon>
						<em v-if="cartData && cartData.length">{{ cartData.length <= 10 ? cartData.length : "10+" }}
								</em>
					</button>
					<div class="zalo-share-button" id="share_zalo_button"
						:data-href="domain + appRoute.ProductComponent + '/' + (productData.slug?.length ? productData.slug : productData.id)"
						:data-oaid="zaloConfig.appIDZaloLogin" :data-customize='true'>
						{{ $t('ProductComponent.chia_se_qua_zalo') }}
					</div>
					<v-menu class="bootstrap-dropdown-container" content-class="share-dropdown-content"
						location="bottom right">
						<template v-slot:activator="{ props }">
							<button class="share-button" dark v-bind="props" :title="$t('ProductComponent.chia_se')">
								<Icon name="material-symbols:more-horiz" class='share' />
							</button>
						</template>

						<v-list>
							<v-list-item key="copy_url" class="share-dropdown-item" v-on:click="() => {
								copyToClipboard(domain + appRoute.ProductComponent + '/' + (productData.slug?.length ? productData.slug : productData.id));
							}">
								<v-list-item-title>{{ $t('ProductComponent.sao_chep_dia_chi') }}</v-list-item-title>
							</v-list-item>
							<v-list-item key="share_to_facebook" class="share-dropdown-item" v-on:click="() => {
								shareToFacebook()
							}">
								<v-list-item-title>{{ $t('ProductComponent.chia_se_qua_facebook')
									}}</v-list-item-title>
							</v-list-item>
							<v-list-item key="share_to_zalo" class="share-dropdown-item" v-if="!webInApp" v-on:click="() => {
								shareToZalo();
							}">
								<v-list-item-title>{{ $t('ProductComponent.chia_se_qua_zalo') }}</v-list-item-title>
							</v-list-item>
							<v-list-item key="share_to_app" class="share-dropdown-item" v-if="webInApp" v-on:click="() => {
								shareToApp();
							}">
								<v-list-item-title>{{ $t('ProductComponent.khac') }}</v-list-item-title>
							</v-list-item>
						</v-list>
					</v-menu>
				</div>
			</div>
			<div class="product-profile-picture" v-if="!selectedChildProduct">
				<Swiper class="my-carousel stack-carousel product-images-carousel" v-if="productData?.images?.length"
					:modules="[SwiperAutoplay, SwiperNavigation]" :slides-per-view="1" :effect="'creative'"
					:navigation="true" :spaceBetween="10" :autoplay="false" key="suggest-carousel">
					<SwiperSlide class="item-stack-slide" v-for="(item, indexImg) of productData.images"
						:key="'suggest_' + item.id">
						<img loading="lazy" class="item-stack" v-on:click="() => {
							listObjectViewer = productData.images;
							indexActive = indexImg;
							showImageViewerModal = true;
						}" v-on:error="(event: any) => {
							event.target.src = icon_for_broken_image;
						}" :src="item?.path ? (domainImage + item?.path) : icon_for_product" :placeholder="icon_for_product"
							:alt="showTranslateProductName(productData)"
							:title="showTranslateProductName(productData)" />
					</SwiperSlide>
				</Swiper>
				<img v-else v-on:click="() => {
					let itemImg = {
						path: productData.profile_picture,
						title: showTranslateProductName(productData),
					}
					listObjectViewer = [itemImg];
					indexActive = 0;
					showImageViewerModal = true;
				}" v-on:error="(event: any) => {
					event.target.src = icon_for_broken_image;
				}" :src="productData?.profile_picture?.length ? (domainImage + productData.profile_picture) : icon_for_product"
					:placeholder="icon_for_product"
					:alt="productData ? showTranslateProductName(productData) : $t('EditProductComponent.khong_co_avatar')" />

			</div>
			<div class="product-profile-picture" v-else>
				<Swiper class="my-carousel stack-carousel product-images-carousel"
					v-if="selectedChildProduct.images?.length" :modules="[SwiperAutoplay, SwiperNavigation]"
					:slides-per-view="1" :effect="'creative'" :navigation="true" :spaceBetween="10" :autoplay="false"
					key="suggest-carousel">
					<SwiperSlide class="item-stack-slide" v-for="item of selectedChildProduct.images"
						:key="'suggest_' + item.id">
						<img loading="lazy" class="item-stack" v-on:error="(event: any) => {
							event.target.src = icon_for_broken_image;
						}" :src="item?.path ? (domainImage + item?.path) : icon_for_product" :placeholder="icon_for_product"
							:alt="showTranslateProductName(productData)"
							:title="showTranslateProductName(productData)" />
					</SwiperSlide>
				</Swiper>
				<img v-else v-on:error="(event: any) => {
					event.target.src = icon_for_broken_image;
				}" :src="selectedChildProduct?.profile_picture && selectedChildProduct?.profile_picture?.length ? domainImage + selectedChildProduct?.profile_picture : icon_for_product"
					:placeholder="icon_for_product"
					:alt="productData ? showTranslateProductName(productData) : $t('EditProductComponent.khong_co_avatar')" />
			</div>

			<div class="product-detail-stack primary-detail">
				<span class="product-name">
					<!-- {{ productData?.name }} -->
					{{ showTranslateProductName(productData) }}
				</span>
				<div class="rating-sold-amount">
					<div class="rating" v-if="productData.ratings">
						<Icon name="ic:round-star-rate"></Icon> {{ productData.ratings.toFixed(1) || 0 }}<span>/{{
							appConst.defaultMaxRate }}</span>
					</div>

					<div class="rate-amount" v-if="productData.ratings">
						{{ productData.comment_count || 0 }} {{ $t('ProductComponent.danh_gia') }}
					</div>

					<div class="sold-amount" v-if="productData.sold_counter > 10">
						{{ $t('ProductComponent.da_ban', { count: productData.sold_counter || 0 }) }}
					</div>
				</div>
				<div class="flash-sale" v-if="false">
					<div class="flash-sale-label">
						<Icon name="bi:lightning-fill"></Icon>
						{{ $t('ProductComponent.flash_sale') }}
					</div>
					<div class="expire-time">
						<span>
							{{ $t('ProductComponent.ket_thuc_sau') }}
						</span>
						<span class="count-down">
							<client-only>
								<!-- <VueCountdown :time="expireTime.valueOf() - moment().valueOf()"
						v-slot="{ days, hours, minutes, seconds }">
						{{ hours }} : {{ minutes }} : {{ seconds }}
					</VueCountdown> -->
							</client-only>
							<!-- <vue-countdown :time="2 * 60 * 60 * 1000" v-slot="{ days, hours, minutes, seconds }">
					{{ hours }} : {{ minutes }} : {{ seconds }}
				</vue-countdown> -->
						</span>
					</div>
				</div>

				<div class="product-price-container">
					<div class="price-content">
						<span class='product-price' v-if="selectedChildProduct && selectedChildProduct.id">
							{{
								(selectedChildProduct.price_off != null && selectedChildProduct.price_off <
									selectedChildProduct.price) ? formatCurrency(parseFloat(selectedChildProduct.price_off),
										productData.shop?.currency) : (parseFloat(selectedChildProduct.price) == 0 ||
											selectedChildProduct.price == null) ? $t('ProductComponent.gia_lien_he') :
									formatCurrency(parseFloat(selectedChildProduct.price), productData.shop?.currency) }}
								<em class="off"
								v-if="(selectedChildProduct.price_off != null && selectedChildProduct.price_off < selectedChildProduct.price)">
								{{
									(parseFloat(selectedChildProduct.price) == 0 || selectedChildProduct.price ==
										null)
										? $t('ProductComponent.gia_lien_he')
										: formatCurrency(parseFloat(selectedChildProduct.price), productData.shop?.currency)
								}}</em>
						</span>
						<span class='product-price' v-else>
							{{
								(productData.price_off != null && productData.price_off < productData.price) ?
									formatCurrency(parseFloat(productData.price_off), productData.shop?.currency) :
									(parseFloat(productData.price) == 0 || productData.price == null) ?
										$t('ProductComponent.gia_lien_he') : formatCurrency(parseFloat(productData.price),
											productData.shop?.currency) }} <em class="off"
								v-if="(productData.price_off != null && productData.price_off < productData.price)">{{
									(parseFloat(productData.price) == 0 || productData.price == null)
										? $t('ProductComponent.gia_lien_he')
										: formatCurrency(parseFloat(productData.price), productData.shop?.currency)
								}}</em>
						</span>
					</div>
					<div class="sale-off-badge"
						v-if="selectedChildProduct?.id && selectedChildProduct?.price_off != null && selectedChildProduct?.price_off < selectedChildProduct.price">
						{{ getPercentSaleOff(selectedChildProduct) }}%
					</div>
					<div class="sale-off-badge"
						v-if="!selectedChildProduct?.id && productData?.price_off != null && productData?.price_off < productData.price">
						{{ getPercentSaleOff(productData) }}%
					</div>
				</div>

				<div class="select-child-product" v-if="productData.children_products?.length">
					<div class="selection-show">
						<span class="label">{{ $t('ProductComponent.lua_chon') }}</span>
						<span class="child-product-name">{{ selectedChildProduct ?
							showTranslateProductName(selectedChildProduct) :
							$t('ProductComponent.chua_chon')
							}}</span>
					</div>
					<v-menu class="bootstrap-dropdown-container children-products-select-container"
						location="bottom right" contained content-class="dropdown-content">

						<template v-slot:activator="{ props }">
							<button class="select-child-product-button" v-bind="props">
								{{ $t('ProductComponent.thay_doi') }}
							</button>
						</template>

						<div class="children-products">
							<button v-for="itemChild of productData.children_products" class="item-child-product"
								:class="{ 'active': itemChild.id == selectedChildProduct?.id }" v-on:click="() => {
									selectChildProduct(itemChild)
								}">
								<!-- {{ itemChild.name }} -->
								{{ showTranslateProductName(itemChild) }}
							</button>
						</div>
					</v-menu>
				</div>

			</div>


			<div class="product-detail-stack list-notes-container" v-if="false">
				<span class="label">{{ $t('ProductComponent.noi_bat') }}</span>
				<div class="list-notes">
					<div class="note-item">
						<Icon name="majesticons:check-circle"></Icon>
						<span>{{ $t('ProductComponent.note_1') }}</span>
					</div>
					<div class="note-item">
						<Icon name="majesticons:check-circle"></Icon>
						<span>{{ $t('ProductComponent.note_2') }}</span>
					</div>
					<div class="note-item">
						<Icon name="majesticons:check-circle"></Icon>
						<span>{{ $t('ProductComponent.note_3') }}</span>
					</div>
				</div>
			</div>
			<!-- <div class="stack-space"></div> -->
			<div class="product-detail-stack add-to-cart-stack">
				<!-- <button class="message" title="{{ $t('ProductComponent.them_ghi_chu') }}" v-on:click="()=>{
		showNoteInCart = !showNoteInCart;
	}">
		<Icon name="ph:chat-circle-dots"></Icon>
	</button> -->
				<button class="add-to-cart" v-on:click="async () => {
					showAddToCartModal = true;
				}" v-on:click.stop="(e) => { e.preventDefault() }">{{ $t('ProductComponent.them_vao_gio') }}</button>
				<!-- <button class="quick-add-to-cart" v-on:click="async () => {
		showAddToCartModal = true;
	}" v-on:click.stop="(e) => { e.preventDefault() }">{{ $t('ProductComponent.mua_nhanh') }}</button> -->
				<input v-show="showNoteInCart" autoComplete="off" class='note-input'
					placeholder="{{ $t('ProductComponent.ghi_chu_cho_san_pham') }}"
					:maxlength="appConst.max_text_short"
					title="{{ $t('ProductComponent.ghi_chu_cho_san_pham') }}" v-model="notesToCartRef" v-if="false" />
			</div>
			<div class="stack-space" v-if="false"></div>
			<div class="product-detail-stack vouchers" v-if="false">
				<span class="label">{{ $t('ProductComponent.uu_dai') }}</span>
				<div class="list-voucher">
					<button>{{ $t('ProductComponent.voucher_5') }}</button>
					<button>{{ $t('ProductComponent.voucher_10k') }}</button>
					<button>{{ $t('ProductComponent.voucher_35k') }}</button>
					<button>{{ $t('ProductComponent.voucher_25k') }}</button>
					<button>{{ $t('ProductComponent.voucher_22k') }}</button>
					<button>{{ $t('ProductComponent.voucher_12') }}</button>
					<button>{{ $t('ProductComponent.voucher_15k') }}</button>
					<button>{{ $t('ProductComponent.voucher_8') }}</button>
				</div>
			</div>
			<div class="stack-space" v-if="false"></div>
			<div class="product-detail-stack shop-info-container" v-if="productData?.shop?.id">
				<div class="shop-info-content">
					<!-- <div class="shop-logo">
			<img loading="lazy" :src="shop_logo" :placeholder="shop_logo" alt=""
				v-if="!productData.shop?.logo" />
			<div class="logo-origin-container"
				:class="{ 'none-style': !(productData.shop?.logo?.style?.length) }" v-else>
				<img :src="domainImage + productData.shop?.logo?.path" :style="{
					transform: (productData.shop?.logo?.style) ? productData.shop?.logo?.style : 'none'
				}" alt="" />
			</div>
		</div> -->
					<AvatarComponent :imgTitle="productData.shop?.name" :imgStyle="productData.shop?.logo?.style"
						:imgSrc="productData.shop?.logo
							? (domainImage + productData.shop?.logo.path)
							: productData.shop?.banner
								? (domainImage + productData.shop?.banner.path)
								: ''
							" :width="50" :height="50" />
					<div class="shop-name-address">
						<span class="name">{{ productData.shop?.name }}</span>
						<span class="address">{{ productData.shop?.address }}</span>
					</div>
					<nuxt-link
						:to="appRoute.DetailShopComponent + '/' + (productData.shop?.slug || productData.shop?.id)">
						<button>{{ $t('ProductComponent.xem_shop') }}</button>
					</nuxt-link>
				</div>
				<div class="shop-preview-content" v-if="false">
					<div class="complete-order">
						<span class="label">{{ $t('ProductComponent.ty_le_don_hang_thanh_cong') }}</span>
						<span class="value">97<em>% (395 {{ $t('ProductComponent.don_hang') }})</em></span>
					</div>
					<div class="online-status">
						<span class="label">
							{{ $t('ProductComponent.da_online') }}
						</span>
						<span class="value">
							13 <em>{{ $t('ProductComponent.phut_truoc') }}</em>
						</span>
					</div>
				</div>
			</div>

			<div class="stack-space" v-if="similarProducts?.length"></div>
			<div class="product-detail-stack stack-similar" v-if="similarProducts?.length">
				<span class="label">{{ $t('ProductComponent.tuong_tu') }}</span>
				<div class="stack-content">
					<Swiper class="my-carousel stack-carousel related-product-carousel"
						:modules="[SwiperNavigation, SwiperFreeMode]" :freeMode="true" :navigation="true"
						:slides-per-view="slidersPerViewSimilar" :slidesPerGroup="1" :loopAdditionalSlides="50"
						:spaceBetween="15" :loop="similarProducts.length >= slidersPerViewSimilar * 2" :effect="'slide'"
						key="similar-product-carousel">
						<SwiperSlide class="item-stack-slide" v-for="(itemProduct, index) of similarProducts"
							:key="'similar_product_' + itemProduct.id + '_' + index"
							:id="'similar_product_' + itemProduct.id + '_' + index">
							<nuxt-link
								:to="appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id)"
								class="product-item-container-grid">
								<img loading="lazy" v-bind:style="{
									height: getSlideHeight('similar_product_' + itemProduct.id) + 'px',
									maxHeight: getSlideHeight('similar_product_' + itemProduct.id) + 'px'
								}" v-on:click="() => {
									router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
								}" :src="(itemProduct && itemProduct.profile_picture)
									? (domainImage + itemProduct.profile_picture)
									: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" v-on:error="(event: any) => {
										event.target.src = icon_for_product;
									}" />
								<div class='v-stack product-detail'>
									<span class='product-name' v-on:click="() => {
										// this.addProductToCart(itemProduct)
										router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
									}">
										<!-- {{ itemProduct.name }} -->
										{{ showTranslateProductName(itemProduct) }}
									</span>

									<div class="h-stack">
										<span class='product-price' :title="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
											formatCurrency(parseFloat(itemProduct.price_off),
												productData?.shop?.currency) : (parseFloat(itemProduct.price) == 0 ||
													itemProduct.price == null) ? $t('ProductComponent.gia_lien_he') :
												formatCurrency(parseFloat(itemProduct.price), productData?.shop?.currency)" v-on:click="() => {
													// this.addProductToCart(itemProduct)
													router.push(appRoute.ProductComponent + '/' + (itemProduct.slug?.length ? itemProduct.slug : itemProduct.id))
												}">
											{{
												(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)
													? formatCurrency(parseFloat(itemProduct.price_off),
														productData?.shop?.currency) : (parseFloat(itemProduct.price) == 0 ||
															itemProduct.price == null) ? $t('ProductComponent.gia_lien_he') :
														formatCurrency(parseFloat(itemProduct.price),
															productData?.shop?.currency) }} <em class="off" :title="formatCurrency(itemProduct.price ? parseFloat(itemProduct.price) : 0,
													productData?.shop?.currency)" v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
												{{
													formatCurrency(itemProduct.price ? parseFloat(itemProduct.price) : 0,
														productData?.shop?.currency)
												}}</em>
										</span>
										<div class="sale-off-badge"
											v-if="(itemProduct.price_off != null && itemProduct.price_off < itemProduct.price)">
											{{ getPercentSaleOff(itemProduct) }}%
										</div>
									</div>
									<div class="rating-sold-amount">
										<div class="rating" v-if="false">
											<Icon name="ic:round-star-rate"></Icon> {{ itemProduct.ratings?.value || 5
											}}<span>/{{ appConst.defaultMaxRate }}</span>
										</div>
										<div class="sold-amount" v-if="productData.sold_counter > 10">
											{{ $t('ProductComponent.da_ban') }} {{ itemProduct.sold_counter || 0 }}
										</div>
									</div>
								</div>
							</nuxt-link>
						</SwiperSlide>
					</Swiper>
				</div>
			</div>

			<div class="stack-space" v-if="productData && productData.categories && productData.categories.length">
			</div>
			<div class="product-detail-stack categories-container"
				v-if="productData && productData.categories && productData.categories.length">
				<span class="label">{{ $t('ProductComponent.thong_tin_phan_loai') }}</span>
				<div class="stack-content">
					<div class="stack-content-item category">
						<span class="label">{{ $t('ProductComponent.danh_muc') }}</span>
						<div class="content">
							<span v-for="(categoryItem, index) of productData.categories"
								:key="'category_' + categoryItem.id">
								{{ categoryItem.name + (index < productData.categories.length - 1 ? ", " : " ") }}
									</span>
						</div>
					</div>
					<div class="stack-content-item" v-if="productData.brand_id">
						<span class="label">{{ $t('ProductComponent.thuong_hieu') }}</span>
						<div class="content">
							<span>{{ productData.brand?.name }}</span>
						</div>
					</div>
					<div class="stack-content-item" v-if="false">
						<span class="label">{{ $t('ProductComponent.xuat_xu') }}</span>
						<div class="content">
							<span>{{ productData.origin?.name || $t('ProductComponent.viet_nam') }}</span>
						</div>
					</div>
				</div>
			</div>

			<div class="stack-space" v-if="showTranslateProductDescription(productData)?.length"></div>
			<div class="product-detail-stack description" v-if="showTranslateProductDescription(productData)?.length">
				<span class="label">{{ $t('ProductComponent.mo_ta') }}</span>
				<div class="description-container" id="product_note_container" :class="{ 'show-full': isShowFullNote }">
					<span id="product_note_content">
						<!-- {{ productData.notes }} -->
						{{ showTranslateProductDescription(productData) }}
					</span>
					<div class="show-full-button" v-if="showFullButton">
						<button v-on:click="() => {
							isShowFullNote = !isShowFullNote;
							checkNoteShowFullable();
						}">{{ isShowFullNote ? $t('ProductComponent.thu_gon') : $t('ProductComponent.mo_rong') }}</button>
					</div>
				</div>
			</div>

			<div class="stack-space"></div>
			<div class="product-detail-stack">
				<div class="stack-title">
					<span class="label">{{ $t('ProductComponent.Danh_gia') }}</span>
					<button class="add-comment" v-on:click="showAddCommentModal = true">
						<Icon name="material-symbols:add-comment-outline"></Icon> {{
							$t('ProductComponent.them_danh_gia') }}
					</button>
				</div>

				<div class="rating-overview">
					<div class="rating" v-if="productData.ratings">
						<Icon name="ic:round-star-rate"></Icon><span>{{ productData.ratings.toFixed(1) || 0 }}/{{
							appConst.defaultMaxRate }}</span>
					</div>
					<div class="comment">
						{{ productData.comment_count }} {{ t('ProductComponent.danh_gia') }}
					</div>
				</div>

				<div class="comment-container" id="product_comment">
					<div class="comment-item-container" v-for="itemComment in productData.comments">
						<div class="rate">
							<NuxtRating v-if="itemComment.ratings" active-color="#ffc107"
								:border-color="'transparent'" :rounded-corners="true" :rating-spacing="5"
								:rating-step=".5" :rating-size="10" :rating-value="itemComment.ratings || 0"
								class="rating" />
							<span>
								{{ t(`ProductComponent.${appConst.rating_text_key[itemComment.ratings]}`) }}
							</span>
						</div>
						<div class="comment-detail" v-if="itemComment.review?.length">
							{{ itemComment.review }}
						</div>
						<div class="comment-images" v-if="itemComment.images?.length">
							<div class="comment-image-item" v-for="(itemImg, index) in itemComment.images" v-on:click="() => {
								listObjectViewer = JSON.parse(JSON.stringify(itemComment.images));
								indexActive = index;
								showImageViewerModal = true;
							}">
								<img :src="itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image"
									v-on:error="(event: any) => {
										event.target.title = t('ProductComponent.loi_anh');
										event.target.src = icon_for_broken_image;
									}" v-on:click="(event: any) => {
										event.target.title = '';
										event.target.src = itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image
									}" loading="lazy"/>
							</div>
						</div>
						<div class="user-info" v-if="itemComment.users?.id">
							<img loading="lazy" :src="itemComment.users?.profile_picture
								? (domainImage + itemComment.users.profile_picture)
								: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:error="(event: any) => {
									event.target.src = non_avatar;
								}" />
							<span class="name">
								{{ itemComment.users.name }}
							</span>
						</div>
					</div>
				</div>
				<button class="rate-loadmore" v-if="productData?.comments?.length < productData.comment_count"
					v-on:click="loadmoreRating()">
					{{ t('ProductComponent.xem_them') }}
				</button>
				<div class="show-all-rating" v-else>
					{{ t('ProductComponent.hien_thi_tat_ca') }}
				</div>
			</div>

		</div>

		<div class="h-100" v-else-if="isErrored == true">
			<HeaderComponent :title="$t('AppRouteTitle.Product404Component')"></HeaderComponent>
			<Product404Component />
		</div>

		<AddProductToCartComponent v-if="showAddToCartModal" v-on:close="() => {
			showAddToCartModal = false
		}" :selectedProduct="selectedChildProduct?.id ? selectedChildProduct : productData" :noteForAddItem="notesToCartRef">
		</AddProductToCartComponent>
		<AddCommentComponent v-if="showAddCommentModal" :showAddCommentModal="showAddCommentModal"
			:object="selectedChildProduct?.id ? selectedChildProduct : productData"
			:object_type="appConst.object_type.product" v-on:close="(e: any) => {
				if (e == true) {
					getProductRating();
				}
				showAddCommentModal = false
			}" v-on:submit="() => {

			}">

		</AddCommentComponent>
		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>
	</div>

</template>
<style lang="scss" src="./ProductStyles.scss"></style>