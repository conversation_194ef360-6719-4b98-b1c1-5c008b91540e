.create-password-modal{
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 0 10px 10px;
  background-color: white;
}
.create-password-container {
  flex: 1;
  display: flex;
  font-size: 1.2em;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  background: white;

  & .create-password-content {
    padding: 10px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: auto;
    gap: 5px;

    & > .h-stack{
      align-items: baseline;

      & > .content-input{
        padding: 0;
        color: var(--primary-color-1);
        font-weight: 600;
      }
    }
    & .label{
      font-size: 13px;
      margin-right: 5px;
    }

    & > .label-content{
      font-size: 17px;
      color: #1a1a1d;
      font-weight: 600;
      text-align: center;

      & > em{
        color: var(--primary-color-1);
        font-weight: bold;
      }
    }

    & > .password-content{
      display: flex;
      flex-direction: column;
    }

    & .content-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: var(--color-background-2);
      padding-left: 10px;
      border-radius: 5px;
      font-size: 17px;
      margin-bottom: 10px;

      & > input {
        flex: 1;
        overflow: auto;
      }
      & > input:-webkit-autofill {
        background-color: initial !important;
      }
      & > button {
        background: transparent;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: var(--primary-color-1);
      }
    }
    & .content-input {
      background: transparent;
      border: none;
      outline: none;
      padding: 10px 0;
      border-radius: inherit;
    }
    & .error-message{
      font-size: 13px;
    }
    & .error-message.hight-light:not(.success){
      transform-origin: 0 0;
      animation: high-light .5s ease-in-out infinite;
    }
    @keyframes high-light {
      50% {
        opacity: 0.0;
      }
    }
    & .error-message.success{
      font-style: normal;
      color: green;
    }
  }

  & > .create-password-footer{
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
  }
}
