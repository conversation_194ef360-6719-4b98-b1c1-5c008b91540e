import axios, { HttpStatusCode } from 'axios';
import { axiosETAGCache } from 'axios-etag-cache';

const axiosWithETAGCache = axiosETAGCache(axios);
export class BaseHTTPService {

    async https(method: 'POST' | 'PUT' | 'GET', url: string, body?: any, cancelToken?: any, authentication_required=false) {
        let token = null;
        let finger_print_id = null;

        token = await localStorage.getItem('token');
        finger_print_id = await localStorage.getItem('finger_print_id');
        if(!authentication_required || (authentication_required && token)){
            return axiosWithETAGCache({
                method: method,
                url: url,
                data: body ? body : null,
                params: (method == 'GET' && body) ? body : null,
                headers: {
                    Authorization: token ? `Bearer ${token}` : null,
                    // "X-Requested-Withs": finger_print_id, // success
                    "X-Device-Id": finger_print_id // fail
                    // "User-Agent": window.navigator.userAgent
                },
                cancelToken: cancelToken,

            }).then(response => {
                if (response.status == 200) {
                    return response.data
                }
                else {
                    console.error(response);
                    return response;
                }            }).catch(error => {
                    if (axios.isCancel(error)) {
                        // Request was cancelled
                    }
                    else if (error.response) {
                        // The request was made and the server responded with a status code
                        // that falls out of the range of 2xx
                        // console.error(error.response.data);
                        // console.error(error.response.status);
                        // console.error(error.response.headers);                    } else if (error.request) {
                        // The request was made but no response was received
                        // Check for network disconnection error
                        console.log('Network error details:', {
                            code: error.code,
                            message: error.message,
                            isOnline: navigator.onLine
                        });
                        
                        if (error.code === 'ERR_INTERNET_DISCONNECTED' || 
                            error.code === 'ERR_NETWORK' ||
                            error.code === 'NETWORK_ERROR' ||
                            !navigator.onLine ||
                            error.message?.includes('Network Error') ||
                            error.message?.includes('ERR_INTERNET_DISCONNECTED') ||
                            error.message?.includes('net::ERR_INTERNET_DISCONNECTED') ||
                            error.message?.includes('ERR_NETWORK')) {
                            
                            console.log('Network error detected, showing modal');
                            // Import and use the network error composable
                            if (process.client) {
                                import('~/composables/useNetworkError').then(({ useNetworkError }) => {
                                    const { show } = useNetworkError()
                                    show()
                                }).catch(err => {
                                    console.error('Failed to load network error composable:', err)
                                })
                            }
                        }
                        // console.error(error.request);
                    } else {
                        // Something happened in setting up the request that triggered an Error
                        // console.error('Error', error.message);
                    }
                    return error;
            });
        }
        else{
            return {
                status: HttpStatusCode.BadRequest,
                body: false
            }
        }
        
    }
}