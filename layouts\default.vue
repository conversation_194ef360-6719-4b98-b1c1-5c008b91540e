<template>
	<div class='container-root contaner-root-1' id='container-root'>
		<NuxtLoadingIndicator />
		<!-- <div v-if="loading" class="fixed left-0 top-0 h-0.5 w-full z-50 bg-green-500"></div> -->
		<div class="root-container" id="root_container">
			<v-app>
				<HeaderV2WithMenuComponent :header_level_2_id="'search_input_container'"
					:last_element_id="'last_of_list_product'">
				</HeaderV2WithMenuComponent>

				<NuxtPage ref="pageWrapper"></NuxtPage>
				<FooterV2Component :class="{ 'hide': !checkShowFooter() }">
				</FooterV2Component>
				<client-only>
					<VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false"
						:overlay-behavior="'persist'"
						content-class="v-stack modal-content-container need-update-container" v-model="needToUpdate"
						v-on:closed="() => {
							needToUpdate = false
						}" contentTransition="vfm-fade">
						<div class='v-stack need-update-content'>
							<span class='need-update-title'>
								{{ $t('DefaultPage.re_ma_gan_da_co_phien_ban_moi') }}
							</span>

							<span class='need-update-message'>
								{{ $t('DefaultPage.vui_long_cap_nhat_ung_dung_va_quay_lai') }}
							</span>
						</div>
						<div class='h-stack confirm-modal-buttons'>
							<button class='accept-button' v-on:click="() => {
								gotoStore()
							}">
								{{ $t('DefaultPage.cap_nhat') }}
							</button>
						</div>
					</VueFinalModal>

					<VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false"
						:overlay-behavior="'persist'"
						content-class="v-stack modal-content-container need-update-container" v-model="showPopupOffline"
						contentTransition="vfm-fade">
						<div class='v-stack need-update-content'>
							<span class='need-update-title'>
								{{ $t('DefaultPage.ban_dang_offline') }}
							</span>

							<span class='need-update-message'>
								{{ $t('DefaultPage.vui_long_kiem_tra_ket_noi_va_tai_lai') }}
							</span>
						</div>
						<div class='h-stack confirm-modal-buttons'>
							<button class='accept-button' v-on:click="() => {
								// gotoStore()
								reloadPage();
							}">
								{{ $t('DefaultPage.tai_lai') }}
							</button>
						</div>
					</VueFinalModal>
					<CreatePasswordComponent v-if="showCreatePasswordModal"
						:userData="JSON.parse(JSON.stringify(userInfo))" :showModal="showCreatePasswordModal"
						v-on:close="() => {
							showCreatePasswordModal = false
						}" v-on:submit="(data: any) => {
							showCreatePasswordModal = false;
							checkAuth();
						}"></CreatePasswordComponent>
					<NewDeliveryNotiComponent v-if="showNewDelivery" :delivery_id="newDeliveryID" :new="true"
						v-on:close="() => {
							showNewDelivery = false;
							newDeliveryID = null;
							nuxtApp.$emit(appConst.event_key.refresh_delivery_detail);
						}"></NewDeliveryNotiComponent>

					<ChangePermissionComponent v-if="showChangePermissionModal" v-on:close="() => {
						showChangePermissionModal = false;
					}">
					</ChangePermissionComponent>

					<VueFinalModal class="my-modal-container" content-class="v-stack user-disabled-modal"
						:overlay-behavior="'persist'" v-model="showUserDisabledModal" v-on:closed="() => {
							showUserDisabledModal = false
						}" contentTransition="vfm-slide-up">
						<div>
							<button v-on:click="() => {
								showUserDisabledModal = false
							}">
								<Icon name="material-symbols:cancel-outline"></Icon>
							</button>
							<div class='v-stack'>
								<img loading="lazy" :src="access_denied" :placeholde="access_denied" />
								<p class='message'>
									{{ $t('LoginComponent.tai_khoan_da_bi_khoa') }}
								</p>
								<p class='message'>
									{{ $t('LoginComponent.vui_long_lien_he') }}
									<nuxt-link :to="appRoute.SupportComponent">{{ $t('LoginComponent.ho_tro')
									}}</nuxt-link>
								</p>
							</div>

						</div>
					</VueFinalModal>

					<VueFinalModal class="my-modal-container" content-class="v-stack popup-first-load-modal"
						:overlay-behavior="'persist'" v-model="showFirstPopupModal" v-on:closed="() => {
							showFirstPopupModal = false
						}" contentTransition="vfm-slide-up">
						<div>
							<img :src="first_popup" loading="lazy" />
							<button v-on:click="() => {
								showFirstPopupModal = false
							}">
								<Icon name="material-symbols:cancel-outline"></Icon>
							</button>

						</div>
					</VueFinalModal>

					<SharePopupComponent v-if="showSharePopup" :link_share="currentShareData?.link"
						:name_share="currentShareData?.name" :image_share="currentShareData?.image" v-on:close="() => {
							currentShareData.value = null;
							showSharePopup = false
						}"></SharePopupComponent>
					<RequireLoginModal v-if="showRequireLoginModal" :redirect_to="requireLoginRedirect" v-on:close="(backClick:any) => {
						showRequireLoginModal = false;
						requireLoginRedirect = null;
						if (backOnClose && !backClick) {
							router.options.history.state.back ? router.back() : router.replace(appRoute.HomeComponent)
							backOnClose = false
						}					}" v-on:goLogin="() => {
							showRequireLoginModal = false;
						}"></RequireLoginModal>
				</client-only>

				<!-- Network Error Modal -->
				<client-only>
					<NetworkErrorComponent />
				</client-only>

			</v-app>
			<!-- <SplashComponent v-else></SplashComponent> -->
		</div>
		<!-- <UseDraggable class="new-orders-ticket" v-show="newOrderAmount > 0" id="new_order_ticket"
			:initial-value="{ x: 10, y: 10 }">
			<div class="my-shop" v-on:click="() => {
				router.push(appRoute.ManageOrdersComponent + '#waiting')
			}">
				<img src="~/assets/image_29_05_2024/new-order.png" alt="có đơn hàng mới">
				<span>{{ newOrderAmount }}</span>
			</div>
		</UseDraggable> -->
		<client-only>
			<div ref="orderTicketEl" class="new-orders-ticket" v-show="newOrderAmount > 0 && orderCheckable"
				:style="{ top: `${orderTicketPosition.y}px`, left: `${orderTicketPosition.x}px` }">

				<v-menu class="bootstrap-dropdown-container" location="left" content-class="overlay-dropdown-content">
					<template v-slot:activator="{ props }">
						<button v-bind="props" class="my-shop" :disabled="ticketDragging">
							<img src="~/assets/image_29_05_2024/new-order.png" :alt="$t('DefaultPage.co_don_hang_moi')"
								:draggable="false" />
							<span>{{ newOrderAmount }}</span>
						</button>
					</template>

					<v-list>
						<v-list-item key="go_to_order_manage" class="overlay-dropdown-item" v-on:click="(e: any) => {
							if (router.currentRoute.value.fullPath.includes(appRoute.ManageOrdersComponent)) {
								router.push({
									path: appRoute.ManageOrdersComponent + '#waiting',
									hash: '#waiting',
									state: {
										tabIndex: 'waiting'
									},
								}).then(() => {
									nuxtApp.$emit('refresh_order_manage')
									// router.go(0);
								})
							}
							else {
								router.push({
									path: appRoute.ManageOrdersComponent + '#waiting',
									hash: '#waiting',
									state: {
										tabIndex: 'waiting'
									},
									force: true,
									replace: true,
								})
							}

						}">
							<Icon name="mdi:eye-check"></Icon>
							<v-list-item-title>{{ $t('DefaultPage.kiem_tra_don_hang') }}</v-list-item-title>
						</v-list-item>
						<v-list-item key="mute_5_minutes" class="overlay-dropdown-item" v-on:click="() => {
							muteCheckOrder(5 * 60 * 1000)
						}">
							<Icon name="material-symbols:volume-off"></Icon>
							<v-list-item-title>{{ $t('DefaultPage.tat_thong_bao_trong_5_phut') }}</v-list-item-title>
						</v-list-item>
					</v-list>
				</v-menu>

			</div>

			<ModalsContainer />
			<div id="auto_click"></div>
		</client-only>


		<!-- <Audio :src="notiSound" id="auto_click" ref="audioRef"></Audio> -->
	</div>
</template>
<script setup lang="ts">
import access_denied from "~/assets/image/access_denied.png";
import first_popup from '~/assets/imageV2/Pop-up_T11_P1(1).png';
import { ref } from 'vue';
import { useDraggable } from '@vueuse/core'
import { isSupported } from 'firebase/analytics';
import { onMessage } from 'firebase/messaging';
import { ModalsContainer, VueFinalModal } from 'vue-final-modal';
import { appDataStartup, appConst } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
// import { requestPermission } from '~/firebase';
import { AuthService } from '~/services/authService/authService';
import { BusinessTypeService } from '~/services/businessTypeService/businessTypeService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { FCMService } from '~/services/fcmService';
import { OrderService } from '~/services/orderService/orderService';
import { PlaceService } from '~/services/placeService/placeService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import notiSound from '~/assets/sound/noti.wav'
import moment from 'moment';
import CreatePasswordComponent from '~/components/createPassword/CreatePasswordComponent.vue';
import { toast } from 'vue3-toastify';
import axios, { HttpStatusCode } from 'axios';
import { ImageService } from '~/services/imageService/imageService';
import environment from "~/assets/environment/environment";
import { InteractionService } from "~/services/interactionService/interactionService";
import { ChatService } from "~/services/chatService/chatService";
import { MqttService } from "~/services/mqttService/mqttService";
import { member_type, type ChannelDTO } from "~/components/chatManage/ChatDTO";
import { DriverService } from "~/services/driverService/driverService";

const RequireLoginModal = defineAsyncComponent(() => import('~/components/_requireLoginComponent/RequireLoginComponent.vue'))

const { t, locale, locales, defaultLocale, setLocale } = useI18n();

const availableLocales = computed(() => {
	return locales.value.map(i => i.code)
})

var router = useRouter();
var route = useRoute();
const online = useOnline();

var authService = new AuthService();
var userService = new UserService();
var placeService = new PlaceService();
var categoryService = new CategoryService();
var businessTypeService = new BusinessTypeService();
var orderService = new OrderService();
var shopService = new ShopService();
var fcmService = new FCMService();
var imageService = new ImageService();
var interactionService = new InteractionService();
var chatService = new ChatService();
var mqttService = new MqttService();
var driverService = new DriverService();

var token = ref(null as any);
var userInfo = ref({} as any);
var isAuth = ref(false);
var loaded = ref(false);
var pressToExit = ref(false);
var showFooter = ref(true);
var myShopId = ref("" as any);
var isSupportedBrowser: any;
var newOrderAmount = ref(0);
var newOrderAgentShop = ref(false);
var orderCheckable = useState('order_check', () => { return true });
var isRefreshing = ref(true);

const device = useDevice();

const nuxtApp = useNuxtApp();
const loading = ref(false);
const error = useError();
// nuxtApp.hook("vue:error", (e) => {
// 	console.log("vueError", e)
// 	nuxtApp.$pwa?.updateServiceWorker()
// 	reloadPage()
// })

// nuxtApp.hook("app:error", (e) => {
// 	console.log("vueError", e)
// 	nuxtApp.$pwa?.updateServiceWorker()
// 	reloadPage()
// })
watch(() => [nuxtApp.$pwa?.needRefresh], (e) => {
	console.log("need update", e);
	nuxtApp.$pwa?.updateServiceWorker();
	reloadPage()
})
nuxtApp.hook("app:chunkError", (e) => {
	console.log("chunkError", e)
	nuxtApp.$pwa?.updateServiceWorker();
	reloadPage()
})
nuxtApp.hook("page:start", async () => {
	// console.log("APP NEED REFRESH", needRefresh);

	loading.value = true;

	// if (needRefresh && false) {
	// 	// nuxtApp.$pwa?.updateServiceWorker()
	// 	updateServiceWorker(true);
	// }
	// else {
	try {

		var lastVisit = localStorage.getItem('lastVisit');
		let valueLastVisit = moment(lastVisit).format('YYYY-MM-DD').valueOf();
		let valueCurrent = moment().format('YYYY-MM-DD').valueOf();
		if (!lastVisit || moment(valueLastVisit).isBefore(moment(valueCurrent))) {
			reloadPage()
		}

		localStorage.setItem('lastVisit', valueCurrent);


	} catch (error) {
		console.error('Error fetching version:', error);
	}
	// }
	// try {

	// 	var lastVisit = localStorage.getItem('lastVisit');
	// 	let valueLastVisit = moment(lastVisit).format('YYYY-MM-DD').valueOf();
	// 	let valueCurrent = moment().format('YYYY-MM-DD').valueOf();
	// 	if (!lastVisit || moment(valueLastVisit).isBefore(moment(valueCurrent))) {
	// 		reloadPage()
	// 	}

	// 	localStorage.setItem('lastVisit', valueCurrent);


	// } catch (error) {
	// 	console.error('Error fetching version:', error);
	// }
});
nuxtApp.hook("page:finish", async () => {
	loading.value = false;
});

let type = ref(null as any);

var checkTicket: any;
var refreshInterval: any;
const orderTicketEl = ref<HTMLElement | null>(null);
var orderTicketPosition = reactive({ x: 0, y: 0 });
var initialOrderTicketDragPosition = ref({ x: 0, y: 0 })
const threshold = 10;
var fixX = ref(0);
var browserLanguage = ref()

useDraggable(orderTicketEl, {
	pointerTypes: ['mouse', 'touch'],
	onStart: ({ x, y }) => {
		initialOrderTicketDragPosition.value.y = orderTicketPosition.y;
		orderTicketEl.value?.classList.add('none-transition');
	},
	onEnd: ({ x, y }) => {
		orderTicketPosition.x = fixX.value;

		const movedY = Math.abs(y - initialOrderTicketDragPosition.value.y)
		if (!y || movedY < threshold) {
			ticketDragging.value = false
		}
		else {
			ticketDragging.value = true
		}
		orderTicketEl.value?.classList.remove('none-transition');
		// handleDragStart(x, y) 
	},
	onMove({ x, y }) {
		orderTicketPosition.x = x;
		orderTicketPosition.y = y
	},
})

const audioRef = ref<HTMLElement>()
var ticketDragging = ref(false);

var needToUpdate = ref(false);
var isOnline = ref(true);
var showPopupOffline = ref(false);
var firstClick = ref(false);
var showCreatePasswordModal = ref(false);
var showNewDelivery = ref(false);
var showFirstPopupModal = ref(false);
var showSharePopup = ref(false);
var showChangePermissionModal = ref(false);
var showRequireLoginModal = ref(false);

var requireLoginRedirect = ref<any>(null);
var backOnClose = ref(false);
var currentShareData = ref("" as any);
var newDeliveryID = ref(null as any);

var showUserDisabledModal = ref(false);

var listChannel = ref<ChannelDTO[]>([]);
var webInApp = ref(false);
var windowInnerWidth = ref();

var userLocationWatcherId: any;

var user_latitude = useState<any>('user_latitude', () => { return null });
var user_longitude = useState<any>('user_longitude', () => { return null });

var lastPublishMoving = ref<any>(null);

onUnmounted(() => {

	clearInterval(refreshInterval);
})

onBeforeMount(async () => {
	setGoogleTags();
	browserLanguage.value = checkBrowserLanguage();

	// if (availableLocales.value.indexOf(browserLanguage.value) != -1) {
	// 	setLocale(browserLanguage.value ?? appConst.defaultLanguage);
	// }
	// let localLang = await localStorage.getItem(appConst.storageKey.language) as string;
	// if (locale.value == defaultLocale && localLang != defaultLocale) {
	// 	setLocale(localLang)
	// }
	// setLocale(localLang ? localLang : 'vi')
	// locale.value = localLang ? localLang : 'vi';

	// lắng nghe sự kiện các actions trong web-app để gửi qua native-app
	nuxtApp.$listen(appConst.event_key.send_request_to_app, (req: any) => {
		let message;
		switch (req.action) {
			case appConst.webToAppAction.requestNotification:
				message = {
					action: appConst.webToAppAction.requestNotification,
					data: true
				};
				sendMessageToApp(message)
				return;

			case appConst.webToAppAction.requestUserLocation:
				message = {
					action: appConst.webToAppAction.requestUserLocation,
					data: true
				};
				sendMessageToApp(message)
				return;
			case appConst.webToAppAction.logout:
				message = {
					action: appConst.webToAppAction.logout,
					data: true
				};
				sendMessageToApp(message)
				return;
			case appConst.webToAppAction.requestCheckType:
				message = {
					action: appConst.webToAppAction.requestCheckType,
					data: true
				};
				sendMessageToApp(message)
				return;
			case appConst.webToAppAction.share:
				message = {
					action: appConst.webToAppAction.share,
					data: req.data
				};
				sendMessageToApp(message)
				return;
			case appConst.webToAppAction.copyToClipboard:
				message = {
					action: appConst.webToAppAction.copyToClipboard,
					data: req.data
				};
				sendMessageToApp(message)
				return;
			case appConst.webToAppAction.setLanguage:
				message = {
					action: appConst.webToAppAction.setLanguage,
					data: req.data
				};
				sendMessageToApp(message)
				return;
			default:
				message = {
					action: req.action,
					data: req.data
				};
				sendMessageToApp(message)
		}
	});

	// lắng nghe sự kiện hiện/ẩn footer đối với từng component
	nuxtApp.$listen(appConst.event_key.show_footer, (req: any) => {
		showFooter.value = req
	});

	// lắng nghe nếu người dùng chưa có mật khẩu
	nuxtApp.$listen(appConst.event_key.check_password, async () => {
		let userInfo$ = await JSON.parse(localStorage.getItem(appConst.storageKey.userInfo) as string);
		if (userInfo$) {
			showCreatePasswordModal.value = userInfo$?.has_password == true ? false : true;
		}
	})

	// lắng nghe để mở pop-up chia sẻ
	nuxtApp.$listen(appConst.event_key.share, (e: any) => {
		currentShareData.value = e
		showSharePopup.value = true;
	});

	nuxtApp.$listen(appConst.event_key.change_permission, () => {
		if (webInApp) {
			showChangePermissionModal.value = true;
		}

	})

	// lắng nghe để mở pop-up đăng nhập trước khi thao tác
	nuxtApp.$listen(appConst.event_key.require_login, (req: any) => {
		console.log(req);
		showRequireLoginModal.value = true;
		requireLoginRedirect.value = req.redirect_url;
		backOnClose.value = req.back_on_close ?? false
	})

	nuxtApp.$listen(appConst.event_key.logout, async (req: any) => {
		console.log("log out");
		fcmService.refreshToken();
		localStorage.removeItem(appConst.storageKey.savedInfo);
		localStorage.removeItem(appConst.storageKey.cart);
		localStorage.removeItem(appConst.storageKey.confirm18Age);
		// localStorage.removeItem(appConst.storageKey.userLocation);
		localStorage.removeItem(appConst.storageKey.userInfo);
		localStorage.removeItem(appConst.storageKey.token);
		localStorage.removeItem(appConst.storageKey.language);
		localStorage.removeItem(appConst.storageKey.productRecent);
		localStorage.removeItem(appConst.storageKey.shopRecent);
		localStorage.removeItem(appConst.storageKey.radiusFilter);
		localStorage.removeItem(appConst.storageKey.stateRestore.HomeComponent);
		localStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);
		localStorage.removeItem(appConst.storageKey.stateRestore.AroundComponent);
		localStorage.removeItem(appConst.storageKey.stateRestore.MyShopComponent);
		localStorage.removeItem(appConst.storageKey.stateRestore.SearchComponent);		
		localStorage.removeItem(appConst.storageKey.stateRestore.ShopProductsComponent);
		nuxtApp.$emit(appConst.event_key.send_request_to_app, { action: appConst.webToAppAction.logout });
		nuxtApp.$emit(appConst.event_key.cart_change);
		nuxtApp.$emit(appConst.event_key.check_unread_message);
		mqttService.unsubscribe(userInfo.value.id);
		userInfo.value = null;
		newOrderAmount.value = 0;
		clearInterval(checkTicket);
		await clearNuxtState([
			'agent_shop_manage_search_str',
			'my_shop',
			'agent_list_shop_managed',
			'agent_list_shop_managed_temp',
			'agent_list_shop_managed_count',
			'list_shop_managed',
			'chat_manage_tab',
			'otp_cooldown_forget',
			'dashboard_scroll_position',
			'dashboard_filter_data',
			'dashboard_hot_deal',
			'dashboard_shop_interaction',
			'dashboard_product_nearby',
			'dashboard_product_new',
			'dashboard_product_new_count',
			'dashboard_sale_off',
			'dashboard_shop_nearby',
			'dashboard_suggest',
			'shop_manage_tab',
			'shop_data',
			'shop_categories',
			'shop_products',
			'shop_products_count',
			'search_text',
			'filter_category',
			'otp_cooldown',
			'reelsData',
			'currentIndex',
			'otp_cooldown_register',
			'login_protocol'
		])
		// Use replace instead of push to prevent navigation issues
		router.replace(appRoute.LoginComponent);
		// await loadData();
	})
	nuxtApp.$listen(appConst.event_key.login, async (req: any) => {
		console.log("log in");
		await clearNuxtState([
			'agent_shop_manage_search_str',
			'my_shop',
			'agent_list_shop_managed',
			'agent_list_shop_managed_temp',
			'agent_list_shop_managed_count',
			'list_shop_managed',
			'chat_manage_tab',
			'otp_cooldown_forget',
			'dashboard_scroll_position',
			'dashboard_filter_data',
			'dashboard_hot_deal',
			'dashboard_shop_interaction',
			'dashboard_product_nearby',
			'dashboard_product_new',
			'dashboard_product_new_count',
			'dashboard_sale_off',
			'dashboard_shop_nearby',
			'dashboard_suggest',
			'shop_manage_tab',
			'shop_data',
			'shop_categories',
			'shop_products',
			'shop_products_count',
			'search_text',
			'filter_category',
			'otp_cooldown',
			'reelsData',
			'currentIndex',
			'otp_cooldown_register',
			'login_protocol'
		])
		await loadData();
		checkNewOrdersInterval()
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.requestNotification,
		})
	})

	nuxtApp.$listen(appConst.event_key.request_listen_mqtt, (req: any) => {
		subscripeUserTopingMqtt()
	});

	nuxtApp.$listen(appConst.event_key.user_info_change, () => {
		checkAuth()
	});

	document.addEventListener('message', (mes: any) => {
		console.log(mes)
		let data = mes['data'];
		handleMessageFromApp(data);
	});

	nuxtApp.$emit(appConst.event_key.send_request_to_app, {
		action: appConst.webToAppAction.setLanguage,
		data: locale.value
	})

	isSupported().then(async value => {

		isSupportedBrowser = value;
		if (isSupportedBrowser) {
			if ("serviceWorker" in navigator) {
				const resetSW = () => {
					return new Promise((resolve) => {
						navigator.serviceWorker.getRegistrations().then(async (sw) => {
							console.log(sw)
							sw.forEach(async (isw) => {
								await isw.unregister()
							});
							resolve(true);
						})
					})
				}
				// await resetSW();
				navigator.serviceWorker
					// .register(`/firebase-messaging-sw.js?ver=${nuxtApp.$config.public.appVersion}`, { scope: '/' })
					.register(`/firebase-messaging-sw.js`, { scope: '/' })
					.then(async function (registration) {

						console.log("Registration successful");
						if (registration.installing) {
							console.log('Service worker installing');
						} else if (registration.waiting) {
							console.log('Service worker installed and waiting');
						} else if (registration.active) {
							console.log('Service worker active');
						}
						await fcmService.requestPermission();
						onMessage(nuxtApp.$messaging as any, async (payload: any) => {
							console.log(payload);
							checkDeliveryOrderMessage(payload.data)
							var notificationTitle = payload.data.title;
							var notificationOptions = {
								body: payload.data.body,
								icon: payload.data.icon,
								data: payload.data
							};
							if (!checkNewChatMessage(payload.data)) {
								const registration = await navigator.serviceWorker.ready;
								registration.showNotification(
									notificationTitle,
									notificationOptions
								)
								// var notification = new Notification(notificationTitle, notificationOptions);

								// let url: any;
								// let params: any;
								// notification.onclick = (event) => {
								// 	switch (payload.data.type) {

								// 		case appConst.notification_type.order_new:
								// 		case appConst.notification_type.order_failed:
								// 		case appConst.notification_type.order_cancel:
								// 			url = appRoute.MyShopOrderDetailComponent;
								// 			params = payload.data.url;
								// 			break;

								// 		case appConst.notification_type.order_status:
								// 			url = appRoute.MyOrderDetailComponent;
								// 			params = payload.data.url;
								// 			break;

								// 		case appConst.notification_type.agent_order_new:
								// 		case appConst.notification_type.agent_order_failed:
								// 		case appConst.notification_type.agent_order_cancel:
								// 			url = appRoute.AgentOrderDetailComponent;
								// 			params = payload.data.url;
								// 			break;

								// 		case appConst.notification_type.delivery_new:
								// 		case appConst.notification_type.delivery:
								// 			url = appRoute.DeliveryDetailComponent.replaceAll(':delivery_id', ':id');
								// 			params = payload.data.url;
								// 			break;

								// 		case appConst.notification_type.message_new:
								// 		case appConst.notification_type.mesage_new:
								// 			url = appRoute.ChatManageComponent;
								// 			params = payload.data.url;
								// 			break;

								// 		default:
								// 			url = appRoute.ProfileComponent;
								// 			params = null
								// 	}

								// 	router.push(url.replaceAll(':id', params));
								// 	// if (payload.data.type == 'order') {
								// 	// 	router.push(appRoute.MyShopOrderDetailComponent.replaceAll(":id", payload.data.url))
								// 	// }
								// 	// else router.push(appRoute.ProfileComponent)

								// 	notification.close();
								// }
							}

						});
					})
					.catch(function (err) {
						console.error("Service worker registration failed, error:", err);
					});
				// })
				// let sw = await navigator.serviceWorker.getRegistration('/firebase-messaging-sw.js');
				// sw?.addEventListener('updatefound', () => {
				// 	console.log("update found")
				// 	sw.update();
				// })
				// await navigator.serviceWorker.s ready



				navigator.serviceWorker.addEventListener('message', (event) => {
					if (event.data && event.data.type === 'new-notification') {
						// Trigger a custom event or handle the payload
						const payload = event.data.payload.data;
						checkDeliveryOrderMessage(payload);
						checkNewChatMessage(payload)
						// // You can trigger a custom event on the Nuxt app
						// const customEvent = new CustomEvent('sw-custom-event', { detail: payload });
						// window.dispatchEvent(customEvent);
					}
				});
			}

		}
	});
	window.addEventListener('resize', () => {
		setOrderTicketPositionToBottomRight();
		windowInnerWidth.value = window.innerWidth;
	})

	window.addEventListener('beforeunload', handleBeforeUnload);
	window.addEventListener('click', () => {
		if (!firstClick.value && isiOS()) {
			firstClick.value = true;
		}
	})
})

onMounted(async () => {
	nuxtApp.$emit(appConst.event_key.send_request_to_app, { action: appConst.webToAppAction.requestCheckType });

	checkNewOrdersInterval()
	setInterval(() => {
		if (isOnline.value == false && online.value == true) {
			reloadPage()
		}
		isOnline.value = online.value;
	}, 5000)

	setOrderTicketPositionToBottomRight();
	windowInnerWidth.value = window.innerWidth;
	console.log(user_latitude.value, user_longitude.value);
	await loadData();
	isRefreshing.value = false;
});
onBeforeUnmount(() => {
	if ("geolocation" in navigator) {
		navigator.geolocation.clearWatch(userLocationWatcherId);
	}
	window.removeEventListener('beforeunload', handleBeforeUnload);
});

watch(() => [user_latitude.value, user_longitude.value], () => {
	nuxtApp.$emit(appConst.event_key.user_moving, {
		latitude: user_latitude.value,
		longitude: user_longitude.value
	})
	if ((!lastPublishMoving.value || (Date.now() - lastPublishMoving.value > 5000))) {
		lastPublishMoving.value = Date.now();

		if (userInfo.value?.role_id == appConst.role_enum.driver && userInfo.value?.user_status == appConst.user_status.online) {
			updateOnlineStatus(appConst.driver_action.moving)
		}
	}

})

async function handleBeforeUnload(event: any) {
	clearInterval(checkTicket);
	// await updateOnlineStatus(appConst.driver_action.set_offline);

	// event.preventDefault();
	// event.returnValue = ''
}
function watchDeviceOrient() {
	if ('requestPermission' in DeviceOrientationEvent) {
		(DeviceOrientationEvent as any).requestPermission().then((response: any) => {
			if (response === "granted") {
				window.addEventListener("deviceorientation", (e: any) => {
					nuxtApp.$emit(appConst.event_key.device_rotated, e.webkitCompassHeading)
				}, true);
			}
		});
	}
}
async function loadData() {
	await performTimeConsumingTask();
}
function isiOS() {
	var Apple = device.isApple;
	var Mac = device.isMacOS;
	var iOS = device.isIos;
	return (Apple || Mac || iOS);
}
async function performTimeConsumingTask() {
	try {
		await checkAuth().then(() => {
			getDriverLocation();
		});
		await listCategories();
		await listProvince();
		await listBusinessType();

		// await checkCart();
	}
	catch {
		return false
	}
	finally {
		nuxtApp.$emit(appConst.event_key.app_data_loaded)
		return true
	}
}

async function listProvince() {
	let res = await placeService.provinces();
	appDataStartup.listProvince = res.body.data;

}

async function listCategories() {
	await categoryService.list(0, 100).then((res => {
		if (res.status == HttpStatusCode.Ok) {
			appDataStartup.listCategory = res.body.data;
		}

	}))
}
async function listBusinessType() {
	await businessTypeService.list().then((res => {
		if (res.status == HttpStatusCode.Ok) {
			appDataStartup.listBusinessType = res.body.data;
		}

	}))
}

async function checkAuth() {
	let token$ = await localStorage.getItem(appConst.storageKey.token);
	token.value = token$;
	let userInfo$;
	if (token.value) {
		await userService.profileInfo().then(res => {
			if (res.status == HttpStatusCode.Ok) {
				userInfo$ = res.data;
				localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(userInfo$));
				userInfo.value = userInfo$;
				isAuth.value = true;
				let indexBrowserLanguageTemp = availableLocales.value.indexOf(browserLanguage.value);
				if (indexBrowserLanguageTemp == -1) {
					browserLanguage.value = appConst.defaultLanguage;
				}
				setLocale(userInfo$.language ?? browserLanguage.value ?? appConst.defaultLanguage);
				nuxtApp.$emit(appConst.event_key.check_password);
				nuxtApp.$emit(appConst.event_key.request_listen_mqtt);
				// nuxtApp.$emit(appConst.event_key.request_listen_mqtt, {
				// 	member_id: userInfo.value.id,
				// 	member_type: member_type.user
				// })
				getMyShop();
			}
			else {
				localStorage.removeItem(appConst.storageKey.userInfo);
				localStorage.removeItem(appConst.storageKey.token);
				userInfo.value = null;
				isAuth.value = false;
				let indexBrowserLanguageTemp = availableLocales.value.indexOf(browserLanguage.value);
				if (indexBrowserLanguageTemp == -1) {
					browserLanguage.value = appConst.defaultLanguage;
				}
				setLocale(browserLanguage.value ?? appConst.defaultLanguage);
			}

		});
	}
	return;
}

async function handleMessageFromApp(message: any) {
	let value = message.value;
	switch (message.action) {
		case appConst.appToWebAction.setType:
			type.value = value;
			localStorage.setItem(appConst.storageKey.webInApp, JSON.stringify(value));
			webInApp.value = value;
			if (type.value?.appInstalledInfo < type.value?.appReleaseInfo?.version) {
				needToUpdate.value = true
			}
			// type.value = value ? true : false;
			// localStorage.setItem(appConst.storageKey.webInApp, JSON.stringify(type.value));
			// localStorage.setItem(appConst.storageKey.webInAppVersion, JSON.stringify({
			// 	appInstalledInfo: value.appInstalledInfo,
			// 	appReleaseInfo: value.appReleaseInfo
			// }));

			// this.latitude = value.latitude;
			// this.longitude = value.longitude;
			// this.leafletMap.setView([value.latitude, value.longitude], 13);
			// this.setLocationLeafletMarker(this.latitude, this.longitude);
			// if (!value.isFirstLoad) {
			//   this.leafletMapBoundChanges();
			// }
			return

		case appConst.appToWebAction.userLocation:
			// this.listProductOnMap = value.data;
			// this.showProductsLeaflet();
			// localStorage.setItem(appConst.storageKey.userLocation, JSON.stringify({
			// 	latitude_user: value.latitude_user,
			// 	longitude_user: value.longitude_user,
			// }))
			return

		case appConst.appToWebAction.notificationToken:
			let userInfoStr = await localStorage.getItem(
				appConst.storageKey.userInfo
			);
			let userInfo = JSON.parse(userInfoStr as string);

			let localToken = await localStorage.getItem(
				appConst.storageKey.token
			);
			localStorage.setItem(appConst.storageKey.fcmToken, value.token);
			if (userInfo != null) {
				let bodyFCMToken = {
					token: value.token,
					token_type: 2,
					user_id: userInfo.id,
				};
				let token = await authService.saveFCMToken(bodyFCMToken);
			}
			return;
		case appConst.appToWebAction.notiClick:
			let url;
			let params;
			let query;
			if (value.target_url) {
				router.push(`${value.target_url}`);
			}
			else {
				switch (value.type) {
					case appConst.notification_type.order_new:
					case appConst.notification_type.order_failed:
					case appConst.notification_type.order_cancel:
						url = appRoute.MyShopOrderDetailComponent;
						params = value.param.orderId;
						router.push(url.replaceAll(':id', params));
						break;

					case appConst.notification_type.order_status:
					case appConst.notification_type.order_status_ready_to_deliver:
					case appConst.notification_type.order_status_in_process:
					case appConst.notification_type.order_status_done:
					case appConst.notification_type.order_status_cancel:
						url = appRoute.MyOrderDetailComponent.replaceAll(':order_id', ':id');
						params = value.param.orderId;
						router.push(url.replaceAll(':id', params));
						break;

					case appConst.notification_type.agent_order_new:
					case appConst.notification_type.agent_order_failed:
					case appConst.notification_type.agent_order_cancel:
						url = appRoute.AgentOrderDetailComponent;
						params = value.param.orderId;
						router.push(url.replaceAll(':id', params));
						break;

					case appConst.notification_type.delivery_new:
					case appConst.notification_type.delivery:
						url = appRoute.DeliveryDetailComponent.replaceAll(':delivery_id', ':id');
						params = value.param.orderId;
						router.push(url.replaceAll(':id', params));
						break;

					case appConst.notification_type.message_new:
					case appConst.notification_type.mesage_new:
						url = appRoute.ChatManageComponent;
						query = value.param.chatId;
						router.push(`${url}?channel_id=${query}`);
						break;

					case appConst.notification_type.shop_message_new:
					case appConst.notification_type.shop_mesage_new:
						url = appRoute.MyShopChatManageComponent;
						query = value.param.chatId;
						router.push(`${url}&channel_id=${query}`);
						break;
					case appConst.notification_type.agent_message_new:
						url = appRoute.AgentShopChatManageComponent;

						let shopId = value.param.shopId;
						url = url.replaceAll(':shop_id', shopId);
						query = value.param.chatId;
						router.push(`${url}&channel_id=${query}`);
						break;
					default:
						url = appRoute.ProfileComponent;
						params = null;
						router.push(`${url}`);
				}
			}
			return
		case appConst.appToWebAction.newDeliveryNoti:
			checkDeliveryOrderMessage(value)
			return;
		case appConst.appToWebAction.loginWithSocial:
			loginWithSocial(value);
			return
		case appConst.appToWebAction.newChatMessage:
			// nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${value.param}`);
			nuxtApp.$emit(appConst.event_key.check_unread_message);
			return;
		case appConst.appToWebAction.goHome:
			router.push(appRoute.HomeComponent);
			return
		default:
			return;
	}
}

function sendMessageToApp(message: any) {
	(window["ReactNativeWebView" as any] || window).postMessage(JSON.stringify(message));
}
function checkNewOrdersInterval() {
	clearInterval(checkTicket);
	checkTicket = setInterval(async () => {
		let muteCheckTime = localStorage.getItem('mute_check_order') as string;
		// getMyShop();

		if (moment().valueOf().toString().localeCompare(muteCheckTime || '') >= 0) {
			checkNewOrders();
			orderCheckable.value = true;
			localStorage.removeItem('mute_check_order');
		}

		// if (muteCheckTime > moment().valueOf()) {
		// 	checkNewOrders();
		// }
	}, 10000)
}

function checkNewOrders() {
	if (myShopId.value?.length) {
		getMyShopNewOrder();
	}

	// getShopAgentNewOrder()
}

function muteCheckOrder(time: number) {
	orderCheckable.value = false;

	localStorage.setItem('mute_check_order', moment().add(time, 'milliseconds').valueOf().toString());
}
function getMyShop() {
	shopService.myShop().then(async res => {
		if (res.status && res.status == HttpStatusCode.Ok) {
			myShopId.value = res.body?.data?.id;
			// nuxtApp.$emit(appConst.event_key.request_listen_mqtt, {
			// 	member_id: myShopId.value,
			// 	member_type: member_type.shop
			// })
		}
		else {
			myShopId.value = null;
		}
	})
}
function getMyShopNewOrder() {
	orderService.orderByShopId(myShopId.value, 0, 20, appConst.order_status.waiting.value).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			newOrderAmount.value = res.body.data.count;
			if (newOrderAmount.value) {
				if (router.currentRoute.value.fullPath == (appRoute.ManageOrdersComponent + '#waiting')) {
					nuxtApp.$emit('refresh_order_manage')
				}
				// var sound = new Audio(notiSound);
				// sound.muted = true;
				// document.getElementById('sound_noti').play
				// sound.play();
				// document.getElementById('sound_noti')?.autoplay  = false;
				// (document.getElementById('sound_noti') as any).play();
				document.getElementById('auto_click')?.click();
				const beep = new Audio(notiSound);
				beep.autoplay = true;
				beep.load();

				beep.play();
			}
		}
		else {
			newOrderAmount.value = 0;
		}
	}).catch(err => {
		newOrderAmount.value = 0;
	})
}

function reloadPage() {
	// reloadNuxtApp()
	window.location.reload();
}

function gotoStore() {
	window.open(type.value?.appReleaseInfo?.url)
}

function checkShowFooter() {
	if (route.path.includes(appRoute.AroundComponent)) return true;
	if (route.path.includes(appRoute.HomeComponent)) return true;
	if (route.path.includes(appRoute.ProfileComponent)) return true;
	if (route.path.includes(appRoute.CartComponent)) return true;
	if (route.path.includes(appRoute.ChatManageComponent)) return true;
	// if (route.path.includes(appRoute.ReelsComponent)) return true;
	return false;
}
function checkDeliveryOrderMessage(data: any) {
	if (data.type == appConst.notification_type.delivery_new || data.type == appConst.notification_type.delivery) {
		showNewDelivery.value = true;
		newDeliveryID.value = data.url
	}
	else if (data.type == appConst.notification_type.delivery_cancel) {
		if (newDeliveryID.value == data.url) {
			showNewDelivery.value = false;
		}
	}
	else {
		showNewDelivery.value = false;
	}
}

function checkNewChatMessage(data: any) {
	// nuxtApp.$emit(appConst.event_key.new_chat_message, data.url)
	if ((
		data.type == appConst.notification_type.message_new
		|| data.type == appConst.notification_type.mesage_new
		|| data.type == appConst.notification_type.shop_message_new
		|| data.type == appConst.notification_type.shop_mesage_new
		|| data.type == appConst.notification_type.agent_message_new
	) && (route.path.includes(appRoute.ChatManageComponent))) {
		return true;
	}
	return false;
}

async function loginWithSocial(data: any) {
	let query = data.data;
	switch (data.type) {
		case appConst.provider_name.google:
			if (query) {
				let body = {
					id: query.id,
					name: query.name,
					email: query.email,
					picture: query.picture
				}
				let login = await authService.loginWithGoogle(body);
				if (login.status == HttpStatusCode.Ok) {
					toast.success(t('LoginComponent.dang_nhap_thanh_cong'));
					localStorage.setItem(appConst.storageKey.token, login.body["token"]);
					localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(login.body.data));
					// Alert.alert("đăng nhập thành công:");

					await fcmService.getFcmToken();
					setTimeout(() => {
						nuxtApp.$emit(appConst.event_key.login);
						router.replace(route.query?.redirect ? JSON.parse(route.query?.redirect as string) : appRoute.HomeComponent)
						// router.push(appRoute.HomeComponent)
					}, 1000);

					// props.navigation.popToTop();
				}
				else if (login.status == HttpStatusCode.Forbidden && login.message_code == "C_E_008") {
					showUserDisabledModal.value = true;
				}
				else {
					toast.error(t('LoginComponent.dang_nhap_that_bai'));
				}
			}
			else {
				toast.error(t('LoginComponent.dang_nhap_that_bai'));
			}
			break;
		case appConst.provider_name.apple:
			if (query) {
				let body = {
					id: query.id,
					name: query.name,
					email: query.email,
					picture: query.picture,
					identifyToken: query.identifyToken
				}
				let login = await authService.loginWithApple(body);
				if (login.status == HttpStatusCode.Ok) {
					toast.success(t('LoginComponent.dang_nhap_thanh_cong'));
					localStorage.setItem(appConst.storageKey.token, login.body["token"]);
					localStorage.setItem(appConst.storageKey.userInfo, JSON.stringify(login.body.data));
					// Alert.alert("đăng nhập thành công:");

					await fcmService.getFcmToken();
					setTimeout(() => {
						nuxtApp.$emit(appConst.event_key.login);
						// router.push(appRoute.HomeComponent)
						router.replace(route.query?.redirect ? JSON.parse(route.query?.redirect as string) : appRoute.HomeComponent)
					}, 1000);

					// props.navigation.popToTop();
				}
				else if (login.status == HttpStatusCode.Forbidden && login.message_code == "C_E_008") {
					showUserDisabledModal.value = true;
				}
				else {
					toast.error(t('LoginComponent.dang_nhap_that_bai'));
				}
			}
			else {
				toast.error(t('LoginComponent.dang_nhap_that_bai'));
			}
			break
	}

}

function setGoogleTags() {
	if (environment.production) {
		const script1 = document.createElement("script");
		script1.src = `https://www.googletagmanager.com/gtag/js?id=${environment.GoogleTagsId}`;
		script1.async = true;

		const script2 = document.createElement("script");
		script2.textContent = `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', '${environment.GoogleTagsId}');
        `

		document.head.appendChild(script1);
		document.head.appendChild(script2);
	}
}
function getListChatChannel(member_id: string, type: string) {
	return new Promise(async (resolve) => {
		chatService.listChannel(member_id, type == member_type.shop ? member_type.shop : member_type.user).then((listChannel) => {
			if (listChannel.status == HttpStatusCode.Ok) {
				resolve(listChannel.data);
			}
			else {
				resolve(null);
			}

		}).catch(() => {
			resolve(null);
		});

	})
}
function subscripeUserTopingMqtt() {
	console.log(userInfo.value.id)
	mqttService.subscribe(userInfo.value.id, async (e: any) => {
		console.log(e);
		if (e.mess?.type == appConst.notification_type.message_new
			|| e.mess?.type == appConst.notification_type.mesage_new
			|| e.mess?.type == appConst.notification_type.shop_message_new
			|| e.mess?.type == appConst.notification_type.shop_mesage_new
			|| e.mess?.type == appConst.notification_type.agent_message_new) {
			console.log(e.mess?.url)
			nuxtApp.$emit(appConst.event_key.check_unread_message);
		}

		if (e.mess?.type == appConst.notification_type.delivery || e.mess?.type == appConst.notification_type.delivery_new || e.mess?.type == appConst.notification_type.delivery_cancel) {
			nuxtApp.$emit(appConst.event_key.check_delivery_update, {
				delivery_id: e.mess?.url ?? null
			});
			checkDeliveryOrderMessage(e.mess)
		}
		if (e.mess?.type == 'new_notification') {
			nuxtApp.$emit(appConst.event_key.check_user_notification)
		}
	})
}
function subscripeChannelsMqtt(channels: ChannelDTO[]) {
	channels.forEach((channel: any) => {
		mqttService.subscribe(`remagan_chat/${channel.id}`, async (e: any) => {
			if (e.mess?.type == appConst.notification_type.message_new
				|| e.mess?.type == appConst.notification_type.mesage_new
				|| e.mess?.type == appConst.notification_type.shop_message_new
				|| e.mess?.type == appConst.notification_type.shop_mesage_new
				|| e.mess?.type == appConst.notification_type.agent_message_new) {
				console.log(e.mess?.url)
				// nuxtApp.$emit(`${appConst.event_key.new_chat_message}_${e.mess?.url}`)
				nuxtApp.$emit(appConst.event_key.check_unread_message);
			}
		})
	});
}

const setOrderTicketPositionToBottomRight = () => {
	let container = document.getElementById('container-root');
	const containerHeight = container?.getBoundingClientRect().height ?? 0;
	const containerWidth = container?.getBoundingClientRect().width ?? 0;
	orderTicketPosition.x = containerWidth - 80 // Adjust based on button width
	orderTicketPosition.y = containerHeight - 255// Adjust based on button height
	fixX.value = orderTicketPosition.x;
}

const checkBrowserLanguage = () => {
	if (process.client) {
		// Accessing browser language in client-side
		let lang = navigator.language || navigator.languages[0];
		return lang.split('-')[0];
	}
	return appConst.defaultLanguage;
}

function checkWindowWidthMax(width: any) {
	if (windowInnerWidth.value <= width) return true;
	return false;
}

function getDriverLocation() {
	if ("geolocation" in navigator) {
		userLocationWatcherId = navigator.geolocation.watchPosition(
			async (position) => {
				// console.log(position);
				user_latitude.value = position.coords.latitude;
				user_longitude.value = position.coords.longitude;
				// setLocationLeafletMarker(position.coords.latitude, position.coords.longitude);
			},
			async (error) => {
				user_latitude.value = appConst.defaultCoordinate.latitude;
				user_longitude.value = appConst.defaultCoordinate.longitude;
			},
			{
				enableHighAccuracy: true,
			}
		);
	}
}

function updateOnlineStatus(action: any) {
	return new Promise(async (resolve) => {
		if (isAuth.value && userInfo.value?.role_id == appConst.role_enum.driver) {
			let body = {
				action: action,
				latitude: user_latitude.value,
				longitude: user_longitude.value,
			}
			driverService.driverAction(body).then(() => {
				resolve(true);
			})
		}
		else {
			resolve(false);
		}
	})

}
</script>