.update-holiday-setting-container {
  height: 95dvh;
  max-height: 95dvh;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  overflow: hidden;
  background-color: white;
  position: relative;

  & h3 {
    text-transform: none;
  }

  &>.update-holiday-setting-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;

    &>#days_off_container {
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 10px;
      flex: 1;
    }

    & .holiday-list-container {
      display: flex;
      flex-direction: column;
      flex: 1;

      &.none-list {
        justify-content: center;
        align-items: center;
        min-height: 200px;
        gap: 10px;
      }

      &>.holiday-item {
        display: flex;
        flex-direction: column;
        border-radius: 7px;
        background: #f5f6fa;
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;

        &>.holiday-item-info {
          padding: 5px;
          animation: none;
          background: white;
          display: flex;
          overflow: hidden;
          gap: 5px;
          border-radius: 7px 7px 0 0;
          align-items: center;

          &>.hoilday-text {
            flex: 1;
            color: var(--primary-color-1);
            font-size: 17px;
            font-weight: 700;
            text-transform: none;
          }

          & .holiday-picker {
            width: 100%;
          }

          &>.delete-holiday-item {
            color: var(--primary-color-2);
            width: 30px;
            min-width: unset;
            height: 30px;
            overflow: hidden;
            border-radius: 50%;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
          }
        }

        &>.holiday-item-reason {
          display: flex;
          flex-direction: column;
          padding: 10px;

          &>.reason-item {
            display: flex;
            align-items: flex-start;
            margin: 5px 0;

            &>label {
              color: #868686;
              font-size: 13px;
              font-weight: 600;
              width: 20%;
            }
          }

          & > .text-area-custom{
            width: 80%;
          }
        }
      }
    }

    & .add-holiday {
      color: var(--primary-color-1);
      height: 30px;
      text-transform: none;
    }

    & .custom-input,
    .text-area-custom {
      background-color: white;
      border-radius: 5px;
      border: none;
      outline: none;
      color: #545454;
      font-weight: 400;
      text-overflow: ellipsis;
      overflow: hidden;
      display: flex;
      padding: 0;
      font-size: 13px;
      flex: 1;
      margin-left: 10px;

      &>textarea {
        width: 100%;
        padding: 10px;
        outline: none;
        height: 100% !important;
        resize: none;
      }

      &:disabled {
        background: #f5f6fa;
      }

    }
  }

  &>.action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;
    user-select: none;

    &>button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }

    &>.cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }

    &>.save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    &>.cancel-button:disabled {
      // color: var(--color-text-note);
      // border-color: var(--color-text-note);
      opacity: 0.5;
    }

    &>.save-button:disabled {
      // background: #ccc;
      // color: var(--primary-color-1);
      // border-color: #ccc;
      opacity: 0.5;
    }
  }
}