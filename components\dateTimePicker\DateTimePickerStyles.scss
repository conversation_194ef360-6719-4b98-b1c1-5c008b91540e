.date-time-picker-overlay-container {
  overflow: hidden;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }
    100% {
      bottom: 0;
    }
  }
  .date-time-picker-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: fit-content;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    height: 50vh;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    max-width: var(--max-width-content-view-1024) !important;

    & > .title-header {
      border-radius: inherit;
    }

    & > .date-time-picker-content-container {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;

      & > .picker-content {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 49%;
        height: 100%;
        overflow: hidden;
        padding: 0 15px;

        & > .picker-buttons-carousel {
          height: 50px;
          width: 100%;
          overflow: visible;
          border: thin solid #ccc;
          border-left: none;
          border-right: none;

          & .picker-item {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all .2s ease-in-out;
            font-size: 1em;
            user-select: none;
          }

          & .picker-item.swiper-slide-active{
            font-size: 1.3em;
            color: var(--primary-color-1);
            font-weight: bold;
          }
        }
      }
    }

    & > .footer-actions{
      padding: 5px 10px;
      width: 100%;

      & > .submit-button{
        padding: 5px;
        background-color: var(--primary-color-1);
        color: white;
        width: 100%;
        border-radius: 7px;
      }
    }
  }
}
