<template>
    <div class="request-quotation-container">
        <SubHeaderV2Component :title="$t('RequestQuotationComponent.tao_yeu_cau_bao_gia')">
            <!-- <template v-slot:header_left>
                <button class="back-button" v-on:click="() => {
                    close();
                }">
                    <Icon name="solar:round-alt-arrow-left-linear"></Icon>
                </button>
            </template> -->
        </SubHeaderV2Component>
        <div class="request-quotation-content-container">
            <v-row class="header-request">
                <v-col cols="12" md="4" lg="4" sm="12" class="information">
                    <input type="text" class="quotation-detail-input" :class="{
                        'error': nameErr,
                        'animation': runErrAnimation
                    }" :placeholder="$t('RequestQuotationComponent.tieu_de')" v-model="name"
                        v-on:input="validateName()" />
                    <input type="text" class="quotation-detail-input" v-show="expandHeader"
                        :placeholder="$t('RequestQuotationComponent.ghi_chu')" v-model="notes" />
                </v-col>
                <v-col cols="12" md="2" lg="2" sm="12" class="expiration" v-show="expandHeader">
                    <div class="select-expire-date">
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained
                            v-model="menuFrom" :closeOnContentClick="false" key="expire_from">
                            <template v-slot:activator="{ props }">
                                <button class="select-date" v-bind="props" :class="{
                                    'error': fromErr,
                                    'animation': runErrAnimation
                                }">
                                    {{ $t('RequestQuotationComponent.tu') }}: {{ moment(from).isValid() ?
                                        moment(from).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :max="to" show-adjacent-months v-model:modelValue="from"
                                v-on:update:model-value="() => {
                                    validateFrom();
                                    menuFrom = false;
                                }"></v-date-picker>
                        </v-menu>
                        <v-menu class="bootstrap-dropdown-container" location="bottom right" contained v-model="menuTo"
                            :closeOnContentClick="false">
                            <template v-slot:activator="{ props }" key="expire_to">
                                <button class="select-date" v-bind="props" :class="{
                                    'error': toErr,
                                    'animation': runErrAnimation
                                }">
                                    {{ $t('RequestQuotationComponent.den') }}: {{ moment(to).isValid() ?
                                        moment(to).format("DD/MM/YYYY") : "--/--/----" }}
                                </button>
                            </template>
                            <v-date-picker :min="from" show-adjacent-months v-model:modelValue="to"
                                v-on:update:model-value="() => {
                                    validateTo();
                                    menuTo = false;
                                }"></v-date-picker>
                        </v-menu>
                    </div>
                </v-col>
                <v-col cols="12" md="4" lg="4" sm="12" class="actions">
                    <!-- <label class="select-supplier-label">{{ $t('RequestQuotationComponent.nha_cung_cap') }}</label> -->
                    <div class="supplier-group">
                        <v-autocomplete label="" chips clearable closable-chips multiple
                            class="custom-v-select supplier-select" :menu-props="{
                                contentClass: 'text-center'
                            }" v-on:update:model-value="(e) => {
                                suppliers = e;
                                validateSupplier()
                            }" :placeholder="$t('RequestQuotationComponent.nha_cung_cap')" v-model="suppliers"
                            :item-value="'id'" :item-title="'name'" :menu-icon="''" :items="data_supplier"
                            variant="plain" :class="{
                                'error': supplierErr,
                                'animation': runErrAnimation
                            }">
                            <!-- <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :title="item.value.name"></v-list-item>
                            </template> -->
                            <template v-slot:chip="{ props, item }">
                                <v-chip v-bind="props" class="chip-supplier">{{ item.title }}</v-chip>
                            </template>
                            <template v-slot:append-inner></template>
                        </v-autocomplete>
                        <v-btn class="add-supplier" :title="$t('RequestQuotationComponent.them_nha_cung_cap')"
                            v-on:click="() => {
                                showAddSupplierModal = true;
                            }">
                            <Icon name="typcn:plus"></Icon>
                        </v-btn>
                    </div>
                </v-col>
                <v-col cols="12" md="2" lg="2" sm="12" class="actions">
                    <v-btn class="save-quotation" variant="outlined" :loading="saving"
                        :disabled="saving || !material_ids?.length" v-on:click="() => {
                            console.log(materials_to_request);
                            submitRequestQuotation();
                        }">
                        {{
                            $t('RequestQuotationComponent.tao')
                        }}
                    </v-btn>
                </v-col>
            </v-row>
            <v-btn class="expand-header-request" v-on:click="() => {
                collapseHeader()
            }">{{ expandHeader ? $t('RequestQuotationComponent.thu_gon') : $t('RequestQuotationComponent.mo_rong')
            }}</v-btn>
            <v-row class="material">
                <label for="select_material" class="select-material-label">{{ t('RequestQuotationComponent.nguyen_lieu')
                }}</label>
                <v-autocomplete label="" :filter-mode="'intersection'" class="custom-v-select material-select" clearable
                    chips multiple closable-chips :menu-props="{
                        contentClass: 'text-center'
                    }" id="select_material" v-on:update:model-value="($event) => {
                        material_ids = $event;
                        fillMaterial()
                    }" :placeholder="$t('RequestQuotationComponent.chon_nguyen_lieu')" v-model="material_ids"
                    :item-value="'id'" :menu-icon="''" :items="data_material" variant="plain" :custom-filter="(item: any, queryText: any, itemObj: any) => {
                        let name = nonAccentVietnamese(item).toLocaleLowerCase();
                        let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
                        return name.includes(query)
                    }" :item-title="'name'">
                    <template v-slot:chip="{ props, item }">
                        <v-chip v-bind="props" class="chip-material">{{ item.title }}</v-chip>
                    </template>
                    <template v-slot:append-inner></template>
                </v-autocomplete>
            </v-row>

            <v-data-table class="data-request" v-if="material_ids?.length" v-model:page="current_data_page"
                v-model:items-per-page="item_per_page" :headers="[
                    ...Object.keys(data_field_structor).map((key, index) => {
                        return {
                            value: key,
                            title: t(`RequestQuotationComponent.${(data_field_structor as any)?.[key].label_key}`),
                        }
                    })
                ]" :items="materials_to_request">
                <template v-slot:headers="{ columns }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="head-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :class="{
                                    'short': column.value == data_field_structor.stt.label_key,
                                    'auto': column.value == 'action'
                                }">
                                <span class="cursor-pointer">{{ column.title }}</span>
                            </th>
                        </template>
                    </tr>
                </template>
                <template v-slot:item="{ item, columns, index }">
                    <tr>
                        <template v-for="column in columns" :key="column.key">
                            <th class="body-col stt" v-if="column.value == data_field_structor.stt?.label_key"
                                :id="`stt_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`">
                                <span>{{ index + (current_data_page - 1) * item_per_page + 1 }}</span>
                            </th>
                            <th class="body-col notes" v-else-if="column.value == 'notes'">
                                <UTextarea autoresize resize :maxrows="5" :rows="1" type="text"
                                    :placeholder="t('RequestQuotationComponent.ghi_chu_cho_nguyen_lieu')"
                                    class="quotation-detail-input" v-on:input="($event: any) => {
                                        (item as any)[column.key ?? ''] = $event.target.value;
                                    }" :value="(item as any)?.[column.key ?? '']" />
                            </th>
                            <th class="body-col"
                                :style="{ 'min-width': (data_field_structor as any)?.[(column.key as any)]?.width + 'px' }"
                                :id="`body_long_${column.value}_${index + (current_data_page - 1) * item_per_page + 1}`"
                                :class="{
                                    'long-text': ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name?.label_key)
                                        || ((data_field_structor as any)?.[(column.value as any)]?.label_key)?.includes(data_field_structor.name_en?.label_key)
                                }" v-else>
                                <span>{{ (item as any)[column.key ?? ''] }}</span>
                            </th>

                        </template>
                    </tr>
                </template>
            </v-data-table>
            <div class="none-data" v-else>
                <img :src="none_list_quotations" alt="">
                {{ $t('RequestQuotationComponent.chua_co_du_lieu') }}
            </div>
        </div>
    </div>

    <AddSupplierComponent v-if="showAddSupplierModal" v-on:close="() => {
        showAddSupplierModal = false;
    }" v-on:submit="() => {
        showAddSupplierModal = false;
        getListSupplier();
    }"></AddSupplierComponent>

    <ShareLinkToSupplierComponent 
        :data="JSON.parse(JSON.stringify(result_quotation))" 
        v-if="showShareLinkModal"
        v-on:close="()=>{
            showShareLinkModal = false;
            close();
        }"
    ></ShareLinkToSupplierComponent>
</template>

<script lang="ts" setup>
import no_data_found from "~/assets/imageV2/nodata-found.png"
import none_list_quotations from "~/assets/image/list-empty-2.jpg"
import { domainImage, appConst, nonAccentVietnamese, validPhone } from "~/assets/AppConst";
import { appRoute } from "~/assets/appRoute";
import { AuthService } from "~/services/authService/authService";
import { ImageService } from "~/services/imageService/imageService";
import { PlaceService } from "~/services/placeService/placeService";
import { UserService } from "~/services/userService/userService";
import moment from "moment";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { HttpStatusCode } from "axios";
import { QuotationService } from "~/services/quotationService/quotationService";
import { ShopService } from "~/services/shopService/shopService";
import { DataFieldStructor, type MaterioDto, type SupplierDto } from "../QuotationDTO";
import { SupplierService } from "~/services/quotationService/supplierService";
import { MaterialService } from "~/services/quotationService/materialService";

var router = useRouter();
var route = useRoute();
const nuxtApp = useNuxtApp();
const { t, setLocale } = useI18n()

var quotationService = new QuotationService();
var supplierService = new SupplierService();
var materialService = new MaterialService();
var shopService = new ShopService();

var data_field_structor = ref<DataFieldStructor>(new DataFieldStructor());

var name = ref<string>("");
var nameErr = ref(false);
var notes = ref<string>("");
var from = ref<string | undefined | null>(null);
var fromErr = ref(false);
var to = ref<string | undefined | null>(null);
var toErr = ref(false);
var suppliers = ref<any[]>([]);
var supplierErr = ref(false);

var runErrAnimation = ref(false);

var data_supplier = ref<SupplierDto[]>([]);
var data_material = ref<MaterioDto[]>([]);
var expandHeader = ref(true);

var menuFrom = ref(false);
var menuTo = ref(false);

var material_ids = ref<any>([]);
var materials_to_request = ref<MaterioDto[]>([]);

var current_data_page = ref(1);
var item_per_page = ref(10);

var saving = ref(false);
var showAddSupplierModal = ref(false);
var showShareLinkModal = ref(false);

var result_quotation = ref<any>(null);
onMounted(async () => {
    window.addEventListener('resize', () => {
        if (window.innerWidth > 961) {
            expandHeader.value = true;
        }
    })
    getListMaterial();
    await getListSupplier();

    Object.keys(data_field_structor.value).map(async (key: string) => {
        (data_field_structor.value as any)[key].value = key;
    });

    delete data_field_structor.value.material_id;
    delete data_field_structor.value.price;
    delete data_field_structor.value.price_vat;

    console.log(data_field_structor.value)

    useSeoMeta({
        title: t('RequestQuotationComponent.tao_yeu_cau_bao_gia')
    })
})

onBeforeUnmount(() => {
});

function getListSupplier() {
    return new Promise((resolve) => {
        supplierService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_supplier.value = JSON.parse(JSON.stringify(res.body.data))
            }
            resolve(data_supplier.value);
        }).catch(() => {
            toast.error(t('RequestQuotationComponent.lay_danh_sach_nha_cung_cap_that_bai'))
        })
    })

}

function getListMaterial() {
    return new Promise((resolve) => {
        materialService.list().then(res => {
            if (res.status == HttpStatusCode.Ok) {
                data_material.value = JSON.parse(JSON.stringify(res.body.data));
            }
            resolve(data_material.value);
        })
    })

}

function collapseHeader() {
    if (window.innerWidth > 961) {
        expandHeader.value = true;
    }
    else {
        expandHeader.value = !expandHeader.value
    }
}

function validateName() {
    if (name.value?.length) {
        nameErr.value = false;
        return true;
    }
    else {
        nameErr.value = true;
        return false;
    }
}

function validateFrom() {
    console.log(from.value, moment(from.value).isValid())
    if (from.value && moment(from.value).isValid()) {
        fromErr.value = false;
        return true;
    }
    else {
        fromErr.value = true;
        return false;
    }
}

function validateTo() {
    if (to.value && moment(to.value).isValid()) {
        toErr.value = false;
        return true;
    }
    else {
        toErr.value = true;
        return false;
    }
}
function validateSupplier() {
    console.log(suppliers.value)
    if (suppliers.value?.length) {
        supplierErr.value = false;
        return true;
    }
    else {
        supplierErr.value = true;
        return false;
    }
}
function fillMaterial() {
    materials_to_request.value = data_material.value.filter((e) => {
        return material_ids.value.indexOf(e.id) != -1;
    })
    console.log(materials_to_request.value)
}

async function submitRequestQuotation() {
    await validateName();
    await validateFrom();
    await validateTo();
    await validateSupplier();
    console.log(nameErr.value, fromErr.value, toErr.value, supplierErr.value);
    if (nameErr.value || fromErr.value || toErr.value || supplierErr.value) {
        runErrAnimation.value = true;
        expandHeader.value = true;
        setTimeout(() => {
            runErrAnimation.value = false;
        }, 1000);
    }
    else {
        saving.value = true;
        materials_to_request.value.map((item: any) => {
            item.material_id = item.id;
            item.notes = item.notes ? item.notes : ""
        });

        quotationService.request(
            suppliers.value,
            name.value,
            from.value,
            to.value,
            notes.value,
            "",
            materials_to_request.value
        ).then((res)=>{
            if(res.status == HttpStatusCode.Ok){
                toast.success(t('RequestQuotationComponent.tao_yeu_cau_bao_gia_thanh_cong'));
                result_quotation.value = JSON.parse(JSON.stringify(res.body.data))
                result_quotation.value.map((item:any)=>{
                    let indexSup = data_supplier.value.findIndex(function(e){
                        return e.id == item.supplier_id
                    })
                    item.supplier = {...data_supplier.value[indexSup]} 
                });

                showShareLinkModal.value = true;
            }
            else{
                toast.error(t('RequestQuotationComponent.tao_yeu_cau_bao_gia_that_bai'));
            }
            saving.value = false;
        }).catch(()=>{
            toast.error(t('RequestQuotationComponent.tao_yeu_cau_bao_gia_that_bai'));
            saving.value = false;
        })
    }
}

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.QuotationComponent)
}
</script>

<style lang="scss" src="./RequestQuotationStyles.scss"></style>
