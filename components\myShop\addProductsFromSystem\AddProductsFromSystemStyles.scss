.add-products-from-system-modal {
  padding: 0 !important;
  min-height: 90dvh !important;
  max-height: 90dvh !important;
  min-height: 88vh;
  max-height: 88vh;
}

.add-products-from-system-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  border-radius: inherit;
  background: white;
  // position: relative;
  // overflow: auto !important;
  // height: 100dvh !important;

  // & .title-header {
  //   border-radius: 10px 10px 0 0;
  //   // font-size: 1.6em;
  // }

  &>.add-from-system-sticky-header {
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    z-index: 1000;

    &>.sub-title-v2-header {
      text-transform: uppercase;
      position: relative;
    }
  }

  & .search-bar-container {
    align-items: center;
    position: relative;
    height: 40px;
    min-height: 40px;
    z-index: 99;
    margin-bottom: 5px;
  }

  & .search-input-container {
    width: 100%;
    background-color: #f5f6fa;
    border-radius: 0 !important;
    border-color: transparent;
    outline: none;
    height: 100%;
    font-size: 15px;
  }

  & .search-button {
    display: flex;
    align-items: center;
    background: #f5f6fa;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    padding: 0 10px;
    color: var(--color-text-note);
    height: 100%;
    border: none;
  }

  .search-result {
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    z-index: 2;
    overflow: auto;
    height: 100%;
    width: 100%;
    display: none;
    flex-direction: column;
    background: white;
    padding: 0 10px;
    border-radius: 10px;

    /* gap: 10px; */
    & .close-search-button {
      display: flex;
      position: sticky;
      top: 0;
      left: 0;
      gap: 10px;
      justify-content: center;
      align-items: center;
      border: none;
      background: var(--primary-color-1);
      color: white;
      font-weight: 500;
      padding: 5px;
      border-radius: 2em;
      box-shadow: 0 0 5px rgb(0, 0, 0, 20%);

      & .close-icon {
        font-size: 1.5em;
      }
    }

    & img.none-search-result {
      margin: 10px auto;
      justify-content: center;
      border-radius: 50%;
      width: 250px;
      height: 250px;
      flex: 1;
      object-fit: contain;
    }

    & .select-all {
      display: flex;
      justify-content: center;
      gap: 10px;
      font-size: 1em;
      font-weight: bold;
      padding: 5px;
      cursor: pointer;

      & svg {
        width: 20px;
        min-width: 20px;
        font-size: 20px;
      }
    }

    & .search-result-item-container {
      display: flex;
      gap: 10px;
      border: none;
      border-bottom: thin solid var(--color-background-2);
      padding: 5px;
      background: white;
      width: 100%;
      cursor: pointer;
      user-select: none;

      & img {
        border: none;
        border-radius: 10px;
        width: 25%;
        width: 100px;
        height: 100px;
        aspect-ratio: 1;
        background-color: var(--color-background-2);
        object-fit: cover;
      }

      & .v-stack {
        flex: 1;
      }

      & span {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        text-align: left;
      }

      & span.price {
        color: var(--color-text-price);
      }

      & .my-checkbox {
        width: 1.5em;
      }
    }

    & .search-footer {
      position: sticky;
      bottom: 0;
      left: 0;
      padding: 5px 0;
      border-top: thin solid #ccc;
      padding: 10px;
      background: white;
      display: flex;
      gap: 5px;
      align-items: center;
      margin-top: auto;

      & span {
        margin-right: auto;
        font-weight: 600;
      }

      & .unselect-all-button {
        display: flex;
        align-items: center;
        margin-left: auto;
        background: white;
        border: thin solid var(--primary-color-2);
        color: var(--primary-color-2);
        font-weight: 600;
        padding: 10px;
        font-size: 1em;
        height: 35px;
        border-radius: 7px;
      }

      & .accept-button {
        font-size: 1em;
        width: fit-content;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        border-radius: 7px;
      }
    }
  }

  .search-result.show {
    display: flex;
  }

  .selected-products {
    display: none;
  }

  .selected-products.show {
    display: flex;
    flex: 1;
    overflow: auto;

    .none-selected {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;

      & img {
        border-radius: 50%;
        height: 250px;
        width: 250px;
        object-fit: contain;
      }

      & span {
        font-size: 1.5em;
        color: var(--color-text-note);
      }
    }
  }

  .selected-products-container {
    display: flex;
    width: 100%;
    flex: 1;
    position: relative;
    flex-direction: column;
    padding: 0;
    // overflow: auto;

    & .list-product-selected {
      flex: 1;
      gap: 10px;
      display: flex;
      flex-direction: column;
    }

    & .item-product-seleted.odd {
      background: #f5f6fa;
    }

    & .item-product-seleted {
      padding: 10px;
      gap: 10px;

      & img {
        width: 25%;
        aspect-ratio: 1;
        width: 100px;
        height: 100px;
        margin-right: 5px;
        align-self: flex-start;
      }

      & .v-stack {
        flex: 1;
      }

      & .product-name {
        font-weight: 600;
        color: var(--primary-color-1);
      }

      & .product-price {
        font-size: 1.2em;
        color: var(--color-text-price);
      }

      & .input-custom,
      .text-area-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
      }

      & .input-custom:disabled,
      .text-area-custom:disabled {
        background: #fafafa;
      }

      & .price-text.notice {
        color: #00823e;
      }
    }

    & .selected-footer {
      display: flex;
      justify-content: space-evenly;
      padding: 10px 0;
      border-top: thin solid #ccc;
      position: sticky;
      bottom: 0;
      background: white;
    }
  }

  .selected-products-container.show-search {
    overflow: hidden;
  }

  & .loading-more {
    background-color: white;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }
}