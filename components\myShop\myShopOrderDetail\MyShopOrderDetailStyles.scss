.my-shop-order-detail-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  z-index: 10;
  max-width: var(--max-width-content-view-1024) !important;
  // overflow: auto;

  &>.title-header {
    // font-size: 1.3em;
    // padding: 5px 0;
    // margin: 0;
    // text-align: center;
    // width: 100%;
    // border-bottom: none;
    background-color: var(--primary-color-1);
    color: white;
    // display: flex;
    // justify-content: center;
    // // font-size: 1.6em;
    // align-items: center;

    // & button {
    //     background: inherit;
    // }

    // & h3 {
    //     margin: 0;
    // }
  }

  & .main-stack {
    width: 100%;
    padding: 15px;
    background-color: white;
    margin-bottom: 15px;
    color: #313036;

    &>.first-content {
      justify-content: space-between;
      align-items: flex-start;
      gap: 5px;
      flex-wrap: wrap;

      &>.title-main-stack {
        gap: 10px;
        display: flex;
        align-items: center;
        font-weight: bold;
        align-self: baseline;
        white-space: nowrap;

        &>svg {
          font-size: 20px;
          color: var(--primary-color-1);
          // align-self: baseline;
        }

        & em {
          color: var(--primary-color-1);
          font-size: 14px;
          font-weight: 500;
        }
      }

      &>.action.copy {
        text-transform: uppercase;
      }

      & .action {
        gap: 5px;
        justify-content: flex-end;
        margin-left: auto;
        margin-bottom: 5px;
        flex-wrap: wrap;
      }

      & .call-customer {
        color: #5f5e66;
        font-size: 15px;
        padding: 2px 7px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        border: thin solid #ecebf3;
        font-weight: 600;
        white-space: nowrap;

        &>svg {
          width: 20px;
        }

        &>img {
          border-radius: 50%;
          border: thin solid #ecebf3;
          width: 20px;
        }
      }
    }

    &>.primary-content {
      display: flex;
      padding: 0 0 0 30px;
      align-items: flex-start;
      gap: 5px;

      &>.content-detail {
        display: flex;
        flex-direction: column;
        font-weight: 600;
        color: #828187;
        white-space: break-spaces;

        &>span.status-name {
          display: list-item;
        }

        &>span.status-name::marker {
          color: var(--primary-color-1);
          font-size: 18px;
        }

        &>.driver-location {
          color: white;
          font-weight: 700;
          align-self: flex-start;
          background: var(--primary-color-1);
          border-radius: 2em;
          padding: 0 10px;
        }
      }

      & .customer-name {
        color: #313036;
        font-weight: bold;

        & > .open-info{
          margin-left: 5px;
          font-weight: 500;
          color: var(--primary-color-1);
          font-style: italic;
        }
      }

      & .customer-address>.direction {
        color: var(--primary-color-1);
        margin-left: 5px;
        font-weight: 700;
        font-style: italic;
      }

      & .customer-delivery-time {
        font-weight: 400;

        & em {
          color: var(--primary-color-1);
          font-weight: bold;
        }
      }
    }

    &>.primary-content.images-content {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
      justify-content: center;
      background: white;
      padding: 0;
      margin: 15px 0 0;
      border-radius: 10px;

      &>.select-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: 2px solid #e7e9ec;
        color: #e7e9ec;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        cursor: pointer;

        &>label {
          width: 100%;
          height: 100%;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;

          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: -1;
          }
        }
      }

      & .selected-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: thin solid #e7e9ec;
        color: #e7e9ec;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        position: relative;
        cursor: pointer;

        &>img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: inherit;
        }

        &>.action-overlay {
          position: absolute;
          bottom: 0;
          right: 0;
          font-size: 20px;
          display: flex;
          color: white;
          justify-content: flex-end;
          align-items: flex-end;
          gap: 5px;
          background-color: rgb(0, 0, 0, 0.5);
          border-radius: 10px 0;

          &>button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      & .selected-image:hover {
        &>.action-overlay {
          display: flex;
        }
      }
    }

    &>.primary-content.images-content.none-images {
      justify-content: flex-start;
      margin: 0;
      padding-left: 25px;
      color: #828187;
    }

    &>.images-content-actions {
      gap: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 15px;

      &>button {
        border-radius: 2em;
        border: thin solid;
        padding: 0 15px;
        min-width: 100px;
      }

      &>button.undo {
        background: white;
        font-weight: 600;
        color: #828187;
      }

      &>button.save-images {
        background: var(--primary-color-1);
        border: thin solid var(--primary-color-1);
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
      }
    }

    & .delivery-content {
      display: flex;
      flex-direction: column;
      margin: 10px 0;
      flex-wrap: wrap;

      &>.label-content {
        color: var(--primary-color-1);
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        flex: 1;

        &>button {
          color: #5f5e66;
          font-size: 15px;
          padding: 2px 7px;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          border: thin solid #ecebf3;
          font-weight: 600;
          white-space: nowrap;
        }
      }

      &>.content-detail.delivery-detail {
        flex-direction: column;
      }

      &>.content-detail {
        justify-content: flex-start;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        width: 100%;

        &>.user-detail {
          padding: 0;
          display: flex;
          flex-direction: column;
          gap: 0;
          align-items: flex-start;
          justify-content: flex-start;

          & .status {
            font-style: italic;
            font-weight: 800;
            font-size: 17px;
            color: var(--primary-color-1);
          }

          & .status.rejected {
            color: var(--primary-color-2);
          }
        }

        &>.content {
          display: flex;
          width: 100%;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 5px;

          &>.label {
            font-size: 15px;
            white-space: nowrap;
            font-weight: 500;
          }

          &>.data {
            color: #313036;
            // font-style: italic;

            &>span:not(:first-child) {
              border-left: thin solid;
              margin-left: 5px;
              padding-left: 5px;
            }


          }

          &>.data.rejected {
            color: var(--primary-color-2);
          }

          &>.data.successfully {
            color: var(--primary-color-1);
          }
        }
      }

      &>.routes {
        display: flex;
        flex-direction: column;
        margin-bottom: 6px;
        overflow: hidden;
        flex: 1;
        width: 100%;

        &>.label {
          font-size: 15px;
          white-space: nowrap;
          font-weight: 500;
        }

        &>.places-info {
          display: flex;
          flex-direction: column;
          width: 100%;
          gap: 10px;
          padding: 0 20px;
          justify-content: space-between;
          overflow: hidden;
          transition: all 0.5s ease;

          &>.place {
            display: flex;
            gap: 5px;
            align-items: flex-start;
            overflow: hidden;
            flex: 1;

            &>img {
              width: 25px;
              height: auto;
              object-fit: contain;
            }

            &>.place-content {
              display: flex;
              flex-direction: column;
              font-weight: 600;
              overflow: hidden;

              &>em {
                font-size: 0.8em;
                overflow: hidden;
                text-overflow: ellipsis;
                // white-space: nowrap;
                color: var(--color-text-note);

                &>span:not(:first-child) {
                  border-left: thin solid;
                  margin-left: 5px;
                  padding-left: 5px;
                }
              }
            }

            & .call-customer {
              color: #5f5e66;
              font-size: 15px;
              padding: 2px 7px;
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 5px;
              border: thin solid #ecebf3;
              font-weight: 600;
              white-space: nowrap;
              margin-left: 10px;

              &>svg {
                width: 20px;
              }

              &>img {
                border-radius: 50%;
                border: thin solid #ecebf3;
                width: 20px;
              }
            }
          }

          &>.distance {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;

            & svg {
              color: var(--primary-color-2);
              font-size: 25px;
            }
          }


        }
      }

      &>.delivery-partner-info {
        display: flex;
        flex: 1;
        margin-bottom: 10px;

        &>.partner-logo {
          height: 25px;
          margin-top: 5px;
          margin-right: 10px;
        }
      }

      & > .driver-location-ahamove{
        color: white;
        padding: 2px 10px;
        border-radius: 7px;
        font-weight: 700;
        font-style: italic;
        background: var(--primary-color-1);
      }
    }

    & button.action {
      color: var(--primary-color-1);
      font-weight: bold;
    }
  }

  &>.h-stack {
    width: 100%;
    padding: 10px;
    background: white;
  }

  &>.v-stack {
    flex: 1;
    width: 100%;
    background: var(--color-background-2);
    justify-content: flex-start;
    align-items: flex-start;
    overflow: auto;
  }

  & .not-existing-order {
    flex: 1 1;
    justify-content: center;
    background: white;
    align-items: center;
    margin-top: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    gap: 10px;
    flex-direction: column;

    &>img {
      width: 80%;
      object-fit: contain;
    }

    &>p {
      font-size: 1.75em;
    }
  }

  & .user-avatar {
    border-radius: 50%;
    width: 50px;
    min-width: 50px;
    height: 50px;
    object-fit: cover;
  }

  & .address-detail {
    font-size: 1.1em;
    gap: 10px;
    margin-top: 5px;

    &>.address {
      display: flex;
      gap: 10px;
      align-items: center;
      justify-content: center;
    }
  }

  & .order-items-container {
    background-color: transparent;
  }

  & .item-order-container {
    padding: 5px;
    align-items: flex-start;
    width: 100%;
    margin-top: 10px;
    gap: 5px;
    flex: 1;
    background-color: white;
    // border-bottom: thin solid #ddd;

    &>.item-order-avatar {
      width: 100px;
      height: 100px;
      position: relative;

      &>img {
        object-fit: cover;
        border-radius: 10px;
        background-color: var(--color-background-2);
        width: 100%;
        height: 100%;
        aspect-ratio: 1;
      }
    }

    &>.item-order-detail {
      gap: 5px;
      height: 100%;
      min-height: 100px;
      flex: 1;
      padding: 5px 0 5px 5px;

      &>.item-order-name {
        font-size: 1.1em;
        font-weight: 600;
      }

      &>.item-order-note {
        font-size: 0.9em;
        font-style: italic;
        color: var(--color-text-note);
      }

      &>.item-order-quantity-price {
        font-size: 1em;
        display: flex;
        gap: 10px;
        font-weight: 600;
        color: #828187;

        &>.price {
          color: var(--primary-color-1);
        }

        & em {
          color: var(--color-text-note);
          font-style: normal;
        }

        & em.off {
          text-decoration: line-through;
          font-style: normal;
          font-size: 0.9em;
        }
      }
    }
  }

  & .payment-detail {
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 0;
    border-bottom: thin solid #ebeaef;

    &>.label {
      color: #828187;
      font-size: 1em;
      font-weight: 400;
    }

    &>.data {
      font-size: 1em;
      font-weight: 500;
      position: relative;
      width: auto;
      text-align: right;
      display: flex;
      flex-direction: column;

      &>.delivery-price-estimate {
        font-size: 12px;
        color: #828187;
        line-height: 1;
        font-weight: 700;
        & > span{
          color: var(--primary-color-2);
          font-style: normal;
        }
      }
    }
  }

  & .payment-detail.total {
    font-size: 1.1em;
    flex-wrap: wrap;
    border-bottom: none;

    &>.label {
      font-weight: 600;
      color: #313036;
    }

    &>.data {
      color: #313036;
      font-weight: bold;
    }

    &>.note {
      color: #313036;
      font-weight: 600;
      text-align: right;
      width: 100%;
      font-size: 0.9em;
    }
  }

  & .actions-stack {
    justify-content: space-evenly;
    gap: 10px;
    position: sticky;
    bottom: 0;
    left: 0;
    display: flex;
    flex-wrap: wrap;

    &>button {
      flex: 0.5;
      height: 35px;
      border-radius: 2em;
      color: #828187;
      border: thin solid #828187;
    }

    &>button.accept-button {
      flex: 0.5;
      color: white;
      border: thin solid #1a95ff;
      background: #1a95ff;
    }

    &>button.view-another {
      flex: 1;
      color: var(--primary-color-1);
      border-color: var(--primary-color-1);
    }

    &>button.find-shipper-button {
      flex: 1;
      color: var(--primary-color-1);
      border-color: var(--primary-color-1);
    }
  }
}

.cancel-order-content {
  font-size: 1.3em;
  text-align: center;
}

.cancel-order-title {
  font-size: 1em;
  font-weight: bold;
}

.cancel-order-message {
  font-size: 1em;
  text-align: center;
}

.cancel-order-message>.order-code {
  color: var(--primary-color-2);
  font-weight: bold;
}

.delivery-type {
  color: var(--primary-color-1);
  font-weight: 600;
  text-align: center;
}

// .edit-order-container{
//     height: 90dvh !important;
//     height: 88vh;
//     overflow: auto;
// }

.confirm-cancel-order-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.create-delivery-overlay-container {
  overflow: hidden;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  .create-delivery-modal-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-height: 95dvh;
    min-height: 85dvh;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    overflow: auto;
    max-width: var(--max-width-content-view-1024);

    &>div {
      max-height: inherit;
    }

    & .create-delivery-container {
      max-height: inherit;
      overflow: auto !important;
    }
  }
}

.driver-location-overlay-container {
  overflow: hidden;
  max-width: 100% !important;

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  .driver-location-modal-container {
    background: white;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-height: 95dvh;
    min-height: 85dvh;
    border-radius: 20px 20px 0 0;
    animation: slide-up 0.5s ease;
    overflow: auto;
    max-width: var(--max-width-content-view-1024);
    display: flex;

    &>div {
      max-height: inherit;
    }

    & .driver-location-container {
      max-height: inherit;
      overflow: auto !important;
      display: flex;
      flex-direction: column;
      flex: 1;

      & .map-container {
        // display: flex;
        width: 100%;
        flex: 1;
        min-height: 50%;
        position: relative;

        &>#driver_leaflet_map {
          // flex: 1;
          // align-self: unsafe;
          // height: 100%;
          // // min-height: 450px;
          // outline: none;
          min-height: inherit;
          position: absolute;
          z-index: 1;
          font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system,
            Tahoma, "Segoe UI";
        }

        & .current-location-leaflet {
          bottom: 175px;
        }

        & .map-type-btn {
          bottom: 60px !important;
        }

        & .leaflet-bottom {
          bottom: 50px;
        }
      }

      & .leaflet-routing-container {
        display: none;
      }

      & .delivery-detail-info {
        height: fit-content;
        min-height: fit-content;
        max-height: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;

        &>.expanding-button {
          position: absolute;
          top: -50px;
          height: 100px;
          z-index: 1;
          width: 100%;
          border-radius: 3.5em 3.5em 0 0;
          background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
          color: white;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          cursor: pointer;
          user-select: none;
          animation: none;

          &>div {
            height: 50px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            justify-content: center;
            text-transform: uppercase;

            &>svg {
              font-size: 25px;
            }
          }
        }

        &>.delivery-info {
          display: flex;
          width: 100%;
          max-height: 80dvh;
          overflow: auto;
          flex-direction: column;
          z-index: 10;
          background: white;
          border-radius: 3.5em 3.5em 0 0;
          padding: 10px 0;
          // overflow: hidden;

          &>.delivery-code {
            font-size: 17px;
            font-weight: 600;
            text-align: center;
          }

          &>.delivery-status {
            color: white;
            background: linear-gradient(to right,
                transparent,
                var(--primary-color-1),
                transparent);
            // clip-path: polygon(25px 0%, 100% 0%, calc(100% - 25px) 100%, 0% 100%);
            width: 70%;
            padding: 0 50px;
            margin: auto;
            padding: 5px;
            margin: auto;
            font-weight: 600;
            text-align: center;
            font-weight: 600;
          }

          &>.delivery-status.pending {
            background: linear-gradient(to right,
                transparent,
                #f24f76,
                transparent);
          }

          &>.delivery-status.cancel {
            background: linear-gradient(to right,
                transparent,
                #a30505,
                transparent);
          }

          &>.delivery-status.confirmed {
            background: linear-gradient(to right,
                transparent,
                #4fb6f2,
                transparent);
          }

          &>.delivery-status.delivered {
            background: linear-gradient(to right,
                transparent,
                var(--primary-color-1),
                transparent);
          }

          &>.places-info {
            display: flex;
            flex-direction: column;
            width: 100%;
            gap: 10px;
            padding: 10px 20px;
            justify-content: space-between;

            &>.place {
              display: flex;
              gap: 5px;
              align-items: flex-start;
              overflow: hidden;
              flex: 1;

              &>img {
                width: 25px;
                height: auto;
                object-fit: contain;
              }

              &>.place-content {
                display: flex;
                flex-direction: column;
                font-weight: 600;
                overflow: hidden;

                &>em {
                  font-size: 0.8em;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  // white-space: nowrap;
                  color: var(--color-text-note);

                  &>span:not(:first-child) {
                    border-left: thin solid;
                    margin-left: 5px;
                    padding-left: 5px;
                  }
                }
              }

              & .call-customer {
                color: #5f5e66;
                font-size: 15px;
                padding: 2px 7px;
                border-radius: 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 5px;
                border: thin solid #ecebf3;
                font-weight: 600;
                white-space: nowrap;
                margin-left: 10px;

                &>svg {
                  width: 20px;
                }

                &>img {
                  border-radius: 50%;
                  border: thin solid #ecebf3;
                  width: 20px;
                }
              }
            }

            &>.distance {
              display: flex;
              align-items: center;
              gap: 10px;
              width: 100%;

              & svg {
                color: #f24f76;
                font-size: 25px;
              }
            }

            &>.direction-button {
              padding: 5px 30px;
              background: linear-gradient(to right, #4ed0f0, #4e94f0);
              border-radius: 2em;
              color: white;
              width: fit-content;
              margin: auto;
            }
          }

          & .primary-info.expanded {
            height: fit-content;
            max-height: 100dvh;
            overflow: visible;
          }

          &>.primary-info {
            display: flex;
            max-height: 0;
            gap: 10px;
            padding: 0px 20px;
            margin: 0;
            flex-wrap: wrap;
            overflow: hidden;
            transition: all 0.5s ease;

            &>.stack-info.auto {
              width: fit-content;
              min-width: 48%;
              margin-right: auto;
            }

            &>.stack-info.full {
              width: 100%;
            }

            &>.stack-info.price {
              flex-direction: column;
              justify-content: space-between;
              gap: 5px;

              & .h-stack-content {
                white-space: nowrap;
                width: 100%;

                & .value {
                  color: #393939;
                  font-weight: 600;
                }
              }

              & .h-stack-content.last .value {
                font-size: 20px;
                color: var(--primary-color-1);
              }
            }

            &>.stack-info {
              display: flex;
              gap: 10px;
              align-items: flex-start;

              &>.icon-left {
                font-size: 25px;
                min-width: 25px;
                color: #f24f76;
              }

              &>.stack-content {
                display: flex;
                flex-direction: column;
                flex: 1;

                &>.label {
                  font-size: 15px;
                  font-weight: 500;
                }

                &>.value {
                  color: #393939;
                  font-weight: 600;
                  white-space: break-spaces;
                  line-height: normal;

                  &>span::marker {
                    color: var(--primary-color-1);
                    font-size: 18px;
                  }

                  &>span {
                    display: list-item;
                  }
                }
              }

              &>.h-stack-content {
                display: flex;
                justify-content: space-between;
                flex: 1;

                &>.label {
                  color: #393939;
                  font-size: 15px;
                  font-weight: 600;
                }

                &>.value {
                  color: var(--primary-color-1);
                  font-weight: bold;
                  font-size: 17px;
                  white-space: break-spaces;
                  line-height: normal;
                }
              }
            }
          }
        }

        &>.actions {
          padding: 0;
          display: flex;
          width: 100%;
          background: transparent;
          justify-content: flex-end;

          &>button {
            padding: 10px 35px;
            margin: 0 -10px;
            font-weight: bold;
            background: #ccc;
            clip-path: polygon(25px 0%, 100% 0%, calc(100% - 25px) 100%, 0% 100%);
          }

          &>button:last-child {
            margin-right: 0;
            padding-right: 20px;
            clip-path: polygon(25px 0%, 100% 0%, 100% 100%, 0% 100%);
          }

          &>button.cancel {
            color: white;
            background: rgb(196, 45, 19);
          }

          &>button.confirm {
            color: white;
            background: linear-gradient(45deg, #f86f64, var(--primary-color-1));
          }

          &>button.pick-up {
            color: white;
            background: linear-gradient(45deg, #64f895, #4ed0f0);
          }

          &>button.completed {
            color: white;
            background: #4e94f0;
          }
        }
      }
    }
  }
}

.warning-container {
  max-height: 95dvh;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 10px;
  background-color: white;

  & .warning-title {
    font-size: 17px;
    color: var(--primary-color-1);
    font-weight: 600;
    text-align: center;
  }

  & .warning-message {
    font-size: 20px;
    // color: var(--primary-color-1);
    text-align: center;
    font-weight: 600;
  }

  & .warning-modal-buttons {
    display: flex;
    justify-content: space-evenly;
    padding: 10px;

    &>.review-button {
      background: var(--primary-color-1);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100px;
      min-width: 100px;
      height: 30px;
      border-radius: 2em;
    }
  }
}
button#edit-order-button {
    background: rgb(53 146 54 / 69%);
    border-radius: 5px;
    padding: 7px;
}