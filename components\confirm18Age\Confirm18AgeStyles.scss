.confirm-18-age-container{
  min-height: unset !important;
  width: 400px !important;
  padding: 20px 15px !important;
  
  & > .confirm-title{
    display: flex;
    flex-direction: column;
    text-align: center;
    color: #626262;
  }

  & > .confirm-modal-buttons > button{
    flex: 1;
    border-radius: 7px;
    border: none;
    font-size: 1em;
  }
  & > .confirm-modal-buttons > button.reject-button{
    color: white;
    background: #b2b2b2;
  }
  & > .confirm-modal-buttons > button.accept-button{
    color: white;
    background: #ed1b24;
  }
}
