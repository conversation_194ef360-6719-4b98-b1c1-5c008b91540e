<template>
	<VueFinalModal class="my-modal-container" content-class="v-stack form-modal add-supplier-container" :click-to-close="false" :overlay-behavior="'persist'"
		v-model="showModal" contentTransition="vfm-fade" v-on:closed="() => {
			close()
		}">
		<SubHeaderV2Component :title="$t('AddSupplierComponent.them_nha_cung_cap')">
			<template v-slot:header_left>

			</template>
		</SubHeaderV2Component>
		<div class="add-supplier-content-container">
			<VeeForm :validation-schema="formSchema" v-slot="{ handleSubmit }" @submit="handleSubmit(submit)"
                class="add-delivery-promotion-content">
                <div class="form-field-container">
                    <label class="required" for="name_supplier">{{
                        $t('AddSupplierComponent.ten_nha_cung_cap') }}</label>                    
                    <Field class="custom-input" :validate-on-input="true" name="name" :id="`name_supplier`"
                        :placeholder="`${$t('AddSupplierComponent.ten_nha_cung_cap')}`"
                        v-bind:model-value="name" v-on:update:model-value="($event: any) => {
                            name = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="name"></ErrorMessage>
                </div>

				<div class="form-field-container">
                    <label class="required" for="address_supplier">{{
                        $t('AddSupplierComponent.dia_chi') }}</label>                    
                    <Field class="custom-input" :validate-on-input="true" name="address" :id="`address_supplier`"
                        :placeholder="`${$t('AddSupplierComponent.dia_chi_nha_cung_cap')}`"
                        v-bind:model-value="address" v-on:update:model-value="($event: any) => {
                            address = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="address"></ErrorMessage>
                </div>

				<div class="form-field-container">
                    <label class="required" for="phone_supplier">{{
                        $t('AddSupplierComponent.so_dien_thoai') }}</label>                    
                    <Field class="custom-input" :validate-on-input="true" name="phone" :id="`phone_supplier`"
                        :placeholder="`${$t('AddSupplierComponent.so_dien_thoai')}`"
                        v-bind:model-value="phone" v-on:update:model-value="($event: any) => {
                            phone = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="phone"></ErrorMessage>
                </div>
				<div class="form-field-container">
                    <label class="required" for="email_supplier">{{
                        $t('AddSupplierComponent.email') }}</label>                    
                    <Field class="custom-input" :validate-on-input="true" name="email" :id="`email_supplier`"
                        :placeholder="`${$t('AddSupplierComponent.email')}`"
                        v-bind:model-value="email" v-on:update:model-value="($event: any) => {
                            email = $event;
                        }"></Field>
                    <ErrorMessage class="error-message" name="email"></ErrorMessage>
                </div>


                <button hidden ref="submitFormButton" v-on:click="() => {
                    submit()
                }"></button>
            </VeeForm>
		</div>
		<div class='h-stack action-buttons'>
			<v-btn class='cancel-button' :disabled="isSaving" v-on:click="() => close()">
				{{ $t('AddSupplierComponent.thoat') }}
			</v-btn>
			<v-btn class='save-button' :loading="isSaving" :disabled="isSaving" v-on:click="() => {
				submitFormButton?.click();
			}">
				<span>{{ $t('AddSupplierComponent.luu') }}</span>
			</v-btn>
		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>

import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName, validPhone } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import { ShopService } from "~/services/shopService/shopService";
import ResetCartComponent from "~/components/resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "~/components/confirm18Age/Confirm18AgeComponent.vue";
import { toast } from "vue3-toastify";
import exifr from "exifr";
import { RatingService } from "~/services/ratingService/ratingService";
import { ImageService } from "~/services/imageService/imageService";
import { HttpStatusCode } from "axios";
import { AuthService } from "~/services/authService/authService";
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm, defineRule } from 'vee-validate';
import { QuotationService } from "~/services/quotationService/quotationService";
import { SupplierService } from "~/services/quotationService/supplierService";

const { t } = useI18n();
var props = defineProps({
	shop_id: null
})
var emit = defineEmits([
	'close', 'submit'
])
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

const quotationService = new QuotationService();
const supplierService = new SupplierService();

var showModal = ref(false);

var isSaving = ref(false);
const formSchema = yup.object({
    name: yup.string().required(t('AddSupplierComponent.vui_long_nhap_ten_nha_cung_cap')).max(500, t('AddSupplierComponent.toi_da_x_ky_tu', {x: 500})),
	address: yup.string().required(t('AddSupplierComponent.vui_long_nhap_dia_chi')).max(500, t('AddSupplierComponent.toi_da_x_ky_tu', {x: 500})),
	email: yup.string().email(t('AddSupplierComponent.email_khong_dung_dinh_dang')).required(t('AddSupplierComponent.vui_long_nhap_email')),
	phone: yup.string().required(t('AddSupplierComponent.vui_long_nhap_so_dien_thoai')).test('is-phone', t('AddSupplierComponent.so_dien_thoai_khong_dung'), (value: any) => {
		const re = appConst.validateValue.phone;
		return re.test(validPhone(value));
	})
});

const { handleSubmit } = useForm({
    validationSchema: formSchema,
});
var submitFormButton = ref<HTMLElement | undefined>();

var name = ref("");
var address = ref("");
var phone = ref("");
var email = ref("");

var shop_id = ref(props.shop_id);

onMounted(async () => {
	showModal.value = true;
})

function close(value?: any) {
	showModal.value = false;
	emit('close', value);
}

async function submit(){
	var valid = await formSchema.isValid({
		name: name.value,
		phone: phone.value,
		address: address.value,
		email: email.value
	});
	if(valid){
		isSaving.value = true;
		supplierService.add(
			name.value,
            phone.value,
            address.value,
            email.value,
            shop_id.value,
		).then((res)=>{
			if(res.status == HttpStatusCode.Ok){
				toast.success(t('AddSupplierComponent.them_nha_cung_cap_thanh_cong'))
				emit('submit');
			}
			else{
				toast.error(t('AddSupplierComponent.them_nha_cung_cap_that_bai'))
			}
			isSaving.value = false;
		}).catch(()=>{
			toast.error(t('AddSupplierComponent.them_nha_cung_cap_that_bai'));
			isSaving.value = false;
		})
	}
	console.log('submit')
}

</script>

<style lang="scss" src="./AddSupplierStyles.scss"></style>