<template>
  <div class="product-picker-modal">
    <div class="modal-overlay" @click="$emit('close')"></div>
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <h2>{{ $t('StockManagement.chon_san_pham') }}</h2>
        <button @click="$emit('close')" class="close-btn">
          <Icon name="solar:close-circle-bold" size="24" />
        </button>
      </div>

      <!-- Search -->
      <div class="search-section">
        <div class="search-input">
          <Icon name="solar:magnifer-bold" size="20" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('StockManagement.tim_kiem')"
            @input="searchProducts"
          />
        </div>
      </div>

      <!-- Product List -->
      <div class="product-list">
        <div v-if="loading" class="loading-state">
          <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
          <p>{{ $t('StockManagement.dang_tai') }}</p>
        </div>

        <div v-else-if="products.length === 0" class="empty-state">
          <Icon name="solar:box-bold" size="48" />
          <p>{{ $t('StockManagement.khong_co_du_lieu') }}</p>
        </div>

        <div v-else class="products-grid">
          <div
            v-for="product in products"
            :key="product.id"
            class="product-item"
            @click="selectProduct(product)"
          >
            <img
              :src="product.image || '/default-product.png'"
              :alt="product.name"
              class="product-image"
            />
            <div class="product-info">
              <h4>{{ product.name }}</h4>
              <p class="product-category">{{ product.category }}</p>
              <p class="product-price">{{ formatPrice(product.price) }}</p>
              <div class="product-stock" :class="getStockClass(product.stock)">
                <Icon :name="getStockIcon(product.stock)" size="16" />
                <span>{{ getStockText(product.stock) }}</span>
              </div>
            </div>
            <div class="select-indicator">
              <Icon name="solar:check-circle-bold" size="24" />
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="hasMore && !loading" class="load-more">
          <button @click="loadMore" class="load-more-btn">
            {{ $t('StockManagement.tai_lai') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'

// Emits
const emit = defineEmits(['close', 'select'])

// Composables
const { t } = useI18n()

// Services
const stockService = new StockService()

// Reactive data
const loading = ref(false)
const searchQuery = ref('')
const products = ref([])
const currentPage = ref(0)
const hasMore = ref(true)
const searchTimeout = ref(null)

// Computed properties
const shopId = computed(() => {
  // Get shop ID from store or route params
  
  return '1' // Placeholder
})

// Methods
const loadProducts = async (reset = false) => {
  if (loading.value) return

  loading.value = true
  try {
    const offset = reset ? 0 : currentPage.value * 20
    const response = searchQuery.value
      ? await stockService.searchProducts(shopId.value, searchQuery.value, offset, 20)
      : await stockService.getCurrentStock(shopId.value, offset, 20)

    if (response.data && response.data.products) {
      if (reset) {
        products.value = response.data.products
        currentPage.value = 0
      } else {
        products.value.push(...response.data.products)
      }
      
      hasMore.value = response.data.products.length === 20
      currentPage.value++
    }
  } catch (error) {
    console.error('Error loading products:', error)
  } finally {
    loading.value = false
  }
}

const searchProducts = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  searchTimeout.value = setTimeout(() => {
    loadProducts(true)
  }, 300)
}

const loadMore = () => {
  loadProducts(false)
}

const selectProduct = (product: any) => {
  emit('select', product)
}

const formatPrice = (price: number) => {
  if (!price) return t('StockManagement.gia_lien_he')
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price)
}

const getStockClass = (stock: number) => {
  if (stock === 0) return 'out-of-stock'
  if (stock < 10) return 'low-stock'
  return 'in-stock'
}

const getStockIcon = (stock: number) => {
  if (stock === 0) return 'solar:close-circle-bold'
  if (stock < 10) return 'solar:danger-triangle-bold'
  return 'solar:check-circle-bold'
}

const getStockText = (stock: number) => {
  if (stock === 0) return t('StockManagement.het_hang')
  if (stock < 10) return t('StockManagement.ton_kho_thap')
  return `${stock} ${t('StockManagement.don_vi')}`
}

// Lifecycle
onMounted(() => {
  loadProducts(true)
})
</script>

<style scoped>
.product-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;

  h2 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #ecf0f1;
    }
  }
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;

  .search-input {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px 15px;

    input {
      flex: 1;
      border: none;
      background: none;
      outline: none;
      font-size: 14px;
      color: #2c3e50;

      &::placeholder {
        color: #7f8c8d;
      }
    }
  }
}

.product-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #7f8c8d;

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
    margin: 10px 0 0 0;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
  }

  .product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    flex-shrink: 0;
  }

  .product-info {
    flex: 1;

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 4px 0;
    }

    .product-category {
      font-size: 12px;
      color: #7f8c8d;
      margin: 0 0 4px 0;
    }

    .product-price {
      font-size: 13px;
      font-weight: 600;
      color: #27ae60;
      margin: 0 0 8px 0;
    }

    .product-stock {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;

      &.in-stock {
        color: #27ae60;
      }

      &.low-stock {
        color: #f39c12;
      }

      &.out-of-stock {
        color: #e74c3c;
      }
    }
  }

  .select-indicator {
    color: #3498db;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover .select-indicator {
    opacity: 1;
  }
}

.load-more {
  text-align: center;
  margin-top: 20px;

  .load-more-btn {
    background: #ecf0f1;
    color: #7f8c8d;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #d5dbdb;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .product-picker-modal {
    padding: 10px;
  }

  .modal-content {
    max-height: 90vh;
  }

  .modal-header,
  .search-section,
  .product-list {
    padding: 15px;
  }

  .product-item {
    padding: 12px;

    .product-image {
      width: 50px;
      height: 50px;
    }
  }
}
</style>
