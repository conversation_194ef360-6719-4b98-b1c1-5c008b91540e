.shop-v2-default-container {
  --banner-aspect-ratio: 2.2;

  height: 100%;
  flex: 1;
  background: #f4f4f4;
  margin: 0;
  overflow: auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-width: var(--max-width-content-view-1024) !important;

  & .search-input-container {
    display: flex;
    padding: 5px;
    gap: 2px;
    background: linear-gradient(to right, #00a873, var(--linear-color-1));
    height: 45px;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.1s cubic-bezier(0.075, 0.82, 0.165, 1);

    &.top-55 {
      top: 55px;
      transition: all 0.3s ease !important;
    }

    &>.search-input-group {
      display: flex;
      background: white;
      border-radius: 5px;
      padding: 0 5px;
      flex: 1;
      height: 35px;

      &>button {
        color: var(--primary-color-1);
        width: 35px;
        height: 35px;
        font-size: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &>button.category-button {
        font-size: 22px;
      }

      &>input {
        background: white;
        height: 35px;
        outline: none;
        font-size: 13px;
        font-weight: bold;
        flex: 1;
      }
    }

    .cart-button {
      color: white;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &>svg {
        width: 30px;
      }

      &>.count {
        position: absolute;
        top: 3px;
        right: -2px;
        padding: 0 5px;
        border-radius: 2em;
        background-color: #cc313a;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
      }
    }

    &>.back-button {
      width: 30px;
      height: 30px;
      color: white;
      font-size: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      align-self: center;
    }
  }

  & .loading-skeleton {
    gap: 10px;
    background: transparent;

    .h-stack,
    .v-stack {
      padding: 0 10px;
      gap: 5px;
      display: flex;
      align-items: flex-start;
      background: transparent;
    }

    &>.banner-skeleton {
      width: 100%;
      aspect-ratio: 2;
      max-height: 360px;
    }

    & .avt-skeleton {
      width: 75px;
      min-width: 75px;
      height: 75px;
      border-radius: 5px;
    }

    & .info-placeholder {
      display: flex;
      gap: 5px;
      flex: 1;
      width: 100%;
      padding: 0;
      background: transparent;

      & .info-skeleton {
        width: 100%;
        height: 100%;
        padding: 0;
        margin: -10px 0 0;
        background: transparent;
      }

      & .actions-skeleton {
        display: flex;
        flex-direction: row;
        flex: 1;
        width: 100%;
        background: transparent;
        border-radius: 2em;
        margin-top: -10px;
        z-index: 10;

        &>.action-skeleton {
          height: 25px;
          width: 100px;
          border-radius: 2em;
          margin-left: 15px;
          justify-content: flex-start;
          background: transparent;
        }
      }
    }
  }

  .shop-v2-default-content-container {
    width: 100%;
    height: 100%;
    flex: 1 1;
    /* min-height: inherit; */
    /* border-radius: 10px; */
    max-width: var(--max-width-content-view-1024);
    background: white;
    margin: auto;
    display: flex;
    flex-direction: column;
    font-size: 20px;
    position: relative;

    &>.shop-content-container {
      font-size: 20px;
      position: relative;
      background: #f4f4f4;
      flex: 1;

      &>.banner-container {
        --banner-max-width: 100%;
        width: var(--banner-max-width);
        margin: 0 auto;
      }

      .slogan {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        overflow: hidden;
        padding: 10px;
        width: 100%;
        text-align: center;
        // background: var(--secondary-color-1);

        .text {
          font-size: 17px;
          background-image: linear-gradient(to right, var(--primary-color-1), var(--primary-color-2), #0575e6, var(--linear-color-1));
          background-clip: text;
          color: transparent;
          background-position: 0%;
          background-size: 300%;
          width: auto;
          margin: auto;
          max-width: 100%;
          font-weight: 700;
          font-style: italic;
          font-family: "Montserrat", "Nunito", "Mulish", "Roboto", sans-serif,
            -apple-system, Tahoma, "Segoe UI", monospace !important;
          animation: textColorChange 2s infinite alternate-reverse;
        }
      }

      @keyframes textColorChange {
        from {
          background-position: 0%;
        }

        to {
          background-position: 100%;
        }
      }


      &>.shop-detail-container {
        display: flex;
        font-size: 15px;
        padding: 13px 10px 12px 10px;
        gap: 15px;

        &>.shop-logo {
          position: relative;
          border-radius: 5px;
          border: 0;
          align-self: flex-start;
          box-shadow: none;
          // box-shadow: 0 0 10px rgb(0,0,0,.1);
        }

        &>.shop-detail-content {
          display: flex;
          flex-direction: column;
          font-size: 17px;
          line-height: 1.125;
          flex: 1;
          justify-content: center;

          &>.name-share {
            display: flex;
            align-items: center;
            overflow: hidden;
            gap: 5px;
            color: var(--primary-color-1);
            font-weight: bold;

            &>.name {
              display: flex;
              gap: 2px;
              align-items: center;

              &>span {
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &>svg {
                width: 20px;
                min-width: 20px;
                align-self: flex-start;
              }
            }

            &>.share {
              width: 24px;
              height: 24px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-left: auto;
              font-size: 24px;
              align-self: flex-start;
            }
          }

          &>.follows {
            color: #8c8c8c;
            font-size: 13px;
            font-weight: 300;

            &>em {
              font-style: normal;
              font-weight: bold;
            }
          }

          &>.is-open {
            font-size: 15px;
            font-weight: 700;
            margin: 5px 0;

            width: auto;
            max-width: max-content;

            color: white;

            &.closed {

              &>em.close {
                display: flex;
                flex-direction: column;
                padding: 0;
                background: transparent;

                &>span {
                  padding: 3px 10px;
                  border-radius: 2em;
                  white-space: nowrap;
                  height: auto;
                  max-height: max-content;
                  width: auto;
                  align-self: flex-start;
                  background: var(--primary-color-2);
                  // background: linear-gradient(to right, var(--primary-color-2) 50%, transparent);
                  z-index: 2;

                }

                &>span.reason {
                  padding: 15px 25px 5px 10px;
                  margin-top: -10px;
                  border-radius: 0 10px 10px 10px;
                  border: thin solid;
                  font-size: 13px;
                  // margin-left: -40px;
                  color: var(--primary-color-2);
                  white-space: break-spaces;
                  z-index: 1;
                  width: 100%;
                  // background: linear-gradient(to right, white 80%, transparent);
                  background: white;
                }

                // background: linear-gradient(to right, var(--primary-color-2) 80%, transparent);
              }
            }

            &>em {
              border-radius: 2em;
              padding: 2px 10px;
              width: auto;
              white-space: normal;
              background: var(--primary-color-1);
              font-size: 13px;
            }
          }

          &>.open-time {
            font-weight: 600;
            font-size: 17px;

            &>em {
              color: var(--primary-color-1);
            }
          }

          &>.actions {
            display: flex;
            justify-content: flex-start;
            position: relative;
            margin-top: 5px;
            flex-wrap: wrap;
            gap: 2px;

            &>.action-button {
              border-radius: 2em;
              color: white;
              font-weight: 600;
              margin-right: 3px;
              font-size: 15px;
              white-space: nowrap;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 1px;

              &.call {
                background: var(--primary-color-1);
                padding: 2px 7px 2px 2px;
                line-height: 14px;

                &>svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }
              }

              &.direction {
                background: linear-gradient(to right,
                    var(--primary-color-1),
                    var(--linear-color-1));
                padding: 2px 10px 2px 5px;
                line-height: 1.125;

                &>svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }
              }

              &.follow {
                background: linear-gradient(to right,
                    var(--primary-color-1),
                    var(--linear-color-1));
                padding: 2px 10px 2px 2px;
                line-height: 1.125;
                color: var(--primary-color-1);
                background: white;
                margin-right: 0;
                box-shadow: inset 0 0 0 1px var(--primary-color-1);

                &>svg {
                  width: 18px;
                  min-width: 18px;
                  height: 18px;
                }

                &>span {
                  padding: 0 5px;
                }
              }

              &.follow.followed {
                color: var(--primary-color-2);
                box-shadow: inset 0 0 0 1px var(--primary-color-2);
                padding: 2px 10px;

                &>svg {
                  display: none;
                }
              }
            }
          }
        }
      }

      &>.shop-detail-open-time {
        padding: 0;
        background: white;
        display: flex;
        flex-direction: column;

        &>.label {
          padding: 10px;
          font-size: 17px;
          color: var(--primary-color-1);
          font-weight: 700;
          display: flex;
          justify-content: space-between;

          &>svg {
            width: 21px;
            height: 21px;
            min-width: 21px;
            color: #d9d9d9;
          }
        }

        &>.line {
          margin: 0;
          height: 1px;
          background-color: #ececec;
        }

        &>.list-open-time {
          display: flex;
          flex-direction: column;
          gap: 5px;
          font-size: 17px;
          padding: 10px;

          &>.item-open-time {
            display: flex;
            align-items: center;
            // justify-content: space-between;

            &>.days {
              font-size: 15px;
              color: #545454;
              font-weight: 400;
              width: 40%;
              line-height: 1.125;
            }

            &>.times {
              font-size: 17px;
              color: var(--primary-color-2);
              font-weight: 700;
              margin-left: auto;
              line-height: 1.125;
            }
          }
        }
      }

      &>.shop-detail-notification {
        padding: 0;
        background: white;
        display: flex;
        flex-direction: column;
        margin-top: 7px;

        &>.label {
          padding: 10px;
          font-size: 15px;
          color: var(--primary-color-1);
          font-weight: 700;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          text-transform: uppercase;

          &>svg {
            width: 25px;
            height: 25px;
            min-width: 25px;
            color: var(--primary-color-2);
            margin-right: 5px;
          }

          & span {
            line-height: 1;
          }
        }

        &>.line {
          margin: 0;
          height: 1px;
          background-color: #ececec;
        }

        &>.notification-content {
          flex: 1;
          padding: 10px;
          font-size: 16px;
          white-space: break-spaces;
          color: #595959;
          font-weight: 700;
        }
      }

      &>.shop-detail-delivery-promotion {
        padding: 0;
        background: white;
        display: flex;
        flex-direction: column;
        margin-top: 7px;


        &>.label {
          padding: 10px;
          font-size: 15px;
          color: var(--primary-color-1);
          font-weight: 800;
          display: flex;
          justify-content: flex-start;
          text-transform: uppercase;

          &>svg {
            width: 25px;
            height: 25px;
            min-width: 25px;
            color: var(--primary-color-2);
            margin-right: 5px;
          }
        }

        &>.line {
          margin: 0;
          height: 1px;
          background-color: #ececec;
        }

        &>.list-delivery-promotion {
          padding: 10px;

          & .delivery-promotion-item-slide {
            max-width: 90dvw;
            padding: 10px;
            background:
              color-mix(in srgb, var(--primary-color-1) 10%, transparent);
            display: flex;
            gap: 10px;
            border-radius: 10px;
            height: auto;
            width: 300px !important;

            &>.value-tag {
              height: 50px;
              min-width: 75px;
              width: 75px;
              font-size: 20px;
              align-items: center;
              justify-content: center;
              display: flex;
              color: white;
              font-weight: 800;
              position: relative;

              &>img {
                height: 50px;
                width: 75px;
                min-height: unset;
                z-index: 1;
                left: 0;
                position: absolute;
              }

              &>span {
                z-index: 2;
              }
            }

            &>.promotion-detail {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: flex-start;
              line-height: normal;

              &>.name {
                font-size: 15px;
                color: #545454;
                font-weight: 800;
              }

              &>.description {
                font-size: 14px;
                color: #6c6c6c;
                font-weight: 500;
                font-style: italic;
              }

              &>.max-value {
                font-size: 13px;
                font-weight: 700;
                color: #6c6c6c;
                font-style: italic;

                &>em {
                  color: var(--primary-color-2);
                }
              }

              &>.condition-action {
                color: var(--primary-color-2);
                font-size: 13px;
                font-style: italic;
                margin-top: auto;

                &.no-conditions {
                  color: var(--primary-color-1);
                }
              }
            }
          }
        }


      }

      &>.shop-detail-description {
        &>.description {
          font-size: 0.8em;
          color: #020202;
          line-height: 1.125;
          display: flex;
          flex-direction: column;
          transition: all 0.2s ease-in-out;
          max-height: calc(1.125 * 3);
          padding: 0 15px;
          margin: 15px 0;

          &>span {
            display: block;

            &>span {
              padding-bottom: 5px;
              white-space: break-spaces;
            }
          }
        }
      }

      &>.shop-products-container {
        display: flex;
        flex-direction: column;
        // padding: 10px 0;
        font-size: 20px;
        position: relative;
        background: white;
        margin-top: 7px;

        &>.title-stack {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;

          &.uppercase {
            text-transform: uppercase;
          }

          &>span {
            font-size: 15px;
            font-weight: 800;
            color: var(--primary-color-2);
          }

          &>.button-fitler-sort {
            color: var(--primary-color-1);
            font-size: 0.8em;
            display: flex;
            align-items: center;

            &>svg {
              color: #595959;
              font-size: 40px;
            }
          }
        }

        & .line {
          margin: 0;
          height: 1px;
          background-color: #c1eee4;
        }

        &>.grid-list-view {
          display: flex;
          justify-content: space-between;
          padding: 10px;

          &>span {
            font-size: 16px;
            color: #a8a7a7;
            font-weight: 700;
          }

          &>.type-view-buttons {
            display: flex;
            gap: 2px;
            align-items: center;
            justify-content: center;

            &>.type-view-button {
              font-size: 27px;
              color: #d9d9d9;
              display: flex;
            }

            &>.type-view-button.active {
              color: var(--primary-color-1);
            }
          }
        }

        &>.categories-container.sticky-padding {
          padding-top: 45px;
          transition: all 0.2s ease-in-out;
          border-bottom: thin solid #c1eee4;
        }

        &>.categories-container {
          font-size: 0.7em;
          position: sticky;
          top: 0;
          background: white;
          z-index: 1;
          padding: 0;
          user-select: none;

          &>.categories-carousel {
            padding: 10px 20px;

            & .swiper-button-prev,
            .swiper-button-next {
              color: #a8a7a7;
              font-size: 15px;
              width: 20px;
              background: white;
              height: 100%;
              top: 0;
              padding: 0;
              margin-top: 0;
              text-shadow: none;
              z-index: 10;

              &::after {
                position: relative;
                top: 0;

                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                padding: 0;
                font-size: 15px;
                border: none;
              }
            }

            & .swiper-button-prev {
              left: 0;
              right: unset;
            }

            & .swiper-button-next {
              right: 0;
              left: unset;
            }
          }

          & .category-item-slide {
            width: fit-content !important;
            padding: 1px;
            color: #a8a7a7;
            font-size: 15px;
            font-weight: 800;
            text-transform: uppercase;
            cursor: pointer;
          }

          & .category-item-slide.active {
            color: var(--primary-color-2);
          }
        }

        &>.products-container {
          flex-direction: column;
          flex-wrap: wrap;
          position: relative;
          display: flex;
          justify-content: flex-start;
          row-gap: 5px;
          padding: 0;
          background: #f5f6fa;

          & .category-products-container {
            background: #f5f6fa;
            margin-top: 10px;
            border-radius: 10px 10px 0 0;
            display: flex;
            flex-direction: column;
            // gap: 10px;
            // box-shadow: 0 0 15px rgb(0, 0, 0, 0.1);
            width: 100%;
            // border-bottom: thin solid #efefef;

            &:last-child {
              padding-bottom: 125px;
            }

            &>.category-title {
              font-size: 13px;
              color: white;
              text-transform: uppercase;
              width: fit-content;
              border-radius: 7px;
              line-height: normal;
              font-weight: 700;
              display: flex;
              align-items: center;
              padding: 5px 10px;
              background: #d19019;
              margin-left: 10px;
            }

            &>.category-products-list {
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              justify-content: flex-start;
              gap: 7px;
              padding: 5px;

              @media screen and (max-width: 500px) {
                &>.product-item-container-grid {
                  --width-item: calc((100vw - 7px - 5px * 2) / 2) !important;
                }
              }

              @media screen and (min-width: 501px) and (max-width: 1024px) {
                &>.product-item-container-grid {
                  --width-item: calc((100dvw - 7px * 3 - 5px * 2) / 3) !important;
                }
              }

              @media screen and (min-width: 1025px) and (max-width: 1320px) {
                &>.product-item-container-grid {
                  --width-item: calc((1024px - 7px * 3 - 5px * 3) / 4) !important;
                }
              }

              @media screen and (min-width: 1321px) {
                &>.product-item-container-grid {
                  --width-item: calc((1024px - 7px * 3 - 5px * 3) / 4) !important;
                }
              }

              &>.product-item-container-grid {
                width: var(--width-item);
                min-width: var(--width-item);
                max-width: var(--width-item);
                justify-content: space-evenly;
                display: block;
                border-radius: 10px;

                &>a.product-item {
                  flex: 1;
                  width: 100%;

                  &>img {
                    flex: 1;
                    width: calc(var(--width-item) - 12px);
                    height: calc(var(--width-item) - 12px);
                    min-height: calc(var(--width-item) - 12px);
                    border-radius: 7px;
                  }
                }
              }

              &>.product-item-container-grid:first {
                margin-left: 0;
              }

              &>.product-item-container-grid:last-child {
                margin-right: auto;
              }

              &>.product-item-container {
                width: 100%;
                border-radius: 10px;

                // @media screen and (min-width: 721px) {
                //   width: calc(50% - 5px);
                // }
                // @media screen and (min-width: 1320px) {
                //   width: calc(50% - 5px);
                // }

                &>a {
                  width: 100%;
                  flex-direction: row;

                  &>img {
                    flex: 0;
                    width: 120px;
                    height: 120px;
                    min-height: 120px;
                    max-height: 120px;
                    border-radius: 10px;
                  }
                }
              }
            }
          }

          &>.product-carousel {
            flex: 1;
            padding: 10px;

            &>.swiper-wrapper {
              height: 100%;
              box-sizing: border-box;
            }
          }

          & .product-item-container {
            cursor: pointer;
            padding: 5px;
            border: thin solid transparent;
            width: 100%;
            height: auto;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
            -webkit-user-drag: none;
            // border: thin solid #D9D9D9;
            background: white;
            border-radius: 10px;

            & .product-item {
              width: 100%;
              overflow: hidden;
              display: flex;
              flex-direction: column;
              position: relative;
              -webkit-user-drag: none;
              border-radius: 7px;

              &>img {
                background: var(--color-background-2);
                width: 120px;
                height: 120px;
                max-height: 120px;
                min-height: 120px;
                aspect-ratio: 1;
                object-fit: cover;
              }

              &>.product-item-content {
                display: flex;
                flex-direction: column;
                padding: 5px 5px 5px 10px;
                text-align: left;
                font-size: 1em;
                gap: 1px;
                flex: 1;

                &>.name {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-weight: 800;
                  color: var(--primary-color-1);
                  font-size: 15px;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  line-clamp: 2;
                  line-height: 1.1;
                  -webkit-box-orient: vertical;
                }

                &>.sold-like-amount {
                  font-size: 13px;
                  color: #a8a7a7;
                  font-weight: 400;
                  line-height: 15px;
                  min-height: 15px;
                  margin-top: 3px;
                }

                &>.h-stack.price-add-to-cart {
                  // flex: 1;
                  background: #ececec;
                  border-radius: 10px;
                  width: auto;
                  min-width: 200px;
                  margin-right: auto;
                  margin-top: auto;
                  padding: 5px;

                }

                & .price {
                  // white-space: nowrap;
                  // overflow: hidden;
                  // text-overflow: ellipsis;
                  // font-weight: bold;
                  // color: var(--primary-color-2);
                  // font-size: 17px;
                  // display: flex;
                  // flex-direction: column;
                  // line-height: 1.1;
                  // margin-top: auto;
                  // font-weight: 800;
                  // padding-top: 10px;

                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-weight: bold;
                  color: var(--primary-color-2);
                  font-size: 17px;
                  display: flex;
                  flex-direction: column;
                  line-height: 1.1;

                  &>.off {
                    color: #6c6c6c;
                    text-decoration: line-through;
                    font-style: normal;
                    font-size: 13px;
                    font-weight: 400;
                  }
                }

                & .add-to-cart {
                  color: var(--primary-color-2);
                  margin-left: auto;
                  margin-top: auto;
                  width: 40px;
                  min-width: 40px;
                  height: 40px;
                  padding: 5px;
                  display: flex;
                  font-size: 30px;
                  align-items: flex-end;
                  justify-content: flex-end;
                }
              }
            }

            &>.top-left-tag {
              z-index: 1;
              position: absolute;
              top: 10px;
              left: 10px;
              width: 50px;
              height: 50px !important;
              min-height: unset;
              object-fit: contain;
            }

            &>.top-left-tag.small {
              width: 30px;
              height: 30px !important;
            }
          }

          & .product-item-container-grid {
            --shop-product-item-width: 160px;
            cursor: pointer;
            padding: 5px;
            border: thin solid transparent;
            width: var(--shop-product-item-width);
            height: auto;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            -webkit-user-drag: none;
            // border: thin solid #D9D9D9;
            background: white;
            border-radius: 10px;

            @media screen and (max-width: 500px) {
              --shop-product-item-width: 175px !important;
            }

            @media screen and (min-width: 501px) and (max-width: 720px) {
              --shop-product-item-width: 200px !important;
            }

            @media screen and (min-width: 721px) and (max-width: 1320px) {
              --shop-product-item-width: 200px !important;
            }

            @media screen and (min-width: 1321px) {
              --shop-product-item-width: 200px !important;
            }

            & .product-item {
              width: calc(var(--shop-product-item-width) - 20px);
              height: 100%;
              overflow: hidden;
              display: flex;
              flex-direction: column;
              position: relative;
              -webkit-user-drag: none;

              &>img {
                flex: 1;
                background: var(--color-background-2);
                width: calc(var(--shop-product-item-width) - 20px);
                height: calc(var(--shop-product-item-width) - 20px);
                max-height: calc(var(--shop-product-item-width) - 20px);
                min-height: calc(var(--shop-product-item-width) - 20px);
                // aspect-ratio: 1;
                object-fit: cover;
                border-radius: 7px;
              }

              &>.product-item-content {
                display: flex;
                flex-direction: column;
                // height: 75px;
                // min-height: 75px;
                padding: 5px 0 0;
                text-align: left;
                font-size: 1em;
                gap: 1px;
                flex: 1;

                &>.name {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-weight: bold;
                  color: var(--primary-color-1);
                  font-size: 15px;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  line-clamp: 2;
                  line-height: 1.1;
                  -webkit-box-orient: vertical;
                }

                &>.sold-like-amount {
                  font-size: 13px;
                  color: #a8a7a7;
                  font-weight: 400;
                  line-height: 15px;
                  min-height: 15px;
                  margin-top: 3px;
                }

                &>.h-stack.price-add-to-cart {
                  // flex: 1;
                  background: #ececec;
                  border-radius: 10px;
                  width: auto;
                  width: 100%;
                  margin-right: auto;
                  margin-top: auto;
                  padding: 5px;

                }

                & .price {
                  // white-space: nowrap;
                  // overflow: hidden;
                  // text-overflow: ellipsis;
                  // font-weight: bold;
                  // color: var(--primary-color-2);
                  // font-size: 17px;
                  // display: flex;
                  // flex-direction: column;
                  // line-height: 1.1;
                  // margin-top: auto;
                  // font-weight: 800;
                  // padding-top: 10px;

                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-weight: bold;
                  color: var(--primary-color-2);
                  font-size: 17px;
                  display: flex;
                  flex-direction: column;
                  line-height: 1.1;

                  &>.off {
                    color: #6c6c6c;
                    text-decoration: line-through;
                    font-style: normal;
                    font-size: 13px;
                    font-weight: 400;
                  }
                }

                & .add-to-cart {
                  color: var(--primary-color-2);
                  margin-left: auto;
                  margin-top: auto;
                  width: 40px;
                  min-width: 40px;
                  height: 40px;
                  padding: 5px;
                  display: flex;
                  font-size: 30px;
                  align-items: flex-end;
                  justify-content: flex-end;
                }
              }
            }

            &>.top-left-tag {
              z-index: 1;
              position: absolute;
              top: 10px;
              left: 10px;
              width: 50px;
              height: 50px !important;
              min-height: unset;
              object-fit: contain;
            }

            &>.top-left-tag.small {
              width: 30px;
              height: 30px !important;
            }
          }

          & .product-item-container:hover,
          .product-item-container:active,
          .product-item-container:focus,
          .product-item-container:target {
            border: thin solid var(--primary-color-1);
            border-radius: 2px;
            background: #ebfeff;
            box-shadow: 0 0px 3px -1px #868686;
          }

          & .product-item-container-grid:hover,
          .product-item-container-grid:active,
          .product-item-container-grid:focus,
          .product-item-container-grid:target {
            border: thin solid var(--primary-color-1);
            border-radius: 10px;
            background: #ebfeff;
            box-shadow: 0 7px 10px -7px #868686;
          }

          .empty-list {
            height: 100%;
            max-height: 500px;
            flex: 1;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 15px;
            text-align: center;
          }

          .empty-list>.empty-list-image {
            margin: 10px 0;
            justify-content: center;
            border-radius: 50%;
            width: 200px;
            height: 200px;
            object-fit: contain;
          }

          .empty-list>span {
            font-size: 1em;
            color: var(--color-text-note);
          }

          & .swiper-button-prev,
          .swiper-button-next {
            color: white;
            font-size: 15px;
            width: 30px;
            height: 50%;
            top: 5px;
            padding: 0;
            bottom: 0;
            margin-top: 0;
            filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
            text-shadow: none;

            &::after {
              position: absolute;
              top: 75px;
              transform: translateY(-50%);
              width: 30px;
              height: 30px;
              padding: 5px;
              border-radius: 50%;
              border: 2px solid white;
            }
          }

          & .swiper-button-prev {
            left: 20px;
            right: unset;
          }

          & .swiper-button-next {
            right: 20px;
            left: unset;
          }
        }
      }

      &>.shop-footer {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        padding: 10px;
        gap: 10px;
        background: white;
        position: sticky;
        bottom: 0;
        box-shadow: 0 0px 10px rgb(0, 0, 0, 0.1);
        z-index: 10;
        margin-top: auto;

        &>button,
        >a {
          border-radius: 2em;
          display: flex;
          align-items: center;
          justify-content: center;
          text-transform: uppercase;
          color: white;
          font-weight: 700;
          font-size: 15px;
          width: fit-content;
          max-width: 200px;
          flex: 1;
          padding: 5px;
          gap: 5px;
          height: 35px;
          white-space: nowrap;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;

          &>svg {
            min-width: 30px;
            height: 100%;
          }

          &>span {
            // flex: 1;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
            line-height: normal;
          }
        }

        &>a.call-to-shop {
          background: var(--primary-color-1);
        }

        &>button.chat-to-shop {
          background: var(--primary-color-2);
        }
      }
    }
  }

  & .scroll-top-button {
    position: fixed;
    bottom: 70px;
    right: 25px;

    &>button.go-up {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      font-size: 30px;
      background: var(--primary-color-1);
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      z-index: 100;
    }
  }

  & .chat-to-shop-button {
    background: var(--primary-color-2);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 26px;
    box-shadow: 0 4px 4px rgb(0, 0, 0, 0.25);
    position: fixed;
    z-index: 1000;
    // bottom: 150px;
    // right: 25px;
    touch-action: none;
    will-change: transform;
    user-select: none;
    transition: all 0.2s ease-in-out;
  }

  & .chat-to-shop-button.none-transition {
    transition: none;
  }

  & .chat-to-shop {
    width: fit-content;
    height: fit-content;
    pointer-events: auto;
  }
}