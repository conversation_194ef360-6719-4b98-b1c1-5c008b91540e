import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";
import type { member_type } from "~/components/chatManage/ChatDTO";

export class ChatService extends BaseHTTPService {
  listChannel(
    member_id: string,
    member_type: member_type,
    query_type = 1,
    offset = 0,
    limit = 1000
  ) {
    let body = {
      member_id: member_id,
      member_type: member_type,
      query_type: query_type,
      offset: offset,
      limit: limit,
    };
    return this.https("POST", appConst.apiURL.chatListChannel, body, null, true);
  }

  getChannelByMemberIds(
    sender_id: string,
    sender_type: member_type,
    receiver_id: string
  ) {
    let body = {
      member_a_id: sender_id,
      member_a_type: sender_type,
      member_b_id: receiver_id,
    };
    return this.https("POST", appConst.apiURL.chatGetChannelByMemberIds, body, null, true);
  }

  listMessage(channel_id: string, language = "vi", offset = 0, limit = 100, member_id: string, member_type: string) {
    let body = {
      channel_id: channel_id,
      offset: offset,
      limit: limit,
      language: language,
      member_id: member_id,
      member_type: member_type
    };
    return this.https("POST", appConst.apiURL.chatListMessage, body, null, true);
  }

  sendMessage(
    sender_id: string,
    receiver: any | null,
    channel_id: any | null,
    sender_type: member_type,
    content: any,
    language = "vi"
  ) {
    let body = {
      sender_id: sender_id,
      sender_type: sender_type,
      content: {
        ...content,
        original_language: language,
      },
      language: language,
      receiver: receiver,
      channel_id: channel_id,
    };

    if (!isNull(channel_id)) {
      delete body.receiver;
    } else {
      delete body.channel_id;
    }

    return this.https("POST", appConst.apiURL.chatSendMessage, body, null, true);
  }

  updateMessage(message_id: any, member_id:any, content:any, language = 'vi') {
    let body = {
      message_id: message_id,
      member_id: member_id,
      content: content,
      language: language
    };
    return this.https("POST", appConst.apiURL.chatUpdateMessage, body, null, true);
  }

  retractMessage(message_id: any, member_id:any, status=2) {
    let body = {
      message_id: message_id,
      member_id: member_id,
      status: status
    };
    return this.https("POST", appConst.apiURL.chatRetractMessage, body, null, true);
  }

  countUnreadMessage(){
    return this.https("GET", appConst.apiURL.chatCountUnreadMessage);
  }
}
