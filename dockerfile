FROM node:18-alpine as builder

# create work directory in app folder
WORKDIR /app

# install required packages for node image
#RUN apk --no-cache add openssh g++ make python3 git

# copy over package.json files
COPY package*.json ./

# install all depencies
RUN npm i
# copy over all files to the work directory
ADD . /app
# RUN if [ "$HTTP_PROXY" = "dev" ];  \
#  then cp -- ./assets/environment/environment.dev.tsx ./assets/environment/environment.tsx; \
#  elif [ "$HTTP_PROXY" = "pro" ]; \
#  then cp -- ./assets/environment/environment.prod.tsx ./assets/environment/environment.tsx; \
#  fi

# build the project
RUN npm run build

# start final image
FROM node:18-alpine


WORKDIR /app

# copy over build files from builder step
#COPY --from=builder /app .
COPY --from=builder /app/.output  .output
COPY --from=builder /app/.nuxt  .nuxt
RUN addgroup -g 1410 appgroup

RUN adduser -D -u 1410 appuser -G appgroup

RUN chown -R appuser:appgroup /app

USER appuser
# expose the host and port 3000 to the server
ENV HOST 0.0.0.0
EXPOSE 3000

# run the build project with node
ENTRYPOINT ["node", ".output/server/index.mjs"]