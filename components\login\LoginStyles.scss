.login-container,
.skeleton-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  padding: 10px 25px;
  overflow: auto;
  font-size: 17px;
  max-width: 720px !important;
  // background-color: var(--color-background-2);

  .sign-up-btn {
    text-align: right;
    color: var(--primary-color-1);
    font-weight: 600;
    // margin-bottom: 10px;
    margin-left: auto;
    display: flex;
    align-items: center;

    & > .back-home{
      display: flex;
      align-items: center;
      font-size: 15px;
      margin-right: auto;
      color: #414141;
      & > svg{
        font-size: 30px;
      }
    }
  }
  .login-title {
    font-size: 1.7em;
    font-weight: 800;
    text-align: left;
  }

  .login-slogan {
    font-size: 1em;
    font-weight: 500;
    text-align: left;
  }

  .login-protocol {
    display: flex;
    gap: 25px;
    margin: 30px 0 0;
    justify-content: flex-end;

    & > .login-protocol-btn {
      color: #7b7b7b;
      font-weight: 600;
    }

    & > .login-protocol-btn.active {
      color: var(--primary-color-1);
    }
  }

  & .content-container {
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease-in-out;
    & > .v-stack {
      margin-top: 10px;
    }
    .content-input {
      background: transparent;
      border: none;
      outline: none;
      padding: 10px 0;
    }

    .label {
      color: var(--color-text-black);
      margin: 5px 0;
      font-size: 15px;
    }
    .content-input-group:focus-within, .content-input-group:hover{
      border-color: var(--primary-color-1);
    }
    .content-input-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      border: thin solid var(--color-text-black);
      padding-left: 10px;
      border-radius: 5px;

      & > input {
        flex: 1;
      }
      & > input:-webkit-autofill {
        background-color: initial !important;
      }
      & > button {
        background: transparent;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: var(--primary-color-1);
      }

      & > button.nation-code {
        color: var(--color-text-black);
        height: 20px;
        width: fit-content;
        padding-right: 15px;
        margin-right: 15px;
        border-right: thin solid #ddd;
        border-radius: 0;
      }
    }

    .forget-password-text {
      font-size: 1em;
      font-weight: 500;
      align-self: flex-end;
      margin-top: 5px;
      cursor: pointer;
    }
  }

  & > .button-action {
    margin-top: 15px;
    margin-bottom: 15px;
    background-color: var(--primary-color-1);
    border-radius: 5px;
    padding: 10px;
    position: relative;
    text-align: center;
    border: none;
    color: white;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1em;
    line-height: 1;
    height: fit-content;
    text-transform: none;

    img {
      width: 25px;
      height: 25px;
      margin-right: 10px;
    }
  }
  & > .button-action:disabled {
    // background-color: #a7a7a7;
    opacity: 0.4;
  }

  .others-protocol{
    margin-top: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    & > span {
      width: 100%;
      display: flex;
      gap: 10px;
      justify-content: center;
      text-align: center;
      color: #a1a1a1;

      & > .line{
        flex: 1;
        height: 1px;
        background: #ccc;
        margin: auto;
      }
    }

    & > .button-action {
      margin-top: 15px;
      background-color: rgba(0, 0, 0, .08);
      border: thin solid transparent;
      border-radius: 5px;
      padding: 10px;
      position: relative;
      text-align: center;
      color: rgba(0, 0, 0, .92);
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1em;
      line-height: 1;
      height: fit-content;
      text-transform: none;
      width: 100%;
  
      img {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }
    }
    .button-action:disabled {
      // background-color: #a7a7a7;
      opacity: 0.4;
    }
  }

  .contact{
    width: 100%;
    text-align: center;
    margin-top: 15px;
    margin-bottom: 15%;

    & > a {
      color: var(--primary-color-1);
    }
  }
  // .login-icon {
  //     margin: 10px auto;
  //     justify-content: center;
  //     border-radius: 50%;
  //     width: 150px;
  //     height: 150px;
  //     object-fit: contain;
  // }

  // .content-container {
  //     margin: 10px 0;
  //     padding: 0 10px;
  // }

  // .content-container>.v-stack {
  //     margin: 10px 0;
  // }

  // .password-input-group {
  //     width: 100%;
  //     display: flex;
  //     flex-direction: row;
  // }

  // .password-input-group>input {
  //     flex: 1;
  // }

  // .password-input-group>button {
  //     background: transparent;
  //     border-radius: 50%;
  //     width: 35px;
  //     height: 35px;
  //     padding: 0;
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     border: none;
  //     color: var(--primary-color-1);
  // }

  // .password-input-group>button:hover {
  //     background-color: #ddd;
  // }

  // .forget-password-text {
  //     font-size: 1em;
  //     font-weight: 500;
  //     align-self: flex-end;
  //     margin-top: 5px;
  //     cursor: pointer;
  // }

  // .button-action {
  //     margin-top: 5px;
  //     background-color: var(--primary-color-1);
  //     border-radius: 5px;
  //     padding: 10px;
  //     position: relative;
  //     text-align: center;
  //     border: none;
  //     color: white;
  //     font-weight: 500;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     font-size: 1em;
  //     line-height: 1;
  //     height: fit-content;
  //     text-transform: none;
  // }
  // .button-action:disabled{
  //     background-color: #a7a7a7;
  // }
  // .button-action img {
  //     width: 25px;
  //     height: 25px;
  //     margin-right: 10px
  // }

  // .signup-button-container {
  //     justify-content: center;
  // }

  // .signup-button-container .signup-button {
  //     margin-left: 10px;
  //     font-weight: 500;
  //     color: var(--primary-color-1);
  // }
}

.button-google {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;

  & iframe {
    width: 100%;
    height: 100%;
  }
}

.user-disabled-modal {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
  position: relative;


  & button{
    margin-left: auto;
    font-size: 25px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
  & img {
    width: 300px;
    object-fit: contain;
    margin: auto;
  }

  & .message {
    font-size: 1.3em;
    // color: var(--primary-color-1);
    text-align: center;

    & a {
      font-weight: 600;
      color: var(--primary-color-1);
    }
  }
}
