.delivery-detail-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  z-index: 100;
  overflow: hidden;

  & .header-right {
    & > .filter-button.active {
      color: var(--primary-color-1);
    }
  }

  & > .delivery-detail-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: white;
    width: 100%;
    height: 100%;

    & > .delivery-detail-content {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    & .map-container {
      // display: flex;
      width: 100%;
      flex: 1;
      min-height: 50%;
      display: flex;
      height: 100%;

      position: relative;

      & > #leaflet_map {
        // flex: 1;
        // align-self: unsafe;
        // height: 100%;
        // // min-height: 450px;
        // outline: none;
        min-height: inherit;
        position: absolute;
        z-index: 1;
        font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system,
          <PERSON><PERSON><PERSON>, "Segoe UI";
      }

      & .current-location-leaflet {
        bottom: 175px;
      }
      & .map-type-btn {
        bottom: 60px !important;
      }
      & .leaflet-bottom {
        bottom: 50px;
      }
    }
    & .leaflet-routing-container {
      display: none;
    }
    & .user-location {
      width: 100%;
      height: 100%;
      color: var(--primary-color-2);
    }
    & .not-existing {
      flex: 1 1;
      justify-content: flex-start;
      align-items: center;
      margin-top: 10px;
      width: 100%;
      display: flex;
      gap: 10px;
      flex-direction: column;

      & > img {
        width: 300px;
        object-fit: contain;
      }

      & > p {
        font-size: 1.75em;
      }
    }

    & .delivery-detail-info {
      height: fit-content;
      min-height: fit-content;
      max-height: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;

      & > .expanding-button {
        position: absolute;
        top: -50px;
        height: 100px;
        z-index: 1;
        width: 100%;
        border-radius: 3.5em 3.5em 0 0;
        background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
        color: white;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        cursor: pointer;
        user-select: none;
        animation: none;

        & > div {
          height: 50px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 5px;
          justify-content: center;
          text-transform: uppercase;

          & > svg {
            font-size: 25px;
          }
        }
      }
      & > .delivery-info {
        display: flex;
        width: 100%;
        max-height: 80dvh;
        overflow: auto;
        flex-direction: column;
        z-index: 10;
        background: white;
        border-radius: 3.5em 3.5em 0 0;
        padding: 10px 0;
        gap: 5px;
        // overflow: hidden;

        & > .delivery-code {
          font-size: 17px;
          font-weight: 600;
          text-align: center;
        }

        & > .delivery-status {
          color: white;
          background: linear-gradient(
            to right,
            transparent,
            var(--primary-color-1),
            transparent
          );
          // clip-path: polygon(25px 0%, 100% 0%, calc(100% - 25px) 100%, 0% 100%);
          width: 70%;
          padding: 0 50px;
          margin: auto;
          padding: 5px;
          margin: auto auto 5px;
          font-weight: 600;
          text-align: center;
          font-weight: 600;
        }
        & > .delivery-status.pending {
          background: linear-gradient(
            to right,
            transparent,
            #f24f76,
            transparent
          );
        }
        & > .delivery-status.cancel {
          background: linear-gradient(
            to right,
            transparent,
            #a30505,
            transparent
          );
        }
        & > .delivery-status.confirmed {
          background: linear-gradient(
            to right,
            transparent,
            #4fb6f2,
            transparent
          );
        }
        & > .delivery-status.delivered {
          background: linear-gradient(
            to right,
            transparent,
            var(--primary-color-1),
            transparent
          );
        }
        & .places-info.expanded {
          height: fit-content;
          max-height: 100dvh;
          overflow: visible;
        }
        & > .places-info {
          display: flex;
          flex-direction: column;
          width: 100%;
          gap: 10px;
          padding: 0 20px;
          justify-content: space-between;
          max-height: 0;
          overflow: hidden;
          transition: all 0.5s ease;

          & > .place {
            display: flex;
            gap: 5px;
            align-items: flex-start;
            overflow: hidden;
            flex: 1;

            & > img {
              width: 25px;
              height: auto;
              object-fit: contain;
            }

            & > .place-content {
              display: flex;
              flex-direction: column;
              font-weight: 600;
              overflow: hidden;

              & > span{
                display: flex;
              }

              & > em {
                font-size: 0.8em;
                overflow: hidden;
                text-overflow: ellipsis;
                // white-space: nowrap;
                color: var(--color-text-note);

                & > span:not(:first-child) {
                  border-left: thin solid;
                  margin-left: 5px;
                  padding-left: 5px;
                }
              }

              & > .open-info{
                font-size: 13px;
                font-style: italic;
                color: var(--primary-color-1);
                align-self: flex-start;
              }
            }

            & .call-customer {
              color: #5f5e66;
              font-size: 15px;
              padding: 2px 7px;
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 5px;
              border: thin solid #ecebf3;
              font-weight: 600;
              white-space: nowrap;
              margin-left: 10px;
              width: auto;
              height: 30px;

              & > svg {
                width: 20px;
              }

              & > img {
                border-radius: 50%;
                border: thin solid #ecebf3;
                width: 20px;
              }
            }
          }

          & > .distance {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;

            & svg {
              color: var(--primary-color-2);
              font-size: 25px;
            }
          }

          
        }
        & .primary-info.expanded {
          height: fit-content;
          max-height: 100dvh;
          overflow: visible;
        }
        & > .primary-info {
          display: flex;
          max-height: 0;
          gap: 10px;
          padding: 0px 20px;
          margin: 0;
          flex-wrap: wrap;
          overflow: hidden;
          transition: all 0.5s ease;

          & > .stack-info.auto {
            width: fit-content;
            min-width: 48%;
            margin-right: auto;
          }
          & > .stack-info.full {
            width: 100%;
          }
          & > .stack-info.price {
            flex-direction: column;
            justify-content: space-between;
            gap: 5px;

            & .h-stack-content {
              white-space: nowrap;
              width: 100%;

              & .value {
                color: #393939;
                font-weight: 600;
              }
            }
            & .h-stack-content.last .value {
              font-size: 20px;
              font-weight: 700;
              color: var(--primary-color-2);
            }
          }
          & > .stack-info {
            display: flex;
            gap: 10px;
            align-items: flex-start;

            & > .icon-left {
              font-size: 25px;
              min-width: 25px;
              color: var(--primary-color-2);
            }

            & > .stack-content {
              display: flex;
              flex-direction: column;
              flex: 1;

              & > .label {
                font-size: 15px;
                font-weight: 500;
              }

              & > .value {
                color: #393939;
                font-weight: 600;
                white-space: break-spaces;
                line-height: normal;

                & > span::marker {
                  color: var(--primary-color-1);
                  font-size: 18px;
                }
                & > span {
                  display: list-item;
                }
              }
            }

            & > .h-stack-content {
              display: flex;
              justify-content: space-between;
              flex: 1;

              & > .label {
                color: #393939;
                font-size: 15px;
                font-weight: 600;
              }

              & > .value {
                color: var(--primary-color-1);
                font-weight: bold;
                font-size: 17px;
                white-space: break-spaces;
                line-height: normal;
              }
            }
          }
        }
      }

      & > .actions {
        padding: 0;
        display: flex;
        width: 100%;
        background: transparent;
        justify-content: flex-end;

        & > button {
          padding: 10px 35px;
          margin: 0 -10px;
          font-weight: bold;
          background: #ccc;
          clip-path: polygon(25px 0%, 100% 0%, calc(100% - 25px) 100%, 0% 100%);
        }

        & > button:last-child {
          margin-right: 0;
          padding-right: 20px;
          clip-path: polygon(25px 0%, 100% 0%, 100% 100%, 0% 100%);
        }

        & > button.cancel {
          color: white;
          background: rgb(196, 45, 19);
        }
        & > button.confirm {
          color: white;
          background: linear-gradient(45deg, #f86f64, var(--primary-color-1));
        }
        & > button.pick-up {
          color: white;
          background: linear-gradient(45deg, #64f895, #4ed0f0);
        }
        & > button.completed {
          color: white;
          background: #4e94f0;
        }

        & > .direction-button {
          padding: 5px 30px;
          background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
          color: white;
          width: fit-content;
        }
      }
    }
  }
}
