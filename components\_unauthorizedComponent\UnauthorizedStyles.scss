.unauthorized-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    align-items: center;
    justify-content: flex-start;
    z-index: 100;

    .access-denied {
        width: 100%;
        align-items: center !important;
        display: flex;
        flex-direction: column;
        gap: 10px;
        justify-content: center !important;
        font-size: 1.5em;
        text-align: center;
        padding: 10px;
        flex: 1;
    
        & img {
            width: 300px;
            height: 300px;
            border-radius: 0 !important;
            align-items: center !important;
            object-fit: cover;
            position: relative;
        }
    
        & span {
            color: var(--primary-color-1);
        }
    }
}

