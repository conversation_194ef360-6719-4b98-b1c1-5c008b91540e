<template>
	<v-overlay v-model="showSelectedProduct" location="bottom" contained :z-index="1001"
		v-on:click:outside="()=>{
			close()
		}"
		key="show_product_select" class="product-select-overlay-container"
		persistent content-class='product-selected-container' no-click-animation>
		<div class="product-selected-content" id="selected_product">
			<div class="close" v-on:click="() => {
				close()
			}">
				<Icon name="clarity:times-line" size="25"></Icon>
			</div>
			<img loading="lazy" :src="selectedProduct && selectedProduct.profile_picture
				? domainImage + selectedProduct.profile_picture
				: icon_for_product
				" :placeholder="icon_for_product" :alt="showTranslateProductName(selectedProduct)" :title="showTranslateProductName(selectedProduct)"
				id="selected_product_img" />
			<span class="name">{{ showTranslateProductName(selectedProduct) }}</span>
			<span class="price">
				{{
					(selectedProduct.price_off != null && selectedProduct.price_off < selectedProduct.price) ?
						formatCurrency(parseFloat(selectedProduct.price_off), selectedProduct.shop ?
							selectedProduct.shop.currency : selectedProduct.currency) : (parseFloat(selectedProduct.price) == 0 ||
								selectedProduct.price == null) ? $t('AddProductToCartComponent.gia_lien_he') :
							formatCurrency(parseFloat(selectedProduct.price), selectedProduct.shop ?
								selectedProduct.shop.currency : selectedProduct.currency) }} </span>
					<span class="origin-price"
						v-if="(selectedProduct.price_off != null && selectedProduct.price_off < selectedProduct.price)">
						{{
							(parseFloat(selectedProduct.price) == 0 || selectedProduct.price ==
								null)
								? $t('AddProductToCartComponent.gia_lien_he')
								: formatCurrency(parseFloat(selectedProduct.price), selectedProduct.shop ?
									selectedProduct.shop.currency : selectedProduct.currency)
						}}
					</span>
					<span class="current-in-cart">{{ $t('AddProductToCartComponent.trong_gio_dang_co') }}: <em>{{ currentQuantityInCart }}</em></span>
					<div class="h-stack actions">
						<div class="quantity-actions">
							<button class='quantity-button left'
								:disabled="!selectedProduct.quantity || selectedProduct.quantity <= 0" v-on:click="() => {
									selectedProduct.quantity = selectedProduct.quantity ? selectedProduct.quantity - 1 : 0
								}">
								<Icon name="iconoir:minus-circle" />
							</button>
							<span>{{ formatNumber(selectedProduct.quantity ? selectedProduct.quantity : 0) }}</span>
							<button class='quantity-button right' v-on:click="() => {
								selectedProduct.quantity = selectedProduct.quantity ? selectedProduct.quantity + 1 : 1;
							}">
								<Icon name="iconoir:plus-circle-solid" />
							</button>
						</div>
						<button class="add-to-cart-action" :disabled="!selectedProduct.quantity" v-on:click="async () => {
							await checkAgeAndAddProductToCart();
							// await addProductToCart()

						}">
							<span class="total-price">
								{{ getTotalPriceSelected() }}
							</span>
							<span class="add-action">{{ $t('AddProductToCartComponent.them_vao_gio') }}</span>
						</button>
					</div>
		</div>

	</v-overlay>

	<ResetCartComponent 
		v-if="showModalConfirmResetCart" 
		v-on:accept="() => { 
			resetCartAndAddProduct(); 
		}" 
		v-on:reject="() => {
			showModalConfirmResetCart = false;
			close()
		}" 
		v-on:close="() => {
			showModalConfirmResetCart = false;
		}"
	></ResetCartComponent>
	<Confirm18AgeComponent v-if="showModalConfirm18Age" v-on:accept="() => {
		showModalConfirm18Age = false;
		addProductToCart();
	}" v-on:reject="() => {
		showModalConfirm18Age = false;
	}" v-on:close="() => {
		showModalConfirm18Age = false;
	}"></Confirm18AgeComponent>
</template>

<script lang="ts" setup>

import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";

import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, showTranslateProductName } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";
import { ShopService } from "~/services/shopService/shopService";
import ResetCartComponent from "~/components/resetCart/ResetCartComponent.vue";
import Confirm18AgeComponent from "~/components/confirm18Age/Confirm18AgeComponent.vue";

const { t } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.CartComponent')
});
const emit = defineEmits(['close']);
const props = defineProps({
	selectedProduct: null,
	noteForAddItem: null,
})
const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();

var cartData = ref();
var currentQuantityInCart = ref(0);
var selectedProduct = ref(props.selectedProduct ? props.selectedProduct : null);
var noteToProduct = ref(props.noteForAddItem ? props.noteForAddItem : null);
var showModalConfirmResetCart = ref(false);
var showModalConfirm18Age = ref(false);
var showSelectedProduct = ref(false);

onMounted(() => {
	let cartData$ = JSON.parse(
		localStorage.getItem(appConst.storageKey.cart) as string
	);
	cartData.value = cartData$;
	checkSelectedProductQuantity()
	showSelectedProduct.value = true;
})

function close(value?: any) {
	showSelectedProduct.value = false;
	emit('close', value);
}
async function checkSelectedProductQuantity() {
	selectedProduct.value.quantity = 1;
	let cartTemp = JSON.parse(JSON.stringify(cartData.value));
	if (cartTemp) {
		let index = cartTemp.findIndex((e: any) => {
			return e.product_id == selectedProduct.value.id
		});
		// selectedProduct.value.quantity = index != -1 ? cartTemp[index].quantity : 1;
		currentQuantityInCart.value = index != -1 ? cartTemp[index].quantity : 0;
	}
}
async function checkAge() {
	let checkAge = await JSON.parse(JSON.stringify(localStorage.getItem(appConst.storageKey.confirm18Age)));
	return checkAge
}

async function checkAgeAndAddProductToCart() {
	let check = await checkAge();
	if (!check && selectedProduct.value.is_alcohol) {
		showModalConfirm18Age.value = true;
	}
	else {
		addProductToCart();
	}
}
async function addProductToCart() {
	let cartTemp = JSON.parse(JSON.stringify(cartData.value)) || [];
	if (cartTemp && cartTemp.length) { }
	let index = cartTemp.findIndex((e: any) => {
		return e.product_id == selectedProduct.value.id
	});
	if (index != -1 && selectedProduct.value.quantity) {
		cartTemp[index].quantity += selectedProduct.value.quantity;
		cartTemp[index].notes = noteToProduct.value?.length ? noteToProduct.value : "";
		if (selectedProduct.value.price_off != null && (selectedProduct.value.price_off < selectedProduct.value.price)) {
			cartTemp[index].price = selectedProduct.value.quantity * selectedProduct.value.price_off
		}
		else if (selectedProduct.value.price != 0 && selectedProduct.value.price != null) {
			cartTemp[index].price = selectedProduct.value.quantity * selectedProduct.value.price
		}
		else cartTemp[index].price = null;
		setTimeout(() => {
			close()
		}, 500);
		runAnimation()
	}
	else if (index != -1 && !selectedProduct.value.quantity) {
		cartTemp.splice(index, 1);
		setTimeout(() => {
			close()
		}, 500);
		runAnimation()
	}
	else if (index == -1) {
		let priceTemp: any;
		if (selectedProduct.value.price_off != null && selectedProduct.value.price_off < selectedProduct.value.price) {
			priceTemp = selectedProduct.value.price_off * selectedProduct.value.quantity
		}
		else if (selectedProduct.value.price != 0 && selectedProduct.value.price != null) {
			priceTemp = selectedProduct.value.price * selectedProduct.value.quantity
		}
		else priceTemp = null;
		let cartItem: CartDto = {
			product_id: selectedProduct.value.id,
			product: selectedProduct.value,
			shop_id: selectedProduct.value.shop_id ? selectedProduct.value.shop_id : null,
			shop: selectedProduct.value.shop ? JSON.parse(JSON.stringify(selectedProduct.value.shop)) : null,
			quantity: selectedProduct.value.quantity,
			price: priceTemp,
			notes: noteToProduct.value?.length ? noteToProduct.value : ""
		};
		if (!cartTemp.length || (cartTemp.length && cartTemp[0].shop_id == cartItem.shop_id)) {
			cartTemp.push(cartItem);
			setTimeout(() => {
				close()
			}, 500);
			runAnimation()
		}
		else if (cartTemp.length && cartTemp[0].shop_id != cartItem.shop_id) {
			showModalConfirmResetCart.value = true
			console.log("khác cửa hàng")
		}
	}
	cartData.value = JSON.parse(JSON.stringify(cartTemp));
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));

	nuxtApp.$emit(appConst.event_key.cart_change);
}
async function resetCartAndAddProduct() {
	let cartItem: CartDto = {
		product_id: selectedProduct.value.id,
		product: selectedProduct.value,
		shop_id: selectedProduct.value.shop ? selectedProduct.value.shop.id : null,
		shop: selectedProduct.value.shop ? selectedProduct.value.shop : null,
		quantity: selectedProduct.value.quantity,
		price: selectedProduct.value.quantity * selectedProduct.value.price,
		notes: ""
	};

	let cartTemp = [cartItem];
	cartData.value = JSON.parse(JSON.stringify(cartTemp));
	localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));
	
	showModalConfirmResetCart.value = false;
	
	nuxtApp.$emit(appConst.event_key.cart_change);
	setTimeout(() => {
		close()
	}, 500);
	runAnimation()
}

function getTotalPriceSelected() {
	if (selectedProduct.value && selectedProduct.value.quantity) {
		if (selectedProduct.value.price_off != null && (selectedProduct.value.price_off < selectedProduct.value.price)) {
			return formatCurrency(parseFloat(selectedProduct.value.price_off) * selectedProduct.value.quantity, selectedProduct.value.shop ? selectedProduct.value.shop.currency : selectedProduct.value.currency)
		}
		else if (parseFloat(selectedProduct.value.price) == 0 || selectedProduct.value.price == null) {
			return t('AddProductToCartComponent.gia_lien_he')
		}
		else return formatCurrency(parseFloat(selectedProduct.value.price) * selectedProduct.value.quantity, selectedProduct.value.shop ? selectedProduct.value.shop.currency : selectedProduct.value.currency)
	}

	return formatCurrency(0);
}

function runAnimation() {
	nuxtApp.$emit(appConst.event_key.cart_add_animation, {
		containerId: 'selected_product',
		imgId: 'selected_product_img'
	})
}

</script>

<style lang="scss" src="./AddProductToCartStyles.scss"></style>