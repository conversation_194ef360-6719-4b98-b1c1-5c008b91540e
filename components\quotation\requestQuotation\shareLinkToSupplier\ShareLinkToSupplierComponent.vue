<template>
	<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
		content-class="v-stack share-link-to-supplier-container" :click-to-close="true" v-model="showModal"
		contentTransition="vfm-fade" v-on:closed="() => {
			close()
		}">
		<HeaderComponent :title="$t('ShareLinkToSupplierComponent.duong_dan_phan_hoi')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="close-button" v-on:click="() => {
					close();
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</HeaderComponent>
		<div class="share-link-to-supplier-content">
			<div class="share-link-item" v-for="itemSup in dataShareLink">
				<v-chip class="supplier-name">
					{{ itemSup.supplier?.name }}
				</v-chip>
				<div class="h-stack link-copy">
					<span>{{ itemSup.link }}</span>
					<UseClipboard v-slot="{ copy, copied }" :source="itemSup.link">
						<v-btn @click="()=>{
							handleCopy(itemSup.link);
							copy()
						}" :variant="copied ? 'tonal' : 'outlined'">
							{{ copied ? $t('ShareLinkToSupplierComponent.da_sao_chep') :
								$t('ShareLinkToSupplierComponent.sao_chep') }}
						</v-btn>
					</UseClipboard>
				</div>

			</div>

		</div>
	</VueFinalModal>
</template>

<script setup lang="ts">
import { UseClipboard } from '@vueuse/components';
import { VueFinalModal } from 'vue-final-modal';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber } from '~/assets/AppConst';
import appRoute from '~/assets/appRoute';
const props = defineProps({
	data: null
});
const emit = defineEmits(['close']);
const { t } = useI18n()
const nuxtApp = useNuxtApp();

var showModal = ref(false);
var webInApp = ref(null as any);

var copiedLink = ref(false);
var copiedTimeout: any;
var clipboard = useClipboard();

var dataShareLink = ref<any>();
onMounted(async () => {
	dataShareLink.value = props.data?.map((item: any) => {
		return {
			supplier: item.supplier,
			link: `${domain}${appRoute.ReplyQuotationComponent.replaceAll(':quotation_id', item.id)}`
		}
	})
	showModal.value = true;
});

function handleCopy(link: string, ){
    if (webInApp.value) {
		nuxtApp.$emit(appConst.event_key.send_request_to_app, {
			action: appConst.webToAppAction.copyToClipboard,
			data: link
		})
	}
}

function close() {
	showModal.value = false
	emit('close')
}

</script>

<style lang="scss" src="./ShareLinkToSupplierStyles.scss"></style>