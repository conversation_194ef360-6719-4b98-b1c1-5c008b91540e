<template>
	<v-overlay v-model="showFindShipper" location="bottom" contained :z-index="1001" key="show_find_shipper"
		class="find-shipper-overlay-container" persistent content-class='find-shipper-container' no-click-animation>
		<SubHeaderV2Component :title="$props.title ? $props.title : $t('AppRouteTitle.FindShipperComponent')">
			<template v-slot:header_left></template>
			<template v-slot:header_right>
				<button class="refresh-find-shipper" v-on:click="() => {
					refreshFindShipper()
				}">
					<Icon name="lets-icons:refresh" size="25"></Icon>
				</button>
				<button class="close" v-on:click="() => {
					close()
				}">
					<Icon name="clarity:times-line" size="25"></Icon>
				</button>
			</template>
		</SubHeaderV2Component>
		<div class="find-shipper-content-container">
			<div class="shipper-list-container">

				<client-only>
					<div class="leaflet-map-container">
						<LMap id="leaflet_map_find_shipper" v-on:ready="(e: any) => {
							leafletMap = e;
							leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
							initLeafletMap();
						}" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }" :world-copy-jump="true"
							:use-global-leaflet="true">
							<!-- <LControlZoom position="bottomright"></LControlZoom> -->
							<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
								:use-global-leaflet="true" :max-zoom="appConst.leafletMapTileOption.maxZoom"
								:min-zoom="5" layer-type="base" :name="$t('AroundComponent.google_map')" />



							<!-- <span class="current-location-leaflet" :title="$t('AroundComponent.vi_tri_cua_ban')" v-on:click="(e: any) => {
							gotoCurrentLocationLeaflet();
						}">
							<Icon name="line-md:my-location-loop" class="my-location-icon" />
						</span>
						<div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
							data-placement="right" :title="$t('AroundComponent.nhan_de_chuyen_loai_map')" v-bind:style="{
								backgroundImage: `url(` + buttonMapTileBackgound + `)`,
							}" v-on:click="(event: any) => {
								if (event.isTrusted) {
									if (leafletMapTileUrl == appConst.leafletMapTileUrl.roadmap) {
										leafletMapTileUrl = appConst.leafletMapTileUrl.hyprid;
										mapTypeTitle = $t('AroundComponent.ve_tinh');
										mapType = 'hyprid';
										buttonMapTileBackgound = map_sateline;
									} else if (
										leafletMapTileUrl == appConst.leafletMapTileUrl.hyprid
									) {
										leafletMapTileUrl = appConst.leafletMapTileUrl.streetmap;
										mapTypeTitle = $t('AroundComponent.co_dien');
										mapType = 'hyprid';
										buttonMapTileBackgound = map_streetmap;
									} else if (
										leafletMapTileUrl == appConst.leafletMapTileUrl.streetmap
									) {
										leafletMapTileUrl = appConst.leafletMapTileUrl.roadmap;
										mapTypeTitle = $t('AroundComponent.ve_tinh_va_nhan');
										mapType = 'roadmap';
										buttonMapTileBackgound = map_sateline;
									}
									// setAreaBoundLeaflet(currentLocaleObject);
								} else event.preventDefault();
							}">
							<span>{{ mapTypeTitle }}</span>
						</div> -->
						</LMap>
						<div class="button-group">
							<button class="find-area" :disabled="finding" v-on:click="() => { findShipper() }">
								<Icon name="eos-icons:loading" v-if="finding"></Icon>
								<span>{{ $t('FindShipperComponent.tim_khu_vuc_nay') }}</span>
							</button>
							<v-select class="custom-v-select mt-2 radius-select" label="" :menu-props="{
								contentClass: 'text-center'
							}" :placeholder="$t('FindShipperComponent.ban_kinh')" v-model="radius" :item-value="'value'" :menu-icon="''"
								:items="listRadiusOptions" variant="plain">
								<template v-slot:item="{ props, item }">
									<v-list-item v-bind="props" :title="item.raw.name"></v-list-item>
								</template>
								<template v-slot:selection="{ item }">
									<span><em class="radius-label">{{ $t('FindShipperComponent.ban_kinh') }}</em>: {{
										item.raw.name }}</span>
								</template>
								<template v-slot:append-inner></template>
							</v-select>
						</div>
					</div>

				</client-only>
				<div class="shipper-list-absolute" :class="{ 'expanded': expandList }">
					<button class="expand-button" v-on:click="() => {
						expandList = true
					}">
						<Icon :name="'ph:user-list-duotone'"></Icon>
					</button>
					<button class="close-expand-button" v-on:click="() => {
						expandList = false
					}">
						<Icon :name="'mdi:arrow-right'"></Icon>
					</button>
					<div class="shipper-list-content-container" v-if="listShipperCount">

						<div class="shipper-item-container" v-for="(itemShipper, index) in listShipper" v-on:click="() => {
							if (!expandList) {
								submit(itemShipper)
							}
						}" :id="`shipper_item_${index}`">


							<div class="shipper-info">
								<div class="h-stack info">
									<img :src="itemShipper?.profile_picture
										? ((appConst.provider_img_domain.some(e => itemShipper?.profile_picture?.includes(e))) ? itemShipper?.profile_picture : (domainImage + itemShipper?.profile_picture))
										: non_avatar" alt="" class="shipper-avt" v-on:error="(event: any) => {
											event.target.src = icon_for_broken_image;
										}" />
									<div class="v-stack">
										<span class="name">{{ itemShipper.name
										}}</span>
										<span class="online-status" :class="{
											'online': itemShipper.user_status == appConst.user_status.online,
											'offline': itemShipper.user_status == appConst.user_status.offline,
											'busy': itemShipper.user_status == appConst.user_status.busy
										}">
											<div class="online-dot"
												v-if="itemShipper.user_status == appConst.user_status.online">
											</div>
											{{ getResponseStatusText(itemShipper) }}
										</span>
									</div>
								</div>
								<div class="h-stack info">
									<span class="rating">
										<Icon name="ic:round-star"></Icon> {{ itemShipper.total_rating != null ?
											itemShipper.total_rating : 5 }}
									</span>
									<span class="gender">
										<Icon name="ion:male-outline" v-if="itemShipper.gender == true" class="male">
										</Icon>
										<Icon name="ion:female-outline" v-if="itemShipper.gender == false"
											class="female">
										</Icon>
										<Icon name="ion:male-female-outline"
											v-if="itemShipper.gender != false && itemShipper.gender != true"
											class="other">
										</Icon>
										{{ itemShipper.gender == true ? $t('FindShipperComponent.nam') :
											itemShipper.gender
												==
												false
												?
												$t('FindShipperComponent.nu') : $t('FindShipperComponent.khac') }}
									</span>
									<span class="distance">
										<Icon name="ri:map-pin-user-line"></Icon> {{
											parseFloat(itemShipper.distance) >
												1000
												?
												(parseFloat(itemShipper.distance) / 1000).toFixed(1)
												:
												parseFloat(itemShipper.distance).toFixed(0) }}
										<em>{{ parseFloat(itemShipper.distance) > 1000 ? ' km' : ' m' }}</em>
									</span>
								</div>

							</div>

							<div class="actions">
								<button class="add-wishlist-button" :class="{ 'active': itemShipper.liked }"
									:title="itemShipper.liked ? $t('FindShipperComponent.da_thich') : $t('FindShipperComponent.them_vao_yeu_thich')"
									v-on:click="() => {
										itemShipper.liked = !itemShipper.liked;
										setDriverToWishlish(itemShipper.id, itemShipper.liked)
									}">
									<Icon name="ri:heart-fill" v-if="itemShipper.liked"></Icon>
									<Icon name="ri:heart-line" v-else></Icon>
									<!-- {{ itemShipper.liked ? $t('FindShipperComponent.da_thich') :
										$t('FindShipperComponent.yeu_thich') }} -->
								</button>
								<nuxt-link :to="itemShipper.phone ? `tel:${validPhone(itemShipper.phone)}` : '#'"
									:target="webInApp ? '_blank' : ''" class="phone" 
									:class="{
										'disabled': !itemShipper.phone?.length
									}"
								>
									<Icon name="material-symbols:call"></Icon>
									<!-- {{ $t('FindShipperComponent.goi_dien') }} -->
									  {{ itemShipper.phone?.length ? validPhone(itemShipper.phone) : $t('FindShipperComponent.chua_cap_sdt') }}
								</nuxt-link>
								<button class="select-shipper" v-on:click="() => {
									submit(itemShipper)
								}">
									{{ $t('FindShipperComponent.chon') }}
								</button>
							</div>
						</div>
					</div>
					<div v-else class="shipper-list-content-container shipper-none-list">
						<img :src="not_found_shipper" />
						<span>{{ $t('FindShipperComponent.khong_tim_thay_shipper') }}</span>
					</div>
				</div>


			</div>

		</div>
	</v-overlay>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { appConst, domainImage, formatCurrency, formatNumber, showTranslateProductName, showTranslateProductDescription, validPhone } from '~/assets/AppConst';
import { DeliveryService } from '~/services/orderService/deliveryService';

import not_found_shipper from '~/assets/image/not-found-shipper.webp'
import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import type { MarkerClusterGroup } from 'leaflet';
import marker_location_icon from "~/assets/image/marker-location.png";
import driver_marker_location_icon from "~/assets/image/driver-marker.png"
import shipper_avt from '~/assets/image/shipper-avt.png'
import { InteractionService } from '~/services/interactionService/interactionService';
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const props = defineProps({
	title: null,
	object_id_from: null,
	object_id_type: null,
	latitude: null,
	longitude: null
});
const nuxtApp = useNuxtApp();

var deliveryService = new DeliveryService();
var interactionService = new InteractionService();
var webInApp = ref(null as any);

var showFindShipper = ref(false);
var listShipper = ref([] as any);
var listShipperCount = ref(0);

var buttonMapTileBackgound = ref(map_sateline);
var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: MarkerClusterGroup;
var markerLeaflets = ref([] as any);
var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('AroundComponent.ve_tinh_va_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);

var expandList = ref(true);
var finding = ref(false);

var radius = ref(25);
var listRadiusOptions = [
	{
		value: 5,
		name: '5 km'
	},
	{
		value: 15,
		name: '15 km'
	},
	{
		value: 25,
		name: '25 km'
	},
	{
		value: 50,
		name: '50 km'
	},
	{
		value: 75,
		name: '75 km'
	}
]
onMounted(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	getListShipper();
	showFindShipper.value = true;
})

function getListShipper() {
	finding.value = true;
	deliveryService.findShipper(props.object_id_from, props.latitude, props.longitude, radius.value).then(res => {
		listShipper.value = res.body.data.result;
		listShipperCount.value = res.body.data.count;
		setTimeout(() => {
			finding.value = false;
		}, 1500);

		showShipperOnMap()
	})
}

async function initLeafletMap() {
	leafletMap.setView([props.latitude ?? appConst.defaultCoordinate.latitude, props.longitude ?? appConst.defaultCoordinate.longitude], 17)
	markersCluster = new nuxtApp.$L.MarkerClusterGroup({
		maxClusterRadius: 0,
		disableClusteringAtZoom: 20,
		iconCreateFunction: (cluster) => createClusterElement(cluster),
	});
	leafletMap.addLayer(markersCluster)

	setCurrentLocationLeaflet();
	showShipperOnMap()
}

function focusShopLocation() {
	leafletMap.flyTo([props.latitude, props.longitude], leafletMap.getZoom(), {
		duration: 1
	})
}
function resetMarkersLeaflet() {
	if (markerLeaflets.value.length) {
		markerLeaflets.value = [];
		markersCluster.clearLayers();
	}
}
async function showShipperOnMap() {
	console.log("show shipper")
	let listShipper$ = JSON.parse(JSON.stringify(listShipper.value));

	await resetMarkersLeaflet();
	listShipper$.forEach(async (element: any, index: number) => {

		let iconDriver = new nuxtApp.$L.Icon({
			iconUrl: driver_marker_location_icon,
			iconSize: appConst.markerCustom.driverIcon.size,
			className: appConst.markerCustom.driverIcon.class,
		});
		localeMarkerLeaflet = nuxtApp.$L.marker([element.latitude ?? appConst.defaultCoordinate.latitude, element.longitude ?? appConst.defaultCoordinate.longitude], {
			icon: iconDriver,
			rotationOrigin: appConst.markerCustom.driverIcon.rotatePosition,
			rotationAngle: element.value ?? Math.floor(Math.random() * 361)
		}).bindTooltip(listShipper$[index].name, {
			direction: 'bottom',
			permanent: true,
			interactive: true,
			className: "tooltip-leaflet-own",
		});;
		markerLeaflets.value.push(localeMarkerLeaflet);

		// let markerMap = nuxtApp.$L.marker([element.latitude, element.longitude], {
		// 	icon: nuxtApp.$L.divIcon({
		// 		className: "img-icon",
		// 		html: `
		//         <div class='my-marker-image'>
		//             ${element.profile_picture
		// 				? `<img 
		//                         src='${element?.profile_picture
		// 					? ((appConst.provider_img_domain.some(e => element?.profile_picture?.includes(e))) ? element?.profile_picture : (domainImage + element?.profile_picture))
		// 					: non_avatar}'
		//                     />`
		// 				: `<img
		//                     [hidden]=${!!element.logo}
		//                     src='${non_avatar}'
		//                     />`
		// 			} 
		//         </div>

		//         <div class='after'></div>
		//         `,
		// 		iconUrl: (element.banner ? domainImage + element.banner : shipper_avt),
		// 		iconSize: [40, 50],
		// 	}),
		// });

		// markerLeaflets.value.push(markerMap);

		markerLeaflets.value[index].addEventListener("mouseover", () => {
			// markerLeaflets.value[index].bindTooltip(listShipper$[index].name, {
			// 	direction: 'bottom',
			// 	permanent: true,
			// 	interactive: true,
			// 	className: "tooltip-leaflet-own",
			// });
			expandList.value = true;
			setTimeout(() => {
				let el = document.getElementById(`shipper_item_${index}`);
				el?.scrollIntoView({
					behavior: 'smooth',
					block: 'center'
				})
				el?.classList.add('active');
			}, 500);


		});

		markerLeaflets.value[index].addEventListener("mouseout", () => {
			// markerLeaflets.value[index].closeTooltip();
			setTimeout(() => {
				document.getElementById(`shipper_item_${index}`)?.classList.remove('active');
			}, 500);
		});
	});
	markersCluster.addLayers(markerLeaflets.value);
	//   markerLeaflets.value.forEach((mark:any)=>{
	// 	mark.openPopup();
	//   })
}

async function autoZoomMapByRadius() {
	let circle = new nuxtApp.$L.Circle(leafletMap.getCenter(), radius.value * 1000).addTo(leafletMap);
	leafletMap.fitBounds(circle.getBounds(), {
		paddingBottomRight: [-100, -100],
		paddingTopLeft: [-100, -100]
	});
	circle.removeFrom(leafletMap)
}
async function setCurrentLocationLeaflet() {
	// await getCurrentLocationOfUser();
	setLocationLeafletMarker(props.latitude, props.longitude);
}
function setLocationLeafletMarker(lat: number, lng: number) {
	if (localeMarkerLeaflet) {
		localeMarkerLeaflet.remove();
	}

	localeMarkerLeaflet = nuxtApp.$L.marker([lat, lng], {
		icon: new nuxtApp.$L.Icon({
			iconUrl: marker_location_icon,
			iconSize: appConst.markerCustom.defaultIcon.size,
			className: appConst.markerCustom.defaultIcon.class,
		}),
		rotationOrigin: 'center 75%'
	});
	localeMarkerLeaflet.addTo(leafletMap);
}
async function gotoCurrentLocationLeaflet(event?: Event) {
	if (!event || event.isTrusted == true) {
		leafletMap.flyTo([props.latitude, props.longitude], 17);
	}
}

function createClusterElement(cluster: any) {
	return nuxtApp.$L.divIcon({
		html: '<div class="my-cluster-icon red-shadow-icon">'
			// + '<span>' 
			+ cluster.getChildCount()
			// + '</span>'
			+ '</div>'
	});
}

function refreshFindShipper() {
	getListShipper();
	focusShopLocation()
}

function findShipper() {
	finding.value = true;
	deliveryService.findShipper(props.object_id_from, leafletMap.getCenter().lat, leafletMap.getCenter().lng, radius.value).then(res => {
		listShipper.value = res.body.data.result;
		listShipperCount.value = res.body.data.count;
		setTimeout(() => {
			finding.value = false;
		}, 1500);
		showShipperOnMap();
		autoZoomMapByRadius()
	})
}

function close(value?: any) {
	showFindShipper.value = false;
	emit('close', value);
}

function submit(shipper: any) {
	showFindShipper.value = false;
	emit('submit', JSON.parse(JSON.stringify(shipper)))
}

function getNameShipper(name: string) {
	const words = name.trim().split(' '); // Split the name into words
	return words[words.length - 1];
}
function setDriverToWishlish(driver_id: any, value: any) {
	interactionService.createOrDelete({
		interaction_type: appConst.interactionType.like,
		object_id_from: props.object_id_from,
		object_type_from: props.object_id_type,
		object_id_to: driver_id,
		object_type_to: appConst.object_type.user as any
	}).then(res => {
		if (res.status != HttpStatusCode.Ok) {
			let indexCurr = listShipper.value.findIndex(function (e: any) {
				return e.id == driver_id
			})
			listShipper.value[indexCurr].liked = !value;
		}
	})
}

function getResponseStatusText(driver_info: any) {
	if (driver_info.user_status == appConst.user_status.online) {
		return t('FindShipperComponent.dang_online')
	}
	if (driver_info.user_status == appConst.user_status.busy) {
		return t('FindShipperComponent.dang_ban')
	}
	if (driver_info.user_status == appConst.user_status.offline) {
		if (moment().diff(moment(driver_info.last_action_at), 'minutes') < 5) {
			return t('FindShipperComponent.vua_moi_offline')
		}
		if (moment().diff(moment(driver_info.last_action_at), 'minutes') >= 5 && moment().diff(moment(driver_info.last_action_at), 'minutes') < 60) {
			return t('FindShipperComponent.online_x_phut_truoc', { x: moment().diff(moment(driver_info.last_action_at), 'minutes') })
		}
		if (moment().diff(moment(driver_info.last_action_at), 'minutes') >= 60 && moment().diff(moment(driver_info.last_action_at), 'hours') < 24) {
			return t('FindShipperComponent.online_x_gio_truoc', { x: moment().diff(moment(driver_info.last_action_at), 'hours') })
		}
		if (moment().diff(moment(driver_info.last_action_at), 'days') >= 1) {
			return t('FindShipperComponent.online_x_ngay_truoc', { x: moment().diff(moment(driver_info.last_action_at), 'days') })
		}
		return t('FindShipperComponent.da_offline')
	}
	return t('FindShipperComponent.da_offline')
}
</script>

<style lang="scss" src="./FindShipperStyles.scss"></style>