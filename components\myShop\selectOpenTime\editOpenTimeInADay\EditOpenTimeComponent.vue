<template>
    <VueFinalModal class="my-modal-container" :modal-id="'edit_open_time'" :keep-overlay="true" :hide-overlay="false"
        :hide-overlay-on-blur="false" content-class="v-stack form-modal edit-open-time-container" :click-to-close="false"
        :esc-to-close="false" v-on:before-close="() => {
            showEditOpenTimeModal = false
        }" v-model="showEditOpenTimeModal" contentTransition="vfm-slide-down">
        <HeaderComponent :title="$t('EditOpenTimeComponent.chon_ngay_va_gio')">
            <template v-slot:header_left></template>
            <template v-slot:header_right>

                <button class="close" v-on:click="() => {
                    close()
                }">
                    <Icon name="clarity:times-line" size="25"></Icon>
                </button>
            </template>
        </HeaderComponent>
        <div class="edit-open-time-content-container">
            <div class="days-content">
                <button class="day-select" :class="{ 'active': checkDaySelect(day) }" v-on:click="setDaySelect(day)"
                    v-for="day in days">
                    {{ $t(`DayInWeekSort.${day}`) }}
                </button>
            </div>
            <div class="times-content">
                <div class="time-inputs" v-for="(itemTime, index) in editTimes">
                    <div class='v-stack'>
                        <span class='label required'>
                            {{ $t('EditOpenTimeComponent.gio_mo_cua') }}
                        </span>
                        <button class="input-time" v-on:click="() => {
                            selectedTime = {
                                index: index,
                                type: 'open'
                            }
                            showPickTimeModal = true
                        }">{{ itemTime[0] }}</button>
                        <!-- <VueDatePicker 
                            class='input-time'
                            :model-value="{
                                hours: itemTime[0].split(':')[0],
                                minutes: itemTime[0].split(':')[1]
                            }"
                            position="left"
                            :auto-apply="true"
                            @open="openTimeEdit = true"
                            @closed="openTimeEdit = false"
                            @update:model-value="(e:any)=>{
                                itemTime[0] = moment().hours(e.hours).minutes(e.minutes).format('HH:mm');
                            }"
                            :clearable="false"
                            :cancelText="t('EditOpenTimeComponent.thoat')" 
                            :selectText="t('EditOpenTimeComponent.chon')"
                            time-picker 
                            teleport-center
                        /> -->
                    </div>
                    <div class='v-stack'>
                        <span class='label required'>
                            {{ $t('EditOpenTimeComponent.gio_dong_cua') }}
                        </span>
                        <button class="input-time" v-on:click="() => {
                            selectedTime = {
                                index: index,
                                type: 'close'
                            }
                            showPickTimeModal = true
                        }">{{ itemTime[1] }}</button>
                        <!-- <VueDatePicker 
                            class='input-time'
                            :model-value="{
                                hours: itemTime[1].split(':')[0],
                                minutes: itemTime[1].split(':')[1]
                            }"
                            position="right"
                            :auto-apply="true"
                            @update:model-value="(e:any)=>{
                                itemTime[1] = moment().hours(e.hours).minutes(e.minutes).format('HH:mm');
                            }"
                            @open="openTimeEdit = true"
                            @blur="openTimeEdit = false"
                            :clearable="false"
                            :cancelText="t('EditOpenTimeComponent.thoat')" 
                            :selectText="t('EditOpenTimeComponent.chon')"
                            time-picker 
                            teleport-center
                        /> -->
                        <!-- <input :title="$t('EditOpenTimeComponent.gio_dong_cua')" name='customer-name' maxLength=255
                            autoComplete="off" class='input-time'
                            :placeholder="$t('EditOpenTimeComponent.thoi_gian_placeholder')"
                            :value="itemTime[1]" v-on:input="($event: any) => {
                                itemTime[1] = $event.target.value;
                            }"/> -->
                    </div>
                    <button class="delete-item-time" v-on:click="() => {
                        deleteTime(index)
                    }">
                        <Icon name="clarity:times-line"></Icon>
                    </button>
                </div>
                <div v-if="!editTimes?.length" class="none-time">
                    {{ $t('EditOpenTimeComponent.chua_chon_gio_mo_cua') }}
                </div>
                <button class="add-time" v-on:click="() => {
                    addTime()
                }">
                    <Icon name="material-symbols:more-time-rounded"></Icon>
                    {{ $t('EditOpenTimeComponent.them_gio') }}
                </button>


            </div>

        </div>
        <div class='h-stack action-buttons'>
            <button class='cancel-button' :disabled="openTimeEdit" v-on:click="() => close()">
                {{ $t('EditOpenTimeComponent.huy') }}
            </button>
            <button class='save-button' :disabled="openTimeEdit" v-on:click="() => submit()">
                <span>{{ $t('EditOpenTimeComponent.xong') }}</span>
            </button>
        </div>

        <HourMinutePickerComponent v-if="showPickTimeModal" :startHour="0" :endHour="23"
            :initialHour="editTimes[selectedTime.index][selectedTime.type == 'open' ? 0 : 1].split(':')[0]"
            :initialMinute="editTimes[selectedTime.index][selectedTime.type == 'open' ? 0 : 1].split(':')[1]"
            :stepMinute="5"
            v-on:close="() => {
                showPickTimeModal = false
                selectedTime = {
                    index: 0,
                    type: 'open'
                };
            }" v-on:submit="(e: any) => {
                showPickTimeModal = false;
                editTimes[selectedTime.index][selectedTime.type == 'open' ? 0 : 1] = e;
                selectedTime = {
                    index: 0,
                    type: 'open'
                };

            }" />
    </VueFinalModal>


</template>
<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, domainImage, formatCurrency, formatNumber } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AgentService } from '~/services/agentService/agentService';
import VueDatePicker from '@vuepic/vue-datepicker';

const router = useRouter();
const route = useRoute();
const emit = defineEmits(['closeEdit', 'submit']);
const props = defineProps({
    title: null,
    datas: null,
})
var nuxtApp = useNuxtApp();
const { t, locale } = useI18n();
var showEditOpenTimeModal = ref(false);
var showPickTimeModal = ref(false)
var openTimeEdit = ref(false);
var editDays = ref([] as any);
var editTimes = ref([] as any);

var selectedTime = ref({
    index: 0,
    type: 'open' // open or close
});
const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

onBeforeUnmount(() => {
})
onMounted(async () => {
    editDays.value = props.datas.days ? props.datas.days : days;
    editTimes.value = props.datas.datas ? props.datas.datas : [];
    showEditOpenTimeModal.value = true;
})
onUpdated(() => {
})

function checkDaySelect(day: any) {
    return editDays.value.indexOf(day) != -1 ? true : false
}
function setDaySelect(day: any) {
    let index = editDays.value.indexOf(day);
    if (index == -1) {
        editDays.value.push(day)
    }
    else {
        editDays.value.splice(index, 1)
    }
}
function addTime() {
    let time = [moment().hours(8).minutes(0).format("HH:mm"), moment().hours(21).minutes(0).format("HH:mm")];
    editTimes.value.push(time)
}
function deleteTime(index: any) {
    editTimes.value.splice(index, 1)
}

function close(value?: any) {
    emit('closeEdit', value);
}

function submit() {
    let value = {
        days: editDays.value,
        datas: editTimes.value
    }
    emit('submit', value);
}
</script>

<style lang="scss" src="./EditOpenTimeStyles.scss"></style>