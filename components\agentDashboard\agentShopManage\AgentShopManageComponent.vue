<template>
    <div class="public-container" id="agent_shop_manage_content" v-on:scroll="() => {
        listScroll()
    }">
        <div class="agent-shop-manage-container">

            <SubHeaderV2Component :title="$t('AppRouteTitle.AgentShopManageComponent')">
                <template v-slot:header_right>
                    <button class="refresh" v-on:click="async () => {
                        await refreshShops()
                    }" :disabled="shopDataRefreshing">
                        <Icon name="eos-icons:loading" v-if="shopDataRefreshing" />
                        <Icon name="lets-icons:refresh" v-else />
                    </button>
                </template>
            </SubHeaderV2Component>
            <div class="agent-shop-manage-content">
                <div class="h-stack search-bar-container">
                    <button class="search-button" :disabled="searchLoading" v-on:click="searchShop()">
                        <Icon name="ion:search" size="20" v-show="!searchLoading" />
                        <Icon name="eos-icons:loading" size="20" v-show="searchLoading" />
                    </button>

                    <input type="search" class="search-input-container" :value="search"
                        :maxlength="appConst.max_text_short"
                        :placeholder="$t('AgentShopManageComponent.tim_cua_hang')" v-on:input="async ($event: any) => {
                            search = $event.target.value;
                            // searchOrders();

                            searchShop();
                        }" />
                </div>
                <span class="list-shop-amount">
                    {{ $t('AgentShopManageComponent.hien_thi', {
                        current: listShopManaged?.length ?? 0,
                        total: listShopManagedCount ?? 0
                    }) }}
                </span>
                <div class="empty-shop-list"
                    v-if="(!listShopManaged || !listShopManaged.length) && !shopDataLoading && !firstLoad">
                    <img loading="lazy" :src="empty_shop_list" :placeholder="empty_shop_list" alt="" />
                    <span>
                        {{ $t('AgentShopManageComponent.khong_tim_thay_cua_hang') }}
                    </span>
                    <button v-on:click="() => {
                        router.push({
                            path: appRoute.AgentCreateShopComponent
                        })
                    }">
                        {{ $t('AgentShopManageComponent.dang_ky_ngay') }}
                    </button>
                </div>
                <v-expansion-panels variant="accordion" class="agent-shop-manage-list" v-if="listShopManaged?.length">

                    <v-expansion-panel class="item-shop-accordion" v-for="(itemShop) in listShopManaged"
                        :key="'item_shop_accordian_' + itemShop.id">
                        <v-expansion-panel-title :class="'item-shop-info'">
                            <nuxt-link
                                :to="itemShop.id != myShopData?.id ? appRoute.AgentShopDetailDashboardComponent.replaceAll(':id', itemShop.slug ? itemShop.slug : itemShop.id) : appRoute.MyShopComponent"
                                class="item-shop-logo">
                                <img loading="lazy" v-if="!(itemShop.logo)" :src="shop_logo" :placeholder="shop_logo"
                                    v-on:click="() => {
                                        router.push(appRoute.DetailShopComponent + '/' + itemShop.slug)
                                    }" />
                                <div class="logo-origin-container"
                                    :class="{ 'none-style': !(itemShop.logo?.style?.length) }" v-else>
                                    <img :src="itemShop?.logo?.path?.length ? (domainImage + itemShop.logo.path) : shop_logo"
                                        :placeholder="shop_logo" :style="{
                                            transform: (itemShop.logo && itemShop.logo.style) ? itemShop.logo.style : 'none'
                                        }" alt="shop logo" />
                                </div>
                            </nuxt-link>

                            <div class="item-shop-detail">
                                <span class="name">
                                    {{ itemShop.name }}
                                </span>
                                <span class="address">
                                    {{ itemShop.address ? itemShop.address :
                                        $t('AgentShopManageComponent.chua_cung_cap_dia_chi') }}
                                </span>
                                <span class="owner" v-if="itemShop.owner && itemShop.owner.id">
                                    <Icon name="fa6-solid:user-tie"></Icon>
                                    <span>{{ itemShop.owner.name }}</span>
                                </span>

                                <span class="waiting-confirm" v-if="itemShop.status == shop_status.in_process">
                                    <span>{{ $t('AgentShopManageComponent.dang_cho_duyet') }}</span>
                                </span>                                <span class="shop-status" 
                                    :class="{
                                        'open': itemShop.settings?.general?.is_open && itemShop.enable,
                                        'closed': !itemShop.settings?.general?.is_open || !itemShop.enable
                                    }" 
                                    v-if="itemShop.settings?.general?.is_open !== undefined">
                                    <span v-if="itemShop.settings.general.is_open && itemShop.enable">{{ $t('AgentShopManageComponent.mo_cua') }}</span>
                                    <span v-else>{{ $t('AgentShopManageComponent.dong_cua') }}</span>
                                </span>

                                <span class="disabled" v-if="!itemShop.enable">
                                    <span>{{ $t('AgentShopManageComponent.cua_hang_bi_khoa') }}</span>
                                </span>

                                <span class="owner-tag" v-if="itemShop.id == myShopData?.id">
                                    <span>{{ $t('AgentShopManageComponent.ban_la_chu_cua_hang') }}</span>
                                </span>
                            </div>
                        </v-expansion-panel-title>

                        <v-expansion-panel-text class="item-shop-action">
                            <nuxt-link class="view" :title="$t('AgentShopManageComponent.xem_thong_tin')" :to="itemShop.id != myShopData?.id
                                ? appRoute.AgentShopDetailDashboardComponent.replaceAll(':id', itemShop.slug ? itemShop.slug : itemShop.id)
                                : appRoute.MyShopComponent">
                                <Icon name="solar:info-circle-outline"></Icon>
                                <span>{{ $t('AgentShopManageComponent.xem_thong_tin') }}</span>
                            </nuxt-link>

                            <button class="duplicate" :title="$t('AgentShopManageComponent.tao_ban_sao')" v-on:click="() => {
                                selectedShop = itemShop;
                                duplicateShop(itemShop.id);
                            }" v-if="itemShop.id != myShopData?.id"
                                :disabled="isUpdating && selectedShop && selectedShop.id == itemShop.id">
                                <Icon name="ion:duplicate"></Icon>
                                <span>{{ $t('AgentShopManageComponent.tao_ban_sao') }}</span>
                            </button>
                            <nuxt-link class="edit" :title="$t('AgentShopManageComponent.sua_thong_tin')"
                                :to="itemShop.id != myShopData?.id ? appRoute.AgentEditShopInfoComponent.replaceAll(':id', itemShop.slug ? itemShop.slug : itemShop.id) : appRoute.EditShopInfoComponent"
                                :disabled="isUpdating && selectedShop && selectedShop.id == itemShop.id">
                                <Icon name="lucide:pencil-line"></Icon>
                                <span>{{ $t('AgentShopManageComponent.sua_thong_tin') }}</span>
                            </nuxt-link>                              
                            <nuxt-link class="config" :title="$t('AgentShopManageComponent.cau_hinh')"
                                :to="itemShop.id != myShopData?.id ? appRoute.AgentShopConfigComponent.replaceAll(':id', itemShop.slug ? itemShop.slug : itemShop.id) : appRoute.MyShopConfigComponent">
                                <Icon name="solar:settings-outline"></Icon>
                                <span>{{ $t('AgentShopDetailDashboardComponent.cau_hinh') }}</span>
                            </nuxt-link>
                            <button v-if="itemShop.id != myShopData?.id" class="delete"
                                :title="$t('AgentShopManageComponent.xoa_cua_hang')" v-on:click="() => {
                                    selectedShop = itemShop;
                                    showDeleteShopModal = true;
                                }" :disabled="isUpdating && selectedShop && selectedShop.id == itemShop.id">
                                <Icon name="eos-icons:loading"
                                    v-if="isUpdating && selectedShop && selectedShop.id == itemShop.id">
                                </Icon>
                                <Icon name="material-symbols:delete-forever" v-else></Icon>
                                <span>{{ $t('AgentShopManageComponent.xoa_cua_hang') }}</span>
                            </button>
                        </v-expansion-panel-text>
                    </v-expansion-panel>

                </v-expansion-panels>
                <div id=last_of_list></div>

                <div class="loading" v-if="loadMore">{{ $t('AgentShopManageComponent.loading') }}</div>
            </div>
            <button v-if="listShopManaged && listShopManaged.length" class="agent-create-shop-button" v-on:click="() => {
                router.push(appRoute.AgentCreateShopComponent)
            }">
                <Icon name="ic:twotone-plus"></Icon>
                {{ $t('AgentShopManageComponent.tao_cua_hang') }}
            </button>



            <VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
                content-class="v-stack modal-content-container agent-delete-shop-container"
                v-model="showDeleteShopModal" v-on:closed="() => {
                    showDeleteShopModal = false
                }" contentTransition="vfm-fade">
                <div class='v-stack agent-delete-shop-content'>
                    <span class='agent-delete-shop-title'>
                        {{ $t('AgentShopManageComponent.xoa_cua_hang') }}?
                        <span>{{ selectedShop.name }}</span>
                    </span>
                </div>
                <div class='h-stack confirm-modal-buttons'>
                    <button class='reject-disable-button' v-on:click="() => {
                        showDeleteShopModal = false;
                    }">
                        {{ $t('AgentShopManageComponent.khong') }}
                    </button>
                    <button class='accept-disable-button' v-on:click="() => {
                        disableShop();
                    }">
                        {{ $t('AgentShopManageComponent.co') }}
                    </button>
                </div>
            </VueFinalModal>
            <!-- <VueFinalModal class="my-modal-container" :click-to-close="false"
            content-class="my-modal-content-container v-stack agent-edit-shop-info-modal"
            v-model="showEditShopInfoModal" v-on:closed="() => {
                showEditShopInfoModal = false
            }" contentTransition="vfm-slide-up">

            <AgentEditShopInfoComponent :shopData="selectedShop" :profileData="profileData" v-on:close="(e: any) => {
                if (e) {
                    getListShopManage();
                }
                showEditShopInfoModal = false;
            }"></AgentEditShopInfoComponent>
        </VueFinalModal> -->
        </div>

        <v-overlay v-model="shopDataLoading" :z-index="100" :absolute="false" contained content-class='spinner-container'
            persistent scrim="#fff" no-click-animation>
            <Icon name="eos-icons:loading"></Icon>
        </v-overlay>
    </div>

</template>
<style lang="scss" src="./AgentShopManageStyles.scss"></style>
<script lang="ts" setup>


import { appConst, appDataStartup, domainImage, formatCurrency, baseLogoUrl, formatNumber, nonAccentVietnamese } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { UserService } from '~/services/userService/userService';
import { AgentService } from '~/services/agentService/agentService';
import { toast } from 'vue3-toastify';
import empty_shop_list from '~/assets/image/none-shop.jpg'
import empty_shop_search from '~/assets/image/list-empty-2.jpg'
import shop_banner from '~/assets/image/remagan-banner-19_1.png'
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png"
import { VueFinalModal } from "vue-final-modal";
import { HttpStatusCode } from "axios";
import { ShopService } from "~/services/shopService/shopService";
import { shop_status } from "~/components/myShop/MyShopDTO";

const router = useRouter();
const route = useRoute();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
useSeoMeta({
    title: t('AppRouteTitle.AgentShopManageComponent')
});

// Use the centralized shop data composable
const {
    shopList,
    myShopData,
    isLoading: shopDataLoading,
    isRefreshing: shopDataRefreshing,
    initializeShopData,
    refreshShops
} = useShopData();

let authService = new AuthService();
let userService = new UserService();
let agentService = new AgentService();
let shopService = new ShopService();
let productService = new ProductService();
let categoryService = new CategoryService();
let imageService = new ImageService();
let loadMoreTimeOut: any;
let searchShopTimeout: any;

let searchLoading = ref(false);
let search = useState<string>('agent_shop_manage_search_str', () => { return "" });

var firstLoad = ref(true);
var refreshing = ref(false);
var limit = ref(100);
var offset = ref(0);
// myShopData and listShopManaged now come from useShopData composable
var listShopManagedTemp = useState<any>('agent_list_shop_managed_temp', () => { return [] });
var listShopManagedCount = computed(() => shopList.value.length);
var loadedAll = ref(false);
var showAgentCreateShopModal = ref(false);
var profileData = ref();
var loadMore = ref(false);
var isUpdating = ref(false);
var selectedShop = ref();
var showDeleteShopModal = ref(false);
var showEditShopInfoModal = ref(false);
var scrollPosition = useState<any>('agent_shop_manage_scroll_position', () => { return 0 });

// Create computed properties to maintain compatibility with existing template
var listShopManaged = computed(() => {
    if (!search.value) {
        return shopList.value;
    }

    // Filter shops based on search
    return shopList.value.filter(shop =>
        nonAccentVietnamese(shop.name || '').toLowerCase().includes(
            nonAccentVietnamese(search.value).toLowerCase()
        )
    );
});

onBeforeRouteLeave(() => {
    // saveState()
})
onActivated(() => {

})
onMounted(async () => {
    profileData.value = await authService.checkAuth();
    await initializeShopData();
    setTimeout(() => {
        document.getElementById('agent_shop_manage_content')?.scrollBy({
            top: scrollPosition.value
        })
    }, 0);
})

onBeforeMount(()=>{
    
})

function close() {
    router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent);
}

// Simplified refresh function that uses the composable
async function refresh() {
    await refreshShops();
}

function searchShop() {
    // Search is now handled reactively by the computed listShopManaged property
    // No need for API calls since we filter the cached data
    searchLoading.value = false;
}
function listScroll() {
    let el = document
        .getElementById("last_of_list")
        ?.getBoundingClientRect().bottom;
    let ep = document
        .getElementById("agent_shop_manage_content")
        ?.scrollTop;
    scrollPosition.value = ep;
    if (el && el <= window.innerHeight + 300) {
        if (!loadedAll.value) {
            loadMoreShop()
        }
    }
}
function loadMoreShop() {
    clearTimeout(loadMoreTimeOut);
    loadMore.value = true;
    loadMoreTimeOut = setTimeout(() => {
        if (listShopManaged.value.length < listShopManagedCount.value) {
            agentService.listShopManage("", limit.value, listShopManaged.value.length).then(res => {
                if (res.body.data.length <= 0) {
                    loadedAll.value = true;
                    loadMore.value = false;
                }
                else {
                    let list = [...listShopManaged.value, ...res.body.data];

                    listShopManaged.value = list;
                    loadMore.value = false;
                }

            }).catch(err => {
                loadMore.value = false;
            })
        }
        else {
            loadedAll.value = true;
            loadMore.value = false;
        }

    }, 500);
}

async function disableShop() {
    isUpdating.value = true;
    agentService.disableShopAgent(selectedShop.value.id).then(async res => {
        if (res.status == HttpStatusCode.Ok) {
            toast.success(t('AgentShopManageComponent.xoa_cua_hang_thanh_cong'))
            await refreshShops()
            // let list = JSON.parse(JSON.stringify(listShopManaged.value));
            // let index = list.findIndex((e: any) => {
            //     return e.id == selectedShop.value.id;
            // })
            // if (index != -1) {
            //     list.splice(index, 1);
            // }
            // listShopManaged.value = JSON.parse(JSON.stringify(list));
            // listShopManagedCount.value = ;
            isUpdating.value = false;
            showDeleteShopModal.value = false;
            selectedShop.value = null
        }
        else {
            isUpdating.value = false;
            showDeleteShopModal.value = false;
            toast.error(res.body?.message ?? t('AgentShopManageComponent.xoa_that_bai_vui_long_gui_lai_sau'))
        }
    }).catch(err => {
        isUpdating.value = false;
        showDeleteShopModal.value = false;
        toast.error(t('AgentShopManageComponent.xoa_that_bai_vui_long_gui_lai_sau'))
    })
}

async function duplicateShop(shopId = null) {
    isUpdating.value = true;
    agentService.duplicateShop(shopId).then(async res => {
        if (res.status == HttpStatusCode.Ok) {
            await refreshShops()
            isUpdating.value = false;
            showDeleteShopModal.value = false;
            selectedShop.value = null
        }
        else {
            isUpdating.value = false;
            showDeleteShopModal.value = false;
            toast.error(res.body?.message ?? t('AgentShopManageComponent.xoa_that_bai_vui_long_gui_lai_sau'))
        }
    }).catch(err => {
        isUpdating.value = false;
        showDeleteShopModal.value = false;
        toast.error(t('AgentShopManageComponent.xoa_that_bai_vui_long_gui_lai_sau'))
    })
}
</script>