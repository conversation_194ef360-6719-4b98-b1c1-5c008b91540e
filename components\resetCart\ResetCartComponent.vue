<template>
	<VueFinalModal class="my-modal-container" content-class="v-stack modal-content-container reset-cart-container" :overlay-behavior="'persist'"
		v-model="show" v-on:closed="() => {
			close()
		}" contentTransition="vfm-fade">
		<div class='v-stack reset-cart-content'>
			<span class='reset-cart-title'>
				{{ $t('ResetCartComponent.tao_gio_hang_moi') }}
			</span>

			<span class='reset-cart-message'>
				{{ $t('ResetCartComponent.ban_co_muon_xoa_gio_hang_tai') }}{{ " " }}
				<span class='shop-name'>
					{{
						cartData && cartData.length
							? cartData[0].shop?.name
							: ""
					}}
				</span>{{ " " }}
				{{ $t('ResetCartComponent.va_them_mon_moi_nay') }}
			</span>
		</div>
		<div class='h-stack confirm-modal-buttons'>
			<button class='reject-button' :disabled="isUpdating" v-on:click="() => {
				reject()
			}">
				{{ $t('ResetCartComponent.khong') }}
			</button>
			<button class='accept-button' :disabled="isUpdating" v-on:click="() => {
				accept()
			}">
				{{ $t('ResetCartComponent.co') }}
			</button>
		</div>

	</VueFinalModal>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";

const emit = defineEmits(['accept', 'close', 'reject']);
const nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();
var cartData = ref([] as CartDto[]);
var refreshing = ref(false);
var show = ref(true);
var isUpdating = ref(false);

onMounted(async () => {
	let cartDataLocal = await localStorage.getItem(appConst.storageKey.cart);

	cartData.value = cartDataLocal ? JSON.parse(cartDataLocal) : [];
})

function close() {
	emit('close')
}

function accept() {
	emit('accept')
}

function reject() {
	emit('reject')
}
</script>

<style lang="scss" src="./ResetCartStyles.scss"></style>