<script setup lang="ts">
import { HttpStatusCode } from 'axios';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, zaloConfig, formatNumber, nonAccentVietnamese, showTranslateProductName, showTranslateProductDescription, validPhone } from '~/assets/AppConst';
import type { CartDto } from '~/assets/appDTO';
import appRoute from '~/assets/appRoute';
import environment from '~/assets/environment/environment';
import { type ChannelDTO, member_type, channel_type } from '~/components/chatManage/ChatDTO';
import { AgentService } from '~/services/agentService/agentService';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { RatingService } from '~/services/ratingService/ratingService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';

import non_avatar from '~/assets/image/non-avatar.jpg';
import icon_for_broken_image from '~/assets/image/image-broken.jpg'

const nuxtApp = useNuxtApp();
const { t, locale } = useI18n()
var props = defineProps({
	mode: null
})
var router = useRouter();
var route = useRoute();
const dataId = props.mode != 'agent' ? route.params.slug : route.params.id;
// var shopGet = await fetch(appConst.apiURL.detailShop + '/' + dataId);
var shopDetails = ref(null as any);
var shopData = useState(() => { return null as any });
var shopGet: any = await useLazyFetch(appConst.apiURL.detailShop + '/' + dataId, {
	key: `shop_detail_${dataId}`,
	headers: {
		Origin: `https://${environment.domain}`
	},
	getCachedData: (key) => {
		// Check if the data is already cached in the Nuxt payload
		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
			if (route.query?.flush == '1') {
				return null;
			} else {
				if (route.query?.flush == '1') {
					return null;
				}
				return nuxtApp.static.data[key];
			}
		}

		// Check if the data is already cached in the static data
		if (nuxtApp.static.data[key]) {
			return nuxtApp.static.data[key]
		}

		return null
	}
});
if (shopGet?.data?.value?.status == 200) {
	shopDetails.value = await JSON.parse(JSON.stringify(shopGet.data.value.body.data));
	shopData.value = shopDetails.value;
	// dataShopProducts.value = shopDetails.products;
	let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
	useServerSeoMeta({
		ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
		title: () => `${shopDetails.value.name} | Rẻ mà gần`,
		description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogUrl: () => domain + route.fullPath,
		ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
		ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
	});
}
var dataShopProducts = ref([] as any);
var dataShopProductsSaleOff = ref([] as any[]);

var showSelectedProduct = ref(false);

var showImageViewerModal = ref(false);
var listObjectViewer = ref([] as any);
var indexActive = ref(0);
var open_time_list = ref([] as any);

var authService = new AuthService();
var userService = new UserService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var ratingService = new RatingService()

var shop_id = ref((route.params && props.mode != 'agent' ? (route.params.slug ? route.params.slug : null) : (route.params.id ? route.params.id : null)) as any);
var scrollPosition = useState(() => { return 0 as any });
var userProfile = ref(null as any);
var refreshing = ref(false);
var open_time_list = ref([] as any);
var baseDescription = ref("");
var isExpanding = ref(false);

var webInApp = ref(null as any);

var showAddCommentModal = ref(false);
var showUpdateCommentModal = ref(false);
var myRating = ref<any>(null);

onUnmounted(async () => {
	nuxtApp.$unsubscribe(appConst.event_key.shop_updated)
});

onMounted(async () => {

	nuxtApp.$listen(appConst.event_key.shop_updated, (e) => {
		console.log("shop update")
		shopData.value = null;
		if (props.mode != 'agent') {
			getDetailShop();
		}
		else {
			agentGetDetailShop()
		}
	})
	init();
});
onBeforeMount(async () => {
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;
	userProfile.value = await authService.checkAuth();
})
async function init() {
	if (shopDetails.value && shopData.value) {

		let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
		useServerSeoMeta({
			ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
			title: () => `${shopDetails.value.name} | Rẻ mà gần`,
			description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
			ogUrl: () => domain + route.fullPath,
			ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
			ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
		});
		shopData.value = await JSON.parse(JSON.stringify(shopDetails.value));
		getShopRatings();
		getShopProduct();
		getMyRatingToShop();
		open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
		refreshing.value = false;
	}
	else {
		refreshing.value = true;
		if (props.mode != 'agent') {
			await getDetailShop();
		}
		else {
			await agentGetDetailShop()
		}
	}
	baseDescription.value =
		`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần. Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
}

function getDetailShop() {
	refreshing.value = true;
	shopService.detailShop(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));

			let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
			useSeoMeta({
				ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
				title: () => `${shopData.value?.name} | Rẻ mà gần`,
				description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
				ogUrl: () => domain + route.fullPath,
				ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
				ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
			});

			open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
			refreshing.value = false;
			// setTimeout(() => {
			// 	checkShowFullDescription();
			// }, 300);
			getShopRatings();
			getShopProduct();
			getMyRatingToShop();

			baseDescription.value =
				`Mua online sản phẩm của cửa hàng ${shopData.value?.name} trên Rẻ mà Gần. Chất lượng cao, uy tín, giá tốt, nhận hàng nhanh, chăm sóc khách hàng nhiệt tình.`
		}
		else {
			refreshing.value = false;
		}
	}).catch(err => {
		refreshing.value = false;
		console.log(err);
	})
}

function agentGetDetailShop() {
	refreshing.value = true;
	agentService.agentShopDetail(shop_id.value).then(async res => {
		if (res.status == 200) {
			shopData.value = JSON.parse(JSON.stringify(res.body.data));
			refreshing.value = false;

			setTimeout(() => {
				// checkShowFullDescription();
				open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
				let seoDescription = shopData.value?.description?.length > 65 ? shopData.value?.description?.slice(0, 65).concat('...') : shopData.value?.description;
				useSeoMeta({
					ogTitle: () => `${shopData.value?.name} | Rẻ mà gần`,
					title: () => `${shopData.value?.name} | Rẻ mà gần`,
					description: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogDescription: () => seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
					ogUrl: () => domain + route.fullPath,
					ogImage: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl,
					ogImageUrl: () => shopData.value?.banner ? domainImage + shopData.value?.banner.path : baseLogoUrl
				});
				getShopRatings();
				getShopProduct();
				getMyRatingToShop();
			}, 300);
		}
		else {
			toast.error(t('ShopInfoComponent.ban_khong_co_quyen_truy_cap'))
			refreshing.value = false;
		}

	})
}
function formatOpeningHours(hoursObj: any) {
	function formatHours(hours: any) {
		return hours.map((timeSlot: any) => `${timeSlot[0]} - ${timeSlot[1]}`).join(' & ');
	}
	const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

	const result = [];
	if (hoursObj) {
		let currentGroup: any = [];
		let currentHours = hoursObj[days[0]];

		for (let i = 0; i < days.length; i++) {
			const day = days[i];
			const hours = hoursObj[day];

			if (JSON.stringify(hours) === JSON.stringify(currentHours)) {
				currentGroup.push(t(`DayInWeek.${day}`));
			} else {
				if (currentGroup.length > 1) {
					// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				} else {
					// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
					result.push({
						days: `${currentGroup[0]}`,
						times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
					})
				}
				currentGroup = [t(`DayInWeek.${day}`)];
				currentHours = hours;
			}
		}
		// Xử lý nhóm cuối cùng
		if (currentGroup.length > 1) {
			// result.push(`${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]} - ${currentGroup[currentGroup.length - 1]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		} else {
			// result.push(`${currentGroup[0]}: ${currentHours?.length ? formatHours(currentHours) : '--:--'}`);
			result.push({
				days: `${currentGroup[0]}`,
				times: `${currentHours?.length ? formatHours(currentHours) : '--:--'}`
			})
		}
	}

	return result;

}
function showHideHeader(event: any) {
	nuxtApp.$emit(appConst.event_key.scrolling, event)
}

function getShopProduct(offset: any = 0, limit: any = 20) {

	shopService.searchProductsInShopClient(
		"",
		shopData.value.id,
		[],
		0,
		1,
		null,
		null,
		false
	).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			shopData.value.product_count = res.body.data.count; 0
		}
	})
}

function calcTimeCreateAt(time: any) {
	let now = moment();
	let timeTemp = moment(time, 'YYYY-MM-DD HH:mm:ss');
	let diffInWeek = now.get('d') - timeTemp.get('d');

	if (diffInWeek == 1) return `${t('ShopInfoComponent.hom_qua')}`;

	if (diffInWeek > 1 && diffInWeek <= 7) return `${diffInWeek} ${t('ShopInfoComponent.ngay_truoc')}`;
	if (diffInWeek > 30) return `${Math.floor(diffInWeek / 30)} ${t('ShopInfoComponent.thang_truoc')}`;
	if (diffInWeek > 365) return `${Math.floor(diffInWeek / 365)} ${t('ShopInfoComponent.nam_truoc')}`;

	return timeTemp.format('DD/MM/YYYY HH:mm')
}

async function loadmoreRating() {
	await ratingService.listRatingByObject(shopData.value.id, shopData.value.comments.length, 20).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			shopData.value.comments = [...shopData.value.comments, ...res.body.data.result.filter((e:any)=>{return e.users?.id != userProfile.value?.id})];
		}
	})
}

async function getShopRatings() {
	await ratingService.calcObjectRating(shopData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			shopData.value.ratings = res.body.data
		}

	})
	await ratingService.listRatingByObject(shopData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			shopData.value.comment_count = res.body.data.count;
			shopData.value.comments = res.body.data.result.filter((e:any)=>{return e.users?.id != userProfile.value?.id});
		}

	})
}

async function getMyRatingToShop(){
	await ratingService.myRatingToObjectId(shopData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			myRating.value = res.body
		}

	})
}
</script>
<template>
	<div class="public-container" id="shop_info_container" v-on:scroll="(e) => {
		// showHideHeader(e);
	}">
		<div class="shop-info-v2-container">
			<SubHeaderV2Component class="title-header-container">
				<template v-slot:header_middle>
					<div class="search-input-group">
						<h3>{{ shopData?.name ?? $t('ShopInfoComponent.chi_tiet_cua_hang') }}</h3>
					</div>
				</template>
			</SubHeaderV2Component>
			<!-- <HeaderV2Component :header_level_2_id="'title_header_container'"
				:last_element_id="'shop_content_container'"></HeaderV2Component> -->
			<div class='v-stack loading-skeleton' v-if="refreshing">

				<div class='h-stack'>
					<v-skeleton-loader class='avt-skeleton' />
					<div class="v-stack info-placeholder">
						<v-skeleton-loader type="list-item-three-line" class="info-skeleton" />
					</div>
				</div>
			</div>
			<div v-else-if="shopData && shopData.id" class="shop-info-container" id='shop-info-container'>
				<!-- <div class="title-header-container" id="title_header_container">
					<button class="back-button" v-on:click="() => {
						router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
					}">
						<Icon name="solar:round-alt-arrow-left-linear"></Icon>
					</button>
					<div class="search-input-group">
						<h3>{{ $t('ShopInfoComponent.chi_tiet_cua_hang') }}</h3>
					</div>
					<nuxt-link class="cart-button"></nuxt-link>
				</div> -->
				<div class='v-stack shop-info-content-container'>
					<div class="shop-detail-container">
						<AvatarComponent class="shop-logo" :imgTitle="shopData?.name" :imgStyle="shopData?.logo?.style"
							:imgSrc="shopData?.logo?.path?.length
								? (domainImage + shopData?.logo?.path)
								: ''" :width="75" :height="75" v-on:click="() => {
									if (shopData?.logo?.path?.length) {
										let itemImg = {
											path: shopData?.logo?.path,
											title: shopData.name,
										}
										listObjectViewer = [itemImg];
										indexActive = 0;
										showImageViewerModal = true;
									}

								}" />
						<div class="shop-detail-content">
							<div class="name-share">
								<span class="name">
									<span>{{ shopData.name }}</span>
								</span>
							</div>
							<span class="follows">
								<em>{{ shopData.follows ?? 0 }}</em> {{ $t('ShopInfoComponent.nguoi_theo_doi') }}
							</span>

						</div>
					</div>
					<div class="shop-detail-open-time" v-if="open_time_list.length > 0">
						<div class="label">
							<span>{{ $t('ShopInfoComponent.gio_mo_cua') }}</span>
							<Icon name="solar:clock-square-linear"></Icon>
						</div>
						<div class="line"></div>
						<div class="list-open-time">
							<div class="item-open-time" v-for="item in open_time_list">
								<span class="days">{{ item.days }}</span>
								<span class="times">{{ item.times }}</span>
							</div>
						</div>

					</div>
					<div class="shop-detail-summary">
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.trang_thai') }}:
							</div>
							<div class="detail-value" :class="{
								'opened': shopData.settings?.general?.is_open?.value
							}" v-if="shopData.settings?.general?.is_open">
								<span v-if="shopData.settings?.general?.is_open?.value">
									{{ $t('ShopInfoComponent.dang_mo_cua') }}
								</span>
								<span v-else>
									{{ $t('ShopInfoComponent.da_dong_cua') }}
									<span v-if="shopData?.settings?.general?.is_open?.reason && (
										shopData?.settings?.general?.is_open?.reason?.[locale]?.length
										|| shopData?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage]?.length
									)"> - {{ 
										shopData?.settings?.general?.is_open?.reason?.[locale] 
										?? shopData?.settings?.general?.is_open?.reason?.[appConst.defaultLanguage] 
									}}</span>
								</span>
							</div>
							<div class="detail-value opened" v-else>
								{{ $t('ShopInfoComponent.dang_mo_cua') }}
							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.danh_muc') }}:
							</div>
							<div class="detail-value">
								{{ shopData.business_types?.name }}
							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.danh_gia') }}:
							</div>
							<div class="detail-value">
								<span v-if="shopData.ratings">{{ shopData.ratings ?? 0 }}/{{ appConst.defaultMaxRate
									}}&nbsp;</span>
								<span class="secondary-detail">({{ $t('ShopInfoComponent.x_danh_gia', {
									count:
										shopData.comment_count
								}) }})</span>

							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.san_pham') }}:
							</div>
							<div class="detail-value">
								{{ formatNumber(shopData.product_count ?? 0) }}
							</div>
						</div>
						<div class="line" v-if="shopData.created_at"></div>
						<div class="detail-item" v-if="shopData.created_at">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.da_tham_gia') }}:
							</div>
							<div class="detail-value">
								{{ calcTimeCreateAt(shopData.created_at) }}
							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.so_dien_thoai') }}:
							</div>
							<div class="detail-value">
								{{ shopData.phone }}
							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.dia_chi') }}:
							</div>
							<div class="detail-value">
								<span class="secondary-detail">{{ shopData.address }}</span>
							</div>
						</div>
						<div class="line"></div>
						<div class="detail-item">
							<div class="detail-label">
								{{ $t('ShopInfoComponent.link_shop') }}:
							</div>
							<div class="detail-value weight-400">
								<nuxt-link :to="`${appRoute.DetailShopComponent}/${shopData.slug}`">{{
									`${domain}${appRoute.DetailShopComponent}/${shopData.slug}` }}</nuxt-link>
							</div>
						</div>
					</div>
					<div class="shop-detail-description">
						<div class="label">
							<span>{{ $t('ShopInfoComponent.gioi_thieu') }}</span>
						</div>
						<div class="line"></div>
						<div class="description" v-if="shopData.description?.length">
							<div class="description-content" :class="{ 'expanding': isExpanding }">
								{{ shopData.description }}
							</div>
						</div>
						<div class="none-description" v-else>
							{{ $t('ShopInfoComponent.chua_co_gioi_thieu') }}
						</div>
						<button class="expand-button" v-if="shopData.description?.length" v-on:click="() => {
							isExpanding = !isExpanding;
						}">
							{{ isExpanding ? $t('ShopInfoComponent.thu_gon') : $t('ShopInfoComponent.xem_them') }}
							<Icon :name="isExpanding ? 'solar:alt-arrow-up-linear' : 'solar:alt-arrow-down-linear'">
							</Icon>
						</button>
					</div>

					<div class="shop-ratings" id="rating_section">
						<div class="label">
							<span>{{ $t('ShopInfoComponent.danh_gia_cua_khach_hang') }}</span>
							<button class="add-comment" v-on:click="showAddCommentModal = true" v-if="!myRating?.id">
								{{ $t('ShopInfoComponent.them_danh_gia') }}
								<Icon name="pajamas:duo-chat"></Icon>
							</button>
						</div>

						<div class="rating-overview">
							<div class="rating" v-if="shopData.ratings">
								<NuxtRating v-if="shopData.ratings" active-color="#ffc107"
									:border-color="'transparent'" :roundedCorners="true" :rating-spacing="5"
									:rating-step=".5" :rating-size="10" :rating-value="shopData.ratings || 0" />
								<span>{{ shopData.ratings?.toFixed(1) || 0 }}/{{
									appConst.defaultMaxRate }}</span>
							</div>
							<div class="comment" v-if="shopData.comment_count">
								({{ shopData.comment_count }})
							</div>
						</div>

						<div class="my-comment comment-container" v-if="myRating?.id">
							<div class="comment-header">
								<label>{{ $t('ShopInfoComponent.danh_gia_cua_ban') }}</label>
								<button class="update-comment" v-on:click="showUpdateCommentModal = true">{{
									$t('ShopInfoComponent.cap_nhat') }}</button>
							</div>

							<div class="comment-item-container">
								<div class="user-info">
									<img loading="lazy" :src="myRating.users?.profile_picture
										? ((appConst.provider_img_domain.some(e => myRating.users?.profile_picture.includes(e)))
											? myRating.users?.profile_picture
											: (domainImage + myRating.users?.profile_picture)
										)
										: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:error="(event: any) => {
										event.target.src = non_avatar;
									}" />
									<div class="name-rate">
										<span class="name">
											{{ myRating.users.name }}
										</span>
										<NuxtRating v-if="myRating.rating" active-color="#ffc107"
											:border-color="'transparent'" :rounded-corners="true" :rating-spacing="5"
											:rating-step=".5" :rating-size="10" :rating-value="myRating.rating || 0"
											class="rating" />
									</div>
								</div>

								<div class="comment-detail" v-if="myRating.review?.length">
									{{ myRating.review }}
								</div>
								<div class="comment-images" v-if="myRating.images?.length">
									<div class="comment-image-item" v-for="(itemImg, index) in myRating.images"
										v-on:click="() => {
											listObjectViewer = JSON.parse(JSON.stringify(myRating.images));
											indexActive = index;
											showImageViewerModal = true;
										}">
										<img :src="itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image"
											v-on:error="(event: any) => {
												event.target.title = t('ProductComponent.loi_anh');
												event.target.src = icon_for_broken_image;
											}" v-on:click="(event: any) => {
											event.target.title = '';
											event.target.src = itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image
										}" loading="lazy" />
									</div>
								</div>
								<div class="comment-time">
									<span>
										{{ moment(myRating.created_at, 'YYYY-MM-DD HH:mm:ss').format('DD-MM-YYYY HH:mm')
										}}</span>
									<button v-if="myRating.shop_reply" class="open-shop-reply" v-on:click="() => {
										myRating.open_shop_reply = true
									}">
										{{ $t('ShopInfoComponent.phan_hoi_tu_cua_hang') }}
										<Icon name="solar:alt-arrow-down-linear"></Icon>
									</button>
								</div>
								<div class="shop-reply" v-if="(myRating.shop_reply && myRating.open_shop_reply)">
									<span class="label">{{ $t('ShopInfoComponent.phan_hoi_tu_cua_hang') }}</span>
									<span class="content">{{ myRating.shop_reply }}</span>
								</div>
							</div>
						</div>

						<div class="comment-container" id="product_comment">
							<div class="comment-item-container" v-for="itemComment in shopData.comments">
								<div class="user-info" v-if="itemComment.users?.id">
									<img loading="lazy" :src="itemComment.users?.profile_picture
										? ((appConst.provider_img_domain.some(e => itemComment.users?.profile_picture.includes(e)))
											? itemComment.users?.profile_picture
											: (domainImage + itemComment.users?.profile_picture)
										)
										: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:error="(event: any) => {
										event.target.src = non_avatar;
									}" />
									<div class="name-rate">
										<span class="name">
											{{ itemComment.users.name }}
										</span>
										<NuxtRating v-if="itemComment.rating" active-color="#ffc107"
											:border-color="'transparent'" :rounded-corners="true" :rating-spacing="5"
											:rating-step=".5" :rating-size="10" :rating-value="itemComment.rating || 0"
											class="rating" />
									</div>
								</div>

								<div class="comment-detail" v-if="itemComment.review?.length">
									{{ itemComment.review }}
								</div>
								<div class="comment-images" v-if="itemComment.images?.length">
									<div class="comment-image-item" v-for="(itemImg, index) in itemComment.images"
										v-on:click="() => {
											listObjectViewer = JSON.parse(JSON.stringify(itemComment.images));
											indexActive = index;
											showImageViewerModal = true;
										}">
										<img :src="itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image"
											v-on:error="(event: any) => {
												event.target.title = t('ProductComponent.loi_anh');
												event.target.src = icon_for_broken_image;
											}" v-on:click="(event: any) => {
											event.target.title = '';
											event.target.src = itemImg.path?.length ? `${domainImage}${itemImg.path}` : icon_for_broken_image
										}" loading="lazy" />
									</div>
								</div>
								<div class="comment-time">
									<span>
										{{ moment(itemComment.created_at, 'YYYY-MM-DD HH:mm:ss').format('DD-MM-YYYY HH:mm')}}</span>
									<button v-if="itemComment.shop_reply" class="open-shop-reply" v-on:click="() => {
										itemComment.open_shop_reply = true
									}">
										{{ $t('ShopInfoComponent.phan_hoi_tu_cua_hang') }}
										<Icon name="solar:alt-arrow-down-linear"></Icon>
									</button>
								</div>
								<div class="shop-reply" v-if="(itemComment.shop_reply && itemComment.open_shop_reply)">
									<span class="label">{{ $t('ShopInfoComponent.phan_hoi_tu_cua_hang') }}</span>
									<span class="content">{{ itemComment.shop_reply }}</span>
								</div>
							</div>
						</div>
						<button class="rate-loadmore" v-if="(shopData?.comments?.length + (myRating?.id ? 1 : 0)) < shopData.comment_count"
							v-on:click="loadmoreRating()">
							{{ t('ShopInfoComponent.hien_thi_them_danh_gia') }}
							<Icon name="solar:alt-arrow-down-linear"></Icon>
						</button>
						<div class="show-all-rating" v-else-if="shopData.comment_count > 0">
							{{ t('ShopInfoComponent.hien_thi_tat_ca') }}
						</div>
						<div class="show-all-rating" v-else>
							{{ t('ShopInfoComponent.chua_co_danh_gia') }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<ImageViewerComponent v-if="showImageViewerModal" :showImageViewerModal="showImageViewerModal"
			:listObject="listObjectViewer" :indexActive="indexActive" v-on:close="(e: any) => {
				showImageViewerModal = false
			}"></ImageViewerComponent>

		<AddCommentComponent v-if="showAddCommentModal" :showModal="showAddCommentModal"
			:object="shopData"
			:object_type="appConst.object_type.shop" v-on:close="(e: any) => {
				if (e == true) {
					getShopRatings();
					getMyRatingToShop();
				}
				showAddCommentModal = false
			}" v-on:submit="() => {

			}">

		</AddCommentComponent>

		<AddCommentComponent v-if="showUpdateCommentModal" :showModal="showUpdateCommentModal"
			:object="shopData"
			:initData="JSON.parse(JSON.stringify(myRating))"
			:object_type="appConst.object_type.shop" v-on:close="(e: any) => {
				if (e == true) {
					getShopRatings();
					getMyRatingToShop();
				}
				showUpdateCommentModal = false
			}" v-on:submit="() => {

			}">

		</AddCommentComponent>
	</div>

</template>

<style lang="scss" src="./ShopInfoStyles.scss"></style>
