.reels-container{
   height: 100%;
   flex: 1;
   background: var(--color-background-2);
   margin: 0;
   overflow: hidden;
   padding: 0 !important;
   display: flex;
   flex-direction: column;
   position: relative;
   z-index: 100;
  // position: relative;
  // height: 100%;
  // flex: 1;
  // display: flex;
  // border-radius: 10px 10px 0 0;
  // max-width: var(--max-width-view);
  // padding: 0 !important;
  // flex-direction: column;
  // background: var(--color-background-2);
  // z-index: 100;


  & .my-carousel {
    width: 100%;
    height: 100%;
  }

  & .stack-carousel {
    flex: 1;

    & .item-stack-slide {
      height: 100%;
      cursor: pointer;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      padding: 30px 10px;
      box-sizing: border-box;
      background: white;
      cursor: grab;

      & > .product-info{
        padding: 10px 10px 20px;
        display: flex;
        width: 100%;
        flex-direction: column;
        gap: 5px;
        justify-content: flex-start;
        align-items: flex-start;

        & > .name {
          color: #626262;
          text-align: left;
          font-weight: 500;
          overflow: hidden;
          font-size: 1.3em;
        }
        & > .shop-name {
          display: flex;
          width: 100%;
          gap: 5px;
          align-items: baseline;
          overflow: hidden;
          font-weight: bold;
          font-style: italic;
          color: #399901;
          text-align: left;
          font-size: 1.2em;
        }

        & > .price {
          text-align: center;
          color: #ed1b24;
          font-size: 1.3em;
          font-weight: bold;
        }

        & > .origin-price {
          text-align: center;
          color: #626262;
          font-size: 1.2em;
          text-decoration: line-through;
        }

        & > .add-to-cart {
          background: #ed1b24;
          color: white;
          font-size: 1.2em;
          border-radius: 2em;
          padding: 5px 15px;
        }
      }

      & > img {
        width: fit-content;
        height: fit-content;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        margin: auto;
      }
    }

    & .item-stack-slide:active{
      cursor: grabbing;
    }
  }

  & > .zalo-share-button{
    display: none;
  }
}