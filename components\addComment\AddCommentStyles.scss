.add-comment-container {
  max-height: 95dvh;
  height: 95dvh;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 0;
  background-color: white;
  overflow: hidden;

  & > .add-comment-content-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 15px 0;
    padding: 0 10px;
    overflow: auto;
    & .label {
      font-size: 16px;
      font-weight: bold;
      color: black;
    }
    & > .rate {
      padding: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & > .rate-description {
        color: var(--color-text-note);
        font-weight: 600;
        font-style: italic;
      }
    }

    & > .comment-content {
      display: flex;
      flex-direction: column;
      gap: 5px;

      & > .review-input {
        width: 100%;
        min-height: 200px;
        background-color: var(--color-background-2);
        border-radius: 5px;
        padding: 10px;
        width: 100%;
        outline: none;
        resize: none;
      }
    }

    & > .image {
      display: flex;
      flex-direction: column;
      gap: 5px;
      & > .image-list {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;

        & > .select-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: 3px dashed #e7e9ec;
          color: #e7e9ec;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          cursor: pointer;

          & > label {
            width: 100%;
            height: 100%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: -1;
            }
          }
        }
        & > .selected-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: thin solid #e7e9ec;
          color: #e7e9ec;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          position: relative;

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: inherit;
          }

          & > .action-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 20px;
            // width: 100%;
            // height: 100%;
            // display: none;
            display: flex;
            color: white;
            justify-content: flex-end;
            align-items: flex-end;
            // padding: 5px;
            gap: 5px;
            background-color: rgb(0, 0, 0, 0.5);
            border-radius: 10px 0;

            & > button {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }

        & > .selected-image:hover {
          & > .action-overlay {
            display: flex;
          }
        }
      }
    }
  }

  & > .action-buttons {
    justify-content: space-evenly;
    margin: auto 0 10px 0;

    & > button {
      border-radius: 2em;
      width: 35%;
      padding: 5px 20px;
      border: none;
      white-space: nowrap;
    }
    & > .cancel-button {
      color: var(--color-button-special);
      border: thin solid var(--color-button-special);
      background: white;
    }
    & > .save-button {
      color: white;
      border: thin solid var(--primary-color-1);
      background: var(--primary-color-1);
    }

    & > .cancel-button:disabled {
      color: var(--color-text-note);
      border-color: var(--color-text-note);
      opacity: 1;
    }
    & > .save-button:disabled {
      background: #ccc;
      color: var(--primary-color-1);
      border-color: #ccc;
      opacity: 1;
    }
  }
}
