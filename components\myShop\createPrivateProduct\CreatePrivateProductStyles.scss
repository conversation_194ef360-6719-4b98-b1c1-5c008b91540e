.create-private-product-modal {
}

.create-private-product-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  // max-height: 90vh;
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: visible;
  position: relative;

  // &>.title-header {
  //     // font-size: 1.3em;

  //     margin: 0;
  //     text-align: center;
  //     border-bottom: thin solid #ccc;
  // }

  & > .product-content-container {
    display: flex;
    flex: 1;
    // overflow: hidden auto;
    flex-direction: column;
    background: #f1f2f6;
    padding: 15px;

    & .product-image-list {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
      justify-content: center;
      background: white;
      padding: 10px;
      margin: 0;
      border-radius: 10px;
      border: 3px dashed #e7e9ec;

      & > .select-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: 2px solid #e7e9ec;
        color: #e7e9ec;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        cursor: pointer;

        & > label {
          width: 100%;
          height: 100%;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: -1;
          }
        }
      }
      & .selected-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: thin solid #e7e9ec;
        color: #e7e9ec;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        position: relative;
        cursor: grab;

        & > img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: inherit;
        }

        & > .action-overlay {
          position: absolute;
          bottom: 0;
          right: 0;
          font-size: 20px;
          // width: 100%;
          // height: 100%;
          // display: none;
          display: flex;
          color: white;
          justify-content: flex-end;
          align-items: flex-end;
          // padding: 5px;
          gap: 5px;
          background-color: rgb(0, 0, 0, 0.5);
          border-radius: 10px 0;

          & > button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      & .selected-image.sortable-chosen {
        cursor: grabbing;
      }
      & .drag-drop-container {
        width: 100%;
      }
      & .selected-image:hover {
        & > .action-overlay {
          display: flex;
        }
      }
    }

    & .product-avt-container {
      position: relative;
      display: flex;
      justify-content: center;
      width: 150px;
      align-items: center;
      margin: 10px auto;

      & > img {
        border-radius: 10px;
        flex: 1 1;
        height: 150px;
        width: 150px;
        object-fit: cover;
        // background-color: var(--color-background-2);
      }
    }
    & .product-avatar-actions {
      display: flex;
      gap: 15px;
      justify-content: center;

      & .select-image {
        // position: absolute;
        // top: 10px;
        // right: 10px;
        width: 25%;
        min-width: fit-content;
        white-space: nowrap;
        cursor: pointer;

        & > div {
          position: relative;
          width: 100%;
          font-size: 1.2em;
          background: white;
          color: #343434;
          height: 100%;
          display: flex;
          justify-content: center;
          font-weight: bold;
          border-radius: 7px;
          border: thin solid #e7e9ec;
          padding: 5px 15px;
          cursor: pointer;
          font-size: 15px;

          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: 2;
          }

          & input::file-selector-button {
            cursor: pointer;
          }
        }
      }
    }
    & .language-options {
      display: flex;
      gap: 5px;
      font-size: 15px;
      justify-content: center;
      width: 100%;
      padding: 10px 0;
      white-space: nowrap;
      flex-wrap: wrap;

      & > .lang-button {
        background-color: #dbdbdb;
        color: #202020;
        padding: 0 10px;
        border-radius: 2em;
      }
      & > .lang-button.active {
        background-color: #202020;
        color: white;
        font-weight: 600;
      }
    }
    & > .product-content {
      margin: 10px 0;
      font-size: 15px;
      color: #2b2b2b;
      font-weight: 600;

      & > .content-header {
        display: flex;
        gap: 5px 10px;
        flex-wrap: wrap;
        justify-content: space-between;

        & > .add-category {
          display: flex;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: var(--primary-color-1);
        }
      }

      & > .checkbox-input-label {
        gap: 5px;
        display: flex;
        cursor: pointer;
        user-select: none;
        font-weight: 500;
        color: var(--primary-color-1);
        font-size: 20px;
        min-height: 25px;

        & span {
          font-size: 17px;
          color: var(--primary-color-1);
        }

        & em {
          color: var(--primary-color-1);
          font-size: 14px;
          line-height: normal;
          align-self: center;
        }
      }

      & .label {
        color: #262a3c;
        font-size: 15px;
      }

      & > .input-custom,
      > .text-area-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
      }
      & > .text-area-custom {
        height: 200px;
        resize: none;
      }
      & > .dropdown-select-container {
        background: white;
        border-radius: 7px;
        margin-top: 5px;
        padding: 10px;
        border: none;
        font-size: 17px;

        & button {
          padding: 0;
        }
      }
      & em {
        color: var(--primary-color-1);
      }

      & .price-text.notice {
        color: #00823e;
      }
    }

    & > .product-notice {
      font-style: italic;
      color: var(--color-text-note);
      display: flex;
      align-items: flex-start;
      gap: 5px;
      font-size: 0.85em;

      & > em {
        color: var(--primary-color-2);
      }
    }
  }

  & .price-input {
    border: thin solid var(--color-text-note);
    border-width: 0 0 1px 0;
    font-weight: 600;
    margin: 0 5px 10px;
    outline: none;
  }

  & .form-actions {
    gap: 15px;
    justify-content: space-evenly;
    margin-top: auto;

    & > button {
      border-radius: 5px;
      flex: 1;
      padding: 5px 20px;
      border: none;
      font-size: 17px;
      font-weight: bold;
    }

    & > .cancel-button {
      color: #343434;
      border: thin solid #e2e5eb;
      background: #e2e5eb;
    }

    & > .save-button {
      color: white;
      border: thin solid #00823e;
      background: #00823e;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  & .dropdown-select-container.category-select .group {
    width: 95%;
    max-height: 500% !important;
    overflow: auto;
    box-shadow: 0 0 15px #ccc;
    border-radius: 10px;
    opacity: 1;
    background: white;
    & ul {
      border-radius: 0;
    }
    & .search-category-empty {
      color: var(--color-text-note);
    }
  }
}

.cancel-create-container {
  max-height: 95dvh;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
  font-size: 15px;
}

.error-message.hight-light:not(.success) {
  transform-origin: 0 0;
  animation: high-light 0.5s ease-in-out infinite;
}
.category-content-container {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}
