<script lang="ts" setup>
import { appConst, baseLogoUrl, domain, domainImage } from '~/assets/AppConst';
import environment from '~/assets/environment/environment';

const nuxtApp = useNuxtApp();
const route = useRoute();
const dataId = route.params.slug;

// Referral code handling
const handleReferralCode = () => {
	const refId = route.query.aff as string;
	if (refId && process.client) {
		// Store or update referral code in localStorage (always uppercase)
		const uppercaseRefId = refId.toUpperCase();
		localStorage.setItem('referral_code', uppercaseRefId);
		console.log('Referral code stored:', uppercaseRefId);
	}
};

var shopDetails = ref(null as any);
var dataShopCategories = useState(`data_shop_categories_${dataId}`, () => { return [] as any[] });
var dataShopCategoriesBackup = useState(`data_shop_categories_backup_${dataId}`, () => { return [] as any[] });

const { data, refresh, execute } = await useFetch(appConst.apiURL.detailShop + '/' + dataId, {
	key: `shop_space_${dataId}`,
	// ttl: 600,
	headers: {
		Origin: `https://${environment.domain}`
	},
	cache: 'default',
	getCachedData: (key) => {
		// Check if the data is already cached in the Nuxt payload
		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
			// if (route.query?.flush == '1') {
			// 	console.log('flush api cache', dataId);
			// 	delete nuxtApp.payload.data[`shop_detail_${dataId}`]; // Remove the specific key
			// 	return null
			// }
			// console.log("using nuxt payload for api", nuxtApp.payload.data[key].body.data);
			// return nuxtApp.payload.data[key]
			if (route.query?.flush == '1') {
				return null;
			} else {
				return nuxtApp.payload.data[key];
			}
		}
		// Check if the data is already cached in the static data
		if (nuxtApp.static.data[key]) {
			// if (route.query?.flush == '1') {
			// 	console.log('flush api cache', dataId);
			// 	delete nuxtApp.payload.data[`shop_detail_${dataId}`]; // Remove the specific key
			// 	return null
			// }
			// return nuxtApp.static.data[key]
			if (route.query?.flush == '1') {
				return null;
			}
			return nuxtApp.static.data[key];
		}
		return null
	}
});

let shopGet: any = data.value;

if (shopGet?.status == 200) {
	shopDetails.value = await JSON.parse(JSON.stringify(shopGet?.body.data));
	// open_time_list.value = formatOpeningHours(JSON.parse(shopData.value.open_hours));
	// dataShopProducts.value = shopDetails.value.products;
	// getProductsWithCategory();
	let seoDescription = shopDetails.value?.description?.length > 65 ? shopDetails.value?.description?.slice(0, 65).concat('...') : shopDetails.value?.description;
	useServerSeoMeta({
		ogTitle: () => `${shopDetails.value.name} | Rẻ mà gần`,
		title: () => `${shopDetails.value.name} | Rẻ mà gần`,
		description: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogDescription: () => seoDescription ? `${shopDetails.value.name} | ${seoDescription} | remagan.com` : `${shopDetails.value.name} | remagan.com`,
		ogUrl: () => domain + route.fullPath,
		ogImage: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl,
		ogImageUrl: () => shopDetails.value.banner ? domainImage + shopDetails.value.banner.path : baseLogoUrl
	});

	// const dataProducts = await useLazyFetch(appConst.apiURL.getProductWithCategoryInShop.replaceAll(':shop_id', shopDetails.value?.id), {
	// 	key: `shop_products_space_${shopDetails.value?.id}`,
	// 	// ttl: 600,
	// 	headers: {
	// 		Origin: `https://${environment.domain}`
	// 	},
	// 	cache: 'default',
	// 	getCachedData: (key) => {
	// 		// Check if the data is already cached in the Nuxt payload
	// 		if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
	// 			if (route.query?.flush == '1') {
	// 				return null;
	// 			} else {
	// 				return nuxtApp.payload.data[key];
	// 			}
	// 		}
	// 		// Check if the data is already cached in the static data
	// 		if (nuxtApp.static.data[key]) {
	// 			if (route.query?.flush == '1') {
	// 				return null;
	// 			}
	// 			return nuxtApp.static.data[key];
	// 		}
	// 		return null
	// 	}
	// });
	// if (dataProducts.data?.value) {
	// 	let products = JSON.parse(JSON.stringify(dataProducts.data?.value));
	// 	dataShopCategories.value = products.body?.data?.categories;
	// 	dataShopCategoriesBackup.value = products.body?.data?.categories;
	// }
}


var ver = ref(route.query.ver);
var _color_1 = ref('var(--primary-color-1)');
var _color_2 = ref('var(--primary-color-2)');
var _color_3 = ref('#ffffff');
var _color_4 = ref('var(--secondary-color-3)');
var _color_5 = ref('#d19019');

// Watch for route query changes to handle referral codes
watch(() => route.query.aff, (newRefId) => {
	if (newRefId && process.client) {
		const uppercaseRefId = (newRefId as string).toUpperCase();
		localStorage.setItem('referral_code', uppercaseRefId);
		console.log('Referral code updated:', uppercaseRefId);
	}
});

onBeforeMount(()=>{
	console.log(shopGet, shopDetails.value, dataId)
    nuxtApp.$emit(appConst.event_key.show_footer, false)
	// Handle referral code from URL
	handleReferralCode();
})
</script>


<template>
	<!-- <NuxtLoadingIndicator /> -->
	<!-- <ShopComponent /> -->
	<ShopV2T1Component v-if="shopDetails?.id && shopDetails.settings?.general?.view_template?.template_id == 'nha_hang'"
		:shopGet="JSON.parse(JSON.stringify(shopDetails))"
		:shopStyle="{
			font: `${shopDetails.settings?.general?.view_template?.font}, ` + 'Nunito, Montserrat, Mulish, Roboto, sans-serif',
			_color_1: shopDetails.settings?.general?.view_template?.colors?._color_1 ?? _color_1,
			_color_2: shopDetails.settings?.general?.view_template?.colors?._color_2 ?? _color_2,
			_color_3: shopDetails.settings?.general?.view_template?.colors?._color_3 ?? _color_3,
			_color_4: shopDetails.settings?.general?.view_template?.colors?._color_4 ?? _color_4,
			_color_5: shopDetails.settings?.general?.view_template?.colors?._color_5 ?? _color_5,
			_font_resize: shopDetails.settings?.general?.view_template?.font_resize ?? 0,
			banner: shopDetails.settings?.general?.view_template?.banner ?? null,
			use_shop_banner: shopDetails.settings?.general?.view_template?.use_shop_banner ?? null,
			background: shopDetails.settings?.general?.view_template?.background ?? null,
			use_default_backgrond: shopDetails.settings?.general?.view_template?.use_default_backgrond ?? null,
		}"
		:style="{
			fontFamily: `${shopDetails.settings?.general?.view_template?.font}, ` + 'Nunito, Montserrat, Mulish, Roboto, sans-serif',
			'--temp-color-1': shopDetails.settings?.general?.view_template?.colors?._color_1 ?? _color_1,
			'--temp-color-2': shopDetails.settings?.general?.view_template?.colors?._color_2 ?? _color_2,
			'--temp-color-3': shopDetails.settings?.general?.view_template?.colors?._color_3 ?? _color_3,
			'--temp-color-4': shopDetails.settings?.general?.view_template?.colors?._color_4 ?? _color_4,
			'--temp-color-5': shopDetails.settings?.general?.view_template?.colors?._color_5 ?? _color_5,
		}"
	></ShopV2T1Component>
	<!-- <ShopV2T2Component v-else-if="ver == '2'"></ShopV2T2Component> -->
	<ShopV2Component v-else :shopGet="JSON.parse(JSON.stringify(shopDetails))"></ShopV2Component>
	 
</template>