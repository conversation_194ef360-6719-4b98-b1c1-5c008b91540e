<template>
  <v-app>
    <v-main>
      <!-- Loading state -->
      <div v-if="isLoading" class="d-flex justify-center align-center" style="min-height: 100vh;">
        <v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
      </div>

      <!-- Main content when loaded -->
      <div v-else>
      <!-- Hero Section with Company Info -->
      <v-container fluid class="hero-section px-2 px-md-4">
        <v-row justify="center" align="center" class="text-center py-4 py-md-8 mx-0">          
          <v-col cols="12" class="px-2 px-md-4">
            <v-card elevation="0" class="transparent">
              <v-img
                :src="logoUrl"
                alt="Shop Logo"
                width="220"
                height="220"
                cover
                class="mx-auto mb-3 mb-md-4 landingpage-shop-logo"
              ></v-img>
              <v-card-title class="text-h4 text-md-h3 text-lg-h2 font-weight-bold mb-2 mb-md-4 px-1 px-md-4">
                {{ shop.name }}
              </v-card-title>              
              <v-card-subtitle class="text-body-1 text-md-h6 text-lg-h5 mb-2 mb-md-4 px-1 px-md-4 text-wrap">
                {{ shop.description }}
              </v-card-subtitle>
              <v-card-text class="px-1 px-md-4">
                <v-chip
                  prepend-icon="mdi-map-marker"
                  size="large"
                  variant="outlined"
                  color="primary"
                  class="text-wrap mx-auto"
                >
                  {{ shop.address }}
                </v-chip>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container><!-- Video Introduction Section -->
      <v-container class="video-section py-6 py-md-12">
        <v-row justify="center">
          <v-col cols="12" sm="11" md="10" lg="8">
            <v-card elevation="4" rounded="lg">
              <v-card-title class="text-h5 text-md-h4 text-center py-4 py-md-6">
                <v-icon icon="mdi-play-circle" size="24" size-md="32" color="red" class="mr-1 mr-md-2"></v-icon>
                Giới thiệu về chúng tôi
              </v-card-title>
              <v-card-text class="px-0 pb-0">
                <div class="video-wrapper">                  
                  <iframe
                    width="100%"
                    height="500"
                    :src="videoUrl"
                    title="YouTube video player"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    class="rounded-b-lg"
                  ></iframe>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>      
      <!-- Products Section -->
      <v-container class="products-section py-6 py-md-12">
        <v-row>
          <v-col cols="12" class="text-center mb-4 mb-md-8">
            <h2 class="text-h4 text-md-h3 font-weight-bold mb-2 mb-md-4">
              <v-icon icon="mdi-fruit-watermelon" size="32" size-md="40" color="green" class="mr-1 mr-md-2"></v-icon>
              Sản phẩm nông sản Đà Lạt
            </h2>
            <p class="text-body-1 text-md-h6 text-grey-darken-1 px-2">
              Tươi ngon, chất lượng cao từ cao nguyên Đà Lạt
            </p>
          </v-col>
        </v-row>
        
        <v-row>
          <v-col
            v-for="product in products"
            :key="product.id"
            cols="12"
            sm="6"
            md="4"
            lg="3"
            class="d-flex"
          >            
          <v-card
              elevation="3"
              hover
              class="product-card flex-grow-1 d-flex flex-column"
              rounded="lg"
            >
              <v-img
                :src="product.image"
                height="200"
                cover
                class="rounded-t-lg"
              >
                <template v-slot:placeholder>
                  <v-row
                    class="fill-height ma-0"
                    align="center"
                    justify="center"
                  >
                    <v-progress-circular
                      indeterminate
                      color="grey-lighten-5"
                    ></v-progress-circular>
                  </v-row>
                </template>
              </v-img>
              
              <div class="flex-grow-1 d-flex flex-column">
                <v-card-title class="text-h6 font-weight-bold pa-3 pa-md-4">
                  {{ product.name }}
                </v-card-title>
                
                <v-card-subtitle class="text-body-2 pa-0 px-3 px-md-4 pb-3 pb-md-4">
                  {{ product.description }}
                </v-card-subtitle>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </v-container>      <!-- Contact Section -->
      <v-container fluid class="contact-section py-6 py-md-12">
        <v-row justify="center">
          <v-col cols="12" sm="11" md="10" lg="8">
            <v-card elevation="6" rounded="xl" class="pa-4 pa-md-6">
              <v-card-title class="text-h5 text-md-h4 text-center mb-4 mb-md-6">
                <v-icon icon="mdi-contacts" size="32" size-md="40" color="primary" class="mr-1 mr-md-2"></v-icon>
                Liên hệ với chúng tôi
              </v-card-title>
              
              <v-row>
                <!-- Contact Info -->
                <v-col cols="12" md="6" class="mb-4 mb-md-0">
                  <v-list lines="two" class="bg-transparent">
                    <v-list-item
                      prepend-icon="mdi-email"
                      :title="contact.email"
                      subtitle="Email liên hệ"
                      class="px-0"
                    >
                      <template v-slot:append>
                        <v-btn
                          icon="mdi-content-copy"
                          variant="text"
                          size="small"
                          @click="copyToClipboard(contact.email)"
                        ></v-btn>
                      </template>
                    </v-list-item>
                    
                    <v-list-item
                      prepend-icon="mdi-phone"
                      :title="contact.phone"
                      subtitle="Số điện thoại"
                      class="px-0"
                    >
                      <template v-slot:append>
                        <v-btn
                          icon="mdi-content-copy"
                          variant="text"
                          size="small"
                          @click="copyToClipboard(contact.phone)"
                        ></v-btn>
                      </template>
                    </v-list-item>
                  </v-list>
                </v-col>
                
                <!-- Social Media -->
                <v-col cols="12" md="6">
                  <div class="text-center">
                    <h3 class="text-body-1 text-md-h6 mb-3 mb-md-4">Theo dõi chúng tôi trên</h3>
                    <div class="d-flex flex-column flex-md-row justify-center gap-2">
                      <v-btn
                        :href="contact.social.facebook"
                        target="_blank"
                        color="blue"
                        prepend-icon="mdi-facebook"
                        size="large"
                        variant="outlined"
                        class="mb-2 mb-md-0 flex-grow-1 flex-md-grow-0"
                      >
                        Facebook
                      </v-btn>
                      
                      <v-btn
                        :href="contact.social.tiktok"
                        target="_blank"
                        color="black"
                        prepend-icon="mdi-music-note"
                        size="large"
                        variant="outlined"
                        class="mb-2 mb-md-0 flex-grow-1 flex-md-grow-0"
                      >
                        TikTok
                      </v-btn>
                      
                      <v-btn
                        :href="contact.social.website"
                        target="_blank"
                        color="green"
                        prepend-icon="mdi-web"
                        size="large"
                        variant="outlined"
                        class="flex-grow-1 flex-md-grow-0"
                      >
                        Website
                      </v-btn>
                    </div>
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
      </v-container>

      <!-- Footer -->
      <v-footer class="text-center py-6" color="grey-darken-3">
        <v-row justify="center">
          <v-col cols="12">
            <p class="text-white">
              © 2024 {{ shop.name }}. Tất cả quyền được bảo lưu.
            </p>
          </v-col>
        </v-row>      </v-footer>
      </div> <!-- End of main content when loaded -->
    </v-main>
  </v-app>
</template>

<script setup>
import { HttpStatusCode } from 'axios';
import { appConst, baseLogoUrl, domain, domainImage } from '~/assets/AppConst';
import environment from '~/assets/environment/environment';
import { ShopService } from '~/services/shopService/shopService';

// Route params
const route = useRoute();
const nuxtApp = useNuxtApp();
const slug = route.params.slug;

// Page meta
definePageMeta({
  title: 'Landing Page',
  layout: false
});

// API services
const shopService = new ShopService();

// State
const isLoading = ref(true);

// Data fetching
const shopData = ref(null);
const shopGet = await useLazyFetch(appConst.apiURL.detailShop + '/' + slug, {
  key: `shop_detail_landingpage_${slug}`,
  headers: {
    Origin: `https://${environment.domain}`
  },
  getCachedData: (key) => {
    // Check if the data is already cached in the Nuxt payload
    if (nuxtApp.isHydrating && nuxtApp.payload.data[key]) {
      if (route.query?.flush == '1') {
        return null;
      } else {
        return nuxtApp.payload.data[key];
      }
    }

    // Check if the data is already cached in the static data
    if (nuxtApp.static.data[key]) {
      return nuxtApp.static.data[key];
    }

    return null;
  }
});

// Process data and set SEO if fetch was successful
if (shopGet?.data?.value?.status == 200) {
  shopData.value = await JSON.parse(JSON.stringify(shopGet.data.value.body.data));
  
  // Set SEO metadata for SSR
  let seoDescription = shopData.value?.description?.length > 65 
    ? shopData.value?.description?.slice(0, 65).concat('...') 
    : shopData.value?.description;
    
  useServerSeoMeta({
    ogTitle: () => `${shopData.value.name} | Landing Page | Rẻ mà gần`,
    title: () => `${shopData.value.name} | Landing Page | Rẻ mà gần`,
    description: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
    ogDescription: () => seoDescription ? `${shopData.value.name} | ${seoDescription} | remagan.com` : `${shopData.value.name} | remagan.com`,
    ogUrl: () => domain + route.fullPath,
    ogImage: () => shopData.value.logo ? domainImage + shopData.value.logo.path : (shopData.value.logo ? domainImage + shopData.value.logo.path : baseLogoUrl),
    ogImageUrl: () => shopData.value.logo ? domainImage + shopData.value.logo.path : (shopData.value.logo ? domainImage + shopData.value.logo.path : baseLogoUrl)
  });
}

// Mock data for fallback
const mockProducts = [
  {
    id: 1,
    name: "Dâu tây Đà Lạt",
    description: "Dâu tây tươi ngon, ngọt tự nhiên",
    price: 150000,
    image: "https://images.unsplash.com/photo-1518635017498-87f514b751ba?w=300&h=200&fit=crop"
  },
  {
    id: 2,
    name: "Atiso Đà Lạt",
    description: "Atiso tươi, giàu dinh dưỡng",
    price: 80000,
    image: "https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=300&h=200&fit=crop"
  },
  {
    id: 3,
    name: "Cà chua cherry",
    description: "Cà chua cherry ngọt, đỏ mọng",
    price: 120000,
    image: "https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=300&h=200&fit=crop"
  },
  {
    id: 4,
    name: "Rau cải xoăn",
    description: "Rau cải xoăn organic, giàu vitamin",
    price: 50000,
    image: "https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=300&h=200&fit=crop"
  },
  {
    id: 5,
    name: "Hoa cúc mâm xôi",
    description: "Hoa cúc mâm xôi tươi, làm trà ngon",
    price: 200000,
    image: "https://images.unsplash.com/photo-1574781330855-d0db8cc6a79c?w=300&h=200&fit=crop"
  },
  {
    id: 6,
    name: "Củ dền đường",
    description: "Củ dền đường ngọt tự nhiên",
    price: 60000,
    image: "https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=300&h=200&fit=crop"
  }
];

// Products data state
const products = ref(mockProducts);

// YouTube video URL
const videoUrl = "https://www.youtube.com/embed/nziA33FrhoI";

// Computed properties
const shop = computed(() => {
  if (!shopData.value) {
    return {
      name: "The Life Farm - Nông sản Đà Lạt",
      description: "Chuyên cung cấp các sản phẩm nông sản tươi ngon, chất lượng cao từ cao nguyên Đà Lạt. Cam kết 100% tự nhiên, không hóa chất.",
      address: "123 Đường Nguyễn Văn Cừ, Phường 4, Đà Lạt, Lâm Đồng"
    };
  }
  return {
    name: shopData.value.name,
    description: shopData.value.description,
    address: shopData.value.address
  };
});

// Logo URL - use shop logo from API if available or fallback to provided logo
const logoUrl = computed(() => {
  if (shopData.value && shopData.value.logo) {
    return domainImage + shopData.value.logo.path;
  }
  return "https://marketplace.canva.com/EAFzZi-J0-E/1/0/1600w/canva-green-vintage-agriculture-and-farming-logo--T6aMcjxDtw.jpg";
});

// Contact information
const contact = computed(() => {
  if (!shopData.value) {
    return {
      email: "<EMAIL>",
      phone: "+84 123 456 789",
      social: {
        facebook: "https://facebook.com/nongsan.dalat",
        tiktok: "https://tiktok.com/@nongsan.dalat",
        website: "https://nongsan-dalat.com"
      }
    };
  }
  
  return {
    email: shopData.value.email || "<EMAIL>",
    phone: shopData.value.phone || "+84 123 456 789",
    social: {
      facebook: shopData.value.facebook_url || "https://facebook.com/nongsan.dalat",
      tiktok: shopData.value.tiktok_url || "https://tiktok.com/@nongsan.dalat",
      website: shopData.value.website || "https://nongsan-dalat.com"
    }
  };
});

// Fetch products with SSR support
const fetchShopProducts = async (shopId) => {
  try {
    if (!shopId) return mockProducts;
    
    const res = await shopService.searchProductsInShopClient(
      "",
      shopId,
      [],
      8, // Limit to 8 products for the landing page
      0, 
      null,
      null,
      false
    );
    
    if (res.status === HttpStatusCode.Ok && res.body.data.result.length > 0) {
      // Transform API products to match our format
      return res.body.data.result.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description || 'Sản phẩm chất lượng cao từ Đà Lạt',
        price: product.price || 0,
        image: domainImage + product.profile_picture || "https://images.unsplash.com/photo-1518635017498-87f514b751ba?w=300&h=200&fit=crop"
      }));
    }
    return mockProducts;
  } catch (error) {
    console.error('Failed to fetch products:', error);
    // Keep the mock products as fallback
    return mockProducts;
  }
}

// Initialize data on server-side and client-side
async function initData() {
  try {
    if (shopGet.data.value) {
      shopData.value = JSON.parse(JSON.stringify(shopGet.data.value.body.data));
      
      // Set SEO metadata if needed for client-side updates
      if (process.client) {
        const seoDescription = shopData.value?.description?.length > 65 
          ? shopData.value?.description?.slice(0, 65).concat('...') 
          : shopData.value?.description;
        
        useSeoMeta({
          ogTitle: `${shopData.value?.name} | Landing Page | Rẻ mà gần`,
          title: `${shopData.value?.name} | Landing Page | Rẻ mà gần`,
          description: seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
          ogDescription: seoDescription ? `${shopData.value?.name} | ${seoDescription} | remagan.com` : `${shopData.value?.name} | remagan.com`,
          ogUrl: domain + route.fullPath,
          ogImage: shopData.value?.logo ? domainImage + shopData.value?.logo.path : (shopData.value?.logo ? domainImage + shopData.value?.logo.path : baseLogoUrl),
          ogImageUrl: shopData.value?.logo ? domainImage + shopData.value?.logo.path : (shopData.value?.logo ? domainImage + shopData.value?.logo.path : baseLogoUrl)
        });
      }
    }
  } catch (error) {
    console.error('Error initializing data:', error);
  } finally {
    isLoading.value = false;
  }
}

// Format price function
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
};

// Copy to clipboard function
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    // You can add a toast notification here if needed
    console.log('Copied to clipboard:', text);
  } catch (err) {
    console.error('Failed to copy:', err);
  }
};

// Initialize data on server-side and client-side
onMounted(async () => {
  // Initialize data if not already loaded from SSR
  if (!shopData.value) {
    await initData();
  }
  
  // Load products if we have shop data
  if (shopData.value && shopData.value.id) {
    const shopProducts = await fetchShopProducts(shopData.value.id);
    if (shopProducts && shopProducts.length) {
      products.value = shopProducts;
    }
  }
  
  // Always set loading to false
  isLoading.value = false;
});
</script>

<style scoped lang="scss">
.hero-section {
  background: linear-gradient(135deg, #669fea 0%, #27a439 100%);
  color: white;
  min-height: 400px;
  
  .v-card-subtitle {
    word-break: break-word;
    white-space: normal;
    line-height: 1.4;
    text-align: center;
  }
}

.video-section {
  background-color: #f5f5f5;
}

.video-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 0 0 12px 12px;
  
  iframe {
    border-radius: 0 0 12px 12px;
    width: 100%;
    height: 500px; /* Base height for large screens */
  }
}

.products-section {
  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
}

.product-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }
}

.contact-section {
  background: linear-gradient(135deg, #669fea 0%, #27a439 100%);
  
  .v-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
  }
}

// Mobile-first responsive improvements
.v-chip {
  word-break: break-word;
  white-space: normal;
  height: auto !important;
  min-height: 32px;
  
  .v-chip__content {
    white-space: normal;
    line-height: 1.3;
    padding: 0.25rem 0;
  }
}

.gap-2 > * + * {
  margin-left: 0.5rem;
}

@media (max-width: 768px) {
  .gap-2 {
    gap: 0.5rem !important;
  }
  
  .gap-2 > * + * {
    margin-left: 0;
  }
}

.landingpage-shop-logo {
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease-in-out;
  border-radius: 100%;
  &:hover {
    transform: scale(1.05);
  }
}

// Responsive design
@media (max-width: 960px) {
  .hero-section {
    min-height: 350px;
    padding: 1.5rem 1rem;
    
    .v-card-title {
      font-size: 2rem !important;
    }
    
    .v-card-subtitle {
      font-size: 1.1rem !important;
    }
  }
    .video-wrapper iframe {
    height: 380px;
  }
  
  .v-card-title.text-h4 {
    font-size: 1.8rem !important;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 300px;
    padding: 1rem 0.5rem;
    
    .v-card-title {
      font-size: 1.75rem !important;
      line-height: 1.3;
    }
      .v-card-subtitle {
      font-size: 1rem !important;
      line-height: 1.4;
      word-break: break-word;
      white-space: normal;
      padding: 0 0.5rem !important;
    }
  }
    .video-wrapper iframe {
    height: 320px;
  }
  
  .v-card-title.text-h4 {
    font-size: 1.6rem !important;
  }
  
  .products-section {
    .text-h3 {
      font-size: 1.8rem !important;
    }
    
    .text-h6 {
      font-size: 1rem !important;
    }
  }
  
  .contact-section {
    .v-card {
      margin: 0 1rem;
    }
    
    .v-card-title.text-h4 {
      font-size: 1.5rem !important;
    }
  }
  
  .v-btn-group {
    flex-direction: column;
    width: 100%;
    
    .v-btn {
      margin-bottom: 0.75rem;
      width: 100% !important;
      justify-content: center;
    }
  }
}

@media (max-width: 600px) {
  .hero-section {
    min-height: 280px;
    padding: 1rem 0 !important;
    
    .v-row {
      margin: 0 !important;
    }
    
    .v-col {
      padding: 0 0.5rem !important;
    }
    
    .v-card-title {
      font-size: 1.5rem !important;
      padding: 0 0.5rem !important;
      word-break: break-word;
    }
      .v-card-subtitle {
      font-size: 0.95rem !important;
      padding: 0 0.5rem !important;
      line-height: 1.4;
      word-break: break-word;
      white-space: normal;
    }
      .v-chip {
      font-size: 0.875rem !important;
      max-width: 100%;
      margin: 0 auto;
    }
  }
  
  .video-wrapper iframe {
    height: 280px;
  }
  
  .v-card-title.text-h4 {
    font-size: 1.4rem !important;
    padding: 1rem !important;
  }
  
  .products-section {
    .text-h3 {
      font-size: 1.6rem !important;
    }
    
    .product-card {
      margin-bottom: 1rem;
      
      .v-card-title {
        font-size: 1.1rem !important;
        padding: 0.75rem;
      }
      
      .v-card-subtitle {
        font-size: 0.9rem !important;
        padding: 0 0.75rem;
      }
      
      .v-card-text {
        padding: 0.5rem 0.75rem;
      }
      
      .v-card-actions {
        padding: 0.75rem;
      }
    }
  }
  
  .contact-section {
    .v-card {
      margin: 0 0.5rem;
      padding: 1rem !important;
    }
    
    .v-card-title.text-h4 {
      font-size: 1.3rem !important;
      margin-bottom: 1rem !important;
    }
    
    .v-list-item {
      padding: 0.5rem 0;
      
      .v-list-item__content {
        .v-list-item-title {
          font-size: 0.9rem !important;
        }
        
        .v-list-item-subtitle {
          font-size: 0.8rem !important;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 250px;
    padding: 0.75rem 0 !important;
    
    .v-card-title {
      font-size: 1.3rem !important;
      padding: 0 0.25rem !important;
    }
      .v-card-subtitle {
      font-size: 0.9rem !important;
      padding: 0 0.25rem !important;
      line-height: 1.3;
      word-break: break-word;
      white-space: normal;
    }
    
    .v-chip {
      font-size: 0.8rem !important;
      padding: 0.25rem 0.5rem !important;
    }
  }
    .video-wrapper iframe {
    height: 240px;
  }
  
  .v-card-title.text-h4 {
    font-size: 1.2rem !important;
  }
  
  .products-section {
    .text-h3 {
      font-size: 1.4rem !important;
    }
    
    .text-h6 {
      font-size: 0.95rem !important;
    }
  }
  
  .contact-section {
    .v-card-title.text-h4 {
      font-size: 1.1rem !important;
    }
    
    .text-h6 {
      font-size: 1rem !important;
    }
  }
  
  .v-footer {
    padding: 1rem !important;
    
    p {
      font-size: 0.8rem !important;
    }
  }
}
</style>
