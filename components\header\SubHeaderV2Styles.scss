.sub-title-v2-header {
  // font-size: 1.6em;
  padding: 7px 10px;
  margin: 0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
  width: 100%;
  max-width: var(--max-width-view);
  color: white;
  font-size: 17px;
  border-bottom: 0;
  transition: all .3s cubic-bezier(0.075, 0.82, 0.165, 1);
  

  & h3 {
    margin: 0;
    text-transform: uppercase;
  }

  & .header-left {
    display: flex;
    justify-content: left;
    gap: 5px;
    flex: 1;
    margin-right: auto;

    & > button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      height: 40px;
      min-width: fit-content;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      font-size: 30px;
    }
  }

  & .header-right {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    flex: 1;
    margin-left: auto;

    & > button, > a {
      // background: var(--color-background-2);
      padding: 0;
      height: 40px;
      font-size: 30px;
      min-width: fit-content;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }

  & .header-middle{
    // display: flex;
    // flex: 1;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    white-space: nowrap;
    padding: 0 5px;
    // padding: 5px;

    & > h3, > div, > span{
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.sub-title-v2-header.sticky{
  // position: sticky;
  top: 0;
}
