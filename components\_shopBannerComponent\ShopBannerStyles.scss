.banner-container {
  --width-banner: 100%;
  --banner-aspect-ratio: 2;
  aspect-ratio: var(--banner-aspect-ratio);
  width: var(--width-banner);
  height: calc((var(--width-banner)) / var(--banner-aspect-ratio));
  object-fit: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  @supports not (aspect-ratio: var(--banner-aspect-ratio)) {
    &::before {
      float: left;
      padding-top: calc(var(--width-banner / var(--banner-aspect-ratio)));
      content: "";
    }

    &::after {
      display: block;
      content: "";
      clear: both;
    }
  }

  & > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .banner-origin-container {
    --banner-width-origin: 100%;
    --banner-height-origin: calc(var(--banner-width-origin) / var(--banner-scale-origin)) !important;
    --banner-scale-origin: 100%;

    transform: scale(calc(var(--width-banner) / var(--banner-scale-origin))) !important;
    width: var(--banner-width-origin);
    height: var(--banner-height-origin);
    // transform: scale(1);
    transform-origin: center;
    display: flex;
    background: white;
    align-items: center;
    justify-content: center;

    & > img {
      object-fit: cover;
      width: 100% !important;
      height: 100% !important;
    }
  }

  .banner-origin-container.none-style {
    width: 100%;
    height: 100%;
    transform: none !important;

    > img {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
    }
  }
}
