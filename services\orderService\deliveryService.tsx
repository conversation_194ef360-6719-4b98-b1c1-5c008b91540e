import axios from "axios";
import { BaseHTTPService } from "../baseHttpService";
import { appConst } from "../../assets/AppConst";

export class DeliveryService extends BaseHTTPService {
  checkDeliveryPriceCancelToken: any;
  checkDeliveryPrice(distance: number, duration?: number | null) {
    if (typeof (this.checkDeliveryPriceCancelToken) != typeof undefined) {
      this.checkDeliveryPriceCancelToken.cancel()
    }
    this.checkDeliveryPriceCancelToken = axios.CancelToken.source();
    let bodyRef = {
      distance: distance,
      duration: duration,
    };
    return this.https("POST", appConst.apiURL.deliveryCheckPrice, bodyRef, this.checkDeliveryPriceCancelToken.token);
  }

  checkDeliveryPriceFromPartner(body: any) {
    if (typeof (this.checkDeliveryPriceCancelToken) != typeof undefined) {
      this.checkDeliveryPriceCancelToken.cancel()
    }
    this.checkDeliveryPriceCancelToken = axios.CancelToken.source();
    return this.https("POST", appConst.apiURL.deliveryCheckPriceFromPartner, body, this.checkDeliveryPriceCancelToken.token);
  }

  findShipper(shop_id: any, latitude: any, longitude: any, radius?: any) {
    let bodyRef = {
      shop_id: shop_id,
      latitude: latitude,
      longitude: longitude,
      radius: radius
    };
    return this.https("POST", appConst.apiURL.deliveryFindShipper, bodyRef, null, true);
  }

  detail(deliveryId: string) {
    return this.https(
      "GET",
      appConst.apiURL.deliveryDetail.replaceAll(":delivery_id", deliveryId)
    );
  }
  detailByOrderId(orderId: string) {
    return this.https(
      "GET",
      appConst.apiURL.deliveryDetailByOrderId.replaceAll(":order_id", orderId)
    );
  }
  detailV2ByDeliveryId(body: any) {
    return this.https(
      "POST",
      appConst.apiURL.deliveryDetailV2By,
      body
    );
  }
  updateDriverId(delivery_id: string, driver_id: string) {
    let body = {
      id: delivery_id,
      driver_id: driver_id,
    };
    return this.https("POST", appConst.apiURL.deliveryUpdateDriverId, body, null, true);
  }

  createDelivery(body: any) {
    return this.https("POST", appConst.apiURL.deliveryCreate, body, null, true);
  }

  createDeliveryV2(body: any) {
    return this.https("POST", appConst.apiURL.deliveryCreateV2, body, null, true);
  }

  cancelDeliveryV2(body: any) {
    return this.https("POST", appConst.apiURL.deliveryCancelV2, body, null, true);
  }
  updateDelivery(body: any) {
    return this.https("POST", appConst.apiURL.deliveryUpdate, body, null, true);
  }

  listOfDriver(offset = 0, limit = 20, status = null, start_date = null, end_date = null) {
    let url =
      appConst.apiURL.deliveryListOfDriver + `?limit=${limit}&offset=${offset}`;
    if (status) {
      url = url.concat(`&status=${status}`)
    }
    if (start_date) {
      url = url.concat(`&start_date=${start_date}`)
    }
    if (end_date) {
      url = url.concat(`&end_date=${end_date}`)
    }
    return this.https("GET", url);
  }

  driverUpdateDelivery(body: any) {
    return this.https("POST", appConst.apiURL.deliveryDriverUpdate, body, null, true);
  }
}
