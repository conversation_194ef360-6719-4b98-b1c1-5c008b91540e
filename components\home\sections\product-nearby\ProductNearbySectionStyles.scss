.content-list.product-nearby-list {

    padding: 10px 0 !important;
    & .stack-content-title{
        padding: 0 10px;
    }
    .stack-content-list-container {
        justify-content: center !important;
        gap: 0 !important;

        .item-stack-slide {
            padding: 10px !important;
            width: var(--item-stack-width) !important;

            @media screen and (min-width: 1321px) {
                // --item-stack-width: 250px;
                // --item-stack-width: calc((var(--max-width-content-view-1320) - 10px * 2) / 6);
                --item-stack-width: calc(100% / 6);
            }

            @media screen and (max-width: 1320px) and (min-width: 1025px) {
                // --item-stack-width: 200px;
                // --item-stack-width: calc((100dvw - 10px * 2) / 5);
                --item-stack-width: calc(100% / 5);
            }

            @media screen and (max-width: 1024px) and (min-width: 721px) {
                // --item-stack-width: 175px;
                // --item-stack-width: calc((100dvw - 10px * 2) / 4);
                --item-stack-width: calc(100% / 4);
            }

            @media screen and (max-width: 720px) and (min-width: 501px) {
                // --item-stack-width: calc((100dvw - 10px * 2) / 3);
                --item-stack-width: calc(100% / 3);
            }

            @media screen and (max-width: 500px) {
                // --item-stack-width: calc((100dvw - 10px * 2) / 2);
                --item-stack-width: calc(100% / 2);
            }

            & .item-stack {
                width: 100% !important;

                & > .item-stack-content{
                    padding: 7px 0 !important;
                }
            }

            
        }

        
    }
}