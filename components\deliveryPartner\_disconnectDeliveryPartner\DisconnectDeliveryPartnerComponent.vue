<template>
	<VueFinalModal class="my-modal-container" :click-to-close="false" :esc-to-close="false" :overlay-behavior="'persist'"
		content-class="v-stack disconnect-delivery-partner-container" v-model="showShopDeliveryPartnerInfoModal"
		v-on:closed="() => {
			showShopDeliveryPartnerInfoModal = false
		}" contentTransition="vfm-fade">
		<HeaderComponent :title="`${$t('DisconnectDeliveryPartnerComponent.ngung_ket_noi')}`">
			<template v-slot:header_left></template>
		</HeaderComponent>

		<img :src="props.delivery_partner_info.information.logo" alt=""
			v-if="props.delivery_partner_info.information.logo">
		<span v-else>{{ props.delivery_partner_info.name }}</span>

		<div class="disconnect-delivery-partner-footer">
			<button class='reject-button' v-on:click="() => {
				close()
			}">
				{{ $t('DisconnectDeliveryPartnerComponent.thoi_de_sau') }}
			</button>
			<button class='accept-button' :disabled="is_submitting" v-on:click="() => {
				submit();
			}">
				{{ $t('DisconnectDeliveryPartnerComponent.ngung_ket_noi') }}
			</button>
		</div>
	</VueFinalModal>
</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { AuthService } from "~/services/authService/authService";
import { toast } from "vue3-toastify";

import delete_account from "~/assets/image/delete_account.jpg";
import { HttpStatusCode } from "axios";
import { DeliveryPartnerService } from "~/services/deliveryPartnerService/deliveryPartnerService";
import { ShopService } from "~/services/shopService/shopService";
import { AgentService } from "~/services/agentService/agentService";
import * as yup from 'yup';
import { Form as VeeForm, Field, ErrorMessage, useForm } from 'vee-validate';

const { t } = useI18n();
const nuxtApp = useNuxtApp();
const props = defineProps({
	mode: null,
	delivery_partner_info: null,
	shop_id: null
});
const emit = defineEmits(['close']);


var router = useRouter();
var route = useRoute();

var authService = new AuthService();
var shopService = new ShopService();
var agentService = new AgentService();
var deliverPartnerService = new DeliveryPartnerService();

var shop_id = ref<any>(props.shop_id ?? route.query?.shop_id ?? null);
var shop_deli_part_connect_data = ref<{
	name: string,
	address: string,
	phone: string,
	email: string
}>() as any;
var shop_deli_part_detail = ref<any>(null);
var shopData = ref<any>();

var showShopDeliveryPartnerInfoModal = ref(false);
var is_submitting = ref(false);

onMounted(() => {
	getShopInfo().then(() => {
		getShopDeliveryPartnerInfo().then((res: any) => {
			if (res.status == HttpStatusCode.Ok && res.body?.data != null) {
				shop_deli_part_detail.value = res.body.data;
				shop_deli_part_connect_data.value = res.body.data.connect_data;
			}
			else if (res.status == HttpStatusCode.NotFound) {
				shop_deli_part_detail.value = null;
				shop_deli_part_connect_data.value = {
					name: shopData.value.name,
					address: shopData.value.address,
					email: shopData.value.email,
					phone: shopData.value.phone,
				}
			}
			else {
				shop_deli_part_detail.value = null;
				shop_deli_part_connect_data.value = {
					name: shopData.value.name,
					address: shopData.value.address,
					email: shopData.value.email,
					phone: shopData.value.phone,
				}
			}
			showShopDeliveryPartnerInfoModal.value = true;
		}).catch(() => {
			toast.error(t('ShopDeliveryPartnerComponent.co_loi_xay_ra'))
		});
	});

})

function close(updated = false) {
	emit('close', updated);
}

async function submit() {
	is_submitting.value = true;
	let data$ = {
		shop_id: props.shop_id ?? shop_id.value,
		partner: props.delivery_partner_info?.name.toLowerCase(),
		is_enabled: false,
		delivery_partner_id: props.delivery_partner_info?.id,
		connect_data: shop_deli_part_connect_data.value
	}
	if (shop_deli_part_detail.value?.id) {
		deliverPartnerService.updateDeliveryPartnerInfo(data$).then((res) => {
			is_submitting.value = false;
			close(true);
		})
	}
	else {
		deliverPartnerService.addDeliveryPartnerToShop(data$).then((res) => {
			is_submitting.value = false;
			close(true);
		})
	}

}
function getShopInfo() {
	return new Promise((resolve) => {
		if (props.mode != 'agent') {
			getMyShop().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
		else {
			getShopDetail().then((res) => {
				if (res) {
					resolve(res);
				}
				resolve(null)
			});
		}
	})
}

function getMyShop() {
	return new Promise((resolve) => {
		shopService.myShop().then(async res => {
			if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
				shopData.value = res?.body?.data;
			}
			else {
				shopData.value = null;
			}
			resolve(shopData.value);
		}).catch(() => {

			resolve(null);
		})
	})

}
function getShopDetail() {
	return new Promise((resolve) => {
		agentService.agentShopDetail(shop_id.value).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				shopData.value = res.body.data;
				useSeoMeta({
					title: `${t('AppRouteTitle.ManageDeliveryPartnerComponent')} - ${shopData.value?.name}`
				})
				resolve(shopData.value);
			}
			else if (res.status == HttpStatusCode.Unauthorized) {
				router.push({
					path: appRoute.LoginComponent,
					query: {
						redirect: JSON.stringify(route.fullPath)
					}
				})
				resolve(null);
			}
			else {
				toast.error(t('ManageDeliveryPartnerComponent.khong_quyen_quan_ly'))
				// setTimeout(() => {
				// 	router.back();	
				// }, 2000);
				resolve(null);
			}
		}).catch(e => {
			toast.error(t('ManageDeliveryPartnerComponent.co_loi_xay_ra'))
			resolve(null);
			// setTimeout(() => {
			// 		router.back();	
			// 	}, 2000);
		})
	})
}

function getShopDeliveryPartnerInfo() {
	return new Promise((resolve) => {
		deliverPartnerService.getShopDeliveryPartnerInfo(props.shop_id, props.delivery_partner_info?.id).then((res) => {
			resolve(res);
		})
	})

}
</script>

<style lang="scss" src="./DisconnectDeliveryPartnerStyles.scss"></style>