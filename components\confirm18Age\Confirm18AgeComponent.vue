<template>
	<VueFinalModal class="my-modal-container" v-model="show" :overlay-behavior="'persist'"
		content-class="my-modal-content-container v-stack modal-content-container confirm-18-age-container" v-on:closed="() => {
			close()
		}" contentTransition="vfm-fade">
		<div class="confirm-title">
			<span>
				{{ $t('Confirm18AgeComponent.san_pham_bia_ruou_chi_danh_cho_khach_hang_tren_18_tuoi') }}
			</span>
			<span>
				{{ $t('Confirm18AgeComponent.vui_long_xac_nhan_truoc_khi_tiep_tuc') }}
			</span>
		</div>

		<div class='h-stack confirm-modal-buttons'>
			<button class='reject-button' v-on:click="() => {
				close()
			}">
				{{ $t('Confirm18AgeComponent.thoat') }}
			</button>
			<button class='accept-button' v-on:click="() => {
				accept()
			}">
				{{ $t('Confirm18AgeComponent.toi_da_du_18_tuoi') }}
			</button>
		</div>
	</VueFinalModal>

</template>

<script lang="ts" setup>
import { appConst, appDataStartup, domain, domainImage, formatCurrency } from "~/assets/AppConst";
import type { CartDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { VueFinalModal } from 'vue-final-modal';
import icon_for_product from '../../assets/image/icon-for-product.png';
import { useCurrencyInput } from "vue-currency-input";

const nuxtApp = useNuxtApp();
var router = useRouter();
var route = useRoute();
var emit = defineEmits(['accept', 'close', 'reject']);
var show = ref(true);
onMounted(async () => {

})

function close() {
	emit('close')
}

function accept() {
	localStorage.setItem(appConst.storageKey.confirm18Age, 'true');
	emit('accept')
}

function reject() {
	emit('reject')
}
</script>

<style lang="scss" src="./Confirm18AgeStyles.scss"></style>