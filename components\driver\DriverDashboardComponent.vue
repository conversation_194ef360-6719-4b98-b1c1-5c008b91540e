<template>
	<div class="public-container">

		<div class='driver-dashboard-container' v-if="profileData && profileData.id">
			<SubHeaderV2Component :title="$t('AppRouteTitle.DriverDashboardComponent')"></SubHeaderV2Component>
			<div class="driver-dashboard-content">
				<v-overlay v-if="isRefreshing" :z-index="100" :absolute="false" contained
					content-class='spinner-container' persistent scrim="#fff" key="loading" no-click-animation>
					<Icon name="eos-icons:loading"></Icon>
				</v-overlay>
				<client-only>
					<div class="map-container" id="driver_map_container">
						<LMap id="leaflet_map" v-on:ready="(e: any) => {
							leafletMap = e;
							leafletMap.setView([driver_latitude ?? appConst.defaultCoordinate.latitude, driver_longitude ?? appConst.defaultCoordinate.longitude], 17);
							leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);
							initLeafletMap();
						}" :max-zoom="appConst.leafletMapTileOption.maxZoom" :options="{ zoomControl: false }" :world-copy-jump="true"
							:use-global-leaflet="true">
							<LControlZoom position="bottomright"></LControlZoom>
							<span class="current-location-leaflet" :title="$t('Map.vi_tri_cua_ban')" v-on:click="() => {
								gotoCurrentLocationLeaflet();
							}">
								<Icon name="line-md:my-location-loop" class="my-location-icon" />
							</span>

							<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
								:max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
								name="GoogleMap">
							</LTileLayer>
						</LMap>
					</div>
				</client-only>
				<div class="primary-driver-info">
					<img loading="lazy" :src="profileData?.profile_picture
						? ((appConst.provider_img_domain.some(e => profileData?.profile_picture?.includes(e))) ? profileData?.profile_picture : (domainImage + profileData?.profile_picture))
						: non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" v-on:click="() => {

							router.push({
								path: appRoute.ProfileInfoComponent,
								state: {
									profileId: profileId,
								}
							})
						}" />
					<div class="driver-info">
						<nuxt-link class="name"
							:to="{ path: appRoute.ProfileInfoComponent, state: { profileId: profileId } }">
							<span>
								{{ profileData ? profileData.name : "NULL" }}
							</span>
							<Icon name="ph:gender-male-duotone" class="gender-icon male" v-if="profileData.gender == 1">
							</Icon>
							<Icon name="ph:gender-female-duotone" class="gender-icon female"
								v-if="profileData.gender == 0"></Icon>
						</nuxt-link>
						<nuxt-link :to="{ path: appRoute.ProfileInfoComponent, state: { profileId: profileId } }">
							<span class="user-info">
								{{
									(profileData && profileData.phone)
										? (profileData.phone)
										: $t('DriverDashboardComponent.chua_co_sdt')
								}}
							</span>
						</nuxt-link>
						<div class="other-info" v-if="profileData.ratings">
							<div class="rating">
								<Icon name="ic:round-star-rate"></Icon> {{ profileData.ratings.toFixed(1) || 0
								}}<span>/{{
									appConst.defaultMaxRate }}</span>
							</div>

							<div class="rate-amount">
								{{ profileData.comment_count || 0 }} {{ $t('DriverDashboardComponent.danh_gia') }}
							</div>
						</div>
						<div v-else class="other-info">
							<span>{{ $t('DriverDashboardComponent.chua_co_danh_gia') }}</span>
						</div>


					</div>
					<button class="action-button"
						:disabled="profileData.user_status == appConst.user_status.busy || isUpdating" :title="profileData.user_status == appConst.user_status.online ? $t('DriverDashboardComponent.nhan_de_tat')
							: profileData.user_status == appConst.user_status.offline ? $t('DriverDashboardComponent.nhan_de_bat')
								: ''" v-on:click="() => {
									updateOnlineStatus()
								}">
						<div class="online-dot"
							:class="{ 'off': profileData.user_status == appConst.user_status.offline }"></div>
						<span>
							{{ profileData.user_status == appConst.user_status.online ?
								$t("DriverDashboardComponent.online") :
								profileData.user_status == appConst.user_status.busy ?
									$t("DriverDashboardComponent.dang_co_don") :
									$t("DriverDashboardComponent.offline") }}
						</span>
						<!-- <Icon name="lucide:chevron-right"></Icon> -->

					</button>
				</div>
				<div class="driver-dashboard-actions">
					<div class='h-stack driver-dashboard-actions'>

						<button class="delivery-history-option" v-on:click="() => {
							router.push(appRoute.DeliveryHistoryComponent)
						}">
							<Icon name="ic:round-checklist" class="option-icon"></Icon>
							<div class="option-label">
								{{ $t("DriverDashboardComponent.danh_sach_don_hang") }}
								<em v-if="newAmount || true">({{ newAmount }} {{
									$t("DriverDashboardComponent.don_dang_cho") }} ) </em>
							</div>
							<Icon name="material-symbols:chevron-right-rounded" class="icon-right" />
						</button>
					</div>
				</div>
			</div>

		</div>
		<div class='driver-dashboard-container' v-else-if="!isRefreshing && !(profileData && profileData.id)">
			<SubHeaderV2Component :title="$t('AppRouteTitle.DriverDashboardComponent')"></SubHeaderV2Component>
			<div class="primary-user-info">
				<div class="primary-info-content">
					<img loading="lazy" :src="non_avatar" :placeholder="non_avatar" alt="Avatar" class="user-avatar" />
					<div class="user-info-buttons">
						<button class="profile-info-button" v-on:click="() => {
							router.push({
								path: appRoute.LoginComponent,
							})
						}">
							<Icon name="material-symbols:login" size="30px"></Icon>
							{{ $t("DriverDashboardComponent.dang_nhap") }}
							<!-- Đăng nhập -->
						</button>
						<button class="my-shop-button" v-on:click="() => {
							router.push({
								path: appRoute.SignupComponent
							})
						}">
							<Icon name="streamline:user-add-plus-solid" size="25px"></Icon>
							{{ $t("DriverDashboardComponent.dang_ky") }}
							<!-- Đăng ký -->
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import non_avatar from '~/assets/image/non-avatar.jpg';
import driver_marker_location_icon from "~/assets/image/driver-marker.png";
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';

import { appConst, domainImage, isiOS } from '~/assets/AppConst';
import { appRoute } from "~/assets/appRoute";
import { UserService } from '~/services/userService/userService';
import { RatingService } from '~/services/ratingService/ratingService';
import { DriverService } from '~/services/driverService/driverService';
import { toast } from 'vue3-toastify';
import { DeliveryService } from '~/services/orderService/deliveryService';
import { HttpStatusCode } from 'axios';
import { AuthService } from '~/services/authService/authService';

const { t } = useI18n();
const emit = defineEmits(['close', 'submit']);
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

var userService = new UserService();
var driverService = new DriverService();
var deliveryService = new DeliveryService();
var ratingService = new RatingService();
var authService = new AuthService();

var webInApp = ref(null as any);
var isRefreshing = ref(true);
var profileId = ref(route.params ? route.params.profileId : null);
var profileData = ref(null as any);
var isAuth = ref(false);

var delivery_enable = ref(true);
var driving_enable = ref(true);
var driver_latitude = ref<any>();
var driver_longitude = ref<any>();
var driver_orient_alpha = ref<any>()

var newAmount = ref(0);

var isUpdating = ref(false);
var driverWatcherId: any;

var leafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var destinationMarkerLeaflet: L.Marker;
var startMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);

var user_latitude = useState<any>('user_latitude', () => { return null});
var user_longitude = useState<any>('user_longitude', () => { return null });

watch(() => [user_latitude.value, user_longitude?.value], () => {
	console.log(user_latitude.value, user_longitude?.value)
	driver_latitude.value = user_latitude?.value;
	driver_longitude.value = user_longitude?.value;
});


onBeforeMount(() => {
	console.log('before mount')
	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		console.log
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;
	});
})

onMounted(async () => {
	delivery_enable.value = true;
	driving_enable.value = false;
	driver_latitude.value = user_latitude?.value;
	driver_longitude.value = user_longitude?.value;
	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	nuxtApp.$listen(appConst.event_key.user_info_change, () => {
		loadData();
	})

	nuxtApp.$listen(appConst.event_key.check_delivery_update, () => {
		getListDelivery();
	})
	loadData();

	useSeoMeta({
		title: t('AppRouteTitle.DriverDashboardComponent')
	});
});

onUnmounted(() => {
	console.log(driverWatcherId);
	nuxtApp.$unsubscribe(appConst.event_key.user_moving);
	// if ('geolocation' in navigator) {
	// 	navigator.geolocation.clearWatch(driverWatcherId)
	// }
})
watch(
	() => [driver_latitude.value, driver_longitude.value, driver_orient_alpha.value],
	async (newQuery: any, oldQuery: any) => {
		if (localeMarkerLeaflet && newQuery) {
			localeMarkerLeaflet.setLatLng([driver_latitude.value, driver_longitude.value])
		}
		if (driver_orient_alpha.value != null && newQuery) {
			let icon = new nuxtApp.$L.Icon({
				iconUrl: driver_marker_location_icon,
				iconSize: appConst.markerCustom.driverIcon.size,
				className: `${appConst.markerCustom.driverIcon.class} non-transition`,
			})
			localeMarkerLeaflet.setIcon(icon);
			localeMarkerLeaflet.setRotationAngle(driver_orient_alpha.value);
		}
	},
	{ deep: true }
);

async function initLeafletMap() {

	// leafletMap.setView([
	//   my_latitude.value,
	//   my_longitude.value
	// ], 17);
	// fitToDirection();
	await addMyLocationMarker();
	watchDriverOrient();
}

async function getProfileInfo() {

	return await userService.profileInfo();
}
async function loadData() {
	isRefreshing.value = true;

	let authCheck = await authService.checkAuth(true);
	console.log(authCheck)
	if (authCheck?.id) {
		isAuth.value = true;
		profileData.value = authCheck;
		getDriverRating();
		getListDelivery();


	}
	else {
		isAuth.value = false;
		profileData.value = null;
		localStorage.removeItem(appConst.storageKey.token);
		localStorage.removeItem(appConst.storageKey.userInfo);
		sessionStorage.removeItem(appConst.storageKey.stateRestore.AgentShopManageComponent);

	}
	isRefreshing.value = false;
}

function getListDelivery() {
	deliveryService.listOfDriver(0, 100).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			newAmount.value = res.body.data.filter((e: any) => { return e.status == appConst.delivery_status.pending }).length;
		}
	})
}

async function getDriverRating() {
	await ratingService.calcObjectRating(profileData.value.id).then((res: any) => {
		if (res.status == HttpStatusCode.Ok) {
			profileData.value.ratings = res.body.data
		}
	})
	await ratingService.listRatingByObject(profileData.value.id).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			profileData.value.comment_count = res.body.data.count;
			profileData.value.comments = res.body.data.result;
		}
	})
}

async function watchDriverOrient() {
	let author = ( await authService.checkAuthorize(appConst.role_enum.driver) || await authService.checkAuthorize(appConst.role_enum.admin) || await authService.checkAuthorize(appConst.role_enum.admin2))
	if (author) {
		await watchDeviceOrient();
		if (isiOS()) {
			nuxtApp.$listen(appConst.event_key.device_rotated, (e: any) => {
				if (localeMarkerLeaflet && e !== null) {
					driver_orient_alpha.value = e;
				}
			});
		} else {
			ondeviceorientationabsolute = (e) => {
				if (localeMarkerLeaflet && e.alpha !== null) {
					driver_orient_alpha.value = -e.alpha;
				}
			}
		}
	}
}

function watchDeviceOrient() {
	if ('requestPermission' in DeviceOrientationEvent) {
		(DeviceOrientationEvent as any).requestPermission().then((response: any) => {
			if (response === "granted") {
				window.addEventListener("deviceorientation", (e: any) => {
					nuxtApp.$emit(appConst.event_key.device_rotated, e.webkitCompassHeading)
				}, true);
			}
		});
	}
}
function updateOnlineStatus() {
	isUpdating.value = true;
	let body = {
		action: profileData.value.user_status == appConst.user_status.online ? appConst.driver_action.set_offline : appConst.driver_action.set_online,
		latitude: driver_latitude.value,
		longitude: driver_longitude.value,
	}
	driverService.driverAction(body).then((res) => {
		if (res.status == HttpStatusCode.Ok) {
			profileData.value.user_status = (profileData.value.user_status == appConst.user_status.online ? appConst.user_status.offline : appConst.user_status.online);
			console.log(profileData.value.user_status)
			// localStorage.setItem(
			// 	appConst.storageKey.userInfo,
			// 	JSON.stringify(res.body.data)
			// );
			// profileData.value = res.body.data;
			// toast.info(t('DriverDashboardComponent.thay_doi_trang_thai_thanh_cong'));
			// nuxtApp.$emit(appConst.event_key.user_info_change);
		}
		else {
			toast.error(res.body?.message ?? t('DriverDashboardComponent.thay_doi_trang_thai_that_bai'));
		}
		setTimeout(() => {
			isUpdating.value = false;	
		}, 1500);
		
	});
}

// function getDriverLocation() {
// 	if (!driverWatcherId) {
// 		console.log('chua nghe nguoi dung di chuyen');
// 		if ("geolocation" in navigator) {
// 			navigator.geolocation.watchPosition(
// 				(position) => {
// 					console.log(position);
// 					driver_latitude.value = position.coords.latitude;
// 					driver_longitude.value = position.coords.longitude;
// 					// setLocationLeafletMarker(position.coords.latitude, position.coords.longitude);
// 				},
// 				(error) => {
// 					console.log(error)
// 					driver_latitude.value = appConst.defaultCoordinate.latitude;
// 					driver_longitude.value = appConst.defaultCoordinate.longitude;
// 				},
// 				{
// 					enableHighAccuracy: false,
// 					timeout: 15000,
					
// 				}
// 			);
// 		}
// 	}
// 	else{
// 		console.log('da nghe nguoi dung di chuyen');
// 	}
// }

function addMyLocationMarker() {
	localeMarkerLeaflet = nuxtApp.$L.marker([driver_latitude.value ?? appConst.defaultCoordinate.latitude, driver_longitude.value ?? appConst.defaultCoordinate.longitude], {
		icon: nuxtApp.$L.divIcon({
			html:
				`<div class='user-location'>
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="56" stroke-dashoffset="56" d="M12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4Z"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.5s" values="56;0"/></path><path d="M12 4v0M20 12h0M12 20v0M4 12h0" opacity="0"><set attributeName="opacity" begin="0.9s" to="1"/><animate fill="freeze" attributeName="d" begin="0.9s" dur="0.2s" values="M12 4v0M20 12h0M12 20v0M4 12h0;M12 4v-2M20 12h2M12 20v2M4 12h-2"/><animateTransform attributeName="transform" dur="30s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path></g><circle cx="12" cy="12" r="0" fill="currentColor" fill-opacity="0"><set attributeName="fill-opacity" begin="0.6s" to="1"/><animate fill="freeze" attributeName="r" begin="0.6s" dur="0.2s" values="0;4"/></circle></svg>
    </div>`,
			iconSize: [30, 30],

		}),
		rotationOrigin: appConst.markerCustom.driverIcon.rotatePosition
	});
	localeMarkerLeaflet.addTo(leafletMap);
	gotoCurrentLocationLeaflet();
}

function gotoCurrentLocationLeaflet() {
	leafletMap.flyTo([driver_latitude.value ?? appConst.defaultCoordinate.latitude, driver_longitude.value ?? appConst.defaultCoordinate.longitude], 17)
}
</script>

<style lang="scss" src="./DriverDashboardStyles.scss"></style>