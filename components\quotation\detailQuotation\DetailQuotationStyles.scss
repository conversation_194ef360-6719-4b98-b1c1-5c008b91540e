.detail-quotation-container {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: hidden auto;
    display: flex;
    justify-content: center;
    flex-direction: column;

    & .view-detail {
        background: transparent;
        color: white;
        padding: 5px !important;
        box-shadow: none !important;
    }

    &>.detail-quotation-content-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        background: white;
        overflow: hidden;

        &>.data-detail {
            height: 100%;
            overflow: auto;

            & .v-table__wrapper {
                overflow: auto;

                @media screen and (min-width: 961px) {
                    scrollbar-width: auto;
                    /* Firefox */
                    scrollbar-color: auto;

                    &::-webkit-scrollbar-thumb {
                        background: auto;
                        border-radius: unset;
                    }

                    &::-webkit-scrollbar {
                        width: auto;
                        height: auto;
                    }
                }

            }



            & thead {
                position: sticky;
                top: 0;
                z-index: 10;
            }

            & .head-col {
                min-width: 200px;
                text-align: center;
                color: var(--primary-color-1);
                background: var(--secondary-color-1);
                font-weight: 700;
                font-size: 15px;
                position: relative;

                &.short {
                    min-width: 100px;
                }

                &.auto {
                    min-width: auto;
                }

                @media screen and (max-width: 961px) {
                    min-width: 100px;
                }
            }

            & .body-col {
                text-align: center;
                padding: 5px;
                border-right: thin solid #ccc;
                border-bottom: thin solid #ccc;

                &.long-text {
                    min-width: 300px !important;
                }

                & .quotation-detail-input {
                    height: auto;
                    min-height: 45px;
                    width: 100%;
                    padding: 5px;
                    outline: none;
                    background: #f5f6fa;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &>textarea {
                        background-color: transparent !important;
                        resize: none;
                        outline: none;
                        width: 100%;
                        text-align: center;
                    }
                }
            }

            & .body-col.actions {
                text-align: center;
                padding: 5px;
                min-width: auto;
                background: linear-gradient(to left, white 80%, transparent);

                @media screen and (min-width: 961px) {
                    position: sticky;
                    right: 0;
                }

                &>.v-btn-group {
                    border-radius: 5px;

                    &>button.action {
                        align-self: center;
                        padding: 0;
                        font-size: 20px;
                        width: 35px;
                        height: 35px !important;
                        min-width: 30px;

                        &.duplicate {
                            color: #868686;
                        }

                        &.add-new {
                            color: var(--primary-color-1);
                        }

                        &.delete {
                            color: var(--primary-color-2);
                        }
                    }
                }
            }

            & .body-col.material-id {
                min-width: 250px !important;



                & .v-input__control {
                    padding: 3px 10px;
                }

                & .v-field__input {
                    align-items: center;

                    &>input {
                        align-self: center;
                        text-align: center;
                    }
                }
            }
        }

        &>.none-data {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: white;
            overflow: auto;
            font-size: 20px;
        }
    }

    @keyframes slide-left {
        0% {
            right: -50%;
        }

        100% {
            right: 0;
        }
    }

    & .primary-detail-quotation-container {
        background: white;
        position: absolute;
        right: 0;
        height: fit-content;
        animation: slide-left 0.5s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        width: 480px;
        max-width: 85%;

        &>.header-detail {
            margin: 0;
            display: flex;
            padding: 5px;
            min-height: 40px;
            align-items: center;

            &>.information {
                flex: 1;
                padding: 0 35px;
                display: flex;
                justify-content: center;
                text-align: center;
                font-size: 20px;
                color: var(--primary-color-1);
                font-weight: 600;
            }

            &>.close-btn {
                width: 30px;
                min-width: 30px;
                height: 30px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #545454;
                font-size: 25px;
                box-shadow: none;
                position: absolute;
                right: 5px;
                top: 5px;
            }
        }

        &>.body-detail {
            display: flex;
            flex-direction: column;
            padding: 0 10px;
            flex: 1;
            overflow: auto;

            &>.data-detail {
                display: flex;
                margin-top: 10px;
                color: #868686;

                &>label {
                    width: 30%;
                    padding: 0 5px 0 0;
                    font-weight: 700;
                    color: #545454;
                    line-height: normal;
                }

                &>.quotation-info {
                    display: flex;
                    flex-direction: column;
                    width: 70%;
                    align-items: flex-start;
                    font-weight: 600;
                    font-size: 15px;

                    & > .link{
                        word-break: break-all;
                    }

                    & > .copy-btn{
                        padding: 0 10px;
                        height: 30px;
                        color: var(--primary-color-1);
                        text-transform: none;
                    }

                    &>#shop_name {
                        color: var(--primary-color-1);
                        font-weight: 600;
                        line-height: normal;

                        &>#shop_phone {
                            color: #545454;
                            font-style: italic;
                        }
                    }

                    &>#shop_address {
                        color: #868686;
                        font-size: 13px;

                    }

                    &>#supplier_name {
                        color: var(--primary-color-2);
                        font-weight: 600;

                        &>#supplier_phone {
                            color: #545454;
                            font-style: italic;
                        }
                    }

                    &>#supplier_address {
                        color: #868686;
                        font-size: 13px;

                    }

                    & .status-chip {
                        font-weight: 700;
                        height: 25px;
                    }
                }
            }
        }
    }
}