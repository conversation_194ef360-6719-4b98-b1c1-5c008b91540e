/* @import url('../../App.scss'); */
@media screen and (max-width: 500px) {
  .list-map-buttons {
    left: unset;
    right: 0;
  }

  .home-v2-container .search-result-item-container {
    width: calc(50% - 5px) !important;
  }
}

.home-v2-container {
  height: 100%;
  flex: 1;
  background: transparent;
  margin: 0;
  overflow: auto;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  max-width: 100% !important;


  & .location-and-search {
    // height: 20%;
    // min-height: 100px;
    width: 100%;
    background-size: 100%;
    background: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 18px;
    transition: min-height 0.5s ease;

    .background-image {
      width: 200%;
      aspect-ratio: 1 / 1;
      position: absolute;
      background-position: 50% 50%;
      top: -150%;
      background-image: url("~/assets/image/imgpsh_fullsize_anim.jpg");
      animation: fullrotate 5s infinite ease-out;
      background-size: 100%;

      @keyframes fullrotate {
        0% {
          -webkit-transform: rotate(0);
          transform: rotate(0);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    }

    .location-text {
      padding: 5px 10px;
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      text-align: left;
      width: 100%;
      color: white;

      &>img {
        margin: auto;
        height: 40px;
        filter: drop-shadow(0 0 1px white);
      }

      & .address {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }

    & .search-bar-container {
      padding-bottom: 10px !important;

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        margin: auto;

        background: white;
        overflow: hidden;
        //   padding: 0 20px;
        box-shadow: 1px 1px 3px -1px;
        //   width: 100%;
      }

      & .search-input-container {
        width: 100%;
        height: 40px;
        border-color: transparent;
        outline: none;
      }
    }

    & .search-button {
      display: flex;
      align-items: center;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .clear-button {
      display: flex;
      align-items: center;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
    }

    & .voice {
      display: flex;
      align-items: center;
      // background: #f5f6fa;
      border-radius: 0;
      padding: 0 5px;
      color: var(--color-text-note);
      height: 100%;
      border: none;
      font-size: 1.25em;
    }
  }

  & .location-and-search.scale-down {
    // height: fit-content;
    // min-height: 0;
    padding-bottom: 0;

    & .search-bar-container {
      position: relative;
      width: calc(100% - 80px);
      top: unset;
      transform: none;
      border-radius: 2em;
      padding: 0 5px;
      width: 100%;

      &>button {
        width: 40px;
        height: 100%;
        color: white;
        font-size: 1em;
        position: relative;
      }

      &>.cart-in-search {
        animation: none;

        &>em {
          border-radius: 2em;
          color: white;
          background: var(--color-button-error);
          min-width: 15px;
          height: 15px;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          position: absolute;
          bottom: 2px;
          right: 2px;
          line-height: 1;
          box-shadow: 0 0 0px 1px white;
          font-weight: 500;

          &>span {
            font-size: 0.8em;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      & .search-container {
        border-radius: 2em;
        padding: 0 5px;
        width: calc(100% - 80px);
        margin: auto;
      }

      & .search-input-container {
        height: 40px;
      }
    }
  }

  .empty-container {
    flex: 1;
    padding: 15px;
    margin: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .empty-image {
    margin: 0px 10px;
    justify-content: center;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    object-fit: contain;
  }

  .empty-text {
    margin: 10px 0 0;
    font-size: 1.5em;
    text-align: center;
    color: var(--primary-color-1);
  }

  .empty-action {
    color: var(--primary-color-1);
    text-align: center;
    font-size: 1.3em;
    cursor: pointer;
    padding: 0;
  }

  .location-value {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
  }

  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 40px;
    padding: 0 10px;
    aspect-ratio: 1;
    background: var(--primary-color-1);
    border-radius: 5px;
    color: white;
    border: none;
    font-size: 1.3em;
  }

  .filter-result-container {
    display: flex;
    width: 100%;
    flex: 1;
    position: relative;
    margin: auto;
    max-width: var(--max-width-content-view-1320) !important;
  }

  #leaflet_map {
    width: 100%;
    height: 500px;
    min-height: 500px;
    /* height: 100%; */
    outline: none;
    z-index: 1;
  }

  .dashboard {
    // position: absolute;
    // top: 0;
    // left: 0;
    flex: 1;
    z-index: 2;
    width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    background: #f7f7f7;
    padding: 0 0 5px;
    overflow: hidden;
  }

  & .scroll-top-button {
    position: fixed;
    bottom: 70px;
    right: 25px;
    z-index: 100;

    &>button.go-up {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      font-size: 30px;
      background: var(--primary-color-1);
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      z-index: 100;
    }
  }

  .list-result {
    z-index: 3;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: white;
    padding: 10px;

    &>.result-item-container {
      display: flex;
      border: thin solid transparent;
      border-radius: 10px;

      // box-shadow: 2px 4px 8px 0 rgba(0, 0, 0, 0.15);
      gap: 10px;
      background: white;
      color: #2f3640;
      position: relative;

      & a {
        color: unset;
      }
    }

    &>.result-item-container:hover {
      background: linear-gradient(#fff7f8, #fff7f8) padding-box,
        linear-gradient(to bottom, #ff0f47, #ffaa96) border-box;
    }

    &>.result-item-container::after {
      content: "";
      height: 1px;
      width: 75%;
      background-color: #e5e5e5;
      position: absolute;
      bottom: 0;
      right: 0;
    }

    &>.result-item-container:hover::after {
      height: 0;
    }

    &>.empty-search,
    .search-placeholder {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }

    & .search-placeholder>img {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }

    & .showed-all {
      color: #626262;
      font-style: italic;
      width: 100%;
      text-align: center;
      padding: 20px 0;
    }
  }

  .search-result {
    z-index: 101;
    gap: 10px;
    padding: 0;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    position: absolute;
    top: 0;
    left: 0;
    flex: 1;
    width: 100%;
    min-height: 100%;
    display: none;
    background: white;
    padding: 10px;

    &>.history-search {
      // padding: 10px;
      // background: white;
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-weight: 600;
      border-radius: 5px;

      &>span {
        display: flex;
        font-size: 15px;
        align-items: center;
        justify-content: space-between;

        &>button {
          font-size: 20px;
          display: flex;
        }
      }

      &>.list-history-search {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        font-size: 15px;

        &>button {
          border: thin solid #b2b2b2;
          padding: 5px 10px;
          border-radius: 2em;
          color: #545454;
          background: white;
        }
      }
    }

    &>.suggest-search {
      // padding: 10px;
      // background: white;
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-weight: 600;
      border-radius: 5px;

      &>span {
        display: flex;
        font-size: 15px;
        align-items: center;
        justify-content: space-between;

        &>button {
          font-size: 20px;
          display: flex;
        }
      }

      &>.list-suggest-search {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        font-size: 15px;

        &>button {
          // border: thin solid #b2b2b2;
          padding: 3px 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2em;
          color: #545454;
          // background: white;
        }
      }
    }

    &>.categories-and-list-suggest {
      display: flex;
      // overflow: auto;
      position: relative;
      flex: 1;

      &>.categories {
        width: 15%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        background: #f4f4f4;
        height: fit-content;

        &>.item-category.active {
          border-right: 2px solid #ed1b24;
        }

        &>.item-category.active::after {
          content: "";
          width: 13px;
          height: 13px;
          background: #ed1b24;
          position: absolute;
          // border-radius: 5px 0;
          top: 50%;
          right: 0;
          transform: translate(50%, -50%) rotate(45deg);
        }

        &>.item-category {
          width: 100%;
          display: flex;
          flex-direction: column;
          background-color: white;
          padding: 10px 10px 5px;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: #626262;
          position: relative;
          overflow: hidden;

          &>img {
            width: 50px;
            height: 50px;
            aspect-ratio: 1;
            border-radius: 50%;
            background-color: #646464;
          }

          &>svg {
            width: 50px;
            height: 50px;
          }

          &>span {
            font-size: 0.8em;
          }
        }
      }

      &>.list-search-suggest-container {
        background: transparent;
        flex: 1;
        height: fit-content;
        min-height: 100%;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        width: 100%;

        &>.list-search-suggest {
          padding: 0;
          gap: 10px;
          // flex: 1;
          height: -moz-fit-content;
          height: fit-content;
          min-height: 100%;
          display: flex;
          flex-wrap: wrap;
          font-weight: 600;
          margin: 7px 0 0;
          width: 100%;

          &>span {
            width: 100%;
            color: #545454;
            text-align: center;
            font-size: 15px;
            text-transform: uppercase;
          }

          &>.recent-item-container {
            --item-recent-width: 100%;
            display: flex;
            width: var(--item-recent-width);
            align-self: normal;
            align-items: flex-start;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            border: thin solid transparent;
            color: #626262;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);

            &>img {
              width: 100px;
              height: 100px;
              object-fit: cover;
            }

            &>.recent-item-content {
              padding: 10px;
              display: flex;
              flex: 1;
              flex-direction: column;
              gap: 5px;
              justify-content: flex-end;
              line-height: normal;
              overflow: hidden;

              &>.name {
                color: var(--primary-color-1);
                text-align: left;
                font-weight: 700;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              &>.shop-name {
                display: flex;
                width: 100%;
                gap: 5px;
                align-items: baseline;
                overflow: hidden;
                font-weight: bold;
                font-style: italic;
                color: #399901;
                text-align: center;
                font-size: 0.9em;
                justify-content: center;
              }

              & .sold-like-amount {
                color: #a8a7a7;
                font-size: 11px;
                line-height: 13px;
                min-height: 13px;
              }

              & .price-actions {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;

                & .price-container {
                  display: flex;
                  gap: 0;
                  flex-direction: column;
                  justify-content: flex-start;
                  text-align: left;
                  align-items: flex-start;
                  line-height: 1;
                }

                & .price {
                  white-space: nowrap;
                  font-weight: 900;
                  width: fit-content;
                  color: var(--primary-color-2);
                  font-size: 15px;
                }

                & .origin-price {
                  white-space: nowrap;
                  overflow: hidden;
                  text-decoration: line-through;
                  text-overflow: ellipsis;
                  font-weight: 400;
                  font-size: 13px;
                  min-height: calc(13px * 1);
                  color: #545454;
                  font-style: normal;
                  overflow: hidden;
                }

                & .origin-price.hide {
                  opacity: 0;
                }

                &>.add-to-cart {
                  color: var(--primary-color-2);
                  margin-left: auto;
                  margin-top: auto;
                  width: 25px;
                  min-width: 25px;
                  height: 25px;
                  display: flex;
                  font-size: 25px;
                  align-items: center;
                  justify-content: center;
                }
              }

              &>.shop-selected-name {
                --font-size: 15px;
                font-weight: 700;
                color: var(--primary-color-1);
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: var(--font-size);
                white-space: nowrap;
                min-height: calc(var(--font-size) * var(--line-height));
                display: flex;
                align-items: center;
                width: 100%;

                &>span {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  text-align: left;
                }

                &>button.close {
                  color: #a8a7a7;
                  font-size: 20px;
                  margin-left: auto;
                  display: flex;
                }
              }

              &>.shop-selected-address {
                --font-size: 13px;
                --line-height: 1.5;
                font-weight: 400;
                color: #8c8c8c;
                font-size: var(--font-size);
                overflow: hidden;
                width: 100%;
                text-overflow: ellipsis;
                white-space: nowrap;
                min-height: calc(var(--font-size) * var(--line-height));
                margin-top: 5px;
                text-align: left;
              }

              &>.shop-selected-business-rating {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                min-height: calc(var(--font-size) * var(--line-height));
                width: 100%;
                text-align: left;

                &>.business-name {
                  color: #545454;
                  font-weight: 700;
                  font-size: var(--font-size);
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                &>.rating {
                  --font-size: 17px;
                  font-weight: 500;
                  color: #545454;
                  font-size: var(--font-size);
                  display: flex;
                  align-items: flex-end;
                  margin-left: auto;
                  min-width: fit-content;

                  &>svg {
                    color: #ffe500;
                    font-size: 25px;
                    margin-right: 5px;
                  }
                }
              }
            }

            &>.shop-recent-logo {
              box-shadow: none;
              background: white;
              border-radius: 5px 0 0 5px;
              cursor: pointer;

              &>.logo-origin-container {
                transform: scale(calc(100 / var(--scale-origin)));
              }

              &>img {
                min-width: 100%;
                min-height: 100%;
                object-fit: cover;
              }
            }

            @media screen and (min-width: 1321px) {
              --item-recent-width: calc(50% - 5px);
            }

            @media screen and (max-width: 1320px) and (min-width: 1025px) {
              --item-recent-width: calc(50% - 5px);
              ;
            }

            @media screen and (max-width: 1024px) and (min-width: 721px) {
              --item-recent-width: calc(50% - 5px);
            }
          }

          &>.recent-item-container:hover {
            background-color: #ebfeff;
            border: thin solid var(--primary-color-1);
          }

          &>.empty-search,
          .search-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
          }

          & .search-placeholder>img {
            width: 200px;
            height: 200px;
            object-fit: cover;
          }
        }

        & .showed-all {
          color: #626262;
          font-style: italic;
          width: 100%;
          text-align: center;
          padding: 20px 0;
        }
      }
    }
  }

  .search-result.show {
    display: flex;
  }

  .dashboard.show-search {
    height: 0;
    z-index: 1;
  }

  .loading-more {
    background-color: transparent;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .categories-filter-tab {
    text-transform: none;
    font-size: 18px;
    color: var(--color-text-note);
    // box-shadow: 0 0 5px #ccc;
    z-index: 1;
    align-items: center;

    & .category-item-tab {
      margin: 5px;
      padding: 5px;
      // border: 2px solid var(--primary-color-1);
      align-self: flex-start;
      border-radius: 10px !important;
      height: unset !important;
      width: -moz-fit-content;
      width: fit-content;
      color: var(--color-text-note);
      text-align: center;
      font-size: 1em;

      & div.tab-title {
        display: flex;
        align-items: center;
        gap: 5px;

        & span {
          text-transform: none;
          // color: var(--color-text-black);
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: normal;
          font-weight: 500;
        }
      }
    }

    & .category-item-tab.active {
      color: var(--primary-color-1);
      font-weight: bold;
    }

    & .v-slide-group__next,
    .v-slide-group__prev {
      color: var(--primary-color-1);
      min-width: unset;
    }
  }



  .content-list {
    padding: 10px;
    background: white;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
  }

  & .stack-content-title {
    display: flex;
    justify-content: space-between;
    padding: 0 0 5px 0;
    align-items: center;

    &>.section-title {
      font-family: "Montserrat", "Nunito", "Mulish", "Roboto", sans-serif,
        -apple-system, Tahoma, "Segoe UI", monospace !important;
      font-weight: bold;
      font-size: 1.2em;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 5px;
      color: var(--primary-color-1);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &>span {
        width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &>svg {
        font-size: 25px;
        min-width: 25px;
        color: var(--primary-color-2);
      }
    }

    &>.view-all {
      font-weight: 700;
      font-size: 12px;
      cursor: pointer;
      color: #6f6f6f;
      display: flex;
      align-items: center;
      white-space: nowrap;

      &>svg {
        font-size: 15px;
      }
    }

    & img {
      height: 50px;
      object-fit: contain;
    }
  }

  & .stack-content-list {
    display: flex;
    flex: 1;
    min-height: 200px;
    overflow: hidden;
    position: relative;

    &>.none-content-list {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      color: var(--color-text-note);
    }

    &>.stack-content-list-container {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      width: 100%;
      justify-content: center;
      align-items: stretch;
      gap: 10px;
      padding: 5px 0 10px;

      & .item-stack-slide {
        display: flex;
        width: fit-content;
        height: auto;
        cursor: pointer;
        border-radius: 7px;
        // border: thin solid #d8d8d8;
        overflow: hidden;
        position: relative;
        // padding: 5px;

        & .item-stack {
          --item-stack-width: 150px;
          width: var(--item-stack-width);
          height: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          position: relative;
          -webkit-user-drag: none;

          @media screen and (max-width:500px) {
            max-height: 200px !important;

            &>img{
              max-height: 120px;
              max-width: 120px;
              margin: auto;
            }
          }
          

          &>img {
            flex: 1;
            background: var(--color-background-2);
            width: var(--item-stack-width);
            height: var(--item-stack-width);
            // max-height: var(--item-stack-width);
            // min-height: var(--item-stack-width);
            aspect-ratio: 1;
            // max-height: 120px;
            object-fit: cover;
          }

          &>.item-stack-content {
            display: flex;
            flex-direction: column;
            // height: 75px;
            // min-height: 75px;
            padding: 7px;
            text-align: left;
            font-size: 1em;
            gap: 1px;
            flex: 1;


            &>.name {
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: bold;
              color: var(--primary-color-1);
              font-size: 15px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              line-height: 1.1;
              min-height: calc(15px * 1.1 * 2);
              -webkit-box-orient: vertical;
            }

            & .price {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: bold;
              color: var(--primary-color-2);
              font-size: 15px;
              display: flex;
              flex-direction: column;
              line-height: 1.1;
              // min-height: calc(15px*1.1 + 12px*1.1 + 10px);
              margin-top: auto;
              font-weight: 800;
              padding-top: 2px;

              &>.off {
                color: #6c6c6c;
                text-decoration: line-through;
                font-style: normal;
                font-size: 12px;
                font-weight: 400;
              }
            }

            & .shop-name {
              color: var(--primary-color-2);
              font-weight: 800;
              font-size: 15px;
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.1;
              // min-height: calc(15px * 1.1 * 2);
            }

            & .shop-address {
              color: #6a7070;
              font-size: 12px;
              font-weight: 700;
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.1;
              min-height: calc(12px * 1.1 * 2);
              margin-top: 5px;
            }
          }

          &>.shop-stack-content {
            display: flex;
            flex-direction: column;
            // background: #ecfef4;
            border-radius: 0 0 10px 10px;
            padding: 15px 5px 5px;
            position: relative;
            color: var(--primary-color-1);
            // border: thin solid #c1eee4;
            border-top: 0;

            &>.shop-logo {
              box-shadow: 0 0 0 2px white;
              position: absolute;
              bottom: 100%;
              left: 50%;
              z-index: 100;
              transform: translate(-50%, 10px);

              & img {
                min-height: unset;
                max-height: unset;
              }
            }

            &>.shop-name {
              line-height: 1.2;
              font-size: 12px;
              color: #6e716f;
              text-align: center;
              height: 31.2px;
              overflow: hidden;
              font-weight: 700;
              display: flex;
              justify-content: center;
              align-items: center;

              &>span {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }
          }

          &>.shop-logo-stack {
            box-shadow: none;
            border-radius: 0;
            z-index: 100;
            margin: auto;
          }

          &>.distance {
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--primary-color-1);
            color: white;
            padding: 1px 1px 1px 10px;
            border-radius: 2em;
            z-index: 100;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;

            &>em {
              background: white;
              border-radius: 2em;
              width: 20px;
              height: 20px;
              justify-content: center;
              align-items: center;
              display: flex;
              color: var(--primary-color-1);
              margin-left: 5px;
              font-size: 10px;
              font-style: normal;
            }
          }

          @media screen and (min-width: 1321px) {
            // --item-stack-width: 250px;
            --item-stack-width: calc(var(--max-width-content-view-1320) / 6 - 10px * 2);
          }

          @media screen and (max-width: 1320px) and (min-width: 1025px) {
            // --item-stack-width: 200px;
            --item-stack-width: calc(100dvw / 5 - 10px * 2);
          }

          @media screen and (max-width: 1024px) and (min-width: 721px) {
            // --item-stack-width: 175px;
            --item-stack-width: calc(100dvw / 4 - 10px * 2);
          }

          @media screen and (max-width: 720px) and (min-width: 501px) {
            --item-stack-width: calc(100dvw / 3 - 10px * 2);
          }

          @media screen and (max-width: 500px) {
            --item-stack-width: calc(100dvw / 2 - 10px * 2);
          }
        }

        &>.top-left-tag {
          z-index: 1;
          position: absolute;
          top: 10px;
          left: 10px;
          width: 50px;
          height: 50px !important;
          min-height: unset;
          object-fit: contain;
        }

        &>.top-left-tag.small {
          width: 50%;
        }

        &>.icon-hang-tuyen {
          padding: 0;
          display: flex;
          border-radius: 3px;
          text-transform: uppercase;
          overflow: hidden;
          color: white;
          z-index: 10;
          font-weight: 700;
          font-size: 7px;
          position: absolute;
          left: 15px;
          top: 10px;

          &>.icon-1 {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 15px;
            padding: 2px 5px 0 5px;
            background: var(--primary-color-1);
            position: relative;

            &::after {
              content: "";
              width: 6px;
              height: 6px;
              transform: rotate(45deg) translate(-50%, -50%);
              transform-origin: top left;
              background: var(--primary-color-1);
              position: absolute;
              top: 50%;
              left: 100%;
            }
          }

          &>.icon-2 {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px 5px 0 5px;
            height: 15px;
            background: var(--primary-color-2);
          }
        }
      }

      & .item-stack-slide:not([class*="best-around-stack"]):hover,
      .item-stack-slide:not([class*="best-around-stack"]):active,
      .item-stack-slide:not([class*="best-around-stack"]):focus,
      .item-stack-slide:not([class*="best-around-stack"]):target {
        // border: thin solid var(--primary-color-1);
        background: #ebfeff;
        box-shadow: 0 7px 10px -7px #868686;
      }
    }
  }

  & .my-carousel {
    width: 100%;
    height: 100%;

    & img {
      width: 100%;
      height: 100%;
      max-height: 200px;
      object-fit: cover;
    }
  }

  & .stack-carousel {
    flex: 1;

    &>.swiper-wrapper {
      height: 100%;
      padding: 0px 0 10px;
      box-sizing: border-box;
    }

    & .item-stack-slide {
      display: flex;
      width: fit-content;
      height: 100%;
      cursor: pointer;
      border-radius: 7px;
      // border: thin solid #d8d8d8;
      overflow: hidden;
      // padding: 5px;

      & .item-stack {
        --item-stack-width: 150px;
        width: var(--item-stack-width);
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
        -webkit-user-drag: none;

        &>img {
          flex: 1;
          background: var(--color-background-2);
          width: var(--item-stack-width);
          height: var(--item-stack-width);
          max-height: var(--item-stack-width);
          min-height: var(--item-stack-width);
          aspect-ratio: 1;
          object-fit: cover;
        }

        &>.item-stack-content {
          display: flex;
          flex-direction: column;
          // height: 75px;
          // min-height: 75px;
          padding: 7px;
          text-align: left;
          font-size: 1em;
          gap: 1px;
          flex: 1;

          &>.name {
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: bold;
            color: var(--primary-color-1);
            font-size: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 1.1;
            -webkit-box-orient: vertical;
          }

          & .price {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: bold;
            color: var(--primary-color-2);
            font-size: 15px;
            display: flex;
            flex-direction: column;
            line-height: 1.1;
            margin-top: auto;
            font-weight: 800;
            padding-top: 5px;
            // min-height: calc(15px*1.1 + 12px*1.1 + 10px);

            &>.off {
              color: #6c6c6c;
              text-decoration: line-through;
              font-style: normal;
              font-size: 12px;
              font-weight: 400;
            }
          }

          & .shop-name {
            color: var(--primary-color-2);
            font-weight: 800;
            font-size: 15px;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.1;
          }

          & .shop-address {
            color: #6a7070;
            font-size: 12px;
            font-weight: 700;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.1;
            margin-top: 5px;
          }
        }

        &>.shop-stack-content {
          display: flex;
          flex-direction: column;
          // background: #ecfef4;
          border-radius: 0 0 10px 10px;
          padding: 15px 5px 5px;
          position: relative;
          color: var(--primary-color-1);
          // border: thin solid #c1eee4;
          border-top: 0;

          &>.shop-logo {
            box-shadow: 0 0 0 2px white;
            position: absolute;
            bottom: 100%;
            left: 50%;
            z-index: 100;
            transform: translate(-50%, 10px);

            & img {
              min-height: unset;
              max-height: unset;
            }
          }

          &>.shop-name {
            line-height: 1.2;
            font-size: 12px;
            color: #6e716f;
            text-align: center;
            height: 31.2px;
            overflow: hidden;
            font-weight: 700;
            display: flex;
            justify-content: center;
            align-items: center;

            &>span {
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
        }

        &>.shop-logo-stack {
          box-shadow: none;
          border-radius: 0;
          z-index: 100;
          margin: auto;
        }

        &>.distance {
          position: absolute;
          top: 10px;
          left: 10px;
          background: var(--primary-color-1);
          color: white;
          padding: 1px 1px 1px 10px;
          border-radius: 2em;
          z-index: 100;
          font-size: 10px;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;

          &>em {
            background: white;
            border-radius: 2em;
            width: 20px;
            height: 20px;
            justify-content: center;
            align-items: center;
            display: flex;
            color: var(--primary-color-1);
            margin-left: 5px;
            font-size: 10px;
            font-style: normal;
          }
        }

        @media screen and (min-width: 1321px) {
          --item-stack-width: 250px;
        }

        @media screen and (max-width: 1320px) and (min-width: 1025px) {
          --item-stack-width: 200px;
        }

        @media screen and (max-width: 1024px) and (min-width: 721px) {
          --item-stack-width: 175px;
        }
      }

      & .item-stack.tiktok-item {
        --item-stack-width: 400px;
      }

      &>.top-left-tag {
        z-index: 1;
        position: absolute;
        top: 10px;
        left: 10px;
        width: 50px;
        height: 50px !important;
        min-height: unset;
        object-fit: contain;
      }

      &>.top-left-tag.small {
        width: 50%;
      }

      &>.icon-hang-tuyen {
        padding: 0;
        display: flex;
        border-radius: 3px;
        text-transform: uppercase;
        overflow: hidden;
        color: white;
        z-index: 10;
        font-weight: 700;
        font-size: 7px;
        position: absolute;
        left: 15px;
        top: 10px;

        &>.icon-1 {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 15px;
          padding: 2px 5px 0 5px;
          background: var(--primary-color-1);
          position: relative;

          &::after {
            content: "";
            width: 6px;
            height: 6px;
            transform: rotate(45deg) translate(-50%, -50%);
            transform-origin: top left;
            background: var(--primary-color-1);
            position: absolute;
            top: 50%;
            left: 100%;
          }
        }

        &>.icon-2 {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2px 5px 0 5px;
          height: 15px;
          background: var(--primary-color-2);
        }
      }
    }

    & .item-stack-slide:not([class*="best-around-stack"]):hover,
    .item-stack-slide:not([class*="best-around-stack"]):active,
    .item-stack-slide:not([class*="best-around-stack"]):focus,
    .item-stack-slide:not([class*="best-around-stack"]):target {
      // border: thin solid var(--primary-color-1);
      background: #ebfeff;
      box-shadow: 0 7px 10px -7px #868686;
    }

    &>.swiper-button-prev,
    >.swiper-button-next {
      color: white;
      font-size: 15px;
      width: 30px;
      height: 50%;
      top: 5px;
      padding: 0;
      bottom: 0;
      margin-top: 0;
      filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
      text-shadow: none;

      &::after {
        position: absolute;
        top: 75px;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        padding: 5px;
        border-radius: 50%;
        border: 2px solid white;
      }
    }

    &>.swiper-button-prev {
      left: 20px;
      right: unset;
    }

    &>.swiper-button-next {
      right: 20px;
      left: unset;
    }
  }



  & .stack-carousel.best-around-carousel {

    &>.swiper-button-prev,
    >.swiper-button-next {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  // css cho giao diện v2
  .logo-header {
    height: 100%;
    width: auto;
  }

  .search-input-container {
    display: flex;
    justify-content: center;
    padding: 5px;
    gap: 2px;
    background: linear-gradient(to right, var(--primary-color-1), var(--linear-color-1));
    height: 45px;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.1s cubic-bezier(0.075, 0.82, 0.165, 1);

    &>button.category-button {
      font-size: 22px;
      color: white;
      padding: 0;
      width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &>button.back-close-search-button {
      font-size: 25px;
      color: white;
      padding: 0;
      width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &>.search-input-group {
      display: flex;
      background: white;
      border-radius: 5px;
      padding: 0 0 0 5px;
      flex: 1;
      height: 35px;
      max-width: calc(1024px);

      &>button {
        color: var(--linear-color-1);
        width: 35px;
        height: 35px;
        font-size: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &>input {
        background: white;
        height: 35px;
        outline: none;
        font-size: 13px;
        font-weight: bold;
        flex: 1;
      }

      &>input::placeholder {
        color: #a8a7a7;
        font-weight: 600;
      }

      @media screen and (max-width: 1024px) {
        &>button.category-button {
          display: none;
        }

        &>button.back-close-search-button {
          display: none;
        }
      }
    }

    @media screen and (min-width: 1025px) {
      &>button.category-button {
        display: none;
      }

      &>button.back-close-search-button {
        display: none;
      }
    }
  }

  .search-input-container.top-55 {
    top: 55px;
    transition: all 0.3s ease !important;
  }

  .cart-button {
    color: white;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &>svg {
      width: 30px;
    }

    &>.count {
      position: absolute;
      top: 3px;
      right: -2px;
      padding: 0 5px;
      border-radius: 2em;
      background-color: #cc313a;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
    }
  }
}