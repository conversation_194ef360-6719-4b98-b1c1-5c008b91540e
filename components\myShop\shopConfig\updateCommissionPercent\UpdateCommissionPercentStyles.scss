.update-commission-percent-container {
    max-width: 600px;
    width: 100%;
    max-height: 95vh;
    min-height: 450px;
    border-radius: 16px;
    background: white;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    position: relative;

    & h3 {
        text-transform: none;
    }

    .update-commission-percent-content-container {
        flex: 1;
        overflow-y: auto;
        padding: 24px 28px;
        background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);        .update-commission-percent-content {
            .form-field-container {
                margin-bottom: 24px;

                .form-label {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 12px;
                    color: #374151;
                    font-weight: 600;
                    font-size: 16px;

                    svg {
                        color: var(--primary-color-1, #007bff);
                    }
                }

                .input-group-container {
                    gap: 0;
                    align-items: center;
                    position: relative;
                    background: white;
                    border-radius: 12px;
                    border: 2px solid #e5e7eb;
                    transition: all 0.3s ease;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

                    &:focus-within {
                        border-color: var(--primary-color-1, #007bff);
                        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
                        transform: translateY(-1px);
                    }

                    .custom-input {
                        flex: 1;
                        padding: 16px 60px 16px 20px;
                        border: none;
                        background: transparent;
                        font-size: 18px;
                        font-weight: 600;
                        color: #1f2937;
                        outline: none;
                        text-align: center;

                        &::placeholder {
                            color: #9ca3af;
                            font-weight: 400;
                        }

                        &:focus {
                            color: var(--primary-color-1, #007bff);
                        }
                    }

                    .input-suffix {
                        position: absolute;
                        right: 20px;
                        color: var(--primary-color-1, #007bff);
                        font-weight: 700;
                        font-size: 20px;
                        pointer-events: none;
                        background: rgba(0, 123, 255, 0.1);
                        padding: 4px 8px;
                        border-radius: 6px;
                    }
                }                .help-text {
                    color: #6b7280;
                    font-size: 0.9rem;
                    margin-top: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;
                    text-align: center;
                    font-style: italic;
                    background: #f3f4f6;
                    padding: 8px 16px;
                    border-radius: 8px;
                    border-left: 4px solid #d1d5db;

                    svg {
                        color: #9ca3af;
                        flex-shrink: 0;
                    }
                }

                .error-message {
                    color: #dc3545;
                    font-size: 0.875rem;
                    margin-top: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;
                    text-align: center;
                    background: #fef2f2;
                    padding: 8px 16px;
                    border-radius: 8px;
                    border-left: 4px solid #dc3545;

                    &::before {
                        content: '⚠';
                        color: #dc3545;
                        font-weight: bold;
                    }
                }
            }
        }
    }

    .action-buttons {
        padding: 20px 28px 24px;
        background: white;
        border-top: 1px solid #f3f4f6;
        gap: 16px;
        justify-content: space-between;        .cancel-button, .save-button {
            padding: 14px 28px;
            border-radius: 12px;
            border: 2px solid;
            cursor: pointer;
            font-weight: 600;
            font-size: 15px;
            transition: all 0.3s ease;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-transform: none;

            &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none !important;
            }

            &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }

            &:active:not(:disabled) {
                transform: translateY(0);
            }
        }

        .cancel-button {
            background: white;
            border-color: #d1d5db;
            color: #6b7280;
            flex: 1;

            &:hover:not(:disabled) {
                background: #f9fafb;
                border-color: #9ca3af;
                color: #374151;
            }
        }

        .save-button {
            background: linear-gradient(135deg, var(--primary-color-1, #007bff) 0%, var(--primary-color-2, #0056b3) 100%);
            border-color: var(--primary-color-1, #007bff);
            color: white;
            flex: 1;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.6s;
            }

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, var(--primary-color-2, #0056b3) 0%, var(--primary-color-1, #007bff) 100%);
                border-color: var(--primary-color-2, #0056b3);
                
                &::before {
                    left: 100%;
                }
            }
        }
    }

    @media screen and (max-width: 600px) {
        max-width: 95%;
        border-radius: 20px 20px 0 0;
        
        .update-commission-percent-content-container {
            padding: 20px 24px;
        }
        
        .action-buttons {
            padding: 16px 24px 20px;
            flex-direction: column;
            
            .cancel-button, .save-button {
                width: 100%;
                margin: 0;
            }
        }
    }
}
