
import { appConst } from "~/assets/AppConst";
import { BaseHTTPService } from "../baseHttpService";
import axios from "axios";

export class UserService extends BaseHTTPService {
    listNotificationCancelToken:any;

    check_exist(body:any){
        return this.https('POST', appConst.apiURL.checkExist, body);
    }
    profileInfo(id?: string){
        let url = id ? appConst.apiURL.userDetail + id : appConst.apiURL.profileDetail;
        return this.https("GET", url);
    }

    updateInfo(body:any){
        return this.https("POST", appConst.apiURL.updateUser, body, null, true)
    }

    logout(){
        return this.https('POST', appConst.apiURL.logout)
    }

    forgotPasswordClient(body: any) {
        return this.https('POST', appConst.apiURL.forgotPasswordClient, body)
    }

    checkCode(body: any) {
        return this.https('POST', appConst.apiURL.checkCodeConfirm, body)
    }

    changeForgetPassword(body: any) {
        return this.https('POST', appConst.apiURL.changeForgetPasswordClient, body)
    }

    updatePasswordClient(password: any) {
        let body = {
            password: password
        }
        return this.https('POST', appConst.apiURL.updatePasswordClient, body)
    }

    changeEmail(body:any){
        return  this.https('POST', appConst.apiURL.changeEmail, body)
        // return this.https('POST', appConst.apiURL.updatePasswordClient, body)
    }

    switchLanguage(langCode: string){
        return  this.https('POST', appConst.apiURL.switchLanguage, {
            language: langCode
        }, null, true)
    }

    listNotify(offset = 0, limit = 20){
        if (typeof (this.listNotificationCancelToken) != typeof undefined) {
            this.listNotificationCancelToken.cancel()
        }

        this.listNotificationCancelToken = axios.CancelToken.source();
        let url = `${appConst.apiURL.listNotify}?offset=${offset}&limit=${limit}`
        return  this.https('GET', url, null, this.listNotificationCancelToken.token)
    }

    detailNotification(noti_id: string){
        return this.https('GET', appConst.apiURL.notifyDetail.replaceAll(':id',noti_id))
    }
}