.loading-skeleton,
.profile-info-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f5f6fa;
  position: relative;
  font-size: 17px;
  overflow: hidden;
  z-index: 1;
  max-width: var(--max-width-content-view-1024) !important;

  & > .profile-info-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    background-color: #f5f6fa;
    position: relative;
    font-size: 17px;
    // overflow: auto;
    padding: 10px;

    & > .profile-avatar-container {
      display: flex;
      flex-direction: column;
      padding: 10px;
      background: white;
      border-radius: 10px;
      width: 100%;
      margin-bottom: 15px;
      border: 3px dashed #e7e9ec;

      & > .avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 5px auto;
        object-fit: cover;
        box-shadow: 0 0 15px rgb(0, 0, 0, 0.3);
      }
      & > .avatar-action {
        display: flex;
        gap: 5px;
        justify-content: center;
        font-size: 17px;
        font-weight: 600;

        & > .action-button {
          background: #fff;
          border: thin solid #e7e9ec;
          border-radius: 7px;
          color: #343434;
          cursor: pointer;
          display: flex;
          font-size: 15px;
          font-weight: 700;
          height: 100%;
          justify-content: center;

          position: relative;
          & label {
            padding: 5px 15px;
            cursor: pointer;
          }
          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: -1;
          }
        }
      }
    }

    & > .v-stack {
      padding: 0 15px;
      margin-bottom: 10px;
      width: 100%;

      & .content-input-group {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        background: var(--color-background-2);
        border-radius: 5px;
        font-size: 17px;

        & > input {
          flex: 1;
          overflow: auto;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
        & > input:-webkit-autofill {
          background-color: initial !important;
        }
        & > .right-button {
          border-radius: 0 7px 7px 0;
          background: #e7e9ec;
          height: 45px;
          margin: 5px 0;
          padding: 0 10px;
          font-size: 15px;
          font-weight: 600;
          border: 3px solid #e7e9ec;
        }
      }

      & .input-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        height: 45px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
      }
      & .input-date-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
        cursor: pointer;
        width: 100%;
      }

      & .input-custom:disabled,
      > .input-date-custom:disabled {
        background-color: rgb(231, 233, 236) !important;
        cursor: default;
      }

      & > .dropdown-custom {
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 0;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
        cursor: pointer;
        & button {
          background-color: white;
          font-size: 1em;
          font-weight: 600;
          color: var(--color-text-black);
          padding: 10px;
          border: none;
          opacity: 1;
          width: 100%;
          animation: none;
          height: 45px;
          cursor: pointer !important;
        }
        & button:disabled {
          cursor: default !important;
          background-color: rgb(231, 233, 236) !important;
        }
        & input {
          outline: none;
        }
        & .group {
          width: 95%;
          max-height: 500% !important;
          overflow: auto;
          box-shadow: 0 0 5px #ccc;
          border-radius: 10px;
          opacity: 1;
          z-index: 1000;
          background: white;
        }
        & ul {
          background: white !important;
          border: none !important;
          box-shadow: 0 0 5px #aaa !important;
          color: var(--color-text-black);
          margin: 0;
          border-radius: 5px;
          padding: 5px;
          list-style: none;
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-radius: 0;

          & li {
            color: var(--color-text-black) !important;
            padding: 5px;
            background-color: white !important;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            font-weight: 500;

            &:hover {
              background-color: #f5f6fa !important;
            }

            & span {
              color: var(--color-text-black) !important;
            }
          }
          & li[data-headlessui-state~="selected"] {
            background-color: var(--primary-color-1) !important;
            color: white !important;

            & span {
              color: white !important;
            }
          }
          & li[data-headlessui-state="selected"]:hover {
            background-color: var(--primary-color-1) !important;
            color: white !important;
          }
        }
      }

      & > .unlink-google {
        padding: 5px 15px;
        border-radius: 7px;
        background: var(--primary-color-2);
        color: white;
        width: fit-content;
        margin: auto;
      }
    }

    & > .link-social {
      font-size: 15px;
      color: #343434;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      gap: 10px;
      font-weight: 600;

      & > img {
        width: 20px;
        height: 20px;
        object-fit: contain;
      }
    }

    & > .h-stack {
      justify-content: space-evenly;
      width: 100%;
      padding: 5px;
      gap: 10px;
      position: sticky;
      bottom: 0;
      background: white;
    }
  }

  & .skeleton-content-avt {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto;
  }

  & .skeleton-content-btn {
    margin-top: 10px;
    width: 100%;
    height: 50px;
  }

  & > .avt-skeleton {
    margin: 5px auto 1em;
    padding: 0 15px;
  }

  & .content-skeleton {
    margin: 5px 0;
    padding: 0 15px;
  }

  & > .edit-button {
    position: fixed;
    bottom: 75px;
    right: 20px;
    background-color: var(--primary-color-1);
    z-index: 100;
    box-shadow: 0 0 5px rgb(0, 0, 0, 40%);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    color: white;
  }

  & > .footer {
    background: #f5f6fa;
    padding: 0 0 20px 0;
    margin-top: auto;
    justify-content: space-evenly;
  }

  & .dropdown-select-container {
    padding: 0 !important;
    & button:disabled {
      color: var(--primary-color-1) !important;
    }
  }

  & .error-message.success {
    font-style: normal;
    color: green;
  }
  @keyframes high-light {
    50% {
      opacity: 0;
    }
  }
  & .error-message.hight-light:not(.success) {
    transform-origin: 0 0;
    animation: high-light 0.5s ease-in-out infinite;
  }
}

.avatar-dropdown-content > div {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 15px !important;
  margin: 5px;

  & > .avatar-dropdown-item {
    color: white !important;
    text-align: center !important;
    background: transparent !important;
    font-size: 1em;
    position: relative;

    & .v-list-item-title {
      position: relative;

      & > input {
        position: absolute;
        opacity: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
}
